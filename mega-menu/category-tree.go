package mega_menu

import (
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
	"sort"
)

var _ cache.CacheableObject = (*TreeNode)(nil)

type TreeNode struct {
	Category *mage_store.CategoryEntity
	Children []*TreeNode
	Parent   *TreeNode

	cacheKey      string `gorm:"-"`
	isCacheLoaded bool   `gorm:"-"`
}

func (n *TreeNode) AddChild(child *TreeNode) {
	n.Children = append(n.Children, child)
}

func (n *TreeNode) SortChildren() {
	if len(n.Children) == 0 {
		return
	}

	for _, child := range n.Children {
		child.SortChildren()
	}

	n.Children = sortChildren(n.Children)
}

func sortChildren(children []*TreeNode) []*TreeNode {
	if len(children) == 0 {
		return children
	}

	sort.Slice(children, func(i, j int) bool {
		pos1 := 999
		id1 := 0
		if children[i].Category != nil {
			pos1 = children[i].Category.Position
			id1 = int(children[i].Category.EntityID)
		}

		pos2 := 999
		id2 := 0
		if children[j].Category != nil {
			pos2 = children[j].Category.Position
			id2 = int(children[j].Category.EntityID)
		}

		if pos1 != pos2 {
			return pos1 < pos2
		} else {
			return id1 < id2
		}
	})

	return children
}

type CategoryTree struct {
	RootNode *TreeNode
}

func LoadCategoryTree(store *magento_core.StoreEntity, attributesToAdd []mage_store.AttributeCode) (*CategoryTree, error) {
	cachedTree := &TreeNode{
		Category: &mage_store.CategoryEntity{
			EntityID: types.EntityID(store.RootCategoryID),
		},
	}

	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	if cacheClient.MustExists(cachedTree.CacheKey()) {
		err := cacheClient.LoadObj(cachedTree)
		if err == nil {
			return &CategoryTree{
				RootNode: cachedTree,
			}, nil
		} else {
			core_utils.ErrorWarning(err)
		}
	}

	tree := &CategoryTree{}
	rootID := types.ToEntityID(store.RootCategoryID)

	categoryRepo := mage_store.NewCategoryRepository()
	rootCat, err := categoryRepo.Get(rootID)
	if err != nil {
		return tree, err
	}

	collection := categoryRepo.GetCollection().
		Select("e.entity_id, e.path, e.level, e.position, :name, :url_path").
		Where(":include_in_menu = 1 and :is_active = 1").
		Where(mage_store.WhereExpr(
			"e.path like '%s/%%'", rootCat.Path,
		)).
		Order("e.level, e.position ASC")
	for _, attr := range attributesToAdd {
		collection = collection.Select(attr.ToSelectCode())
	}

	var categories *[]*mage_store.CategoryEntity
	categories, err = collection.Data()

	_ = categories

	rootNode := categoryArrayToTree(rootCat, *categories)

	err = cacheClient.SaveObj(rootNode)
	if err != nil {
		core_utils.ErrorWarning(err)
	}

	tree.RootNode = rootNode

	return tree, nil
}

func categoryArrayToTree(rootCat *mage_store.CategoryEntity, categories []*mage_store.CategoryEntity) *TreeNode {
	rootNode := &TreeNode{
		Category: rootCat,
		Children: make([]*TreeNode, 0),
	}

	categoriesIdMap := make(map[types.EntityID]*TreeNode)
	categoriesIdMap[rootCat.EntityID] = rootNode

	for _, cat := range categories {
		categoriesIdMap[cat.EntityID] = &TreeNode{
			Category: cat,
			Children: make([]*TreeNode, 0),
		}
	}

	for _, cat := range categories {
		parentId := cat.GetParentID()
		catId := cat.EntityID

		parentNode, ok := categoriesIdMap[parentId]
		if !ok {
			continue
		}

		var node *TreeNode
		node, ok = categoriesIdMap[catId]
		if node == nil || !ok {
			core_utils.Notice("Category %d not found", catId)
			continue
		}

		node.Parent = parentNode
		parentNode.AddChild(node)
	}
	categoriesIdMap = nil

	rootNode.SortChildren()

	return rootNode
}
