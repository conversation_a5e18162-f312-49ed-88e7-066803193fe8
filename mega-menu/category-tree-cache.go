package mega_menu

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
	"strings"
	"time"
)

func (n *TreeNode) CacheKey() string {
	if n == nil || n.Category == nil {
		return ""
	}

	catId := n.Category.EntityID

	return fmt.Sprintf("category_tree_parent_%d", catId)
}

func (n *TreeNode) SetCacheKey(key string) {
	n.cacheKey = key
}

func (n *TreeNode) IsCacheLoaded() bool {
	return n.isCacheLoaded
}

func (n *TreeNode) GetCacheObject() map[string]string {
	result := make(map[string]string)
	var parentID types.EntityID = 0
	if n.Parent != nil {
		parentID = n.Parent.Category.EntityID
	}

	for key, val := range n.Category.GetCacheObject() {
		key = fmt.Sprintf("%d|%s", parentID, key)
		key = fmt.Sprintf("%d|%s", n.Category.EntityID, key)

		result[key] = val
	}

	for _, child := range n.Children {
		data := child.GetCacheObject()
		for key, val := range data {
			result[key] = val
		}
	}

	return result
}

func (n *TreeNode) SetCacheObject(v map[string]string) error {
	categoriesData := map[string]map[string]string{}
	rootCategoryData := map[string]string{}

	for key, val := range v {
		parts := strings.Split(key, "|")
		if len(parts) < 3 {
			core_utils.Notice("Invalid cache key: %s", key)
			continue
		}

		id := parts[0]
		parent := parts[1]
		keyCode := parts[2]

		if parent == "0" {
			rootCategoryData[keyCode] = val
			continue
		} else {
			if _, ok := categoriesData[id]; !ok {
				categoriesData[id] = map[string]string{}
			}

			categoriesData[id][keyCode] = val
		}
	}

	rootCategory := &mage_entity.CategoryEntity{}
	_ = rootCategory.SetCacheObject(rootCategoryData)

	categories := make([]*mage_entity.CategoryEntity, 0)
	for _, catData := range categoriesData {
		cat := &mage_entity.CategoryEntity{}
		_ = cat.SetCacheObject(catData)
		categories = append(categories, cat)
	}

	rootNode := categoryArrayToTree(rootCategory, categories)

	*n = *rootNode

	return nil
}

func (n *TreeNode) CacheTTL() time.Duration {
	return time.Minute * 9999
}
