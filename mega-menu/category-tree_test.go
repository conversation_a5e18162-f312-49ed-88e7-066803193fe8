package mega_menu

import (
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
	"testing"
)

func treeNodeHasChildrenChildren(tree *TreeNode) bool {
	var hasChildren bool
	for _, child := range tree.Children {
		if len(child.Children) < 1 {
			hasChildren = true
			break
		}
	}

	if !hasChildren {
		return false
	}

	return true
}

func getTestTree() *TreeNode {
	root := &mage_entity.CategoryEntity{
		EntityID: 1,
		Path:     "1",
		UrlPath:  "root",
		Level:    0,
		Name:     "Root",
	}

	categories := []*mage_entity.CategoryEntity{
		{
			EntityID: 2,
			Level:    1,
			Path:     "1/2",
			UrlPath:  "category-2",
			Name:     "Category 2",
			Attributes: types.AttributesData{
				"thumbnail": "test",
			},
		},
		{
			EntityID: 3,
			Level:    1,
			Path:     "1/3",
			UrlPath:  "category-3",
			Name:     "Category 3",
			Attributes: types.AttributesData{
				"thumbnail": "test-2",
			},
		},
		{
			EntityID: 4,
			Level:    2,
			Path:     "1/2/4",
			UrlPath:  "category-4",
			Name:     "Category 4",
		},
	}

	return categoryArrayToTree(root, categories)
}

func Test_TreeNode_categoriesToTree(t *testing.T) {
	tree := getTestTree()
	if !treeNodeHasChildrenChildren(tree) {
		t.Fatal("Expected tree to have children")
	}

	if tree.Children[0].Children[0].Category.Name != "Category 4" {
		t.Fatal("Expected Category 4")
	}
}

func Test_TreeNode_CacheObj(t *testing.T) {
	tree := getTestTree()
	object := tree.CacheObj()

	// 6 per category + 1 thumbnail
	// 4*6 + 2 = 26
	if len(object) != 26 {
		t.Fatal("Expected object to have 22 elements")
	}

	newTree := &TreeNode{}
	newTree.LoadCacheMap(object)

	if !treeNodeHasChildrenChildren(newTree) {
		t.Fatal("Expected tree to have children")
	}
}
