package storage

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	"github.com/siper92/core-utils/config_utils"
	"strings"
	"time"
)

const DefaultCacheTTL = 1 * time.Hour
const MagentoCachePrefix = "graph_api"

type StoreCacheAdapter struct {
	client        *cache.RedisCacheProvider
	customerCache *cache.RedisCacheProvider

	storeCode string
}

func InitNewCacheClient(
	config config_utils.RedisConfig,
	customerDB int,
	storeCode string,
) (*StoreCacheAdapter, error) {
	err := cache.InitDefaultClient(config, MagentoCachePrefix)
	if err != nil {
		return nil, err
	}
	client := cache.Client()
	if client == nil {
		return nil, fmt.Errorf("failed to initialize redis cache client")
	} else if err = client.Test(); err != nil {
		return nil, err
	}

	var customerCache *cache.RedisCacheProvider
	if customerDB != config.Database {
		clientConfig := config
		clientConfig.Database = customerDB
		customerCache, err = cache.NewClient(clientConfig, "c:")
	}

	return &StoreCacheAdapter{
		client:        client,
		customerCache: customerCache,
		storeCode:     storeCode,
	}, err
}

func (c *StoreCacheAdapter) GetClient() *cache.RedisCacheProvider {
	return c.client
}

func (c *StoreCacheAdapter) GetCustomerCache() *cache.RedisCacheProvider {
	if c.customerCache == nil {
		return c.GetClient()
	} else {
		return c.customerCache
	}
}

type MissingFieldsError struct {
	Fields []string
}

func (e *MissingFieldsError) Error() string {
	return fmt.Sprintf("missing fields: %v", strings.Join(e.Fields, ", "))
}
