package storage

import (
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"time"
)

type MysqlAdapter struct {
	conn *gorm.DB
}

func InitNewConnection(
	dbDns string,
	isDevMode bool,
	maxConn int,
) (*MysqlAdapter, error) {
	var err error
	var dbConn *gorm.DB
	var _logger logger.Interface
	if isDevMode {
		_logger = logger.Default.LogMode(logger.Info)
	} else {
		_logger = logger.Default.LogMode(logger.Warn)
	}

	dbConn, err = gorm.Open(mysql.Open(dbDns), &gorm.Config{
		Logger: _logger,
	})
	if err != nil {
		return nil, err
	}

	sqlDB, err := dbConn.DB()
	if err != nil {
		return nil, err
	}

	sqlDB.SetMaxIdleConns(10)
	sqlDB.SetMaxOpenConns(maxConn)
	sqlDB.SetConnMaxLifetime(time.Minute * 30)

	return &MysqlAdapter{
		conn: dbConn,
	}, nil
}

func (a *MysqlAdapter) GetConn() *gorm.DB {
	return a.conn
}

type DbTable string

func (t DbTable) ContainsOptionValue() bool {
	return t == "catalog_product_entity_int"
}

func (t DbTable) String() string {
	return string(t)
}

type SQLFilter string

const (
	EQ SQLFilter = " = ?"
	LT SQLFilter = " < ?"
	GT SQLFilter = " > ?"
	IN SQLFilter = " IN (?)"
)
