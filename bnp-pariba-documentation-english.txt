1. Glossary
NIR - GLP - (Annual Percentage Rate)
APR - GPR - (Annual Percentage Rate)
POS - Partner Point of Sale
SSL - Secure Socket Layer
HTTPS - Secure Hypertext Transfer Protocol

2. Overview
BNP Paribas PF provides its trading partners with a web service called Pricing Service. It is a web-based service that provides a trading interface for communication between the partner's site and the BNP Paribas PF trading system. This service provides a set of methods whose parameters are passed as QueryString. The result of each method is in XML format. The communication between the web service and the trading partner's site is protected and encrypted with certificates. The communication protocol is HTTPS. Each trading partner receives two (2) certificates - one for testing and one for the real working environment.

3. Interface

3.1.Addresses
The service can be accessed at the following addresses:
Test environment: https://ws-test.bnpparibas-pf.bg/ServicesPricing/<MethodName>/<parameters>
Product environment: https://ws.bnpparibas-pf.bg/ServicesPricing/<MethodName>/<parameters>

3.2.Result
Each method of the service returns the same type of result in the following format:
Legend:
• Data – These are method-specific collections of results.
• ErrorCode – Error code (if any). Zero (0) means no error.
• ErrorMessage – Description of the error, if any is registered.
• ErrorDetails – Detailed description of the error, if any is registered

3.3.Methods

3.3.1.GetGoodCategories
This method returns a list of product categories by unique ID of the trading partner. The unique number will be known to each trading partner of BNP Paribas PF.

*******. Input

Parameter | Description | Format | Mandatory |
| PosId | Unique trading partner number | Integer | Yes

*******. Output

| Parameter | Description | Format |
| GoodCategoryId | Unique product category number | Integer |
| GoodCategoryName | Product category name | String |

3.3.2.GetGoodTypes
This method returns a list of products that belong to the corresponding category.

*******. Input

| Parameter | Description | Format | Mandatory |
| GoodCategoryId | Unique product category number | Integer | Yes |

*******. Output

| Parameter | Description | Format |
| GoodTypeId | Unique product number | Integer |
| GoodTypeName | Product Name | String |

3.3.3.GetAvailablePricingSchemes
This method returns a list of the top 10 most suitable payment schemes, based on the specified criteria.

*******. Input
| Parameter | Description | Format | Mandatory |
| PosId | Unique trading partner number | Integer | Yes |
| Goods | List of unique goods numbers | Integer [] (separated ",") | Yes |
| Total Good Price | Total price of all goods | Decimal | Yes |
| Initial Payment | Initial Payment | Decimal | Yes |

*******. Output
| Parameter | Description | Format |
| PricingSchemeId | Unique pricing scheme number | Integer |
| PricingSchemeName | Pricing scheme name | String |

3.3.4.GetAvailablePricingVariants
This method returns a list of the top 15 most suitable variants for the respective trading partner.

*******. Input
| Parameter | Description | Format | Mandatory |
| PosId | Unique trading partner number | Integer | Yes |
| Goods | List of unique item numbers | Integer[](separated by ",") | Yes |
| Total price of goods | Total price of all goods | Decimal | Yes |
| Initial payment | Initial installment | Decimal | Yes |
| Preferred installment | Preferred installment | Decimal | No |
| PricingSchemeId | Unique pricing scheme number | Integer | Yes |

*******. Output
| Parameter | Description | Format |
| Preferred installment | Preferred installment | Decimal |
| PricingVariantId | Unique pricing variant number | Integer |
| PricingSchemeId | Unique pricing scheme number | Integer |
| PricingSchemeName | Pricing scheme name | String |
| Maturity Maturity (number of installments) | Integer |
| NIR% | Nominal interest rate | Decimal |
| APR% | Annual interest rate | Decimal |
| CorrectDownPaymentAmount | Mandatory selected installment | Decimal |
| Installment amount | Monthly installment | Decimal |
| Total payment amount | Full loan amount | Decimal |

3.3.5.Loan calculation
This method returns the final data for the desired loan.

*******. Input
| Parameter | Description | Format | Oblig. |
| PosId | Unique Trading Partner Number | Integer | Yes |
| Goods List of unique item numbers | Integer [] (separate ",") | Yes |
| Total Price of Goods | Total Price of All Items | Decimal | Yes |
| Initial Payment | Initial Payment | Decimal | Yes |
| PricingVariantId | Unique Pricing Scheme Number | Integer | Yes |

*******. Output
| Parameter | Description | Format |
| PricingVariantId | Unique Pricing Variant Number | Integer |
| PricingSchemeId Unique Pricing Scheme Number | C