package magento_core

import (
	"errors"
	"github.com/siper92/api-base/cache"
	"gorm.io/gorm"
	"strconv"
	"strings"
)

var _ cache.CacheableObject = (*UrlRewrite)(nil)

type UrlRewriteRepository struct {
	store  *StoreEntity
	dbConn *gorm.DB
}

func (u *UrlRewriteRepository) resolveUrl(requestUrl string, dept int) (*UrlRewrite, error) {
	var err error
	rewrite := &UrlRewrite{}
	if dept > 5 {
		return rewrite, errors.New("too many redirects")
	}

	_requestUrl := strings.TrimRight(requestUrl, "/")
	dbSelect := u.DB().Table(rewrite.TableName()).
		Select("request_path", "id_path", "target_path", "options").
		Where(
			"request_path = ? OR request_path = ?",
			_requestUrl,
			_requestUrl+"/",
		).
		Where("store_id = ?", u.store.GetID())

	err = dbSelect.Find(rewrite).Error
	if err != nil {
		return rewrite, err
	}

	if rewrite.GetRedirectCode() > 0 {
		var redirect *UrlRewrite
		// find last redirect
		redirect, err = u.resolveUrl(rewrite.TargetPath, dept+1)
		if err != nil {
			return rewrite, err
		}

		rewrite.TargetPath = redirect.RequestPath
	}

	return rewrite, nil
}

const (
	splashPageUrlCacheKey = "splash_page_urls"
	cmsPageUrlCacheKey    = "cms_page_urls"
)

type UrlTypeCache struct {
	EntityId string `gorm:"column:entity_id"`
	URLKey   string `gorm:"column:url_key"`
}

func (u *UrlRewriteRepository) getCMSRoute(identifier string) (*UrlRewrite, error) {
	if u.Cache().MustExists(cmsPageUrlCacheKey) == false {
		var urls []UrlTypeCache
		u.DB().Raw(`select cms_page.page_id as entity_id, cms_page.identifier as url_key from cms_page
 left join cms_page_store on cms_page_store.page_id = cms_page.page_id
 where (cms_page_store.store_id = ? or cms_page_store.store_id = 0 or cms_page_store.store_id is null)
   and cms_page.is_active = 1
order by cms_page_store.store_id`, u.store.GetID()).
			Scan(&urls)
		urlMap := make(map[string]string)
		for _, _url := range urls {
			urlMap[_url.URLKey] = _url.EntityId
		}

		err := u.Cache().SaveMap(cmsPageUrlCacheKey, urlMap, cache.InfiniteTTL)
		if err != nil {
			return nil, err
		}
	}

	val, _ := u.Cache().GetMapValue(cmsPageUrlCacheKey, identifier)
	if val == "" {
		return nil, nil
	}
	entityId, _ := strconv.Atoi(val)
	idPath := NewType(CmsPageUrl, int64(entityId))
	return &UrlRewrite{
		RequestPath: identifier,
		IDPath:      idPath,
		TargetPath:  idPath.String(),
	}, nil
}

func (u *UrlRewriteRepository) getSplashPageRoute(identifier string) (*UrlRewrite, error) {
	if u.Cache().MustExists(splashPageUrlCacheKey) == false {
		var urls []UrlTypeCache
		u.DB().
			Select("page_id as entity_id, url_key").
			Table("attributesplash_page").
			Where("is_enabled = ?", 1).
			Scan(&urls)
		urlMap := make(map[string]string)
		for _, _url := range urls {
			urlMap[_url.URLKey] = _url.EntityId
		}

		err := u.Cache().SaveMap(splashPageUrlCacheKey, urlMap, cache.InfiniteTTL)
		if err != nil {
			return &UrlRewrite{
				RequestPath: identifier,
			}, err
		}
	}

	val, _ := u.Cache().GetMapValue(splashPageUrlCacheKey, identifier)
	if val == "" {
		return &UrlRewrite{
			RequestPath: identifier,
		}, nil
	}

	entityId, _ := strconv.Atoi(val)
	idPath := NewType(SplashPageUrl, int64(entityId))
	return &UrlRewrite{
		RequestPath: identifier,
		IDPath:      idPath,
		TargetPath:  idPath.String(),
	}, nil
}

func (u *UrlRewriteRepository) Get(requestUrl string) (*UrlRewrite, error) {
	var err error
	rewrite := &UrlRewrite{
		RequestPath: requestUrl,
	}

	cacheClient := GetStoreClient().GetCacheClient()
	if cacheClient.MustExists(rewrite.CacheKey()) {
		err = cacheClient.LoadObj(rewrite)
		if err != nil {
			return rewrite, err
		}
	} else {
		rewrite, err = u.resolveUrl(requestUrl, 0)
		if err != nil {
			return rewrite, err
		}

		if rewrite == nil || rewrite.IDPath == "" {
			rewrite, err = u.getCMSRoute(requestUrl)
			if err != nil {
				return rewrite, err
			}
		}

		if rewrite == nil || rewrite.IDPath == "" {
			rewrite, err = u.getSplashPageRoute(requestUrl)
			if err != nil {
				return rewrite, err
			}
		}

		if rewrite != nil && rewrite.IDPath != "" {
			err = cacheClient.SaveObj(rewrite)
		}
	}

	return rewrite, err
}

func (u *UrlRewriteRepository) DB() *gorm.DB {
	return u.dbConn
}

func (u *UrlRewriteRepository) Cache() *cache.RedisCacheProvider {
	cacheClient := GetStoreClient().GetCacheClient()
	return cacheClient
}

func NewUrlRewriteRepository(store *StoreEntity) *UrlRewriteRepository {
	return &UrlRewriteRepository{
		store:  store,
		dbConn: GetStoreClient().DbConn(),
	}
}
