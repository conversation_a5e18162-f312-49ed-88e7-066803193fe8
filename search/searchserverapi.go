package search

import (
	"encoding/json"
	"errors"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/internal/config"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/packages/magento-core/api"
	"strings"
	"time"
)

type CategoryResult struct {
	CategoryId string `json:"category_id"`
}

type ProductResult struct {
	ProductId string `json:"product_id"`
}

type SearchFilterOption struct {
	Value string `json:"value"`
	Count int    `json:"count"`
	Title string `json:"title"`
}

type SearchFilter struct {
	Title         string               `json:"title"`
	AttributeCode string               `json:"attribute"`
	Type          string               `json:"type"`
	Buckets       []SearchFilterOption `json:"buckets"`
}

type SearchApiResult struct {
	TotalItems   int              `json:"totalItems"`
	StartIndex   int              `json:"startIndex"`
	ItemsPerPage int              `json:"itemsPerPage"`
	Suggestions  []string         `json:"suggestions"`
	Categories   []CategoryResult `json:"categories"`
	Items        []ProductResult  `json:"items"`
	Filters      []SearchFilter   `json:"facets"`
}

type SearchParams struct {
	ItemsPerPage       int
	SearchString       string
	Page               int
	IncludeSuggestions bool
	IncludeCategories  bool
	IncludeFilters     bool
	SortBy             string
	SortDirection      string
	Filters            map[string][]string
}

func GetSearchResults(params SearchParams) (SearchApiResult, error) {
	var result SearchApiResult
	apiKey := getApiKey()
	if apiKey == "" {
		return result, errors.New("липсва API ключ за търсене")
	}

	filters := map[string]string{
		"api_key": getApiKey(),
		"maxResults": fmt.Sprintf("%d", core_utils.IF(
			params.ItemsPerPage < 1,
			24,
			params.ItemsPerPage,
		)),
		"startIndex": fmt.Sprintf("%d", core_utils.IF(
			params.Page < 1,
			0,
			params.ItemsPerPage*(params.Page-1),
		)),
		"suggestions":             fmt.Sprintf("%t", params.IncludeSuggestions),
		"categories":              fmt.Sprintf("%t", params.IncludeCategories),
		"facets":                  fmt.Sprintf("%t", params.IncludeFilters),
		"categoriesMaxResults":    "5",
		"restrictBy[status]":      "1",
		"restrictBy[visibility]":  "3|4",
		"restrictBy[is_in_stock]": "1",
		"q":                       params.SearchString,
	}

	if params.SortBy != "" {
		filters["sortBy"] = params.SortBy
	}

	if params.SortDirection != "" {
		filters["sortOrder"] = params.SortDirection
	}

	for k, v := range params.Filters {
		filters[fmt.Sprintf("restrictBy[%s]", k)] = strings.Join(v, "|")
	}

	res, err := api.ExternalApi{
		BaseUrl: config.GetConfig().Search.Endpoint,
	}.Get(filters)

	if err != nil {
		return result, err
	}

	if err = json.Unmarshal(res, &result); err != nil {
		core_utils.ErrorWarning(err)
		return result, errors.New("невалиден респонс")
	}

	return result, nil
}

func getApiKey() string {
	var err error
	var val string
	cacheKey := "searchanise_api_key"
	if praktis.GetCacheClient().MustExists(cacheKey) {
		val, err = praktis.GetCacheClient().Get(cacheKey)
		core_utils.ErrorWarning(err)
		if val != "" {
			return val
		}
	}

	err = praktis.GetDbClient().
		Table("searchanise_config").
		Select("value").
		Where("path = 'se_api_key'").Scan(&val).Error
	core_utils.ErrorWarning(err)

	if val != "" {
		err = praktis.GetCacheClient().Save(cacheKey, val, time.Minute*30)
		core_utils.ErrorWarning(err)
	}

	return val
}
