package search

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/internal/praktis/catalog"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
)

func GetCategoryList(catResults []CategoryResult) []*mage_entity.CategoryEntity {
	var res []*mage_entity.CategoryEntity
	for _, category := range catResults {
		cat, err := catalog.GetCategoryEntity(
			types.ToEntityID(category.CategoryId),
		)
		if err != nil {
			core_utils.ErrorWarning(fmt.Errorf("GetCategoryList: %w", err))
			continue
		}

		if cat != nil && cat.IsValid() {
			res = append(res, cat)
		}
	}

	return res
}
