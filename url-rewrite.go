package magento_core

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"strings"
	"time"
)

type UrlType string

const (
	CategoryUrl   UrlType = "category"
	ProductUrl    UrlType = "product"
	CmsPageUrl    UrlType = "cms-page"
	UndefinedUrl  UrlType = "undefined"
	SplashPageUrl UrlType = "splash_page"
)

type IDPath string

func (p IDPath) GetID() types.EntityID {
	idPathParts := strings.Split(string(p), "/")
	if len(idPathParts) == 2 {
		id, _ := strconv.ParseInt(idPathParts[1], 10, 32)
		return types.EntityID(id)
	}

	return 0
}

func (p IDPath) GetIntID() int64 {
	idPathParts := strings.Split(string(p), "/")
	if len(idPathParts) == 2 {
		id, _ := strconv.ParseInt(idPathParts[1], 10, 32)
		return id
	}

	return 0
}

func NewType(entityType UrlType, id int64) IDPath {
	switch entityType {
	case CategoryUrl:
		return IDPath(fmt.Sprintf("category/%d", id))
	case ProductUrl:
		return IDPath(fmt.Sprintf("product/%d", id))
	case CmsPageUrl:
		return IDPath(fmt.Sprintf("cms-page/%d", id))
	case SplashPageUrl:
		return IDPath(fmt.Sprintf("%s/%d", SplashPageUrl, id))
	default:
		return IDPath(fmt.Sprintf("undefined/%d", id))
	}
}

func (p IDPath) Type() UrlType {
	if strings.HasPrefix(string(p), "category/") {
		return CategoryUrl
	} else if strings.HasPrefix(string(p), "product/") {
		return ProductUrl
	} else if strings.HasPrefix(string(p), "cms-page/") {
		return CmsPageUrl
	} else if strings.HasPrefix(string(p), "splash_page/") {
		return SplashPageUrl
	}

	return UndefinedUrl
}

func (p IDPath) String() string {
	return string(p)
}

type UrlRewrite struct {
	RequestPath  string
	IDPath       IDPath
	TargetPath   string
	RedirectType string `gorm:"column:options"`

	cacheKey      string `gorm:"-"`
	isCacheLoaded bool   `gorm:"-"`
}

func (u *UrlRewrite) GetRedirectCode() int {
	if u.RedirectType == "RP" {
		return 301
	} else if u.RedirectType == "R" {
		return 302
	} else {
		return 0
	}
}

func (u *UrlRewrite) TableName() string {
	return "core_url_rewrite"
}

func (u *UrlRewrite) CacheKey() string {
	return "url_rewrite_" + core_utils.Sha256(u.GetUrl())
}

func (u *UrlRewrite) SetCacheKey(key string) {
	u.cacheKey = key
}

func (u *UrlRewrite) IsCacheLoaded() bool {
	return u.isCacheLoaded
}

func (u *UrlRewrite) CacheTTL() time.Duration {
	return time.Hour * 1
}

func (u *UrlRewrite) GetCacheObject() map[string]string {
	return map[string]string{
		"request_path": u.RequestPath,
		"id_path":      string(u.IDPath),
		"target_path":  u.TargetPath,
		"redirect":     u.RedirectType,
	}
}

func (u *UrlRewrite) SetCacheObject(v map[string]string) error {
	u.RequestPath = v["request_path"]
	u.IDPath = IDPath(v["id_path"])
	u.TargetPath = v["target_path"]
	u.RedirectType = v["redirect"]

	return nil
}

func (u *UrlRewrite) GetUrl() string {
	return u.RequestPath
}

func (u *UrlRewrite) EntityID() int64 {
	return u.IDPath.GetIntID()
}
