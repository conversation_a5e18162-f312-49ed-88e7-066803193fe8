package magento_core

import (
	"context"
	"fmt"
	"github.com/siper92/api-base/cache"
	"github.com/siper92/core-utils/config_utils"
	"gorm.io/gorm"
	"praktis.bg/store-api/packages/magento-core/storage"
)

type StoreClient struct {
	mode  string
	store *StoreEntity

	dbAdapter *storage.MysqlAdapter
	cache     *storage.StoreCacheAdapter
	ctx       context.Context
}

const UseCacheConst = true

func (mc *StoreClient) GetCacheClient() *cache.RedisCacheProvider {
	return mc.cache.GetClient()
}

func (mc *StoreClient) GetCustomerCacheClient() *cache.RedisCacheProvider {
	return mc.cache.GetCustomerCache()
}

func (mc *StoreClient) GetDbAdapter() *storage.MysqlAdapter {
	return mc.dbAdapter
}
func (mc *StoreClient) DbConn() *gorm.DB {
	return mc.dbAdapter.GetConn()
}

func (mc *StoreClient) GetStore() *StoreEntity {
	if mc.store == nil {
		return &StoreEntity{
			StoreID: 1,
		}
	}
	return mc.store
}

var (
	storeClient *StoreClient
)

func GetStoreClient() *StoreClient {
	return storeClient
}

type InitMagentoOptions struct {
	Mode      string
	StoreCode string
	Database  struct {
		DSN            string
		MacConnections int
	}
	Cache           config_utils.RedisConfig
	CustomerCacheDB int
	JWTSecret       string
}

func InitStoreClient(p InitMagentoOptions) error {
	var err error
	var dbAdapter *storage.MysqlAdapter

	storeClient = &StoreClient{}
	storeClient.mode = p.Mode
	storeClient.ctx = context.Background()

	maxConn := p.Database.MacConnections
	if maxConn < 1 {
		maxConn = 300
	}

	dbAdapter, err = storage.InitNewConnection(p.Database.DSN, p.Mode == "dev", maxConn)
	if err != nil {
		return err
	}
	storeClient.dbAdapter = dbAdapter

	cacheClient, err := storage.InitNewCacheClient(
		p.Cache,
		p.CustomerCacheDB,
		p.StoreCode)
	if err != nil {
		return err
	}
	storeClient.cache = cacheClient

	store := &StoreEntity{}
	err = store.InitStoreByCode(p.StoreCode)
	if err != nil {
		return err
	}

	storeClient.store = store

	if len(p.JWTSecret) < 2 {
		return fmt.Errorf(`missing required JWT secret`)
	} else {
		SetJWTSecret([]byte(p.JWTSecret))
	}

	return nil
}
