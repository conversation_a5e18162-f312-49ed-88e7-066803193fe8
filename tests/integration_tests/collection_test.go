package integration_tests

import (
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"testing"
)

func Test_GetData(t *testing.T) {
	categoryRepo := mage_entity.NewCategoryRepository()
	collection := categoryRepo.GetCollection().
		Select("e.entity_id, e.path").
		Select(":name, :url_path, :thumbnail").
		Where(":include_in_menu = 1 and :is_active = 1").
		Order("e.level, e.position ASC")

	categories, err := collection.Data()
	if err != nil {
		t.Fatal(err)
	}

	if len(*categories) < 1 {
		t.Fatal("categories is empty")
	}

	for _, cat := range *categories {
		if cat.EntityID < 1 {
			t.Fatal("Category EntityID is invalid")
		} else if cat.Path == "" {
			t.Fatal("Category Path is empty")
		}
	}
}
