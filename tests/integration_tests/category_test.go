package integration_tests

import (
	"errors"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/storage"
	"testing"
)

func Test_AssignAttributesToCategory(t *testing.T) {
	categoryRepo := mage_entity.NewCategoryRepository()
	rootCat, err := categoryRepo.Get(2)
	if err != nil {
		t.Fatal(err)
	}

	// Assign attributes to root category
	if rootCat.EntityID != 2 {
		t.Fatal("Expected root category")
	}
}

func Test_CategoryCaching(t *testing.T) {
	categoryRepo := mage_entity.NewCategoryRepository()
	cat, err := categoryRepo.Get(
		1516,
		"name",
		"description",
	)

	if err != nil {
		t.Fatal(err)
	}

	if !cat.Validate() {
		t.Fatal("Expected valid category")
	}

	if cat.GetAttributeValue("description") == "" {
		t.<PERSON>al("Expected description")
	}

	cat2, err2 := categoryRepo.Get(
		1516,
		"name",
		"description",
		"thumbnail",
	)
	if err2 != nil {
		t.Fatal(err2)
	}

	if !cat2.Validate() {
		t.Fatal("Expected valid category")
	}

	if cat2.GetAttributeValue("description") == "" {
		t.Fatal("Expected description")
	}

	if cat2.GetAttributeValue("thumbnail") == "" {
		t.Fatal("Expected thumbnail")
	}

	_, err = categoryRepo.GetCached(1516, "name", "description", "thumbnail", "banner_url")
	var err3 *storage.MissingFieldsError
	if !errors.As(err, &err3) {
		t.Fatal(err)
	}

	if len(err3.Fields) != 1 {
		t.Fatal("Expected 1 missing field")
	} else if err3.Fields[0] != "banner_url" {
		t.Fatal("Expected banner_url to be missing")
	}

}
