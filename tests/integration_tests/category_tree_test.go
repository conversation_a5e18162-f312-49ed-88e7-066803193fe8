package integration_tests

import (
	"encoding/json"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	mega_menu "praktis.bg/store-api/packages/magento-core/mega-menu"
	"testing"
)

func validateTreeNode(tree *mega_menu.CategoryTree) bool {
	if tree == nil {
		return false
	}

	if tree.RootNode == nil {
		return false
	}

	if len(tree.RootNode.Children) < 1 {
		return false
	}

	var hasChildren bool
	for _, child := range tree.RootNode.Children {
		if len(child.Children) < 1 {
			hasChildren = true
			break
		}
	}

	if !hasChildren {
		return false
	}

	return true
}

func Test_GetCategoryTree(t *testing.T) {
	store := magento_core.GetStoreClient().GetStore()
	tree, err := mega_menu.LoadCategoryTree(
		store,
		[]mage_entity.AttributeCode{"url_path", "name", "thumbnail"},
	)
	if err != nil {
		t.<PERSON>al(err)
	}

	if tree == nil {
		t.Fatal("Tree is nil")
	}

	if tree.RootNode == nil {
		t.Fatal("RootNode is nil")
	}

	if len(tree.RootNode.Children) < 1 {
		t.Fatal("RootNode.Children is empty")
	}

	var hasChildren bool
	for _, child := range tree.RootNode.Children {
		if len(child.Children) < 1 {
			hasChildren = true
			break
		}
	}

	if !hasChildren {
		t.Fatal("RootNode.Children.Children is empty")
	}
}

func Benchmark_CategoryTreeToJson(t *testing.B) {
	store := magento_core.GetStoreClient().GetStore()
	tree, err := mega_menu.LoadCategoryTree(
		store,
		[]mage_entity.AttributeCode{"url_path", "name", "thumbnail"},
	)

	t.ResetTimer()
	for i := 0; i < t.N; i++ {
		var val []byte
		val, err = json.Marshal(tree)
		if err != nil {
			t.Fatal(err)
		}

		if len(val) < 1 {
			t.Fatal("JSON is empty")
		}

		valS := string(val)
		if valS == "" {
			t.Fatal("JSON is empty")
		}

		tree := &mega_menu.CategoryTree{}
		err = json.Unmarshal(val, tree)
		if err != nil {
			t.Fatal(err)
		}
		if !validateTreeNode(tree) {
			t.Fatal("Invalid tree")
		}
	}
}
