package integration_tests

import (
	"context"
	"flag"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"github.com/siper92/core-utils/config_utils"
	"os"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"testing"
)

// go test -v -db=praktis_20231006
func TestMain(m *testing.M) {
	core_utils.AllowDebug()
	core_utils.AllowNotice()

	database := ""
	flag.StringVar(&database, "db", "", "Test Database.")
	flag.Parse()

	initOptions := magento_core.InitMagentoOptions{
		Mode:      "test",
		StoreCode: "bg",
		Cache: config_utils.RedisConfig{
			Host:     "localhost:33421",
			Database: 0,
			Pass:     "",
		},
		Database: struct {
			DSN string
		}{DSN: fmt.Sprintf(
			"root:root@tcp(localhost:33420)/%s?charset=utf8mb4&parseTime=True&loc=Local",
			database, // change this to your database name
		)},
	}
	err := magento_core.InitStoreClient(initOptions)

	redis := magento_core.GetStoreClient().GetCacheClient().Client()
	ctx := context.Background()
	core_utils.Notice("Flushing redis cache")
	redis.FlushAll(ctx)

	core_utils.StopOnError(err)
	exitCode := m.Run()
	os.Exit(exitCode)
}
