package integration_tests

import (
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"testing"
)

func Test_LoadAttributesAndUseCache(t *testing.T) {
	repo := mage_entity.AttributeRepository{}
	attrCodes := []string{"url_path", "name", "image"}
	attributes := repo.GetAttributes(
		mage_entity.CategoryEntityType,
		attrCodes,
	)

	if len(attributes) < len(attrCodes) {
		t.Error("attributes are empty")
	}

	for _, attr := range attributes {
		testAttribute(attr, t)
	}

	for _, attrCode := range attrCodes {
		a := repo.GetAttributeCache(mage_entity.CategoryEntityType, attrCode)
		testAttribute(a, t)
	}
}

func testAttribute(a *mage_entity.CoreAttribute, t *testing.T) {
	if a == nil {
		t.Error("Attr is nil")
	} else if a.AttributeCode == "" {
		t.Error("Attr code is empty")
	} else if a.BackendType == "" {
		t.Error("Attr:" + a.AttributeCode + " backend type is empty")
	} else if a.FrontendLabel == "" {
		t.Error("Attr:" + a.AttributeCode + " front label is empty")
	} else if a.EntityType != mage_entity.CategoryEntityType {
		t.Error("Attr:" + a.AttributeCode + " entity type is wrong")
	}
}
