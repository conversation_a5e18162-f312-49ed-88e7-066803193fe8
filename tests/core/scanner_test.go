package core

import (
	"errors"
	"reflect"
	"testing"
)

type AE struct {
	Attributes map[string]string
}

func (ae *AE) SetData(m map[string]string) {
	ae.Attributes = m
}

type AttributeEntity interface {
	SetData(m map[string]string)
	//GetData(key string) string
}

func mapEntity(data map[string]string, dest AttributeEntity) error {
	dest.SetData(data)
	return nil
}

func mapEntities(data []map[string]string, dest interface{}) error {
	destRef := reflect.ValueOf(dest)
	if destRef.Kind() != reflect.Ptr || destRef.Elem().Kind() != reflect.Slice {
		return errors.New("dest must be a pointer to a slice")
	}

	slice := destRef.Elem()
	elemType := slice.Type().Elem()
	if elemType.Kind() != reflect.Ptr {
		return errors.New("dest must be a pointer to a slice of pointers")
	}

	for _, item := range data {
		newElem := reflect.New(elemType.Elem()).Interface().(AttributeEntity)
		newElem.SetData(item)
		v := reflect.ValueOf(newElem)
		slice = reflect.Append(slice, v)
	}

	destRef.Elem().Set(slice)
	return nil
}

func TestScannerEntities(t *testing.T) {
	entities := []AE{}
	data2 := []map[string]string{
		{
			"entity_id": "1",
			"name":      "test",
		},
		{
			"entity_id": "2",
			"name":      "test2",
			"test":      "test",
		},
		{
			"entity_id": "3",
		},
	}

	err := mapEntities(data2, &entities)
	if err != nil {
		t.Fatal(err)
	}

	if len(entities) != 3 {
		t.Fatal("Expected 3 entities")
	}

	if entities[0].Attributes["entity_id"] != "1" {
		t.Fatal("Expected entity_id to be 1")
	} else if entities[1].Attributes["entity_id"] != "2" {
		t.Fatal("Expected entity_id to be 2")
	} else if entities[2].Attributes["entity_id"] != "3" {
		t.Fatal("Expected entity_id to be 3")
	}
}

func BenchmarkScannerEntities(t *testing.B) {
	t.ResetTimer()
	for i := 0; i < t.N; i++ {
		entities := []*AE{}
		data2 := []map[string]string{
			{
				"entity_id": "1",
				"name":      "test",
			},
			{
				"entity_id": "2",
				"name":      "test2",
				"test":      "test",
			},
			{
				"entity_id": "3",
			},
		}
		err := mapEntities(data2, &entities)
		if err != nil {
			t.Fatal(err)
		}

		if len(entities) != 3 || entities[0].Attributes["entity_id"] != "1" ||
			entities[1].Attributes["entity_id"] != "2" ||
			entities[2].Attributes["entity_id"] != "3" {
			t.Fatal("Expected 3 entities")
		}
	}
}

func mapRawMap(data []map[string]string, dest *[]*AE) error {
	for _, item := range data {
		newElem := &AE{}
		newElem.SetData(item)
		*dest = append(*dest, newElem)
	}

	return nil
}

func BenchmarkRawScannerEntities(t *testing.B) {
	t.ResetTimer()
	for i := 0; i < t.N; i++ {
		entities := []*AE{}
		data2 := []map[string]string{
			{
				"entity_id": "1",
				"name":      "test",
			},
			{
				"entity_id": "2",
				"name":      "test2",
				"test":      "test",
			},
			{
				"entity_id": "3",
			},
		}
		err := mapRawMap(data2, &entities)
		if err != nil {
			t.Fatal(err)
		}

		if len(entities) != 3 || entities[0].Attributes["entity_id"] != "1" ||
			entities[1].Attributes["entity_id"] != "2" ||
			entities[2].Attributes["entity_id"] != "3" {
			t.Fatal("Expected 3 entities")
		}
	}
}

func TestScanner(t *testing.T) {
	ae := &AE{}
	data := map[string]string{
		"entity_id": "1",
		"name":      "test",
	}

	err := mapEntity(data, ae)
	if err != nil {
		t.Fatal(err)
	}

	if ae.Attributes["entity_id"] != "1" {
		t.Fatal("Expected entity_id to be 1")
	}
}
