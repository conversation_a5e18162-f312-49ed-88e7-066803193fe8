package core

import "testing"

type Test struct {
	ID   int
	Name string
}

type ISetData interface {
	SetData(m map[string]interface{})
}

func (t *Test) SetData(m map[string]interface{}) {
	for k, v := range m {
		switch k {
		case "id":
			t.ID = v.(int)
		case "name":
			t.Name = v.(string)
		}
	}
}

func Test_SetData(t *testing.T) {
	var val Test

	callSetData(map[string]interface{}{
		"id":   1,
		"name": "test",
	}, &val)

	if val.ID != 1 {
		t.Fatal("Expected ID to be 1")
	}

	if val.Name != "test" {
		t.Fatal("Expected Name to be test")
	}
}

func callSetData(m map[string]interface{}, t ISetData) {
	t.SetData(m)
}
