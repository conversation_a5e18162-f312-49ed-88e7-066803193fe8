package performance_tests

import (
	"encoding/json"
	"praktis.bg/store-api/packages/magento-core/mage-store"
	"testing"
)

func BenchmarkJSONMarshalUnmarshal(b *testing.B) {
	cat := &mage_store.CategoryEntity{
		EntityID: 1,
		Name:     "Electronics",
		UrlPath:  "/electronics",
		Path:     "/root/electronics",
		Position: 1,
		Level:    2,
	}

	b.<PERSON>setTimer()

	for i := 0; i < b.N; i++ {
		data, _ := json.Marshal(cat)
		_ = json.Unmarshal(data, &mage_store.CategoryEntity{})
	}
}

func BenchmarkCacheObjLoadCacheMap(b *testing.B) {
	cat := &mage_store.CategoryEntity{
		EntityID: 1,
		Name:     "Electronics",
		UrlPath:  "/electronics",
		Path:     "/root/electronics",
		Position: 1,
		Level:    2,
	}

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		cacheMap := cat.CacheObj()
		cat.LoadCacheMap(cacheMap)
	}
}
