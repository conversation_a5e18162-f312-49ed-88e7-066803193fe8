package types

import (
	"github.com/siper92/api-base/cache"
)

type EntityType string

const (
	ProductEntityType         EntityType = "catalog_product"
	CategoryEntityType        EntityType = "catalog_category"
	CustomerEntityType        EntityType = "customer"
	CustomerAddressEntityType EntityType = "customer_address"
)

func (e EntityType) String() string {
	return string(e)
}

type AttributesData map[string]string

type EntityData map[string]interface{}

type ISetData interface {
	SetData(m EntityData)
}

type ValueGetter interface {
	Get(key string) interface{}
}

type IGetData interface {
	GetData(keys ...string) EntityData
}

type IAttributeEntity interface {
	GetAttributeValue(key string) string
}

type ILoadableEntity interface {
	Load(id EntityID, attrs ...string) error
}

type IInitializableEntity interface {
	New() IEntity
	NewSlice() []IEntity
}

type ICopyableEntity[T IEntity] interface {
	copyTo(T) error
}

type Validatable interface {
	Validate() error
	IsValid() bool
}

type IEntity interface {
	ISetData
	IGetData
	GetID() EntityID
	New() IEntity
	NewSlice() []IEntity
	TableName() string
}

type IMageEntity interface {
	IEntity
	IInitializableEntity
	cache.CacheableObject
	GetEntityType() EntityType
	SaveInCache() bool
}
