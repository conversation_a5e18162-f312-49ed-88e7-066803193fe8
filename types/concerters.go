package types

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"strconv"
	"time"
)

func ToEntityID(d interface{}) EntityID {
	if d == nil {
		return 0
	}

	switch v := d.(type) {
	case int:
		if v > 0 {
			return EntityID(v)
		}
	case int32:
		if v > 0 {
			return EntityID(v)
		}
	case int64:
		if v > 0 && v < 1<<32-1 {
			return EntityID(v)
		}
	case uint32:
		return EntityID(v)
	case uint64:
		if v < 1<<32-1 {
			return EntityID(v)
		}
	case string:
		converted, _ := strconv.ParseUint(v, 10, 32)
		if converted > 0 {
			return ToEntityID(converted)
		}
	default:
		core_utils.Warning("Cannot convert type %T to EntityID", v)
		return 0
	}

	core_utils.Warning("Cannot convert %T %v to EntityID", d, d)
	return 0
}

func ToInt(d interface{}) int {
	if d == nil {
		return 0
	}

	switch v := d.(type) {
	case int:
		return v
	case string:
		if v == "" || v == "0" {
			return 0
		}

		converted, _ := strconv.ParseUint(v, 10, 32)
		if converted > 0 {
			return int(converted)
		}
	case int64:
		if v > 0 && v < 1<<32-1 {
			return int(v)
		}
	case int32:
		return int(v)
	case uint32:
		return int(v)
	case float64:
		return int(v)
	case float32:
		return int(v)
	case uint64:
		if v < 1<<32-1 {
			return int(v)
		}
	}

	core_utils.Debug("Cannot convert %T %v to int", d, d)
	return 0
}

func ToInt64(d interface{}) int64 {
	if d == nil {
		return 0
	}

	switch v := d.(type) {
	case int64:
		return v
	case string:
		if v == "" || v == "0" {
			return 0
		}

		converted, err := strconv.ParseInt(v, 10, 32)
		core_utils.ErrorWarning(err)
		return converted
	case int:
		return int64(v)
	case int32:
		return int64(v)
	case float64:
		return int64(v)
	case float32:
		return int64(v)
	case *int64:
		if v == nil {
			return 0
		}

		return *v
	case *int:
		if v == nil {
			return 0
		}
		return int64(*v)
	case *string:
		if v == nil {
			return 0
		}
		return ToInt64(*v)
	case *float64:
		if v == nil {
			return 0
		}
		return int64(*v)
	case *float32:
		if v == nil {
			return 0
		}
		return int64(*v)
	case uint32:
		return int64(v)
	case uint64:
		return int64(v)
	}

	core_utils.Warning("Cannot convert %T %v to int", d, d)
	return 0
}

func ToStr(d interface{}) string {
	if d == nil {
		return ""
	}

	// Fast path for most common types (ordered by likely frequency)
	switch v := d.(type) {
	case string:
		return v
	case int64:
		return strconv.FormatInt(v, 10)
	case int:
		return strconv.Itoa(v)
	case float64:
		return strconv.FormatFloat(v, 'f', -1, 64)
	case bool:
		return strconv.FormatBool(v)
	case time.Time:
		return v.Format(time.RFC3339)
	case []byte:
		return string(v)
	case float32:
		return strconv.FormatFloat(float64(v), 'f', -1, 32)
	case *string:
		if v != nil {
			return *v
		}
	case *int:
		if v != nil {
			return strconv.Itoa(*v)
		}
	case *int64:
		if v != nil {
			return strconv.FormatInt(*v, 10)
		}
	case *time.Time:
		if v != nil {
			return v.Format(time.RFC3339)
		}
	case fmt.Stringer:
		return v.String()
	// Less common numeric types
	case uint:
		return strconv.FormatUint(uint64(v), 10)
	case int32:
		return strconv.FormatInt(int64(v), 10)
	case int16:
		return strconv.FormatInt(int64(v), 10)
	case int8:
		return strconv.FormatInt(int64(v), 10)
	case uint64:
		return strconv.FormatUint(v, 10)
	case uint32:
		return strconv.FormatUint(uint64(v), 10)
	case uint16:
		return strconv.FormatUint(uint64(v), 10)
	case uint8:
		return strconv.FormatUint(uint64(v), 10)
	case *int32:
		if v != nil {
			return strconv.FormatInt(int64(*v), 10)
		}
	case *int16:
		if v != nil {
			return strconv.FormatInt(int64(*v), 10)
		}
	case *int8:
		if v != nil {
			return strconv.FormatInt(int64(*v), 10)
		}
	case *uint:
		if v != nil {
			return strconv.FormatUint(uint64(*v), 10)
		}
	case *uint64:
		if v != nil {
			return strconv.FormatUint(*v, 10)
		}
	case *uint32:
		if v != nil {
			return strconv.FormatUint(uint64(*v), 10)
		}
	case *uint16:
		if v != nil {
			return strconv.FormatUint(uint64(*v), 10)
		}
	case *uint8:
		if v != nil {
			return strconv.FormatUint(uint64(*v), 10)
		}
	case *float64:
		if v != nil {
			return strconv.FormatFloat(*v, 'f', -1, 64)
		}
	case *float32:
		if v != nil {
			return strconv.FormatFloat(float64(*v), 'f', -1, 32)
		}
	case *bool:
		if v != nil {
			return strconv.FormatBool(*v)
		}
	default:
		core_utils.Debug("Cannot convert type %T to int", v)
		return fmt.Sprintf("%v", v)
	}

	return ""
}

func ToFloat(d interface{}) float64 {
	if d == nil {
		return 0
	}

	switch v := d.(type) {
	case string:
		converted, _ := strconv.ParseFloat(v, 64)
		return converted
	case int:
		return float64(v)
	case float64:
		return v
	case int32, uint32, uint64, int8, int16, uint8, uint16, int64:
		return float64(ToInt64(v))
	case float32:
		return float64(v)
	case bool:
		return 0
	default:
		core_utils.Warning("Cannot convert %T %v to float64", d, d)
	}

	return 0
}

func ToBool(s interface{}) bool {
	switch v := s.(type) {
	case string:
		converted, _ := strconv.ParseBool(v)
		return converted
	case int, int32, int64, *int, *int64:
		return ToInt(v) > 0
	case bool:
		return v
	default:
		return false
	}
}

func ToTime(d interface{}) string {
	if d == nil {
		return ""
	}

	switch v := d.(type) {
	case string:
		return v
	default:
		core_utils.Warning("Cannot convert %T %v to string", d, d)
	}

	return ""
}

func DateToString(d *time.Time) string {
	if d == nil {
		return ""
	}

	return d.Format(time.RFC3339)
}

func ToDateTime(d interface{}) time.Time {
	var timeVal time.Time
	var err error
	if d == nil {
		return time.Time{}
	}

	switch v := d.(type) {
	case string:
		timeVal, err = time.Parse(time.RFC3339, v)
		if err != nil {
		}
		return timeVal
	case time.Time:
		timeVal = v
	case int, float64, *int, *float64:
		return ToDateTime(ToInt64(v))
	case int64:
		timeVal = time.Unix(v, 0)
	case *string:
		return ToDateTime(*v)
	case *time.Time:
		timeVal = *v

	}

	core_utils.Warning("Cannot convert %T %v to string - %s", d, d, err)
	return time.Time{}
}
