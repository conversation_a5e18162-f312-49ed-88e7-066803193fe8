package types

import (
	"fmt"
	"gorm.io/gorm"
)

type EntityID uint32

func (e EntityID) String() string {
	return fmt.Sprintf("%d", e)
}

func (e EntityID) Int() int {
	return int(e)
}

func (e EntityID) Int64() int64 {
	return int64(e)
}

type IDType interface {
	string | EntityID
}

type ISimpleRepository[E any, K IDType] interface {
	Get(identifier K) (E, error)
	DB() *gorm.DB
}

type IAttributeRepository[T any] interface {
	Get(id EntityID, attrs ...string) (T, error)
}
