package magento_core

import (
	"errors"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	"time"
)

type ConfigValuePath string

func (c ConfigValuePath) getCacheKey() string {
	return fmt.Sprintf("cnf:%s", c)
}

func (c ConfigValuePath) getDefaultConfigCacheTTL() time.Duration {
	return time.Hour * 1
}

type ConfigData struct {
	Id      int `gorm:"column:config_id"`
	StoreId int
	Value   string
	Scope   string
}

const configDataTable = "core_config_data"

func (ConfigData) TableName() string {
	return configDataTable
}

type GetParams struct {
	Default string
	Path    ConfigValuePath
}

func (s *StoreEntity) GetConfig(path ConfigValuePath, defaultV string) string {
	cacheKey := path.getCacheKey()
	if s.GetID() > 0 {
		cacheKey = fmt.Sprintf("str[%d]:%s", s.GetID(), cacheKey)
	}

	cacheClient := GetStoreClient().GetCacheClient()
	if cacheClient.MustExists(cacheKey) {
		val, err := cacheClient.Get(cacheKey)
		if err == nil {
			return val
		}
	}

	var res []ConfigData
	err := GetStoreClient().DbConn().
		Table(configDataTable).
		Where("path = ?", string(path)).
		Where(
			`(scope = 'default' OR (scope = 'stores' AND scope_id = ?))`,
			s.GetID(),
		).Order("scope_id DESC").Scan(&res).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) == false {
			core_utils.ErrorWarning(err)
			return defaultV
		}
	}

	result := defaultV
	if len(res) > 0 {
		for _, v := range res {
			if v.Scope == "stores" {
				result = v.Value
				break
			}
			result = v.Value
		}
	}

	err = cacheClient.Save(cacheKey, result, path.getDefaultConfigCacheTTL())
	core_utils.ErrorWarning(err)

	return result
}

func (s *StoreEntity) GetConfigP(path ConfigValuePath, defaultV string) *string {
	val := s.GetConfig(path, defaultV)
	return &val
}

func (s *StoreEntity) GetConfigInt(path ConfigValuePath, defaultV int) int {
	val := s.GetConfig(path, core_utils.IntToString(defaultV))
	return core_utils.StringToInt(val)
}

func (s *StoreEntity) GetConfigBool(path ConfigValuePath, defaultV bool) bool {
	defaultVStr := "0"
	if defaultV {
		defaultVStr = "1"
	}
	val := s.GetConfig(path, defaultVStr)
	return val == "1" || val == "true" || val == "yes" || val == "on"
}

func (s *StoreEntity) GetConfigIntP(path ConfigValuePath, defaultV int) *int {
	val := s.GetConfigInt(path, defaultV)
	return &val
}
