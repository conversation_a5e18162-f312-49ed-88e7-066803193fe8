package mage_types

import (
	"errors"
	"fmt"
	"github.com/siper92/api-base/cache"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
	"strings"
)

// #########################################

type EntityData map[string]string

type LoadableEntity interface {
	Load(attrs ...string) error
}

type EntityValueSetter interface {
	SetVal(key string, value interface{}) error
}

type EntityValueGetter interface {
	GetVal(key string) (string, error)
	GetData(attrs ...string) (EntityData, error)
}

type EntityValueProvider interface {
	EntityValueGetter
	EntityValueSetter
}

type CacheProvider interface {
	Cache() *cache.RedisCacheProvider
}

type DBProvider interface {
	DB() *gorm.DB
}

type Entity[E IDProvider] interface {
	schema.Tabler
	cache.CacheableObject
	CacheProvider
	Initializable[E]
	//DBProvider
	//LoadableEntity
	EntityValueGetter
	EntityValueSetter
}

var FieldNotFound = errors.New("no value found")

type FieldError struct {
	Err      error
	FieldKey string
}

func (f *FieldError) Error() string {
	return fmt.Sprintf("field '%s': %v", f.FieldKey, f.Err)
}

func (f *FieldError) Is(target error) bool {
	return target == FieldNotFound
}

func NewFieldError(key string) error {
	return &FieldError{
		Err:      FieldNotFound,
		FieldKey: key,
	}
}

type FieldErrors map[string]error

// Error implements the error interface
func (fe FieldErrors) Error() string {
	var msgs []string
	for field, err := range fe {
		msgs = append(msgs, fmt.Sprintf("%s: %v", field, err))
	}
	return fmt.Sprintf("failed to get fields: %s", strings.Join(msgs, "; "))
}
