package mage_types

import (
	"gorm.io/gorm"
)

type IDProvider interface {
	GetID() int64
}

type Loadable[T IDProvider] interface {
	Load() (T, error)
}

type CRUDEntity[T IDProvider] interface {
	Save() (T, error)
}

type IsValid interface {
	Validate() error
	IsValid() bool
}

type Initializable[T IDProvider] interface {
	New() T
	NewSlice() []T
}

type StoreEntity[T IDProvider] interface {
	IDProvider
	IsValid
	Initializable[T]
	CRUDEntity[T]
	TableName() string
}

type TransactionAware[T any] interface {
	SetTransaction(tx *gorm.DB) T
}

type DbRepository interface {
	DB() *gorm.DB
}

type EntityRepository[E StoreEntity[E]] interface {
	DbRepository
	Save(entity E) (E, error)
	GetByID(id int64) (E, error)
}
