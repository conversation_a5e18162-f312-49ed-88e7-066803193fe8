package mage_store

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"

	"strconv"
)

func GetEntityTypeID(e types.EntityType) int {
	key := fmt.Sprintf("core_entity_type_id_%s", e)
	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	entityTypeId, _ := cacheClient.Get(key)

	if entityTypeId == "" {
		var _entityTypeId int
		magento_core.GetStoreClient().DbConn().
			Table("eav_entity_type").
			Select("entity_type_id").
			Where("entity_type_code = ?", e).Scan(&_entityTypeId)
		err := cacheClient.Save(key, _entityTypeId, cache.InfiniteTTL)
		core_utils.StopOnError(err)
		entityTypeId = strconv.Itoa(_entityTypeId)
	}

	id, err := strconv.Atoi(entityTypeId)
	core_utils.ErrorWarning(err)

	if id < 1 {
		panic("could not load entity type id: " + e.String())
	}

	return id
}
