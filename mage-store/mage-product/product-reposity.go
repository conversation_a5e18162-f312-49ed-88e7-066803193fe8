package mage_product

import (
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"strconv"
	"sync"
)

type IProductRepository interface {
	mage_types.CacheProvider
	mage_types.DBProvider
	GetID(id int64, attrs []string) (*ProductEntity, error)
	GetSKU(sku string, attrs []string) (*ProductEntity, error)
	GetIDs(ids []int64, attrs []string) ([]*ProductEntity, error)
	GetSKUs(sku []string, attrs []string) ([]*ProductEntity, error)
	GetProductAttributesValueChannel(
		id int64,
		attributes []string,
		storeID int,
	) (chan map[string]string, chan error)
	GetMassProductAttributesValueChannel(
		ids []int64,
		attributes []string,
		storeID int,
	) (chan map[int64]map[string]string, chan error)
}

var _ IProductRepository = (*ProductRepository)(nil)

type ProductRepository struct {
	lock sync.Mutex
}

func (r *ProductRepository) DB() *gorm.DB {
	return magento_core.GetStoreClient().DbConn()
}

func (r *ProductRepository) Cache() *cache.RedisCacheProvider {
	storeClient := magento_core.GetStoreClient()
	return storeClient.GetCacheClient()
}

func NewProductRepository() *ProductRepository {
	return &ProductRepository{}
}

func (r *ProductRepository) newProduct(
	id int64,
	attrs []string,
) (*ProductEntity, []string, error) {
	product := &ProductEntity{
		EntityID:   id,
		Attributes: map[string]string{},
	}

	toLoad, err := product.LoadFromCache(attrs)
	if err != nil {
		return nil, toLoad, err
	} else if product.isFullCacheLoad {
		return product, []string{}, nil
	}

	return product, toLoad, nil
}

func (r *ProductRepository) GetID(id int64, attrs []string) (*ProductEntity, error) {
	if len(attrs) == 0 {
		attrs = ProductFieldsKeys
	}

	loadProduct := false
	product, toLoad, err := r.newProduct(id, attrs)
	core_utils.ErrorWarning(err)

	if len(toLoad) > 0 {
		_toLoad := make([]string, 0, len(toLoad))
		for _, t := range toLoad {
			if product.AttributeIsField(t) {
				loadProduct = true
			} else {
				_toLoad = append(_toLoad, t)
			}
		}
		toLoad = _toLoad
	}

	if loadProduct == false && len(toLoad) == 0 {
		return product, nil
	}

	var productErr error
	productDone := make(chan struct{})
	if loadProduct {
		go func() {
			defer close(productDone)
			productErr = r.DB().Model(&ProductEntity{}).
				Find(product, id).Error
		}()
	} else {
		close(productDone)
	}

	attrsChan := make(chan map[string]string)
	attrErrChan := make(chan error, 1)

	if len(toLoad) > 0 {
		attrsChan, attrErrChan = r.GetProductAttributesValueChannel(
			id,
			toLoad,
			product.GetStoreID(),
		)
	} else {
		close(attrsChan)
		close(attrErrChan)
	}

	<-productDone
	if productErr != nil {
		for range attrsChan {
		}
		for range attrErrChan {
		}
		return nil, productErr
	}

	if product != nil {
		for attrsTableData := range attrsChan {
			for k, v := range attrsTableData {
				product.Attributes[k] = v
			}
		}

		if attrErr := <-attrErrChan; attrErr != nil {
			return nil, attrErr
		}

		if loadProduct {
			err = product.SaveInCache()
		} else {
			err = product.SaveAttributes(toLoad...)
		}
	}

	return product, err
}

func (r *ProductRepository) GetSkuID(sku string) (int64, error) {
	cacheKey := (&ProductEntity{Sku: sku}).GetSkuIDMapKey()
	var id int64
	if r.Cache().MustExists(cacheKey) {
		val, err := r.Cache().Get(cacheKey)
		if err != nil {
			return 0, err
		}

		return strconv.ParseInt(val, 10, 64)
	} else {
		err := r.DB().Model(&ProductEntity{}).
			Where("sku = ?", sku).
			Select("entity_id").
			Scan(&id).Error
		if err != nil {
			return 0, err
		}

		err = r.Cache().Save(cacheKey, id, 0)

		return id, err
	}
}

func (r *ProductRepository) GetSKU(sku string, attrs []string) (*ProductEntity, error) {
	id, err := r.GetSkuID(sku)
	if err != nil {
		return nil, err
	}

	return r.GetID(id, attrs)
}

func (r *ProductRepository) GetIDs(ids []int64, attrs []string) ([]*ProductEntity, error) {
	if len(attrs) == 0 {
		attrs = ProductFieldsKeys
	}

	dataCh := make(chan struct {
		index int
		id    int64
		data  map[string]string
		err   error
	})

	go func() {
		for i, id := range ids {
			data, err := (&ProductEntity{
				EntityID: id,
			}).loadCacheData()

			dataCh <- struct {
				index int
				id    int64
				data  map[string]string
				err   error
			}{index: i, id: id, data: data, err: err}
		}
		close(dataCh)
	}()

	result := make([]*ProductEntity, len(ids))
	idToResultIndex := make(map[int64]int)
	loadProductIds := make(map[int64]struct{})
	attrToLoad := map[string]struct{}{}
	for item := range dataCh {
		if item.err != nil {
			core_utils.ErrorWarning(item.err)
			continue
		}

		p := &ProductEntity{EntityID: item.id}
		for _, a := range attrs {
			if v, ok := item.data[a]; ok {
				core_utils.DebugError(
					p.SetVal(a, v),
				)
			} else {
				if p.AttributeIsField(a) {
					loadProductIds[p.EntityID] = struct{}{}
				} else {
					attrToLoad[a] = struct{}{}
				}
			}
		}

		result[item.index] = p
		idToResultIndex[p.EntityID] = item.index
	}

	if len(attrToLoad) == 0 && len(loadProductIds) == 0 {
		return result, nil
	}

	var productErr error
	productsDone := make(chan struct{})
	if len(loadProductIds) > 0 {
		go func() {
			products := make([]*ProductEntity, 0) // Initialize with a non-nil slice
			defer close(productsDone)
			ids = []int64{}
			for id, _ := range loadProductIds {
				ids = append(ids, id)
			}

			productErr = r.DB().Model(&ProductEntity{}).
				Find(&products, ids).Error

			if productErr == nil {
				for _, p := range products {
					if index, ok := idToResultIndex[p.EntityID]; ok {
						result[index] = p
					}
				}
			}
		}()
	} else {
		close(productsDone)
	}

	_toLoad := make([]string, 0, len(attrToLoad))
	for a := range attrToLoad {
		_toLoad = append(_toLoad, a)
	}

	attrsChan := make(chan map[int64]map[string]string)
	attrErrChan := make(chan error, 1)

	if len(attrToLoad) > 0 {
		attrsChan, attrErrChan = r.GetMassProductAttributesValueChannel(
			ids,
			_toLoad,
			(&ProductEntity{}).GetStoreID(),
		)
	} else {
		close(attrsChan)
		close(attrErrChan)
	}

	<-productsDone
	if productErr != nil {
		for range attrsChan {
		}
		for range attrErrChan {
		}
		return nil, productErr
	}

	for attrsTableData := range attrsChan {
		for productID, values := range attrsTableData {
			if index, ok := idToResultIndex[productID]; ok {
				err := result[index].SetCacheObject(values)
				core_utils.ErrorWarning(err)
			}
		}
	}

	if attrErr := <-attrErrChan; attrErr != nil {
		return nil, attrErr
	}

	var err error
	for _, res := range result {
		if _, ok := loadProductIds[res.EntityID]; ok {
			err = res.SaveInCache()
		} else {
			err = res.SaveAttributes(_toLoad...)
		}
		core_utils.ErrorWarning(err)
	}

	return result, nil
}

func (r *ProductRepository) GetIDsBySkus(skus []string) ([]int64, error) {
	ids := make([]int64, 0, len(skus))
	toGet := make([]string, 0, len(skus))
	for _, sku := range skus {
		cacheKey := productSkuMapKey(sku)
		if r.Cache().MustExists(cacheKey) {
			val, err := r.Cache().Get(cacheKey)
			core_utils.ErrorWarning(err)
			if val != "" {
				id, err := strconv.ParseInt(val, 10, 64)
				core_utils.ErrorWarning(err)
				if id > 0 {
					ids = append(ids, id)
					continue
				}
			}
		} else {
			toGet = append(toGet, sku)
		}
	}

	if len(toGet) > 0 {
		var loaded []struct {
			EntityID int64
			Sku      string
		}
		err := r.DB().Model(&ProductEntity{}).
			Where("sku in ?", toGet).
			Select("entity_id, sku").
			Scan(&loaded).Error
		if err != nil {
			return ids, err
		}

		for _, d := range loaded {
			ids = append(ids, d.EntityID)

			cacheKey := productSkuMapKey(d.Sku)
			err = r.Cache().Save(cacheKey, d.EntityID, cache.InfiniteTTL)
			core_utils.ErrorWarning(err)
		}
	}

	return ids, nil
}

func (r *ProductRepository) GetSKUs(skus []string, attrs []string) ([]*ProductEntity, error) {
	ids, err := r.GetIDsBySkus(skus)
	if err != nil {
		return nil, err
	}

	return r.GetIDs(ids, attrs)
}
