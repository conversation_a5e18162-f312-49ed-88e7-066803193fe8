package mage_product

import (
	"errors"
	"fmt"
	"praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"strings"
	"time"
)

func (p *ProductEntity) GetVal(key string) (string, error) {
	_key := strings.ToLower(key)
	switch _key {
	case "entity_id", "id", "entityid":
		return strconv.FormatInt(p.EntityID, 10), nil
	case "type_id", "typeid":
		return string(p.TypeID), nil
	case "sku":
		return p.Sku, nil
	case "entity_type_id", "entitytypeid":
		return strconv.FormatInt(p.EntityTypeID, 10), nil
	case "created_at", "createdat":
		return p.CreatedAt.Format(time.RFC3339), nil
	case "updated_at", "updatedat":
		return p.UpdatedAt.Format(time.RFC3339), nil
	case "store_id", "storeid":
		return strconv.Itoa(p.GetStoreID()), nil
	case "gallery":
		return galleryToStr(p.GalleryImages), nil
	case "image", "thumbnail":
		return p.Image.String(), nil
	default:
		if p.Attributes != nil {
			if val, ok := p.Attributes[_key]; ok {
				return val, nil
			}
		}
	}

	return "", mage_types.NewFieldError(key)
}

const GallerySeparator = "|"

func galleryToStr(image []GalleryImage) string {
	strGalleryVal := ""
	for _, i := range image {
		if strGalleryVal != "" {
			strGalleryVal += GallerySeparator
		}

		strGalleryVal += i.String()
	}

	return strGalleryVal
}

func strToGallery(strVal string) []GalleryImage {
	parts := strings.Split(strVal, GallerySeparator)
	images := make([]GalleryImage, len(parts))
	for i, part := range parts {
		images[i] = fromStrToGalleryImage(part)
	}

	return images
}

func (p *ProductEntity) GetData(keys ...string) (mage_types.EntityData, error) {
	if len(keys) == 0 {
		res := mage_types.EntityData{
			"entity_id":      strconv.FormatInt(p.EntityID, 10),
			"type_id":        string(p.TypeID),
			"sku":            p.Sku,
			"entity_type_id": strconv.FormatInt(p.EntityTypeID, 10),
			"created_at":     p.CreatedAt.Format(time.RFC3339),
			"updated_at":     p.UpdatedAt.Format(time.RFC3339),
		}

		if len(p.GalleryImages) > 0 {
			res["gallery"] = galleryToStr(p.GalleryImages)
		}
		if p.Image.String() != "!!0" {
			res["image"] = p.Image.String()
		}

		for k, v := range p.Attributes {
			res[k] = v
		}

		return res, nil
	}

	res := make(mage_types.EntityData)
	var notFound []string
	for _, key := range keys {
		val, err := p.GetVal(key)
		if errors.Is(err, mage_types.FieldNotFound) {
			notFound = append(notFound, key)
		} else if err != nil {
			return nil, err
		}

		res[key] = val
	}

	if len(notFound) > 0 {
		return res, fmt.Errorf("no such key: %s", strings.Join(notFound, ","))
	}

	return res, nil
}

func (p *ProductEntity) SetVal(key string, val interface{}) error {
	switch strings.ToLower(key) {
	case "entity_id", "id", "entityid":
		p.EntityID = types.ToInt64(val)
	case "type_id":
		switch v := val.(type) {
		case ProductTypeID:
			p.TypeID = v
		default:
			p.TypeID = ProductTypeID(types.ToStr(val))
		}
	case "sku":
		p.Sku = types.ToStr(val)
	case "entity_type_id", "entitytypeid":
		p.EntityTypeID = types.ToInt64(val)
	case "gallery":
		switch v := val.(type) {
		case GalleryImage:
			p.GalleryImages = append(p.GalleryImages, v)
		case []GalleryImage:
			p.GalleryImages = v
		case string:
			p.GalleryImages = strToGallery(v)
		}
	case "image":
		switch v := val.(type) {
		case string:
			p.Image = fromStrToGalleryImage(v)
		case GalleryImage:
			p.Image = v
		}
	default:
		if p.Attributes == nil {
			p.Attributes = make(mage_types.EntityData)
		}

		p.Attributes[key] = types.ToStr(val)
	}

	return nil
}
