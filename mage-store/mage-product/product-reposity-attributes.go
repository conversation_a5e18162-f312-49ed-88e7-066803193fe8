package mage_product

import (
	"errors"
	"fmt"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/storage"
)

var AttributeNotFound = errors.New("attribute not found")

func (r *ProductRepository) getProductAttributeValue(
	productID int64,
	attr string,
	storeID int,
) (string, error) {
	attrE, err := mage_store.ProductAttributes.GetAttribute(attr)
	if err != nil {
		return "", err
	}

	if attrE.GetID() > 0 {
		var vals map[int64]map[string]string
		vals, err = r.loadIDsAttributes(
			attrE.GetMagentoTable(),
			[]int64{productID},
			[]int{attrE.GetID()},
			storeID,
		)
		if err != nil {
			return "", err
		}

		if v, ok := vals[productID][attr]; ok {
			return v, nil
		}
	}

	return "", nil
}

func (r *ProductRepository) GetProductAttributesValueChannel(
	id int64,
	attributes []string,
	storeID int,
) (chan map[string]string, chan error) {
	attrChan := make(chan map[string]string)
	errChan := make(chan error, 1)

	go func() {
		defer close(attrChan)
		defer close(errChan)

		tables, err := mage_store.ProductAttributes.SplitToByTable(attributes)
		if err != nil {
			errChan <- err
			return
		}

		for table, attrIds := range tables {
			var vals map[int64]map[string]string
			vals, err = r.loadIDsAttributes(table, []int64{id}, attrIds, storeID)
			if err != nil {
				errChan <- err
			}

			if _, ok := vals[id]; ok {
				attrChan <- vals[id]
			}
		}
	}()

	return attrChan, errChan
}

func (r *ProductRepository) GetMassProductAttributesValueChannel(
	ids []int64,
	attributes []string,
	storeID int,
) (chan map[int64]map[string]string, chan error) {
	attrChan := make(chan map[int64]map[string]string)
	errChan := make(chan error, 1)

	go func() {
		defer close(attrChan)
		defer close(errChan)

		tables, err := mage_store.ProductAttributes.SplitToByTable(attributes)
		if err != nil {
			errChan <- err
			return
		}

		for table, attrIds := range tables {
			var vals map[int64]map[string]string
			vals, err = r.loadIDsAttributes(table, ids, attrIds, storeID)
			if err != nil {
				errChan <- err
			}

			attrChan <- vals
		}
	}()

	return attrChan, errChan
}

func (r *ProductRepository) loadIDsAttributes(
	table storage.DbTable,
	ids []int64,
	attrIds []int,
	storeID int,
) (map[int64]map[string]string, error) {
	if len(ids) < 1 {
		return nil, fmt.Errorf("empty ids")
	}

	var result = make(map[int64]map[string]string)

	var values []struct {
		EntityID    int64
		AttributeID int
		Value       string
		StoreID     int
	}

	err := r.DB().Raw(fmt.Sprintf(`select 
    entity_id, attribute_id, value, store_id
from %s where entity_id in (?) and attribute_id in (?) and store_id in (0, ?)`, table),
		ids, attrIds, storeID,
	).Scan(&values).Error
	if err != nil {
		return nil, fmt.Errorf("GetAttributesByEntityIDs: %w", err)
	}

	for _, v := range values {
		if _, ok := result[v.EntityID]; !ok {
			result[v.EntityID] = make(map[string]string)
		}

		attrCode, _ := mage_store.ProductAttributes.GetAttributeCode(v.AttributeID)
		if attrCode != "" {
			if v.StoreID == 0 {
				if _, ok := result[v.EntityID][attrCode]; ok {
					// value for store exists don't override
					continue
				}
			}

			result[v.EntityID][attrCode] = v.Value
		}
	}

	return result, nil
}
