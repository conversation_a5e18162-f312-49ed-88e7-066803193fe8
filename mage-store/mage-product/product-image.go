package mage_product

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/internal/config"
	"praktis.bg/store-api/internal/praktis"
	"strconv"
	"strings"
)

type ProductImage string

func (p ProductImage) GetMedialUrl() string {
	if p == "" {
		return ""
	}

	urlConfig := config.GetConfig().Api.ProductsImageUrl
	url := "/catalog/product" + string(p)
	if urlConfig != "" {
		url = strings.TrimRight(urlConfig, "/") + url
	} else {
		url = praktis.GetPraktisStore().GetMedialUrl(url)
	}

	return url
}

const GalleryImagePartsSeparator = "!"

type GalleryImage struct {
	Value    ProductImage `gorm:"value"`
	Label    string       `gorm:"label"`
	Position int          `gorm:"position"`
}

func (g GalleryImage) String() string {
	return fmt.Sprintf("%s%s%s%s%d",
		g.<PERSON>, GalleryImagePartsSeparator,
		g.Label, GalleryImagePartsSeparator,
		g.Position,
	)
}

func fromStrToGalleryImage(value string) GalleryImage {
	parts := strings.Split(value, GalleryImagePartsSeparator)
	var image GalleryImage
	if len(parts) < 3 {
		return image
	}

	image.Value = ProductImage(parts[0])
	image.Label = parts[1]

	if len(parts) > 2 {
		position, err := strconv.Atoi(parts[2])
		core_utils.ErrorWarning(err)
		image.Position = position
	}

	return image
}

func (p *ProductEntity) GetMedialUrl() string {
	return p.Image.Value.GetMedialUrl()
}

func (p *ProductEntity) setGallery(images []GalleryImage) *ProductEntity {
	minPosition := 1000
	p.Image = GalleryImage{
		Position: minPosition,
	}

	for _, img := range images {
		if img.Position < minPosition {
			minPosition = img.Position
			p.Image = img
		}
	}

	p.GalleryImages = images
	return p
}

func (p *ProductEntity) LoadGallery() error {
	if p == nil || p.EntityID < 1 {
		return fmt.Errorf("product entity not initilized")
	}

	// Clear any existing gallery images to ensure fresh data
	p.GalleryImages = nil

	// Force load from database every time
	db := praktis.GetDbClient()
	images := make([]GalleryImage, 0)

	err := db.Raw(`select value, label, position from catalog_product_entity_media_gallery g
    inner join catalog_product_entity_media_gallery_value go on g.value_id = go.value_id
where entity_id = ? and disabled = 0 and store_id in (0) order by position asc`, p.EntityID).
		Scan(&images).Error
	if err != nil {
		core_utils.ErrorWarning(err)
		return err
	}

	p.setGallery(images)

	return nil
}
