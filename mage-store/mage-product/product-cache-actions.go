package mage_product

import (
	"errors"
	core_utils "github.com/siper92/core-utils"
)

func (p *ProductEntity) SaveInCache() error {
	cacheClient := p.Cache()
	err := cacheClient.SaveObj(p)
	if err == nil {
		skuIdCacheKey := p.GetSkuIDMapKey()
		if cacheClient.MustExists(skuIdCacheKey) == false {
			err2 := cacheClient.Save(skuIdCacheKey, p.EntityID, 0)
			core_utils.ErrorWarning(err2)
		}
	}

	return err
}

func (p *ProductEntity) SaveAttributes(attrs ...string) error {
	for _, attr := range attrs {
		val, err := p.GetVal(attr)
		if err == nil {
			_, err = p.Cache().SetMapValue(p.Cache<PERSON>ey(), attr, val)
			core_utils.DebugError(err)
		}
	}

	return nil
}

func (p *ProductEntity) loadCacheData() (map[string]string, error) {
	cacheKey := p.CacheKey()
	cacheClient := p.Cache()
	if cacheClient.MustExists(cacheKey) == false {
		return map[string]string{}, nil
	}

	data, err := cacheClient.GetMap(cacheKey)
	if err != nil {
		return map[string]string{}, err
	}

	return data, nil
}

func (p *ProductEntity) LoadFromCache(
	attr []string,
) ([]string, error) {
	if len(attr) < 1 {
		return attr, errors.New("no attributes provided")
	}

	p.isFullCacheLoad = false
	data, err := p.loadCacheData()
	if err != nil || len(data) == 0 {
		return attr, err
	}

	toLoad := make([]string, 0)
	for _, a := range attr {
		if v, ok := data[a]; ok {
			core_utils.DebugError(
				p.SetVal(a, v),
			)
		} else if p.AttributeIsField(a) == false {
			toLoad = append(toLoad, a)
		}
	}

	p.isFullCacheLoad = len(toLoad) < 1

	return toLoad, nil
}
