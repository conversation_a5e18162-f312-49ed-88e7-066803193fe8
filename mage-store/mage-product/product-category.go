package mage_product

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
	"strings"
)

func (p *ProductEntity) GetCategoryIDs() ([]types.EntityID, error) {
	return GetProductCategories(p.EntityID)
}

func GetProductCategories(productId int64) (res []types.EntityID, err error) {
	var cats []string
	if magento_core.UseCacheConst {
		cacheClient := magento_core.GetStoreClient().GetCacheClient()
		cacheKey := fmt.Sprintf("product:%d:categories", productId)
		if cacheClient.MustExists(cacheKey) {
			if cats, err = cacheClient.GetSet(cacheKey); err == nil {
				for _, cat := range cats {
					res = append(res, types.ToEntityID(cat))
				}
				return res, nil
			} else {
				core_utils.ErrorWarning(fmt.Errorf("GetProductCategories: %w", err))
			}
		}
	}

	var path string
	db := magento_core.GetStoreClient().DbConn()
	err = db.Raw(`select c.path from catalog_category_product p
    inner join catalog_category_entity c on p.category_id = c.entity_id
         where product_id = ?
order BY (LENGTH(c.path) - LENGTH(REPLACE(c.path, '/', ''))) DESC, p.category_id DESC`,
		productId).Find(&path).Error
	if err != nil {
		return nil, fmt.Errorf("GetProductCategories: %w", err)
	}

	for _, id := range strings.Split(path, "/") {
		eId := types.ToEntityID(id)
		if eId > 0 {
			cats = append(cats, id)
			res = append(res, eId)
		}
	}

	if magento_core.UseCacheConst {
		err = magento_core.GetStoreClient().GetCacheClient().SaveSet(
			fmt.Sprintf("product:%d:categories", productId),
			cats,
			(&ProductEntity{}).CacheTTL(),
		)
		if err != nil {
			core_utils.ErrorWarning(fmt.Errorf("GetProductCategories: %w", err))
		}
	}

	return res, nil
}
