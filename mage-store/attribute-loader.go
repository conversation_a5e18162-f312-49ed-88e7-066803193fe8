package mage_store

import (
	"fmt"
	"gorm.io/gorm"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/storage"
	"praktis.bg/store-api/packages/magento-core/types"
	"strings"
)

type AttributeLoader struct {
	db         *gorm.DB
	entityType types.EntityType
}

func NewAttributeLoader(
	db *gorm.DB,
	entityType types.EntityType,
) *AttributeLoader {
	return &AttributeLoader{
		db:         db,
		entityType: entityType,
	}
}

type AttributeValuesParams struct {
	Table   storage.DbTable
	Ids     []int64
	AttrIds []int
	StoreID int
}

func (a *AttributeLoader) DB() *gorm.DB {
	if a.db == nil {
		a.db = magento_core.GetStoreClient().DbConn()
	}

	return a.db
}

const NoStoreID = -1

func (a *AttributeLoader) LoadAttribute(p AttributeValuesParams) (map[int64]mage_types.EntityData, error) {
	result := make(map[int64]mage_types.EntityData)
	if len(p.Ids) < 1 || p.Table == "" {
		return result, fmt.Errorf("empty ids")
	}

	args := []interface{}{p.Ids}

	var values []struct {
		EntityID    int64
		AttributeID int
		Value       string
		StoreID     int
	}

	sql := fmt.Sprintf(`select 
    entity_id, attribute_id, value, store_id
from %s where entity_id in (?)`, p.Table.String())

	if len(p.AttrIds) > 0 {
		sql += " and attribute_id in (?)"
		args = append(args, p.AttrIds)
	}

	if p.StoreID != NoStoreID {
		if p.StoreID != 0 {
			sql += fmt.Sprintf("store_id in (0, %d)", p.StoreID)
		} else {
			sql = fmt.Sprintf(" and store_id = %d", p.StoreID)
		}
	} else {
		sql = strings.Replace(sql, "value, store_id", "value, 0 as store_id", -1)
	}

	err := a.DB().Raw(sql, args...).Scan(&values).Error
	if err != nil {
		return nil, fmt.Errorf("GetAttributesByEntityIDs: %w", err)
	}

	for _, v := range values {
		if _, ok := result[v.EntityID]; !ok {
			result[v.EntityID] = make(mage_types.EntityData)
		}

		var attrCode string
		if a.entityType == types.CustomerEntityType {
			attrCode, _ = CustomerAttributes.GetAttributeCode(v.AttributeID)
		} else if a.entityType == types.CustomerAddressEntityType {
			attrCode, _ = CustomerAddressAttributes.GetAttributeCode(v.AttributeID)
		} else if a.entityType == types.ProductEntityType {
			attrCode, _ = ProductAttributes.GetAttributeCode(v.AttributeID)
		} else {
			return result, fmt.Errorf("неизвестен тип: %s", a.entityType)
		}

		if attrCode != "" {
			if v.StoreID == 0 {
				if _, ok := result[v.EntityID][attrCode]; ok {
					// value for store exists don't override
					continue
				}
			}

			result[v.EntityID][attrCode] = v.Value
		}
	}

	return result, nil
}

type LoadEntityAttribute struct {
	Table    storage.DbTable
	EntityId int64
	AttrIds  []int
	StoreID  int
}

func (a *AttributeLoader) LoadEntityAttribute(p LoadEntityAttribute) (mage_types.EntityData, error) {
	result := make(mage_types.EntityData)
	if p.EntityId < 1 || p.Table == "" {
		return result, fmt.Errorf("empty ids")
	}

	args := []interface{}{p.EntityId}

	var values []struct {
		AttributeID int
		Value       string
		StoreID     int
	}

	dataTable := p.Table.String()

	sql := fmt.Sprintf(`select 
    attribute_id, value, store_id
from %s where entity_id = ?`, dataTable)

	if len(p.AttrIds) > 0 {
		sql += " and attribute_id in (?)"
		args = append(args, p.AttrIds)
	}

	if p.StoreID != NoStoreID {
		if p.StoreID != 0 {
			sql += fmt.Sprintf("store_id in (0, %d)", p.StoreID)
		} else {
			sql = fmt.Sprintf(" and store_id = %d", p.StoreID)
		}
	} else {
		sql = strings.Replace(sql, "value, store_id", "value, 0 as store_id", -1)
	}

	err := a.DB().Raw(sql, args...).Scan(&values).Error
	if err != nil {
		return nil, fmt.Errorf("GetAttributesByEntityIDs: %w", err)
	}

	var attrCode string
	for _, v := range values {
		if a.entityType == types.CustomerEntityType {
			attrCode, _ = CustomerAttributes.GetAttributeCode(v.AttributeID)
		} else if a.entityType == types.CustomerAddressEntityType {
			attrCode, _ = CustomerAddressAttributes.GetAttributeCode(v.AttributeID)
		} else if a.entityType == types.ProductEntityType {
			attrCode, _ = ProductAttributes.GetAttributeCode(v.AttributeID)
		} else {
			return result, fmt.Errorf("неизвестен тип: %s", a.entityType)
		}

		if attrCode != "" {
			result[attrCode] = v.Value
		}
	}

	return result, nil
}
