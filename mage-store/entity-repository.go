package mage_store

import (
	"fmt"
	"strings"
)

func NewMissingAttributesError(missing []string) *MissingAttributesError {
	return &MissingAttributesError{
		missing: missing,
	}
}

type MissingAttributesError struct {
	missing []string
}

func (e MissingAttributesError) Error() string {
	return fmt.Sprintf("missing attributes: %s", strings.Join(e.missing, ", "))
}

func (e MissingAttributesError) Missing() []string {
	return e.missing
}
