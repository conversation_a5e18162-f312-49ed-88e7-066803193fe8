package mage_store

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
)

type AttributeSetEntity struct {
	ID types.EntityID `gorm:"column:attribute_set_id"`
}

func (e *AttributeSetEntity) TableName() string {
	return "eav_attribute_set"
}

func (e *AttributeSetEntity) GetAttributesCodes() ([]string, error) {
	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	db := magento_core.GetStoreClient().DbConn()

	var attributes []string
	cacheKey := fmt.Sprintf("attribute_set_%d_attributes", e.ID)
	if cacheClient.MustExists(cacheKey) {
		values, err := cacheClient.GetSet(cacheKey)
		if err != nil {
			core_utils.ErrorWarning(err)
			return attributes, err
		} else if len(values) > 0 {
			return values, nil
		}
	}

	db.Raw(`select a.attribute_code from eav_entity_attribute as ea
	join eav_attribute as a on ea.attribute_id = a.attribute_id
 where ea.attribute_set_id = ?`, e.ID).Scan(&attributes)

	_, err := cacheClient.AddSetMember(cacheKey, attributes...)
	core_utils.ErrorWarning(err)

	return attributes, nil
}
