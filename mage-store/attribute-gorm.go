package mage_store

import (
	"fmt"
	"gorm.io/gorm"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
)

type ScopeParam struct {
	Alias string
}

func (a *CoreAttribute) GetScope(p ScopeParam) (func(db *gorm.DB) *gorm.DB, error) {
	switch a.EntityType {
	case types.CustomerEntityType:
		return a.getCustomerScope()
	default:
		return (func(db *gorm.DB) *gorm.DB)(nil),
			fmt.Errorf("scope for entity type %s not implemented", a.EntityType)
	}
}

func (a *CoreAttribute) getCustomerScope() (func(db *gorm.DB) *gorm.DB, error) {
	return func(db *gorm.DB) *gorm.DB {
		alias := a.GetAlias(magento_core.DefaultStoreID)

		db.Statement.Selects = append(
			db.Statement.Selects,
			fmt.Sprintf("%s.value as %s", alias, a.AttributeCode),
		)

		return db.
			Joins(fmt.Sprintf(
				"left join %s %s on %s.entity_id = e.entity_id and %s.attribute_id = %d",
				a.GetMagentoTable(),
				alias,
				alias,
				alias,
				a.GetID(),
			))
	}, nil
}
