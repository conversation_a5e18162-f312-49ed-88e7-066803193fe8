package mage_store

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	"praktis.bg/store-api/packages/magento-core/types"

	"strconv"
	"time"
)

func (a *CoreAttribute) CacheKey() string {
	return fmt.Sprintf("attr:%s:%s", a.EntityType.String(), a.AttributeCode.String())
}

func (a *CoreAttribute) SetCacheKey(key string) {
	a.cacheKey = key
}

func (a *CoreAttribute) IsCacheLoaded() bool {
	return a.isCacheLoaded
}

func (a *CoreAttribute) CacheTTL() time.Duration {
	return cache.InfiniteTTL
}

func (a *CoreAttribute) GetCacheObject() map[string]string {
	return map[string]string{
		"entity_type":    a.EntityType.String(),
		"attribute_id":   fmt.Sprintf("%d", a.AttributeID),
		"attribute_code": a.AttributeCode.String(),
		"backend_type":   string(a.BackendType),
		"frontend_label": a.FrontendLabel,
	}
}

func (a *CoreAttribute) SetCacheObject(v map[string]string) error {
	a.EntityType = types.EntityType(v["entity_type"])
	a.AttributeID, _ = strconv.Atoi(v["attribute_id"])
	a.AttributeCode = AttributeCode(v["attribute_code"])
	a.BackendType = BackendType(v["backend_type"])
	a.FrontendLabel = v["frontend_label"]

	return nil
}
