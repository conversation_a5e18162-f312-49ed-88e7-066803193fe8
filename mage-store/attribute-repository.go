package mage_store

import (
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
)

type AttributeRepository struct {
	storeID int
}

func NewAttributeRepository(
	storeID int,
) *AttributeRepository {
	return &AttributeRepository{
		storeID,
	}
}

func (r *AttributeRepository) GetAttribute(
	entityType types.EntityType,
	attributeCode string,
) *CoreAttribute {
	var attr *CoreAttribute
	attrs := r.GetAttributes(entityType, []string{attributeCode})
	var ok bool
	attr, ok = attrs[attributeCode]
	if !ok {
		core_utils.Debug("Could not load attribute %s for entity type %s", attributeCode, entityType)
	}

	return attr
}

func (r *AttributeRepository) GetAttributes(
	entityType types.EntityType,
	attributeCodes []string,
) map[string]*CoreAttribute {
	var loadCodes []string
	attrs := make(map[string]*CoreAttribute, len(attributeCodes))
	for _, attributeCode := range attributeCodes {
		cacheAttr := r.GetAttributeCache(entityType, attributeCode)
		if cacheAttr != nil {
			attrs[attributeCode] = cacheAttr
			continue
		}
		loadCodes = append(loadCodes, attributeCode)
	}

	if len(loadCodes) > 0 {
		loadedAttrs := make([]*CoreAttribute, len(loadCodes))

		db := magento_core.GetStoreClient().DbConn()
		db.Raw(`
select a.attribute_id as attribute_id, 
       a.backend_type as backend_type, 
       a.attribute_code as attribute_code,
       eet.entity_type_code as entity_type,
       COALESCE(label.value, a.frontend_label, a.attribute_code) as frontend_label,
       cea.position as sort_order
from eav_attribute a 
    inner join eav_entity_type eet on a.entity_type_id = eet.entity_type_id
    left join eav_attribute_label label on label.attribute_id = a.attribute_id and label.store_id = ? 
	inner join catalog_eav_attribute cea on a.attribute_id = cea.attribute_id
where eet.entity_type_code = ? and a.attribute_code in ?`, r.storeID, entityType, loadCodes).
			Scan(&loadedAttrs)

		for _, attr := range loadedAttrs {
			if attr != nil {
				saved := r.SaveAttributeToCache(attr)
				if !saved {
					core_utils.Warning("Could not save attribute %s for entity type %s", attr.AttributeCode, entityType)
				}
				attrs[attr.AttributeCode.String()] = attr
			}
		}
	}

	if len(attrs) != len(attributeCodes) {
		core_utils.Warning("Could not load all attributes for entity type %s", entityType)
		if core_utils.IsDebugMode() {
			for _, attributeCode := range attributeCodes {
				if _, ok := attrs[attributeCode]; !ok {
					core_utils.Warning("Could not load attribute %s for entity type %s", attributeCode, entityType)
				}
			}
		}
	}

	return attrs
}

func (r *AttributeRepository) GetAttributeCache(
	entityType types.EntityType,
	attributeCode string,
) *CoreAttribute {
	cacheAttr := &CoreAttribute{
		EntityType:    entityType,
		AttributeCode: AttributeCode(attributeCode),
	}

	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	_ = cacheClient.LoadObj(cacheAttr)
	if cacheAttr.AttributeID < 1 {
		return nil
	}

	return cacheAttr
}

func (r *AttributeRepository) SaveAttributeToCache(
	attr *CoreAttribute,
) bool {
	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	err := cacheClient.SaveObj(attr)
	if err != nil {
		core_utils.ErrorWarning(err)
		return false
	}

	return true
}
