package mage_store

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	mage_entity "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
	"strings"
)

type ICollection[T types.IEntity] interface {
	GetStore() *mage_entity.StoreEntity
	Select(fields ...Select) ICollection[T]
	Where(conditions ...Where) ICollection[T]
	Order(order ...Order) ICollection[T]

	Load() error
	Data() (*[]T, error)
	FirstItem() (T, error)
}

type IRepository[T types.IEntity] interface {
	GetCollection() ICollection[T]
}

type IOrmAction interface {
	Apply(db *gorm.DB) *gorm.DB
}

type ICollectionOperation interface {
	fmt.Stringer
}

type OperationSet []ICollectionOperation

// EntityCollection
// -> ICollection
type EntityCollection[T types.IEntity] struct {
	db                  *gorm.DB
	store               *mage_entity.StoreEntity
	attributeRepository *AttributeRepository

	data       *[]T
	isLoaded   bool
	attributes map[string]*CoreAttribute

	joinedAttributes map[string]bool
}

func (d *EntityCollection[T]) GetEntityType() types.EntityType {
	var emptyVal T
	table := emptyVal.TableName()

	if table == "" {
		panic("table name is empty")
	} else if table == "catalog_category_entity" {
		return types.CategoryEntityType
	} else if table == "catalog_product_entity" {
		return types.ProductEntityType
	}

	panic("unknown entity type table:" + table)
}

func (d *EntityCollection[T]) Load() error {
	db := d.db
	if db == nil {
		return fmt.Errorf("DB is not set")
	}

	var entity T
	db.Table(fmt.Sprintf("%s as e", entity.TableName()))

	var _data []T
	err := ScanEntities(db, &_data)
	if err != nil {
		return err
	}

	d.isLoaded = true
	d.data = &_data

	return nil
}

func (d *EntityCollection[T]) Data() (*[]T, error) {
	var noResult *[]T // init empty entity slice
	if !d.isLoaded {
		err := d.Load()
		if err != nil {
			return noResult, err
		}
		d.isLoaded = true
	}

	return d.data, nil
}

func (d *EntityCollection[T]) FirstItem() (T, error) {
	var result T // init empty entity
	if d.db == nil {
		return result, fmt.Errorf("DB is not set")
	}
	var ok bool

	result, ok = result.New().(T)
	if !ok {
		return result, fmt.Errorf("cannot convert to entity")
	}

	db := d.db.
		Table(fmt.Sprintf("%s as e", result.TableName())).
		Limit(1)

	err := ScanEntity(db, result)
	if err != nil {
		return result, err
	}

	return result, nil
}

func (d *EntityCollection[T]) GetStore() *mage_entity.StoreEntity {
	return d.store
}

func (d *EntityCollection[T]) Select(fields ...Select) ICollection[T] {
	if d.db == nil {
		panic("DB is not set")
	}

	for _, field := range fields {
		attrs := field.ExtractAttributeCodes()
		for _, attrCode := range attrs {
			d.joinAttributeTable(attrCode)
		}

		selectVal := field.GetSelect(GetFullSelect)
		if selectVal != "" {
			if len(d.db.Statement.Selects) < 1 {
				d.db = d.db.Select(selectVal)
			} else {
				var included bool
				for _, v := range d.db.Statement.Selects {
					if strings.Contains(v, selectVal) {
						included = true
						break
					}
				}

				if included {
					continue
				}

				d.db.Statement.Selects = append(
					d.db.Statement.Selects,
					selectVal,
				)
			}

		}
	}

	return d
}

func (d *EntityCollection[T]) AddAttributes(attributes []string) *EntityCollection[T] {
	for _, attr := range attributes {
		d.Select(Select(":" + attr))
	}

	return d
}

func (d *EntityCollection[T]) Where(conditions ...Where) ICollection[T] {
	for _, condition := range conditions {
		conditionAttr := condition.ExtractAttributeCodes()
		for _, attrCode := range conditionAttr {
			d.joinAttributeTable(attrCode)
		}
		whereVal := condition.GetWhere()
		if whereVal != "" {
			d.db = d.db.Where(whereVal)
		}
	}
	return d
}

func (d *EntityCollection[T]) Order(orders ...Order) ICollection[T] {
	for _, order := range orders {
		orderAttr := order.ExtractAttributeCodes()
		for _, attrCode := range orderAttr {
			d.joinAttributeTable(attrCode)
		}

		orderVal := order.GetOrder()
		if orderVal != "" {
			d.db = d.db.Order(orderVal)
		}
	}
	return d
}

func (d *EntityCollection[T]) getAttribute(code string) *CoreAttribute {
	repo := d.getAttributeRepository()
	if repo == nil {
		core_utils.Notice("Attribute repository is not set")
		return &CoreAttribute{}
	}

	if d.attributes == nil {
		d.attributes = make(map[string]*CoreAttribute)
	}

	if d.attributes[code] == nil {
		d.attributes[code] = repo.GetAttribute(d.GetEntityType(), code)
	}

	return d.attributes[code]
}

func (d *EntityCollection[T]) joinAttributeTable(code string) *EntityCollection[T] {
	if d.joinedAttributes == nil {
		d.joinedAttributes = make(map[string]bool)
	}

	if d.joinedAttributes[code] {
		return d
	}

	repo := d.getAttributeRepository()
	attr := d.getAttribute(code)
	d.db = d.db.Scopes(
		repo.JoinAttributeTable(attr, Nothing),
	)
	d.joinedAttributes[code] = true

	return d
}

func (d *EntityCollection[T]) getAttributeRepository() *AttributeRepository {
	if d.attributeRepository != nil {
		return d.attributeRepository
	}

	var storeId = 0
	if d.store != nil && d.store.GetID() > 0 {
		storeId = d.store.GetID()
	} else {
		core_utils.Notice("Store is not set")
	}

	d.attributeRepository = NewAttributeRepository(storeId)

	return d.attributeRepository
}

func NewCollectionFromDb[T types.IEntity](db *gorm.DB, store *mage_entity.StoreEntity) *EntityCollection[T] {
	return &EntityCollection[T]{
		db:    db,
		store: store,
	}
}
