package mage_store

import (
	"errors"
	"gorm.io/gorm"
	"praktis.bg/store-api/packages/magento-core/types"
	"reflect"
)

func ScanEntity(db *gorm.DB, dest types.ISetData) error {
	if dest == nil {
		return errors.New("dest cannot be nil")
	}

	data := make(map[string]interface{})
	if err := db.Scan(&data).Error; err != nil {
		return err
	}

	dest.SetData(data)

	return nil
}

func ScanEntities(db *gorm.DB, dest interface{}) error {
	data := make([]map[string]interface{}, 0)
	if err := db.Scan(&data).Error; err != nil {
		return err
	}

	return mapEntities(&data, dest)
}

func mapEntities(data *[]map[string]interface{}, dest interface{}) error {
	destRef := reflect.ValueOf(dest)
	if destRef.Kind() != reflect.Ptr || destRef.Elem().Kind() != reflect.Slice {
		return errors.New("dest must be a pointer to a slice")
	}

	slice := destRef.Elem()
	elemType := slice.Type().Elem()
	if elemType.Kind() != reflect.Ptr {
		return errors.New("slice must contain pointers")
	}

	if elemType.Elem().Kind() == reflect.Invalid {
		return errors.New("invalid element type in slice")
	}

	for _, item := range *data {
		newElem, ok := reflect.New(elemType.Elem()).Interface().(types.ISetData)
		if !ok {
			return errors.New("slice element must implement ISetData")
		}

		newElem.SetData(item)
		v := reflect.ValueOf(newElem)
		slice = reflect.Append(slice, v)
	}

	destRef.Elem().Set(slice)
	return nil
}
