package mage_store

import (
	"errors"
	"fmt"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/storage"
	"praktis.bg/store-api/packages/magento-core/types"

	"strconv"
	"strings"
)

// CategoryRepository
// -> IMagentoRepository
type CategoryRepository struct {
}

func NewCategoryRepository() *CategoryRepository {
	return &CategoryRepository{}
}

func (r *CategoryRepository) GetCached(id types.EntityID, attrs ...string) (*CategoryEntity, error) {
	var result *CategoryEntity
	cacheKeyObj := &CategoryEntity{
		EntityID: id,
	}

	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	if cacheClient.MustExists(cacheKeyObj.CacheKey()) {
		// if path and entity_id are not in attrs, add them
		cachedMap, err := cacheClient.GetMapKeys(cacheKeyObj.CacheKey(), attrs...)
		if err != nil {
			return nil, err
		}
		cacheKeyObj.SetStrData(cachedMap)

		var missing []string
		for _, attr := range attrs {
			if _, ok := cachedMap[attr]; !ok {
				missing = append(missing, attr)
			}
		}

		if len(missing) > 0 {
			return cacheKeyObj, &storage.MissingFieldsError{
				Fields: missing,
			}
		}

		return cacheKeyObj, err
	}

	return result, nil
}

func (r *CategoryRepository) Get(id types.EntityID, attrs ...string) (*CategoryEntity, error) {
	var category *CategoryEntity
	var err error

	fields := append(attrs, "url_path", "name", "path")
	var cachedCat *CategoryEntity
	cachedCat, err = r.GetCached(id, fields...)
	if err == nil && cachedCat.IsValid() {
		// found with all attributes
		return cachedCat, nil
	}

	if err != nil {
		var missErr *storage.MissingFieldsError
		if !errors.As(err, &missErr) {
			return nil, err
		}
		fields = missErr.Fields
	}

	category = &CategoryEntity{
		EntityID: id,
	}

	if cachedCat != nil {
		err = cachedCat.copyTo(category)
		if err != nil {
			return nil, err
		}
	}

	err = category.Load(id, fields...)
	if err != nil {
		return nil, err
	}

	category.SaveInCache()

	return category, nil
}

func (r *CategoryRepository) GetChildren(
	catID types.EntityID,
	attrs ...string,
) (*[]*CategoryEntity, error) {
	var result []*CategoryEntity
	ids, err := getDirectChildrenIds(catID)
	if err != nil {
		return nil, err
	}

	notFound := make([]string, 0)
	for _, id := range ids {
		cached, err := r.GetCached(id, attrs...)
		core_utils.ErrorWarning(err)
		if err == nil && cached.IsValid() {
			result = append(result, cached)
		} else {
			notFound = append(notFound, fmt.Sprintf("%d", id))
		}
	}

	if len(notFound) > 0 {
		collection := r.GetCollection().
			Select(
				"e.entity_id, e.path, e.level, e.position, :name, :url_path",
			).(*EntityCollection[*CategoryEntity]).
			AddAttributes(attrs)

		collection.Where(WhereExpr(
			"e.entity_id in (%s)", strings.Join(notFound, ","),
		))

		cats, err := collection.Data()
		if err != nil {
			return nil, err
		}

		for _, cat := range *cats {
			cat.SaveInCache()
		}

		return cats, nil
	}

	return &result, nil
}

func (r *CategoryRepository) GetCollection() ICollection[*CategoryEntity] {
	storeClient := magento_core.GetStoreClient()
	return NewCollectionFromDb[*CategoryEntity](
		storeClient.DbConn(),
		storeClient.GetStore(),
	)
}

func getDirectChildrenIds(catID types.EntityID) ([]types.EntityID, error) {
	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	key := fmt.Sprintf("category_children_ids:%d", catID)
	result := make([]types.EntityID, 0)
	if cacheClient.MustExists(key) {
		val, err := cacheClient.Get(key)
		if err != nil {
			return nil, err
		}

		for _, stringId := range strings.Split(val, ",") {
			if stringId == "" {
				continue
			}

			id, err := strconv.Atoi(stringId)
			if err != nil {
				return nil, err
			}

			result = append(result, types.EntityID(id))
		}

		return result, nil
	} else {
		var ids []int
		attr := NewAttributeRepository(1).GetAttribute(types.CategoryEntityType, "is_active")
		db := magento_core.GetStoreClient().DbConn()
		db = db.Raw(`select e.entity_id from catalog_category_entity e
    inner join catalog_category_entity_int v 
        on v.entity_id = e.entity_id and v.attribute_id = ? and v.value = 1
where e.parent_id = ? group by e.entity_id;`, attr.AttributeID, catID).Scan(&ids)
		if db.Error != nil {
			return nil, db.Error
		}

		sIds := make([]string, len(ids))
		for i, id := range ids {
			sIds[i] = strconv.Itoa(id)
			result = append(result, types.EntityID(id))
		}

		err := cacheClient.Save(key, strings.Join(sIds, ","), cache.InfiniteTTL)
		if err != nil {
			return result, err
		}
	}

	return result, nil
}
