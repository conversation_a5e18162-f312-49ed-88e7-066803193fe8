package mage_store

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
	"time"
)

func (c *CategoryEntity) SetCacheKey(key string) {
	c.cacheKey = key
}

func (c *CategoryEntity) IsCacheLoaded() bool {
	return c.isCacheLoaded
}

func (c *CategoryEntity) CacheKey() string {
	if c.cacheKey != "" {
		return c.cacheKey
	}

	return fmt.Sprintf("category:%d", c.GetID())
}

func (c *CategoryEntity) CacheTTL() time.Duration {
	return 1 * time.Hour
}

func (c *CategoryEntity) GetCacheObject() map[string]string {
	res := make(map[string]string)

	data := c.GetData()
	for k, v := range data {
		res[k] = types.ToStr(v)
	}

	return res
}

func (c *CategoryEntity) SetCacheObject(cachedMap map[string]string) error {
	c.SetStrData(cachedMap)
	return nil
}

func (c *CategoryEntity) SaveInCache() bool {
	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	err := cacheClient.SaveObj(c)
	core_utils.ErrorWarning(err)

	return err == nil
}
