package mage_store

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	"praktis.bg/store-api/packages/magento-core/types"
	"time"
)

var _ cache.CacheableObject = (*CMSPageEntity)(nil)

type CMSPageEntity struct {
	PageID          int64
	Title           string
	Identifier      string
	Content         string `gorm:"column:content"`
	IsActive        bool
	MetaKeywords    string
	MetaDescription string

	cacheKey  string
	fromCache bool
}

func (c *CMSPageEntity) CacheKey() string {
	return fmt.Sprintf("cms_page:%d", c.PageID)
}

func (c *CMSPageEntity) SetCacheKey(key string) {
	c.cacheKey = key
}

func (c *CMSPageEntity) CacheTTL() time.Duration {
	return cache.InfiniteTTL
}
func (c *CMSPageEntity) IsCacheLoaded() bool {
	return c.fromCache
}

func (c *CMSPageEntity) GetCacheObject() map[string]string {
	return map[string]string{
		"page_id":    types.ToStr(c.<PERSON>),
		"title":      c.Title,
		"identifier": c.Identifier,
		"content":    c.Content,
		"is_active":  types.ToStr(c.IsActive),
	}
}

func (c *CMSPageEntity) SetCacheObject(v map[string]string) error {
	c.PageID = types.ToInt64(v["page_id"])
	c.Title = v["title"]
	c.Identifier = v["identifier"]
	c.Content = v["content"]
	c.IsActive = types.ToBool(v["is_active"])

	return nil
}
