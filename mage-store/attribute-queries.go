package mage_store

import (
	"fmt"
	"gorm.io/gorm"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/storage"
)

type Operations int

const (
	Nothing Operations = 1 << iota
	AddToSelect
)

func (r *AttributeRepository) JoinAttributeTable(
	attr *CoreAttribute,
	joinOptions ...Operations,
) func(db *gorm.DB) *gorm.DB {
	if attr.AttributeID < 1 || attr.AttributeCode == "" {
		panic(fmt.Sprintf("Attribute %s for entity type %s not found", attr.AttributeCode, attr.EntityType))
	}

	return func(db *gorm.DB) *gorm.DB {
		// @TODO make it with default mage-store id
		defaultStoreID := magento_core.DefaultStoreID

		for _, joinOption := range joinOptions {
			if joinOption == AddToSelect {
				db.Statement.Selects = append(
					db.Statement.Selects,
					getAttributeSelect(
						attr.AttributeCode.String(),
						defaultStoreID,
					),
				)
			}
		}

		return db.Joins(fmt.Sprintf(
			"left join %s %s on %s.entity_id = e.entity_id and %s.attribute_id = %d and %s.store_id = %d",
			attr.GetMagentoTable(),
			attr.GetAlias(defaultStoreID),
			attr.GetAlias(defaultStoreID),
			attr.GetAlias(defaultStoreID),
			attr.GetID(),
			attr.GetAlias(defaultStoreID),
			defaultStoreID, // @todo: manage multiple stores
		))
	}
}

func (r *AttributeRepository) FilterByAttribute(
	attr *CoreAttribute,
	cond storage.SQLFilter,
	fValue string,
) func(db *gorm.DB) *gorm.DB {
	if attr.AttributeID < 1 || attr.AttributeCode == "" {
		panic(fmt.Sprintf("Attribute %s for entity type %s not found", attr.AttributeCode, attr.EntityType))
	}

	return func(db *gorm.DB) *gorm.DB {
		defaultStoreID := magento_core.DefaultStoreID
		db.Statement.Selects = append(
			db.Statement.Selects,
			fmt.Sprintf("%s.value as %s", attr.GetAlias(defaultStoreID), attr.AttributeCode),
		)

		return db.Joins(fmt.Sprintf(
			"inner join %s %s on %s.entity_id = e.entity_id and %s.attribute_id = %d and %s.store_id = %d",
			attr.GetMagentoTable(),
			attr.GetAlias(defaultStoreID),
			attr.GetAlias(defaultStoreID),
			attr.GetAlias(defaultStoreID),
			attr.GetID(),
			attr.GetAlias(defaultStoreID),
			defaultStoreID, // @todo: manage multiple stores
		)).Where(fmt.Sprintf(
			"%s.value %s", attr.GetAlias(defaultStoreID), cond), fValue,
		)
	}
}
