package mage_store

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"strconv"
	"strings"
	"time"
)

func (a *CoreAttribute) GetOptions() []CoreAttributeOption {
	options, err := ProductAttributes.GetOptions(a.AttributeCode.String())
	if err != nil {
		core_utils.Debug("Attribute %s type %s has no options", a.AttributeCode, a.BackendType)
	}

	return options
}

func (a *CoreAttribute) GetSortedOptions(raw interface{}) []CoreAttributeOption {
	var options []CoreAttributeOption
	var valsMap = make(map[int]struct{})
	switch val := raw.(type) {
	case string:
		if val != "" {
			for _, optVal := range strings.Split(val, ",") {
				if intVal, _ := strconv.Atoi(optVal); intVal > 0 {
					valsMap[intVal] = struct{}{}
				}
			}
		} else {
			if intVal, _ := strconv.Atoi(val); intVal > 0 {
				valsMap[intVal] = struct{}{}
			}
		}
	default:
		return options
	}

	var ok bool
	if len(valsMap) == 0 {
		return a.GetOptions()
	} else {
		for _, option := range a.GetOptions() {
			if _, ok = valsMap[option.Value]; ok {
				options = append(options, option)
			}
		}
	}

	return options
}

type CoreAttributeOption struct {
	Value     int
	Label     string
	SortOrder int `gorm:"column:sort_order"`
}

var _ cache.CacheableObject = (*OptionCollection)(nil)

type OptionCollection struct {
	AttributeCode string
	Options       []*CoreAttributeOption

	cacheKey      string `gorm:"-"`
	isCacheLoaded bool   `gorm:"-"`
}

func (o *OptionCollection) CacheKey() string {
	return fmt.Sprintf("attribute_%s_options", o.AttributeCode)
}

func (o *OptionCollection) SetCacheKey(key string) {
	o.cacheKey = key
}

func (o *OptionCollection) IsCacheLoaded() bool {
	return o.isCacheLoaded
}

func (o *OptionCollection) GetCacheObject() map[string]string {
	var result = make(map[string]string, len(o.Options))
	for _, option := range o.Options {
		result[fmt.Sprintf("%d", option.Value)] = option.Label
	}

	return result
}

func (o *OptionCollection) SetCacheObject(v map[string]string) error {
	o.Options = make([]*CoreAttributeOption, 0)
	for value, label := range v {
		o.Options = append(o.Options, &CoreAttributeOption{
			Value: core_utils.StringToInt(value),
			Label: label,
		})
	}

	return nil
}

func (o *OptionCollection) CacheTTL() time.Duration {
	return 1 * time.Hour
}

func (r *AttributeRepository) GetAttributeOptions(attr *CoreAttribute) []*CoreAttributeOption {
	result := &OptionCollection{}

	result.AttributeCode = attr.AttributeCode.String()
	cacheClient := magento_core.GetStoreClient().GetCacheClient()

	if cacheClient.MustExists(result.CacheKey()) {
		err := cacheClient.LoadObj(result)
		core_utils.ErrorWarning(err)

		return result.Options
	}

	var options []*CoreAttributeOption
	magento_core.GetStoreClient().DbConn().Raw(`select
    o.option_id as value,
    COALESCE(ov_label.value, ov.value) as label,
	o.sort_order as sort_order
from eav_attribute_option o
  inner join eav_attribute_option_value ov on o.option_id = ov.option_id
  left join eav_attribute_option_value ov_label on o.option_id = ov_label.option_id and ov_label.store_id = ?
where ov.store_id = 0 and o.attribute_id = ?`, r.storeID, attr.GetID()).
		Scan(&options)

	if len(options) == 0 {
		return options
	}

	result.Options = options

	core_utils.ErrorWarning(
		cacheClient.SaveObj(result),
	)

	return result.Options
}
