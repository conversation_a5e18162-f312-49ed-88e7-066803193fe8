package sales

type MagentoShipmentItem struct {
	EntityID    int64 `gorm:"primaryKey;column:entity_id"`
	ParentID    int64 `gorm:"column:parent_id"`
	OrderItemID int64 `gorm:"column:order_item_id"`

	// Relationships
	Shipment  *MagentoOrderShipment `gorm:"foreignKey:ParentID;references:EntityID"`
	OrderItem *MagentoOrderItem     `gorm:"foreignKey:OrderItemID;references:ItemID"`
}

func (MagentoShipmentItem) TableName() string {
	return "sales_flat_shipment_item"
}
