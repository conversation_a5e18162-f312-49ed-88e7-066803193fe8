package sales

import (
	"fmt"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

type IShippingCarrier interface {
	GetCarrier() string
	GetMethod() string
	GetTitle() string
	GetShippingMethod() string
	IsActive() bool
	GetConfigVal(key string) string
	getConfigPath(key string) magento_core.ConfigValuePath
}

func InitNewStoreShipping(
	store *magento_core.StoreEntity,
	carrier string,
	methodCode string,
) *StoreShipping {
	return &StoreShipping{
		store:   store,
		carrier: carrier,
		method:  methodCode,
	}
}

var _ IShippingCarrier = (*StoreShipping)(nil)

type StoreShipping struct {
	store   *magento_core.StoreEntity
	method  string
	carrier string
}

func (s *StoreShipping) GetCarrier() string {
	return s.carrier
}

func (s *StoreShipping) GetMethod() string {
	return s.method
}

func (s *StoreShipping) GetShippingMethod() string {
	return fmt.Sprintf("%s_%s", s.carrier, s.method)
}

func (s *StoreShipping) IsActive() bool {
	return s.store.GetConfigBool(s.getConfigPath("active"), false)
}

func (s *StoreShipping) GetTitle() string {
	return s.store.GetConfig(s.getConfigPath("title"), "Доставка")
}

func (s *StoreShipping) GetConfigVal(key string) string {
	return s.store.GetConfig(s.getConfigPath(key), "")
}

func (s *StoreShipping) getConfigPath(key string) magento_core.ConfigValuePath {
	return magento_core.ConfigValuePath(fmt.Sprintf("carriers/%s/%s", s.GetCarrier(), key))
}
