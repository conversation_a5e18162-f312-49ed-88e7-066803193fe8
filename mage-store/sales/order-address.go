package sales

import "database/sql"

type MagentoOrderAddress struct {
	EntityID               int64          `gorm:"primaryKey;column:entity_id"`
	ParentID               sql.NullInt64  `gorm:"column:parent_id"`
	CustomerAddressID      sql.NullInt64  `gorm:"column:customer_address_id"`
	QuoteAddressID         sql.NullInt64  `gorm:"column:quote_address_id"`
	RegionID               sql.NullInt64  `gorm:"column:region_id"`
	CustomerID             sql.NullInt64  `gorm:"column:customer_id"`
	Fax                    sql.NullString `gorm:"column:fax"`
	Region                 sql.NullString `gorm:"column:region"`
	Postcode               sql.NullString `gorm:"column:postcode"`
	Lastname               sql.NullString `gorm:"column:lastname"`
	Street                 sql.NullString `gorm:"column:street"`
	City                   sql.NullString `gorm:"column:city"`
	Email                  sql.NullString `gorm:"column:email"`
	Telephone              sql.NullString `gorm:"column:telephone"`
	CountryID              sql.NullString `gorm:"column:country_id"`
	Firstname              sql.NullString `gorm:"column:firstname"`
	AddressType            sql.NullString `gorm:"column:address_type"`
	Prefix                 sql.NullString `gorm:"column:prefix"`
	Middlename             sql.NullString `gorm:"column:middlename"`
	Suffix                 sql.NullString `gorm:"column:suffix"`
	Company                sql.NullString `gorm:"column:company"`
	VatID                  sql.NullString `gorm:"column:vat_id;type:text"`
	VatIsValid             sql.NullInt16  `gorm:"column:vat_is_valid"`
	VatRequestID           sql.NullString `gorm:"column:vat_request_id;type:text"`
	VatRequestDate         sql.NullString `gorm:"column:vat_request_date;type:text"`
	VatRequestSuccess      sql.NullInt16  `gorm:"column:vat_request_success"`
	Invoice                sql.NullString `gorm:"column:invoice;type:text"`
	InvoiceType            sql.NullString `gorm:"column:invoice_type;type:text"`
	InvoiceCompanyName     sql.NullString `gorm:"column:invoice_company_name;type:text"`
	InvoiceCompanyMol      sql.NullString `gorm:"column:invoice_company_mol;type:text"`
	InvoiceCompanyCity     sql.NullString `gorm:"column:invoice_company_city;type:text"`
	InvoiceCompanyAddress  sql.NullString `gorm:"column:invoice_company_address;type:text"`
	InvoiceCompanyBulstat  sql.NullString `gorm:"column:invoice_company_bulstat;type:text"`
	InvoiceCompanyVat      sql.NullString `gorm:"column:invoice_company_vat;type:text"`
	InvoicePersonalName    sql.NullString `gorm:"column:invoice_personal_name;type:text"`
	InvoicePersonalPin     sql.NullString `gorm:"column:invoice_personal_pin;type:text"`
	InvoicePersonalCity    sql.NullString `gorm:"column:invoice_personal_city;type:text"`
	CfBilling1             string         `gorm:"column:cf_billing1"`
	CfShipping1            string         `gorm:"column:cf_shipping1"`
	CfBilling2             string         `gorm:"column:cf_billing2"`
	CfShipping2            string         `gorm:"column:cf_shipping2"`
	CfBilling3             string         `gorm:"column:cf_billing3"`
	CfShipping3            string         `gorm:"column:cf_shipping3"`
	InvoicePersonalVat     sql.NullString `gorm:"column:invoice_personal_vat;type:text"`
	InvoicePersonalAddress sql.NullString `gorm:"column:invoice_personal_address;type:text"`
}

// TableName specifies the table name to map to
func (*MagentoOrderAddress) TableName() string {
	return "sales_flat_order_address"
}

func (o *MagentoOrderAddress) GetID() int64 {
	return o.EntityID
}
