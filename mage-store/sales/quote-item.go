package sales

import (
	"database/sql"
	"errors"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_product "praktis.bg/store-api/packages/magento-core/mage-store/mage-product"
)

var ErrorNotCreatedQuoteItem = errors.New("item not created")

type MagentoQuoteItem struct {
	ItemID          int64                       `gorm:"primaryKey;column:item_id"`
	QuoteID         int64                       `gorm:"column:quote_id"`
	ProductID       int64                       `gorm:"column:product_id"`
	Product         *mage_product.ProductEntity `gorm:"foreignKey:ProductID;references:EntityID"`
	StoreID         int                         `gorm:"column:store_id"`
	IsVirtual       int                         `gorm:"column:is_virtual"`
	ParentItemID    int64                       `gorm:"column:parent_item_id"`
	ParentItem      *MagentoQuoteItem           `gorm:"foreignKey:ParentItemID;references:ItemID"`
	Sku             string                      `gorm:"column:sku"`
	Name            string                      `gorm:"column:name"`
	Weight          float64                     `gorm:"column:weight"`
	RowWeight       float64                     `gorm:"column:row_weight"`
	ProductType     string                      `gorm:"column:product_type"`
	Qty             float64                     `gorm:"column:qty"`
	Price           float64                     `gorm:"column:price"`
	BasePrice       float64                     `gorm:"column:base_price"`
	CustomPrice     float64                     `gorm:"column:custom_price"`
	DiscountPct     float64                     `gorm:"column:discount_percent"`
	DiscountAmt     float64                     `gorm:"column:discount_amount"`
	BaseDiscountAmt float64                     `gorm:"column:base_discount_amount"`
	TaxPct          float64                     `gorm:"column:tax_percent"`
	TaxAmt          float64                     `gorm:"column:tax_amount"`
	BaseTaxAmt      float64                     `gorm:"column:base_tax_amount"`
	RowTotal        float64                     `gorm:"column:row_total"`
	BaseRowTotal    float64                     `gorm:"column:base_row_total"`

	PriceInclTax        float64 `gorm:"column:price_incl_tax"`
	BasePriceInclTax    float64 `gorm:"column:base_price_incl_tax"`
	RowTotalInclTax     float64 `gorm:"column:row_total_incl_tax"`
	BaseRowTotalInclTax float64 `gorm:"column:base_row_total_incl_tax"`

	CreatedAt sql.NullTime `gorm:"autoCreateTime;column:created_at"`
	UpdatedAt sql.NullTime `gorm:"autoUpdateTime;column:updated_at"`
}

func (i *MagentoQuoteItem) TableName() string {
	return "sales_flat_quote_item"
}

func (i *MagentoQuoteItem) GetStore() *magento_core.StoreEntity {
	store := magento_core.GetStoreClient().GetStore()
	// @TODO: handle multi store by store ID load
	return store
}

func (q *MagentoQuote) loadItems() ([]*MagentoQuoteItem, error) {
	if q.GetID() < 1 {
		return nil, NoQuoteIDError
	}

	result := make([]*MagentoQuoteItem, 0)
	err := q.Repository().DB().Model(&MagentoQuoteItem{}).
		Where("quote_id = ?", q.GetID()).
		Find(&result).Error
	if err != nil {
		return nil, err
	}

	return result, nil
}

func (q *MagentoQuote) GetItems() ([]*MagentoQuoteItem, error) {
	q.lock.Lock()
	defer q.lock.Unlock()

	if len(q.Items) > 0 {
		return q.Items, nil
	}

	items, err := q.loadItems()
	if err != nil {
		return nil, err
	}
	q.Items = items

	return q.Items, nil
}

func (q *MagentoQuote) MustGetItems() []*MagentoQuoteItem {
	items, err := q.GetItems()
	core_utils.DebugError(err)

	return items
}
