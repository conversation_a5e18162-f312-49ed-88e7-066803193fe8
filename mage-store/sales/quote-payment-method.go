package sales

import (
	"fmt"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

type IPaymentMethod interface {
	GetCode() string
	GetTitle() string
	IsActive() bool
	GetInstructions() string
	GetConfigVal(key string) string
	getConfigPath(key string) magento_core.ConfigValuePath
}

func InitNewStorePayment(
	store *magento_core.StoreEntity,
	code string,
) IPaymentMethod {
	return &StorePayment{
		store: store,
		code:  code,
	}
}

var _ IPaymentMethod = (*StorePayment)(nil)

type StorePayment struct {
	store *magento_core.StoreEntity
	code  string
}

func (s *StorePayment) GetCode() string {
	return s.code
}

func (s *StorePayment) GetTitle() string {
	return s.GetConfigVal("title")
}

func (s *StorePayment) IsActive() bool {
	return s.store.GetConfigBool(s.getConfigPath("active"), false)
}

func (s *StorePayment) GetConfigVal(key string) string {
	return s.store.GetConfig(s.getConfigPath(key), "")
}

func (s *StorePayment) getConfigPath(key string) magento_core.ConfigValuePath {
	return magento_core.ConfigValuePath(fmt.Sprintf("payment/%s/%s", s.code, key))
}

func (s *StorePayment) GetInstructions() string {
	return s.GetConfigVal("instructions")
}
