package sales

import (
	"database/sql"
	"errors"
	"fmt"
	"github.com/google/uuid"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/internal/config"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/auth"
	"praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"sync"
)

var _ mage_types.StoreEntity[*MagentoQuote] = (*MagentoQuote)(nil)

func GetUUID() string {
	id := uuid.New()
	return id.String()
}

type MagentoQuote struct {
	lock sync.RWMutex            `gorm:"-"`
	repo *MagentoQuoteRepository `gorm:"-"`
	// token
	RawToken string         `gorm:"-"`
	Token    *CartTokenData `gorm:"-"`
	// entity
	EntityID   int64           `gorm:"primaryKey;column:entity_id"`
	CartID     string          `gorm:"column:uuid"`
	StoreID    int64           `gorm:"column:store_id"`
	CreatedAt  sql.NullTime    `gorm:"autoCreateTime;column:created_at"`
	UpdatedAt  sql.NullTime    `gorm:"autoUpdateTime;column:updated_at"`
	IsActive   sql.NullInt16   `gorm:"column:is_active"`
	IsVirtual  sql.NullInt16   `gorm:"column:is_virtual"`
	ItemsCount sql.NullInt16   `gorm:"column:items_count"`
	ItemsQty   sql.NullFloat64 `gorm:"column:items_qty"`
	// payment
	Payment *MagentoQuotePayment `gorm:"foreignKey:QuoteID;references:EntityID"`
	// items
	Items []*MagentoQuoteItem `gorm:"foreignKey:QuoteID;references:EntityID"`
	//Items []*MagentoQuoteItem `gorm:"-"`
	// addresses
	loadedAddresses bool
	Addresses       []*MagentoQuoteAddress `gorm:"foreignKey:QuoteID;references:EntityID"`
	// Customer
	CheckoutMethod    sql.NullString `gorm:"column:checkout_method"`
	CustomerNote      sql.NullString `gorm:"column:customer_note"`
	CustomerID        int64          `gorm:"column:customer_id"`
	CustomerEmail     sql.NullString `gorm:"column:customer_email"`
	CustomerFirstName sql.NullString `gorm:"column:customer_firstname"`
	CustomerLastName  sql.NullString `gorm:"column:customer_middlename"`
	CustomerIsGuest   sql.NullInt16  `gorm:"column:customer_is_guest"`
	RemoteIp          sql.NullString `gorm:"column:remote_ip"`
	// Promo
	AppliedRuleIDs sql.NullString `gorm:"column:applied_rule_ids"`
	CouponCode     sql.NullString `gorm:"column:coupon_code"`
	// totals
	BaseCurrencyCode  string `gorm:"column:base_currency_code"`
	QuoteCurrencyCode string `gorm:"column:quote_currency_code"`
	//ShippingAmount           sql.NullFloat64 `gorm:"column:shipping_amount"`
	//BaseShippingAmount       sql.NullFloat64 `gorm:"column:base_shipping_amount"`
	Subtotal                 sql.NullFloat64 `gorm:"column:subtotal"`
	BaseSubtotal             sql.NullFloat64 `gorm:"column:base_subtotal"`
	SubtotalWithDiscount     sql.NullFloat64 `gorm:"column:subtotal_with_discount"`
	BaseSubtotalWithDiscount sql.NullFloat64 `gorm:"column:base_subtotal_with_discount"`
	GrandTotal               sql.NullFloat64 `gorm:"column:grand_total"`
}

func (q *MagentoQuote) GetID() int64 {
	return q.EntityID
}

func (q *MagentoQuote) GetApiID() string {
	return fmt.Sprintf("%d|%s", q.EntityID, q.CartID)
}

func (q *MagentoQuote) New() *MagentoQuote {
	return &MagentoQuote{}
}

func (q *MagentoQuote) NewSlice() []*MagentoQuote {
	return []*MagentoQuote{}
}

func (q *MagentoQuote) TableName() string {
	return "sales_flat_quote"
}

var MissingCartIDCartError = errors.New("missing cart_id")
var MissingStoreIDCartError = errors.New("missing store_id")
var MissingCustomerIDCartError = errors.New("missing customer_id")
var InvalidTokenCartError = errors.New("invalid token cart")

func (q *MagentoQuote) Validate() error {
	if q.CartID == "" {
		return MissingCartIDCartError
	} else if q.StoreID < 1 {
		return MissingStoreIDCartError
	} else if q.CustomerID < 1 && q.CustomerIsGuest.Int16 < 1 {
		return MissingCustomerIDCartError
	} else if auth.JWTToken(q.RawToken).IsActive() == false {
		return InvalidTokenCartError
	}

	return nil
}

func (q *MagentoQuote) IsValid() bool {
	return q.Validate() == nil
}

var CantLoadQuote = errors.New("cant load quote")

func (q *MagentoQuote) Load() (*MagentoQuote, error) {
	repo := q.Repository()
	if q.GetID() > 0 {
		return repo.GetByID(q.GetID())
	} else if q.CartID != "" {
		return repo.GetByCartID(q.CartID)
	}

	return nil, CantLoadQuote
}

func (q *MagentoQuote) Save() (*MagentoQuote, error) {
	q.lock.Lock()
	defer q.lock.Unlock()
	return q.Repository().Save(q)
}

func (q *MagentoQuote) NewToken() *CartTokenData {
	exp := config.GetConfig().CartTokenExpirationSeconds()
	token := NewTokenFromQuote(q, exp)
	return token
}

func (q *MagentoQuote) GetToken() *CartTokenData {
	if q.Token != nil {
		return q.Token
	}

	q.Token = q.NewToken()
	q.RawToken, _ = q.Token.String()

	return q.Token
}

func (q *MagentoQuote) GetStringToken() string {
	str, err := q.GetToken().String()
	core_utils.DebugError(err)

	return str
}

func (q *MagentoQuote) SetRepository(r *MagentoQuoteRepository) *MagentoQuote {
	q.repo = r
	return q
}

func (q *MagentoQuote) Repository() *MagentoQuoteRepository {
	if q.repo == nil {
		q.repo = NewQuoteRepository(nil)
	}

	return q.repo
}

func (q *MagentoQuote) GetBillingAddress() *MagentoQuoteAddress {
	return q.GetAddress(AddressTypeBilling)
}

func (q *MagentoQuote) GetShippingAddress() *MagentoQuoteAddress {
	return q.GetAddress(AddressTypeShipping)
}

func (q *MagentoQuote) GetAddresses() []*MagentoQuoteAddress {
	q.loadAddresses()
	return q.Addresses
}

func (q *MagentoQuote) GetAddress(
	addressType AddressType,
) *MagentoQuoteAddress {
	q.loadAddresses()
	if len(q.Addresses) > 0 {
		for _, a := range q.Addresses {
			if a.AddressType == string(addressType) {
				return a
			}
		}

		return nil
	}

	return nil
}

func (q *MagentoQuote) loadAddresses() {
	q.lock.RLock()
	if q.EntityID < 1 {
		q.lock.RUnlock()
		core_utils.Debug("loading addresses failed - no entity_id")
		return
	} else if q.loadedAddresses {
		q.lock.RUnlock()
		return
	}

	q.lock.RUnlock()
	q.lock.Lock()
	defer q.lock.Unlock()

	if q.Addresses == nil || len(q.Addresses) < 1 {
		q.Addresses = []*MagentoQuoteAddress{}
		err := q.Repository().DB().Model(q).
			Association("Addresses").Find(&q.Addresses)
		core_utils.DebugError(err)
	}

	q.loadedAddresses = true
}

func (q *MagentoQuote) GetStore() *magento_core.StoreEntity {
	store := magento_core.GetStoreClient().GetStore()
	// @TODO: handle multi store
	return store
}
