package sales

import (
	"database/sql"
	"errors"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
)

type NewCartData struct {
	CustomerID int64
	CustomerIP string
}

type CartRepository interface {
	mage_types.EntityRepository[*MagentoQuote]
	GetStore() *magento_core.StoreEntity
	SetStore(*magento_core.StoreEntity)
	CreateNew(d NewCartData) (*MagentoQuote, error)
}

var _ CartRepository = (*MagentoQuoteRepository)(nil)

func NewQuoteRepository(db *gorm.DB) *MagentoQuoteRepository {
	result := &MagentoQuoteRepository{
		db: db,
	}
	if result.db == nil {
		result.db = magento_core.GetStoreClient().DbConn()
	}

	return result
}

type MagentoQuoteRepository struct {
	db    *gorm.DB
	store *magento_core.StoreEntity
}

func (r *MagentoQuoteRepository) GetStore() *magento_core.StoreEntity {
	if r.store == nil {
		return magento_core.GetStoreClient().GetStore()
	}

	return r.store
}

func (r *MagentoQuoteRepository) SetStore(entity *magento_core.StoreEntity) {
	r.store = entity
}

func (r *MagentoQuoteRepository) DB() *gorm.DB {
	if r.db == nil {
		return magento_core.GetStoreClient().DbConn()
	}

	return r.db
}

var ErrorNotCreatedQuote = errors.New("quote not created")

func (r *MagentoQuoteRepository) Save(q *MagentoQuote) (*MagentoQuote, error) {
	if err := q.Validate(); err != nil {
		return nil, err
	}

	// Exclude Payment from save to prevent overwriting additional_information
	err := r.DB().Omit("Payment").Save(q).Error
	if err != nil {
		return nil, err
	} else if q.EntityID < 1 || q.CartID == "" {
		return nil, ErrorNotCreatedQuote
	}

	return q, nil
}

func (r *MagentoQuoteRepository) GetByID(id int64) (*MagentoQuote, error) {
	result := &MagentoQuote{}
	err := r.DB().Model(result).
		Preload("Payment").
		Where("entity_id = ?", id).First(result).Error
	return result, err
}

const NewCartToken = "new-cart"

func (r *MagentoQuoteRepository) GetQuote(
	token string,
) (*MagentoQuote, error) {
	data, err := GetCartTokenData(token)
	if err != nil {
		return nil, err
	}

	quote, err := r.GetByCartID(data.CartID)
	if err != nil {
		return nil, err
	}
	quote.Token = data

	return quote, nil
}

func (r *MagentoQuoteRepository) CreateNew(d NewCartData) (*MagentoQuote, error) {
	store := r.GetStore()
	jwtToken := GetNewCartToken()
	quote := &MagentoQuote{
		RawToken: "",
		Token:    jwtToken,
		CartID:   GetUUID(),
		IsActive: sql.NullInt16{
			Int16: 1,
			Valid: true,
		},
		CustomerID: d.CustomerID,
		CustomerIsGuest: sql.NullInt16{
			Int16: core_utils.IF[int16](d.CustomerID > 0, 0, 1),
			Valid: true,
		},
		CheckoutMethod: sql.NullString{
			String: "guest",
			Valid:  true,
		},
		RemoteIp: sql.NullString{
			String: d.CustomerIP,
			Valid:  true,
		},
		StoreID:           int64(store.StoreID),
		BaseCurrencyCode:  store.GetBaseCurrencyCode(),
		QuoteCurrencyCode: store.GetBaseCurrencyCode(),
	}

	err := r.DB().Transaction(func(tx *gorm.DB) error {
		err := tx.Omit("Items").Create(quote).Error
		if err != nil {
			return err
		}

		for _, addressType := range []AddressType{
			AddressTypeBilling,
			AddressTypeShipping,
		} {
			address := MagentoQuoteAddress{
				QuoteID:     quote.EntityID,
				Quote:       quote,
				AddressType: string(addressType),
				CountryID: sql.NullString{
					String: store.GetCountryCode(),
					Valid:  true,
				},
				CustomerID: sql.NullInt64{
					Int64: d.CustomerID,
					Valid: true,
				},
				SameAsBilling: 1,
			}
			err = tx.Create(&address).Error
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return nil, err
	}

	jwtToken.CartID = quote.CartID
	quote.RawToken, err = jwtToken.String()

	return quote, err
}

func (r *MagentoQuoteRepository) GetByCartID(id string) (*MagentoQuote, error) {
	if id == "" {
		return nil, NoQuoteIDError
	}

	result := &MagentoQuote{}

	err := r.DB().Model(result).
		Preload("Payment").
		Where("uuid = ?", id).First(result).Error
	if err != nil {
		return nil, err
	} else if result.EntityID < 1 {
		return nil, NoQuoteIDError
	}

	result.repo = r

	return result, err
}
