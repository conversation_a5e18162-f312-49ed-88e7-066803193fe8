package sales

import (
	"database/sql"
)

type MagentoQuotePayment struct {
	PaymentID int64 `gorm:"primaryKey;column:payment_id"`
	QuoteID   int64 `gorm:"column:quote_id"`

	Method                string `gorm:"column:method"`
	AdditionalInformation string `gorm:"column:additional_information"`

	CreatedAt sql.NullTime `gorm:"autoCreateTime;column:created_at"`
	UpdatedAt sql.NullTime `gorm:"autoUpdateTime;column:updated_at"`
}

func (p *MagentoQuotePayment) TableName() string {
	return "sales_flat_quote_payment"
}
