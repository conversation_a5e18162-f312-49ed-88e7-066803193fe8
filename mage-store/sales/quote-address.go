package sales

import (
	"database/sql"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

type AddressType string

const (
	AddressTypeShipping AddressType = "shipping"
	AddressTypeBilling  AddressType = "billing"
)

type MagentoQuoteAddress struct {
	loaded bool `gorm:"-"`

	AddressID int64        `gorm:"column:address_id;primaryKey;autoIncrement"`
	CreatedAt sql.NullTime `gorm:"autoCreateTime;column:created_at"`
	UpdatedAt sql.NullTime `gorm:"autoUpdateTime;column:updated_at"`
	// quote
	AddressType string        `gorm:"column:address_type"`
	QuoteID     int64         `gorm:"column:quote_id"`
	Quote       *MagentoQuote `gorm:"foreignKey:QuoteID"`

	CustomerID sql.NullInt64 `gorm:"column:customer_id"`
	//SaveInAddressBook    sql.NullInt32  `gorm:"column:save_in_address_book"`
	CustomerAddressID sql.NullInt64  `gorm:"column:customer_address_id"`
	Email             sql.NullString `gorm:"column:email"`
	Prefix            sql.NullString `gorm:"column:prefix"`
	Firstname         sql.NullString `gorm:"column:firstname"`
	Middlename        sql.NullString `gorm:"column:middlename"`
	Lastname          sql.NullString `gorm:"column:lastname"`
	//Suffix               sql.NullString `gorm:"column:suffix"`
	//Company              sql.NullString `gorm:"column:company"`
	Street sql.NullString `gorm:"column:street"`

	// Custom
	CityID     sql.NullString `gorm:"column:city_id"`
	StoreCode  sql.NullString `gorm:"column:store_code"`
	OfficeCode sql.NullString `gorm:"column:office_code"`
	//
	City      sql.NullString `gorm:"column:city"`
	Region    sql.NullString `gorm:"column:region"`
	RegionID  sql.NullInt64  `gorm:"column:region_id"` // not set
	Postcode  sql.NullString `gorm:"column:postcode"`
	CountryID sql.NullString `gorm:"column:country_id"` // BG
	Telephone sql.NullString `gorm:"column:telephone"`
	//Fax                  sql.NullString `gorm:"column:fax"`
	SameAsBilling        int32          `gorm:"column:same_as_billing"` // 1
	FreeShipping         int32          `gorm:"column:free_shipping"`
	CollectShippingRates int32          `gorm:"column:collect_shipping_rates"`
	ShippingMethod       sql.NullString `gorm:"column:shipping_method"`
	ShippingDescription  sql.NullString `gorm:"column:shipping_description"`
	Weight               float64        `gorm:"column:weight"`
	//Subtotal                    float64         `gorm:"column:subtotal"`
	//BaseSubtotal                float64         `gorm:"column:base_subtotal"`
	//SubtotalWithDiscount        float64         `gorm:"column:subtotal_with_discount"`
	//BaseSubtotalWithDiscount    float64         `gorm:"column:base_subtotal_with_discount"`
	//TaxAmount                   float64         `gorm:"column:tax_amount"`
	//BaseTaxAmount               float64         `gorm:"column:base_tax_amount"`
	ShippingAmount     float64 `gorm:"column:shipping_amount"`
	BaseShippingAmount float64 `gorm:"column:base_shipping_amount"`
	//ShippingTaxAmount           sql.NullFloat64 `gorm:"column:shipping_tax_amount"`
	//BaseShippingTaxAmount       sql.NullFloat64 `gorm:"column:base_shipping_tax_amount"`
	DiscountAmount     float64        `gorm:"column:discount_amount"`
	BaseDiscountAmount float64        `gorm:"column:base_discount_amount"`
	GrandTotal         float64        `gorm:"column:grand_total"`
	BaseGrandTotal     float64        `gorm:"column:base_grand_total"`
	CustomerNotes      sql.NullString `gorm:"column:customer_notes"`
	//AppliedTaxes                sql.NullString  `gorm:"column:applied_taxes"`
	DiscountDescription        sql.NullString  `gorm:"column:discount_description"`
	ShippingDiscountAmount     sql.NullFloat64 `gorm:"column:shipping_discount_amount"`
	BaseShippingDiscountAmount sql.NullFloat64 `gorm:"column:base_shipping_discount_amount"`
	//SubtotalInclTax             sql.NullFloat64 `gorm:"column:subtotal_incl_tax"`
	//BaseSubtotalTotalInclTax    sql.NullFloat64 `gorm:"column:base_subtotal_total_incl_tax"`
	//HiddenTaxAmount             sql.NullFloat64 `gorm:"column:hidden_tax_amount"`
	//BaseHiddenTaxAmount         sql.NullFloat64 `gorm:"column:base_hidden_tax_amount"`
	//ShippingHiddenTaxAmount     sql.NullFloat64 `gorm:"column:shipping_hidden_tax_amount"`
	//BaseShippingHiddenTaxAmount sql.NullFloat64 `gorm:"column:base_shipping_hidden_tax_amnt"`
	//ShippingInclTax             sql.NullFloat64 `gorm:"column:shipping_incl_tax"`
	//BaseShippingInclTax         sql.NullFloat64 `gorm:"column:base_shipping_incl_tax"`
	//VatID                             sql.NullString  `gorm:"column:vat_id"`
	//VatIsValid                        sql.NullInt32   `gorm:"column:vat_is_valid"`
	//VatRequestID                      sql.NullString  `gorm:"column:vat_request_id"`
	//VatRequestDate                    sql.NullString  `gorm:"column:vat_request_date"`
	//VatRequestSuccess                 sql.NullInt32   `gorm:"column:vat_request_success"`
	//GiftMessageID                     sql.NullInt32   `gorm:"column:gift_message_id"`
	Invoice                sql.NullString `gorm:"column:invoice"` // requires invoiced
	InvoiceType            sql.NullString `gorm:"column:invoice_type"`
	InvoiceCompanyName     sql.NullString `gorm:"column:invoice_company_name"`
	InvoiceCompanyMol      sql.NullString `gorm:"column:invoice_company_mol"`
	InvoiceCompanyCity     sql.NullString `gorm:"column:invoice_company_city"`
	InvoiceCompanyAddress  sql.NullString `gorm:"column:invoice_company_address"`
	InvoiceCompanyBulstat  sql.NullString `gorm:"column:invoice_company_bulstat"`
	InvoiceCompanyVat      sql.NullString `gorm:"column:invoice_company_vat"`
	InvoicePersonalName    sql.NullString `gorm:"column:invoice_personal_name"`
	InvoicePersonalPin     sql.NullString `gorm:"column:invoice_personal_pin"`
	InvoicePersonalCity    sql.NullString `gorm:"column:invoice_personal_city"`
	InvoicePersonalVat     sql.NullString `gorm:"column:invoice_personal_vat"`
	InvoicePersonalAddress sql.NullString `gorm:"column:invoice_personal_address"`
	//CfBilling1                        string          `gorm:"column:cf_billing1"`
	//CfShipping1                       string          `gorm:"column:cf_shipping1"`
	//CfBilling2                        string          `gorm:"column:cf_billing2"`
	//CfShipping2                       string          `gorm:"column:cf_shipping2"`
	//CfBilling3                        string          `gorm:"column:cf_billing3"`
	//CfShipping3                       string          `gorm:"column:cf_shipping3"`
	PraktisShippingDiscountAmount     float64       `gorm:"column:praktis_shipping_discount_amount"`
	BasePraktisShippingDiscountAmount float64       `gorm:"column:base_praktis_shipping_discount_amount"`
	CashOnDeliveryTaxAmount           float64       `gorm:"column:cash_on_delivery_tax_amount"`
	BaseCashOnDeliveryTaxAmount       float64       `gorm:"column:base_cash_on_delivery_tax_amount"`
	ShippingType                      sql.NullInt32 `gorm:"column:shipping_type"`

	ShippingRates []*MagentoQuoteShippingRate `gorm:"foreignKey:AddressID"`
}

func (i *MagentoQuoteAddress) TableName() string {
	return "sales_flat_quote_address"
}

func (i *MagentoQuoteAddress) GetRates() []*MagentoQuoteShippingRate {
	if len(i.ShippingRates) > 0 || i.loaded {
		return i.ShippingRates
	}

	db := magento_core.GetStoreClient().DbConn()
	err := db.Model(i).Association("ShippingRates").Find(&i.ShippingRates)
	core_utils.ErrorWarning(err)
	i.loaded = true

	return i.ShippingRates
}
