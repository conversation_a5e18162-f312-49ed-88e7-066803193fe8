package sales

import (
	cache_utils "github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

func GetStatusLabel(status string) string {
	cachekey := "order:statuses"
	cache := magento_core.GetStoreClient().GetCacheClient()

	if cache.MustExists(cachekey) {
		val, err := cache.GetMapValue(cachekey, status)
		if err == nil {
			return val
		} else {
			core_utils.ErrorWarning(err)
			return status
		}
	}

	type StatusMap struct {
		Status string `json:"status"`
		Label  string `json:"label"`
	}

	var statusMap []StatusMap
	err := magento_core.GetStoreClient().DbConn().Raw(`SELECT
    sos.status,
    COALESCE(sosl.label, sos.label) as label
FROM
    sales_order_status sos
        LEFT JOIN
    sales_order_status_label sosl
    ON sos.status = sosl.status
        AND sosl.store_id = 1
order by sos.status asc`).Scan(&statusMap).Error
	core_utils.ErrorWarning(err)

	if len(statusMap) != 0 {
		statuses := make(map[string]string)
		for _, v := range statusMap {
			statuses[v.Status] = v.Label
		}
		err = cache.SaveMap(cachekey, statuses, cache_utils.InfiniteTTL)
		core_utils.ErrorWarning(err)
	}

	return status
}
