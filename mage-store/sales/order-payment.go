package sales

type MagentoOrderPayment struct {
	EntityID              int64  `gorm:"primaryKey;column:entity_id"`
	ParentID              int64  `gorm:"column:parent_id"`
	QuotePaymentID        int64  `gorm:"column:quote_payment_id"`
	Method                string `gorm:"column:method"`
	AdditionalInformation string `gorm:"column:additional_information"`

	Order *MagentoOrder `gorm:"foreignKey:ParentID;references:EntityID"`
}

func (MagentoOrderPayment) TableName() string {
	return "sales_flat_order_payment"
}
