package sales

type MagentoOrderItem struct {
	ItemID       int64   `gorm:"primaryKey;column:item_id"`
	OrderID      int64   `gorm:"column:order_id"`
	Sku          string  `gorm:"column:sku"`
	QtyOrdered   float64 `gorm:"column:qty_ordered"`
	ParentItemID int64   `gorm:"column:parent_item_id"`
	QuoteItemID  int64   `gorm:"column:quote_item_id"`
	ProductID    int64   `gorm:"column:product_id"`

	Price           float64 `gorm:"column:price"`
	BasePrice       float64 `gorm:"column:base_price"`
	DiscountPct     float64 `gorm:"column:discount_percent"`
	DiscountAmt     float64 `gorm:"column:discount_amount"`
	BaseDiscountAmt float64 `gorm:"column:base_discount_amount"`
	TaxPct          float64 `gorm:"column:tax_percent"`
	TaxAmt          float64 `gorm:"column:tax_amount"`
	BaseTaxAmt      float64 `gorm:"column:base_tax_amount"`
	RowTotal        float64 `gorm:"column:row_total"`
	BaseRowTotal    float64 `gorm:"column:base_row_total"`

	// Relationships
	Order      *MagentoOrder       `gorm:"foreignKey:OrderID;references:EntityID"`
	ParentItem *MagentoOrderItem   `gorm:"foreignKey:ParentItemID;references:ItemID"`
	ChildItems []*MagentoOrderItem `gorm:"foreignKey:ParentItemID;references:ItemID"`
}

func (MagentoOrderItem) TableName() string {
	return "sales_flat_order_item"
}
