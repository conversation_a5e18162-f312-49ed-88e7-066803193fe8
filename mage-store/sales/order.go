package sales

import (
	"database/sql"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"sync"
)

var _ mage_types.StoreEntity[*MagentoOrder] = (*MagentoOrder)(nil)

type MagentoOrder struct {
	lock sync.Mutex              `gorm:"-"`
	repo *MagentoOrderRepository `gorm:"-"`

	EntityID            int64           `gorm:"primaryKey;column:entity_id"`
	IncrementID         sql.NullString  `gorm:"column:increment_id"`
	State               sql.NullString  `gorm:"column:state"`
	Status              sql.NullString  `gorm:"column:status"`
	CouponCode          sql.NullString  `gorm:"column:coupon_code"`
	ProtectCode         sql.NullString  `gorm:"column:protect_code"`
	ShippingDescription sql.NullString  `gorm:"column:shipping_description"`
	IsVirtual           sql.NullInt16   `gorm:"column:is_virtual"`
	StoreID             sql.NullInt16   `gorm:"column:store_id"`
	CustomerID          sql.NullInt32   `gorm:"column:customer_id"`
	DiscountAmount      sql.NullFloat64 `gorm:"column:discount_amount"`
	GrandTotal          sql.NullFloat64 `gorm:"column:grand_total"`
	ShippingAmount      sql.NullFloat64 `gorm:"column:shipping_amount"`
	ShippingTaxAmount   sql.NullFloat64 `gorm:"column:shipping_tax_amount"`
	Subtotal            sql.NullFloat64 `gorm:"column:subtotal"`
	TaxAmount           sql.NullFloat64 `gorm:"column:tax_amount"`
	TotalPaid           sql.NullFloat64 `gorm:"column:total_paid"`
	TotalQtyOrdered     sql.NullFloat64 `gorm:"column:total_qty_ordered"`
	TotalRefunded       sql.NullFloat64 `gorm:"column:total_refunded"`
	CustomerIsGuest     sql.NullInt16   `gorm:"column:customer_is_guest"`
	CustomerNoteNotify  sql.NullInt16   `gorm:"column:customer_note_notify"`
	//// billing
	BillingAddressID int64                `gorm:"column:billing_address_id"`
	BillingAddress   *MagentoOrderAddress `gorm:"-"`

	ShippingAddressID int64                `gorm:"column:shipping_address_id"`
	ShippingAddress   *MagentoOrderAddress `gorm:"-"`

	CustomerGroupID sql.NullInt16 `gorm:"column:customer_group_id"`
	//EmailSent       sql.NullInt16 `gorm:"column:email_sent"`
	QuoteAddressID sql.NullInt32 `gorm:"column:quote_address_id"`
	QuoteID        sql.NullInt32 `gorm:"column:quote_id"`

	Weight                        sql.NullFloat64 `gorm:"column:weight"`
	BaseCurrencyCode              sql.NullString  `gorm:"column:base_currency_code"`
	CustomerEmail                 sql.NullString  `gorm:"column:customer_email"`
	CustomerFirstname             sql.NullString  `gorm:"column:customer_firstname"`
	CustomerLastname              sql.NullString  `gorm:"column:customer_lastname"`
	CustomerMiddlename            sql.NullString  `gorm:"column:customer_middlename"`
	CustomerTaxvat                sql.NullString  `gorm:"column:customer_taxvat"`
	DiscountDescription           sql.NullString  `gorm:"column:discount_description"`
	OrderCurrencyCode             sql.NullString  `gorm:"column:order_currency_code"`
	RemoteIP                      sql.NullString  `gorm:"column:remote_ip"`
	ShippingType                  sql.NullInt32   `gorm:"column:shipping_type"`
	ShippingMethod                sql.NullString  `gorm:"column:shipping_method"`
	StoreCurrencyCode             sql.NullString  `gorm:"column:store_currency_code"`
	CustomerNote                  sql.NullString  `gorm:"column:customer_note;type:text"`
	CreatedAt                     sql.NullTime    `gorm:"column:created_at;autoUpdateTime"`
	UpdatedAt                     sql.NullTime    `gorm:"column:updated_at;autoUpdateTime"`
	TotalItemCount                int16           `gorm:"column:total_item_count"`
	PraktisShippingDiscountAmount float64         `gorm:"column:praktis_shipping_discount_amount"`
	CashOnDeliveryTaxAmount       float64         `gorm:"column:cash_on_delivery_tax_amount"`

	// items

	Payment  *MagentoOrderPayment  `gorm:"foreignKey:ParentID;references:EntityID"`
	Shipment *MagentoOrderShipment `gorm:"foreignKey:OrderID;references:EntityID"`
	Items    []*MagentoOrderItem   `gorm:"foreignKey:OrderID;references:EntityID"`
}

func (o *MagentoOrder) GetID() int64 {
	return o.EntityID
}

func (o *MagentoOrder) Validate() error {
	return nil
}

func (o *MagentoOrder) IsValid() bool {
	return true
}

func (o *MagentoOrder) New() *MagentoOrder {
	return &MagentoOrder{}
}

func (o *MagentoOrder) NewSlice() []*MagentoOrder {
	return []*MagentoOrder{}
}

func (o *MagentoOrder) Save() (*MagentoOrder, error) {
	return NewOrderRepository(nil).Save(o)
}

// TableName specifies the table name to map to
func (*MagentoOrder) TableName() string {
	return "sales_flat_order"
}

func (o *MagentoOrder) SetRepository(r *MagentoOrderRepository) *MagentoOrder {
	o.repo = r
	return o
}

func (o *MagentoOrder) Repository() OrderRepository {
	if o.repo == nil {
		o.repo = NewOrderRepository(nil)
	}

	return o.repo
}

func (o *MagentoOrder) GetBillingAddress() *MagentoOrderAddress {
	o.lock.Lock()
	defer o.lock.Unlock()
	if o.BillingAddress == nil || o.BillingAddress.EntityID < 1 {
		if o.BillingAddressID > 0 {
			err := o.Repository().DB().Model(&MagentoOrderAddress{}).
				Where("entity_id = ?", o.BillingAddressID).
				First(&o.BillingAddress).Error
			core_utils.DebugError(err)
		} else {
			core_utils.Debug("no billing address")
		}
	}

	return o.BillingAddress
}

func (o *MagentoOrder) GetShippingAddress() *MagentoOrderAddress {
	o.lock.Lock()
	defer o.lock.Unlock()

	if o.ShippingAddress == nil || o.ShippingAddress.EntityID < 1 {
		if o.ShippingAddressID > 0 {
			err := o.Repository().DB().Model(&MagentoOrderAddress{}).
				Where("entity_id = ?", o.ShippingAddressID).
				First(&o.ShippingAddress).Error
			core_utils.DebugError(err)
		} else {
			core_utils.Debug("no shipping address")
		}
	}

	return o.ShippingAddress
}

func (o *MagentoOrder) GetStore() *magento_core.StoreEntity {
	store := magento_core.GetStoreClient().GetStore()
	// @TODO: handle multi store
	return store
}

func (o *MagentoOrder) GetItems() ([]*MagentoOrderItem, error) {
	o.lock.Lock()
	defer o.lock.Unlock()

	if len(o.Items) < 1 {
		err := o.Repository().DB().Model(&MagentoOrderItem{}).
			Where("order_id = ?", o.EntityID).
			First(&o.Items).Error
		if err != nil {
			return nil, err
		}
	}

	return o.Items, nil
}
