package sales

import (
	"gorm.io/gorm"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"sync"
)

type OrderRepository interface {
	mage_types.EntityRepository[*MagentoOrder]
	GetByIncrementID(incrementID string) (*MagentoOrder, error)
	GetStore() *magento_core.StoreEntity
	SetStore(*magento_core.StoreEntity)
}

var _ OrderRepository = (*MagentoOrderRepository)(nil)

func NewOrderRepository(db *gorm.DB) *MagentoOrderRepository {
	result := &MagentoOrderRepository{
		db: db,
	}
	if result.db == nil {
		result.db = magento_core.GetStoreClient().DbConn()
	}

	return result
}

type MagentoOrderRepository struct {
	lock  sync.Mutex
	db    *gorm.DB
	store *magento_core.StoreEntity
}

func (m *MagentoOrderRepository) DB() *gorm.DB {
	if m.db == nil {
		m.db = magento_core.GetStoreClient().DbConn()
	}

	return m.db
}

func (m *MagentoOrderRepository) Save(entity *MagentoOrder) (*MagentoOrder, error) {
	return nil, nil
}

func (m *MagentoOrderRepository) GetByID(id int64) (*MagentoOrder, error) {
	order := &MagentoOrder{}
	err := m.DB().
		Model(&MagentoOrder{}).Where("id = ?", id).
		Find(order).Error
	return order, err
}

func (m *MagentoOrderRepository) GetByIncrementID(incrementID string) (*MagentoOrder, error) {
	order := &MagentoOrder{}
	err := m.DB().
		Model(&MagentoOrder{}).
		Preload("Payment").
		Where("increment_id = ?", incrementID).
		First(order).Error
	return order, err
}

func (m *MagentoOrderRepository) GetStore() *magento_core.StoreEntity {
	if m.store == nil {
		return magento_core.GetStoreClient().GetStore()
	}

	return m.store
}

func (m *MagentoOrderRepository) SetStore(entity *magento_core.StoreEntity) {
	m.store = entity
}
