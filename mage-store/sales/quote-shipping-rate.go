package sales

import (
	"database/sql"
	"time"
)

func (q *MagentoQuote) GetShippingRates() []*MagentoQuoteShippingRate {
	q.loadAddresses()
	address := q.GetShippingAddress()
	if address != nil {
		return address.GetRates()
	}

	return []*MagentoQuoteShippingRate{}
}

type MagentoQuoteShippingRate struct {
	RateID            uint           `gorm:"primaryKey;column:rate_id"`
	AddressID         uint           `gorm:"column:address_id"`
	CreatedAt         time.Time      `gorm:"column:created_at"`
	UpdatedAt         time.Time      `gorm:"column:updated_at"`
	Carrier           sql.NullString `gorm:"column:carrier"`
	CarrierTitle      sql.NullString `gorm:"column:carrier_title"`
	Code              sql.NullString `gorm:"column:carrier"`
	Method            sql.NullString `gorm:"column:method"`
	MethodDescription sql.NullString `gorm:"column:method_description"`
	Price             float64        `gorm:"column:price"`
	ErrorMessage      sql.NullString `gorm:"column:error_message"`
	MethodTitle       sql.NullString `gorm:"column:method_title"`

	Address *MagentoQuoteAddress `gorm:"foreignKey:AddressID"`
}

func (MagentoQuoteShippingRate) TableName() string {
	return "sales_flat_quote_shipping_rate"
}
