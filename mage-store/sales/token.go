package sales

import (
	"errors"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/sha3"
	"praktis.bg/store-api/internal/config"
	"praktis.bg/store-api/packages/magento-core/auth"
	"praktis.bg/store-api/packages/magento-core/types"
	"strings"
	"time"
)

var ExpiredSalesTokenError = errors.New("cart token is expired")

func NewTokenFromQuote(
	q *MagentoQuote,
	exp time.Duration,
) *CartTokenData {
	items := q.MustGetItems()
	var skus = make([]string, len(items))
	for i, item := range items {
		skus[i] = item.Sku
	}

	return newTokenData(q.CartID, skus, exp)
}

func NewToken(exp time.Duration) *CartTokenData {
	return newTokenData("", []string{}, exp)
}

func GetCartTokenData(token string) (*CartTokenData, error) {
	if token == "" || token == NewCartToken {
		return nil, NoQuoteIDError
	}

	claims, err := auth.JWTToken(token).GetClaims()
	if err != nil {
		return nil, err
	}

	var data = &CartTokenData{}
	data.SetData(claims)
	if data.IsExpired() {
		return nil, ExpiredSalesTokenError
	}

	return data, nil
}

var NoQuoteIDError = errors.New("no cart id")

func getSalt(cartID string, skusData string) string {
	if cartID != "" {
		return ""
	}

	_saltStr := fmt.Sprintf("slt345re:%s", skusData)
	_saltStr = fmt.Sprintf("%s:%s", cartID, _saltStr)

	salt := sha3.New256().Sum([]byte(_saltStr))
	return fmt.Sprintf("%x", salt)
}

func newTokenData(
	cartID string,
	skus []string,
	expiration time.Duration,
) *CartTokenData {
	var salt string
	productsCount := len(skus)
	skusData := ""
	if cartID != "" {
		if len(skus) > 0 {
			skusData = strings.Join(skus, ",")
		}
		salt = getSalt(cartID, skusData)
	}

	return &CartTokenData{
		Salt:           salt,
		CartID:         cartID,
		Skus:           skusData,
		ProductsCount:  productsCount,
		CreationTime:   time.Now().Unix(),
		ExpirationTime: time.Now().Add(expiration).Unix(),
	}
}

type CartTokenData struct {
	Salt           string `json:"salt"`
	CartID         string `json:"cart_id"`
	Skus           string `json:"skus"`
	ProductsCount  int    `json:"products_count"`
	CreationTime   int64  `json:"crt"`
	ExpirationTime int64  `json:"exp"`
}

func (c *CartTokenData) String() (string, error) {
	if c == nil {
		return "", fmt.Errorf("cart token is nil")
	}

	token, err := c.ToToken()
	if err != nil {
		return "", err
	}

	return token.String(), nil
}

func (c *CartTokenData) ToToken() (auth.JWTToken, error) {
	return auth.GetNewToken(c.ToClaims())
}

func (c *CartTokenData) ToClaims() jwt.MapClaims {
	return jwt.MapClaims{
		"salt":           c.Salt,
		"cart_id":        c.CartID,
		"skus":           c.Skus,
		"products_count": c.ProductsCount,
		"crt":            c.CreationTime,
		"exp":            c.ExpirationTime,
	}
}

func getTimestamp(data interface{}) int64 {
	switch data.(type) {
	case float64:
		return int64(data.(float64))
	case int64:
		return data.(int64)
	case int:
		return int64(data.(int))
	default:
		return 0
	}
}

func (c *CartTokenData) SetData(data jwt.MapClaims) {
	c.Salt, _ = data["salt"].(string)
	c.CartID, _ = data["cart_id"].(string)
	c.Skus, _ = data["skus"].(string)
	count, _ := data["products_count"]
	c.ProductsCount = types.ToInt(count)
	c.CreationTime = getTimestamp(data["crt"])
	c.ExpirationTime = getTimestamp(data["exp"])
}

func (c *CartTokenData) IsExpired() bool {
	now := time.Now().Unix()
	return c.ExpirationTime < now || c.CreationTime > now
}

func GetNewCartToken() *CartTokenData {
	exp := config.GetConfig().CartTokenExpirationSeconds()
	return NewToken(exp)
}
