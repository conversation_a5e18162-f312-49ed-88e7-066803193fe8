package mage_store

import (
	"errors"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"strings"
)

type CategoryImage string

func (i CategoryImage) ToUrl() string {
	if i == "" {
		return ""
	}

	store := magento_core.GetStoreClient().GetStore()
	return store.GetImageUrl("/catalog/category/" + string(i))
}

type treePath string

func (p treePath) GetParentIds() []int {
	_p := string(p)
	var ids []int
	if _p != "" {
		rawIds := strings.Split(_p, "/")
		for _, rawId := range rawIds[:len(rawIds)-1] {
			id, _ := strconv.Atoi(rawId)
			ids = append(ids, id)
		}
	}

	return ids
}

var _ types.IMageEntity = (*CategoryEntity)(nil)

type CategoryEntity struct {
	EntityID types.EntityID
	Name     string   `gorm:"column:name"`
	UrlPath  string   `gorm:"column:url_path"`
	Path     treePath `gorm:"column:path"`
	Position int      `gorm:"column:position"`
	Level    int      `gorm:"column:level"`

	Attributes types.AttributesData `gorm:"-"`

	cacheKey      string `gorm:"-"`
	isCacheLoaded bool   `gorm:"-"`
}

func (c *CategoryEntity) Validate() error {
	if c != nil && c.EntityID > 0 &&
		(c.Name != "" || c.Path != "" || c.UrlPath != "") {
		return nil
	}

	return errors.New("invalid entity")
}

func (c *CategoryEntity) IsValid() bool {
	return c.Validate() == nil
}

func (c *CategoryEntity) GetID() types.EntityID {
	return c.EntityID
}

func (c *CategoryEntity) GetEntityType() types.EntityType {
	return types.CategoryEntityType
}

func (c *CategoryEntity) setValues(key string, val interface{}) {
	switch strings.ToLower(key) {
	case "entity_id", "id", "entityid":
		c.EntityID = types.ToEntityID(val)
	case "name":
		c.Name = types.ToStr(val)
	case "url_path":
		c.UrlPath = types.ToStr(val)
	case "path":
		c.Path = treePath(types.ToStr(val))
	case "position":
		c.Position = types.ToInt(val)
	case "level":
		c.Level = types.ToInt(val)
	default:
		if c.Attributes == nil {
			c.Attributes = make(types.AttributesData)
		}
		c.Attributes[key] = types.ToStr(val)
	}
}

func (c *CategoryEntity) SetData(m types.EntityData) {
	for k, v := range m {
		c.setValues(k, v)
	}
}

func (c *CategoryEntity) SetStrData(m map[string]string) *CategoryEntity {
	for k, v := range m {
		c.setValues(k, v)
	}

	return c
}

func (c *CategoryEntity) GetAttributeValue(key string) string {
	data := c.GetData(key)

	val, ok := data[key]
	if !ok {
		return ""
	}

	var valStr string
	valStr, ok = val.(string)
	if !ok {
		return ""
	}

	return valStr
}

func (c *CategoryEntity) GetAttributeValueP(key string) *string {
	return core_utils.ToStringPointer(c.GetAttributeValue(key))
}

func (c *CategoryEntity) GetData(keys ...string) types.EntityData {
	result := make(types.EntityData)
	if len(keys) == 0 {
		result = types.EntityData{
			"entity_id": c.EntityID,
			"name":      c.Name,
			"url_path":  c.UrlPath,
			"path":      string(c.Path),
			"position":  c.Position,
			"level":     c.Level,
		}
		for k, v := range c.Attributes {
			result[k] = v
		}
	} else {
		for _, key := range keys {
			switch key {
			case "entity_id":
				result[key] = c.EntityID
			case "name":
				result[key] = c.Name
			case "url_path":
				result[key] = c.UrlPath
			case "path":
				result[key] = string(c.Path)
			case "position":
				result[key] = c.Position
			case "level":
				result[key] = c.Level
			default:
				attrVal, ok := c.Attributes[key]
				if !ok {
					//core_utils.Notice("Attribute %s not found", key)
					result[key] = ""
				} else {
					result[key] = attrVal
				}
			}
		}
	}

	return result
}

func (c *CategoryEntity) TableName() string {
	return "catalog_category_entity"
}

func (c *CategoryEntity) Load(id types.EntityID, attrs ...string) error {
	categoryRepo := NewCategoryRepository()
	collection := categoryRepo.GetCollection().
		Select("e.entity_id, e.path, e.level, e.position").
		Where(
			WhereExpr("e.entity_id = %d", id),
		)

	for _, attr := range attrs {
		if attr == "path" || attr == "level" || attr == "position" || attr == "entity_id" {
			// not attributes
			continue
		}

		collection = collection.Select(Select(":" + attr))
	}

	cat, err := collection.FirstItem()
	if err != nil {
		return err
	}

	return cat.copyTo(c)
}

func (c *CategoryEntity) copyTo(newE *CategoryEntity) error {
	if newE == nil {
		return errors.New("destination is not initialized")
	}

	if c == nil {
		return errors.New("source is not initialized")
	}

	if c.EntityID != 0 {
		newE.EntityID = c.EntityID
	}
	if c.Name != "" {
		newE.Name = c.Name
	}
	if c.UrlPath != "" {
		newE.UrlPath = c.UrlPath
	}
	if c.Path != "" {
		newE.Path = c.Path
	}
	if c.Position != 0 {
		newE.Position = c.Position
	}
	if c.Level != 0 {
		newE.Level = c.Level
	}

	if newE.Attributes == nil {
		newE.Attributes = make(types.AttributesData)
	}

	for k, v := range c.Attributes {
		newE.Attributes[k] = v
	}

	return nil
}

func (c *CategoryEntity) New() types.IEntity {
	return &CategoryEntity{}
}

func (c *CategoryEntity) NewSlice() []types.IEntity {
	var iEntitySlice []types.IEntity

	return iEntitySlice
}

func (c *CategoryEntity) GetParentID() types.EntityID {
	ids := c.Path.GetParentIds()
	if len(ids) == 0 {
		return 0
	}

	return types.ToEntityID(ids[len(ids)-1])
}

func (c *CategoryEntity) GetImageAttribute(code string) string {
	val := c.GetAttributeValue(code)
	return CategoryImage(val).ToUrl()
}

func (c *CategoryEntity) GetUrl() string {
	store := magento_core.GetStoreClient().GetStore()
	return store.GetBaseUrl(c.UrlPath)
}

func (c *CategoryEntity) GetRelPath() string {
	return "/" + strings.TrimRight(c.UrlPath, "/")
}
