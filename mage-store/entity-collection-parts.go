package mage_store

import (
	"fmt"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"regexp"
	"strings"
)

type AttributeString interface {
	ExtractAttributeCodes() []string
}

type Select string

func (s Select) String() string {
	return string(s)
}

func (s Select) ExtractAttributeCodes() []string {
	return extractAttributeCodes(s.String())
}

type SelectType int

const (
	GetFullSelect SelectType = 1 << iota
	GetAttributesOnly
	GetWithoutAttributes
)

func (s Select) GetSelect(o SelectType) string {
	val := s.String()
	parts := strings.Split(val, ",")
	var newParts []string
	for _, part := range parts {
		if strings.Contains(part, ":") {
			if o == GetAttributesOnly || o == GetFullSelect {
				newParts = append(
					newParts,
					getAttributeSelect(part, magento_core.DefaultStoreID),
				)
			} else {
				continue
			}
		} else {
			newParts = append(newParts, strings.TrimSpace(part))
		}
	}

	return strings.TrimSpace(strings.Join(newParts, ", "))
}

type Where string

func (w Where) String() string {
	return string(w)
}

func WhereExpr(val string, args ...interface{}) Where {
	return Where(fmt.Sprintf(val, args...))
}

func (w Where) ExtractAttributeCodes() []string {
	return extractAttributeCodes(w.String())
}

func (w Where) GetWhere() string {
	val := w.String()
	codes := w.ExtractAttributeCodes()

	for _, code := range codes {
		val = strings.ReplaceAll(val,
			":"+code,
			fmt.Sprintf("%s.value", getAttributeAlias(code, magento_core.DefaultStoreID)),
		)
	}

	return strings.TrimSpace(val)
}

type Order string

func (o Order) String() string {
	return string(o)
}

func (o Order) GetOrder() string {
	val := o.String()
	codes := o.ExtractAttributeCodes()

	for _, code := range codes {
		val = strings.ReplaceAll(val,
			":"+code,
			fmt.Sprintf("%s.value", getAttributeAlias(code, magento_core.DefaultStoreID)),
		)
	}

	return strings.TrimSpace(val)
}

func (o Order) ExtractAttributeCodes() []string {
	return extractAttributeCodes(o.String())
}

func extractAttributeCodes(val string) []string {
	var codes []string
	if strings.Contains(val, ":") {
		re := regexp.MustCompile(`:\w+`)
		matches := re.FindAllString(val, -1)

		for _, match := range matches {
			match = strings.TrimPrefix(match, ":")
			codes = append(codes, match)
		}
	}

	return codes
}
