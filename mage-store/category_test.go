package mage_store

import (
	"praktis.bg/store-api/packages/magento-core/types"
	"reflect"
	"testing"
)

func TestCategorySetData(t *testing.T) {
	data := types.EntityData{
		"entity_id":   1,
		"name":        "test",
		"url_path":    "/test",
		"path":        "1/2/3",
		"position":    1,
		"level":       1,
		"description": "test",
		"thumbnail":   "test",
	}

	var category CategoryEntity
	category.SetData(data)

	if category.EntityID != 1 {
		t.Fatal("Expected ID to be 1")
	}

	if category.Name != "test" {
		t.Fatal("Expected Name to be test")
	}

	if category.UrlPath != "/test" {
		t.Fatal("Expected UrlPath to be /test")
	}

	if category.Path != "1/2/3" || reflect.TypeOf(category.Path).String() != "mage_entity.treePath" {
		t.<PERSON>al("Expected Path to be 1/2/3")
	}

	if category.Position != 1 {
		t.<PERSON>al("Expected Position to be 1")
	}

	if category.Level != 1 {
		t.Fatal("Expected Level to be 1")
	}

	if category.Attributes["description"] != "test" {
		t.<PERSON>al("Expected Description to be test")
	}

	if category.Attributes["thumbnail"] != "test" {
		t.Fatal("Expected Thumbnail to be test")
	}
}

func Test_CategoryGetData(t *testing.T) {
	category := CategoryEntity{
		EntityID: 1,
		Name:     "test",
		UrlPath:  "/test",
		Path:     "1/2/3",
		Position: 1,
		Level:    1,
		Attributes: types.AttributesData{
			"description": "test",
			"thumbnail":   "test",
		},
	}

	data := category.GetData()
	if data["entity_id"] != types.EntityID(1) {
		t.Fatal("Expected entity_id to be 1")
	} else if data["name"] != "test" {
		t.Fatal("Expected name to be test")
	} else if data["url_path"] != "/test" {
		t.Fatal("Expected url_path to be /test")
	} else if data["path"] != "1/2/3" {
		t.Fatal("Expected path to be 1/2/3")
	} else if data["position"] != 1 {
		t.Fatal("Expected position to be 1")
	} else if data["level"] != 1 {
		t.Fatal("Expected level to be 1")
	} else if data["description"] != "test" {
		t.Fatal("Expected description to be test")
	} else if data["thumbnail"] != "test" {
		t.Fatal("Expected thumbnail to be test")
	}

	data = category.GetData("name", "path", "description", "test2")
	if len(data) != 4 {
		t.Fatal("Expected data to have 4 elements")
	}

	if data["name"] != "test" || data["path"] != "1/2/3" || data["description"] != "test" || data["test2"] != "" {
		t.Fatal("Expected data to be correct")
	}
}

func Test_DataCopyForEntity(t *testing.T) {
	category := &CategoryEntity{
		EntityID: 1,
		Name:     "test",
		UrlPath:  "/test",
		Path:     "1/2/3",
		Position: 1,
		Level:    1,
		Attributes: types.AttributesData{
			"description": "test",
			"thumbnail":   "test",
		},
	}

	var copyCat *CategoryEntity
	category.copyTo(copyCat)

	if int(copyCat.EntityID) != 1 {
		t.Fatal("Expected ID to be 1")
	} else if copyCat.Name != "test" {
		t.Fatal("Expected Name to be test")
	} else if copyCat.UrlPath != "/test" {
		t.Fatal("Expected UrlPath to be /test")
	} else if copyCat.Path != "1/2/3" || reflect.TypeOf(copyCat.Path).String() != "mage_entity.treePath" {
		t.Fatal("Expected Path to be 1/2/3")
	} else if copyCat.Position != 1 {
		t.Fatal("Expected Position to be 1")
	} else if copyCat.Level != 1 {
		t.Fatal("Expected Level to be 1")
	} else if copyCat.Attributes["description"] != "test" {
		t.Fatal("Expected Description to be test")
	} else if copyCat.Attributes["thumbnail"] != "test" {
		t.Fatal("Expected Thumbnail to be test")
	}
}

func Test_GetParentID(t *testing.T) {
	category := &CategoryEntity{
		EntityID: 1,
		Name:     "test",
		UrlPath:  "/test",
		Path:     "1/2/3",
		Position: 1,
		Level:    1,
		Attributes: types.AttributesData{
			"description": "test",
			"thumbnail":   "test",
		},
	}

	if category.GetParentID() != 2 {
		t.Fatal("Expected parent ID to be 2")
	}

	category.Path = "1"
	if category.GetParentID() != 0 {
		t.Fatal("Expected parent ID to be 0")
	}

	category.Path = "1/2/3/4433/5"
	if category.GetParentID() != 4433 {
		t.Fatal("Expected parent ID to be 4433")
	}
}

func TestCategoryEntity_IsValid(t *testing.T) {
	var category *CategoryEntity

	if category != nil {
		t.Fatal("Expected category to be nil")
	}

	if category.Validate() {
		t.Fatal("Expected category to be invalid")
	}
}
