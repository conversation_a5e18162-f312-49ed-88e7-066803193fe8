package mage_store

import (
	"gorm.io/gorm"
	mage_entity "praktis.bg/store-api/packages/magento-core"
)

type CmsBlockRepository struct {
	dbConn *gorm.DB
	store  *mage_entity.StoreEntity
}

func NewCmsBlockRepository(store *mage_entity.StoreEntity) *CmsBlockRepository {
	return &CmsBlockRepository{
		dbConn: mage_entity.GetStoreClient().DbConn(),
		store:  store,
	}
}

func (r *CmsBlockRepository) Get(identifier string) (*CmsBlock, error) {
	var err error
	cmsBlock := &CmsBlock{
		Identifier: identifier,
		StoreID:    int64(r.store.GetID()),
	}

	cacheClient := mage_entity.GetStoreClient().GetCacheClient()

	if cacheClient.MustExists(cmsBlock.CacheKey()) {
		err = cacheClient.LoadObj(cmsBlock)
		if err != nil {
			return cmsBlock, err
		}
	}

	dbSelect := r.DB().Model(&cmsBlock).Where("identifier = ?", identifier)
	err = dbSelect.First(cmsBlock).Error
	if err != nil {
		return cmsBlock, err
	}

	if cmsBlock.Content != "" {
		err = cacheClient.SaveObj(cmsBlock)
		if err != nil {
			return cmsBlock, err
		}
	}

	return cmsBlock, nil
}

func (r *CmsBlockRepository) DB() *gorm.DB {
	return r.dbConn
}
