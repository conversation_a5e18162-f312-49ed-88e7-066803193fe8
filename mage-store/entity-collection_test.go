package mage_store

import (
	core_utils "github.com/siper92/core-utils"
	"testing"
)

func TestSelect_ExtractAttributeCodes(t *testing.T) {
	selectS := Select("name, url_path, thumbnail")
	codes := selectS.ExtractAttributeCodes()
	if len(codes) != 0 {
		t.<PERSON><PERSON>("Expected no codes")
	}

	selectS = "name, url_path, :thumbnail"
	codes = selectS.ExtractAttributeCodes()
	if len(codes) != 1 {
		t.<PERSON>("Expected 1 code")
	}

	if codes[0] != "thumbnail" {
		t.<PERSON>("Expected :thumbnail code")
	}

	selectS = ":name :url_path :thumbnail"
	codes = selectS.ExtractAttributeCodes()
	if len(codes) != 3 {
		t.<PERSON><PERSON>("Expected 3 codes")
	}

	if codes[0] != "name" || codes[1] != "url_path" || codes[2] != "thumbnail" {
		t.<PERSON>("Expected :name :url_path :thumbnail codes")
	}
}

func TestSelect_GetRestOfSelect(t *testing.T) {
	selectS := Select("name, url_path, thumbnail")
	rest := selectS.GetSelect(GetWithoutAttributes)
	if rest != "name, url_path, thumbnail" {
		t.Fatal("Expected name, url_path, thumbnail")
	}

	selectS = "name, url_path, :thumbnail"
	rest = selectS.GetSelect(GetWithoutAttributes)
	if rest != "name, url_path" {
		t.Fatal("Expected 'name, url_path'")
	}

	selectS = ":name :url_path :thumbnail"
	rest = selectS.GetSelect(GetWithoutAttributes)
	if rest != "" {
		t.Fatal("Expected empty string")
	}

	selectS = ":name, url_path, :thumbnail"
	rest = selectS.GetSelect(GetWithoutAttributes)
	if rest != "url_path" {
		t.Fatal("Expected 'url_path'")
	}
}

func TestSelect_GetSelectWithAttributes(t *testing.T) {
	selectS := Select("name, :thumbnail")
	rest := selectS.GetSelect(GetFullSelect)

	core_utils.AMatchesB(t, rest, "name, attr_thumbnail_0.value as thumbnail")

	selectS = ":name, :url"
	rest = selectS.GetSelect(GetFullSelect)
	core_utils.AMatchesB(
		t,
		rest,
		"attr_name_0.value as name, attr_url_0.value as url",
	)
}

func TestOrder_GetOrder(t *testing.T) {
	order := Order("name ASC")
	core_utils.AMatchesB(t, order.GetOrder(), "name ASC")

	order = ":name ASC"
	core_utils.AMatchesB(
		t,
		order.GetOrder(),
		"attr_name_0.value ASC",
	)

	order = ":level, e.position ASC"
	core_utils.AMatchesB(
		t,
		order.GetOrder(),
		"attr_level_0.value, e.position ASC",
	)
}

func TestWhere_GetWhere(t *testing.T) {
	where := Where(":name = 'test'")
	core_utils.AMatchesB(
		t,
		where.GetWhere(),
		"attr_name_0.value = 'test'",
	)

	attrCode := where.ExtractAttributeCodes()
	if len(attrCode) != 1 || attrCode[0] != "name" {
		t.Fatal("Expected 1 code name")
	}
}
