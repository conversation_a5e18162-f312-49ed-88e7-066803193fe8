package mage_store

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	"praktis.bg/store-api/internal/praktis"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

type CMSPageRepository struct {
	db    *gorm.DB
	store *magento_core.StoreEntity
}

func NewCMSPageRepository(store *magento_core.StoreEntity) *CMSPageRepository {
	return &CMSPageRepository{
		db:    magento_core.GetStoreClient().DbConn(),
		store: store,
	}
}

func (c *CMSPageRepository) Get(id int64) (*CMSPageEntity, error) {
	page := &CMSPageEntity{
		PageID: id,
	}
	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	if cacheClient.MustExists(page.CacheKey()) {
		err := cacheClient.LoadObj(page)
		return page, err
	}

	res := c.DB().Raw(`select * from cms_page
left join cms_page_store on cms_page_store.page_id = cms_page.page_id
where (cms_page_store.store_id = ? or cms_page_store.store_id = 0 or cms_page_store.store_id is null)
   and cms_page.page_id = ?
order by cms_page_store.store_id desc limit 1`, c.store.GetID(), id).Find(page)
	if res.Error != nil {
		return nil, res.Error
	}

	connect, err := praktis.GetNewStoreContentConnector()
	if err != nil {
		return nil, err
	}

	content, err := connect.GetPageContent(fmt.Sprintf("%d", id))
	page.Content = content

	core_utils.ErrorWarning(cacheClient.SaveObj(page))

	return page, nil
}

func (c *CMSPageRepository) DB() *gorm.DB {
	return c.db
}
