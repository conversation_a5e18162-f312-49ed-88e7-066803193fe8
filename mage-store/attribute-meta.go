package mage_store

import (
	"errors"
	"fmt"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/storage"
	"praktis.bg/store-api/packages/magento-core/types"
	"sync"
	"time"
)

type TableAttributesMapping map[storage.DbTable][]int

type AttributeProvider interface {
	GetAttributeCode(id int) (string, error)
	GetAttribute(attrCode string) (CoreAttribute, error)
	GetOptions(attrCode string) ([]CoreAttributeOption, error)
	SplitToByTable(
		attrCodes []string,
	) (TableAttributesMapping, error)
}

type EntityAttributeProvider interface {
	AttributeProvider
	mage_types.CacheProvider
	CacheKey() string
	GetEntityType() types.EntityType
}

var ProductAttributes EntityAttributeProvider = &AttributesMeta{
	entityType: types.ProductEntityType,
}

var CustomerAttributes EntityAttributeProvider = &AttributesMeta{
	entityType: types.CustomerEntityType,
}

var CustomerAddressAttributes EntityAttributeProvider = &AttributesMeta{
	entityType: types.CustomerAddressEntityType,
}

type AttributesMeta struct {
	prodivder   mage_types.CacheProvider
	lock        sync.RWMutex
	optionsLock sync.RWMutex

	attributesIdCodeMap map[int]string
	attributes          map[string]CoreAttribute
	optionsMap          map[string][]CoreAttributeOption
	entityType          types.EntityType
	initTime            int64
}

func (a *AttributesMeta) CacheKey() string {
	return fmt.Sprintf("attributes:is_init:%s", a.GetEntityType().String())
}

func (a *AttributesMeta) OptionsCacheKey() string {
	return fmt.Sprintf("attributes:options_init:%s", a.GetEntityType().String())
}

func (a *AttributesMeta) Cache() *cache.RedisCacheProvider {
	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	return cacheClient
}

func (a *AttributesMeta) loadAttributeMetaOnce() error {
	a.lock.RLock()
	if a.requiresInit() == false {
		a.lock.RUnlock()
		return nil
	}

	a.lock.RUnlock()

	return a.loadAttributesMeta()
}

func (a *AttributesMeta) loadAttributeOptionsMetaOnce() error {
	a.optionsLock.RLock()
	if a.requiresOptionsInit() == false {
		a.optionsLock.RUnlock()
		return nil
	}

	a.optionsLock.RUnlock()

	return a.loadAttributesOptionsMeta()
}

func (a *AttributesMeta) GetEntityType() types.EntityType {
	return a.entityType
}

func (a *AttributesMeta) GetAttributeCode(id int) (string, error) {
	if err := a.loadAttributeMetaOnce(); err != nil {
		return "", err
	}

	val, ok := a.attributesIdCodeMap[id]
	if !ok {
		return "", fmt.Errorf("attribute id %d - no code foud", id)
	}

	return val, nil
}

var NoAttributeError = errors.New("no attribute meta found")

type NoAttributeCodeError struct {
	Err           error
	AttributeCode string
	EntityType    types.EntityType
}

func (f *NoAttributeCodeError) Error() string {
	return fmt.Sprintf("[%s]:%s -> err -> %v", f.EntityType, f.AttributeCode, f.Err)
}

func (f *NoAttributeCodeError) Is(target error) bool {
	return target == NoAttributeError
}

func NewNoAttributeError(code string, eType types.EntityType) error {
	return &NoAttributeCodeError{
		Err:           NoAttributeError,
		AttributeCode: code,
		EntityType:    eType,
	}
}

func (a *AttributesMeta) GetAttribute(attrCode string) (CoreAttribute, error) {
	err := a.loadAttributeMetaOnce()
	if err != nil {
		return CoreAttribute{}, err
	}

	val, ok := a.attributes[attrCode]
	if !ok {
		return CoreAttribute{}, NewNoAttributeError(
			attrCode, a.entityType,
		)
	}

	return val, nil
}

func (a *AttributesMeta) loadAttributesMeta() error {
	a.lock.Lock()
	defer a.lock.Unlock()
	var err error

	if len(a.attributes) < 1 || a.requiresInit() {
		var loadedAttrs []CoreAttribute

		var sql string
		if a.GetEntityType() == types.CustomerEntityType ||
			a.GetEntityType() == types.CustomerAddressEntityType {
			sql = `
select a.attribute_id as attribute_id, 
       a.backend_type as backend_type, 
       a.attribute_code as attribute_code,
       eet.entity_type_code as entity_type,
       COALESCE(a.frontend_label, a.attribute_code) as frontend_label,
       cea.sort_order as sort_order
from eav_attribute a 
    inner join eav_entity_type eet on a.entity_type_id = eet.entity_type_id
	inner join customer_eav_attribute cea on a.attribute_id = cea.attribute_id
where eet.entity_type_code = ?`
		} else {
			sql = `
select a.attribute_id as attribute_id, 
       a.backend_type as backend_type, 
       a.attribute_code as attribute_code,
       eet.entity_type_code as entity_type,
       COALESCE(a.frontend_label, a.attribute_code) as frontend_label,
       cea.position as sort_order
from eav_attribute a 
    inner join eav_entity_type eet on a.entity_type_id = eet.entity_type_id
	inner join catalog_eav_attribute cea on a.attribute_id = cea.attribute_id
where eet.entity_type_code = ?`
		}

		db := magento_core.GetStoreClient().DbConn()
		err = db.Raw(sql, a.GetEntityType().String()).Scan(&loadedAttrs).Error
		if err != nil {
			return err
		}

		a.attributesIdCodeMap = make(map[int]string)
		a.attributes = make(map[string]CoreAttribute)
		for _, attr := range loadedAttrs {
			a.attributesIdCodeMap[attr.AttributeID] = attr.AttributeCode.String()
			a.attributes[attr.AttributeCode.String()] = attr
		}
	}

	a.initTime = time.Now().Unix()
	return a.Cache().Save(a.CacheKey(), "1", 0)
}

func (a *AttributesMeta) requiresInit() bool {
	return len(a.attributes) < 1 || a.Cache().MustExists(a.CacheKey()) == false
}

func (a *AttributesMeta) GetOptions(attrCode string) ([]CoreAttributeOption, error) {
	err := a.loadAttributeOptionsMetaOnce()
	if err != nil {
		return []CoreAttributeOption{}, &NoAttributeCodeError{
			Err:           err,
			AttributeCode: attrCode,
			EntityType:    a.entityType,
		}
	}

	val, ok := a.optionsMap[attrCode]
	if !ok {
		return []CoreAttributeOption{}, &NoAttributeCodeError{
			Err:           fmt.Errorf("no options found"),
			AttributeCode: attrCode,
			EntityType:    a.entityType,
		}
	}

	return val, nil
}

func (a *AttributesMeta) requiresOptionsInit() bool {
	return len(a.optionsMap) < 1 || a.Cache().MustExists(a.OptionsCacheKey()) == false
}

type OptionsResult struct {
	AttributeCode string `json:"attribute_code"`
	OptionID      int    `json:"option_id"`
	StoreID       int    `json:"store_id"`
	Value         string `json:"value"`
	SortOrder     int    `json:"sort_order"`
}

func (a *AttributesMeta) loadAttributesOptionsMeta() error {
	a.optionsLock.Lock()
	defer a.optionsLock.Unlock()

	if a.requiresOptionsInit() {
		resultType := make([]*OptionsResult, 0)
		db := magento_core.GetStoreClient().DbConn()
		err := db.Raw(`select a.attribute_code, ov.option_id, ov.store_id, ov.value, o.sort_order
from eav_attribute_option_value ov
    inner join eav_attribute_option o on ov.option_id = o.option_id
    inner join eav_attribute a on o.attribute_id = a.attribute_id
    inner join eav_entity_type eet on a.entity_type_id = eet.entity_type_id
where eet.entity_type_code = ? and ov.store_id in (0, ?)`,
			a.GetEntityType().String(),
			magento_core.GetStoreClient().GetStore().GetID(),
		).Scan(&resultType).Error
		if err != nil {
			return err
		}

		options := map[string]map[int]CoreAttributeOption{}
		for _, v := range resultType {
			if _, ok := options[v.AttributeCode]; !ok {
				options[v.AttributeCode] = map[int]CoreAttributeOption{}
			}

			if _, ok := options[v.AttributeCode][v.OptionID]; !ok {

				options[v.AttributeCode][v.OptionID] = CoreAttributeOption{
					Value:     v.OptionID,
					Label:     v.Value,
					SortOrder: v.SortOrder,
				}
			} else if v.StoreID > 0 {
				options[v.AttributeCode][v.OptionID] = CoreAttributeOption{
					Value:     v.OptionID,
					Label:     v.Value,
					SortOrder: v.SortOrder,
				}
			}
		}

		a.optionsMap = make(map[string][]CoreAttributeOption)
		for k, v := range options {
			for _, opt := range v {
				a.optionsMap[k] = append(a.optionsMap[k], opt)
			}
		}
	}

	a.initTime = time.Now().Unix()
	return a.Cache().Save(a.OptionsCacheKey(), "1", 0)
}

func (a *AttributesMeta) SplitToByTable(
	attrCodes []string,
) (TableAttributesMapping, error) {
	tables := make(TableAttributesMapping)
	for _, attrCode := range attrCodes {
		attrEntity, err := a.GetAttribute(attrCode)
		if err != nil || attrEntity.GetID() < 1 {
			core_utils.ErrorWarning(err)
			continue
		}

		table := attrEntity.GetMagentoTable()
		if _, ok := tables[table]; !ok {
			tables[table] = make([]int, 0)
		}
		tables[table] = append(tables[table], attrEntity.GetID())
	}

	return tables, nil
}
