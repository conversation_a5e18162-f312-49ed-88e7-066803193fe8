package mage_store

import (
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/packages/magento-core/types"
	"testing"
)

type TestEntity struct {
	ID         int
	Attributes types.AttributesData
}

func (te *TestEntity) SetData(d types.EntityData) {
	for k, v := range d {
		switch k {
		case "id":
			te.ID = v.(int)
		default:
			if te.Attributes == nil {
				te.Attributes = make(types.AttributesData)
			}
			if v, ok := v.(string); ok {
				te.Attributes[k] = v
			} else {
				core_utils.Warning("Attribute %s is not a string", k)
				te.Attributes[k] = ""
			}
		}

	}
}

func TestEntitySliceScanner(t *testing.T) {
	data := []map[string]interface{}{
		{
			"id":   1,
			"name": "test",
		},
		{
			"id":   2,
			"name": "test2",
			"test": "test",
		},
	}

	var entities []*TestEntity
	err := mapEntities(&data, &entities)
	if err != nil {
		t.Error(err)
	}

	if len(entities) != 2 {
		t.Fatal("Expected 2 entities")
	}

	if entities[0].ID != 1 || entities[1].ID != 2 {
		t.Fatal("Expected entities to be sorted by ID")
	}

	if entities[0].Attributes["name"] != "test" || entities[1].Attributes["name"] != "test2" {
		t.Fatal("Expected entities to have attributes")
	}

	if entities[0].Attributes["test"] != "" || entities[1].Attributes["test"] != "test" {
		t.Fatal("Expected entities to have attributes")
	}
}
