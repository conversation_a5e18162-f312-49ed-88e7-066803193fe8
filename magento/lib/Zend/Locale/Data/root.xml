<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2013 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9737 $"/>
		<generation date="$Date: 2014-02-14 16:49:13 -0600 (Fri, 14 Feb 2014) $"/>
		<language type="root"/>
	</identity>
	<localeDisplayNames>
		<localeDisplayPattern>
			<localePattern>{0} ({1})</localePattern>
			<localeSeparator>{0}, {1}</localeSeparator>
			<localeKeyTypePattern>{0}: {1}</localeKeyTypePattern>
		</localeDisplayPattern>
		<measurementSystemNames>
			<measurementSystemName type="metric">Metric</measurementSystemName>
			<measurementSystemName type="UK">UK</measurementSystemName>
			<measurementSystemName type="US">US</measurementSystemName>
		</measurementSystemNames>
		<codePatterns>
			<codePattern type="language">{0}</codePattern>
			<codePattern type="script">{0}</codePattern>
			<codePattern type="territory">{0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<layout>
		<orientation>
			<characterOrder>left-to-right</characterOrder>
			<lineOrder>top-to-bottom</lineOrder>
		</orientation>
	</layout>
	<characters>
		<exemplarCharacters>[]</exemplarCharacters>
		<exemplarCharacters type="auxiliary">[]</exemplarCharacters>
		<exemplarCharacters type="punctuation">[\- , ; \: ! ? . ( ) \[ \] \{ \}]</exemplarCharacters>
		<ellipsis type="final">{0}…</ellipsis>
		<ellipsis type="initial">…{0}</ellipsis>
		<ellipsis type="medial">{0}…{1}</ellipsis>
		<ellipsis type="word-final">{0} …</ellipsis>
		<ellipsis type="word-initial">… {0}</ellipsis>
		<ellipsis type="word-medial">{0} … {1}</ellipsis>
		<moreInformation>?</moreInformation>
	</characters>
	<delimiters>
		<quotationStart>“</quotationStart>
		<quotationEnd>”</quotationEnd>
		<alternateQuotationStart>‘</alternateQuotationStart>
		<alternateQuotationEnd>’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="buddhist">
				<months>
					<alias source="locale" path="../../calendar[@type='gregorian']/months"/>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">BE</era>
						<!-- = 544 BC gregorian. -->
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="chinese">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../monthWidth[@type='wide']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">M01</month>
							<month type="2">M02</month>
							<month type="3">M03</month>
							<month type="4">M04</month>
							<month type="5">M05</month>
							<month type="6">M06</month>
							<month type="7">M07</month>
							<month type="8">M08</month>
							<month type="9">M09</month>
							<month type="10">M10</month>
							<month type="11">M11</month>
							<month type="12">M12</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<monthPatterns>
					<monthPatternContext type="format">
						<monthPatternWidth type="abbreviated">
							<alias source="locale" path="../monthPatternWidth[@type='wide']"/>
						</monthPatternWidth>
						<monthPatternWidth type="narrow">
							<alias source="locale" path="../../monthPatternContext[@type='stand-alone']/monthPatternWidth[@type='narrow']"/>
						</monthPatternWidth>
						<monthPatternWidth type="wide">
							<monthPattern type="leap">{0}bis</monthPattern>
						</monthPatternWidth>
					</monthPatternContext>
					<monthPatternContext type="numeric">
						<monthPatternWidth type="all">
							<monthPattern type="leap">{0}bis</monthPattern>
						</monthPatternWidth>
					</monthPatternContext>
					<monthPatternContext type="stand-alone">
						<monthPatternWidth type="abbreviated">
							<alias source="locale" path="../../monthPatternContext[@type='format']/monthPatternWidth[@type='abbreviated']"/>
						</monthPatternWidth>
						<monthPatternWidth type="narrow">
							<monthPattern type="leap">{0}b</monthPattern>
						</monthPatternWidth>
						<monthPatternWidth type="wide">
							<alias source="locale" path="../../monthPatternContext[@type='format']/monthPatternWidth[@type='wide']"/>
						</monthPatternWidth>
					</monthPatternContext>
				</monthPatterns>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<!-- chinese eras are computed, and don't fall back to gregorian -->
				<cyclicNameSets>
					<cyclicNameSet type="dayParts">
						<cyclicNameContext type="format">
							<cyclicNameWidth type="abbreviated">
								<cyclicName type="1">zi</cyclicName>
								<cyclicName type="2">chou</cyclicName>
								<cyclicName type="3">yin</cyclicName>
								<cyclicName type="4">mao</cyclicName>
								<cyclicName type="5">chen</cyclicName>
								<cyclicName type="6">si</cyclicName>
								<cyclicName type="7">wu</cyclicName>
								<cyclicName type="8">wei</cyclicName>
								<cyclicName type="9">shen</cyclicName>
								<cyclicName type="10">you</cyclicName>
								<cyclicName type="11">xu</cyclicName>
								<cyclicName type="12">hai</cyclicName>
							</cyclicNameWidth>
							<cyclicNameWidth type="narrow">
								<alias source="locale" path="../cyclicNameWidth[@type='abbreviated']"/>
							</cyclicNameWidth>
							<cyclicNameWidth type="wide">
								<alias source="locale" path="../cyclicNameWidth[@type='abbreviated']"/>
							</cyclicNameWidth>
						</cyclicNameContext>
					</cyclicNameSet>
					<cyclicNameSet type="days">
						<alias source="locale" path="../cyclicNameSet[@type='years']"/>
					</cyclicNameSet>
					<cyclicNameSet type="months">
						<alias source="locale" path="../cyclicNameSet[@type='years']"/>
					</cyclicNameSet>
					<cyclicNameSet type="years">
						<cyclicNameContext type="format">
							<cyclicNameWidth type="abbreviated">
								<cyclicName type="1">jia-zi</cyclicName>
								<cyclicName type="2">yi-chou</cyclicName>
								<cyclicName type="3">bing-yin</cyclicName>
								<cyclicName type="4">ding-mao</cyclicName>
								<cyclicName type="5">wu-chen</cyclicName>
								<cyclicName type="6">ji-si</cyclicName>
								<cyclicName type="7">geng-wu</cyclicName>
								<cyclicName type="8">xin-wei</cyclicName>
								<cyclicName type="9">ren-shen</cyclicName>
								<cyclicName type="10">gui-you</cyclicName>
								<cyclicName type="11">jia-xu</cyclicName>
								<cyclicName type="12">yi-hai</cyclicName>
								<cyclicName type="13">bing-zi</cyclicName>
								<cyclicName type="14">ding-chou</cyclicName>
								<cyclicName type="15">wu-yin</cyclicName>
								<cyclicName type="16">ji-mao</cyclicName>
								<cyclicName type="17">geng-chen</cyclicName>
								<cyclicName type="18">xin-si</cyclicName>
								<cyclicName type="19">ren-wu</cyclicName>
								<cyclicName type="20">gui-wei</cyclicName>
								<cyclicName type="21">jia-shen</cyclicName>
								<cyclicName type="22">yi-you</cyclicName>
								<cyclicName type="23">bing-xu</cyclicName>
								<cyclicName type="24">ding-hai</cyclicName>
								<cyclicName type="25">wu-zi</cyclicName>
								<cyclicName type="26">ji-chou</cyclicName>
								<cyclicName type="27">geng-yin</cyclicName>
								<cyclicName type="28">xin-mao</cyclicName>
								<cyclicName type="29">ren-chen</cyclicName>
								<cyclicName type="30">gui-si</cyclicName>
								<cyclicName type="31">jia-wu</cyclicName>
								<cyclicName type="32">yi-wei</cyclicName>
								<cyclicName type="33">bing-shen</cyclicName>
								<cyclicName type="34">ding-you</cyclicName>
								<cyclicName type="35">wu-xu</cyclicName>
								<cyclicName type="36">ji-hai</cyclicName>
								<cyclicName type="37">geng-zi</cyclicName>
								<cyclicName type="38">xin-chou</cyclicName>
								<cyclicName type="39">ren-yin</cyclicName>
								<cyclicName type="40">gui-mao</cyclicName>
								<cyclicName type="41">jia-chen</cyclicName>
								<cyclicName type="42">yi-si</cyclicName>
								<cyclicName type="43">bing-wu</cyclicName>
								<cyclicName type="44">ding-wei</cyclicName>
								<cyclicName type="45">wu-shen</cyclicName>
								<cyclicName type="46">ji-you</cyclicName>
								<cyclicName type="47">geng-xu</cyclicName>
								<cyclicName type="48">xin-hai</cyclicName>
								<cyclicName type="49">ren-zi</cyclicName>
								<cyclicName type="50">gui-chou</cyclicName>
								<cyclicName type="51">jia-yin</cyclicName>
								<cyclicName type="52">yi-mao</cyclicName>
								<cyclicName type="53">bing-chen</cyclicName>
								<cyclicName type="54">ding-si</cyclicName>
								<cyclicName type="55">wu-wu</cyclicName>
								<cyclicName type="56">ji-wei</cyclicName>
								<cyclicName type="57">geng-shen</cyclicName>
								<cyclicName type="58">xin-you</cyclicName>
								<cyclicName type="59">ren-xu</cyclicName>
								<cyclicName type="60">gui-hai</cyclicName>
							</cyclicNameWidth>
							<cyclicNameWidth type="narrow">
								<alias source="locale" path="../cyclicNameWidth[@type='abbreviated']"/>
							</cyclicNameWidth>
							<cyclicNameWidth type="wide">
								<alias source="locale" path="../cyclicNameWidth[@type='abbreviated']"/>
							</cyclicNameWidth>
						</cyclicNameContext>
					</cyclicNameSet>
					<cyclicNameSet type="zodiacs">
						<cyclicNameContext type="format">
							<cyclicNameWidth type="abbreviated">
								<alias source="locale" path="../../../cyclicNameSet[@type='dayParts']/cyclicNameContext[@type='format']/cyclicNameWidth[@type='abbreviated']"/>
							</cyclicNameWidth>
							<cyclicNameWidth type="narrow">
								<alias source="locale" path="../cyclicNameWidth[@type='abbreviated']"/>
							</cyclicNameWidth>
							<cyclicNameWidth type="wide">
								<alias source="locale" path="../cyclicNameWidth[@type='abbreviated']"/>
							</cyclicNameWidth>
						</cyclicNameContext>
					</cyclicNameSet>
				</cyclicNameSets>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>U MMMM d, EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>U MMMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>U MMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<!-- using y here to be short in locales that may not understand U anyway -->
							<pattern>y-MM-dd</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">d, E</dateFormatItem>
						<dateFormatItem id="Gy">U</dateFormatItem>
						<dateFormatItem id="GyMMM">U MMM</dateFormatItem>
						<dateFormatItem id="GyMMMd">U MMM d</dateFormatItem>
						<dateFormatItem id="GyMMMEd">U MMM d, E</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">MM-dd</dateFormatItem>
						<dateFormatItem id="MEd">MM-dd, E</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">MMM d, E</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">U</dateFormatItem>
						<dateFormatItem id="yMd">y-MM-dd</dateFormatItem>
						<dateFormatItem id="yyyy">U</dateFormatItem>
						<dateFormatItem id="yyyyM">y-MM</dateFormatItem>
						<dateFormatItem id="yyyyMd">y-MM-dd</dateFormatItem>
						<dateFormatItem id="yyyyMEd">y-MM-dd, E</dateFormatItem>
						<dateFormatItem id="yyyyMMM">U MMM</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">U MMM d</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">U MMM d, E</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">U QQQ</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">U QQQQ</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Day">{0} ({2}: {1})</appendItem>
						<appendItem request="Day-Of-Week">{0} {1}</appendItem>
						<appendItem request="Era">{1} {0}</appendItem>
						<appendItem request="Hour">{0} ({2}: {1})</appendItem>
						<appendItem request="Minute">{0} ({2}: {1})</appendItem>
						<appendItem request="Month">{0} ({2}: {1})</appendItem>
						<appendItem request="Quarter">{0} ({2}: {1})</appendItem>
						<appendItem request="Second">{0} ({2}: {1})</appendItem>
						<appendItem request="Timezone">{0} {1}</appendItem>
						<appendItem request="Week">{0} ({2}: {1})</appendItem>
						<appendItem request="Year">{1} {0}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">MM–MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">MM-dd – MM-dd</greatestDifference>
							<greatestDifference id="M">MM-dd – MM-dd</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">MM-dd, E – MM-dd, E</greatestDifference>
							<greatestDifference id="M">MM-dd, E – MM-dd, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">LLL–LLL</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">MMM d–d</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="M">MMM d, E – MMM d, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">U–U</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">y-MM – y-MM</greatestDifference>
							<greatestDifference id="y">y-MM – y-MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">y-MM-dd – y-MM-dd</greatestDifference>
							<greatestDifference id="M">y-MM-dd – y-MM-dd</greatestDifference>
							<greatestDifference id="y">y-MM-dd – y-MM-dd</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">y-MM-dd, E – y-MM-dd, E</greatestDifference>
							<greatestDifference id="M">y-MM-dd, E – y-MM-dd, E</greatestDifference>
							<greatestDifference id="y">y-MM-dd, E – y-MM-dd, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">U MMM–MMM</greatestDifference>
							<greatestDifference id="y">U MMM – U MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">U MMM d–d</greatestDifference>
							<greatestDifference id="M">U MMM d – MMM d</greatestDifference>
							<greatestDifference id="y">U MMM d – U MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">U MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="M">U MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="y">U MMM d, E – U MMM d, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">U MMMM–MMMM</greatestDifference>
							<greatestDifference id="y">U MMMM – U MMMM</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="coptic">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../monthWidth[@type='wide']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Tout</month>
							<month type="2">Baba</month>
							<month type="3">Hator</month>
							<month type="4">Kiahk</month>
							<month type="5">Toba</month>
							<month type="6">Amshir</month>
							<month type="7">Baramhat</month>
							<month type="8">Baramouda</month>
							<month type="9">Bashans</month>
							<month type="10">Paona</month>
							<month type="11">Epep</month>
							<month type="12">Mesra</month>
							<month type="13">Nasie</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
							<month type="13">13</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">ERA0</era>
						<era type="1">ERA1</era>
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="dangi">
				<months>
					<alias source="locale" path="../../calendar[@type='chinese']/months"/>
				</months>
				<monthPatterns>
					<alias source="locale" path="../../calendar[@type='chinese']/monthPatterns"/>
				</monthPatterns>
				<days>
					<alias source="locale" path="../../calendar[@type='chinese']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='chinese']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='chinese']/dayPeriods"/>
				</dayPeriods>
				<cyclicNameSets>
					<alias source="locale" path="../../calendar[@type='chinese']/cyclicNameSets"/>
				</cyclicNameSets>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='chinese']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='chinese']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='chinese']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="ethiopic">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../monthWidth[@type='wide']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Meskerem</month>
							<month type="2">Tekemt</month>
							<month type="3">Hedar</month>
							<month type="4">Tahsas</month>
							<month type="5">Ter</month>
							<month type="6">Yekatit</month>
							<month type="7">Megabit</month>
							<month type="8">Miazia</month>
							<month type="9">Genbot</month>
							<month type="10">Sene</month>
							<month type="11">Hamle</month>
							<month type="12">Nehasse</month>
							<month type="13">Pagumen</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
							<month type="13">13</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">ERA0</era>
						<era type="1">ERA1</era>
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="ethiopic-amete-alem">
				<months>
					<alias source="locale" path="../../calendar[@type='ethiopic']/months"/>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='ethiopic']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='ethiopic']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='ethiopic']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">ERA0</era>
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='ethiopic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='ethiopic']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='ethiopic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="generic">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../monthWidth[@type='wide']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">M01</month>
							<month type="2">M02</month>
							<month type="3">M03</month>
							<month type="4">M04</month>
							<month type="5">M05</month>
							<month type="6">M06</month>
							<month type="7">M07</month>
							<month type="8">M08</month>
							<month type="9">M09</month>
							<month type="10">M10</month>
							<month type="11">M11</month>
							<month type="12">M12</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">ERA0</era>
						<era type="1">ERA1</era>
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>G y MMMM d, EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>G y MMMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>G y MMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>GGGGG y-MM-dd</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">d, E</dateFormatItem>
						<dateFormatItem id="Ehm">E h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">G y</dateFormatItem>
						<dateFormatItem id="GyMMM">G y MMM</dateFormatItem>
						<dateFormatItem id="GyMMMd">G y MMM d</dateFormatItem>
						<dateFormatItem id="GyMMMEd">G y MMM d, E</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">MM-dd</dateFormatItem>
						<dateFormatItem id="MEd">MM-dd, E</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">MMM d, E</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">G y</dateFormatItem>
						<dateFormatItem id="yyyy">G y</dateFormatItem>
						<dateFormatItem id="yyyyM">GGGGG y-MM</dateFormatItem>
						<dateFormatItem id="yyyyMd">GGGGG y-MM-dd</dateFormatItem>
						<dateFormatItem id="yyyyMEd">GGGGG y-MM-dd, E</dateFormatItem>
						<dateFormatItem id="yyyyMMM">G y MMM</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">G y MMM d</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">G y MMM d, E</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">G y QQQ</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">G y QQQQ</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Day">{0} ({2}: {1})</appendItem>
						<appendItem request="Day-Of-Week">{0} {1}</appendItem>
						<appendItem request="Era">{1} {0}</appendItem>
						<appendItem request="Hour">{0} ({2}: {1})</appendItem>
						<appendItem request="Minute">{0} ({2}: {1})</appendItem>
						<appendItem request="Month">{0} ({2}: {1})</appendItem>
						<appendItem request="Quarter">{0} ({2}: {1})</appendItem>
						<appendItem request="Second">{0} ({2}: {1})</appendItem>
						<appendItem request="Timezone">{0} {1}</appendItem>
						<appendItem request="Week">{0} ({2}: {1})</appendItem>
						<appendItem request="Year">{1} {0}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">MM–MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">MM-dd – MM-dd</greatestDifference>
							<greatestDifference id="M">MM-dd – MM-dd</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">MM-dd, E – MM-dd, E</greatestDifference>
							<greatestDifference id="M">MM-dd, E – MM-dd, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">LLL–LLL</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">MMM d–d</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="M">MMM d, E – MMM d, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">G y–y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">GGGGG y-MM – y-MM</greatestDifference>
							<greatestDifference id="y">GGGGG y-MM – y-MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">GGGGG y-MM-dd – y-MM-dd</greatestDifference>
							<greatestDifference id="M">GGGGG y-MM-dd – y-MM-dd</greatestDifference>
							<greatestDifference id="y">GGGGG y-MM-dd – y-MM-dd</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">GGGGG y-MM-dd, E – y-MM-dd, E</greatestDifference>
							<greatestDifference id="M">GGGGG y-MM-dd, E – y-MM-dd, E</greatestDifference>
							<greatestDifference id="y">GGGGG y-MM-dd, E – y-MM-dd, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">G y MMM–MMM</greatestDifference>
							<greatestDifference id="y">G y MMM – y MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">G y MMM d–d</greatestDifference>
							<greatestDifference id="M">G y MMM d – MMM d</greatestDifference>
							<greatestDifference id="y">G y MMM d – y MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">G y MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="M">G y MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="y">G y MMM d, E – y MMM d, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">G y MMMM–MMMM</greatestDifference>
							<greatestDifference id="y">G y MMMM – y MMMM</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../monthWidth[@type='wide']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">M01</month>
							<month type="2">M02</month>
							<month type="3">M03</month>
							<month type="4">M04</month>
							<month type="5">M05</month>
							<month type="6">M06</month>
							<month type="7">M07</month>
							<month type="8">M08</month>
							<month type="9">M09</month>
							<month type="10">M10</month>
							<month type="11">M11</month>
							<month type="12">M12</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<alias source="locale" path="../dayWidth[@type='wide']"/>
						</dayWidth>
						<dayWidth type="narrow">
							<alias source="locale" path="../../dayContext[@type='stand-alone']/dayWidth[@type='narrow']"/>
						</dayWidth>
						<dayWidth type="short">
							<alias source="locale" path="../dayWidth[@type='abbreviated']"/>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Sun</day>
							<day type="mon">Mon</day>
							<day type="tue">Tue</day>
							<day type="wed">Wed</day>
							<day type="thu">Thu</day>
							<day type="fri">Fri</day>
							<day type="sat">Sat</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="abbreviated">
							<alias source="locale" path="../../dayContext[@type='format']/dayWidth[@type='abbreviated']"/>
						</dayWidth>
						<dayWidth type="narrow">
							<day type="sun">S</day>
							<day type="mon">M</day>
							<day type="tue">T</day>
							<day type="wed">W</day>
							<day type="thu">T</day>
							<day type="fri">F</day>
							<day type="sat">S</day>
						</dayWidth>
						<dayWidth type="short">
							<alias source="locale" path="../../dayContext[@type='format']/dayWidth[@type='short']"/>
						</dayWidth>
						<dayWidth type="wide">
							<alias source="locale" path="../../dayContext[@type='format']/dayWidth[@type='wide']"/>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="format">
						<quarterWidth type="abbreviated">
							<alias source="locale" path="../quarterWidth[@type='wide']"/>
						</quarterWidth>
						<quarterWidth type="narrow">
							<alias source="locale" path="../../quarterContext[@type='stand-alone']/quarterWidth[@type='narrow']"/>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">Q1</quarter>
							<quarter type="2">Q2</quarter>
							<quarter type="3">Q3</quarter>
							<quarter type="4">Q4</quarter>
						</quarterWidth>
					</quarterContext>
					<quarterContext type="stand-alone">
						<quarterWidth type="abbreviated">
							<alias source="locale" path="../../quarterContext[@type='format']/quarterWidth[@type='abbreviated']"/>
						</quarterWidth>
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<alias source="locale" path="../../quarterContext[@type='format']/quarterWidth[@type='wide']"/>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="abbreviated">
							<alias source="locale" path="../dayPeriodWidth[@type='wide']"/>
						</dayPeriodWidth>
						<dayPeriodWidth type="narrow">
							<dayPeriod type="am">a</dayPeriod>
                                                        <dayPeriod type="noon">n</dayPeriod>
							<dayPeriod type="pm">p</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">AM</dayPeriod>
                                                        <dayPeriod type="noon">noon</dayPeriod>
							<dayPeriod type="pm">PM</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
					<dayPeriodContext type="stand-alone">
						<alias source="locale" path="../dayPeriodContext[@type='format']"/>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">BCE</era>
						<era type="1">CE</era>
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>y MMMM d, EEEE</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>y MMMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>y MMM d</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>y-MM-dd</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>HH:mm:ss zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>HH:mm:ss z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>HH:mm:ss</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>HH:mm</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1} {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">d, E</dateFormatItem>
						<dateFormatItem id="Ehm">E h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">G y</dateFormatItem>
						<dateFormatItem id="GyMMM">G y MMM</dateFormatItem>
						<dateFormatItem id="GyMMMd">G y MMM d</dateFormatItem>
						<dateFormatItem id="GyMMMEd">G y MMM d, E</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">MM-dd</dateFormatItem>
						<dateFormatItem id="MEd">MM-dd, E</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">MMM d, E</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">y-MM</dateFormatItem>
						<dateFormatItem id="yMd">y-MM-dd</dateFormatItem>
						<dateFormatItem id="yMEd">y-MM-dd, E</dateFormatItem>
						<dateFormatItem id="yMMM">y MMM</dateFormatItem>
						<dateFormatItem id="yMMMd">y MMM d</dateFormatItem>
						<dateFormatItem id="yMMMEd">y MMM d, E</dateFormatItem>
						<dateFormatItem id="yQQQ">y QQQ</dateFormatItem>
						<dateFormatItem id="yQQQQ">y QQQQ</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Day">{0} ({2}: {1})</appendItem>
						<appendItem request="Day-Of-Week">{0} {1}</appendItem>
						<appendItem request="Era">{1} {0}</appendItem>
						<appendItem request="Hour">{0} ({2}: {1})</appendItem>
						<appendItem request="Minute">{0} ({2}: {1})</appendItem>
						<appendItem request="Month">{0} ({2}: {1})</appendItem>
						<appendItem request="Quarter">{0} ({2}: {1})</appendItem>
						<appendItem request="Second">{0} ({2}: {1})</appendItem>
						<appendItem request="Timezone">{0} {1}</appendItem>
						<appendItem request="Week">{0} ({2}: {1})</appendItem>
						<appendItem request="Year">{1} {0}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d–d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h–h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH–HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm–HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm–h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm–h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm–HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm–HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h–h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH–HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">MM–MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">MM-dd – MM-dd</greatestDifference>
							<greatestDifference id="M">MM-dd – MM-dd</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">MM-dd, E – MM-dd, E</greatestDifference>
							<greatestDifference id="M">MM-dd, E – MM-dd, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">LLL–LLL</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">MMM d–d</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="M">MMM d, E – MMM d, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y–y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">y-MM – y-MM</greatestDifference>
							<greatestDifference id="y">y-MM – y-MM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">y-MM-dd – y-MM-dd</greatestDifference>
							<greatestDifference id="M">y-MM-dd – y-MM-dd</greatestDifference>
							<greatestDifference id="y">y-MM-dd – y-MM-dd</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">y-MM-dd, E – y-MM-dd, E</greatestDifference>
							<greatestDifference id="M">y-MM-dd, E – y-MM-dd, E</greatestDifference>
							<greatestDifference id="y">y-MM-dd, E – y-MM-dd, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">y MMM–MMM</greatestDifference>
							<greatestDifference id="y">y MMM – y MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">y MMM d–d</greatestDifference>
							<greatestDifference id="M">y MMM d – MMM d</greatestDifference>
							<greatestDifference id="y">y MMM d – y MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">y MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="M">y MMM d, E – MMM d, E</greatestDifference>
							<greatestDifference id="y">y MMM d, E – y MMM d, E</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">y MMMM–MMMM</greatestDifference>
							<greatestDifference id="y">y MMMM – y MMMM</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="hebrew">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../monthWidth[@type='wide']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Tishri</month>
							<month type="2">Heshvan</month>
							<month type="3">Kislev</month>
							<month type="4">Tevet</month>
							<month type="5">Shevat</month>
							<month type="6">Adar I</month>
							<month type="7">Adar</month>
							<month type="7" yeartype="leap">Adar II</month>
							<month type="8">Nisan</month>
							<month type="9">Iyar</month>
							<month type="10">Sivan</month>
							<month type="11">Tamuz</month>
							<month type="12">Av</month>
							<month type="13">Elul</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="7" yeartype="leap">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
							<month type="13">13</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">AM</era>
						<!-- HY = Anno Mundi
							 = -180799862400000 milliseconds since 1/1/1970 AD
						-->
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="indian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../monthWidth[@type='wide']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Chaitra</month>
							<month type="2">Vaisakha</month>
							<month type="3">Jyaistha</month>
							<month type="4">Asadha</month>
							<month type="5">Sravana</month>
							<month type="6">Bhadra</month>
							<month type="7">Asvina</month>
							<month type="8">Kartika</month>
							<month type="9">Agrahayana</month>
							<month type="10">Pausa</month>
							<month type="11">Magha</month>
							<month type="12">Phalguna</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">Saka</era>
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="islamic">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Muh.</month>
							<month type="2">Saf.</month>
							<month type="3">Rab. I</month>
							<month type="4">Rab. II</month>
							<month type="5">Jum. I</month>
							<month type="6">Jum. II</month>
							<month type="7">Raj.</month>
							<month type="8">Sha.</month>
							<month type="9">Ram.</month>
							<month type="10">Shaw.</month>
							<month type="11">Dhuʻl-Q.</month>
							<month type="12">Dhuʻl-H.</month>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Muharram</month>
							<month type="2">Safar</month>
							<month type="3">Rabiʻ I</month>
							<month type="4">Rabiʻ II</month>
							<month type="5">Jumada I</month>
							<month type="6">Jumada II</month>
							<month type="7">Rajab</month>
							<month type="8">Shaʻban</month>
							<month type="9">Ramadan</month>
							<month type="10">Shawwal</month>
							<month type="11">Dhuʻl-Qiʻdah</month>
							<month type="12">Dhuʻl-Hijjah</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">AH</era>
						<!-- Anno Hijri, = 7/16/622 AD
							 = -42521587200000.0 millis since 1/1/1970
						-->
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="islamic-civil">
				<months>
					<alias source="locale" path="../../calendar[@type='islamic']/months"/>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='islamic']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='islamic']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='islamic']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<alias source="locale" path="../../calendar[@type='islamic']/eras"/>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="islamic-rgsa">
				<months>
					<alias source="locale" path="../../calendar[@type='islamic']/months"/>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='islamic']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='islamic']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='islamic']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<alias source="locale" path="../../calendar[@type='islamic']/eras"/>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="islamic-tbla">
				<months>
					<alias source="locale" path="../../calendar[@type='islamic']/months"/>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='islamic']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='islamic']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='islamic']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<alias source="locale" path="../../calendar[@type='islamic']/eras"/>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="islamic-umalqura">
				<months>
					<alias source="locale" path="../../calendar[@type='islamic']/months"/>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='islamic']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='islamic']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='islamic']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<alias source="locale" path="../../calendar[@type='islamic']/eras"/>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='islamic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="japanese">
				<months>
					<alias source="locale" path="../../calendar[@type='gregorian']/months"/>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<!-- These eras are numbered according to the data used
							 by the ICU Japanese Calendar.
							 search for 'ERAS ='
						-->
						<era type="0">Taika (645-650)</era>
						<era type="1">Hakuchi (650-671)</era>
						<era type="2">Hakuhō (672-686)</era>
						<era type="3">Shuchō (686-701)</era>
						<era type="4">Taihō (701-704)</era>
						<era type="5">Keiun (704-708)</era>
						<era type="6">Wadō (708-715)</era>
						<era type="7">Reiki (715-717)</era>
						<era type="8">Yōrō (717-724)</era>
						<era type="9">Jinki (724-729)</era>
						<era type="10">Tempyō (729-749)</era>
						<era type="11">Tempyō-kampō (749-749)</era>
						<era type="12">Tempyō-shōhō (749-757)</era>
						<era type="13">Tempyō-hōji (757-765)</era>
						<era type="14">Temphō-jingo (765-767)</era>
						<era type="15">Jingo-keiun (767-770)</era>
						<era type="16">Hōki (770-780)</era>
						<era type="17">Ten-ō (781-782)</era>
						<era type="18">Enryaku (782-806)</era>
						<era type="19">Daidō (806-810)</era>
						<era type="20">Kōnin (810-824)</era>
						<era type="21">Tenchō (824-834)</era>
						<era type="22">Jōwa (834-848)</era>
						<era type="23">Kajō (848-851)</era>
						<era type="24">Ninju (851-854)</era>
						<era type="25">Saiko (854-857)</era>
						<era type="26">Tennan (857-859)</era>
						<era type="27">Jōgan (859-877)</era>
						<era type="28">Genkei (877-885)</era>
						<era type="29">Ninna (885-889)</era>
						<era type="30">Kampyō (889-898)</era>
						<era type="31">Shōtai (898-901)</era>
						<era type="32">Engi (901-923)</era>
						<era type="33">Enchō (923-931)</era>
						<era type="34">Shōhei (931-938)</era>
						<era type="35">Tengyō (938-947)</era>
						<era type="36">Tenryaku (947-957)</era>
						<era type="37">Tentoku (957-961)</era>
						<era type="38">Ōwa (961-964)</era>
						<era type="39">Kōhō (964-968)</era>
						<era type="40">Anna (968-970)</era>
						<era type="41">Tenroku (970-973)</era>
						<era type="42">Ten-en (973-976)</era>
						<era type="43">Jōgen (976-978)</era>
						<era type="44">Tengen (978-983)</era>
						<era type="45">Eikan (983-985)</era>
						<era type="46">Kanna (985-987)</era>
						<era type="47">Ei-en (987-989)</era>
						<era type="48">Eiso (989-990)</era>
						<era type="49">Shōryaku (990-995)</era>
						<era type="50">Chōtoku (995-999)</era>
						<era type="51">Chōhō (999-1004)</era>
						<era type="52">Kankō (1004-1012)</era>
						<era type="53">Chōwa (1012-1017)</era>
						<era type="54">Kannin (1017-1021)</era>
						<era type="55">Jian (1021-1024)</era>
						<era type="56">Manju (1024-1028)</era>
						<era type="57">Chōgen (1028-1037)</era>
						<era type="58">Chōryaku (1037-1040)</era>
						<era type="59">Chōkyū (1040-1044)</era>
						<era type="60">Kantoku (1044-1046)</era>
						<era type="61">Eishō (1046-1053)</era>
						<era type="62">Tengi (1053-1058)</era>
						<era type="63">Kōhei (1058-1065)</era>
						<era type="64">Jiryaku (1065-1069)</era>
						<era type="65">Enkyū (1069-1074)</era>
						<era type="66">Shōho (1074-1077)</era>
						<era type="67">Shōryaku (1077-1081)</era>
						<era type="68">Eiho (1081-1084)</era>
						<era type="69">Ōtoku (1084-1087)</era>
						<era type="70">Kanji (1087-1094)</era>
						<era type="71">Kaho (1094-1096)</era>
						<era type="72">Eichō (1096-1097)</era>
						<era type="73">Shōtoku (1097-1099)</era>
						<era type="74">Kōwa (1099-1104)</era>
						<era type="75">Chōji (1104-1106)</era>
						<era type="76">Kashō (1106-1108)</era>
						<era type="77">Tennin (1108-1110)</era>
						<era type="78">Ten-ei (1110-1113)</era>
						<era type="79">Eikyū (1113-1118)</era>
						<era type="80">Gen-ei (1118-1120)</era>
						<era type="81">Hoan (1120-1124)</era>
						<era type="82">Tenji (1124-1126)</era>
						<era type="83">Daiji (1126-1131)</era>
						<era type="84">Tenshō (1131-1132)</era>
						<era type="85">Chōshō (1132-1135)</era>
						<era type="86">Hoen (1135-1141)</era>
						<era type="87">Eiji (1141-1142)</era>
						<era type="88">Kōji (1142-1144)</era>
						<era type="89">Tenyō (1144-1145)</era>
						<era type="90">Kyūan (1145-1151)</era>
						<era type="91">Ninpei (1151-1154)</era>
						<era type="92">Kyūju (1154-1156)</era>
						<era type="93">Hogen (1156-1159)</era>
						<era type="94">Heiji (1159-1160)</era>
						<era type="95">Eiryaku (1160-1161)</era>
						<era type="96">Ōho (1161-1163)</era>
						<era type="97">Chōkan (1163-1165)</era>
						<era type="98">Eiman (1165-1166)</era>
						<era type="99">Nin-an (1166-1169)</era>
						<era type="100">Kaō (1169-1171)</era>
						<era type="101">Shōan (1171-1175)</era>
						<era type="102">Angen (1175-1177)</era>
						<era type="103">Jishō (1177-1181)</era>
						<era type="104">Yōwa (1181-1182)</era>
						<era type="105">Juei (1182-1184)</era>
						<era type="106">Genryuku (1184-1185)</era>
						<era type="107">Bunji (1185-1190)</era>
						<era type="108">Kenkyū (1190-1199)</era>
						<era type="109">Shōji (1199-1201)</era>
						<era type="110">Kennin (1201-1204)</era>
						<era type="111">Genkyū (1204-1206)</era>
						<era type="112">Ken-ei (1206-1207)</era>
						<era type="113">Shōgen (1207-1211)</era>
						<era type="114">Kenryaku (1211-1213)</era>
						<era type="115">Kenpō (1213-1219)</era>
						<era type="116">Shōkyū (1219-1222)</era>
						<era type="117">Jōō (1222-1224)</era>
						<era type="118">Gennin (1224-1225)</era>
						<era type="119">Karoku (1225-1227)</era>
						<era type="120">Antei (1227-1229)</era>
						<era type="121">Kanki (1229-1232)</era>
						<era type="122">Jōei (1232-1233)</era>
						<era type="123">Tempuku (1233-1234)</era>
						<era type="124">Bunryaku (1234-1235)</era>
						<era type="125">Katei (1235-1238)</era>
						<era type="126">Ryakunin (1238-1239)</era>
						<era type="127">En-ō (1239-1240)</era>
						<era type="128">Ninji (1240-1243)</era>
						<era type="129">Kangen (1243-1247)</era>
						<era type="130">Hōji (1247-1249)</era>
						<era type="131">Kenchō (1249-1256)</era>
						<era type="132">Kōgen (1256-1257)</era>
						<era type="133">Shōka (1257-1259)</era>
						<era type="134">Shōgen (1259-1260)</era>
						<era type="135">Bun-ō (1260-1261)</era>
						<era type="136">Kōchō (1261-1264)</era>
						<era type="137">Bun-ei (1264-1275)</era>
						<era type="138">Kenji (1275-1278)</era>
						<era type="139">Kōan (1278-1288)</era>
						<era type="140">Shōō (1288-1293)</era>
						<era type="141">Einin (1293-1299)</era>
						<era type="142">Shōan (1299-1302)</era>
						<era type="143">Kengen (1302-1303)</era>
						<era type="144">Kagen (1303-1306)</era>
						<era type="145">Tokuji (1306-1308)</era>
						<era type="146">Enkei (1308-1311)</era>
						<era type="147">Ōchō (1311-1312)</era>
						<era type="148">Shōwa (1312-1317)</era>
						<era type="149">Bunpō (1317-1319)</era>
						<era type="150">Genō (1319-1321)</era>
						<era type="151">Genkyō (1321-1324)</era>
						<era type="152">Shōchū (1324-1326)</era>
						<era type="153">Kareki (1326-1329)</era>
						<era type="154">Gentoku (1329-1331)</era>
						<era type="155">Genkō (1331-1334)</era>
						<era type="156">Kemmu (1334-1336)</era>
						<era type="157">Engen (1336-1340)</era>
						<era type="158">Kōkoku (1340-1346)</era>
						<era type="159">Shōhei (1346-1370)</era>
						<era type="160">Kentoku (1370-1372)</era>
						<era type="161">Bunchū (1372-1375)</era>
						<era type="162">Tenju (1375-1379)</era>
						<era type="163">Kōryaku (1379-1381)</era>
						<era type="164">Kōwa (1381-1384)</era>
						<era type="165">Genchū (1384-1392)</era>
						<era type="166">Meitoku (1384-1387)</era>
						<era type="167">Kakei (1387-1389)</era>
						<era type="168">Kōō (1389-1390)</era>
						<era type="169">Meitoku (1390-1394)</era>
						<era type="170">Ōei (1394-1428)</era>
						<era type="171">Shōchō (1428-1429)</era>
						<era type="172">Eikyō (1429-1441)</era>
						<era type="173">Kakitsu (1441-1444)</era>
						<era type="174">Bun-an (1444-1449)</era>
						<era type="175">Hōtoku (1449-1452)</era>
						<era type="176">Kyōtoku (1452-1455)</era>
						<era type="177">Kōshō (1455-1457)</era>
						<era type="178">Chōroku (1457-1460)</era>
						<era type="179">Kanshō (1460-1466)</era>
						<era type="180">Bunshō (1466-1467)</era>
						<era type="181">Ōnin (1467-1469)</era>
						<era type="182">Bunmei (1469-1487)</era>
						<era type="183">Chōkyō (1487-1489)</era>
						<era type="184">Entoku (1489-1492)</era>
						<era type="185">Meiō (1492-1501)</era>
						<era type="186">Bunki (1501-1504)</era>
						<era type="187">Eishō (1504-1521)</era>
						<era type="188">Taiei (1521-1528)</era>
						<era type="189">Kyōroku (1528-1532)</era>
						<era type="190">Tenmon (1532-1555)</era>
						<era type="191">Kōji (1555-1558)</era>
						<era type="192">Eiroku (1558-1570)</era>
						<era type="193">Genki (1570-1573)</era>
						<era type="194">Tenshō (1573-1592)</era>
						<era type="195">Bunroku (1592-1596)</era>
						<era type="196">Keichō (1596-1615)</era>
						<era type="197">Genwa (1615-1624)</era>
						<era type="198">Kan-ei (1624-1644)</era>
						<era type="199">Shōho (1644-1648)</era>
						<era type="200">Keian (1648-1652)</era>
						<era type="201">Shōō (1652-1655)</era>
						<era type="202">Meiryaku (1655-1658)</era>
						<era type="203">Manji (1658-1661)</era>
						<era type="204">Kanbun (1661-1673)</era>
						<era type="205">Enpō (1673-1681)</era>
						<era type="206">Tenwa (1681-1684)</era>
						<era type="207">Jōkyō (1684-1688)</era>
						<era type="208">Genroku (1688-1704)</era>
						<era type="209">Hōei (1704-1711)</era>
						<era type="210">Shōtoku (1711-1716)</era>
						<era type="211">Kyōhō (1716-1736)</era>
						<era type="212">Genbun (1736-1741)</era>
						<era type="213">Kanpō (1741-1744)</era>
						<era type="214">Enkyō (1744-1748)</era>
						<era type="215">Kan-en (1748-1751)</era>
						<era type="216">Hōryaku (1751-1764)</era>
						<era type="217">Meiwa (1764-1772)</era>
						<era type="218">An-ei (1772-1781)</era>
						<era type="219">Tenmei (1781-1789)</era>
						<era type="220">Kansei (1789-1801)</era>
						<era type="221">Kyōwa (1801-1804)</era>
						<era type="222">Bunka (1804-1818)</era>
						<era type="223">Bunsei (1818-1830)</era>
						<era type="224">Tenpō (1830-1844)</era>
						<era type="225">Kōka (1844-1848)</era>
						<era type="226">Kaei (1848-1854)</era>
						<era type="227">Ansei (1854-1860)</era>
						<era type="228">Man-en (1860-1861)</era>
						<era type="229">Bunkyū (1861-1864)</era>
						<era type="230">Genji (1864-1865)</era>
						<era type="231">Keiō (1865-1868)</era>
						<era type="232">Meiji</era>
						<era type="233">Taishō</era>
						<era type="234">Shōwa</era>
						<era type="235">Heisei</era>
					</eraAbbr>
					<eraNarrow>
						<era type="0">Taika (645-650)</era>
						<era type="1">Hakuchi (650-671)</era>
						<era type="2">Hakuhō (672-686)</era>
						<era type="3">Shuchō (686-701)</era>
						<era type="4">Taihō (701-704)</era>
						<era type="5">Keiun (704-708)</era>
						<era type="6">Wadō (708-715)</era>
						<era type="7">Reiki (715-717)</era>
						<era type="8">Yōrō (717-724)</era>
						<era type="9">Jinki (724-729)</era>
						<era type="10">Tempyō (729-749)</era>
						<era type="11">Tempyō-kampō (749-749)</era>
						<era type="12">Tempyō-shōhō (749-757)</era>
						<era type="13">Tempyō-hōji (757-765)</era>
						<era type="14">Temphō-jingo (765-767)</era>
						<era type="15">Jingo-keiun (767-770)</era>
						<era type="16">Hōki (770-780)</era>
						<era type="17">Ten-ō (781-782)</era>
						<era type="18">Enryaku (782-806)</era>
						<era type="19">Daidō (806-810)</era>
						<era type="20">Kōnin (810-824)</era>
						<era type="21">Tenchō (824-834)</era>
						<era type="22">Jōwa (834-848)</era>
						<era type="23">Kajō (848-851)</era>
						<era type="24">Ninju (851-854)</era>
						<era type="25">Saiko (854-857)</era>
						<era type="26">Tennan (857-859)</era>
						<era type="27">Jōgan (859-877)</era>
						<era type="28">Genkei (877-885)</era>
						<era type="29">Ninna (885-889)</era>
						<era type="30">Kampyō (889-898)</era>
						<era type="31">Shōtai (898-901)</era>
						<era type="32">Engi (901-923)</era>
						<era type="33">Enchō (923-931)</era>
						<era type="34">Shōhei (931-938)</era>
						<era type="35">Tengyō (938-947)</era>
						<era type="36">Tenryaku (947-957)</era>
						<era type="37">Tentoku (957-961)</era>
						<era type="38">Ōwa (961-964)</era>
						<era type="39">Kōhō (964-968)</era>
						<era type="40">Anna (968-970)</era>
						<era type="41">Tenroku (970-973)</era>
						<era type="42">Ten-en (973-976)</era>
						<era type="43">Jōgen (976-978)</era>
						<era type="44">Tengen (978-983)</era>
						<era type="45">Eikan (983-985)</era>
						<era type="46">Kanna (985-987)</era>
						<era type="47">Ei-en (987-989)</era>
						<era type="48">Eiso (989-990)</era>
						<era type="49">Shōryaku (990-995)</era>
						<era type="50">Chōtoku (995-999)</era>
						<era type="51">Chōhō (999-1004)</era>
						<era type="52">Kankō (1004-1012)</era>
						<era type="53">Chōwa (1012-1017)</era>
						<era type="54">Kannin (1017-1021)</era>
						<era type="55">Jian (1021-1024)</era>
						<era type="56">Manju (1024-1028)</era>
						<era type="57">Chōgen (1028-1037)</era>
						<era type="58">Chōryaku (1037-1040)</era>
						<era type="59">Chōkyū (1040-1044)</era>
						<era type="60">Kantoku (1044-1046)</era>
						<era type="61">Eishō (1046-1053)</era>
						<era type="62">Tengi (1053-1058)</era>
						<era type="63">Kōhei (1058-1065)</era>
						<era type="64">Jiryaku (1065-1069)</era>
						<era type="65">Enkyū (1069-1074)</era>
						<era type="66">Shōho (1074-1077)</era>
						<era type="67">Shōryaku (1077-1081)</era>
						<era type="68">Eiho (1081-1084)</era>
						<era type="69">Ōtoku (1084-1087)</era>
						<era type="70">Kanji (1087-1094)</era>
						<era type="71">Kaho (1094-1096)</era>
						<era type="72">Eichō (1096-1097)</era>
						<era type="73">Shōtoku (1097-1099)</era>
						<era type="74">Kōwa (1099-1104)</era>
						<era type="75">Chōji (1104-1106)</era>
						<era type="76">Kashō (1106-1108)</era>
						<era type="77">Tennin (1108-1110)</era>
						<era type="78">Ten-ei (1110-1113)</era>
						<era type="79">Eikyū (1113-1118)</era>
						<era type="80">Gen-ei (1118-1120)</era>
						<era type="81">Hoan (1120-1124)</era>
						<era type="82">Tenji (1124-1126)</era>
						<era type="83">Daiji (1126-1131)</era>
						<era type="84">Tenshō (1131-1132)</era>
						<era type="85">Chōshō (1132-1135)</era>
						<era type="86">Hoen (1135-1141)</era>
						<era type="87">Eiji (1141-1142)</era>
						<era type="88">Kōji (1142-1144)</era>
						<era type="89">Tenyō (1144-1145)</era>
						<era type="90">Kyūan (1145-1151)</era>
						<era type="91">Ninpei (1151-1154)</era>
						<era type="92">Kyūju (1154-1156)</era>
						<era type="93">Hogen (1156-1159)</era>
						<era type="94">Heiji (1159-1160)</era>
						<era type="95">Eiryaku (1160-1161)</era>
						<era type="96">Ōho (1161-1163)</era>
						<era type="97">Chōkan (1163-1165)</era>
						<era type="98">Eiman (1165-1166)</era>
						<era type="99">Nin-an (1166-1169)</era>
						<era type="100">Kaō (1169-1171)</era>
						<era type="101">Shōan (1171-1175)</era>
						<era type="102">Angen (1175-1177)</era>
						<era type="103">Jishō (1177-1181)</era>
						<era type="104">Yōwa (1181-1182)</era>
						<era type="105">Juei (1182-1184)</era>
						<era type="106">Genryuku (1184-1185)</era>
						<era type="107">Bunji (1185-1190)</era>
						<era type="108">Kenkyū (1190-1199)</era>
						<era type="109">Shōji (1199-1201)</era>
						<era type="110">Kennin (1201-1204)</era>
						<era type="111">Genkyū (1204-1206)</era>
						<era type="112">Ken-ei (1206-1207)</era>
						<era type="113">Shōgen (1207-1211)</era>
						<era type="114">Kenryaku (1211-1213)</era>
						<era type="115">Kenpō (1213-1219)</era>
						<era type="116">Shōkyū (1219-1222)</era>
						<era type="117">Jōō (1222-1224)</era>
						<era type="118">Gennin (1224-1225)</era>
						<era type="119">Karoku (1225-1227)</era>
						<era type="120">Antei (1227-1229)</era>
						<era type="121">Kanki (1229-1232)</era>
						<era type="122">Jōei (1232-1233)</era>
						<era type="123">Tempuku (1233-1234)</era>
						<era type="124">Bunryaku (1234-1235)</era>
						<era type="125">Katei (1235-1238)</era>
						<era type="126">Ryakunin (1238-1239)</era>
						<era type="127">En-ō (1239-1240)</era>
						<era type="128">Ninji (1240-1243)</era>
						<era type="129">Kangen (1243-1247)</era>
						<era type="130">Hōji (1247-1249)</era>
						<era type="131">Kenchō (1249-1256)</era>
						<era type="132">Kōgen (1256-1257)</era>
						<era type="133">Shōka (1257-1259)</era>
						<era type="134">Shōgen (1259-1260)</era>
						<era type="135">Bun-ō (1260-1261)</era>
						<era type="136">Kōchō (1261-1264)</era>
						<era type="137">Bun-ei (1264-1275)</era>
						<era type="138">Kenji (1275-1278)</era>
						<era type="139">Kōan (1278-1288)</era>
						<era type="140">Shōō (1288-1293)</era>
						<era type="141">Einin (1293-1299)</era>
						<era type="142">Shōan (1299-1302)</era>
						<era type="143">Kengen (1302-1303)</era>
						<era type="144">Kagen (1303-1306)</era>
						<era type="145">Tokuji (1306-1308)</era>
						<era type="146">Enkei (1308-1311)</era>
						<era type="147">Ōchō (1311-1312)</era>
						<era type="148">Shōwa (1312-1317)</era>
						<era type="149">Bunpō (1317-1319)</era>
						<era type="150">Genō (1319-1321)</era>
						<era type="151">Genkyō (1321-1324)</era>
						<era type="152">Shōchū (1324-1326)</era>
						<era type="153">Kareki (1326-1329)</era>
						<era type="154">Gentoku (1329-1331)</era>
						<era type="155">Genkō (1331-1334)</era>
						<era type="156">Kemmu (1334-1336)</era>
						<era type="157">Engen (1336-1340)</era>
						<era type="158">Kōkoku (1340-1346)</era>
						<era type="159">Shōhei (1346-1370)</era>
						<era type="160">Kentoku (1370-1372)</era>
						<era type="161">Bunchũ (1372-1375)</era>
						<era type="162">Tenju (1375-1379)</era>
						<era type="163">Kōryaku (1379-1381)</era>
						<era type="164">Kōwa (1381-1384)</era>
						<era type="165">Genchũ (1384-1392)</era>
						<era type="166">Meitoku (1384-1387)</era>
						<era type="167">Kakei (1387-1389)</era>
						<era type="168">Kōō (1389-1390)</era>
						<era type="169">Meitoku (1390-1394)</era>
						<era type="170">Ōei (1394-1428)</era>
						<era type="171">Shōchō (1428-1429)</era>
						<era type="172">Eikyō (1429-1441)</era>
						<era type="173">Kakitsu (1441-1444)</era>
						<era type="174">Bun-an (1444-1449)</era>
						<era type="175">Hōtoku (1449-1452)</era>
						<era type="176">Kyōtoku (1452-1455)</era>
						<era type="177">Kōshō (1455-1457)</era>
						<era type="178">Chōroku (1457-1460)</era>
						<era type="179">Kanshō (1460-1466)</era>
						<era type="180">Bunshō (1466-1467)</era>
						<era type="181">Ōnin (1467-1469)</era>
						<era type="182">Bunmei (1469-1487)</era>
						<era type="183">Chōkyō (1487-1489)</era>
						<era type="184">Entoku (1489-1492)</era>
						<era type="185">Meiō (1492-1501)</era>
						<era type="186">Bunki (1501-1504)</era>
						<era type="187">Eishō (1504-1521)</era>
						<era type="188">Taiei (1521-1528)</era>
						<era type="189">Kyōroku (1528-1532)</era>
						<era type="190">Tenmon (1532-1555)</era>
						<era type="191">Kōji (1555-1558)</era>
						<era type="192">Eiroku (1558-1570)</era>
						<era type="193">Genki (1570-1573)</era>
						<era type="194">Tenshō (1573-1592)</era>
						<era type="195">Bunroku (1592-1596)</era>
						<era type="196">Keichō (1596-1615)</era>
						<era type="197">Genwa (1615-1624)</era>
						<era type="198">Kan-ei (1624-1644)</era>
						<era type="199">Shōho (1644-1648)</era>
						<era type="200">Keian (1648-1652)</era>
						<era type="201">Shōō (1652-1655)</era>
						<era type="202">Meiryaku (1655-1658)</era>
						<era type="203">Manji (1658-1661)</era>
						<era type="204">Kanbun (1661-1673)</era>
						<era type="205">Enpō (1673-1681)</era>
						<era type="206">Tenwa (1681-1684)</era>
						<era type="207">Jōkyō (1684-1688)</era>
						<era type="208">Genroku (1688-1704)</era>
						<era type="209">Hōei (1704-1711)</era>
						<era type="210">Shōtoku (1711-1716)</era>
						<era type="211">Kyōhō (1716-1736)</era>
						<era type="212">Genbun (1736-1741)</era>
						<era type="213">Kanpō (1741-1744)</era>
						<era type="214">Enkyō (1744-1748)</era>
						<era type="215">Kan-en (1748-1751)</era>
						<era type="216">Hōryaku (1751-1764)</era>
						<era type="217">Meiwa (1764-1772)</era>
						<era type="218">An-ei (1772-1781)</era>
						<era type="219">Tenmei (1781-1789)</era>
						<era type="220">Kansei (1789-1801)</era>
						<era type="221">Kyōwa (1801-1804)</era>
						<era type="222">Bunka (1804-1818)</era>
						<era type="223">Bunsei (1818-1830)</era>
						<era type="224">Tenpō (1830-1844)</era>
						<era type="225">Kōka (1844-1848)</era>
						<era type="226">Kaei (1848-1854)</era>
						<era type="227">Ansei (1854-1860)</era>
						<era type="228">Man-en (1860-1861)</era>
						<era type="229">Bunkyū (1861-1864)</era>
						<era type="230">Genji (1864-1865)</era>
						<era type="231">Keiō (1865-1868)</era>
						<era type="232">M</era>
						<era type="233">T</era>
						<era type="234">S</era>
						<era type="235">H</era>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="persian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../monthWidth[@type='wide']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<alias source="locale" path="../../monthContext[@type='stand-alone']/monthWidth[@type='narrow']"/>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Farvardin</month>
							<month type="2">Ordibehesht</month>
							<month type="3">Khordad</month>
							<month type="4">Tir</month>
							<month type="5">Mordad</month>
							<month type="6">Shahrivar</month>
							<month type="7">Mehr</month>
							<month type="8">Aban</month>
							<month type="9">Azar</month>
							<month type="10">Dey</month>
							<month type="11">Bahman</month>
							<month type="12">Esfand</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="abbreviated">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='abbreviated']"/>
						</monthWidth>
						<monthWidth type="narrow">
							<month type="1">1</month>
							<month type="2">2</month>
							<month type="3">3</month>
							<month type="4">4</month>
							<month type="5">5</month>
							<month type="6">6</month>
							<month type="7">7</month>
							<month type="8">8</month>
							<month type="9">9</month>
							<month type="10">10</month>
							<month type="11">11</month>
							<month type="12">12</month>
						</monthWidth>
						<monthWidth type="wide">
							<alias source="locale" path="../../monthContext[@type='format']/monthWidth[@type='wide']"/>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<eraAbbr>
						<era type="0">AP</era>
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
			<calendar type="roc">
				<months>
					<alias source="locale" path="../../calendar[@type='gregorian']/months"/>
				</months>
				<days>
					<alias source="locale" path="../../calendar[@type='gregorian']/days"/>
				</days>
				<quarters>
					<alias source="locale" path="../../calendar[@type='gregorian']/quarters"/>
				</quarters>
				<dayPeriods>
					<alias source="locale" path="../../calendar[@type='gregorian']/dayPeriods"/>
				</dayPeriods>
				<eras>
					<eraNames>
						<alias source="locale" path="../eraAbbr"/>
					</eraNames>
					<!-- = 1912 AD gregorian. See: http://en.wikipedia.org/wiki/Minguo_calendar -->
					<eraAbbr>
						<era type="0">Before R.O.C.</era>
						<era type="1">R.O.C.</era>
					</eraAbbr>
					<eraNarrow>
						<alias source="locale" path="../eraAbbr"/>
					</eraNarrow>
				</eras>
				<dateFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateFormats"/>
				</dateFormats>
				<timeFormats>
					<alias source="locale" path="../../calendar[@type='gregorian']/timeFormats"/>
				</timeFormats>
				<dateTimeFormats>
					<alias source="locale" path="../../calendar[@type='generic']/dateTimeFormats"/>
				</dateTimeFormats>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>Era</displayName>
			</field>
			<field type="year">
				<displayName>Year</displayName>
				<relative type="-1">last year</relative>
				<relative type="0">this year</relative>
				<relative type="1">next year</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">+{0} y</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">-{0} y</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month">
				<displayName>Month</displayName>
				<relative type="-1">last month</relative>
				<relative type="0">this month</relative>
				<relative type="1">next month</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">+{0} m</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">-{0} m</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName>Week</displayName>
				<relative type="-1">last week</relative>
				<relative type="0">this week</relative>
				<relative type="1">next week</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">+{0} w</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">-{0} w</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day">
				<displayName>Day</displayName>
				<relative type="-1">yesterday</relative>
				<relative type="0">today</relative>
				<relative type="1">tomorrow</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">+{0} d</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">-{0} d</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="weekday">
				<displayName>Day of the Week</displayName>
			</field>
			<field type="sun">
				<relative type="-1">last Sunday</relative>
				<relative type="0">this Sunday</relative>
				<relative type="1">next Sunday</relative>
			</field>
			<field type="mon">
				<relative type="-1">last Monday</relative>
				<relative type="0">this Monday</relative>
				<relative type="1">next Monday</relative>
			</field>
			<field type="tue">
				<relative type="-1">last Tuesday</relative>
				<relative type="0">this Tuesday</relative>
				<relative type="1">next Tuesday</relative>
			</field>
			<field type="wed">
				<relative type="-1">last Wednesday</relative>
				<relative type="0">this Wednesday</relative>
				<relative type="1">next Wednesday</relative>
			</field>
			<field type="thu">
				<relative type="-1">last Thursday</relative>
				<relative type="0">this Thursday</relative>
				<relative type="1">next Thursday</relative>
			</field>
			<field type="fri">
				<relative type="-1">last Friday</relative>
				<relative type="0">this Friday</relative>
				<relative type="1">next Friday</relative>
			</field>
			<field type="sat">
				<relative type="-1">last Saturday</relative>
				<relative type="0">this Saturday</relative>
				<relative type="1">next Saturday</relative>
			</field>
			<field type="dayperiod">
				<displayName>Dayperiod</displayName>
			</field>
			<field type="hour">
				<displayName>Hour</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="other">+{0} h</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">-{0} h</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName>Minute</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="other">+{0} min</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">-{0} min</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName>Second</displayName>
				<relative type="0">now</relative>
				<relativeTime type="future">
					<relativeTimePattern count="other">+{0} s</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="other">-{0} s</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName>Zone</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat>+HH:mm;-HH:mm</hourFormat>
			<gmtFormat>GMT{0}</gmtFormat>
			<gmtZeroFormat>GMT</gmtZeroFormat>
			<regionFormat>{0}</regionFormat>
			<regionFormat type="daylight">{0} (+1)</regionFormat>
			<regionFormat type="standard">{0} (+0)</regionFormat>
			<fallbackFormat>{1} ({0})</fallbackFormat>
			<zone type="Etc/Unknown">
				<exemplarCity>Unknown</exemplarCity>
			</zone>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity>Dumont d’Urville</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity>St. Barthelemy</exemplarCity>
			</zone>
			<zone type="America/Coral_Harbour">
				<exemplarCity>Atikokan</exemplarCity>
			</zone>
			<zone type="America/St_Johns">
				<exemplarCity>St. John’s</exemplarCity>
			</zone>
			<zone type="Africa/Asmera">
				<exemplarCity>Asmara</exemplarCity>
			</zone>
			<zone type="Pacific/Truk">
				<exemplarCity>Chuuk</exemplarCity>
			</zone>
			<zone type="Pacific/Ponape">
				<exemplarCity>Pohnpei</exemplarCity>
			</zone>
			<zone type="Atlantic/Faeroe">
				<exemplarCity>Faroe</exemplarCity>
			</zone>
			<zone type="America/Godthab">
				<exemplarCity>Nuuk</exemplarCity>
			</zone>
			<zone type="America/Scoresbysund">
				<exemplarCity>Ittoqqortoormiit</exemplarCity>
			</zone>
			<zone type="Asia/Calcutta">
				<exemplarCity>Kolkata</exemplarCity>
			</zone>
			<zone type="America/St_Kitts">
				<exemplarCity>St. Kitts</exemplarCity>
			</zone>
			<zone type="America/St_Lucia">
				<exemplarCity>St. Lucia</exemplarCity>
			</zone>
			<zone type="Asia/Katmandu">
				<exemplarCity>Kathmandu</exemplarCity>
			</zone>
			<zone type="Atlantic/St_Helena">
				<exemplarCity>St. Helena</exemplarCity>
			</zone>
			<zone type="America/Lower_Princes">
				<exemplarCity>Lower Prince's Quarter</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Beulah">
				<exemplarCity>Beulah, North Dakota</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/New_Salem">
				<exemplarCity>New Salem, North Dakota</exemplarCity>
			</zone>
			<zone type="America/North_Dakota/Center">
				<exemplarCity>Center, North Dakota</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vincennes">
				<exemplarCity>Vincennes, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Petersburg">
				<exemplarCity>Petersburg, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Tell_City">
				<exemplarCity>Tell City, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Knox">
				<exemplarCity>Knox, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Winamac">
				<exemplarCity>Winamac, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Marengo">
				<exemplarCity>Marengo, Indiana</exemplarCity>
			</zone>
			<zone type="America/Indiana/Vevay">
				<exemplarCity>Vevay, Indiana</exemplarCity>
			</zone>
			<zone type="America/Kentucky/Monticello">
				<exemplarCity>Monticello, Kentucky</exemplarCity>
			</zone>
			<zone type="America/St_Vincent">
				<exemplarCity>St. Vincent</exemplarCity>
			</zone>
			<zone type="America/St_Thomas">
				<exemplarCity>St. Thomas</exemplarCity>
			</zone>
			<zone type="Asia/Saigon">
				<exemplarCity>Ho Chi Minh</exemplarCity>
			</zone>
		</timeZoneNames>
	</dates>
	<numbers>
		<defaultNumberingSystem>latn</defaultNumberingSystem>
		<otherNumberingSystems>
			<native>latn</native>
		</otherNumberingSystems>
		<symbols>
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="arab">
			<decimal>٫</decimal>
			<group>٬</group>
			<list>؛</list>
			<percentSign>٪</percentSign>
			<plusSign>‏+</plusSign> <!-- includes RLM before sign -->
			<minusSign>‏-</minusSign> <!-- includes RLM before sign (002D) -->
			<exponential>اس</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>؉</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<symbols numberSystem="arabext">
			<decimal>٫</decimal>
			<group>٬</group>
			<list>؛</list>
			<percentSign>٪</percentSign>
			<plusSign>‎+‎</plusSign> <!-- includes LRM before and after sign -->
			<minusSign>‎-‎</minusSign> <!-- includes LRM before and after sign (002D) -->
			<exponential>×۱۰^</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>؉</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<symbols numberSystem="bali">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="beng">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="brah">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="cakm">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="cham">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="deva">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="fullwide">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="gujr">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="guru">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="hanidec">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="java">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="kali">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="khmr">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="knda">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="lana">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="lanatham">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="laoo">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="latn">
			<decimal>.</decimal>
			<group>,</group>
			<list>;</list>
			<percentSign>%</percentSign>
			<plusSign>+</plusSign>
			<minusSign>-</minusSign>
			<exponential>E</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>‰</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<symbols numberSystem="lepc">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="limb">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="mlym">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="mong">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="mtei">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="mymr">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="mymrshan">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="nkoo">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="olck">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="orya">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="osma">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="saur">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="shrd">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="sora">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="sund">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="takr">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="talu">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="tamldec">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="telu">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="thai">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="tibt">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<symbols numberSystem="vaii">
			<alias source="locale" path="../symbols[@numberSystem='latn']"/>
		</symbols>
		<decimalFormats>
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="arab">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="arabext">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="bali">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="beng">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="brah">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="cakm">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="cham">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="deva">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="fullwide">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="gujr">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="guru">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="hanidec">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="java">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="kali">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="khmr">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="knda">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="lana">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="lanatham">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="laoo">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern>#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="long">
				<alias source="locale" path="../decimalFormatLength[@type='short']"/>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="other">0K</pattern>
					<pattern type="10000" count="other">00K</pattern>
					<pattern type="100000" count="other">000K</pattern>
					<pattern type="1000000" count="other">0M</pattern>
					<pattern type="10000000" count="other">00M</pattern>
					<pattern type="100000000" count="other">000M</pattern>
					<pattern type="1000000000" count="other">0G</pattern>
					<pattern type="10000000000" count="other">00G</pattern>
					<pattern type="100000000000" count="other">000G</pattern>
					<pattern type="1000000000000" count="other">0T</pattern>
					<pattern type="10000000000000" count="other">00T</pattern>
					<pattern type="100000000000000" count="other">000T</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<decimalFormats numberSystem="lepc">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="limb">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="mlym">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="mong">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="mtei">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="mymr">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="mymrshan">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="nkoo">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="olck">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="orya">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="osma">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="saur">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="shrd">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="sora">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="sund">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="takr">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="talu">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="tamldec">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="telu">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="thai">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="tibt">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<decimalFormats numberSystem="vaii">
			<alias source="locale" path="../decimalFormats[@numberSystem='latn']"/>
		</decimalFormats>
		<scientificFormats>
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="arab">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="arabext">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="bali">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="beng">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="brah">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="cakm">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="cham">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="deva">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="fullwide">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="gujr">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="guru">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="hanidec">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="java">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="kali">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="khmr">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="knda">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="lana">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="lanatham">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="laoo">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern>#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<scientificFormats numberSystem="lepc">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="limb">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="mlym">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="mong">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="mtei">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="mymr">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="mymrshan">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="nkoo">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="olck">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="orya">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="osma">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="saur">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="shrd">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="sora">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="sund">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="takr">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="talu">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="tamldec">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="telu">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="thai">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="tibt">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<scientificFormats numberSystem="vaii">
			<alias source="locale" path="../scientificFormats[@numberSystem='latn']"/>
		</scientificFormats>
		<percentFormats>
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="arab">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="arabext">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="bali">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="beng">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="brah">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="cakm">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="cham">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="deva">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="fullwide">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="gujr">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="guru">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="hanidec">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="java">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="kali">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="khmr">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="knda">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="lana">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="lanatham">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="laoo">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern>#,##0%</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<percentFormats numberSystem="lepc">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="limb">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="mlym">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="mong">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="mtei">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="mymr">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="mymrshan">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="nkoo">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="olck">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="orya">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="osma">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="saur">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="shrd">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="sora">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="sund">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="takr">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="talu">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="tamldec">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="telu">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="thai">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="tibt">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<percentFormats numberSystem="vaii">
			<alias source="locale" path="../percentFormats[@numberSystem='latn']"/>
		</percentFormats>
		<currencyFormats>
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="arab">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="arabext">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="bali">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="beng">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="brah">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="cakm">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="cham">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="deva">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="fullwide">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="gujr">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="guru">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="hanidec">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="java">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="kali">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="khmr">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="knda">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="lana">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="lanatham">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="laoo">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="latn">
			<currencySpacing>
				<beforeCurrency>
					<currencyMatch>[:^S:]</currencyMatch>
					<surroundingMatch>[:digit:]</surroundingMatch>
					<insertBetween> </insertBetween>
				</beforeCurrency>
				<afterCurrency>
					<currencyMatch>[:^S:]</currencyMatch>
					<surroundingMatch>[:digit:]</surroundingMatch>
					<insertBetween> </insertBetween>
				</afterCurrency>
			</currencySpacing>
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤ #,##0.00</pattern>
				</currencyFormat>
				<currencyFormat type="accounting">
					<alias source="locale" path="../currencyFormat[@type='standard']"/>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="other">{0} {1}</unitPattern>
		</currencyFormats>
		<currencyFormats numberSystem="lepc">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="limb">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="mlym">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="mong">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="mtei">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="mymr">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="mymrshan">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="nkoo">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="olck">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="orya">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="osma">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="saur">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="shrd">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="sora">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="sund">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="takr">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="talu">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="tamldec">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="telu">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="thai">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="tibt">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencyFormats numberSystem="vaii">
			<alias source="locale" path="../currencyFormats[@numberSystem='latn']"/>
		</currencyFormats>
		<currencies>
			<currency type="ARS">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="AUD">
				<symbol>A$</symbol>
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="BBD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="BDT">
				<symbol alt="narrow">৳</symbol>
			</currency>
			<currency type="BMD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="BND">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="BRL">
				<symbol>R$</symbol>
				<symbol alt="narrow">R$</symbol>
			</currency>
			<currency type="BSD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="BYR">
				<symbol alt="narrow">р.</symbol>
			</currency>
			<currency type="BZD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="CAD">
				<symbol>CA$</symbol>
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="CLP">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="CNY">
				<symbol>CN¥</symbol>
				<symbol alt="narrow">¥</symbol>
			</currency>
			<currency type="COP">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="CRC">
				<symbol alt="narrow">₡</symbol>
			</currency>
			<currency type="CUP">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="DOP">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="ESP">
				<symbol alt="narrow">₧</symbol>
			</currency>
			<currency type="EUR">
				<symbol>€</symbol>
				<symbol alt="narrow">€</symbol>
			</currency>
			<currency type="FJD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="GBP">
				<symbol>£</symbol>
				<symbol alt="narrow">£</symbol>
			</currency>
			<currency type="GHS">
				<symbol alt="narrow">₵</symbol>
			</currency>
			<currency type="GIP">
				<symbol alt="narrow">£</symbol>
			</currency>
			<currency type="GYD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="HKD">
				<symbol>HK$</symbol>
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="ILS">
				<symbol>₪</symbol>
				<symbol alt="narrow">₪</symbol>
			</currency>
			<currency type="INR">
				<symbol>₹</symbol>
				<symbol alt="narrow">₹</symbol>
			</currency>
			<currency type="JMD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="JPY">
				<symbol>JP¥</symbol>
				<symbol alt="narrow">¥</symbol>
			</currency>
			<currency type="KHR">
				<symbol alt="narrow">៛</symbol>
			</currency>
			<currency type="KRW">
				<symbol>₩</symbol>
				<symbol alt="narrow">₩</symbol>
			</currency>
			<currency type="KYD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="KZT">
				<symbol alt="narrow">₸</symbol>
			</currency>
			<currency type="LAK">
				<symbol alt="narrow">₭</symbol>
			</currency>
			<currency type="LRD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="MNT">
				<symbol alt="narrow">₮</symbol>
			</currency>
			<currency type="MXN">
				<symbol>MX$</symbol>
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="NAD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="NGN">
				<symbol alt="narrow">₦</symbol>
			</currency>
			<currency type="NZD">
				<symbol>NZ$</symbol>
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="PHP">
				<symbol alt="narrow">₱</symbol>
			</currency>
			<currency type="PYG">
				<symbol alt="narrow">₲</symbol>
			</currency>
			<currency type="RUR">
				<symbol alt="narrow">р.</symbol>
			</currency>
			<currency type="SBD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="SGD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="SRD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="SSP">
				<symbol alt="narrow">£</symbol>
			</currency>
			<currency type="THB">
				<symbol>฿</symbol>
				<symbol alt="narrow">฿</symbol>
			</currency>
			<currency type="TRY">
				<symbol alt="narrow">₺</symbol>
				<symbol alt="variant">TL</symbol>
			</currency>
			<currency type="TTD">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="TWD">
				<symbol>NT$</symbol>
				<symbol alt="narrow">NT$</symbol>
			</currency>
			<currency type="UAH">
				<symbol alt="narrow">₴</symbol>
			</currency>
			<currency type="USD">
				<symbol>US$</symbol>
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="UYU">
				<symbol alt="narrow">$</symbol>
			</currency>
			<currency type="VND">
				<symbol>₫</symbol>
				<symbol alt="narrow">₫</symbol>
			</currency>
			<currency type="XAF">
				<symbol>FCFA</symbol>
			</currency>
			<currency type="XCD">
				<symbol>EC$</symbol>
			</currency>
			<currency type="XOF">
				<symbol>CFA</symbol>
			</currency>
			<currency type="XPF">
				<symbol>CFPF</symbol>
			</currency>
		</currencies>
		<miscPatterns numberSystem="arab">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="arabext">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="bali">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="beng">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="brah">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="cakm">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="cham">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="deva">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="fullwide">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="gujr">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="guru">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="hanidec">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="java">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="kali">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="khmr">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="knda">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="lana">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="lanatham">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="laoo">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="latn">
			<pattern type="atLeast">⩾{0}</pattern>
			<pattern type="range">{0}–{1}</pattern>
		</miscPatterns>
		<miscPatterns numberSystem="lepc">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="limb">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="mlym">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="mong">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="mtei">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="mymr">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="mymrshan">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="nkoo">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="olck">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="orya">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="osma">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="saur">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="shrd">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="sora">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="sund">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="takr">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="talu">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="tamldec">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="telu">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="thai">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="tibt">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
		<miscPatterns numberSystem="vaii">
			<alias source="locale" path="../miscPatterns[@numberSystem='latn']"/>
		</miscPatterns>
	</numbers>
	<units>
		<unitLength type="long">
			<alias source="locale" path="../unitLength[@type='short']"/>
		</unitLength>
		<unitLength type="short">
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="other">{0} G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="other">{0}′</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="other">{0}″</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="other">{0} ac</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="other">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="other">{0} ft²</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="other">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="other">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="other">{0} mi²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="other">{0} d</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="other">{0} h</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="other">{0} min</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="other">{0} s</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="other">{0} w</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="other">{0} y</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="other">{0} ft</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="other">{0} in</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="other">{0} ly</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="other">{0} mi</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="other">{0} pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="other">{0} oz</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="other">{0} lb</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="other">{0} hp</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="other">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="other">{0} W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="other">{0} hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="other">{0} inHg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="other">{0} mbar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="other">{0} km/h</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="other">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="other">{0} mi/h</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="other">{0}°C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="other">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="narrow">
			<alias source="locale" path="../unitLength[@type='short']"/>
		</unitLength>
		<durationUnit type="hm">
			<durationUnitPattern>h:mm</durationUnitPattern> <!-- 33:59 -->
		</durationUnit>
		<durationUnit type="hms">
			<durationUnitPattern>h:mm:ss</durationUnitPattern> <!-- 33:04:59 -->
		</durationUnit>
		<durationUnit type="ms">
			<durationUnitPattern>m:ss</durationUnitPattern> <!-- 33:59 -->
		</durationUnit>
	</units>
	<listPatterns>
		<listPattern>
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, {1}</listPatternPart>
			<listPatternPart type="2">{0}, {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit">
			<alias source="locale" path="../listPattern[@type='unit-short']"/>
		</listPattern>
		<listPattern type="unit-narrow">
			<alias source="locale" path="../listPattern[@type='unit-short']"/>
		</listPattern>
		<listPattern type="unit-short">
			<alias source="locale" path="../listPattern"/>
		</listPattern>
	</listPatterns>
	<posix>
		<messages>
			<yesstr>yes:y</yesstr>
			<nostr>no:n</nostr>
		</messages>
	</posix>
</ldml>

