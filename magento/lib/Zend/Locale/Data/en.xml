<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE ldml SYSTEM "../../common/dtd/ldml.dtd">
<!-- Copyright © 1991-2014 Unicode, Inc.
CLDR data files are interpreted according to the LDML specification (http://unicode.org/reports/tr35/)
For terms of use, see http://www.unicode.org/copyright.html
-->
<ldml>
	<identity>
		<version number="$Revision: 9876 $"/>
		<generation date="$Date: 2014-03-05 23:14:25 -0600 (Wed, 05 Mar 2014) $"/>
		<language type="en"/>
	</identity>
	<localeDisplayNames>
		<localeDisplayPattern>
			<localePattern>{0} ({1})</localePattern>
			<localeSeparator>{0}, {1}</localeSeparator>
			<localeKeyTypePattern>{0}: {1}</localeKeyTypePattern>
		</localeDisplayPattern>
		<languages>
			<language type="aa">Afar</language>
			<language type="ab">Abkhazian</language>
			<language type="ace">Achinese</language>
			<language type="ach">Acoli</language>
			<language type="ada">Adangme</language>
			<language type="ady">Adyghe</language>
			<language type="ae">Avestan</language>
			<language type="af">Afrikaans</language>
			<language type="afh">Afrihili</language>
			<language type="agq">Aghem</language>
			<language type="ain">Ainu</language>
			<language type="ak">Akan</language>
			<language type="akk">Akkadian</language>
			<language type="ale">Aleut</language>
			<language type="alt">Southern Altai</language>
			<language type="am">Amharic</language>
			<language type="an">Aragonese</language>
			<language type="ang">Old English</language>
			<language type="anp">Angika</language>
			<language type="ar">Arabic</language>
			<language type="ar_001">Modern Standard Arabic</language>
			<language type="arc">Aramaic</language>
			<language type="arn">Mapuche</language>
			<language type="arp">Arapaho</language>
			<language type="arw">Arawak</language>
			<language type="as">Assamese</language>
			<language type="asa">Asu</language>
			<language type="ast">Asturian</language>
			<language type="av">Avaric</language>
			<language type="awa">Awadhi</language>
			<language type="ay">Aymara</language>
			<language type="az">Azerbaijani</language>
			<language type="az" alt="short">Azeri</language>
			<language type="ba">Bashkir</language>
			<language type="bal">Baluchi</language>
			<language type="ban">Balinese</language>
			<language type="bas">Basaa</language>
			<language type="bax">Bamun</language>
			<language type="bbj">Ghomala</language>
			<language type="be">Belarusian</language>
			<language type="bej">Beja</language>
			<language type="bem">Bemba</language>
			<language type="bez">Bena</language>
			<language type="bfd">Bafut</language>
			<language type="bg">Bulgarian</language>
			<language type="bho">Bhojpuri</language>
			<language type="bi">Bislama</language>
			<language type="bik">Bikol</language>
			<language type="bin">Bini</language>
			<language type="bkm">Kom</language>
			<language type="bla">Siksika</language>
			<language type="bm">Bambara</language>
			<language type="bn">Bengali</language>
			<language type="bo">Tibetan</language>
			<language type="br">Breton</language>
			<language type="bra">Braj</language>
			<language type="brx">Bodo</language>
			<language type="bs">Bosnian</language>
			<language type="bss">Akoose</language>
			<language type="bua">Buriat</language>
			<language type="bug">Buginese</language>
			<language type="bum">Bulu</language>
			<language type="byn">Blin</language>
			<language type="byv">Medumba</language>
			<language type="ca">Catalan</language>
			<language type="cad">Caddo</language>
			<language type="car">Carib</language>
			<language type="cay">Cayuga</language>
			<language type="cch">Atsam</language>
			<language type="ce">Chechen</language>
			<language type="ceb">Cebuano</language>
			<language type="cgg">Chiga</language>
			<language type="ch">Chamorro</language>
			<language type="chb">Chibcha</language>
			<language type="chg">Chagatai</language>
			<language type="chk">Chuukese</language>
			<language type="chm">Mari</language>
			<language type="chn">Chinook Jargon</language>
			<language type="cho">Choctaw</language>
			<language type="chp">Chipewyan</language>
			<language type="chr">Cherokee</language>
			<language type="chy">Cheyenne</language>
			<language type="ckb">Sorani Kurdish</language>
			<language type="co">Corsican</language>
			<language type="cop">Coptic</language>
			<language type="cr">Cree</language>
			<language type="crh">Crimean Turkish</language>
			<language type="cs">Czech</language>
			<language type="csb">Kashubian</language>
			<language type="cu">Church Slavic</language>
			<language type="cv">Chuvash</language>
			<language type="cy">Welsh</language>
			<language type="da">Danish</language>
			<language type="dak">Dakota</language>
			<language type="dar">Dargwa</language>
			<language type="dav">Taita</language>
			<language type="de">German</language>
			<language type="de_AT">Austrian German</language>
			<language type="de_CH">Swiss High German</language>
			<language type="del">Delaware</language>
			<language type="den">Slave</language>
			<language type="dgr">Dogrib</language>
			<language type="din">Dinka</language>
			<language type="dje">Zarma</language>
			<language type="doi">Dogri</language>
			<language type="dsb">Lower Sorbian</language>
			<language type="dua">Duala</language>
			<language type="dum">Middle Dutch</language>
			<language type="dv">Divehi</language>
			<language type="dyo">Jola-Fonyi</language>
			<language type="dyu">Dyula</language>
			<language type="dz">Dzongkha</language>
			<language type="dzg">Dazaga</language>
			<language type="ebu">Embu</language>
			<language type="ee">Ewe</language>
			<language type="efi">Efik</language>
			<language type="egy">Ancient Egyptian</language>
			<language type="eka">Ekajuk</language>
			<language type="el">Greek</language>
			<language type="elx">Elamite</language>
			<language type="en">English</language>
			<language type="en_AU">Australian English</language>
			<language type="en_CA">Canadian English</language>
			<language type="en_GB">British English</language>
			<language type="en_GB" alt="short">U.K. English</language>
			<language type="en_US">American English</language>
			<language type="en_US" alt="short">U.S. English</language>
			<language type="enm">Middle English</language>
			<language type="eo">Esperanto</language>
			<language type="es">Spanish</language>
			<language type="es_419">Latin American Spanish</language>
			<language type="es_ES">European Spanish</language>
			<language type="es_MX">Mexican Spanish</language>
			<language type="et">Estonian</language>
			<language type="eu">Basque</language>
			<language type="ewo">Ewondo</language>
			<language type="fa">Persian</language>
			<language type="fan">Fang</language>
			<language type="fat">Fanti</language>
			<language type="ff">Fulah</language>
			<language type="fi">Finnish</language>
			<language type="fil">Filipino</language>
			<language type="fj">Fijian</language>
			<language type="fo">Faroese</language>
			<language type="fon">Fon</language>
			<language type="fr">French</language>
			<language type="fr_CA">Canadian French</language>
			<language type="fr_CH">Swiss French</language>
			<language type="frm">Middle French</language>
			<language type="fro">Old French</language>
			<language type="frr">Northern Frisian</language>
			<language type="frs">Eastern Frisian</language>
			<language type="fur">Friulian</language>
			<language type="fy">Western Frisian</language>
			<language type="ga">Irish</language>
			<language type="gaa">Ga</language>
			<language type="gay">Gayo</language>
			<language type="gba">Gbaya</language>
			<language type="gd">Scottish Gaelic</language>
			<language type="gez">Geez</language>
			<language type="gil">Gilbertese</language>
			<language type="gl">Galician</language>
			<language type="gmh">Middle High German</language>
			<language type="gn">Guarani</language>
			<language type="goh">Old High German</language>
			<language type="gon">Gondi</language>
			<language type="gor">Gorontalo</language>
			<language type="got">Gothic</language>
			<language type="grb">Grebo</language>
			<language type="grc">Ancient Greek</language>
			<language type="gsw">Swiss German</language>
			<language type="gu">Gujarati</language>
			<language type="guz">Gusii</language>
			<language type="gv">Manx</language>
			<language type="gwi">Gwichʼin</language>
			<language type="ha">Hausa</language>
			<language type="hai">Haida</language>
			<language type="haw">Hawaiian</language>
			<language type="he">Hebrew</language>
			<language type="hi">Hindi</language>
			<language type="hil">Hiligaynon</language>
			<language type="hit">Hittite</language>
			<language type="hmn">Hmong</language>
			<language type="ho">Hiri Motu</language>
			<language type="hr">Croatian</language>
			<language type="hsb">Upper Sorbian</language>
			<language type="ht">Haitian</language>
			<language type="hu">Hungarian</language>
			<language type="hup">Hupa</language>
			<language type="hy">Armenian</language>
			<language type="hz">Herero</language>
			<language type="ia">Interlingua</language>
			<language type="iba">Iban</language>
			<language type="ibb">Ibibio</language>
			<language type="id">Indonesian</language>
			<language type="ie">Interlingue</language>
			<language type="ig">Igbo</language>
			<language type="ii">Sichuan Yi</language>
			<language type="ik">Inupiaq</language>
			<language type="ilo">Iloko</language>
			<language type="inh">Ingush</language>
			<language type="io">Ido</language>
			<language type="is">Icelandic</language>
			<language type="it">Italian</language>
			<language type="iu">Inuktitut</language>
			<language type="ja">Japanese</language>
			<language type="jbo">Lojban</language>
			<language type="jgo">Ngomba</language>
			<language type="jmc">Machame</language>
			<language type="jpr">Judeo-Persian</language>
			<language type="jrb">Judeo-Arabic</language>
			<language type="jv">Javanese</language>
			<language type="ka">Georgian</language>
			<language type="kaa">Kara-Kalpak</language>
			<language type="kab">Kabyle</language>
			<language type="kac">Kachin</language>
			<language type="kaj">Jju</language>
			<language type="kam">Kamba</language>
			<language type="kaw">Kawi</language>
			<language type="kbd">Kabardian</language>
			<language type="kbl">Kanembu</language>
			<language type="kcg">Tyap</language>
			<language type="kde">Makonde</language>
			<language type="kea">Kabuverdianu</language>
			<language type="kfo">Koro</language>
			<language type="kg">Kongo</language>
			<language type="kha">Khasi</language>
			<language type="kho">Khotanese</language>
			<language type="khq">Koyra Chiini</language>
			<language type="ki">Kikuyu</language>
			<language type="kj">Kuanyama</language>
			<language type="kk">Kazakh</language>
			<language type="kkj">Kako</language>
			<language type="kl">Kalaallisut</language>
			<language type="kln">Kalenjin</language>
			<language type="km">Khmer</language>
			<language type="kmb">Kimbundu</language>
			<language type="kn">Kannada</language>
			<language type="ko">Korean</language>
			<language type="kok">Konkani</language>
			<language type="kos">Kosraean</language>
			<language type="kpe">Kpelle</language>
			<language type="kr">Kanuri</language>
			<language type="krc">Karachay-Balkar</language>
			<language type="krl">Karelian</language>
			<language type="kru">Kurukh</language>
			<language type="ks">Kashmiri</language>
			<language type="ksb">Shambala</language>
			<language type="ksf">Bafia</language>
			<language type="ksh">Colognian</language>
			<language type="ku">Kurdish</language>
			<language type="kum">Kumyk</language>
			<language type="kut">Kutenai</language>
			<language type="kv">Komi</language>
			<language type="kw">Cornish</language>
			<language type="ky">Kyrgyz</language>
			<language type="ky" alt="variant">Kirghiz</language>
			<language type="la">Latin</language>
			<language type="lad">Ladino</language>
			<language type="lag">Langi</language>
			<language type="lah">Lahnda</language>
			<language type="lam">Lamba</language>
			<language type="lb">Luxembourgish</language>
			<language type="lez">Lezghian</language>
			<language type="lg">Ganda</language>
			<language type="li">Limburgish</language>
			<language type="lkt">Lakota</language>
			<language type="ln">Lingala</language>
			<language type="lo">Lao</language>
			<language type="lol">Mongo</language>
			<language type="loz">Lozi</language>
			<language type="lt">Lithuanian</language>
			<language type="lu">Luba-Katanga</language>
			<language type="lua">Luba-Lulua</language>
			<language type="lui">Luiseno</language>
			<language type="lun">Lunda</language>
			<language type="luo">Luo</language>
			<language type="lus">Mizo</language>
			<language type="luy">Luyia</language>
			<language type="lv">Latvian</language>
			<language type="mad">Madurese</language>
			<language type="maf">Mafa</language>
			<language type="mag">Magahi</language>
			<language type="mai">Maithili</language>
			<language type="mak">Makasar</language>
			<language type="man">Mandingo</language>
			<language type="mas">Masai</language>
			<language type="mde">Maba</language>
			<language type="mdf">Moksha</language>
			<language type="mdr">Mandar</language>
			<language type="men">Mende</language>
			<language type="mer">Meru</language>
			<language type="mfe">Morisyen</language>
			<language type="mg">Malagasy</language>
			<language type="mga">Middle Irish</language>
			<language type="mgh">Makhuwa-Meetto</language>
			<language type="mgo">Meta'</language>
			<language type="mh">Marshallese</language>
			<language type="mi">Maori</language>
			<language type="mic">Micmac</language>
			<language type="min">Minangkabau</language>
			<language type="mk">Macedonian</language>
			<language type="ml">Malayalam</language>
			<language type="mn">Mongolian</language>
			<language type="mnc">Manchu</language>
			<language type="mni">Manipuri</language>
			<language type="moh">Mohawk</language>
			<language type="mos">Mossi</language>
			<language type="mr">Marathi</language>
			<language type="ms">Malay</language>
			<language type="mt">Maltese</language>
			<language type="mua">Mundang</language>
			<language type="mul">Multiple Languages</language>
			<language type="mus">Creek</language>
			<language type="mwl">Mirandese</language>
			<language type="mwr">Marwari</language>
			<language type="my">Burmese</language>
			<language type="mye">Myene</language>
			<language type="myv">Erzya</language>
			<language type="na">Nauru</language>
			<language type="nap">Neapolitan</language>
			<language type="naq">Nama</language>
			<language type="nb">Norwegian Bokmål</language>
			<language type="nd">North Ndebele</language>
			<language type="nds">Low German</language>
			<language type="ne">Nepali</language>
			<language type="new">Newari</language>
			<language type="ng">Ndonga</language>
			<language type="nia">Nias</language>
			<language type="niu">Niuean</language>
			<language type="nl">Dutch</language>
			<language type="nl_BE">Flemish</language>
			<language type="nmg">Kwasio</language>
			<language type="nn">Norwegian Nynorsk</language>
			<language type="nnh">Ngiemboon</language>
			<language type="no">Norwegian</language>
			<language type="nog">Nogai</language>
			<language type="non">Old Norse</language>
			<language type="nqo">N’Ko</language>
			<language type="nr">South Ndebele</language>
			<language type="nso">Northern Sotho</language>
			<language type="nus">Nuer</language>
			<language type="nv">Navajo</language>
			<language type="nwc">Classical Newari</language>
			<language type="ny">Nyanja</language>
			<language type="nym">Nyamwezi</language>
			<language type="nyn">Nyankole</language>
			<language type="nyo">Nyoro</language>
			<language type="nzi">Nzima</language>
			<language type="oc">Occitan</language>
			<language type="oj">Ojibwa</language>
			<language type="om">Oromo</language>
			<language type="or">Oriya</language>
			<language type="os">Ossetic</language>
			<language type="osa">Osage</language>
			<language type="ota">Ottoman Turkish</language>
			<language type="pa">Punjabi</language>
			<language type="pag">Pangasinan</language>
			<language type="pal">Pahlavi</language>
			<language type="pam">Pampanga</language>
			<language type="pap">Papiamento</language>
			<language type="pau">Palauan</language>
			<language type="peo">Old Persian</language>
			<language type="phn">Phoenician</language>
			<language type="pi">Pali</language>
			<language type="pl">Polish</language>
			<language type="pon">Pohnpeian</language>
			<language type="pro">Old Provençal</language>
			<language type="ps">Pashto</language>
			<language type="ps" alt="variant">Pushto</language>
			<language type="pt">Portuguese</language>
			<language type="pt_BR">Brazilian Portuguese</language>
			<language type="pt_PT">European Portuguese</language>
			<language type="qu">Quechua</language>
			<language type="raj">Rajasthani</language>
			<language type="rap">Rapanui</language>
			<language type="rar">Rarotongan</language>
			<language type="rm">Romansh</language>
			<language type="rn">Rundi</language>
			<language type="ro">Romanian</language>
			<language type="ro_MD">Moldavian</language>
			<language type="rof">Rombo</language>
			<language type="rom">Romany</language>
			<language type="root">Root</language>
			<language type="ru">Russian</language>
			<language type="rup">Aromanian</language>
			<language type="rw">Kinyarwanda</language>
			<language type="rwk">Rwa</language>
			<language type="sa">Sanskrit</language>
			<language type="sad">Sandawe</language>
			<language type="sah">Sakha</language>
			<language type="sam">Samaritan Aramaic</language>
			<language type="saq">Samburu</language>
			<language type="sas">Sasak</language>
			<language type="sat">Santali</language>
			<language type="sba">Ngambay</language>
			<language type="sbp">Sangu</language>
			<language type="sc">Sardinian</language>
			<language type="scn">Sicilian</language>
			<language type="sco">Scots</language>
			<language type="sd">Sindhi</language>
			<language type="se">Northern Sami</language>
			<language type="see">Seneca</language>
			<language type="seh">Sena</language>
			<language type="sel">Selkup</language>
			<language type="ses">Koyraboro Senni</language>
			<language type="sg">Sango</language>
			<language type="sga">Old Irish</language>
			<language type="sh">Serbo-Croatian</language>
			<language type="shi">Tachelhit</language>
			<language type="shn">Shan</language>
			<language type="shu">Chadian Arabic</language>
			<language type="si">Sinhala</language>
			<language type="sid">Sidamo</language>
			<language type="sk">Slovak</language>
			<language type="sl">Slovenian</language>
			<language type="sm">Samoan</language>
			<language type="sma">Southern Sami</language>
			<language type="smj">Lule Sami</language>
			<language type="smn">Inari Sami</language>
			<language type="sms">Skolt Sami</language>
			<language type="sn">Shona</language>
			<language type="snk">Soninke</language>
			<language type="so">Somali</language>
			<language type="sog">Sogdien</language>
			<language type="sq">Albanian</language>
			<language type="sr">Serbian</language>
			<language type="srn">Sranan Tongo</language>
			<language type="srr">Serer</language>
			<language type="ss">Swati</language>
			<language type="ssy">Saho</language>
			<language type="st">Southern Sotho</language>
			<language type="su">Sundanese</language>
			<language type="suk">Sukuma</language>
			<language type="sus">Susu</language>
			<language type="sux">Sumerian</language>
			<language type="sv">Swedish</language>
			<language type="sw">Swahili</language>
			<language type="swb">Comorian</language>
			<language type="swc">Congo Swahili</language>
			<language type="syc">Classical Syriac</language>
			<language type="syr">Syriac</language>
			<language type="ta">Tamil</language>
			<language type="te">Telugu</language>
			<language type="tem">Timne</language>
			<language type="teo">Teso</language>
			<language type="ter">Tereno</language>
			<language type="tet">Tetum</language>
			<language type="tg">Tajik</language>
			<language type="th">Thai</language>
			<language type="ti">Tigrinya</language>
			<language type="tig">Tigre</language>
			<language type="tiv">Tiv</language>
			<language type="tk">Turkmen</language>
			<language type="tkl">Tokelau</language>
			<language type="tl">Tagalog</language>
			<language type="tlh">Klingon</language>
			<language type="tli">Tlingit</language>
			<language type="tmh">Tamashek</language>
			<language type="tn">Tswana</language>
			<language type="to">Tongan</language>
			<language type="tog">Nyasa Tonga</language>
			<language type="tpi">Tok Pisin</language>
			<language type="tr">Turkish</language>
			<language type="trv">Taroko</language>
			<language type="ts">Tsonga</language>
			<language type="tsi">Tsimshian</language>
			<language type="tt">Tatar</language>
			<language type="tum">Tumbuka</language>
			<language type="tvl">Tuvalu</language>
			<language type="tw">Twi</language>
			<language type="twq">Tasawaq</language>
			<language type="ty">Tahitian</language>
			<language type="tyv">Tuvinian</language>
			<language type="tzm">Central Atlas Tamazight</language>
			<language type="udm">Udmurt</language>
			<language type="ug">Uyghur</language>
			<language type="ug" alt="variant">Uighur</language>
			<language type="uga">Ugaritic</language>
			<language type="uk">Ukrainian</language>
			<language type="umb">Umbundu</language>
			<language type="und">Unknown Language</language>
			<language type="ur">Urdu</language>
			<language type="uz">Uzbek</language>
			<language type="vai">Vai</language>
			<language type="ve">Venda</language>
			<language type="vi">Vietnamese</language>
			<language type="vo">Volapük</language>
			<language type="vot">Votic</language>
			<language type="vun">Vunjo</language>
			<language type="wa">Walloon</language>
			<language type="wae">Walser</language>
			<language type="wal">Wolaytta</language>
			<language type="war">Waray</language>
			<language type="was">Washo</language>
			<language type="wo">Wolof</language>
			<language type="xal">Kalmyk</language>
			<language type="xh">Xhosa</language>
			<language type="xog">Soga</language>
			<language type="yao">Yao</language>
			<language type="yap">Yapese</language>
			<language type="yav">Yangben</language>
			<language type="ybb">Yemba</language>
			<language type="yi">Yiddish</language>
			<language type="yo">Yoruba</language>
			<language type="yue">Cantonese</language>
			<language type="za">Zhuang</language>
			<language type="zap">Zapotec</language>
			<language type="zbl">Blissymbols</language>
			<language type="zen">Zenaga</language>
			<language type="zgh">Standard Moroccan Tamazight</language>
			<language type="zh">Chinese</language>
			<language type="zh_Hans">Simplified Chinese</language>
			<language type="zh_Hant">Traditional Chinese</language>
			<language type="zu">Zulu</language>
			<language type="zun">Zuni</language>
			<language type="zxx">No linguistic content</language>
			<language type="zza">Zaza</language>
		</languages>
		<scripts>
			<script type="Afak">Afaka</script>
			<script type="Arab">Arabic</script>
			<script type="Arab" alt="variant">Perso-Arabic</script>
			<script type="Armi">Imperial Aramaic</script>
			<script type="Armn">Armenian</script>
			<script type="Avst">Avestan</script>
			<script type="Bali">Balinese</script>
			<script type="Bamu">Bamum</script>
			<script type="Bass">Bassa Vah</script>
			<script type="Batk">Batak</script>
			<script type="Beng">Bengali</script>
			<script type="Blis">Blissymbols</script>
			<script type="Bopo">Bopomofo</script>
			<script type="Brah">Brahmi</script>
			<script type="Brai">Braille</script>
			<script type="Bugi">Buginese</script>
			<script type="Buhd">Buhid</script>
			<script type="Cakm">Chakma</script>
			<script type="Cans">Unified Canadian Aboriginal Syllabics</script>
			<script type="Cari">Carian</script>
			<script type="Cham">Cham</script>
			<script type="Cher">Cherokee</script>
			<script type="Cirt">Cirth</script>
			<script type="Copt">Coptic</script>
			<script type="Cprt">Cypriot</script>
			<script type="Cyrl">Cyrillic</script>
			<script type="Cyrs">Old Church Slavonic Cyrillic</script>
			<script type="Deva">Devanagari</script>
			<script type="Dsrt">Deseret</script>
			<script type="Dupl">Duployan shorthand</script>
			<script type="Egyd">Egyptian demotic</script>
			<script type="Egyh">Egyptian hieratic</script>
			<script type="Egyp">Egyptian hieroglyphs</script>
			<script type="Ethi">Ethiopic</script>
			<script type="Geok">Georgian Khutsuri</script>
			<script type="Geor">Georgian</script>
			<script type="Glag">Glagolitic</script>
			<script type="Goth">Gothic</script>
			<script type="Gran">Grantha</script>
			<script type="Grek">Greek</script>
			<script type="Gujr">Gujarati</script>
			<script type="Guru">Gurmukhi</script>
			<script type="Hang">Hangul</script>
			<script type="Hani">Han</script>
			<script type="Hano">Hanunoo</script>
			<script type="Hans">Simplified</script>
			<script type="Hans" alt="stand-alone">Simplified Han</script>
			<script type="Hant">Traditional</script>
			<script type="Hant" alt="stand-alone">Traditional Han</script>
			<script type="Hebr">Hebrew</script>
			<script type="Hira">Hiragana</script>
			<script type="Hluw">Anatolian Hieroglyphs</script>
			<script type="Hmng">Pahawh Hmong</script>
			<script type="Hrkt">Japanese syllabaries</script>
			<script type="Hung">Old Hungarian</script>
			<script type="Inds">Indus</script>
			<script type="Ital">Old Italic</script>
			<script type="Java">Javanese</script>
			<script type="Jpan">Japanese</script>
			<script type="Jurc">Jurchen</script>
			<script type="Kali">Kayah Li</script>
			<script type="Kana">Katakana</script>
			<script type="Khar">Kharoshthi</script>
			<script type="Khmr">Khmer</script>
			<script type="Khoj">Khojki</script>
			<script type="Knda">Kannada</script>
			<script type="Kore">Korean</script>
			<script type="Kpel">Kpelle</script>
			<script type="Kthi">Kaithi</script>
			<script type="Lana">Lanna</script>
			<script type="Laoo">Lao</script>
			<script type="Latf">Fraktur Latin</script>
			<script type="Latg">Gaelic Latin</script>
			<script type="Latn">Latin</script>
			<script type="Lepc">Lepcha</script>
			<script type="Limb">Limbu</script>
			<script type="Lina">Linear A</script>
			<script type="Linb">Linear B</script>
			<script type="Lisu">Fraser</script>
			<script type="Loma">Loma</script>
			<script type="Lyci">Lycian</script>
			<script type="Lydi">Lydian</script>
			<script type="Mand">Mandaean</script>
			<script type="Mani">Manichaean</script>
			<script type="Maya">Mayan hieroglyphs</script>
			<script type="Mend">Mende</script>
			<script type="Merc">Meroitic Cursive</script>
			<script type="Mero">Meroitic</script>
			<script type="Mlym">Malayalam</script>
			<script type="Mong">Mongolian</script>
			<script type="Moon">Moon</script>
			<script type="Mroo">Mro</script>
			<script type="Mtei">Meitei Mayek</script>
			<script type="Mymr">Myanmar</script>
			<script type="Narb">Old North Arabian</script>
			<script type="Nbat">Nabataean</script>
			<script type="Nkgb">Naxi Geba</script>
			<script type="Nkoo">N’Ko</script>
			<script type="Nshu">Nüshu</script>
			<script type="Ogam">Ogham</script>
			<script type="Olck">Ol Chiki</script>
			<script type="Orkh">Orkhon</script>
			<script type="Orya">Oriya</script>
			<script type="Osma">Osmanya</script>
			<script type="Palm">Palmyrene</script>
			<script type="Perm">Old Permic</script>
			<script type="Phag">Phags-pa</script>
			<script type="Phli">Inscriptional Pahlavi</script>
			<script type="Phlp">Psalter Pahlavi</script>
			<script type="Phlv">Book Pahlavi</script>
			<script type="Phnx">Phoenician</script>
			<script type="Plrd">Pollard Phonetic</script>
			<script type="Prti">Inscriptional Parthian</script>
			<script type="Rjng">Rejang</script>
			<script type="Roro">Rongorongo</script>
			<script type="Runr">Runic</script>
			<script type="Samr">Samaritan</script>
			<script type="Sara">Sarati</script>
			<script type="Sarb">Old South Arabian</script>
			<script type="Saur">Saurashtra</script>
			<script type="Sgnw">SignWriting</script>
			<script type="Shaw">Shavian</script>
			<script type="Shrd">Sharada</script>
			<script type="Sind">Khudawadi</script>
			<script type="Sinh">Sinhala</script>
			<script type="Sora">Sora Sompeng</script>
			<script type="Sund">Sundanese</script>
			<script type="Sylo">Syloti Nagri</script>
			<script type="Syrc">Syriac</script>
			<script type="Syre">Estrangelo Syriac</script>
			<script type="Syrj">Western Syriac</script>
			<script type="Syrn">Eastern Syriac</script>
			<script type="Tagb">Tagbanwa</script>
			<script type="Takr">Takri</script>
			<script type="Tale">Tai Le</script>
			<script type="Talu">New Tai Lue</script>
			<script type="Taml">Tamil</script>
			<script type="Tang">Tangut</script>
			<script type="Tavt">Tai Viet</script>
			<script type="Telu">Telugu</script>
			<script type="Teng">Tengwar</script>
			<script type="Tfng">Tifinagh</script>
			<script type="Tglg">Tagalog</script>
			<script type="Thaa">Thaana</script>
			<script type="Thai">Thai</script>
			<script type="Tibt">Tibetan</script>
			<script type="Tirh">Tirhuta</script>
			<script type="Ugar">Ugaritic</script>
			<script type="Vaii">Vai</script>
			<script type="Visp">Visible Speech</script>
			<script type="Wara">Varang Kshiti</script>
			<script type="Wole">Woleai</script>
			<script type="Xpeo">Old Persian</script>
			<script type="Xsux">Sumero-Akkadian Cuneiform</script>
			<script type="Yiii">Yi</script>
			<script type="Zinh">Inherited</script>
			<script type="Zmth">Mathematical Notation</script>
			<script type="Zsym">Symbols</script>
			<script type="Zxxx">Unwritten</script>
			<script type="Zyyy">Common</script>
			<script type="Zzzz">Unknown Script</script>
		</scripts>
		<territories>
			<territory type="001">World</territory>
			<!-- UN codes -->
			<territory type="002">Africa</territory>
			<territory type="003">North America</territory>
			<territory type="005">South America</territory>
			<territory type="009">Oceania</territory>
			<territory type="011">Western Africa</territory>
			<territory type="013">Central America</territory>
			<territory type="014">Eastern Africa</territory>
			<territory type="015">Northern Africa</territory>
			<territory type="017">Middle Africa</territory>
			<territory type="018">Southern Africa</territory>
			<territory type="019">Americas</territory>
			<territory type="021">Northern America</territory>
			<territory type="029">Caribbean</territory>
			<territory type="030">Eastern Asia</territory>
			<territory type="034">Southern Asia</territory>
			<territory type="035">South-Eastern Asia</territory>
			<territory type="039">Southern Europe</territory>
			<territory type="053">Australasia</territory>
			<territory type="054">Melanesia</territory>
			<territory type="057">Micronesian Region</territory>
			<territory type="061">Polynesia</territory>
			<territory type="142">Asia</territory>
			<territory type="143">Central Asia</territory>
			<territory type="145">Western Asia</territory>
			<territory type="150">Europe</territory>
			<territory type="151">Eastern Europe</territory>
			<territory type="154">Northern Europe</territory>
			<territory type="155">Western Europe</territory>
			<territory type="419">Latin America</territory>
			<territory type="AC">Ascension Island</territory>
			<territory type="AD">Andorra</territory>
			<territory type="AE">United Arab Emirates</territory>
			<territory type="AF">Afghanistan</territory>
			<territory type="AG">Antigua and Barbuda</territory>
			<territory type="AI">Anguilla</territory>
			<territory type="AL">Albania</territory>
			<territory type="AM">Armenia</territory>
			<territory type="AN">Netherlands Antilles</territory>
			<territory type="AO">Angola</territory>
			<territory type="AQ">Antarctica</territory>
			<territory type="AR">Argentina</territory>
			<territory type="AS">American Samoa</territory>
			<territory type="AT">Austria</territory>
			<territory type="AU">Australia</territory>
			<territory type="AW">Aruba</territory>
			<territory type="AX">Åland Islands</territory>
			<territory type="AZ">Azerbaijan</territory>
			<territory type="BA">Bosnia and Herzegovina</territory>
			<territory type="BB">Barbados</territory>
			<territory type="BD">Bangladesh</territory>
			<territory type="BE">Belgium</territory>
			<territory type="BF">Burkina Faso</territory>
			<territory type="BG">Bulgaria</territory>
			<territory type="BH">Bahrain</territory>
			<territory type="BI">Burundi</territory>
			<territory type="BJ">Benin</territory>
			<territory type="BL">Saint Barthélemy</territory>
			<territory type="BM">Bermuda</territory>
			<territory type="BN">Brunei</territory>
			<territory type="BO">Bolivia</territory>
			<territory type="BQ">Caribbean Netherlands</territory>
			<territory type="BR">Brazil</territory>
			<territory type="BS">Bahamas</territory>
			<territory type="BT">Bhutan</territory>
			<territory type="BV">Bouvet Island</territory>
			<territory type="BW">Botswana</territory>
			<territory type="BY">Belarus</territory>
			<territory type="BZ">Belize</territory>
			<territory type="CA">Canada</territory>
			<territory type="CC">Cocos (Keeling) Islands</territory>
			<territory type="CD">Congo - Kinshasa</territory>
			<territory type="CD" alt="variant">Congo (DRC)</territory>
			<territory type="CF">Central African Republic</territory>
			<territory type="CG">Congo - Brazzaville</territory>
			<territory type="CG" alt="variant">Congo (Republic)</territory>
			<territory type="CH">Switzerland</territory>
			<territory type="CI">Côte d’Ivoire</territory>
			<territory type="CI" alt="variant">Ivory Coast</territory>
			<territory type="CK">Cook Islands</territory>
			<territory type="CL">Chile</territory>
			<territory type="CM">Cameroon</territory>
			<territory type="CN">China</territory>
			<territory type="CO">Colombia</territory>
			<territory type="CP">Clipperton Island</territory>
			<territory type="CR">Costa Rica</territory>
			<territory type="CU">Cuba</territory>
			<territory type="CV">Cape Verde</territory>
			<territory type="CW">Curaçao</territory>
			<territory type="CX">Christmas Island</territory>
			<territory type="CY">Cyprus</territory>
			<territory type="CZ">Czech Republic</territory>
			<territory type="DE">Germany</territory>
			<territory type="DG">Diego Garcia</territory>
			<territory type="DJ">Djibouti</territory>
			<territory type="DK">Denmark</territory>
			<territory type="DM">Dominica</territory>
			<territory type="DO">Dominican Republic</territory>
			<territory type="DZ">Algeria</territory>
			<territory type="EA">Ceuta and Melilla</territory>
			<territory type="EC">Ecuador</territory>
			<territory type="EE">Estonia</territory>
			<territory type="EG">Egypt</territory>
			<territory type="EH">Western Sahara</territory>
			<territory type="ER">Eritrea</territory>
			<territory type="ES">Spain</territory>
			<territory type="ET">Ethiopia</territory>
			<territory type="EU">European Union</territory>
			<territory type="FI">Finland</territory>
			<territory type="FJ">Fiji</territory>
			<territory type="FK">Falkland Islands</territory>
			<territory type="FK" alt="variant">Falkland Islands (Islas Malvinas)</territory>
			<territory type="FM">Micronesia</territory>
			<territory type="FO">Faroe Islands</territory>
			<territory type="FR">France</territory>
			<territory type="GA">Gabon</territory>
			<territory type="GB">United Kingdom</territory>
			<territory type="GB" alt="short">U.K.</territory>
			<territory type="GD">Grenada</territory>
			<territory type="GE">Georgia</territory>
			<territory type="GF">French Guiana</territory>
			<territory type="GG">Guernsey</territory>
			<territory type="GH">Ghana</territory>
			<territory type="GI">Gibraltar</territory>
			<territory type="GL">Greenland</territory>
			<territory type="GM">Gambia</territory>
			<territory type="GN">Guinea</territory>
			<territory type="GP">Guadeloupe</territory>
			<territory type="GQ">Equatorial Guinea</territory>
			<territory type="GR">Greece</territory>
			<territory type="GS">South Georgia &amp; South Sandwich Islands</territory>
			<territory type="GT">Guatemala</territory>
			<territory type="GU">Guam</territory>
			<territory type="GW">Guinea-Bissau</territory>
			<territory type="GY">Guyana</territory>
			<territory type="HK">Hong Kong SAR China</territory>
			<territory type="HK" alt="short">Hong Kong</territory>
			<territory type="HM">Heard &amp; McDonald Islands</territory>
			<territory type="HN">Honduras</territory>
			<territory type="HR">Croatia</territory>
			<territory type="HT">Haiti</territory>
			<territory type="HU">Hungary</territory>
			<territory type="IC">Canary Islands</territory>
			<territory type="ID">Indonesia</territory>
			<territory type="IE">Ireland</territory>
			<territory type="IL">Israel</territory>
			<territory type="IM">Isle of Man</territory>
			<territory type="IN">India</territory>
			<territory type="IO">British Indian Ocean Territory</territory>
			<territory type="IQ">Iraq</territory>
			<territory type="IR">Iran</territory>
			<territory type="IS">Iceland</territory>
			<territory type="IT">Italy</territory>
			<territory type="JE">Jersey</territory>
			<territory type="JM">Jamaica</territory>
			<territory type="JO">Jordan</territory>
			<territory type="JP">Japan</territory>
			<territory type="KE">Kenya</territory>
			<territory type="KG">Kyrgyzstan</territory>
			<territory type="KH">Cambodia</territory>
			<territory type="KI">Kiribati</territory>
			<territory type="KM">Comoros</territory>
			<territory type="KN">Saint Kitts and Nevis</territory>
			<territory type="KP">North Korea</territory>
			<territory type="KR">South Korea</territory>
			<territory type="KW">Kuwait</territory>
			<territory type="KY">Cayman Islands</territory>
			<territory type="KZ">Kazakhstan</territory>
			<territory type="LA">Laos</territory>
			<territory type="LB">Lebanon</territory>
			<territory type="LC">Saint Lucia</territory>
			<territory type="LI">Liechtenstein</territory>
			<territory type="LK">Sri Lanka</territory>
			<territory type="LR">Liberia</territory>
			<territory type="LS">Lesotho</territory>
			<territory type="LT">Lithuania</territory>
			<territory type="LU">Luxembourg</territory>
			<territory type="LV">Latvia</territory>
			<territory type="LY">Libya</territory>
			<territory type="MA">Morocco</territory>
			<territory type="MC">Monaco</territory>
			<territory type="MD">Moldova</territory>
			<territory type="ME">Montenegro</territory>
			<territory type="MF">Saint Martin</territory>
			<territory type="MG">Madagascar</territory>
			<territory type="MH">Marshall Islands</territory>
			<territory type="MK">Macedonia</territory>
			<territory type="MK" alt="variant">Macedonia (FYROM)</territory>
			<territory type="ML">Mali</territory>
			<territory type="MM">Myanmar (Burma)</territory>
			<territory type="MN">Mongolia</territory>
			<territory type="MO">Macau SAR China</territory>
			<territory type="MO" alt="short">Macau</territory>
			<territory type="MP">Northern Mariana Islands</territory>
			<territory type="MQ">Martinique</territory>
			<territory type="MR">Mauritania</territory>
			<territory type="MS">Montserrat</territory>
			<territory type="MT">Malta</territory>
			<territory type="MU">Mauritius</territory>
			<territory type="MV">Maldives</territory>
			<territory type="MW">Malawi</territory>
			<territory type="MX">Mexico</territory>
			<territory type="MY">Malaysia</territory>
			<territory type="MZ">Mozambique</territory>
			<territory type="NA">Namibia</territory>
			<territory type="NC">New Caledonia</territory>
			<territory type="NE">Niger</territory>
			<territory type="NF">Norfolk Island</territory>
			<territory type="NG">Nigeria</territory>
			<territory type="NI">Nicaragua</territory>
			<territory type="NL">Netherlands</territory>
			<territory type="NO">Norway</territory>
			<territory type="NP">Nepal</territory>
			<territory type="NR">Nauru</territory>
			<territory type="NU">Niue</territory>
			<territory type="NZ">New Zealand</territory>
			<territory type="OM">Oman</territory>
			<territory type="PA">Panama</territory>
			<territory type="PE">Peru</territory>
			<territory type="PF">French Polynesia</territory>
			<territory type="PG">Papua New Guinea</territory>
			<territory type="PH">Philippines</territory>
			<territory type="PK">Pakistan</territory>
			<territory type="PL">Poland</territory>
			<territory type="PM">Saint Pierre and Miquelon</territory>
			<territory type="PN">Pitcairn Islands</territory>
			<territory type="PR">Puerto Rico</territory>
			<territory type="PS">Palestinian Territories</territory>
			<territory type="PS" alt="short">Palestine</territory>
			<territory type="PT">Portugal</territory>
			<territory type="PW">Palau</territory>
			<territory type="PY">Paraguay</territory>
			<territory type="QA">Qatar</territory>
			<territory type="QO">Outlying Oceania</territory>
			<territory type="RE">Réunion</territory>
			<territory type="RO">Romania</territory>
			<territory type="RS">Serbia</territory>
			<territory type="RU">Russia</territory>
			<territory type="RW">Rwanda</territory>
			<territory type="SA">Saudi Arabia</territory>
			<territory type="SB">Solomon Islands</territory>
			<territory type="SC">Seychelles</territory>
			<territory type="SD">Sudan</territory>
			<territory type="SE">Sweden</territory>
			<territory type="SG">Singapore</territory>
			<territory type="SH">Saint Helena</territory>
			<territory type="SI">Slovenia</territory>
			<territory type="SJ">Svalbard and Jan Mayen</territory>
			<territory type="SK">Slovakia</territory>
			<territory type="SL">Sierra Leone</territory>
			<territory type="SM">San Marino</territory>
			<territory type="SN">Senegal</territory>
			<territory type="SO">Somalia</territory>
			<territory type="SR">Suriname</territory>
			<territory type="SS">South Sudan</territory>
			<territory type="ST">São Tomé and Príncipe</territory>
			<territory type="SV">El Salvador</territory>
			<territory type="SX">Sint Maarten</territory>
			<territory type="SY">Syria</territory>
			<territory type="SZ">Swaziland</territory>
			<territory type="TA">Tristan da Cunha</territory>
			<territory type="TC">Turks and Caicos Islands</territory>
			<territory type="TD">Chad</territory>
			<territory type="TF">French Southern Territories</territory>
			<territory type="TG">Togo</territory>
			<territory type="TH">Thailand</territory>
			<territory type="TJ">Tajikistan</territory>
			<territory type="TK">Tokelau</territory>
			<territory type="TL">Timor-Leste</territory>
			<territory type="TL" alt="variant">East Timor</territory>
			<territory type="TM">Turkmenistan</territory>
			<territory type="TN">Tunisia</territory>
			<territory type="TO">Tonga</territory>
			<territory type="TR">Turkey</territory>
			<territory type="TT">Trinidad and Tobago</territory>
			<territory type="TV">Tuvalu</territory>
			<territory type="TW">Taiwan</territory>
			<territory type="TZ">Tanzania</territory>
			<territory type="UA">Ukraine</territory>
			<territory type="UG">Uganda</territory>
			<territory type="UM">U.S. Outlying Islands</territory>
			<territory type="US">United States</territory>
			<territory type="US" alt="short">U.S.</territory>
			<territory type="UY">Uruguay</territory>
			<territory type="UZ">Uzbekistan</territory>
			<territory type="VA">Vatican City</territory>
			<territory type="VC">St. Vincent &amp; Grenadines</territory>
			<territory type="VE">Venezuela</territory>
			<territory type="VG">British Virgin Islands</territory>
			<territory type="VI">U.S. Virgin Islands</territory>
			<territory type="VN">Vietnam</territory>
			<territory type="VU">Vanuatu</territory>
			<territory type="WF">Wallis and Futuna</territory>
			<territory type="WS">Samoa</territory>
			<territory type="XK">Kosovo</territory>
			<territory type="YE">Yemen</territory>
			<territory type="YT">Mayotte</territory>
			<territory type="ZA">South Africa</territory>
			<territory type="ZM">Zambia</territory>
			<territory type="ZW">Zimbabwe</territory>
			<territory type="ZZ">Unknown Region</territory>
		</territories>
		<variants>
			<variant type="1901">Traditional German orthography</variant>
			<variant type="1994">Standardized Resian orthography</variant>
			<variant type="1996">German orthography of 1996</variant>
			<variant type="1606NICT">Late Middle French to 1606</variant>
			<variant type="1694ACAD">Early Modern French</variant>
			<variant type="1959ACAD">Academic</variant>
			<variant type="ALALC97">ALA-LC Romanization, 1997 edition</variant>
			<variant type="ALUKU">Aluku dialect</variant>
			<variant type="AREVELA">Eastern Armenian</variant>
			<variant type="AREVMDA">Western Armenian</variant>
			<variant type="BAKU1926">Unified Turkic Latin Alphabet</variant>
			<variant type="BALANKA">Balanka dialect of Anii</variant>
			<variant type="BARLA">&gt;Barlavento dialect group of Kabuverdianu</variant>
			<variant type="BISKE">San Giorgio/Bila dialect</variant>
			<variant type="BOHORIC">Bohorič alphabet</variant>
			<variant type="BOONT">Boontling</variant>
			<variant type="DAJNKO">Dajnko alphabet</variant>
			<variant type="EKAVSK">Serbian with Ekavian pronunciation</variant>
			<variant type="EMODENG">Early Modern English</variant>
			<variant type="FONIPA">IPA Phonetics</variant>
			<variant type="FONUPA">UPA Phonetics</variant>
			<variant type="HEPBURN">Hepburn romanization</variant>
			<variant type="HEPLOC">Hepburn romanization, Library of Congress method</variant>
			<variant type="IJEKAVSK">Serbian with Ijekavian pronunciation</variant>
			<variant type="KKCOR">Common Orthography</variant>
			<variant type="KSCOR">Standard Orthography</variant>
			<variant type="LIPAW">The Lipovaz dialect of Resian</variant>
			<variant type="METELKO">Metelko alphabet</variant>
			<variant type="MONOTON">Monotonic</variant>
			<variant type="NDYUKA">Ndyuka dialect</variant>
			<variant type="NEDIS">Natisone dialect</variant>
			<variant type="NJIVA">Gniva/Njiva dialect</variant>
			<variant type="NULIK">Modern Volapük</variant>
			<variant type="OSOJS">Oseacco/Osojane dialect</variant>
			<variant type="PAMAKA">Pamaka dialect</variant>
			<variant type="PINYIN">Pinyin Romanization</variant>
			<variant type="POLYTON">Polytonic</variant>
			<variant type="POSIX">Computer</variant>
			<variant type="REVISED">Revised Orthography</variant>
			<variant type="RIGIK">Classic Volapük</variant>
			<variant type="ROZAJ">Resian</variant>
			<variant type="SAAHO">Saho</variant>
			<variant type="SCOTLAND">Scottish Standard English</variant>
			<variant type="SCOUSE">Scouse</variant>
			<variant type="SOLBA">Stolvizza/Solbica dialect</variant>
			<variant type="SOTAV">Sotavento dialect group of Kabuverdianu</variant>
			<variant type="TARASK">Taraskievica orthography</variant>
			<variant type="UCCOR">Unified Orthography</variant>
			<variant type="UCRCOR">Unified Revised Orthography</variant>
			<variant type="UNIFON">Unifon phonetic alphabet</variant>
			<variant type="VALENCIA">Valencian</variant>
			<variant type="WADEGILE">Wade-Giles Romanization</variant>
		</variants>
		<keys>
			<key type="calendar">Calendar</key>
			<key type="colAlternate">Ignore Symbols Sorting</key>
			<key type="colBackwards">Reversed Accent Sorting</key>
			<key type="colCaseFirst">Uppercase/Lowercase Ordering</key>
			<key type="colCaseLevel">Case Sensitive Sorting</key>
			<key type="colHiraganaQuaternary">Kana Sorting</key>
			<key type="collation">Sort Order</key>
			<key type="colNormalization">Normalized Sorting</key>
			<key type="colNumeric">Numeric Sorting</key>
			<key type="colReorder">Script/Block Reordering</key>
			<key type="colStrength">Sorting Strength</key>
			<key type="currency">Currency</key>
			<key type="kv">Highest Ignored</key>
			<key type="numbers">Numbers</key>
			<key type="timezone">Time Zone</key>
			<key type="va">Locale Variant</key>
			<key type="variableTop">Sort As Symbols</key>
			<key type="x">Private-Use</key>
		</keys>
		<types>
			<type type="arab" key="numbers">Arabic-Indic Digits</type>
			<type type="arabext" key="numbers">Extended Arabic-Indic Digits</type>
			<type type="armn" key="numbers">Armenian Numerals</type>
			<type type="armnlow" key="numbers">Armenian Lowercase Numerals</type>
			<type type="bali" key="numbers">Balinese Digits</type>
			<type type="beng" key="numbers">Bengali Digits</type>
			<type type="big5han" key="collation">Traditional Chinese Sort Order - Big5</type>
			<type type="brah" key="numbers">Brahmi Digits</type>
			<type type="buddhist" key="calendar">Buddhist Calendar</type>
			<type type="cakm" key="numbers">Chakma Digits</type>
			<type type="cham" key="numbers">Cham Digits</type>
			<type type="chinese" key="calendar">Chinese Calendar</type>
			<type type="coptic" key="calendar">Coptic Calendar</type>
			<type type="dangi" key="calendar">Dangi Calendar</type>
			<type type="deva" key="numbers">Devanagari Digits</type>
			<type type="dictionary" key="collation">Dictionary Sort Order</type>
			<type type="ducet" key="collation">Default Unicode Sort Order</type>
			<type type="eor" key="collation">European Ordering Rules</type>
			<type type="ethi" key="numbers">Ethiopic Numerals</type>
			<type type="ethiopic" key="calendar">Ethiopic Calendar</type>
			<type type="ethiopic-amete-alem" key="calendar">Ethiopic Amete Alem Calendar</type>
			<type type="finance" key="numbers">Financial Numerals</type>
			<type type="fullwide" key="numbers">Full Width Digits</type>
			<type type="gb2312han" key="collation">Simplified Chinese Sort Order - GB2312</type>
			<type type="geor" key="numbers">Georgian Numerals</type>
			<type type="gregorian" key="calendar">Gregorian Calendar</type>
			<type type="grek" key="numbers">Greek Numerals</type>
			<type type="greklow" key="numbers">Greek Lowercase Numerals</type>
			<type type="gujr" key="numbers">Gujarati Digits</type>
			<type type="guru" key="numbers">Gurmukhi Digits</type>
			<type type="hanidays" key="numbers">Chinese Calendar Day-of-Month Numerals</type>
			<type type="hanidec" key="numbers">Chinese Decimal Numerals</type>
			<type type="hans" key="numbers">Simplified Chinese Numerals</type>
			<type type="hansfin" key="numbers">Simplified Chinese Financial Numerals</type>
			<type type="hant" key="numbers">Traditional Chinese Numerals</type>
			<type type="hantfin" key="numbers">Traditional Chinese Financial Numerals</type>
			<type type="hebr" key="numbers">Hebrew Numerals</type>
			<type type="hebrew" key="calendar">Hebrew Calendar</type>
			<type type="identical" key="colStrength">Sort All</type>
			<type type="indian" key="calendar">Indian National Calendar</type>
			<type type="islamic" key="calendar">Islamic Calendar</type>
			<type type="islamic-civil" key="calendar">Islamic Calendar (tabular, civil epoch)</type>
			<type type="islamic-rgsa" key="calendar">Islamic Calendar (Saudi Arabia, sighting)</type>
			<type type="islamic-tbla" key="calendar">Islamic Calendar (tabular, astronomical epoch)</type>
			<type type="islamic-umalqura" key="calendar">Islamic Calendar (Umm al-Qura)</type>
			<type type="iso8601" key="calendar">ISO-8601 Calendar</type>
			<type type="japanese" key="calendar">Japanese Calendar</type>
			<type type="java" key="numbers">Javanese Digits</type>
			<type type="jpan" key="numbers">Japanese Numerals</type>
			<type type="jpanfin" key="numbers">Japanese Financial Numerals</type>
			<type type="kali" key="numbers">Kayah Li Digits</type>
			<type type="khmr" key="numbers">Khmer Digits</type>
			<type type="knda" key="numbers">Kannada Digits</type>
			<type type="lana" key="numbers">Tai Tham Hora Digits</type>
			<type type="lanatham" key="numbers">Tai Tham Tham Digits</type>
			<type type="laoo" key="numbers">Lao Digits</type>
			<type type="latn" key="numbers">Western Digits</type>
			<type type="lepc" key="numbers">Lepcha Digits</type>
			<type type="limb" key="numbers">Limbu Digits</type>
			<type type="lower" key="colCaseFirst">Sort Lowercase First</type>
			<type type="mlym" key="numbers">Malayalam Digits</type>
			<type type="mong" key="numbers">Mongolian Digits</type>
			<type type="mtei" key="numbers">Meetei Mayek Digits</type>
			<type type="mymr" key="numbers">Myanmar Digits</type>
			<type type="mymrshan" key="numbers">Myanmar Shan Digits</type>
			<type type="native" key="numbers">Native Digits</type>
			<type type="nkoo" key="numbers">N'Ko Digits</type>
			<type type="no" key="colBackwards">Sort Accents Normally</type>
			<type type="no" key="colCaseFirst">Sort Normal Case Order</type>
			<type type="no" key="colCaseLevel">Sort Case Insensitive</type>
			<type type="no" key="colHiraganaQuaternary">Sort Kana Separately</type>
			<type type="no" key="colNormalization">Sort Without Normalization</type>
			<type type="no" key="colNumeric">Sort Digits Individually</type>
			<type type="non-ignorable" key="colAlternate">Sort Symbols</type>
			<type type="olck" key="numbers">Ol Chiki Digits</type>
			<type type="orya" key="numbers">Oriya Digits</type>
			<type type="osma" key="numbers">Osmanya Digits</type>
			<type type="persian" key="calendar">Persian Calendar</type>
			<type type="phonebook" key="collation">Phonebook Sort Order</type>
			<type type="phonetic" key="collation">Phonetic Sort Order</type>
			<type type="pinyin" key="collation">Pinyin Sort Order</type>
			<type type="posix" key="va">POSIX Compliant Locale</type>
			<type type="primary" key="colStrength">Sort Base Letters Only</type>
			<type type="quaternary" key="colStrength">Sort Accents/Case/Width/Kana</type>
			<type type="reformed" key="collation">Reformed Sort Order</type>
			<type type="roc" key="calendar">Minguo Calendar</type>
			<type type="roman" key="numbers">Roman Numerals</type>
			<type type="romanlow" key="numbers">Roman Lowercase Numerals</type>
			<type type="saur" key="numbers">Saurashtra Digits</type>
			<type type="search" key="collation">General-Purpose Search</type>
			<type type="searchjl" key="collation">Search By Hangul Initial Consonant</type>
			<type type="secondary" key="colStrength">Sort Accents</type>
			<type type="shifted" key="colAlternate">Sort Ignoring Symbols</type>
			<type type="shrd" key="numbers">Sharada Digits</type>
			<type type="sora" key="numbers">Sora Sompeng Digits</type>
			<type type="standard" key="collation">Standard Sort Order</type>
			<type type="stroke" key="collation">Stroke Sort Order</type>
			<type type="sund" key="numbers">Sundanese Digits</type>
			<type type="takr" key="numbers">Takri Digits</type>
			<type type="talu" key="numbers">New Tai Lue Digits</type>
			<type type="taml" key="numbers">Traditional Tamil Numerals</type>
			<type type="tamldec" key="numbers">Tamil Digits</type>
			<type type="telu" key="numbers">Telugu Digits</type>
			<type type="tertiary" key="colStrength">Sort Accents/Case/Width</type>
			<type type="thai" key="numbers">Thai Digits</type>
			<type type="tibt" key="numbers">Tibetan Digits</type>
			<type type="traditional" key="collation">Traditional Sort Order</type>
			<type type="traditional" key="numbers">Traditional Numerals</type>
			<type type="unihan" key="collation">Radical-Stroke Sort Order</type>
			<type type="upper" key="colCaseFirst">Sort Uppercase First</type>
			<type type="vaii" key="numbers">Vai Digits</type>
			<type type="yes" key="colBackwards">Sort Accents Reversed</type>
			<type type="yes" key="colCaseLevel">Sort Case Sensitive</type>
			<type type="yes" key="colHiraganaQuaternary">Sort Kana Differently</type>
			<type type="yes" key="colNormalization">Sort Unicode Normalized</type>
			<type type="yes" key="colNumeric">Sort Digits Numerically</type>
			<type type="zhuyin" key="collation">Zhuyin Sort Order</type>
		</types>
		<transformNames>
			<transformName type="BGN">BGN</transformName>
			<transformName type="Numeric">Numeric</transformName>
			<transformName type="Tone">Tone</transformName>
			<transformName type="UNGEGN">UNGEGN</transformName>
			<transformName type="x-Accents">Accents</transformName>
			<transformName type="x-Fullwidth">Fullwidth</transformName>
			<transformName type="x-Halfwidth">Halfwidth</transformName>
			<transformName type="x-Jamo">Jamo</transformName>
			<transformName type="x-Pinyin">Pinyin</transformName>
			<transformName type="x-Publishing">Publishing</transformName>
		</transformNames>
		<measurementSystemNames>
			<measurementSystemName type="metric">Metric</measurementSystemName>
			<measurementSystemName type="UK">UK</measurementSystemName>
			<measurementSystemName type="US">US</measurementSystemName>
		</measurementSystemNames>
		<codePatterns>
			<codePattern type="language">Language: {0}</codePattern>
			<codePattern type="script">Script: {0}</codePattern>
			<codePattern type="territory">Region: {0}</codePattern>
		</codePatterns>
	</localeDisplayNames>
	<contextTransforms>
		<contextTransformUsage type="calendar-field">
			<contextTransform type="stand-alone">titlecase-firstword</contextTransform>
			<contextTransform type="uiListOrMenu">titlecase-firstword</contextTransform>
		</contextTransformUsage>
		<contextTransformUsage type="keyValue">
			<contextTransform type="stand-alone">titlecase-firstword</contextTransform>
			<contextTransform type="uiListOrMenu">titlecase-firstword</contextTransform>
		</contextTransformUsage>
		<contextTransformUsage type="number-spellout">
			<contextTransform type="stand-alone">titlecase-firstword</contextTransform>
			<contextTransform type="uiListOrMenu">titlecase-firstword</contextTransform>
		</contextTransformUsage>
		<contextTransformUsage type="relative">
			<contextTransform type="stand-alone">titlecase-firstword</contextTransform>
			<contextTransform type="uiListOrMenu">titlecase-firstword</contextTransform>
		</contextTransformUsage>
	</contextTransforms>
	<characters>
		<exemplarCharacters>[a b c d e f g h i j k l m n o p q r s t u v w x y z]</exemplarCharacters>
		<exemplarCharacters type="auxiliary">[á à ă â å ä ã ā æ ç é è ĕ ê ë ē í ì ĭ î ï ī ñ ó ò ŏ ô ö ø ō œ ú ù ŭ û ü ū ÿ]</exemplarCharacters>
		<exemplarCharacters type="index">[A B C D E F G H I J K L M N O P Q R S T U V W X Y Z]</exemplarCharacters>
		<exemplarCharacters type="punctuation">[\- ‐ – — , ; \: ! ? . … ' ‘ ’ &quot; “ ” ( ) \[ \] § @ * / \&amp; # † ‡ ′ ″]</exemplarCharacters>
	</characters>
	<delimiters>
		<quotationStart>“</quotationStart>
		<quotationEnd>”</quotationEnd>
		<alternateQuotationStart>‘</alternateQuotationStart>
		<alternateQuotationEnd>’</alternateQuotationEnd>
	</delimiters>
	<dates>
		<calendars>
			<calendar type="buddhist">
				<eras>
					<eraAbbr>
						<era type="0">BE</era>
					</eraAbbr>
				</eras>
			</calendar>
			<calendar type="chinese">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Mo1</month>
							<month type="2">Mo2</month>
							<month type="3">Mo3</month>
							<month type="4">Mo4</month>
							<month type="5">Mo5</month>
							<month type="6">Mo6</month>
							<month type="7">Mo7</month>
							<month type="8">Mo8</month>
							<month type="9">Mo9</month>
							<month type="10">Mo10</month>
							<month type="11">Mo11</month>
							<month type="12">Mo12</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">Month1</month>
							<month type="2">Month2</month>
							<month type="3">Month3</month>
							<month type="4">Month4</month>
							<month type="5">Month5</month>
							<month type="6">Month6</month>
							<month type="7">Month7</month>
							<month type="8">Month8</month>
							<month type="9">Month9</month>
							<month type="10">Month10</month>
							<month type="11">Month11</month>
							<month type="12">Month12</month>
						</monthWidth>
					</monthContext>
				</months>
				<cyclicNameSets>
					<cyclicNameSet type="zodiacs">
						<cyclicNameContext type="format">
							<cyclicNameWidth type="abbreviated">
								<cyclicName type="1">Rat</cyclicName>
								<cyclicName type="2">Ox</cyclicName>
								<cyclicName type="3">Tiger</cyclicName>
								<cyclicName type="4">Rabbit</cyclicName>
								<cyclicName type="5">Dragon</cyclicName>
								<cyclicName type="6">Snake</cyclicName>
								<cyclicName type="7">Horse</cyclicName>
								<cyclicName type="8">Goat</cyclicName>
								<cyclicName type="9">Monkey</cyclicName>
								<cyclicName type="10">Rooster</cyclicName>
								<cyclicName type="11">Dog</cyclicName>
								<cyclicName type="12">Pig</cyclicName>
							</cyclicNameWidth>
						</cyclicNameContext>
					</cyclicNameSet>
				</cyclicNameSets>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, MMMM d, U</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>MMMM d, U</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>MMM d, U</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>M/d/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} 'at' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} 'at' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">d E</dateFormatItem>
						<dateFormatItem id="Ehm">E h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">U</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM U</dateFormatItem>
						<dateFormatItem id="GyMMMd">MMM d, U</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, MMM d, U</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">M/d</dateFormatItem>
						<dateFormatItem id="MEd">E, M/d</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">E, MMM d</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">U</dateFormatItem>
						<dateFormatItem id="yMd">M/d/y</dateFormatItem>
						<dateFormatItem id="yyyy">U</dateFormatItem>
						<dateFormatItem id="yyyyM">M/y</dateFormatItem>
						<dateFormatItem id="yyyyMd">M/d/y</dateFormatItem>
						<dateFormatItem id="yyyyMEd">E, M/d/y</dateFormatItem>
						<dateFormatItem id="yyyyMMM">MMM U</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">MMM d, U</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">E, MMM d, U</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">QQQ U</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">QQQQ U</dateFormatItem>
					</availableFormats>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d – d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h – h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH – HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm – h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm – h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm – HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm – HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm – h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm – h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm – HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm – HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h – h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH – HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M – M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">M/d – M/d</greatestDifference>
							<greatestDifference id="M">M/d – M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, M/d – E, M/d</greatestDifference>
							<greatestDifference id="M">E, M/d – E, M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM – MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">MMM d – d</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, MMM d – E, MMM d</greatestDifference>
							<greatestDifference id="M">E, MMM d – E, MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">U – U</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y</greatestDifference>
							<greatestDifference id="y">M/y – M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">M/d/y – M/d/y</greatestDifference>
							<greatestDifference id="M">M/d/y – M/d/y</greatestDifference>
							<greatestDifference id="y">M/d/y – M/d/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, M/d/y – E, M/d/y</greatestDifference>
							<greatestDifference id="M">E, M/d/y – E, M/d/y</greatestDifference>
							<greatestDifference id="y">E, M/d/y – E, M/d/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM – MMM U</greatestDifference>
							<greatestDifference id="y">MMM U – MMM U</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">MMM d – d, U</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d, U</greatestDifference>
							<greatestDifference id="y">MMM d, U – MMM d, U</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, MMM d – E, MMM d, U</greatestDifference>
							<greatestDifference id="M">E, MMM d – E, MMM d, U</greatestDifference>
							<greatestDifference id="y">E, MMM d, U – E, MMM d, U</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM – MMMM U</greatestDifference>
							<greatestDifference id="y">MMMM U – MMMM U</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="generic">
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, MMMM d, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>MMMM d, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>MMM d, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>M/d/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} 'at' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} 'at' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">d E</dateFormatItem>
						<dateFormatItem id="Ehm">E h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">MMM d, y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, MMM d, y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">M/d</dateFormatItem>
						<dateFormatItem id="MEd">E, M/d</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">E, MMM d</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y G</dateFormatItem>
						<dateFormatItem id="yyyy">y G</dateFormatItem>
						<dateFormatItem id="yyyyM">M/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMd">M/d/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMEd">E, M/d/y GGGGG</dateFormatItem>
						<dateFormatItem id="yyyyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMd">MMM d, y G</dateFormatItem>
						<dateFormatItem id="yyyyMMMEd">E, MMM d, y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQ">QQQ y G</dateFormatItem>
						<dateFormatItem id="yyyyQQQQ">QQQQ y G</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Day">{0} ({2}: {1})</appendItem>
						<appendItem request="Day-Of-Week">{0} {1}</appendItem>
						<appendItem request="Era">{0} {1}</appendItem>
						<appendItem request="Hour">{0} ({2}: {1})</appendItem>
						<appendItem request="Minute">{0} ({2}: {1})</appendItem>
						<appendItem request="Month">{0} ({2}: {1})</appendItem>
						<appendItem request="Quarter">{0} ({2}: {1})</appendItem>
						<appendItem request="Second">{0} ({2}: {1})</appendItem>
						<appendItem request="Timezone">{0} {1}</appendItem>
						<appendItem request="Week">{0} ({2}: {1})</appendItem>
						<appendItem request="Year">{0} {1}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d – d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h – h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH – HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm – h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm – h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm – HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm – HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm – h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm – h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm – HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm – HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h – h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH – HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M – M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">M/d – M/d</greatestDifference>
							<greatestDifference id="M">M/d – M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, M/d – E, M/d</greatestDifference>
							<greatestDifference id="M">E, M/d – E, M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM – MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">MMM d – d</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, MMM d – E, MMM d</greatestDifference>
							<greatestDifference id="M">E, MMM d – E, MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y – y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y GGGGG</greatestDifference>
							<greatestDifference id="y">M/y – M/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">M/d/y – M/d/y GGGGG</greatestDifference>
							<greatestDifference id="M">M/d/y – M/d/y GGGGG</greatestDifference>
							<greatestDifference id="y">M/d/y – M/d/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, M/d/y – E, M/d/y GGGGG</greatestDifference>
							<greatestDifference id="M">E, M/d/y – E, M/d/y GGGGG</greatestDifference>
							<greatestDifference id="y">E, M/d/y – E, M/d/y GGGGG</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM – MMM y G</greatestDifference>
							<greatestDifference id="y">MMM y – MMM y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">MMM d – d, y G</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d, y G</greatestDifference>
							<greatestDifference id="y">MMM d, y – MMM d, y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, MMM d – E, MMM d, y G</greatestDifference>
							<greatestDifference id="M">E, MMM d – E, MMM d, y G</greatestDifference>
							<greatestDifference id="y">E, MMM d, y – E, MMM d, y G</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM – MMMM y G</greatestDifference>
							<greatestDifference id="y">MMMM y – MMMM y G</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="gregorian">
				<months>
					<monthContext type="format">
						<monthWidth type="abbreviated">
							<month type="1">Jan</month>
							<month type="2">Feb</month>
							<month type="3">Mar</month>
							<month type="4">Apr</month>
							<month type="5">May</month>
							<month type="6">Jun</month>
							<month type="7">Jul</month>
							<month type="8">Aug</month>
							<month type="9">Sep</month>
							<month type="10">Oct</month>
							<month type="11">Nov</month>
							<month type="12">Dec</month>
						</monthWidth>
						<monthWidth type="wide">
							<month type="1">January</month>
							<month type="2">February</month>
							<month type="3">March</month>
							<month type="4">April</month>
							<month type="5">May</month>
							<month type="6">June</month>
							<month type="7">July</month>
							<month type="8">August</month>
							<month type="9">September</month>
							<month type="10">October</month>
							<month type="11">November</month>
							<month type="12">December</month>
						</monthWidth>
					</monthContext>
					<monthContext type="stand-alone">
						<monthWidth type="narrow">
							<month type="1">J</month>
							<month type="2">F</month>
							<month type="3">M</month>
							<month type="4">A</month>
							<month type="5">M</month>
							<month type="6">J</month>
							<month type="7">J</month>
							<month type="8">A</month>
							<month type="9">S</month>
							<month type="10">O</month>
							<month type="11">N</month>
							<month type="12">D</month>
						</monthWidth>
					</monthContext>
				</months>
				<days>
					<dayContext type="format">
						<dayWidth type="abbreviated">
							<day type="sun">Sun</day>
							<day type="mon">Mon</day>
							<day type="tue">Tue</day>
							<day type="wed">Wed</day>
							<day type="thu">Thu</day>
							<day type="fri">Fri</day>
							<day type="sat">Sat</day>
						</dayWidth>
						<dayWidth type="short">
							<day type="sun">Su</day>
							<day type="mon">Mo</day>
							<day type="tue">Tu</day>
							<day type="wed">We</day>
							<day type="thu">Th</day>
							<day type="fri">Fr</day>
							<day type="sat">Sa</day>
						</dayWidth>
						<dayWidth type="wide">
							<day type="sun">Sunday</day>
							<day type="mon">Monday</day>
							<day type="tue">Tuesday</day>
							<day type="wed">Wednesday</day>
							<day type="thu">Thursday</day>
							<day type="fri">Friday</day>
							<day type="sat">Saturday</day>
						</dayWidth>
					</dayContext>
					<dayContext type="stand-alone">
						<dayWidth type="narrow">
							<day type="sun">S</day>
							<day type="mon">M</day>
							<day type="tue">T</day>
							<day type="wed">W</day>
							<day type="thu">T</day>
							<day type="fri">F</day>
							<day type="sat">S</day>
						</dayWidth>
					</dayContext>
				</days>
				<quarters>
					<quarterContext type="format">
						<quarterWidth type="abbreviated">
							<quarter type="1">Q1</quarter>
							<quarter type="2">Q2</quarter>
							<quarter type="3">Q3</quarter>
							<quarter type="4">Q4</quarter>
						</quarterWidth>
						<quarterWidth type="wide">
							<quarter type="1">1st quarter</quarter>
							<quarter type="2">2nd quarter</quarter>
							<quarter type="3">3rd quarter</quarter>
							<quarter type="4">4th quarter</quarter>
						</quarterWidth>
					</quarterContext>
					<quarterContext type="stand-alone">
						<quarterWidth type="narrow">
							<quarter type="1">1</quarter>
							<quarter type="2">2</quarter>
							<quarter type="3">3</quarter>
							<quarter type="4">4</quarter>
						</quarterWidth>
					</quarterContext>
				</quarters>
				<dayPeriods>
					<dayPeriodContext type="format">
						<dayPeriodWidth type="narrow">
							<dayPeriod type="am">a</dayPeriod>
							<dayPeriod type="noon">n</dayPeriod>
							<dayPeriod type="pm">p</dayPeriod>
						</dayPeriodWidth>
						<dayPeriodWidth type="wide">
							<dayPeriod type="am">AM</dayPeriod>
							<dayPeriod type="am" alt="variant">am</dayPeriod>
							<dayPeriod type="noon">noon</dayPeriod>
							<dayPeriod type="pm">PM</dayPeriod>
							<dayPeriod type="pm" alt="variant">pm</dayPeriod>
						</dayPeriodWidth>
					</dayPeriodContext>
				</dayPeriods>
				<eras>
					<eraNames>
						<era type="0">Before Christ</era>
						<era type="0" alt="variant">Before Common Era</era>
						<era type="1">Anno Domini</era>
						<era type="1" alt="variant">Common Era</era>
					</eraNames>
					<eraAbbr>
						<era type="0">BC</era>
						<era type="0" alt="variant">BCE</era>
						<era type="1">AD</era>
						<era type="1" alt="variant">CE</era>
					</eraAbbr>
					<eraNarrow>
						<era type="0">B</era>
						<era type="1">A</era>
					</eraNarrow>
				</eras>
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, MMMM d, y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>MMMM d, y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>MMM d, y</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>M/d/yy</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
				<timeFormats>
					<timeFormatLength type="full">
						<timeFormat>
							<pattern>h:mm:ss a zzzz</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="long">
						<timeFormat>
							<pattern>h:mm:ss a z</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="medium">
						<timeFormat>
							<pattern>h:mm:ss a</pattern>
						</timeFormat>
					</timeFormatLength>
					<timeFormatLength type="short">
						<timeFormat>
							<pattern>h:mm a</pattern>
						</timeFormat>
					</timeFormatLength>
				</timeFormats>
				<dateTimeFormats>
					<dateTimeFormatLength type="full">
						<dateTimeFormat>
							<pattern>{1} 'at' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="long">
						<dateTimeFormat>
							<pattern>{1} 'at' {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="medium">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<dateTimeFormatLength type="short">
						<dateTimeFormat>
							<pattern>{1}, {0}</pattern>
						</dateTimeFormat>
					</dateTimeFormatLength>
					<availableFormats>
						<dateFormatItem id="d">d</dateFormatItem>
						<dateFormatItem id="Ed">d E</dateFormatItem>
						<dateFormatItem id="Ehm">E h:mm a</dateFormatItem>
						<dateFormatItem id="EHm">E HH:mm</dateFormatItem>
						<dateFormatItem id="Ehms">E h:mm:ss a</dateFormatItem>
						<dateFormatItem id="EHms">E HH:mm:ss</dateFormatItem>
						<dateFormatItem id="Gy">y G</dateFormatItem>
						<dateFormatItem id="GyMMM">MMM y G</dateFormatItem>
						<dateFormatItem id="GyMMMd">MMM d, y G</dateFormatItem>
						<dateFormatItem id="GyMMMEd">E, MMM d, y G</dateFormatItem>
						<dateFormatItem id="h">h a</dateFormatItem>
						<dateFormatItem id="H">HH</dateFormatItem>
						<dateFormatItem id="hm">h:mm a</dateFormatItem>
						<dateFormatItem id="Hm">HH:mm</dateFormatItem>
						<dateFormatItem id="hms">h:mm:ss a</dateFormatItem>
						<dateFormatItem id="Hms">HH:mm:ss</dateFormatItem>
						<dateFormatItem id="M">L</dateFormatItem>
						<dateFormatItem id="Md">M/d</dateFormatItem>
						<dateFormatItem id="MEd">E, M/d</dateFormatItem>
						<dateFormatItem id="MMM">LLL</dateFormatItem>
						<dateFormatItem id="MMMd">MMM d</dateFormatItem>
						<dateFormatItem id="MMMEd">E, MMM d</dateFormatItem>
						<dateFormatItem id="ms">mm:ss</dateFormatItem>
						<dateFormatItem id="y">y</dateFormatItem>
						<dateFormatItem id="yM">M/y</dateFormatItem>
						<dateFormatItem id="yMd">M/d/y</dateFormatItem>
						<dateFormatItem id="yMEd">E, M/d/y</dateFormatItem>
						<dateFormatItem id="yMMM">MMM y</dateFormatItem>
						<dateFormatItem id="yMMMd">MMM d, y</dateFormatItem>
						<dateFormatItem id="yMMMEd">E, MMM d, y</dateFormatItem>
						<dateFormatItem id="yQQQ">QQQ y</dateFormatItem>
						<dateFormatItem id="yQQQQ">QQQQ y</dateFormatItem>
					</availableFormats>
					<appendItems>
						<appendItem request="Day">{0} ({2}: {1})</appendItem>
						<appendItem request="Day-Of-Week">{0} {1}</appendItem>
						<appendItem request="Era">{0} {1}</appendItem>
						<appendItem request="Hour">{0} ({2}: {1})</appendItem>
						<appendItem request="Minute">{0} ({2}: {1})</appendItem>
						<appendItem request="Month">{0} ({2}: {1})</appendItem>
						<appendItem request="Quarter">{0} ({2}: {1})</appendItem>
						<appendItem request="Second">{0} ({2}: {1})</appendItem>
						<appendItem request="Timezone">{0} {1}</appendItem>
						<appendItem request="Week">{0} ({2}: {1})</appendItem>
						<appendItem request="Year">{0} {1}</appendItem>
					</appendItems>
					<intervalFormats>
						<intervalFormatFallback>{0} – {1}</intervalFormatFallback>
						<intervalFormatItem id="d">
							<greatestDifference id="d">d – d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="h">
							<greatestDifference id="a">h a – h a</greatestDifference>
							<greatestDifference id="h">h – h a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="H">
							<greatestDifference id="H">HH – HH</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hm">
							<greatestDifference id="a">h:mm a – h:mm a</greatestDifference>
							<greatestDifference id="h">h:mm – h:mm a</greatestDifference>
							<greatestDifference id="m">h:mm – h:mm a</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hm">
							<greatestDifference id="H">HH:mm – HH:mm</greatestDifference>
							<greatestDifference id="m">HH:mm – HH:mm</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hmv">
							<greatestDifference id="a">h:mm a – h:mm a v</greatestDifference>
							<greatestDifference id="h">h:mm – h:mm a v</greatestDifference>
							<greatestDifference id="m">h:mm – h:mm a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hmv">
							<greatestDifference id="H">HH:mm – HH:mm v</greatestDifference>
							<greatestDifference id="m">HH:mm – HH:mm v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="hv">
							<greatestDifference id="a">h a – h a v</greatestDifference>
							<greatestDifference id="h">h – h a v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Hv">
							<greatestDifference id="H">HH – HH v</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="M">
							<greatestDifference id="M">M – M</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="Md">
							<greatestDifference id="d">M/d – M/d</greatestDifference>
							<greatestDifference id="M">M/d – M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MEd">
							<greatestDifference id="d">E, M/d – E, M/d</greatestDifference>
							<greatestDifference id="M">E, M/d – E, M/d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMM">
							<greatestDifference id="M">MMM – MMM</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMd">
							<greatestDifference id="d">MMM d – d</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="MMMEd">
							<greatestDifference id="d">E, MMM d – E, MMM d</greatestDifference>
							<greatestDifference id="M">E, MMM d – E, MMM d</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="y">
							<greatestDifference id="y">y – y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yM">
							<greatestDifference id="M">M/y – M/y</greatestDifference>
							<greatestDifference id="y">M/y – M/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMd">
							<greatestDifference id="d">M/d/y – M/d/y</greatestDifference>
							<greatestDifference id="M">M/d/y – M/d/y</greatestDifference>
							<greatestDifference id="y">M/d/y – M/d/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMEd">
							<greatestDifference id="d">E, M/d/y – E, M/d/y</greatestDifference>
							<greatestDifference id="M">E, M/d/y – E, M/d/y</greatestDifference>
							<greatestDifference id="y">E, M/d/y – E, M/d/y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMM">
							<greatestDifference id="M">MMM – MMM y</greatestDifference>
							<greatestDifference id="y">MMM y – MMM y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMd">
							<greatestDifference id="d">MMM d – d, y</greatestDifference>
							<greatestDifference id="M">MMM d – MMM d, y</greatestDifference>
							<greatestDifference id="y">MMM d, y – MMM d, y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMEd">
							<greatestDifference id="d">E, MMM d – E, MMM d, y</greatestDifference>
							<greatestDifference id="M">E, MMM d – E, MMM d, y</greatestDifference>
							<greatestDifference id="y">E, MMM d, y – E, MMM d, y</greatestDifference>
						</intervalFormatItem>
						<intervalFormatItem id="yMMMM">
							<greatestDifference id="M">MMMM – MMMM y</greatestDifference>
							<greatestDifference id="y">MMMM y – MMMM y</greatestDifference>
						</intervalFormatItem>
					</intervalFormats>
				</dateTimeFormats>
			</calendar>
			<calendar type="hebrew">
				<eras>
					<eraAbbr>
						<era type="0">AM</era>
					</eraAbbr>
				</eras>
			</calendar>
			<calendar type="islamic">
				<eras>
					<eraAbbr>
						<era type="0">AH</era>
					</eraAbbr>
				</eras>
			</calendar>
			<calendar type="japanese">
				<!-- need some non-empty element here to make inheritance work,
					 this copy of generic dateFormats above is smaller than root japanese eras
				-->
				<dateFormats>
					<dateFormatLength type="full">
						<dateFormat>
							<pattern>EEEE, MMMM d, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="long">
						<dateFormat>
							<pattern>MMMM d, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="medium">
						<dateFormat>
							<pattern>MMM d, y G</pattern>
						</dateFormat>
					</dateFormatLength>
					<dateFormatLength type="short">
						<dateFormat>
							<pattern>M/d/y GGGGG</pattern>
						</dateFormat>
					</dateFormatLength>
				</dateFormats>
			</calendar>
			<calendar type="roc">
				<eras>
					<eraAbbr>
						<era type="0">Before R.O.C.</era>
						<era type="1">Minguo</era>
					</eraAbbr>
				</eras>
			</calendar>
		</calendars>
		<fields>
			<field type="era">
				<displayName>Era</displayName>
			</field>
			<field type="year">
				<displayName>Year</displayName>
				<relative type="-1">last year</relative>
				<relative type="0">this year</relative>
				<relative type="1">next year</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">in {0} year</relativeTimePattern>
					<relativeTimePattern count="other">in {0} years</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} year ago</relativeTimePattern>
					<relativeTimePattern count="other">{0} years ago</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="month">
				<displayName>Month</displayName>
				<relative type="-1">last month</relative>
				<relative type="0">this month</relative>
				<relative type="1">next month</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">in {0} month</relativeTimePattern>
					<relativeTimePattern count="other">in {0} months</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} month ago</relativeTimePattern>
					<relativeTimePattern count="other">{0} months ago</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="week">
				<displayName>Week</displayName>
				<relative type="-1">last week</relative>
				<relative type="0">this week</relative>
				<relative type="1">next week</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">in {0} week</relativeTimePattern>
					<relativeTimePattern count="other">in {0} weeks</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} week ago</relativeTimePattern>
					<relativeTimePattern count="other">{0} weeks ago</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="day">
				<displayName>Day</displayName>
				<relative type="-1">yesterday</relative>
				<relative type="0">today</relative>
				<relative type="1">tomorrow</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">in {0} day</relativeTimePattern>
					<relativeTimePattern count="other">in {0} days</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} day ago</relativeTimePattern>
					<relativeTimePattern count="other">{0} days ago</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="weekday">
				<displayName>Day of the Week</displayName>
			</field>
			<field type="sun">
				<relative type="-1">last Sunday</relative>
				<relative type="0">this Sunday</relative>
				<relative type="1">next Sunday</relative>
			</field>
			<field type="mon">
				<relative type="-1">last Monday</relative>
				<relative type="0">this Monday</relative>
				<relative type="1">next Monday</relative>
			</field>
			<field type="tue">
				<relative type="-1">last Tuesday</relative>
				<relative type="0">this Tuesday</relative>
				<relative type="1">next Tuesday</relative>
			</field>
			<field type="wed">
				<relative type="-1">last Wednesday</relative>
				<relative type="0">this Wednesday</relative>
				<relative type="1">next Wednesday</relative>
			</field>
			<field type="thu">
				<relative type="-1">last Thursday</relative>
				<relative type="0">this Thursday</relative>
				<relative type="1">next Thursday</relative>
			</field>
			<field type="fri">
				<relative type="-1">last Friday</relative>
				<relative type="0">this Friday</relative>
				<relative type="1">next Friday</relative>
			</field>
			<field type="sat">
				<relative type="-1">last Saturday</relative>
				<relative type="0">this Saturday</relative>
				<relative type="1">next Saturday</relative>
			</field>
			<field type="dayperiod">
				<displayName>AM/PM</displayName>
			</field>
			<field type="hour">
				<displayName>Hour</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">in {0} hour</relativeTimePattern>
					<relativeTimePattern count="other">in {0} hours</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} hour ago</relativeTimePattern>
					<relativeTimePattern count="other">{0} hours ago</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="minute">
				<displayName>Minute</displayName>
				<relativeTime type="future">
					<relativeTimePattern count="one">in {0} minute</relativeTimePattern>
					<relativeTimePattern count="other">in {0} minutes</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} minute ago</relativeTimePattern>
					<relativeTimePattern count="other">{0} minutes ago</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="second">
				<displayName>Second</displayName>
				<relative type="0">now</relative>
				<relativeTime type="future">
					<relativeTimePattern count="one">in {0} second</relativeTimePattern>
					<relativeTimePattern count="other">in {0} seconds</relativeTimePattern>
				</relativeTime>
				<relativeTime type="past">
					<relativeTimePattern count="one">{0} second ago</relativeTimePattern>
					<relativeTimePattern count="other">{0} seconds ago</relativeTimePattern>
				</relativeTime>
			</field>
			<field type="zone">
				<displayName>Time Zone</displayName>
			</field>
		</fields>
		<timeZoneNames>
			<hourFormat>+HH:mm;-HH:mm</hourFormat>
			<gmtFormat>GMT{0}</gmtFormat>
			<regionFormat>{0} Time</regionFormat>
			<regionFormat type="daylight">{0} Daylight Time</regionFormat>
			<regionFormat type="standard">{0} Standard Time</regionFormat>
			<fallbackFormat>{1} ({0})</fallbackFormat>
			<zone type="Etc/Unknown">
				<exemplarCity>Unknown City</exemplarCity>
			</zone>
			<zone type="Antarctica/DumontDUrville">
				<exemplarCity>Dumont d’Urville</exemplarCity>
			</zone>
			<zone type="America/St_Barthelemy">
				<exemplarCity>Saint Barthélemy</exemplarCity>
			</zone>
			<zone type="America/Curacao">
				<exemplarCity>Curaçao</exemplarCity>
			</zone>
			<zone type="Europe/London">
				<long>
					<daylight>British Summer Time</daylight>
				</long>
			</zone>
			<zone type="Europe/Dublin">
				<long>
					<daylight>Irish Summer Time</daylight>
				</long>
			</zone>
			<zone type="America/Asuncion">
				<exemplarCity>Asunción</exemplarCity>
			</zone>
			<zone type="Indian/Reunion">
				<exemplarCity>Réunion</exemplarCity>
			</zone>
			<zone type="Africa/Sao_Tome">
				<exemplarCity>São Tomé</exemplarCity>
			</zone>
			<zone type="Pacific/Honolulu">
				<short>
					<generic>HST</generic>
					<standard>HST</standard>
					<daylight>HDT</daylight>
				</short>
			</zone>
			<zone type="Asia/Saigon">
				<exemplarCity>Ho Chi Minh City</exemplarCity>
			</zone>
			<metazone type="Acre">
				<long>
					<generic>Acre Time</generic>
					<standard>Acre Standard Time</standard>
					<daylight>Acre Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Afghanistan">
				<long>
					<standard>Afghanistan Time</standard>
				</long>
			</metazone>
			<metazone type="Africa_Central">
				<long>
					<standard>Central Africa Time</standard>
				</long>
			</metazone>
			<metazone type="Africa_Eastern">
				<long>
					<standard>East Africa Time</standard>
				</long>
			</metazone>
			<metazone type="Africa_Southern">
				<long>
					<standard>South Africa Standard Time</standard>
				</long>
			</metazone>
			<metazone type="Africa_Western">
				<long>
					<generic>West Africa Time</generic>
					<standard>West Africa Standard Time</standard>
					<daylight>West Africa Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Alaska">
				<long>
					<generic>Alaska Time</generic>
					<standard>Alaska Standard Time</standard>
					<daylight>Alaska Daylight Time</daylight>
				</long>
				<short>
					<generic>AKT</generic>
					<standard>AKST</standard>
					<daylight>AKDT</daylight>
				</short>
			</metazone>
			<metazone type="Almaty">
				<long>
					<generic>Almaty Time</generic>
					<standard>Almaty Standard Time</standard>
					<daylight>Almaty Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Amazon">
				<long>
					<generic>Amazon Time</generic>
					<standard>Amazon Standard Time</standard>
					<daylight>Amazon Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="America_Central">
				<long>
					<generic>Central Time</generic>
					<standard>Central Standard Time</standard>
					<daylight>Central Daylight Time</daylight>
				</long>
				<short>
					<generic>CT</generic>
					<standard>CST</standard>
					<daylight>CDT</daylight>
				</short>
			</metazone>
			<metazone type="America_Eastern">
				<long>
					<generic>Eastern Time</generic>
					<standard>Eastern Standard Time</standard>
					<daylight>Eastern Daylight Time</daylight>
				</long>
				<short>
					<generic>ET</generic>
					<standard>EST</standard>
					<daylight>EDT</daylight>
				</short>
			</metazone>
			<metazone type="America_Mountain">
				<long>
					<generic>Mountain Time</generic>
					<standard>Mountain Standard Time</standard>
					<daylight>Mountain Daylight Time</daylight>
				</long>
				<short>
					<generic>MT</generic>
					<standard>MST</standard>
					<daylight>MDT</daylight>
				</short>
			</metazone>
			<metazone type="America_Pacific">
				<long>
					<generic>Pacific Time</generic>
					<standard>Pacific Standard Time</standard>
					<daylight>Pacific Daylight Time</daylight>
				</long>
				<short>
					<generic>PT</generic>
					<standard>PST</standard>
					<daylight>PDT</daylight>
				</short>
			</metazone>
			<metazone type="Anadyr">
				<long>
					<generic>Anadyr Time</generic>
					<standard>Anadyr Standard Time</standard>
					<daylight>Anadyr Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Aqtau">
				<long>
					<generic>Aqtau Time</generic>
					<standard>Aqtau Standard Time</standard>
					<daylight>Aqtau Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Aqtobe">
				<long>
					<generic>Aqtobe Time</generic>
					<standard>Aqtobe Standard Time</standard>
					<daylight>Aqtobe Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Arabian">
				<long>
					<generic>Arabian Time</generic>
					<standard>Arabian Standard Time</standard>
					<daylight>Arabian Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Argentina">
				<long>
					<generic>Argentina Time</generic>
					<standard>Argentina Standard Time</standard>
					<daylight>Argentina Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Argentina_Western">
				<long>
					<generic>Western Argentina Time</generic>
					<standard>Western Argentina Standard Time</standard>
					<daylight>Western Argentina Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Armenia">
				<long>
					<generic>Armenia Time</generic>
					<standard>Armenia Standard Time</standard>
					<daylight>Armenia Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Atlantic">
				<long>
					<generic>Atlantic Time</generic>
					<standard>Atlantic Standard Time</standard>
					<daylight>Atlantic Daylight Time</daylight>
				</long>
				<short>
					<generic>AT</generic>
					<standard>AST</standard>
					<daylight>ADT</daylight>
				</short>
			</metazone>
			<metazone type="Australia_Central">
				<long>
					<generic>Central Australia Time</generic>
					<standard>Australian Central Standard Time</standard>
					<daylight>Australian Central Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Australia_CentralWestern">
				<long>
					<generic>Australian Central Western Time</generic>
					<standard>Australian Central Western Standard Time</standard>
					<daylight>Australian Central Western Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Eastern">
				<long>
					<generic>Eastern Australia Time</generic>
					<standard>Australian Eastern Standard Time</standard>
					<daylight>Australian Eastern Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Australia_Western">
				<long>
					<generic>Western Australia Time</generic>
					<standard>Australian Western Standard Time</standard>
					<daylight>Australian Western Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Azerbaijan">
				<long>
					<generic>Azerbaijan Time</generic>
					<standard>Azerbaijan Standard Time</standard>
					<daylight>Azerbaijan Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Azores">
				<long>
					<generic>Azores Time</generic>
					<standard>Azores Standard Time</standard>
					<daylight>Azores Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Bangladesh">
				<long>
					<generic>Bangladesh Time</generic>
					<standard>Bangladesh Standard Time</standard>
					<daylight>Bangladesh Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Bhutan">
				<long>
					<standard>Bhutan Time</standard>
				</long>
			</metazone>
			<metazone type="Bolivia">
				<long>
					<standard>Bolivia Time</standard>
				</long>
			</metazone>
			<metazone type="Brasilia">
				<long>
					<generic>Brasilia Time</generic>
					<standard>Brasilia Standard Time</standard>
					<daylight>Brasilia Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Brunei">
				<long>
					<standard>Brunei Darussalam Time</standard>
				</long>
			</metazone>
			<metazone type="Cape_Verde">
				<long>
					<generic>Cape Verde Time</generic>
					<standard>Cape Verde Standard Time</standard>
					<daylight>Cape Verde Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Casey">
				<long>
					<standard>Casey Time</standard>
				</long>
			</metazone>
			<metazone type="Chamorro">
				<long>
					<standard>Chamorro Standard Time</standard>
				</long>
			</metazone>
			<metazone type="Chatham">
				<long>
					<generic>Chatham Time</generic>
					<standard>Chatham Standard Time</standard>
					<daylight>Chatham Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Chile">
				<long>
					<generic>Chile Time</generic>
					<standard>Chile Standard Time</standard>
					<daylight>Chile Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="China">
				<long>
					<generic>China Time</generic>
					<standard>China Standard Time</standard>
					<daylight>China Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Choibalsan">
				<long>
					<generic>Choibalsan Time</generic>
					<standard>Choibalsan Standard Time</standard>
					<daylight>Choibalsan Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Christmas">
				<long>
					<standard>Christmas Island Time</standard>
				</long>
			</metazone>
			<metazone type="Cocos">
				<long>
					<standard>Cocos Islands Time</standard>
				</long>
			</metazone>
			<metazone type="Colombia">
				<long>
					<generic>Colombia Time</generic>
					<standard>Colombia Standard Time</standard>
					<daylight>Colombia Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Cook">
				<long>
					<generic>Cook Islands Time</generic>
					<standard>Cook Islands Standard Time</standard>
					<daylight>Cook Islands Half Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Cuba">
				<long>
					<generic>Cuba Time</generic>
					<standard>Cuba Standard Time</standard>
					<daylight>Cuba Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Davis">
				<long>
					<standard>Davis Time</standard>
				</long>
			</metazone>
			<metazone type="DumontDUrville">
				<long>
					<standard>Dumont-d’Urville Time</standard>
				</long>
			</metazone>
			<metazone type="East_Timor">
				<long>
					<standard>East Timor Time</standard>
				</long>
			</metazone>
			<metazone type="Easter">
				<long>
					<generic>Easter Island Time</generic>
					<standard>Easter Island Standard Time</standard>
					<daylight>Easter Island Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Ecuador">
				<long>
					<standard>Ecuador Time</standard>
				</long>
			</metazone>
			<metazone type="Europe_Central">
				<long>
					<generic>Central European Time</generic>
					<standard>Central European Standard Time</standard>
					<daylight>Central European Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Europe_Eastern">
				<long>
					<generic>Eastern European Time</generic>
					<standard>Eastern European Standard Time</standard>
					<daylight>Eastern European Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Europe_Western">
				<long>
					<generic>Western European Time</generic>
					<standard>Western European Standard Time</standard>
					<daylight>Western European Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Falkland">
				<long>
					<generic>Falkland Islands Time</generic>
					<standard>Falkland Islands Standard Time</standard>
					<daylight>Falkland Islands Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Fiji">
				<long>
					<generic>Fiji Time</generic>
					<standard>Fiji Standard Time</standard>
					<daylight>Fiji Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="French_Guiana">
				<long>
					<standard>French Guiana Time</standard>
				</long>
			</metazone>
			<metazone type="French_Southern">
				<long>
					<standard>French Southern and Antarctic Time</standard>
				</long>
			</metazone>
			<metazone type="Galapagos">
				<long>
					<standard>Galapagos Time</standard>
				</long>
			</metazone>
			<metazone type="Gambier">
				<long>
					<standard>Gambier Time</standard>
				</long>
			</metazone>
			<metazone type="Georgia">
				<long>
					<generic>Georgia Time</generic>
					<standard>Georgia Standard Time</standard>
					<daylight>Georgia Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Gilbert_Islands">
				<long>
					<standard>Gilbert Islands Time</standard>
				</long>
			</metazone>
			<metazone type="GMT">
				<long>
					<standard>Greenwich Mean Time</standard>
				</long>
				<short>
					<standard>GMT</standard>
				</short>
			</metazone>
			<metazone type="Greenland_Eastern">
				<long>
					<generic>East Greenland Time</generic>
					<standard>East Greenland Standard Time</standard>
					<daylight>East Greenland Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Greenland_Western">
				<long>
					<generic>West Greenland Time</generic>
					<standard>West Greenland Standard Time</standard>
					<daylight>West Greenland Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Guam">
				<long>
					<standard>Guam Standard Time</standard>
				</long>
			</metazone>
			<metazone type="Gulf">
				<long>
					<standard>Gulf Standard Time</standard>
				</long>
			</metazone>
			<metazone type="Guyana">
				<long>
					<standard>Guyana Time</standard>
				</long>
			</metazone>
			<metazone type="Hawaii_Aleutian">
				<long>
					<generic>Hawaii-Aleutian Time</generic>
					<standard>Hawaii-Aleutian Standard Time</standard>
					<daylight>Hawaii-Aleutian Daylight Time</daylight>
				</long>
				<short>
					<generic>HAT</generic>
					<standard>HAST</standard>
					<daylight>HADT</daylight>
				</short>
			</metazone>
			<metazone type="Hong_Kong">
				<long>
					<generic>Hong Kong Time</generic>
					<standard>Hong Kong Standard Time</standard>
					<daylight>Hong Kong Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Hovd">
				<long>
					<generic>Hovd Time</generic>
					<standard>Hovd Standard Time</standard>
					<daylight>Hovd Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="India">
				<long>
					<standard>India Standard Time</standard>
				</long>
			</metazone>
			<metazone type="Indian_Ocean">
				<long>
					<standard>Indian Ocean Time</standard>
				</long>
			</metazone>
			<metazone type="Indochina">
				<long>
					<standard>Indochina Time</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Central">
				<long>
					<standard>Central Indonesia Time</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Eastern">
				<long>
					<standard>Eastern Indonesia Time</standard>
				</long>
			</metazone>
			<metazone type="Indonesia_Western">
				<long>
					<standard>Western Indonesia Time</standard>
				</long>
			</metazone>
			<metazone type="Iran">
				<long>
					<generic>Iran Time</generic>
					<standard>Iran Standard Time</standard>
					<daylight>Iran Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Irkutsk">
				<long>
					<generic>Irkutsk Time</generic>
					<standard>Irkutsk Standard Time</standard>
					<daylight>Irkutsk Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Israel">
				<long>
					<generic>Israel Time</generic>
					<standard>Israel Standard Time</standard>
					<daylight>Israel Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Japan">
				<long>
					<generic>Japan Time</generic>
					<standard>Japan Standard Time</standard>
					<daylight>Japan Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Kamchatka">
				<long>
					<generic>Petropavlovsk-Kamchatski Time</generic>
					<standard>Petropavlovsk-Kamchatski Standard Time</standard>
					<daylight>Petropavlovsk-Kamchatski Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Eastern">
				<long>
					<standard>East Kazakhstan Time</standard>
				</long>
			</metazone>
			<metazone type="Kazakhstan_Western">
				<long>
					<standard>West Kazakhstan Time</standard>
				</long>
			</metazone>
			<metazone type="Korea">
				<long>
					<generic>Korean Time</generic>
					<standard>Korean Standard Time</standard>
					<daylight>Korean Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Kosrae">
				<long>
					<standard>Kosrae Time</standard>
				</long>
			</metazone>
			<metazone type="Krasnoyarsk">
				<long>
					<generic>Krasnoyarsk Time</generic>
					<standard>Krasnoyarsk Standard Time</standard>
					<daylight>Krasnoyarsk Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Kyrgystan">
				<long>
					<standard>Kyrgystan Time</standard>
				</long>
			</metazone>
			<metazone type="Lanka">
				<long>
					<standard>Lanka Time</standard>
				</long>
			</metazone>
			<metazone type="Line_Islands">
				<long>
					<standard>Line Islands Time</standard>
				</long>
			</metazone>
			<metazone type="Lord_Howe">
				<long>
					<generic>Lord Howe Time</generic>
					<standard>Lord Howe Standard Time</standard>
					<daylight>Lord Howe Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Macau">
				<long>
					<generic>Macau Time</generic>
					<standard>Macau Standard Time</standard>
					<daylight>Macau Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Macquarie">
				<long>
					<standard>Macquarie Island Time</standard>
				</long>
			</metazone>
			<metazone type="Magadan">
				<long>
					<generic>Magadan Time</generic>
					<standard>Magadan Standard Time</standard>
					<daylight>Magadan Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Malaysia">
				<long>
					<standard>Malaysia Time</standard>
				</long>
			</metazone>
			<metazone type="Maldives">
				<long>
					<standard>Maldives Time</standard>
				</long>
			</metazone>
			<metazone type="Marquesas">
				<long>
					<standard>Marquesas Time</standard>
				</long>
			</metazone>
			<metazone type="Marshall_Islands">
				<long>
					<standard>Marshall Islands Time</standard>
				</long>
			</metazone>
			<metazone type="Mauritius">
				<long>
					<generic>Mauritius Time</generic>
					<standard>Mauritius Standard Time</standard>
					<daylight>Mauritius Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Mawson">
				<long>
					<standard>Mawson Time</standard>
				</long>
			</metazone>
			<metazone type="Mexico_Northwest">
				<long>
					<generic>Northwest Mexico Time</generic>
					<standard>Northwest Mexico Standard Time</standard>
					<daylight>Northwest Mexico Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Mexico_Pacific">
				<long>
					<generic>Mexican Pacific Time</generic>
					<standard>Mexican Pacific Standard Time</standard>
					<daylight>Mexican Pacific Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Mongolia">
				<long>
					<generic>Ulan Bator Time</generic>
					<standard>Ulan Bator Standard Time</standard>
					<daylight>Ulan Bator Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Moscow">
				<long>
					<generic>Moscow Time</generic>
					<standard>Moscow Standard Time</standard>
					<daylight>Moscow Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Myanmar">
				<long>
					<standard>Myanmar Time</standard>
				</long>
			</metazone>
			<metazone type="Nauru">
				<long>
					<standard>Nauru Time</standard>
				</long>
			</metazone>
			<metazone type="Nepal">
				<long>
					<standard>Nepal Time</standard>
				</long>
			</metazone>
			<metazone type="New_Caledonia">
				<long>
					<generic>New Caledonia Time</generic>
					<standard>New Caledonia Standard Time</standard>
					<daylight>New Caledonia Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="New_Zealand">
				<long>
					<generic>New Zealand Time</generic>
					<standard>New Zealand Standard Time</standard>
					<daylight>New Zealand Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Newfoundland">
				<long>
					<generic>Newfoundland Time</generic>
					<standard>Newfoundland Standard Time</standard>
					<daylight>Newfoundland Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Niue">
				<long>
					<standard>Niue Time</standard>
				</long>
			</metazone>
			<metazone type="Norfolk">
				<long>
					<standard>Norfolk Island Time</standard>
				</long>
			</metazone>
			<metazone type="Noronha">
				<long>
					<generic>Fernando de Noronha Time</generic>
					<standard>Fernando de Noronha Standard Time</standard>
					<daylight>Fernando de Noronha Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="North_Mariana">
				<long>
					<standard>North Mariana Islands Time</standard>
				</long>
			</metazone>
			<metazone type="Novosibirsk">
				<long>
					<generic>Novosibirsk Time</generic>
					<standard>Novosibirsk Standard Time</standard>
					<daylight>Novosibirsk Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Omsk">
				<long>
					<generic>Omsk Time</generic>
					<standard>Omsk Standard Time</standard>
					<daylight>Omsk Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Pakistan">
				<long>
					<generic>Pakistan Time</generic>
					<standard>Pakistan Standard Time</standard>
					<daylight>Pakistan Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Palau">
				<long>
					<standard>Palau Time</standard>
				</long>
			</metazone>
			<metazone type="Papua_New_Guinea">
				<long>
					<standard>Papua New Guinea Time</standard>
				</long>
			</metazone>
			<metazone type="Paraguay">
				<long>
					<generic>Paraguay Time</generic>
					<standard>Paraguay Standard Time</standard>
					<daylight>Paraguay Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Peru">
				<long>
					<generic>Peru Time</generic>
					<standard>Peru Standard Time</standard>
					<daylight>Peru Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Philippines">
				<long>
					<generic>Philippine Time</generic>
					<standard>Philippine Standard Time</standard>
					<daylight>Philippine Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Phoenix_Islands">
				<long>
					<standard>Phoenix Islands Time</standard>
				</long>
			</metazone>
			<metazone type="Pierre_Miquelon">
				<long>
					<generic>Saint Pierre and Miquelon Time</generic>
					<standard>Saint Pierre and Miquelon Standard Time</standard>
					<daylight>Saint Pierre and Miquelon Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Pitcairn">
				<long>
					<standard>Pitcairn Time</standard>
				</long>
			</metazone>
			<metazone type="Ponape">
				<long>
					<standard>Ponape Time</standard>
				</long>
			</metazone>
			<metazone type="Qyzylorda">
				<long>
					<generic>Qyzylorda Time</generic>
					<standard>Qyzylorda Standard Time</standard>
					<daylight>Qyzylorda Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Reunion">
				<long>
					<standard>Reunion Time</standard>
				</long>
			</metazone>
			<metazone type="Rothera">
				<long>
					<standard>Rothera Time</standard>
				</long>
			</metazone>
			<metazone type="Sakhalin">
				<long>
					<generic>Sakhalin Time</generic>
					<standard>Sakhalin Standard Time</standard>
					<daylight>Sakhalin Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Samara">
				<long>
					<generic>Samara Time</generic>
					<standard>Samara Standard Time</standard>
					<daylight>Samara Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Samoa">
				<long>
					<generic>Samoa Time</generic>
					<standard>Samoa Standard Time</standard>
					<daylight>Samoa Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Seychelles">
				<long>
					<standard>Seychelles Time</standard>
				</long>
			</metazone>
			<metazone type="Singapore">
				<long>
					<standard>Singapore Standard Time</standard>
				</long>
			</metazone>
			<metazone type="Solomon">
				<long>
					<standard>Solomon Islands Time</standard>
				</long>
			</metazone>
			<metazone type="South_Georgia">
				<long>
					<standard>South Georgia Time</standard>
				</long>
			</metazone>
			<metazone type="Suriname">
				<long>
					<standard>Suriname Time</standard>
				</long>
			</metazone>
			<metazone type="Syowa">
				<long>
					<standard>Syowa Time</standard>
				</long>
			</metazone>
			<metazone type="Tahiti">
				<long>
					<standard>Tahiti Time</standard>
				</long>
			</metazone>
			<metazone type="Taipei">
				<long>
					<generic>Taipei Time</generic>
					<standard>Taipei Standard Time</standard>
					<daylight>Taipei Daylight Time</daylight>
				</long>
			</metazone>
			<metazone type="Tajikistan">
				<long>
					<standard>Tajikistan Time</standard>
				</long>
			</metazone>
			<metazone type="Tokelau">
				<long>
					<standard>Tokelau Time</standard>
				</long>
			</metazone>
			<metazone type="Tonga">
				<long>
					<generic>Tonga Time</generic>
					<standard>Tonga Standard Time</standard>
					<daylight>Tonga Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Truk">
				<long>
					<standard>Chuuk Time</standard>
				</long>
			</metazone>
			<metazone type="Turkmenistan">
				<long>
					<generic>Turkmenistan Time</generic>
					<standard>Turkmenistan Standard Time</standard>
					<daylight>Turkmenistan Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Tuvalu">
				<long>
					<standard>Tuvalu Time</standard>
				</long>
			</metazone>
			<metazone type="Uruguay">
				<long>
					<generic>Uruguay Time</generic>
					<standard>Uruguay Standard Time</standard>
					<daylight>Uruguay Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Uzbekistan">
				<long>
					<generic>Uzbekistan Time</generic>
					<standard>Uzbekistan Standard Time</standard>
					<daylight>Uzbekistan Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Vanuatu">
				<long>
					<generic>Vanuatu Time</generic>
					<standard>Vanuatu Standard Time</standard>
					<daylight>Vanuatu Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Venezuela">
				<long>
					<standard>Venezuela Time</standard>
				</long>
			</metazone>
			<metazone type="Vladivostok">
				<long>
					<generic>Vladivostok Time</generic>
					<standard>Vladivostok Standard Time</standard>
					<daylight>Vladivostok Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Volgograd">
				<long>
					<generic>Volgograd Time</generic>
					<standard>Volgograd Standard Time</standard>
					<daylight>Volgograd Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Vostok">
				<long>
					<standard>Vostok Time</standard>
				</long>
			</metazone>
			<metazone type="Wake">
				<long>
					<standard>Wake Island Time</standard>
				</long>
			</metazone>
			<metazone type="Wallis">
				<long>
					<standard>Wallis and Futuna Time</standard>
				</long>
			</metazone>
			<metazone type="Yakutsk">
				<long>
					<generic>Yakutsk Time</generic>
					<standard>Yakutsk Standard Time</standard>
					<daylight>Yakutsk Summer Time</daylight>
				</long>
			</metazone>
			<metazone type="Yekaterinburg">
				<long>
					<generic>Yekaterinburg Time</generic>
					<standard>Yekaterinburg Standard Time</standard>
					<daylight>Yekaterinburg Summer Time</daylight>
				</long>
			</metazone>
		</timeZoneNames>
	</dates>
	<numbers>
		<symbols numberSystem="latn">
			<decimal>.</decimal>
			<group>,</group>
			<list>;</list>
			<percentSign>%</percentSign>
			<plusSign>+</plusSign>
			<minusSign>-</minusSign>
			<exponential>E</exponential>
			<superscriptingExponent>×</superscriptingExponent>
			<perMille>‰</perMille>
			<infinity>∞</infinity>
			<nan>NaN</nan>
		</symbols>
		<decimalFormats numberSystem="latn">
			<decimalFormatLength>
				<decimalFormat>
					<pattern>#,##0.###</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="long">
				<decimalFormat>
					<pattern type="1000" count="one">0 thousand</pattern>
					<pattern type="1000" count="other">0 thousand</pattern>
					<pattern type="10000" count="one">00 thousand</pattern>
					<pattern type="10000" count="other">00 thousand</pattern>
					<pattern type="100000" count="one">000 thousand</pattern>
					<pattern type="100000" count="other">000 thousand</pattern>
					<pattern type="1000000" count="one">0 million</pattern>
					<pattern type="1000000" count="other">0 million</pattern>
					<pattern type="10000000" count="one">00 million</pattern>
					<pattern type="10000000" count="other">00 million</pattern>
					<pattern type="100000000" count="one">000 million</pattern>
					<pattern type="100000000" count="other">000 million</pattern>
					<pattern type="1000000000" count="one">0 billion</pattern>
					<pattern type="1000000000" count="other">0 billion</pattern>
					<pattern type="10000000000" count="one">00 billion</pattern>
					<pattern type="10000000000" count="other">00 billion</pattern>
					<pattern type="100000000000" count="one">000 billion</pattern>
					<pattern type="100000000000" count="other">000 billion</pattern>
					<pattern type="1000000000000" count="one">0 trillion</pattern>
					<pattern type="1000000000000" count="other">0 trillion</pattern>
					<pattern type="10000000000000" count="one">00 trillion</pattern>
					<pattern type="10000000000000" count="other">00 trillion</pattern>
					<pattern type="100000000000000" count="one">000 trillion</pattern>
					<pattern type="100000000000000" count="other">000 trillion</pattern>
				</decimalFormat>
			</decimalFormatLength>
			<decimalFormatLength type="short">
				<decimalFormat>
					<pattern type="1000" count="one">0K</pattern>
					<pattern type="1000" count="other">0K</pattern>
					<pattern type="10000" count="one">00K</pattern>
					<pattern type="10000" count="other">00K</pattern>
					<pattern type="100000" count="one">000K</pattern>
					<pattern type="100000" count="other">000K</pattern>
					<pattern type="1000000" count="one">0M</pattern>
					<pattern type="1000000" count="other">0M</pattern>
					<pattern type="10000000" count="one">00M</pattern>
					<pattern type="10000000" count="other">00M</pattern>
					<pattern type="100000000" count="one">000M</pattern>
					<pattern type="100000000" count="other">000M</pattern>
					<pattern type="1000000000" count="one">0B</pattern>
					<pattern type="1000000000" count="other">0B</pattern>
					<pattern type="10000000000" count="one">00B</pattern>
					<pattern type="10000000000" count="other">00B</pattern>
					<pattern type="100000000000" count="one">000B</pattern>
					<pattern type="100000000000" count="other">000B</pattern>
					<pattern type="1000000000000" count="one">0T</pattern>
					<pattern type="1000000000000" count="other">0T</pattern>
					<pattern type="10000000000000" count="one">00T</pattern>
					<pattern type="10000000000000" count="other">00T</pattern>
					<pattern type="100000000000000" count="one">000T</pattern>
					<pattern type="100000000000000" count="other">000T</pattern>
				</decimalFormat>
			</decimalFormatLength>
		</decimalFormats>
		<scientificFormats numberSystem="latn">
			<scientificFormatLength>
				<scientificFormat>
					<pattern>#E0</pattern>
				</scientificFormat>
			</scientificFormatLength>
		</scientificFormats>
		<percentFormats numberSystem="latn">
			<percentFormatLength>
				<percentFormat>
					<pattern>#,##0%</pattern>
				</percentFormat>
			</percentFormatLength>
		</percentFormats>
		<currencyFormats numberSystem="latn">
			<currencyFormatLength>
				<currencyFormat type="standard">
					<pattern>¤#,##0.00</pattern>
				</currencyFormat>
				<currencyFormat type="accounting">
					<pattern>¤#,##0.00;(¤#,##0.00)</pattern>
				</currencyFormat>
			</currencyFormatLength>
			<unitPattern count="one">{0} {1}</unitPattern>
			<unitPattern count="other">{0} {1}</unitPattern>
		</currencyFormats>
		<currencies>
			<currency type="ADP">
				<displayName>Andorran Peseta</displayName>
				<displayName count="one">Andorran peseta</displayName>
				<displayName count="other">Andorran pesetas</displayName>
			</currency>
			<currency type="AED">
				<displayName>United Arab Emirates Dirham</displayName>
				<displayName count="one">UAE dirham</displayName>
				<displayName count="other">UAE dirhams</displayName>
			</currency>
			<currency type="AFA">
				<displayName>Afghan Afghani (1927–2002)</displayName>
				<displayName count="one">Afghan afghani (1927–2002)</displayName>
				<displayName count="other">Afghan afghanis (1927–2002)</displayName>
			</currency>
			<currency type="AFN">
				<displayName>Afghan Afghani</displayName>
				<displayName count="one">Afghan Afghani</displayName>
				<displayName count="other">Afghan Afghanis</displayName>
			</currency>
			<currency type="ALK">
				<displayName>Albanian Lek (1946–1965)</displayName>
				<displayName count="one">Albanian lek (1946–1965)</displayName>
				<displayName count="other">Albanian lekë (1946–1965)</displayName>
			</currency>
			<currency type="ALL">
				<displayName>Albanian Lek</displayName>
				<displayName count="one">Albanian lek</displayName>
				<displayName count="other">Albanian lekë</displayName>
			</currency>
			<currency type="AMD">
				<displayName>Armenian Dram</displayName>
				<displayName count="one">Armenian dram</displayName>
				<displayName count="other">Armenian drams</displayName>
			</currency>
			<currency type="ANG">
				<displayName>Netherlands Antillean Guilder</displayName>
				<displayName count="one">Netherlands Antillean guilder</displayName>
				<displayName count="other">Netherlands Antillean guilders</displayName>
			</currency>
			<currency type="AOA">
				<displayName>Angolan Kwanza</displayName>
				<displayName count="one">Angolan kwanza</displayName>
				<displayName count="other">Angolan kwanzas</displayName>
			</currency>
			<currency type="AOK">
				<displayName>Angolan Kwanza (1977–1991)</displayName>
				<displayName count="one">Angolan kwanza (1977–1991)</displayName>
				<displayName count="other">Angolan kwanzas (1977–1991)</displayName>
			</currency>
			<currency type="AON">
				<displayName>Angolan New Kwanza (1990–2000)</displayName>
				<displayName count="one">Angolan new kwanza (1990–2000)</displayName>
				<displayName count="other">Angolan new kwanzas (1990–2000)</displayName>
			</currency>
			<currency type="AOR">
				<displayName>Angolan Readjusted Kwanza (1995–1999)</displayName>
				<displayName count="one">Angolan readjusted kwanza (1995–1999)</displayName>
				<displayName count="other">Angolan readjusted kwanzas (1995–1999)</displayName>
			</currency>
			<currency type="ARA">
				<displayName>Argentine Austral</displayName>
				<displayName count="one">Argentine austral</displayName>
				<displayName count="other">Argentine australs</displayName>
			</currency>
			<currency type="ARL">
				<displayName>Argentine Peso Ley (1970–1983)</displayName>
				<displayName count="one">Argentine peso ley (1970–1983)</displayName>
				<displayName count="other">Argentine pesos ley (1970–1983)</displayName>
			</currency>
			<currency type="ARM">
				<displayName>Argentine Peso (1881–1970)</displayName>
				<displayName count="one">Argentine peso (1881–1970)</displayName>
				<displayName count="other">Argentine pesos (1881–1970)</displayName>
			</currency>
			<currency type="ARP">
				<displayName>Argentine Peso (1983–1985)</displayName>
				<displayName count="one">Argentine peso (1983–1985)</displayName>
				<displayName count="other">Argentine pesos (1983–1985)</displayName>
			</currency>
			<currency type="ARS">
				<displayName>Argentine Peso</displayName>
				<displayName count="one">Argentine peso</displayName>
				<displayName count="other">Argentine pesos</displayName>
			</currency>
			<currency type="ATS">
				<displayName>Austrian Schilling</displayName>
				<displayName count="one">Austrian schilling</displayName>
				<displayName count="other">Austrian schillings</displayName>
			</currency>
			<currency type="AUD">
				<displayName>Australian Dollar</displayName>
				<displayName count="one">Australian dollar</displayName>
				<displayName count="other">Australian dollars</displayName>
			</currency>
			<currency type="AWG">
				<displayName>Aruban Florin</displayName>
				<displayName count="one">Aruban florin</displayName>
				<displayName count="other">Aruban florin</displayName>
			</currency>
			<currency type="AZM">
				<displayName>Azerbaijani Manat (1993–2006)</displayName>
				<displayName count="one">Azerbaijani manat (1993–2006)</displayName>
				<displayName count="other">Azerbaijani manats (1993–2006)</displayName>
			</currency>
			<currency type="AZN">
				<displayName>Azerbaijani Manat</displayName>
				<displayName count="one">Azerbaijani manat</displayName>
				<displayName count="other">Azerbaijani manats</displayName>
			</currency>
			<currency type="BAD">
				<displayName>Bosnia-Herzegovina Dinar (1992–1994)</displayName>
				<displayName count="one">Bosnia-Herzegovina dinar (1992–1994)</displayName>
				<displayName count="other">Bosnia-Herzegovina dinars (1992–1994)</displayName>
			</currency>
			<currency type="BAM">
				<displayName>Bosnia-Herzegovina Convertible Mark</displayName>
				<displayName count="one">Bosnia-Herzegovina convertible mark</displayName>
				<displayName count="other">Bosnia-Herzegovina convertible marks</displayName>
			</currency>
			<currency type="BAN">
				<displayName>Bosnia-Herzegovina New Dinar (1994–1997)</displayName>
				<displayName count="one">Bosnia-Herzegovina new dinar (1994–1997)</displayName>
				<displayName count="other">Bosnia-Herzegovina new dinars (1994–1997)</displayName>
			</currency>
			<currency type="BBD">
				<displayName>Barbadian Dollar</displayName>
				<displayName count="one">Barbadian dollar</displayName>
				<displayName count="other">Barbadian dollars</displayName>
			</currency>
			<currency type="BDT">
				<displayName>Bangladeshi Taka</displayName>
				<displayName count="one">Bangladeshi taka</displayName>
				<displayName count="other">Bangladeshi takas</displayName>
			</currency>
			<currency type="BEC">
				<displayName>Belgian Franc (convertible)</displayName>
				<displayName count="one">Belgian franc (convertible)</displayName>
				<displayName count="other">Belgian francs (convertible)</displayName>
			</currency>
			<currency type="BEF">
				<displayName>Belgian Franc</displayName>
				<displayName count="one">Belgian franc</displayName>
				<displayName count="other">Belgian francs</displayName>
			</currency>
			<currency type="BEL">
				<displayName>Belgian Franc (financial)</displayName>
				<displayName count="one">Belgian franc (financial)</displayName>
				<displayName count="other">Belgian francs (financial)</displayName>
			</currency>
			<currency type="BGL">
				<displayName>Bulgarian Hard Lev</displayName>
				<displayName count="one">Bulgarian hard lev</displayName>
				<displayName count="other">Bulgarian hard leva</displayName>
			</currency>
			<currency type="BGM">
				<displayName>Bulgarian Socialist Lev</displayName>
				<displayName count="one">Bulgarian socialist lev</displayName>
				<displayName count="other">Bulgarian socialist leva</displayName>
			</currency>
			<currency type="BGN">
				<displayName>Bulgarian Lev</displayName>
				<displayName count="one">Bulgarian lev</displayName>
				<displayName count="other">Bulgarian leva</displayName>
			</currency>
			<currency type="BGO">
				<displayName>Bulgarian Lev (1879–1952)</displayName>
				<displayName count="one">Bulgarian lev (1879–1952)</displayName>
				<displayName count="other">Bulgarian leva (1879–1952)</displayName>
			</currency>
			<currency type="BHD">
				<displayName>Bahraini Dinar</displayName>
				<displayName count="one">Bahraini dinar</displayName>
				<displayName count="other">Bahraini dinars</displayName>
			</currency>
			<currency type="BIF">
				<displayName>Burundian Franc</displayName>
				<displayName count="one">Burundian franc</displayName>
				<displayName count="other">Burundian francs</displayName>
			</currency>
			<currency type="BMD">
				<displayName>Bermudan Dollar</displayName>
				<displayName count="one">Bermudan dollar</displayName>
				<displayName count="other">Bermudan dollars</displayName>
			</currency>
			<currency type="BND">
				<displayName>Brunei Dollar</displayName>
				<displayName count="one">Brunei dollar</displayName>
				<displayName count="other">Brunei dollars</displayName>
			</currency>
			<currency type="BOB">
				<displayName>Bolivian Boliviano</displayName>
				<displayName count="one">Bolivian boliviano</displayName>
				<displayName count="other">Bolivian bolivianos</displayName>
			</currency>
			<currency type="BOL">
				<displayName>Bolivian Boliviano (1863–1963)</displayName>
				<displayName count="one">Bolivian boliviano (1863–1963)</displayName>
				<displayName count="other">Bolivian bolivianos (1863–1963)</displayName>
			</currency>
			<currency type="BOP">
				<displayName>Bolivian Peso</displayName>
				<displayName count="one">Bolivian peso</displayName>
				<displayName count="other">Bolivian pesos</displayName>
			</currency>
			<currency type="BOV">
				<displayName>Bolivian Mvdol</displayName>
				<displayName count="one">Bolivian mvdol</displayName>
				<displayName count="other">Bolivian mvdols</displayName>
			</currency>
			<currency type="BRB">
				<displayName>Brazilian New Cruzeiro (1967–1986)</displayName>
				<displayName count="one">Brazilian new cruzeiro (1967–1986)</displayName>
				<displayName count="other">Brazilian new cruzeiros (1967–1986)</displayName>
			</currency>
			<currency type="BRC">
				<displayName>Brazilian Cruzado (1986–1989)</displayName>
				<displayName count="one">Brazilian cruzado (1986–1989)</displayName>
				<displayName count="other">Brazilian cruzados (1986–1989)</displayName>
			</currency>
			<currency type="BRE">
				<displayName>Brazilian Cruzeiro (1990–1993)</displayName>
				<displayName count="one">Brazilian cruzeiro (1990–1993)</displayName>
				<displayName count="other">Brazilian cruzeiros (1990–1993)</displayName>
			</currency>
			<currency type="BRL">
				<displayName>Brazilian Real</displayName>
				<displayName count="one">Brazilian real</displayName>
				<displayName count="other">Brazilian reals</displayName>
			</currency>
			<currency type="BRN">
				<displayName>Brazilian New Cruzado (1989–1990)</displayName>
				<displayName count="one">Brazilian new cruzado (1989–1990)</displayName>
				<displayName count="other">Brazilian new cruzados (1989–1990)</displayName>
			</currency>
			<currency type="BRR">
				<displayName>Brazilian Cruzeiro (1993–1994)</displayName>
				<displayName count="one">Brazilian cruzeiro (1993–1994)</displayName>
				<displayName count="other">Brazilian cruzeiros (1993–1994)</displayName>
			</currency>
			<currency type="BRZ">
				<displayName>Brazilian Cruzeiro (1942–1967)</displayName>
				<displayName count="one">Brazilian cruzeiro (1942–1967)</displayName>
				<displayName count="other">Brazilian cruzeiros (1942–1967)</displayName>
			</currency>
			<currency type="BSD">
				<displayName>Bahamian Dollar</displayName>
				<displayName count="one">Bahamian dollar</displayName>
				<displayName count="other">Bahamian dollars</displayName>
			</currency>
			<currency type="BTN">
				<displayName>Bhutanese Ngultrum</displayName>
				<displayName count="one">Bhutanese ngultrum</displayName>
				<displayName count="other">Bhutanese ngultrums</displayName>
			</currency>
			<currency type="BUK">
				<displayName>Burmese Kyat</displayName>
				<displayName count="one">Burmese kyat</displayName>
				<displayName count="other">Burmese kyats</displayName>
			</currency>
			<currency type="BWP">
				<displayName>Botswanan Pula</displayName>
				<displayName count="one">Botswanan pula</displayName>
				<displayName count="other">Botswanan pulas</displayName>
			</currency>
			<currency type="BYB">
				<displayName>Belarusian New Ruble (1994–1999)</displayName>
				<displayName count="one">Belarusian new ruble (1994–1999)</displayName>
				<displayName count="other">Belarusian new rubles (1994–1999)</displayName>
			</currency>
			<currency type="BYR">
				<displayName>Belarusian Ruble</displayName>
				<displayName count="one">Belarusian ruble</displayName>
				<displayName count="other">Belarusian rubles</displayName>
			</currency>
			<currency type="BZD">
				<displayName>Belize Dollar</displayName>
				<displayName count="one">Belize dollar</displayName>
				<displayName count="other">Belize dollars</displayName>
			</currency>
			<currency type="CAD">
				<displayName>Canadian Dollar</displayName>
				<displayName count="one">Canadian dollar</displayName>
				<displayName count="other">Canadian dollars</displayName>
			</currency>
			<currency type="CDF">
				<displayName>Congolese Franc</displayName>
				<displayName count="one">Congolese franc</displayName>
				<displayName count="other">Congolese francs</displayName>
			</currency>
			<currency type="CHE">
				<displayName>WIR Euro</displayName>
				<displayName count="one">WIR euro</displayName>
				<displayName count="other">WIR euros</displayName>
			</currency>
			<currency type="CHF">
				<displayName>Swiss Franc</displayName>
				<displayName count="one">Swiss franc</displayName>
				<displayName count="other">Swiss francs</displayName>
			</currency>
			<currency type="CHW">
				<displayName>WIR Franc</displayName>
				<displayName count="one">WIR franc</displayName>
				<displayName count="other">WIR francs</displayName>
			</currency>
			<currency type="CLE">
				<displayName>Chilean Escudo</displayName>
				<displayName count="one">Chilean escudo</displayName>
				<displayName count="other">Chilean escudos</displayName>
			</currency>
			<currency type="CLF">
				<displayName>Chilean Unit of Account (UF)</displayName>
				<displayName count="one">Chilean unit of account (UF)</displayName>
				<displayName count="other">Chilean units of account (UF)</displayName>
			</currency>
			<currency type="CLP">
				<displayName>Chilean Peso</displayName>
				<displayName count="one">Chilean peso</displayName>
				<displayName count="other">Chilean pesos</displayName>
			</currency>
			<currency type="CNX">
				<displayName>Chinese People’s Bank Dollar</displayName>
				<displayName count="one">Chinese People’s Bank dollar</displayName>
				<displayName count="other">Chinese People’s Bank dollars</displayName>
			</currency>
			<currency type="CNY">
				<displayName>Chinese Yuan</displayName>
				<displayName count="one">Chinese yuan</displayName>
				<displayName count="other">Chinese yuan</displayName>
			</currency>
			<currency type="COP">
				<displayName>Colombian Peso</displayName>
				<displayName count="one">Colombian peso</displayName>
				<displayName count="other">Colombian pesos</displayName>
			</currency>
			<currency type="COU">
				<displayName>Colombian Real Value Unit</displayName>
				<displayName count="one">Colombian real value unit</displayName>
				<displayName count="other">Colombian real value units</displayName>
			</currency>
			<currency type="CRC">
				<displayName>Costa Rican Colón</displayName>
				<displayName count="one">Costa Rican colón</displayName>
				<displayName count="other">Costa Rican colóns</displayName>
			</currency>
			<currency type="CSD">
				<displayName>Serbian Dinar (2002–2006)</displayName>
				<displayName count="one">Serbian dinar (2002–2006)</displayName>
				<displayName count="other">Serbian dinars (2002–2006)</displayName>
			</currency>
			<currency type="CSK">
				<displayName>Czechoslovak Hard Koruna</displayName>
				<displayName count="one">Czechoslovak hard koruna</displayName>
				<displayName count="other">Czechoslovak hard korunas</displayName>
			</currency>
			<currency type="CUC">
				<displayName>Cuban Convertible Peso</displayName>
				<displayName count="one">Cuban convertible peso</displayName>
				<displayName count="other">Cuban convertible pesos</displayName>
			</currency>
			<currency type="CUP">
				<displayName>Cuban Peso</displayName>
				<displayName count="one">Cuban peso</displayName>
				<displayName count="other">Cuban pesos</displayName>
			</currency>
			<currency type="CVE">
				<displayName>Cape Verdean Escudo</displayName>
				<displayName count="one">Cape Verdean escudo</displayName>
				<displayName count="other">Cape Verdean escudos</displayName>
			</currency>
			<currency type="CYP">
				<displayName>Cypriot Pound</displayName>
				<displayName count="one">Cypriot pound</displayName>
				<displayName count="other">Cypriot pounds</displayName>
			</currency>
			<currency type="CZK">
				<displayName>Czech Republic Koruna</displayName>
				<displayName count="one">Czech Republic koruna</displayName>
				<displayName count="other">Czech Republic korunas</displayName>
			</currency>
			<currency type="DDM">
				<displayName>East German Mark</displayName>
				<displayName count="one">East German mark</displayName>
				<displayName count="other">East German marks</displayName>
			</currency>
			<currency type="DEM">
				<displayName>German Mark</displayName>
				<displayName count="one">German mark</displayName>
				<displayName count="other">German marks</displayName>
			</currency>
			<currency type="DJF">
				<displayName>Djiboutian Franc</displayName>
				<displayName count="one">Djiboutian franc</displayName>
				<displayName count="other">Djiboutian francs</displayName>
			</currency>
			<currency type="DKK">
				<displayName>Danish Krone</displayName>
				<displayName count="one">Danish krone</displayName>
				<displayName count="other">Danish kroner</displayName>
			</currency>
			<currency type="DOP">
				<displayName>Dominican Peso</displayName>
				<displayName count="one">Dominican peso</displayName>
				<displayName count="other">Dominican pesos</displayName>
			</currency>
			<currency type="DZD">
				<displayName>Algerian Dinar</displayName>
				<displayName count="one">Algerian dinar</displayName>
				<displayName count="other">Algerian dinars</displayName>
			</currency>
			<currency type="ECS">
				<displayName>Ecuadorian Sucre</displayName>
				<displayName count="one">Ecuadorian sucre</displayName>
				<displayName count="other">Ecuadorian sucres</displayName>
			</currency>
			<currency type="ECV">
				<displayName>Ecuadorian Unit of Constant Value</displayName>
				<displayName count="one">Ecuadorian unit of constant value</displayName>
				<displayName count="other">Ecuadorian units of constant value</displayName>
			</currency>
			<currency type="EEK">
				<displayName>Estonian Kroon</displayName>
				<displayName count="one">Estonian kroon</displayName>
				<displayName count="other">Estonian kroons</displayName>
			</currency>
			<currency type="EGP">
				<displayName>Egyptian Pound</displayName>
				<displayName count="one">Egyptian pound</displayName>
				<displayName count="other">Egyptian pounds</displayName>
			</currency>
			<currency type="ERN">
				<displayName>Eritrean Nakfa</displayName>
				<displayName count="one">Eritrean nakfa</displayName>
				<displayName count="other">Eritrean nakfas</displayName>
			</currency>
			<currency type="ESA">
				<displayName>Spanish Peseta (A account)</displayName>
				<displayName count="one">Spanish peseta (A account)</displayName>
				<displayName count="other">Spanish pesetas (A account)</displayName>
			</currency>
			<currency type="ESB">
				<displayName>Spanish Peseta (convertible account)</displayName>
				<displayName count="one">Spanish peseta (convertible account)</displayName>
				<displayName count="other">Spanish pesetas (convertible account)</displayName>
			</currency>
			<currency type="ESP">
				<displayName>Spanish Peseta</displayName>
				<displayName count="one">Spanish peseta</displayName>
				<displayName count="other">Spanish pesetas</displayName>
			</currency>
			<currency type="ETB">
				<displayName>Ethiopian Birr</displayName>
				<displayName count="one">Ethiopian birr</displayName>
				<displayName count="other">Ethiopian birrs</displayName>
			</currency>
			<currency type="EUR">
				<displayName>Euro</displayName>
				<displayName count="one">euro</displayName>
				<displayName count="other">euros</displayName>
			</currency>
			<currency type="FIM">
				<displayName>Finnish Markka</displayName>
				<displayName count="one">Finnish markka</displayName>
				<displayName count="other">Finnish markkas</displayName>
			</currency>
			<currency type="FJD">
				<displayName>Fijian Dollar</displayName>
				<displayName count="one">Fijian dollar</displayName>
				<displayName count="other">Fijian dollars</displayName>
			</currency>
			<currency type="FKP">
				<displayName>Falkland Islands Pound</displayName>
				<displayName count="one">Falkland Islands pound</displayName>
				<displayName count="other">Falkland Islands pounds</displayName>
			</currency>
			<currency type="FRF">
				<displayName>French Franc</displayName>
				<displayName count="one">French franc</displayName>
				<displayName count="other">French francs</displayName>
			</currency>
			<currency type="GBP">
				<displayName>British Pound Sterling</displayName>
				<displayName count="one">British pound sterling</displayName>
				<displayName count="other">British pounds sterling</displayName>
			</currency>
			<currency type="GEK">
				<displayName>Georgian Kupon Larit</displayName>
				<displayName count="one">Georgian kupon larit</displayName>
				<displayName count="other">Georgian kupon larits</displayName>
			</currency>
			<currency type="GEL">
				<displayName>Georgian Lari</displayName>
				<displayName count="one">Georgian lari</displayName>
				<displayName count="other">Georgian laris</displayName>
			</currency>
			<currency type="GHC">
				<displayName>Ghanaian Cedi (1979–2007)</displayName>
				<displayName count="one">Ghanaian cedi (1979–2007)</displayName>
				<displayName count="other">Ghanaian cedis (1979–2007)</displayName>
			</currency>
			<currency type="GHS">
				<displayName>Ghanaian Cedi</displayName>
				<displayName count="one">Ghanaian cedi</displayName>
				<displayName count="other">Ghanaian cedis</displayName>
			</currency>
			<currency type="GIP">
				<displayName>Gibraltar Pound</displayName>
				<displayName count="one">Gibraltar pound</displayName>
				<displayName count="other">Gibraltar pounds</displayName>
			</currency>
			<currency type="GMD">
				<displayName>Gambian Dalasi</displayName>
				<displayName count="one">Gambian dalasi</displayName>
				<displayName count="other">Gambian dalasis</displayName>
			</currency>
			<currency type="GNF">
				<displayName>Guinean Franc</displayName>
				<displayName count="one">Guinean franc</displayName>
				<displayName count="other">Guinean francs</displayName>
			</currency>
			<currency type="GNS">
				<displayName>Guinean Syli</displayName>
				<displayName count="one">Guinean syli</displayName>
				<displayName count="other">Guinean sylis</displayName>
			</currency>
			<currency type="GQE">
				<displayName>Equatorial Guinean Ekwele</displayName>
				<displayName count="one">Equatorial Guinean ekwele</displayName>
				<displayName count="other">Equatorial Guinean ekwele</displayName>
			</currency>
			<currency type="GRD">
				<displayName>Greek Drachma</displayName>
				<displayName count="one">Greek drachma</displayName>
				<displayName count="other">Greek drachmas</displayName>
			</currency>
			<currency type="GTQ">
				<displayName>Guatemalan Quetzal</displayName>
				<displayName count="one">Guatemalan quetzal</displayName>
				<displayName count="other">Guatemalan quetzals</displayName>
			</currency>
			<currency type="GWE">
				<displayName>Portuguese Guinea Escudo</displayName>
				<displayName count="one">Portuguese Guinea escudo</displayName>
				<displayName count="other">Portuguese Guinea escudos</displayName>
			</currency>
			<currency type="GWP">
				<displayName>Guinea-Bissau Peso</displayName>
				<displayName count="one">Guinea-Bissau peso</displayName>
				<displayName count="other">Guinea-Bissau pesos</displayName>
			</currency>
			<currency type="GYD">
				<displayName>Guyanaese Dollar</displayName>
				<displayName count="one">Guyanaese dollar</displayName>
				<displayName count="other">Guyanaese dollars</displayName>
			</currency>
			<currency type="HKD">
				<displayName>Hong Kong Dollar</displayName>
				<displayName count="one">Hong Kong dollar</displayName>
				<displayName count="other">Hong Kong dollars</displayName>
			</currency>
			<currency type="HNL">
				<displayName>Honduran Lempira</displayName>
				<displayName count="one">Honduran lempira</displayName>
				<displayName count="other">Honduran lempiras</displayName>
			</currency>
			<currency type="HRD">
				<displayName>Croatian Dinar</displayName>
				<displayName count="one">Croatian dinar</displayName>
				<displayName count="other">Croatian dinars</displayName>
			</currency>
			<currency type="HRK">
				<displayName>Croatian Kuna</displayName>
				<displayName count="one">Croatian kuna</displayName>
				<displayName count="other">Croatian kunas</displayName>
			</currency>
			<currency type="HTG">
				<displayName>Haitian Gourde</displayName>
				<displayName count="one">Haitian gourde</displayName>
				<displayName count="other">Haitian gourdes</displayName>
			</currency>
			<currency type="HUF">
				<displayName>Hungarian Forint</displayName>
				<displayName count="one">Hungarian forint</displayName>
				<displayName count="other">Hungarian forints</displayName>
			</currency>
			<currency type="IDR">
				<displayName>Indonesian Rupiah</displayName>
				<displayName count="one">Indonesian rupiah</displayName>
				<displayName count="other">Indonesian rupiahs</displayName>
			</currency>
			<currency type="IEP">
				<displayName>Irish Pound</displayName>
				<displayName count="one">Irish pound</displayName>
				<displayName count="other">Irish pounds</displayName>
			</currency>
			<currency type="ILP">
				<displayName>Israeli Pound</displayName>
				<displayName count="one">Israeli pound</displayName>
				<displayName count="other">Israeli pounds</displayName>
			</currency>
			<currency type="ILR">
				<displayName>Israeli Sheqel (1980–1985)</displayName>
				<displayName count="one">Israeli sheqel (1980–1985)</displayName>
				<displayName count="other">Israeli sheqels (1980–1985)</displayName>
			</currency>
			<currency type="ILS">
				<displayName>Israeli New Sheqel</displayName>
				<displayName count="one">Israeli new sheqel</displayName>
				<displayName count="other">Israeli new sheqels</displayName>
			</currency>
			<currency type="INR">
				<displayName>Indian Rupee</displayName>
				<displayName count="one">Indian rupee</displayName>
				<displayName count="other">Indian rupees</displayName>
			</currency>
			<currency type="IQD">
				<displayName>Iraqi Dinar</displayName>
				<displayName count="one">Iraqi dinar</displayName>
				<displayName count="other">Iraqi dinars</displayName>
			</currency>
			<currency type="IRR">
				<displayName>Iranian Rial</displayName>
				<displayName count="one">Iranian rial</displayName>
				<displayName count="other">Iranian rials</displayName>
			</currency>
			<currency type="ISJ">
				<displayName>Icelandic Króna (1918–1981)</displayName>
				<displayName count="one">Icelandic króna (1918–1981)</displayName>
				<displayName count="other">Icelandic krónur (1918–1981)</displayName>
			</currency>
			<currency type="ISK">
				<displayName>Icelandic Króna</displayName>
				<displayName count="one">Icelandic króna</displayName>
				<displayName count="other">Icelandic krónur</displayName>
			</currency>
			<currency type="ITL">
				<displayName>Italian Lira</displayName>
				<displayName count="one">Italian lira</displayName>
				<displayName count="other">Italian liras</displayName>
			</currency>
			<currency type="JMD">
				<displayName>Jamaican Dollar</displayName>
				<displayName count="one">Jamaican dollar</displayName>
				<displayName count="other">Jamaican dollars</displayName>
			</currency>
			<currency type="JOD">
				<displayName>Jordanian Dinar</displayName>
				<displayName count="one">Jordanian dinar</displayName>
				<displayName count="other">Jordanian dinars</displayName>
			</currency>
			<currency type="JPY">
				<displayName>Japanese Yen</displayName>
				<displayName count="one">Japanese yen</displayName>
				<displayName count="other">Japanese yen</displayName>
				<symbol>¥</symbol>
			</currency>
			<currency type="KES">
				<displayName>Kenyan Shilling</displayName>
				<displayName count="one">Kenyan shilling</displayName>
				<displayName count="other">Kenyan shillings</displayName>
			</currency>
			<currency type="KGS">
				<displayName>Kyrgystani Som</displayName>
				<displayName count="one">Kyrgystani som</displayName>
				<displayName count="other">Kyrgystani soms</displayName>
			</currency>
			<currency type="KHR">
				<displayName>Cambodian Riel</displayName>
				<displayName count="one">Cambodian riel</displayName>
				<displayName count="other">Cambodian riels</displayName>
			</currency>
			<currency type="KMF">
				<displayName>Comorian Franc</displayName>
				<displayName count="one">Comorian franc</displayName>
				<displayName count="other">Comorian francs</displayName>
			</currency>
			<currency type="KPW">
				<displayName>North Korean Won</displayName>
				<displayName count="one">North Korean won</displayName>
				<displayName count="other">North Korean won</displayName>
			</currency>
			<currency type="KRH">
				<displayName>South Korean Hwan (1953–1962)</displayName>
				<displayName count="one">South Korean hwan (1953–1962)</displayName>
				<displayName count="other">South Korean hwan (1953–1962)</displayName>
			</currency>
			<currency type="KRO">
				<displayName>South Korean Won (1945–1953)</displayName>
				<displayName count="one">South Korean won (1945–1953)</displayName>
				<displayName count="other">South Korean won (1945–1953)</displayName>
			</currency>
			<currency type="KRW">
				<displayName>South Korean Won</displayName>
				<displayName count="one">South Korean won</displayName>
				<displayName count="other">South Korean won</displayName>
			</currency>
			<currency type="KWD">
				<displayName>Kuwaiti Dinar</displayName>
				<displayName count="one">Kuwaiti dinar</displayName>
				<displayName count="other">Kuwaiti dinars</displayName>
			</currency>
			<currency type="KYD">
				<displayName>Cayman Islands Dollar</displayName>
				<displayName count="one">Cayman Islands dollar</displayName>
				<displayName count="other">Cayman Islands dollars</displayName>
			</currency>
			<currency type="KZT">
				<displayName>Kazakhstani Tenge</displayName>
				<displayName count="one">Kazakhstani tenge</displayName>
				<displayName count="other">Kazakhstani tenges</displayName>
			</currency>
			<currency type="LAK">
				<displayName>Laotian Kip</displayName>
				<displayName count="one">Laotian kip</displayName>
				<displayName count="other">Laotian kips</displayName>
			</currency>
			<currency type="LBP">
				<displayName>Lebanese Pound</displayName>
				<displayName count="one">Lebanese pound</displayName>
				<displayName count="other">Lebanese pounds</displayName>
			</currency>
			<currency type="LKR">
				<displayName>Sri Lankan Rupee</displayName>
				<displayName count="one">Sri Lankan rupee</displayName>
				<displayName count="other">Sri Lankan rupees</displayName>
			</currency>
			<currency type="LRD">
				<displayName>Liberian Dollar</displayName>
				<displayName count="one">Liberian dollar</displayName>
				<displayName count="other">Liberian dollars</displayName>
			</currency>
			<currency type="LSL">
				<displayName>Lesotho Loti</displayName>
				<displayName count="one">Lesotho loti</displayName>
				<displayName count="other">Lesotho lotis</displayName>
			</currency>
			<currency type="LTL">
				<displayName>Lithuanian Litas</displayName>
				<displayName count="one">Lithuanian litas</displayName>
				<displayName count="other">Lithuanian litai</displayName>
			</currency>
			<currency type="LTT">
				<displayName>Lithuanian Talonas</displayName>
				<displayName count="one">Lithuanian talonas</displayName>
				<displayName count="other">Lithuanian talonases</displayName>
			</currency>
			<currency type="LUC">
				<displayName>Luxembourgian Convertible Franc</displayName>
				<displayName count="one">Luxembourgian convertible franc</displayName>
				<displayName count="other">Luxembourgian convertible francs</displayName>
			</currency>
			<currency type="LUF">
				<displayName>Luxembourgian Franc</displayName>
				<displayName count="one">Luxembourgian franc</displayName>
				<displayName count="other">Luxembourgian francs</displayName>
			</currency>
			<currency type="LUL">
				<displayName>Luxembourg Financial Franc</displayName>
				<displayName count="one">Luxembourg financial franc</displayName>
				<displayName count="other">Luxembourg financial francs</displayName>
			</currency>
			<currency type="LVL">
				<displayName>Latvian Lats</displayName>
				<displayName count="one">Latvian lats</displayName>
				<displayName count="other">Latvian lati</displayName>
			</currency>
			<currency type="LVR">
				<displayName>Latvian Ruble</displayName>
				<displayName count="one">Latvian ruble</displayName>
				<displayName count="other">Latvian rubles</displayName>
			</currency>
			<currency type="LYD">
				<displayName>Libyan Dinar</displayName>
				<displayName count="one">Libyan dinar</displayName>
				<displayName count="other">Libyan dinars</displayName>
			</currency>
			<currency type="MAD">
				<displayName>Moroccan Dirham</displayName>
				<displayName count="one">Moroccan dirham</displayName>
				<displayName count="other">Moroccan dirhams</displayName>
			</currency>
			<currency type="MAF">
				<displayName>Moroccan Franc</displayName>
				<displayName count="one">Moroccan franc</displayName>
				<displayName count="other">Moroccan francs</displayName>
			</currency>
			<currency type="MCF">
				<displayName>Monegasque Franc</displayName>
				<displayName count="one">Monegasque franc</displayName>
				<displayName count="other">Monegasque francs</displayName>
			</currency>
			<currency type="MDC">
				<displayName>Moldovan Cupon</displayName>
				<displayName count="one">Moldovan cupon</displayName>
				<displayName count="other">Moldovan cupon</displayName>
			</currency>
			<currency type="MDL">
				<displayName>Moldovan Leu</displayName>
				<displayName count="one">Moldovan leu</displayName>
				<displayName count="other">Moldovan lei</displayName>
			</currency>
			<currency type="MGA">
				<displayName>Malagasy Ariary</displayName>
				<displayName count="one">Malagasy Ariary</displayName>
				<displayName count="other">Malagasy Ariaries</displayName>
			</currency>
			<currency type="MGF">
				<displayName>Malagasy Franc</displayName>
				<displayName count="one">Malagasy franc</displayName>
				<displayName count="other">Malagasy francs</displayName>
			</currency>
			<currency type="MKD">
				<displayName>Macedonian Denar</displayName>
				<displayName count="one">Macedonian denar</displayName>
				<displayName count="other">Macedonian denari</displayName>
			</currency>
			<currency type="MKN">
				<displayName>Macedonian Denar (1992–1993)</displayName>
				<displayName count="one">Macedonian denar (1992–1993)</displayName>
				<displayName count="other">Macedonian denari (1992–1993)</displayName>
			</currency>
			<currency type="MLF">
				<displayName>Malian Franc</displayName>
				<displayName count="one">Malian franc</displayName>
				<displayName count="other">Malian francs</displayName>
			</currency>
			<currency type="MMK">
				<displayName>Myanmar Kyat</displayName>
				<displayName count="one">Myanmar kyat</displayName>
				<displayName count="other">Myanmar kyats</displayName>
			</currency>
			<currency type="MNT">
				<displayName>Mongolian Tugrik</displayName>
				<displayName count="one">Mongolian tugrik</displayName>
				<displayName count="other">Mongolian tugriks</displayName>
			</currency>
			<currency type="MOP">
				<displayName>Macanese Pataca</displayName>
				<displayName count="one">Macanese pataca</displayName>
				<displayName count="other">Macanese patacas</displayName>
			</currency>
			<currency type="MRO">
				<displayName>Mauritanian Ouguiya</displayName>
				<displayName count="one">Mauritanian ouguiya</displayName>
				<displayName count="other">Mauritanian ouguiyas</displayName>
			</currency>
			<currency type="MTL">
				<displayName>Maltese Lira</displayName>
				<displayName count="one">Maltese lira</displayName>
				<displayName count="other">Maltese lira</displayName>
			</currency>
			<currency type="MTP">
				<displayName>Maltese Pound</displayName>
				<displayName count="one">Maltese pound</displayName>
				<displayName count="other">Maltese pounds</displayName>
			</currency>
			<currency type="MUR">
				<displayName>Mauritian Rupee</displayName>
				<displayName count="one">Mauritian rupee</displayName>
				<displayName count="other">Mauritian rupees</displayName>
			</currency>
			<currency type="MVP">
				<displayName>Maldivian Rupee</displayName>
				<displayName count="one">Maldivian rupee</displayName>
				<displayName count="other">Maldivian rupees</displayName>
			</currency>
			<currency type="MVR">
				<displayName>Maldivian Rufiyaa</displayName>
				<displayName count="one">Maldivian rufiyaa</displayName>
				<displayName count="other">Maldivian rufiyaas</displayName>
			</currency>
			<currency type="MWK">
				<displayName>Malawian Kwacha</displayName>
				<displayName count="one">Malawian Kwacha</displayName>
				<displayName count="other">Malawian Kwachas</displayName>
			</currency>
			<currency type="MXN">
				<displayName>Mexican Peso</displayName>
				<displayName count="one">Mexican peso</displayName>
				<displayName count="other">Mexican pesos</displayName>
			</currency>
			<currency type="MXP">
				<displayName>Mexican Silver Peso (1861–1992)</displayName>
				<displayName count="one">Mexican silver peso (1861–1992)</displayName>
				<displayName count="other">Mexican silver pesos (1861–1992)</displayName>
			</currency>
			<currency type="MXV">
				<displayName>Mexican Investment Unit</displayName>
				<displayName count="one">Mexican investment unit</displayName>
				<displayName count="other">Mexican investment units</displayName>
			</currency>
			<currency type="MYR">
				<displayName>Malaysian Ringgit</displayName>
				<displayName count="one">Malaysian ringgit</displayName>
				<displayName count="other">Malaysian ringgits</displayName>
			</currency>
			<currency type="MZE">
				<displayName>Mozambican Escudo</displayName>
				<displayName count="one">Mozambican escudo</displayName>
				<displayName count="other">Mozambican escudos</displayName>
			</currency>
			<currency type="MZM">
				<displayName>Mozambican Metical (1980–2006)</displayName>
				<displayName count="one">Mozambican metical (1980–2006)</displayName>
				<displayName count="other">Mozambican meticals (1980–2006)</displayName>
			</currency>
			<currency type="MZN">
				<displayName>Mozambican Metical</displayName>
				<displayName count="one">Mozambican metical</displayName>
				<displayName count="other">Mozambican meticals</displayName>
			</currency>
			<currency type="NAD">
				<displayName>Namibian Dollar</displayName>
				<displayName count="one">Namibian dollar</displayName>
				<displayName count="other">Namibian dollars</displayName>
			</currency>
			<currency type="NGN">
				<displayName>Nigerian Naira</displayName>
				<displayName count="one">Nigerian naira</displayName>
				<displayName count="other">Nigerian nairas</displayName>
			</currency>
			<currency type="NIC">
				<displayName>Nicaraguan Córdoba (1988–1991)</displayName>
				<displayName count="one">Nicaraguan córdoba (1988–1991)</displayName>
				<displayName count="other">Nicaraguan córdobas (1988–1991)</displayName>
			</currency>
			<currency type="NIO">
				<displayName>Nicaraguan Córdoba</displayName>
				<displayName count="one">Nicaraguan córdoba</displayName>
				<displayName count="other">Nicaraguan córdobas</displayName>
			</currency>
			<currency type="NLG">
				<displayName>Dutch Guilder</displayName>
				<displayName count="one">Dutch guilder</displayName>
				<displayName count="other">Dutch guilders</displayName>
			</currency>
			<currency type="NOK">
				<displayName>Norwegian Krone</displayName>
				<displayName count="one">Norwegian krone</displayName>
				<displayName count="other">Norwegian kroner</displayName>
			</currency>
			<currency type="NPR">
				<displayName>Nepalese Rupee</displayName>
				<displayName count="one">Nepalese rupee</displayName>
				<displayName count="other">Nepalese rupees</displayName>
			</currency>
			<currency type="NZD">
				<displayName>New Zealand Dollar</displayName>
				<displayName count="one">New Zealand dollar</displayName>
				<displayName count="other">New Zealand dollars</displayName>
			</currency>
			<currency type="OMR">
				<displayName>Omani Rial</displayName>
				<displayName count="one">Omani rial</displayName>
				<displayName count="other">Omani rials</displayName>
			</currency>
			<currency type="PAB">
				<displayName>Panamanian Balboa</displayName>
				<displayName count="one">Panamanian balboa</displayName>
				<displayName count="other">Panamanian balboas</displayName>
			</currency>
			<currency type="PEI">
				<displayName>Peruvian Inti</displayName>
				<displayName count="one">Peruvian inti</displayName>
				<displayName count="other">Peruvian intis</displayName>
			</currency>
			<currency type="PEN">
				<displayName>Peruvian Nuevo Sol</displayName>
				<displayName count="one">Peruvian nuevo sol</displayName>
				<displayName count="other">Peruvian nuevos soles</displayName>
			</currency>
			<currency type="PES">
				<displayName>Peruvian Sol (1863–1965)</displayName>
				<displayName count="one">Peruvian sol (1863–1965)</displayName>
				<displayName count="other">Peruvian soles (1863–1965)</displayName>
			</currency>
			<currency type="PGK">
				<displayName>Papua New Guinean Kina</displayName>
				<displayName count="one">Papua New Guinean kina</displayName>
				<displayName count="other">Papua New Guinean kina</displayName>
			</currency>
			<currency type="PHP">
				<displayName>Philippine Peso</displayName>
				<displayName count="one">Philippine peso</displayName>
				<displayName count="other">Philippine pesos</displayName>
			</currency>
			<currency type="PKR">
				<displayName>Pakistani Rupee</displayName>
				<displayName count="one">Pakistani rupee</displayName>
				<displayName count="other">Pakistani rupees</displayName>
			</currency>
			<currency type="PLN">
				<displayName>Polish Zloty</displayName>
				<displayName count="one">Polish zloty</displayName>
				<displayName count="other">Polish zlotys</displayName>
			</currency>
			<currency type="PLZ">
				<displayName>Polish Zloty (1950–1995)</displayName>
				<displayName count="one">Polish zloty (PLZ)</displayName>
				<displayName count="other">Polish zlotys (PLZ)</displayName>
			</currency>
			<currency type="PTE">
				<displayName>Portuguese Escudo</displayName>
				<displayName count="one">Portuguese escudo</displayName>
				<displayName count="other">Portuguese escudos</displayName>
			</currency>
			<currency type="PYG">
				<displayName>Paraguayan Guarani</displayName>
				<displayName count="one">Paraguayan guarani</displayName>
				<displayName count="other">Paraguayan guaranis</displayName>
			</currency>
			<currency type="QAR">
				<displayName>Qatari Rial</displayName>
				<displayName count="one">Qatari rial</displayName>
				<displayName count="other">Qatari rials</displayName>
			</currency>
			<currency type="RHD">
				<displayName>Rhodesian Dollar</displayName>
				<displayName count="one">Rhodesian dollar</displayName>
				<displayName count="other">Rhodesian dollars</displayName>
			</currency>
			<currency type="ROL">
				<displayName>Romanian Leu (1952–2006)</displayName>
				<displayName count="one">Romanian leu (1952–2006)</displayName>
				<displayName count="other">Romanian Lei (1952–2006)</displayName>
			</currency>
			<currency type="RON">
				<displayName>Romanian Leu</displayName>
				<displayName count="one">Romanian leu</displayName>
				<displayName count="other">Romanian lei</displayName>
			</currency>
			<currency type="RSD">
				<displayName>Serbian Dinar</displayName>
				<displayName count="one">Serbian dinar</displayName>
				<displayName count="other">Serbian dinars</displayName>
			</currency>
			<currency type="RUB">
				<displayName>Russian Ruble</displayName>
				<displayName count="one">Russian ruble</displayName>
				<displayName count="other">Russian rubles</displayName>
			</currency>
			<currency type="RUR">
				<displayName>Russian Ruble (1991–1998)</displayName>
				<displayName count="one">Russian ruble (1991–1998)</displayName>
				<displayName count="other">Russian rubles (1991–1998)</displayName>
			</currency>
			<currency type="RWF">
				<displayName>Rwandan Franc</displayName>
				<displayName count="one">Rwandan franc</displayName>
				<displayName count="other">Rwandan francs</displayName>
			</currency>
			<currency type="SAR">
				<displayName>Saudi Riyal</displayName>
				<displayName count="one">Saudi riyal</displayName>
				<displayName count="other">Saudi riyals</displayName>
			</currency>
			<currency type="SBD">
				<displayName>Solomon Islands Dollar</displayName>
				<displayName count="one">Solomon Islands dollar</displayName>
				<displayName count="other">Solomon Islands dollars</displayName>
			</currency>
			<currency type="SCR">
				<displayName>Seychellois Rupee</displayName>
				<displayName count="one">Seychellois rupee</displayName>
				<displayName count="other">Seychellois rupees</displayName>
			</currency>
			<currency type="SDD">
				<displayName>Sudanese Dinar (1992–2007)</displayName>
				<displayName count="one">Sudanese dinar (1992–2007)</displayName>
				<displayName count="other">Sudanese dinars (1992–2007)</displayName>
			</currency>
			<currency type="SDG">
				<displayName>Sudanese Pound</displayName>
				<displayName count="one">Sudanese pound</displayName>
				<displayName count="other">Sudanese pounds</displayName>
			</currency>
			<currency type="SDP">
				<displayName>Sudanese Pound (1957–1998)</displayName>
				<displayName count="one">Sudanese pound (1957–1998)</displayName>
				<displayName count="other">Sudanese pounds (1957–1998)</displayName>
			</currency>
			<currency type="SEK">
				<displayName>Swedish Krona</displayName>
				<displayName count="one">Swedish krona</displayName>
				<displayName count="other">Swedish kronor</displayName>
			</currency>
			<currency type="SGD">
				<displayName>Singapore Dollar</displayName>
				<displayName count="one">Singapore dollar</displayName>
				<displayName count="other">Singapore dollars</displayName>
			</currency>
			<currency type="SHP">
				<displayName>Saint Helena Pound</displayName>
				<displayName count="one">Saint Helena pound</displayName>
				<displayName count="other">Saint Helena pounds</displayName>
			</currency>
			<currency type="SIT">
				<displayName>Slovenian Tolar</displayName>
				<displayName count="one">Slovenian tolar</displayName>
				<displayName count="other">Slovenian tolars</displayName>
			</currency>
			<currency type="SKK">
				<displayName>Slovak Koruna</displayName>
				<displayName count="one">Slovak koruna</displayName>
				<displayName count="other">Slovak korunas</displayName>
			</currency>
			<currency type="SLL">
				<displayName>Sierra Leonean Leone</displayName>
				<displayName count="one">Sierra Leonean leone</displayName>
				<displayName count="other">Sierra Leonean leones</displayName>
			</currency>
			<currency type="SOS">
				<displayName>Somali Shilling</displayName>
				<displayName count="one">Somali shilling</displayName>
				<displayName count="other">Somali shillings</displayName>
			</currency>
			<currency type="SRD">
				<displayName>Surinamese Dollar</displayName>
				<displayName count="one">Surinamese dollar</displayName>
				<displayName count="other">Surinamese dollars</displayName>
			</currency>
			<currency type="SRG">
				<displayName>Surinamese Guilder</displayName>
				<displayName count="one">Surinamese guilder</displayName>
				<displayName count="other">Surinamese guilders</displayName>
			</currency>
			<currency type="SSP">
				<displayName>South Sudanese Pound</displayName>
				<displayName count="one">South Sudanese pound</displayName>
				<displayName count="other">South Sudanese pounds</displayName>
			</currency>
			<currency type="STD">
				<displayName>São Tomé and Príncipe Dobra</displayName>
				<displayName count="one">São Tomé and Príncipe dobra</displayName>
				<displayName count="other">São Tomé and Príncipe dobras</displayName>
			</currency>
			<currency type="SUR">
				<displayName>Soviet Rouble</displayName>
				<displayName count="one">Soviet rouble</displayName>
				<displayName count="other">Soviet roubles</displayName>
			</currency>
			<currency type="SVC">
				<displayName>Salvadoran Colón</displayName>
				<displayName count="one">Salvadoran colón</displayName>
				<displayName count="other">Salvadoran colones</displayName>
			</currency>
			<currency type="SYP">
				<displayName>Syrian Pound</displayName>
				<displayName count="one">Syrian pound</displayName>
				<displayName count="other">Syrian pounds</displayName>
			</currency>
			<currency type="SZL">
				<displayName>Swazi Lilangeni</displayName>
				<displayName count="one">Swazi lilangeni</displayName>
				<displayName count="other">Swazi emalangeni</displayName>
			</currency>
			<currency type="THB">
				<displayName>Thai Baht</displayName>
				<displayName count="one">Thai baht</displayName>
				<displayName count="other">Thai baht</displayName>
			</currency>
			<currency type="TJR">
				<displayName>Tajikistani Ruble</displayName>
				<displayName count="one">Tajikistani ruble</displayName>
				<displayName count="other">Tajikistani rubles</displayName>
			</currency>
			<currency type="TJS">
				<displayName>Tajikistani Somoni</displayName>
				<displayName count="one">Tajikistani somoni</displayName>
				<displayName count="other">Tajikistani somonis</displayName>
			</currency>
			<currency type="TMM">
				<displayName>Turkmenistani Manat (1993–2009)</displayName>
				<displayName count="one">Turkmenistani manat (1993–2009)</displayName>
				<displayName count="other">Turkmenistani manat (1993–2009)</displayName>
			</currency>
			<currency type="TMT">
				<displayName>Turkmenistani Manat</displayName>
				<displayName count="one">Turkmenistani manat</displayName>
				<displayName count="other">Turkmenistani manat</displayName>
			</currency>
			<currency type="TND">
				<displayName>Tunisian Dinar</displayName>
				<displayName count="one">Tunisian dinar</displayName>
				<displayName count="other">Tunisian dinars</displayName>
			</currency>
			<currency type="TOP">
				<displayName>Tongan Paʻanga</displayName>
				<displayName count="one">Tongan paʻanga</displayName>
				<displayName count="other">Tongan paʻanga</displayName>
			</currency>
			<currency type="TPE">
				<displayName>Timorese Escudo</displayName>
				<displayName count="one">Timorese escudo</displayName>
				<displayName count="other">Timorese escudos</displayName>
			</currency>
			<currency type="TRL">
				<displayName>Turkish Lira (1922–2005)</displayName>
				<displayName count="one">Turkish lira (1922–2005)</displayName>
				<displayName count="other">Turkish Lira (1922–2005)</displayName>
			</currency>
			<currency type="TRY">
				<displayName>Turkish Lira</displayName>
				<displayName count="one">Turkish lira</displayName>
				<displayName count="other">Turkish Lira</displayName>
			</currency>
			<currency type="TTD">
				<displayName>Trinidad and Tobago Dollar</displayName>
				<displayName count="one">Trinidad and Tobago dollar</displayName>
				<displayName count="other">Trinidad and Tobago dollars</displayName>
			</currency>
			<currency type="TWD">
				<displayName>New Taiwan Dollar</displayName>
				<displayName count="one">New Taiwan dollar</displayName>
				<displayName count="other">New Taiwan dollars</displayName>
			</currency>
			<currency type="TZS">
				<displayName>Tanzanian Shilling</displayName>
				<displayName count="one">Tanzanian shilling</displayName>
				<displayName count="other">Tanzanian shillings</displayName>
			</currency>
			<currency type="UAH">
				<displayName>Ukrainian Hryvnia</displayName>
				<displayName count="one">Ukrainian hryvnia</displayName>
				<displayName count="other">Ukrainian hryvnias</displayName>
			</currency>
			<currency type="UAK">
				<displayName>Ukrainian Karbovanets</displayName>
				<displayName count="one">Ukrainian karbovanets</displayName>
				<displayName count="other">Ukrainian karbovantsiv</displayName>
			</currency>
			<currency type="UGS">
				<displayName>Ugandan Shilling (1966–1987)</displayName>
				<displayName count="one">Ugandan shilling (1966–1987)</displayName>
				<displayName count="other">Ugandan shillings (1966–1987)</displayName>
			</currency>
			<currency type="UGX">
				<displayName>Ugandan Shilling</displayName>
				<displayName count="one">Ugandan shilling</displayName>
				<displayName count="other">Ugandan shillings</displayName>
			</currency>
			<currency type="USD">
				<displayName>US Dollar</displayName>
				<displayName count="one">US dollar</displayName>
				<displayName count="other">US dollars</displayName>
				<symbol>$</symbol>
			</currency>
			<currency type="USN">
				<displayName>US Dollar (Next day)</displayName>
				<displayName count="one">US dollar (next day)</displayName>
				<displayName count="other">US dollars (next day)</displayName>
			</currency>
			<currency type="USS">
				<displayName>US Dollar (Same day)</displayName>
				<displayName count="one">US dollar (same day)</displayName>
				<displayName count="other">US dollars (same day)</displayName>
			</currency>
			<currency type="UYI">
				<displayName>Uruguayan Peso (Indexed Units)</displayName>
				<displayName count="one">Uruguayan peso (indexed units)</displayName>
				<displayName count="other">Uruguayan pesos (indexed units)</displayName>
			</currency>
			<currency type="UYP">
				<displayName>Uruguayan Peso (1975–1993)</displayName>
				<displayName count="one">Uruguayan peso (1975–1993)</displayName>
				<displayName count="other">Uruguayan pesos (1975–1993)</displayName>
			</currency>
			<currency type="UYU">
				<displayName>Uruguayan Peso</displayName>
				<displayName count="one">Uruguayan peso</displayName>
				<displayName count="other">Uruguayan pesos</displayName>
			</currency>
			<currency type="UZS">
				<displayName>Uzbekistan Som</displayName>
				<displayName count="one">Uzbekistan som</displayName>
				<displayName count="other">Uzbekistan som</displayName>
			</currency>
			<currency type="VEB">
				<displayName>Venezuelan Bolívar (1871–2008)</displayName>
				<displayName count="one">Venezuelan bolívar (1871–2008)</displayName>
				<displayName count="other">Venezuelan bolívars (1871–2008)</displayName>
			</currency>
			<currency type="VEF">
				<displayName>Venezuelan Bolívar</displayName>
				<displayName count="one">Venezuelan bolívar</displayName>
				<displayName count="other">Venezuelan bolívars</displayName>
			</currency>
			<currency type="VND">
				<displayName>Vietnamese Dong</displayName>
				<displayName count="one">Vietnamese dong</displayName>
				<displayName count="other">Vietnamese dong</displayName>
			</currency>
			<currency type="VNN">
				<displayName>Vietnamese Dong (1978–1985)</displayName>
				<displayName count="one">Vietnamese dong (1978–1985)</displayName>
				<displayName count="other">Vietnamese dong (1978–1985)</displayName>
			</currency>
			<currency type="VUV">
				<displayName>Vanuatu Vatu</displayName>
				<displayName count="one">Vanuatu vatu</displayName>
				<displayName count="other">Vanuatu vatus</displayName>
			</currency>
			<currency type="WST">
				<displayName>Samoan Tala</displayName>
				<displayName count="one">Samoan tala</displayName>
				<displayName count="other">Samoan tala</displayName>
			</currency>
			<currency type="XAF">
				<displayName>CFA Franc BEAC</displayName>
				<displayName count="one">CFA franc BEAC</displayName>
				<displayName count="other">CFA francs BEAC</displayName>
			</currency>
			<currency type="XAG">
				<displayName>Silver</displayName>
				<displayName count="one">troy ounce of silver</displayName>
				<displayName count="other">troy ounces of silver</displayName>
			</currency>
			<currency type="XAU">
				<displayName>Gold</displayName>
				<displayName count="one">troy ounce of gold</displayName>
				<displayName count="other">troy ounces of gold</displayName>
			</currency>
			<currency type="XBA">
				<displayName>European Composite Unit</displayName>
				<displayName count="one">European composite unit</displayName>
				<displayName count="other">European composite units</displayName>
			</currency>
			<currency type="XBB">
				<displayName>European Monetary Unit</displayName>
				<displayName count="one">European monetary unit</displayName>
				<displayName count="other">European monetary units</displayName>
			</currency>
			<currency type="XBC">
				<displayName>European Unit of Account (XBC)</displayName>
				<displayName count="one">European unit of account (XBC)</displayName>
				<displayName count="other">European units of account (XBC)</displayName>
			</currency>
			<currency type="XBD">
				<displayName>European Unit of Account (XBD)</displayName>
				<displayName count="one">European unit of account (XBD)</displayName>
				<displayName count="other">European units of account (XBD)</displayName>
			</currency>
			<currency type="XCD">
				<displayName>East Caribbean Dollar</displayName>
				<displayName count="one">East Caribbean dollar</displayName>
				<displayName count="other">East Caribbean dollars</displayName>
			</currency>
			<currency type="XDR">
				<displayName>Special Drawing Rights</displayName>
				<displayName count="one">special drawing rights</displayName>
				<displayName count="other">special drawing rights</displayName>
			</currency>
			<currency type="XEU">
				<displayName>European Currency Unit</displayName>
				<displayName count="one">European currency unit</displayName>
				<displayName count="other">European currency units</displayName>
			</currency>
			<currency type="XFO">
				<displayName>French Gold Franc</displayName>
				<displayName count="one">French gold franc</displayName>
				<displayName count="other">French gold francs</displayName>
			</currency>
			<currency type="XFU">
				<displayName>French UIC-Franc</displayName>
				<displayName count="one">French UIC-franc</displayName>
				<displayName count="other">French UIC-francs</displayName>
			</currency>
			<currency type="XOF">
				<displayName>CFA Franc BCEAO</displayName>
				<displayName count="one">CFA franc BCEAO</displayName>
				<displayName count="other">CFA francs BCEAO</displayName>
			</currency>
			<currency type="XPD">
				<displayName>Palladium</displayName>
				<displayName count="one">troy ounce of palladium</displayName>
				<displayName count="other">troy ounces of palladium</displayName>
			</currency>
			<currency type="XPF">
				<displayName>CFP Franc</displayName>
				<displayName count="one">CFP franc</displayName>
				<displayName count="other">CFP francs</displayName>
			</currency>
			<currency type="XPT">
				<displayName>Platinum</displayName>
				<displayName count="one">troy ounce of platinum</displayName>
				<displayName count="other">troy ounces of platinum</displayName>
			</currency>
			<currency type="XRE">
				<displayName>RINET Funds</displayName>
				<displayName count="one">RINET Funds unit</displayName>
				<displayName count="other">RINET Funds units</displayName>
			</currency>
			<currency type="XSU">
				<displayName>Sucre</displayName>
				<displayName count="one">Sucre</displayName>
				<displayName count="other">Sucres</displayName>
			</currency>
			<currency type="XTS">
				<displayName>Testing Currency Code</displayName>
				<displayName count="one">Testing Currency unit</displayName>
				<displayName count="other">Testing Currency units</displayName>
			</currency>
			<currency type="XUA">
				<displayName>ADB Unit of Account</displayName>
				<displayName count="one">ADB unit of account</displayName>
				<displayName count="other">ADB units of account</displayName>
			</currency>
			<currency type="XXX">
				<displayName>Unknown Currency</displayName>
				<displayName count="one">(unknown unit of currency)</displayName>
				<displayName count="other">(unknown currency)</displayName>
			</currency>
			<currency type="YDD">
				<displayName>Yemeni Dinar</displayName>
				<displayName count="one">Yemeni dinar</displayName>
				<displayName count="other">Yemeni dinars</displayName>
			</currency>
			<currency type="YER">
				<displayName>Yemeni Rial</displayName>
				<displayName count="one">Yemeni rial</displayName>
				<displayName count="other">Yemeni rials</displayName>
			</currency>
			<currency type="YUD">
				<displayName>Yugoslavian Hard Dinar (1966–1990)</displayName>
				<displayName count="one">Yugoslavian hard dinar (1966–1990)</displayName>
				<displayName count="other">Yugoslavian hard dinars (1966–1990)</displayName>
			</currency>
			<currency type="YUM">
				<displayName>Yugoslavian New Dinar (1994–2002)</displayName>
				<displayName count="one">Yugoslavian new dinar (1994–2002)</displayName>
				<displayName count="other">Yugoslavian new dinars (1994–2002)</displayName>
			</currency>
			<currency type="YUN">
				<displayName>Yugoslavian Convertible Dinar (1990–1992)</displayName>
				<displayName count="one">Yugoslavian convertible dinar (1990–1992)</displayName>
				<displayName count="other">Yugoslavian convertible dinars (1990–1992)</displayName>
			</currency>
			<currency type="YUR">
				<displayName>Yugoslavian Reformed Dinar (1992–1993)</displayName>
				<displayName count="one">Yugoslavian reformed dinar (1992–1993)</displayName>
				<displayName count="other">Yugoslavian reformed dinars (1992–1993)</displayName>
			</currency>
			<currency type="ZAL">
				<displayName>South African Rand (financial)</displayName>
				<displayName count="one">South African rand (financial)</displayName>
				<displayName count="other">South African rands (financial)</displayName>
			</currency>
			<currency type="ZAR">
				<displayName>South African Rand</displayName>
				<displayName count="one">South African rand</displayName>
				<displayName count="other">South African rand</displayName>
			</currency>
			<currency type="ZMK">
				<displayName>Zambian Kwacha (1968–2012)</displayName>
				<displayName count="one">Zambian kwacha (1968–2012)</displayName>
				<displayName count="other">Zambian kwachas (1968–2012)</displayName>
			</currency>
			<currency type="ZMW">
				<displayName>Zambian Kwacha</displayName>
				<displayName count="one">Zambian kwacha</displayName>
				<displayName count="other">Zambian kwachas</displayName>
			</currency>
			<currency type="ZRN">
				<displayName>Zairean New Zaire (1993–1998)</displayName>
				<displayName count="one">Zairean new zaire (1993–1998)</displayName>
				<displayName count="other">Zairean new zaires (1993–1998)</displayName>
			</currency>
			<currency type="ZRZ">
				<displayName>Zairean Zaire (1971–1993)</displayName>
				<displayName count="one">Zairean zaire (1971–1993)</displayName>
				<displayName count="other">Zairean zaires (1971–1993)</displayName>
			</currency>
			<currency type="ZWD">
				<displayName>Zimbabwean Dollar (1980–2008)</displayName>
				<displayName count="one">Zimbabwean dollar (1980–2008)</displayName>
				<displayName count="other">Zimbabwean dollars (1980–2008)</displayName>
			</currency>
			<currency type="ZWL">
				<displayName>Zimbabwean Dollar (2009)</displayName>
				<displayName count="one">Zimbabwean dollar (2009)</displayName>
				<displayName count="other">Zimbabwean dollars (2009)</displayName>
			</currency>
			<currency type="ZWR">
				<displayName>Zimbabwean Dollar (2008)</displayName>
				<displayName count="one">Zimbabwean dollar (2008)</displayName>
				<displayName count="other">Zimbabwean dollars (2008)</displayName>
			</currency>
		</currencies>
		<miscPatterns numberSystem="latn">
			<pattern type="atLeast">{0}+</pattern>
		</miscPatterns>
	</numbers>
	<units>
		<unitLength type="long">
			<compoundUnit type="per">
				<compoundUnitPattern>{0} per {1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0} g-force</unitPattern>
				<unitPattern count="other">{0} g-force</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0} arcminute</unitPattern>
				<unitPattern count="other">{0} arcminutes</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0} arcsecond</unitPattern>
				<unitPattern count="other">{0} arcseconds</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0} degree</unitPattern>
				<unitPattern count="other">{0} degrees</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} acre</unitPattern>
				<unitPattern count="other">{0} acres</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0} hectare</unitPattern>
				<unitPattern count="other">{0} hectares</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} square foot</unitPattern>
				<unitPattern count="other">{0} square feet</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one">{0} square kilometer</unitPattern>
				<unitPattern count="other">{0} square kilometers</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one">{0} square meter</unitPattern>
				<unitPattern count="other">{0} square meters</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} square mile</unitPattern>
				<unitPattern count="other">{0} square miles</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0} day</unitPattern>
				<unitPattern count="other">{0} days</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} hour</unitPattern>
				<unitPattern count="other">{0} hours</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} millisecond</unitPattern>
				<unitPattern count="other">{0} milliseconds</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} minute</unitPattern>
				<unitPattern count="other">{0} minutes</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} month</unitPattern>
				<unitPattern count="other">{0} months</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} second</unitPattern>
				<unitPattern count="other">{0} seconds</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} week</unitPattern>
				<unitPattern count="other">{0} weeks</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} year</unitPattern>
				<unitPattern count="other">{0} years</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} centimeter</unitPattern>
				<unitPattern count="other">{0} centimeters</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0} foot</unitPattern>
				<unitPattern count="other">{0} feet</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0} inch</unitPattern>
				<unitPattern count="other">{0} inches</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} kilometer</unitPattern>
				<unitPattern count="other">{0} kilometers</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} light year</unitPattern>
				<unitPattern count="other">{0} light years</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} meter</unitPattern>
				<unitPattern count="other">{0} meters</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} mile</unitPattern>
				<unitPattern count="other">{0} miles</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} millimeter</unitPattern>
				<unitPattern count="other">{0} millimeters</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0} picometer</unitPattern>
				<unitPattern count="other">{0} picometers</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} yard</unitPattern>
				<unitPattern count="other">{0} yards</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} gram</unitPattern>
				<unitPattern count="other">{0} grams</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} kilogram</unitPattern>
				<unitPattern count="other">{0} kilograms</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0} ounce</unitPattern>
				<unitPattern count="other">{0} ounces</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0} pound</unitPattern>
				<unitPattern count="other">{0} pounds</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0} horsepower</unitPattern>
				<unitPattern count="other">{0} horsepower</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0} kilowatt</unitPattern>
				<unitPattern count="other">{0} kilowatts</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0} watt</unitPattern>
				<unitPattern count="other">{0} watts</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0} hectopascal</unitPattern>
				<unitPattern count="other">{0} hectopascals</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0} inch of mercury</unitPattern>
				<unitPattern count="other">{0} inches of mercury</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0} millibar</unitPattern>
				<unitPattern count="other">{0} millibars</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} kilometer per hour</unitPattern>
				<unitPattern count="other">{0} kilometers per hour</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0} meter per second</unitPattern>
				<unitPattern count="other">{0} meters per second</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} mile per hour</unitPattern>
				<unitPattern count="other">{0} miles per hour</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0} degree Celsius</unitPattern>
				<unitPattern count="other">{0} degrees Celsius</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0} degree Fahrenheit</unitPattern>
				<unitPattern count="other">{0} degrees Fahrenheit</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0} cubic kilometer</unitPattern>
				<unitPattern count="other">{0} cubic kilometers</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} cubic mile</unitPattern>
				<unitPattern count="other">{0} cubic miles</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} liter</unitPattern>
				<unitPattern count="other">{0} liters</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="short">
			<compoundUnit type="per">
				<compoundUnitPattern>{0}/{1}</compoundUnitPattern>
			</compoundUnit>
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0} G</unitPattern>
				<unitPattern count="other">{0} G</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0} arcmin</unitPattern>
				<unitPattern count="other">{0} arcmin</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0} arcsec</unitPattern>
				<unitPattern count="other">{0} arcsecs</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0} deg</unitPattern>
				<unitPattern count="other">{0} deg</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0} ac</unitPattern>
				<unitPattern count="other">{0} ac</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0} ha</unitPattern>
				<unitPattern count="other">{0} ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0} sq ft</unitPattern>
				<unitPattern count="other">{0} sq ft</unitPattern>
			</unit>
			<unit type="area-square-kilometer">
				<unitPattern count="one">{0} km²</unitPattern>
				<unitPattern count="other">{0} km²</unitPattern>
			</unit>
			<unit type="area-square-meter">
				<unitPattern count="one">{0} m²</unitPattern>
				<unitPattern count="other">{0} m²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0} sq mi</unitPattern>
				<unitPattern count="other">{0} sq mi</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0} day</unitPattern>
				<unitPattern count="other">{0} days</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0} hr</unitPattern>
				<unitPattern count="other">{0} hrs</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0} ms</unitPattern>
				<unitPattern count="other">{0} ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0} min</unitPattern>
				<unitPattern count="other">{0} mins</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0} mth</unitPattern>
				<unitPattern count="other">{0} mths</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0} sec</unitPattern>
				<unitPattern count="other">{0} secs</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0} wk</unitPattern>
				<unitPattern count="other">{0} wks</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0} yr</unitPattern>
				<unitPattern count="other">{0} yrs</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0} cm</unitPattern>
				<unitPattern count="other">{0} cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0} ft</unitPattern>
				<unitPattern count="other">{0} ft</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0} in</unitPattern>
				<unitPattern count="other">{0} in</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0} km</unitPattern>
				<unitPattern count="other">{0} km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0} ly</unitPattern>
				<unitPattern count="other">{0} ly</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0} m</unitPattern>
				<unitPattern count="other">{0} m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0} mi</unitPattern>
				<unitPattern count="other">{0} mi</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0} mm</unitPattern>
				<unitPattern count="other">{0} mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0} pm</unitPattern>
				<unitPattern count="other">{0} pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0} yd</unitPattern>
				<unitPattern count="other">{0} yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0} g</unitPattern>
				<unitPattern count="other">{0} g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0} kg</unitPattern>
				<unitPattern count="other">{0} kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0} oz</unitPattern>
				<unitPattern count="other">{0} oz</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0} lb</unitPattern>
				<unitPattern count="other">{0} lb</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0} hp</unitPattern>
				<unitPattern count="other">{0} hp</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0} kW</unitPattern>
				<unitPattern count="other">{0} kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0} W</unitPattern>
				<unitPattern count="other">{0} W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0} hPa</unitPattern>
				<unitPattern count="other">{0} hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0} inHg</unitPattern>
				<unitPattern count="other">{0} inHg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0} mbar</unitPattern>
				<unitPattern count="other">{0} mbar</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0} kph</unitPattern>
				<unitPattern count="other">{0} kph</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0} m/s</unitPattern>
				<unitPattern count="other">{0} m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0} mph</unitPattern>
				<unitPattern count="other">{0} mph</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}°C</unitPattern>
				<unitPattern count="other">{0}°C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0}°F</unitPattern>
				<unitPattern count="other">{0}°F</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0} km³</unitPattern>
				<unitPattern count="other">{0} km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0} mi³</unitPattern>
				<unitPattern count="other">{0} mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0} l</unitPattern>
				<unitPattern count="other">{0} l</unitPattern>
			</unit>
		</unitLength>
		<unitLength type="narrow">
			<unit type="acceleration-g-force">
				<unitPattern count="one">{0}G</unitPattern>
				<unitPattern count="other">{0}Gs</unitPattern>
			</unit>
			<unit type="angle-arc-minute">
				<unitPattern count="one">{0}′</unitPattern>
				<unitPattern count="other">{0}′</unitPattern>
			</unit>
			<unit type="angle-arc-second">
				<unitPattern count="one">{0}″</unitPattern>
				<unitPattern count="other">{0}″</unitPattern>
			</unit>
			<unit type="angle-degree">
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="area-acre">
				<unitPattern count="one">{0}ac</unitPattern>
				<unitPattern count="other">{0}ac</unitPattern>
			</unit>
			<unit type="area-hectare">
				<unitPattern count="one">{0}ha</unitPattern>
				<unitPattern count="other">{0}ha</unitPattern>
			</unit>
			<unit type="area-square-foot">
				<unitPattern count="one">{0}ft²</unitPattern>
				<unitPattern count="other">{0}ft²</unitPattern>
			</unit>
			<unit type="area-square-mile">
				<unitPattern count="one">{0}mi²</unitPattern>
				<unitPattern count="other">{0}mi²</unitPattern>
			</unit>
			<unit type="duration-day">
				<unitPattern count="one">{0}d</unitPattern>
				<unitPattern count="other">{0}d</unitPattern>
			</unit>
			<unit type="duration-hour">
				<unitPattern count="one">{0}h</unitPattern>
				<unitPattern count="other">{0}h</unitPattern>
			</unit>
			<unit type="duration-millisecond">
				<unitPattern count="one">{0}ms</unitPattern>
				<unitPattern count="other">{0}ms</unitPattern>
			</unit>
			<unit type="duration-minute">
				<unitPattern count="one">{0}m</unitPattern>
				<unitPattern count="other">{0}m</unitPattern>
			</unit>
			<unit type="duration-month">
				<unitPattern count="one">{0}m</unitPattern>
				<unitPattern count="other">{0}m</unitPattern>
			</unit>
			<unit type="duration-second">
				<unitPattern count="one">{0}s</unitPattern>
				<unitPattern count="other">{0}s</unitPattern>
			</unit>
			<unit type="duration-week">
				<unitPattern count="one">{0}w</unitPattern>
				<unitPattern count="other">{0}w</unitPattern>
			</unit>
			<unit type="duration-year">
				<unitPattern count="one">{0}y</unitPattern>
				<unitPattern count="other">{0}y</unitPattern>
			</unit>
			<unit type="length-centimeter">
				<unitPattern count="one">{0}cm</unitPattern>
				<unitPattern count="other">{0}cm</unitPattern>
			</unit>
			<unit type="length-foot">
				<unitPattern count="one">{0}′</unitPattern>
				<unitPattern count="other">{0}′</unitPattern>
			</unit>
			<unit type="length-inch">
				<unitPattern count="one">{0}″</unitPattern>
				<unitPattern count="other">{0}″</unitPattern>
			</unit>
			<unit type="length-kilometer">
				<unitPattern count="one">{0}km</unitPattern>
				<unitPattern count="other">{0}km</unitPattern>
			</unit>
			<unit type="length-light-year">
				<unitPattern count="one">{0}ly</unitPattern>
				<unitPattern count="other">{0}ly</unitPattern>
			</unit>
			<unit type="length-meter">
				<unitPattern count="one">{0}m</unitPattern>
				<unitPattern count="other">{0}m</unitPattern>
			</unit>
			<unit type="length-mile">
				<unitPattern count="one">{0}mi</unitPattern>
				<unitPattern count="other">{0}mi</unitPattern>
			</unit>
			<unit type="length-millimeter">
				<unitPattern count="one">{0}mm</unitPattern>
				<unitPattern count="other">{0}mm</unitPattern>
			</unit>
			<unit type="length-picometer">
				<unitPattern count="one">{0}pm</unitPattern>
				<unitPattern count="other">{0}pm</unitPattern>
			</unit>
			<unit type="length-yard">
				<unitPattern count="one">{0}yd</unitPattern>
				<unitPattern count="other">{0}yd</unitPattern>
			</unit>
			<unit type="mass-gram">
				<unitPattern count="one">{0}g</unitPattern>
				<unitPattern count="other">{0}g</unitPattern>
			</unit>
			<unit type="mass-kilogram">
				<unitPattern count="one">{0}kg</unitPattern>
				<unitPattern count="other">{0}kg</unitPattern>
			</unit>
			<unit type="mass-ounce">
				<unitPattern count="one">{0}oz</unitPattern>
				<unitPattern count="other">{0}oz</unitPattern>
			</unit>
			<unit type="mass-pound">
				<unitPattern count="one">{0}#</unitPattern>
				<unitPattern count="other">{0}#</unitPattern>
			</unit>
			<unit type="power-horsepower">
				<unitPattern count="one">{0}hp</unitPattern>
				<unitPattern count="other">{0}hp</unitPattern>
			</unit>
			<unit type="power-kilowatt">
				<unitPattern count="one">{0}kW</unitPattern>
				<unitPattern count="other">{0}kW</unitPattern>
			</unit>
			<unit type="power-watt">
				<unitPattern count="one">{0}W</unitPattern>
				<unitPattern count="other">{0}W</unitPattern>
			</unit>
			<unit type="pressure-hectopascal">
				<unitPattern count="one">{0}hPa</unitPattern>
				<unitPattern count="other">{0}hPa</unitPattern>
			</unit>
			<unit type="pressure-inch-hg">
				<unitPattern count="one">{0}&quot; Hg</unitPattern>
				<unitPattern count="other">{0}&quot; Hg</unitPattern>
			</unit>
			<unit type="pressure-millibar">
				<unitPattern count="one">{0}mb</unitPattern>
				<unitPattern count="other">{0}mb</unitPattern>
			</unit>
			<unit type="speed-kilometer-per-hour">
				<unitPattern count="one">{0}kph</unitPattern>
				<unitPattern count="other">{0}kph</unitPattern>
			</unit>
			<unit type="speed-meter-per-second">
				<unitPattern count="one">{0}m/s</unitPattern>
				<unitPattern count="other">{0}m/s</unitPattern>
			</unit>
			<unit type="speed-mile-per-hour">
				<unitPattern count="one">{0}mph</unitPattern>
				<unitPattern count="other">{0}mph</unitPattern>
			</unit>
			<unit type="temperature-celsius">
				<unitPattern count="one">{0}°C</unitPattern>
				<unitPattern count="other">{0}°C</unitPattern>
			</unit>
			<unit type="temperature-fahrenheit">
				<unitPattern count="one">{0}°</unitPattern>
				<unitPattern count="other">{0}°</unitPattern>
			</unit>
			<unit type="volume-cubic-kilometer">
				<unitPattern count="one">{0}km³</unitPattern>
				<unitPattern count="other">{0}km³</unitPattern>
			</unit>
			<unit type="volume-cubic-mile">
				<unitPattern count="one">{0}mi³</unitPattern>
				<unitPattern count="other">{0}mi³</unitPattern>
			</unit>
			<unit type="volume-liter">
				<unitPattern count="one">{0}l</unitPattern>
				<unitPattern count="other">{0}l</unitPattern>
			</unit>
		</unitLength>
	</units>
	<listPatterns>
		<listPattern>
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, and {1}</listPatternPart>
			<listPatternPart type="2">{0} and {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, {1}</listPatternPart>
			<listPatternPart type="2">{0}, {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-narrow">
			<listPatternPart type="start">{0} {1}</listPatternPart>
			<listPatternPart type="middle">{0} {1}</listPatternPart>
			<listPatternPart type="end">{0} {1}</listPatternPart>
			<listPatternPart type="2">{0} {1}</listPatternPart>
		</listPattern>
		<listPattern type="unit-short">
			<listPatternPart type="start">{0}, {1}</listPatternPart>
			<listPatternPart type="middle">{0}, {1}</listPatternPart>
			<listPatternPart type="end">{0}, {1}</listPatternPart>
			<listPatternPart type="2">{0}, {1}</listPatternPart>
		</listPattern>
	</listPatterns>
	<posix>
		<messages>
			<yesstr>yes:y</yesstr>
			<nostr>no:n</nostr>
		</messages>
	</posix>
</ldml>

