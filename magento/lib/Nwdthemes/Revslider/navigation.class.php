<?php
/**
 * @package   Revolution Slider
 * <AUTHOR> <<EMAIL>>
 * @link      http://revolution.themepunch.com/
 * @copyright 2015 ThemePunch
 */
 
class RevSliderNavigation {
	
	public function __construct(){
		
	}
	
	
	public function init_by_id($nav_id){
		if(intval($nav_id) == 0) return false;
		
		$wpdb = Mage::helper('nwdrevslider/query');
		
		$row = $wpdb->get_row($wpdb->prepare("SELECT `id`, `handle`, `type`, `css`, `settings` FROM ".$wpdb->prefix.RevSliderGlobals::TABLE_NAVIGATION_NAME." WHERE `id` = %d", $nav_id), Nwdthemes_Revslider_Helper_Query::ARRAY_A);
		
		return $row;
		
	}
	
	
	/**
	 * Get all Navigations Short
	 * @since: 5.0
	 **/
	public static function get_all_navigations_short(){
		$wpdb = Mage::helper('nwdrevslider/query');
		
		$navigations = $wpdb->get_results("SELECT `id`, `handle`, `name` FROM ".$wpdb->prefix.RevSliderGlobals::TABLE_NAVIGATION_NAME, Nwdthemes_Revslider_Helper_Query::ARRAY_A);
		
		return $navigations;
	}
	
	
	/**
	 * Get all Navigations
	 * @since: 5.0
	 **/
    public static function get_all_navigations($defaults = true, $raw = false){
		$wpdb = Mage::helper('nwdrevslider/query');
		
		$navigations = $wpdb->get_results("SELECT * FROM ".$wpdb->prefix.RevSliderGlobals::TABLE_NAVIGATION_NAME, Nwdthemes_Revslider_Helper_Query::ARRAY_A);
		
        if($raw == false){
            foreach($navigations as $key => $nav){
                $navigations[$key]['css'] = RevSliderBase::stripslashes_deep(json_decode($navigations[$key]['css'], true));
                $navigations[$key]['markup'] = RevSliderBase::stripslashes_deep(json_decode($navigations[$key]['markup'], true));
                if(isset($navigations[$key]['settings'])){
                    $navigations[$key]['settings'] = RevSliderBase::stripslashes_deep(json_decode($navigations[$key]['settings'], true));
                    if(!is_array($navigations[$key]['settings'])){
                        $navigations[$key]['settings'] = json_decode($navigations[$key]['settings'], true);
                    }
                }
			}
		}
		
		if($defaults){
			$def = self::get_default_navigations();
			
            $default_presets = Mage::helper('nwdrevslider/framework')->get_option('revslider-nav-preset-default', array());
            
			if(!empty($def)){
                if($raw == false){
                    foreach($def as $key => $nav){
                        $def[$key]['css'] = RevSliderBase::stripslashes_deep(json_decode($def[$key]['css'], true));
                        $def[$key]['markup'] = RevSliderBase::stripslashes_deep(json_decode($def[$key]['markup'], true));
                        
                        if(isset($def[$key]['settings'])){
                            $def[$key]['settings'] = RevSliderBase::stripslashes_deep(json_decode($def[$key]['settings'], true));
                            if(!is_array($def[$key]['settings'])){
                                $def[$key]['settings'] = json_decode($def[$key]['settings'], true);
                            }
                        }
                        //add custom settings (placeholders) to the default navigation
                        if(!empty($default_presets)){
                            if(!isset($def[$key]['settings'])) $def[$key]['settings'] = array();
                            if(!isset($def[$key]['settings']['presets'])) $def[$key]['settings']['presets'] = array();
                            foreach($default_presets as $id => $v){
                                if($id !== $def[$key]['id']) continue;
                                
                                foreach($v as $values){
                                    $def[$key]['settings']['presets'][] = $values;
                                }
                            }
                        }
                    }
				}
				$navigations = array_merge($navigations, $def);
			}
		}
		
		return $navigations;
	}
	
	
	/**
	 * Creates / Updates Navigation skins
	 * @since: 5.0
	 **/
	public function create_update_full_navigation($data){
		$wpdb = Mage::helper('nwdrevslider/query');
		
		if(!empty($data) && is_array($data)){
			
			$navigations = self::get_all_navigations();
			
			foreach($data as $vals){
				$found = false;
				
				if(!isset($vals['markup']) || !isset($vals['css'])) continue;
				if(isset($vals['default']) && $vals['default'] == true) continue; //defaults can't be deleted
				
				if(isset($vals['id'])){ //new will be added temporary to navs to tell here that they are new
					
					if(intval($vals['id']) === 0) continue; //0
					
					foreach($navigations as $nav){
						if($vals['id'] == $nav['id']){
							$found = true;
							break;
						}
					}
				}
				
				if($found == true){ //update
					self::create_update_navigation($vals, $vals['id']);
				}else{ //create
					self::create_update_navigation($vals);
				}
			}
		}
		
		return true;
	}
	
	/**
	 * Creates / Updates Navigation skins
	 * @since: 5.0
	 **/
	public static function create_update_navigation($data, $nav_id = 0){
		$wpdb = Mage::helper('nwdrevslider/query');
		
		if(isset($data['default']) && $data['default'] == true) return false;
		
		if(!isset($data['settings'])) $data['settings'] = '';
		
		if($nav_id > 0){
			$response = $wpdb->update($wpdb->prefix.RevSliderGlobals::TABLE_NAVIGATION_NAME,
				array(
                    'name' => Mage::helper('nwdrevslider/framework')->esc_attr(stripslashes($data['name'])),
					'handle' => Mage::helper('nwdrevslider/framework')->sanitize_title($data['name']),
					'markup' => json_encode($data['markup']),
					'css' => json_encode($data['css']),
					'settings' => json_encode($data['settings'])
				),
				array('id' => $nav_id)
			);
		}else{
			if(!isset($data['settings'])) $data['settings'] = '';
			$response = $wpdb->insert($wpdb->prefix.RevSliderGlobals::TABLE_NAVIGATION_NAME, array('name' => $data['name'], 'handle' => Mage::helper('nwdrevslider/framework')->sanitize_title($data['name']), 'css' => json_encode($data['css']), 'markup' => json_encode($data['markup']), 'settings' => json_encode($data['settings'])));
		}
		
		return $response;
	}
	
	
	/**
	 * Delete Navigation
	 * @since: 5.0
	 **/
	public function delete_navigation($nav_id = 0){
		$wpdb = Mage::helper('nwdrevslider/query');
		
		if(!isset($nav_id) || intval($nav_id) == 0) return __('Invalid ID');
		
		
		$response = $wpdb->delete($wpdb->prefix.RevSliderGlobals::TABLE_NAVIGATION_NAME, array('id' => $nav_id));
		if($response === false) return __('Navigation could not be deleted');
		
		return true;

	}
	
	
	/**
	 * Get Default Navigation
	 * @since: 5.0
	 **/
	public static function get_default_navigations(){
		$navigations = array();
							
							
		$navigations[] = array(
			'id' => 5000,
			'default' => true,
			'name' => 'Hesperiden',
			'handle' => 'round',
			'markup' => '{"arrows":"","bullets":"","thumbs":"<span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-title\\\\\\">{{title}}<\\/span>","tabs":"<div class=\\\\\\"tp-tab-content\\\\\\">\\n  <span class=\\\\\\"tp-tab-date\\\\\\">{{param1}}<\\/span>\\n  <span class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/span>\\n<\\/div>\\n<div class=\\\\\\"tp-tab-image\\\\\\"><\\/div>"}',
			'css' => '{"arrows":".hesperiden.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:rgba(##bg-color##);\\n\\twidth:##bg-size##px;\\n\\theight:##bg-size##px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n    border-radius: 50%;\\n}\\n.hesperiden.tparrows:hover {\\n\\tbackground:rgba(##hover-bg-color##);\\n}\\n.hesperiden.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:##arrow-size##px;\\n\\tcolor:rgb(##arrow-color##);\\n\\tdisplay:block;\\n\\tline-height: ##bg-size##px;\\n\\ttext-align: center;\\n}\\n.hesperiden.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e82c\\\\\\";\\n    margin-left:-3px;\\n}\\n.hesperiden.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e82d\\\\\\";\\n    margin-right:-3px;\\n}","bullets":".hesperiden.tp-bullets {\\n}\\n.hesperiden.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n   border-radius:8px;\\n  \\n}\\n.hesperiden .tp-bullet {\\n\\twidth:##bullet-size##px;\\n\\theight:##bullet-size##px;\\n\\tposition:absolute;\\n\\tbackground: rgb(##bullet-bg-top##); \\/* old browsers *\\/\\n    background: -moz-linear-gradient(top,  rgb(##bullet-bg-top##) 0%, rgb(##bullet-bg-bottom##) 100%); \\/* ff3.6+ *\\/\\n    background: -webkit-linear-gradient(top,  rgb(##bullet-bg-top##) 0%,rgb(##bullet-bg-bottom##) 100%); \\/* chrome10+,safari5.1+ *\\/\\n    background: -o-linear-gradient(top,  rgb(##bullet-bg-top##) 0%,rgb(##bullet-bg-bottom##) 100%); \\/* opera 11.10+ *\\/\\n    background: -ms-linear-gradient(top,  rgb(##bullet-bg-top##) 0%,rgb(##bullet-bg-bottom##) 100%); \\/* ie10+ *\\/\\n    background: linear-gradient(to bottom,  rgb(##bullet-bg-top##) 0%,rgb(##bullet-bg-bottom##) 100%); \\/* w3c *\\/\\n    filter: progid:dximagetransform.microsoft.gradient( \\n    startcolorstr=\\\\\\"rgb(##bullet-bg-top##)\\\\\\", endcolorstr=\\\\\\"rgb(##bullet-bg-bottom##)\\\\\\",gradienttype=0 ); \\/* ie6-9 *\\/\\n\\tborder:##border-size##px solid rgb(##border-color##);\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.hesperiden .tp-bullet:hover,\\n.hesperiden .tp-bullet.selected {\\n\\tbackground:rgb(##hover-bullet-bg##);\\n}\\n.hesperiden .tp-bullet-image {\\n}\\n.hesperiden .tp-bullet-title {\\n}\\n","thumbs":".hesperiden .tp-thumb {\\n  opacity:1;\\n  -webkit-perspective: 600px;\\n  perspective: 600px;\\n}\\n.hesperiden .tp-thumb .tp-thumb-title {\\n    font-size:##title-font-size##px;\\n    position:absolute;\\n    margin-top:-10px;\\n    color:rgba(##title-color##);\\n    display:block;\\n    z-index:1000;\\n    background-color:rgba(##title-bg##);\\n    padding:5px 10px; \\n    bottom:0px;\\n    left:0px;\\n    width:100%;\\n  box-sizing:border-box;\\n    text-align:center;\\n    overflow:hidden;\\n    white-space:nowrap;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    transform:rotatex(90deg) translatez(0.001px);\\n    transform-origin:50% 100%;\\n    -webkit-transform:rotatex(90deg) translatez(0.001px);\\n    -webkit-transform-origin:50% 100%;\\n    opacity:0;\\n }\\n.hesperiden .tp-thumb:hover .tp-thumb-title {\\n  \\t transform:rotatex(0deg);\\n    -webkit-transform:rotatex(0deg);\\n    opacity:1;\\n}","tabs":".hesperiden .tp-tab { \\n  opacity:1;      \\n  padding:10px;\\n  box-sizing:border-box;\\n  font-family: \\\\\\"##font-family##\\\\\\", sans-serif;\\n  border-bottom: ##border-size##px solid rgba(##border-color##);\\n }\\n.hesperiden .tp-tab-image \\n{ \\n  width:##image-size##px;\\n  height:##image-size##px; max-height:100%; max-width:100%;\\n  position:relative;\\n  display:inline-block;\\n  float:left;\\n\\n}\\n.hesperiden .tp-tab-content \\n{\\n    background:rgba(##bgcolor##); \\n    position:relative;\\n    padding:15px 15px 15px 85px;\\n left:0px;\\n overflow:hidden;\\n margin-top:-15px;\\n    box-sizing:border-box;\\n    color:rgba(##contentcolor##);\\n    display: inline-block;\\n    width:100%;\\n    height:100%;\\n position:absolute; }\\n.hesperiden .tp-tab-date\\n  {\\n  display:block;\\n  color: rgba(##param1-color##);\\n  font-weight:500;\\n  font-size:##param1-size##px;\\n  margin-bottom:0px;\\n  }\\n.hesperiden .tp-tab-title \\n{\\n    display:block;\\t\\n    text-align:left\\t;\\n    color:rgba(##param2-color##);\\n    font-size:##param2-size##px;\\n    font-weight:500;\\n    text-transform:none;\\n    line-height:17px;\\n}\\n.hesperiden .tp-tab:hover,\\n.hesperiden .tp-tab.selected {\\n\\tbackground:rgba(##hover-bg-color##); \\n}\\n\\n.hesperiden .tp-tab-mask {\\n}\\n\\n\\/* media queries *\\/\\n@media only screen and (max-width: 960px) {\\n\\n}\\n@media only screen and (max-width: 768px) {\\n\\n}"}',
			'settings' => '{"width":{"thumbs":"160","arrows":"160","bullets":"160","tabs":"250"},"height":{"thumbs":"160","arrows":"160","bullets":"160","tabs":"80"},"original":{"css":{"arrows":".hesperiden.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:#000;\\n\\tbackground:rgba(0,0,0,0.5);\\n\\twidth:40px;\\n\\theight:40px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n    border-radius: 50%;\\n}\\n.hesperiden.tparrows:hover {\\n\\tbackground:#000;\\n}\\n.hesperiden.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:20px;\\n\\tcolor:#fff;\\n\\tdisplay:block;\\n\\tline-height: 40px;\\n\\ttext-align: center;\\n}\\n.hesperiden.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e82c\\\\\\";\\n    margin-left:-3px;\\n}\\n.hesperiden.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e82d\\\\\\";\\n    margin-right:-3px;\\n}","bullets":".hesperiden.tp-bullets {\\n}\\n.hesperiden.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n   border-radius:8px;\\n  \\n}\\n.hesperiden .tp-bullet {\\n\\twidth:12px;\\n\\theight:12px;\\n\\tposition:absolute;\\n\\tbackground: #999999; \\/* old browsers *\\/\\n    background: -moz-linear-gradient(top,  #999999 0%, #e1e1e1 100%); \\/* ff3.6+ *\\/\\n    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#999999), \\n    color-stop(100%,#e1e1e1)); \\/* chrome,safari4+ *\\/\\n    background: -webkit-linear-gradient(top,  #999999 0%,#e1e1e1 100%); \\/* chrome10+,safari5.1+ *\\/\\n    background: -o-linear-gradient(top,  #999999 0%,#e1e1e1 100%); \\/* opera 11.10+ *\\/\\n    background: -ms-linear-gradient(top,  #999999 0%,#e1e1e1 100%); \\/* ie10+ *\\/\\n    background: linear-gradient(to bottom,  #999999 0%,#e1e1e1 100%); \\/* w3c *\\/\\n    filter: progid:dximagetransform.microsoft.gradient( \\n    startcolorstr=\\\\\\"#999999\\\\\\", endcolorstr=\\\\\\"#e1e1e1\\\\\\",gradienttype=0 ); \\/* ie6-9 *\\/\\n\\tborder:3px solid #e5e5e5;\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.hesperiden .tp-bullet:hover,\\n.hesperiden .tp-bullet.selected {\\n\\tbackground:#666;\\n}\\n.hesperiden .tp-bullet-image {\\n}\\n.hesperiden .tp-bullet-title {\\n}\\n","thumbs":".hesperiden .tp-thumb {\\n  opacity:1;\\n  -webkit-perspective: 600px;\\n  perspective: 600px;\\n}\\n.hesperiden .tp-thumb .tp-thumb-title {\\n    font-size:12px;\\n    position:absolute;\\n    margin-top:-10px;\\n    color:#fff;\\n    display:block;\\n    z-index:1000;\\n    background-color:#000;\\n    padding:5px 10px; \\n    bottom:0px;\\n    left:0px;\\n    width:100%;\\n  box-sizing:border-box;\\n    text-align:center;\\n    overflow:hidden;\\n    white-space:nowrap;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    transform:rotatex(90deg) translatez(0.001px);\\n    transform-origin:50% 100%;\\n    -webkit-transform:rotatex(90deg) translatez(0.001px);\\n    -webkit-transform-origin:50% 100%;\\n    opacity:0;\\n }\\n.hesperiden .tp-thumb:hover .tp-thumb-title {\\n  \\t transform:rotatex(0deg);\\n    -webkit-transform:rotatex(0deg);\\n    opacity:1;\\n}","tabs":".hesperiden .tp-tab { \\n  opacity:1;      \\n  padding:10px;\\n  box-sizing:border-box;\\n  font-family: \\\\\\"Roboto\\\\\\", sans-serif;\\n  border-bottom: 1px solid #e5e5e5;\\n }\\n.hesperiden .tp-tab-image \\n{ \\n  width:60px;\\n  height:60px; max-height:100%; max-width:100%;\\n  position:relative;\\n  display:inline-block;\\n  float:left;\\n\\n}\\n.hesperiden .tp-tab-content \\n{\\n    background:rgba(0,0,0,0); \\n    position:relative;\\n    padding:15px 15px 15px 85px;\\n left:0px;\\n overflow:hidden;\\n margin-top:-15px;\\n    box-sizing:border-box;\\n    color:#333;\\n    display: inline-block;\\n    width:100%;\\n    height:100%;\\n position:absolute; }\\n.hesperiden .tp-tab-date\\n  {\\n  display:block;\\n  color: #aaa;\\n  font-weight:500;\\n  font-size:12px;\\n  margin-bottom:0px;\\n  }\\n.hesperiden .tp-tab-title \\n{\\n    display:block;\\t\\n    text-align:left;\\n    color:#333;\\n    font-size:14px;\\n    font-weight:500;\\n    text-transform:none;\\n    line-height:17px;\\n}\\n.hesperiden .tp-tab:hover,\\n.hesperiden .tp-tab.selected {\\n\\tbackground:#eee; \\n}\\n\\n.hesperiden .tp-tab-mask {\\n}\\n\\n\\/* MEDIA QUERIES *\\/\\n@media only screen and (max-width: 960px) {\\n\\n}\\n@media only screen and (max-width: 768px) {\\n\\n}"},"markup":{"arrows":"","bullets":"","thumbs":"<span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-title\\\\\\">{{title}}<\\/span>","tabs":"<div class=\\\\\\"tp-tab-content\\\\\\">\\n  <span class=\\\\\\"tp-tab-date\\\\\\">{{param1}}<\\/span>\\n  <span class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/span>\\n<\\/div>\\n<div class=\\\\\\"tp-tab-image\\\\\\"><\\/div>"}},"placeholders":[{"title":"BG-Color","handle":"bg-color","type":"custom","nav-type":"arrows","data":{"custom":"0,0,0,0.5"}},{"title":"BG-Size","handle":"bg-size","type":"custom","nav-type":"arrows","data":{"custom":"40"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"arrows","data":{"custom":"20"}},{"title":"Hover-BG-Color","handle":"hover-bg-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#000000"}},{"title":"Bullet-Size","handle":"bullet-size","type":"custom","nav-type":"bullets","data":{"custom":"12"}},{"title":"Bullet-BG-Top","handle":"bullet-bg-top","type":"color","nav-type":"bullets","data":{"color":"#999999"}},{"title":"Bullet-BG-Bottom","handle":"bullet-bg-bottom","type":"color","nav-type":"bullets","data":{"color":"#e1e1e1"}},{"title":"Border-Color","handle":"border-color","type":"color","nav-type":"bullets","data":{"color":"#e5e5e5"}},{"title":"Border-Size","handle":"border-size","type":"custom","nav-type":"bullets","data":{"custom":"3"}},{"title":"Hover-Bullet-BG","handle":"hover-bullet-bg","type":"color","nav-type":"bullets","data":{"color":"#666666"}},{"title":"Title-BG-Color","handle":"title-bg","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"rgba(0,0,0,0.85)"}},{"title":"Title-Font-Color","handle":"title-color","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"#ffffff"}},{"title":"Title-Font-Size","handle":"title-font-size","type":"custom","nav-type":"thumbs","data":{"custom":"12"}},{"title":"Font-Family","handle":"font-family","type":"font-family","nav-type":"tabs","data":{"font_family":"Roboto"}},{"title":"Border-Bottom-Color","handle":"border-color","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#e5e5e5"}},{"title":"Border-Bottom-Size","handle":"border-size","type":"custom","nav-type":"tabs","data":{"custom":"1"}},{"title":"Image-Size","handle":"image-size","type":"custom","nav-type":"tabs","data":{"custom":"60"}},{"title":"Date-Color","handle":"param1-color","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(51,51,51,0.5)"}},{"title":"Date-Size","handle":"param1-size","type":"custom","nav-type":"tabs","data":{"custom":"12"}},"","",{"title":"Hover-BG-Color","handle":"hover-bg-color","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#eeeeee"}},{"title":"Background","handle":"bgcolor","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(0,0,0,0)"}},{"title":"Content","handle":"contentcolor","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#333333"}},{"title":"Title-Color","handle":"param2-color","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"0,0,0,0"}},{"title":"Title-Size","handle":"param2-size","type":"custom","nav-type":"tabs","data":{"custom":"14"}}]}');
			
		$navigations[] = array(
			'id' => 5001,
			'default' => true,
			'name' => 'Gyges',
			'handle' => 'navbar',
			'markup' => '{"arrows":"","bullets":"","tabs":"<div class=\\\\\\"tp-tab-content\\\\\\">\\n  <span class=\\\\\\"tp-tab-date\\\\\\">{{param1}}<\\/span>\\n  <span class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/span>\\n<\\/div>\\n<div class=\\\\\\"tp-tab-image\\\\\\"><\\/div>","thumbs":"<span class=\\\\\\"tp-thumb-img-wrap\\\\\\">\\n  <span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<\\/span>\\n"}',
			'css' => '{"arrows":"","bullets":".gyges.tp-bullets {\\n}\\n.gyges.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n    background: -moz-linear-gradient(top,  rgba(##bgtop##) 0%, rgba(##bgbottom##) 100%); \\n    background: -webkit-linear-gradient(top,  rgba(##bgtop##) 0%,rgba(##bgbottom##) 100%); \\n    background: -o-linear-gradient(top,  rgba(##bgtop##) 0%,rgba(##bgbottom##) 100%); \\n    background: -ms-linear-gradient(top,  rgba(##bgtop##) 0%,rgba(##bgbottom##) 100%); \\n    background: linear-gradient(to bottom,  rgba(##bgtop##) 0%, rgba(##bgbottom##) 100%); \\n    filter: progid:dximagetransform.microsoft.gradient( startcolorstr=\\\\\\"rgba(##bgtop##)\\\\\\", \\n    endcolorstr=\\\\\\"rgba(##bgbottom##)\\\\\\",gradienttype=0 ); \\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n  border-radius:10px;\\n}\\n.gyges .tp-bullet {\\n\\twidth:12px;\\n\\theight:12px;\\n\\tposition:absolute;\\n\\tbackground:rgba(##bulletbg##);\\n\\tborder:3px solid rgba(##bordercolor##);\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.gyges .tp-bullet:hover,\\n.gyges .tp-bullet.selected {\\n\\n    background: -moz-linear-gradient(top,  rgba(##hbgtop##) 0%, rgba(##hbgbottom##) 100%); \\/* ff3.6+ *\\/\\n    background: -webkit-linear-gradient(top,  rgba(##hbgtop##) 0%,rgba(##hbgbottom##) 100%); \\/* chrome10+,safari5.1+ *\\/\\n    background: -o-linear-gradient(top,  rgba(##hbgtop##) 0%,rgba(##hbgbottom##) 100%); \\/* opera 11.10+ *\\/\\n    background: -ms-linear-gradient(top,  rgba(##hbgtop##) 0%,rgba(##hbgbottom##) 100%); \\/* ie10+ *\\/\\n    background: linear-gradient(to bottom,  rgba(##hbgtop##) 0%,rgba(##hbgbottom##) 100%); \\/* w3c *\\/\\n    filter: progid:dximagetransform.microsoft.gradient( startcolorstr=\\\\\\"rgba(##hbgtop##)\\\\\\", \\n    endcolorstr=\\\\\\"rgba(##hbgbottom##)\\\\\\",gradienttype=0 ); \\/* ie6-9 *\\/\\n\\n}\\n.gyges .tp-bullet-image {\\n}\\n.gyges .tp-bullet-title {\\n}\\n\\t","tabs":".gyges .tp-tab { \\n  opacity:1;      \\n  padding:10px;\\n  box-sizing:border-box;\\n  font-family: \\\\\\"roboto\\\\\\", sans-serif;\\n  border-bottom: 1px solid rgba(##borderc##);\\n }\\n.gyges .tp-tab-image \\n{ \\n  width:##size##px;\\n  height:##size##px; \\n  max-height:100%; \\n  max-width:100%;\\n  position:relative;\\n  display:inline-block;\\n  float:left;\\n\\n}\\n.gyges .tp-tab-content \\n{\\n    background:rgba(##bg##); \\n    position:relative;\\n    padding:15px 15px 15px 85px;\\n    left:0px;\\n    overflow:hidden;\\n    margin-top:-15px;\\n    box-sizing:border-box;\\n    color:rgba(##color##);\\n    display: inline-block;\\n    width:100%;\\n    height:100%;\\n position:absolute; }\\n.gyges .tp-tab-date\\n  {\\n  display:block;\\n  color: rgba(##datecolor##);\\n  font-weight:500;\\n  font-size:##datesize##px;\\n  margin-bottom:0px;\\n  }\\n.gyges .tp-tab-title \\n{\\n    display:block;  \\n    text-align:left;\\n    color:rgba(##titlecolor##);\\n    font-size:##titlesize##px;\\n    font-weight:500;\\n    text-transform:none;\\n    line-height:17px;\\n}\\n.gyges .tp-tab:hover,\\n.gyges .tp-tab.selected {\\n  background:rgba(##hbg##); \\n}\\n\\n.gyges .tp-tab-mask {\\n}\\n\\n\\/* media queries *\\/\\n@media only screen and (max-width: 960px) {\\n\\n}\\n@media only screen and (max-width: 768px) {\\n\\n}","thumbs":".gyges .tp-thumb { \\n      opacity:1\\n  }\\n.gyges .tp-thumb-img-wrap {\\n  padding:3px;\\n  background-color:rgba(##bg##);\\n  display:inline-block;\\n\\n  width:100%;\\n  height:100%;\\n  position:relative;\\n  margin:0px;\\n  box-sizing:border-box;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n}\\n.gyges .tp-thumb-image {\\n   padding:3px; \\n   display:block;\\n   box-sizing:border-box;\\n   position:relative;\\n    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(##bg##);\\n  -moz-box-shadow: inset 5px 5px 10px 0px rgba(##bg##);\\n  box-shadow: inset 5px 5px 10px 0px rgba(##bg##);\\n }  \\n\\n.gyges .tp-thumb:hover .tp-thumb-img-wrap,\\n .gyges .tp-thumb.selected .tp-thumb-img-wrap {\\n    background: -moz-linear-gradient(top,  rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(##hovercolor##)), color-stop(100%, rgba(##hbgb##)));\\nbackground: -webkit-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -o-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -ms-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: linear-gradient(to bottom, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\n\\n}\\n\\n"}',
			'settings' => '{"width":{"thumbs":"70","arrows":"160","bullets":"160","tabs":"300"},"height":{"thumbs":"70","arrows":"160","bullets":"160","tabs":"80"},"original":{"css":{"arrows":"","bullets":".gyges.tp-bullets {\\n}\\n.gyges.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground: #777777; \\/* Old browsers *\\/\\n    background: -moz-linear-gradient(top,  #777777 0%, #666666 100%); \\n    background: -webkit-gradient(linear, left top, left bottom, \\n    color-stop(0%,#777777), color-stop(100%,#666666)); \\n    background: -webkit-linear-gradient(top,  #777777 0%,#666666 100%); \\n    background: -o-linear-gradient(top,  #777777 0%,#666666 100%); \\n    background: -ms-linear-gradient(top,  #777777 0%,#666666 100%); \\n    background: linear-gradient(to bottom,  #777777 0%,#666666 100%); \\n    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\\\\\\"#777777\\\\\\", \\n    endColorstr=\\\\\\"#666666\\\\\\",GradientType=0 ); \\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n  border-radius:10px;\\n}\\n.gyges .tp-bullet {\\n\\twidth:12px;\\n\\theight:12px;\\n\\tposition:absolute;\\n\\tbackground:#333;\\n\\tborder:3px solid #444;\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.gyges .tp-bullet:hover,\\n.gyges .tp-bullet.selected {\\n\\tbackground: #ffffff; \\/* Old browsers *\\/\\n    background: -moz-linear-gradient(top,  #ffffff 0%, #e1e1e1 100%); \\/* FF3.6+ *\\/\\n    background: -webkit-gradient(linear, left top, left bottom, \\n    color-stop(0%,#ffffff), color-stop(100%,#e1e1e1)); \\/* Chrome,Safari4+ *\\/\\n    background: -webkit-linear-gradient(top,  #ffffff 0%,#e1e1e1 100%); \\/* Chrome10+,Safari5.1+ *\\/\\n    background: -o-linear-gradient(top,  #ffffff 0%,#e1e1e1 100%); \\/* Opera 11.10+ *\\/\\n    background: -ms-linear-gradient(top,  #ffffff 0%,#e1e1e1 100%); \\/* IE10+ *\\/\\n    background: linear-gradient(to bottom,  #ffffff 0%,#e1e1e1 100%); \\/* W3C *\\/\\n    filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\\\\\\"#ffffff\\\\\\", \\n    endColorstr=\\\\\\"#e1e1e1\\\\\\",GradientType=0 ); \\/* IE6-9 *\\/\\n\\n}\\n.gyges .tp-bullet-image {\\n}\\n.gyges .tp-bullet-title {\\n}\\n\\t","tabs":".gyges .tp-tab { \\n  opacity:1;      \\n  padding:10px;\\n  box-sizing:border-box;\\n  font-family: \\\\\\"Roboto\\\\\\", sans-serif;\\n  border-bottom: 1px solid rgba(255,255,255,0.15);\\n }\\n.gyges .tp-tab-image \\n{ \\n  width:60px;\\n  height:60px; max-height:100%; max-width:100%;\\n  position:relative;\\n  display:inline-block;\\n  float:left;\\n\\n}\\n.gyges .tp-tab-content \\n{\\n    background:rgba(0,0,0,0); \\n    position:relative;\\n    padding:15px 15px 15px 85px;\\n left:0px;\\n  overflow:hidden;\\n margin-top:-15px;\\n    box-sizing:border-box;\\n    color:#333;\\n    display: inline-block;\\n    width:100%;\\n    height:100%;\\n position:absolute; }\\n.gyges .tp-tab-date\\n  {\\n  display:block;\\n  color: rgba(255,255,255,0.25);\\n  font-weight:500;\\n  font-size:12px;\\n  margin-bottom:0px;\\n  }\\n.gyges .tp-tab-title \\n{\\n    display:block;  \\n    text-align:left;\\n    color:#fff;\\n    font-size:14px;\\n    font-weight:500;\\n    text-transform:none;\\n    line-height:17px;\\n}\\n.gyges .tp-tab:hover,\\n.gyges .tp-tab.selected {\\n  background:rgba(0,0,0,0.5); \\n}\\n\\n.gyges .tp-tab-mask {\\n}\\n\\n\\/* MEDIA QUERIES *\\/\\n@media only screen and (max-width: 960px) {\\n\\n}\\n@media only screen and (max-width: 768px) {\\n\\n}","thumbs":".gyges .tp-thumb { \\n      opacity:1\\n  }\\n.gyges .tp-thumb-img-wrap {\\n  padding:3px;\\n    background:#000;\\n  background-color:rgba(0,0,0,0.25);\\n  display:inline-block;\\n\\n  width:100%;\\n  height:100%;\\n  position:relative;\\n  margin:0px;\\n  box-sizing:border-box;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n}\\n.gyges .tp-thumb-image {\\n   padding:3px; \\n   display:block;\\n   box-sizing:border-box;\\n   position:relative;\\n    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n }  \\n.gyges .tp-thumb-title { \\n     position:absolute; \\n     bottom:100%; \\n     display:inline-block;\\n     left:50%;\\n     background:rgba(255,255,255,0.8);\\n     padding:10px 30px;\\n     border-radius:4px;\\n\\t -webkit-border-radius:4px;\\n     margin-bottom:20px;\\n     opacity:0;\\n      transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    transform: translateZ(0.001px) translateX(-50%) translateY(14px);\\n    transform-origin:50% 100%;\\n    -webkit-transform: translateZ(0.001px) translateX(-50%) translateY(14px);\\n    -webkit-transform-origin:50% 100%;\\n    white-space:nowrap;\\n }\\n.gyges .tp-thumb:hover .tp-thumb-title {\\n  \\t transform:rotateX(0deg) translateX(-50%);\\n    -webkit-transform:rotateX(0deg) translateX(-50%);\\n    opacity:1;\\n}\\n\\n.gyges .tp-thumb:hover .tp-thumb-img-wrap,\\n .gyges .tp-thumb.selected .tp-thumb-img-wrap {\\n\\n  background: rgba(255,255,255,1);\\n  background: -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(255,255,255,1)), color-stop(100%, rgba(119,119,119,1)));\\n  background: -webkit-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -o-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -ms-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\\\\\\"#ffffff\\\\\\", endColorstr=\\\\\\"#777777\\\\\\", GradientType=0 );\\n }\\n.gyges .tp-thumb-title:after {\\n        content:\\\\\\" \\\\\\";\\n        position:absolute;\\n        left:50%;\\n        margin-left:-8px;\\n        width: 0;\\n\\t\\theight: 0;\\n\\t\\tborder-style: solid;\\n\\t\\tborder-width: 8px 8px 0 8px;\\n\\t\\tborder-color: rgba(255,255,255,0.8) transparent transparent transparent;\\n        bottom:-8px;\\n   }\\n"},"markup":{"arrows":"","bullets":"","tabs":"<div class=\\\\\\"tp-tab-content\\\\\\">\\n  <span class=\\\\\\"tp-tab-date\\\\\\">{{param1}}<\\/span>\\n  <span class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/span>\\n<\\/div>\\n<div class=\\\\\\"tp-tab-image\\\\\\"><\\/div>","thumbs":"<span class=\\\\\\"tp-thumb-img-wrap\\\\\\">\\n  <span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<\\/span>\\n"}},"placeholders":[{"title":"BG-Top","handle":"bgtop","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#777777"}},{"title":"BG-Bottom","handle":"bgbottom","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#666666"}},{"title":"Border","handle":"bordercolor","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#444444"}},{"title":"Bullet-BG","handle":"bulletbg","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#333333"}},{"title":"Hover-BG-Top","handle":"hbgtop","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#ffffff"}},{"title":"Hover-BG-Bottom","handle":"hbgbottom","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#e0e0e0"}},{"title":"Background","handle":"bg","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"rgba(0,0,0,0.25)"}},{"title":"Title-Background","handle":"titlebg","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"rgba(255,255,255,0.81)"}},{"title":"Hover-Top","handle":"hovercolor","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"#ffffff"}},{"title":"Border-Color","handle":"borderc","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(255,255,255,0.15)"}},{"title":"Size","handle":"size","type":"custom","nav-type":"tabs","data":{"custom":"60"}},{"title":"Background","handle":"bg","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(0,0,0,0)"}},{"title":"Content-Color","handle":"color","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(51,51,51,0)"}},{"title":"Date-Color","handle":"datecolor","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(255,255,255,0.5)"}},{"title":"Date-Size","handle":"datesize","type":"custom","nav-type":"tabs","data":{"custom":"12"}},{"title":"Title-Color","handle":"titlecolor","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#ffffff"}},{"title":"Title-Size","handle":"titlesize","type":"custom","nav-type":"tabs","data":{"custom":"14"}},{"title":"Hover-Background","handle":"hbg","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(0,0,0,0.51)"}},{"title":"Hover-Bottom","handle":"hbgb","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"#777777"}}]}');
			
		$navigations[] = array(
			'id' => 5002,
			'default' => true,
			'name' => 'Hades',
			'handle' => 'preview1',
			'markup' => '{"arrows":"<div class=\\\\\\"tp-arr-allwrapper\\\\\\">\\n\\t<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n<\\/div>","bullets":"<span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>","tabs":"<div class=\\\\\\"tp-tab-inner\\\\\\">\\n  <span class=\\\\\\"tp-tab-title\\\\\\">{{param1}}<\\/span>\\n  <span class=\\\\\\"tp-tab-price\\\\\\">{{param2}}<\\/span>\\n  <span class=\\\\\\"tp-tab-button\\\\\\">{{param3}}<\\/span>\\n<\\/div>","thumbs":"<span class=\\\\\\"tp-thumb-img-wrap\\\\\\">\\n  <span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<\\/span>\\n"}',
			'css' => '{"arrows":".hades.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:rgba(##bg##);\\n\\twidth:100px;\\n\\theight:100px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n}\\n\\n.hades.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:30px;\\n\\tcolor:rgba(##acolor##);\\n\\tdisplay:block;\\n\\tline-height: 100px;\\n\\ttext-align: center;\\n  transition: background 0.3s, color 0.3s;\\n}\\n.hades.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e824\\\\\\";\\n}\\n.hades.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e825\\\\\\";\\n}\\n\\n.hades.tparrows:hover:before {\\n   color:rgba(##harrow##);\\n   background:rgba(##hbg##);\\n }\\n.hades .tp-arr-allwrapper {\\n  position:absolute;\\n  left:100%;\\n  top:0px;\\n  background:#888; \\n  width:100px;height:100px;\\n  -webkit-transition: all 0.3s;\\n  transition: all 0.3s;\\n  -ms-filter: \\\\\\"progid:dximagetransform.microsoft.alpha(opacity=0)\\\\\\";\\n  filter: alpha(opacity=0);\\n  -moz-opacity: 0.0;\\n  -khtml-opacity: 0.0;\\n  opacity: 0.0;\\n  -webkit-transform: rotatey(-90deg);\\n  transform: rotatey(-90deg);\\n  -webkit-transform-origin: 0% 50%;\\n  transform-origin: 0% 50%;\\n}\\n.hades.tp-rightarrow .tp-arr-allwrapper {\\n   left:auto;\\n   right:100%;\\n   -webkit-transform-origin: 100% 50%;\\n  transform-origin: 100% 50%;\\n   -webkit-transform: rotatey(90deg);\\n  transform: rotatey(90deg);\\n}\\n\\n.hades:hover .tp-arr-allwrapper {\\n   -ms-filter: \\\\\\"progid:dximagetransform.microsoft.alpha(opacity=100)\\\\\\";\\n  filter: alpha(opacity=100);\\n  -moz-opacity: 1;\\n  -khtml-opacity: 1;\\n  opacity: 1;  \\n    -webkit-transform: rotatey(0deg);\\n  transform: rotatey(0deg);\\n\\n }\\n    \\n.hades .tp-arr-iwrapper {\\n}\\n.hades .tp-arr-imgholder {\\n  background-size:cover;\\n  position:absolute;\\n  top:0px;left:0px;\\n  width:100%;height:100%;\\n}\\n.hades .tp-arr-titleholder {\\n}\\n.hades .tp-arr-subtitleholder {\\n}\\n","bullets":".hades.tp-bullets {\\n}\\n.hades.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n}\\n.hades .tp-bullet {\\n\\twidth:##innersize##px;\\n\\theight:##innersize##px;\\n\\tposition:absolute;\\n\\tbackground:rgba(##colorinner##);\\n\\tcursor: pointer;\\n    border:##outersize##px solid rgba(##outercolor##);\\n\\tbox-sizing:content-box;\\n    box-shadow:0px 0px 3px 1px rgba(0,0,0,0.2);\\n    -webkit-perspective:400;\\n    perspective:400;\\n    -webkit-transform:translatez(0.01px);\\n    transform:translatez(0.01px);\\n}\\n.hades .tp-bullet:hover,\\n.hades .tp-bullet.selected {\\n\\tbackground:rgba(##innerhover##);\\n    border-color:rgba(##outerhover##);\\n}\\n\\n.hades .tp-bullet-image {\\n  position:absolute;\\n  top:##voffset##px; \\n  left:##hoffset##;\\n  width:##width##px;\\n  height:##height##px;\\n  background-position:center center;\\n  background-size:cover;\\n  visibility:hidden;\\n  opacity:0;\\n  transition:all 0.3s;\\n  -webkit-transform-style:flat;\\n  transform-style:flat;\\n  perspective:600;\\n  -webkit-perspective:600;\\n  transform: rotatex(-90deg) translatex(-50%);\\n  -webkit-transform: rotatex(-90deg) translate(-50%);\\n  box-shadow:0px 0px 3px 1px rgba(0,0,0,0.2);\\n  transform-origin:50% 100%;\\n  -webkit-transform-origin:50% 100%;\\n  \\n  \\n}\\n.hades .tp-bullet:hover .tp-bullet-image {\\n  display:block;\\n  opacity:1;\\n  transform: rotatex(0deg) translatex(-50%);\\n  -webkit-transform: rotatex(0deg) translatex(-50%);\\n  visibility:visible;\\n    }\\n.hades .tp-bullet-title {\\n}\\n","tabs":".hades .tp-tab {\\n  opacity:1;\\n }\\n    \\n.hades .tp-tab-title\\n {\\n      display:block;\\n      color:rgba(##param1##);\\n      font-weight:600;\\n      font-size:##param1size##px;\\n      text-align:center;\\n      line-height:25px;      \\n    } \\n.hades .tp-tab-price\\n {\\n\\tdisplay:block;\\n    text-align:center;\\n    color:rgba(##param2##);\\n    font-size:##p2size##px;\\n    margin-top:10px;\\n   line-height:20px\\n}\\n\\n.hades .tp-tab-button {\\n    display:inline-block;\\n    margin-top:15px;\\n    text-align:center;\\n\\tpadding:5px 15px;\\n  \\tcolor:rgba(##p3##);\\n  \\tfont-size:##p3size##px;\\n  \\tbackground:rgba(##p3bg##);\\n   \\tborder-radius:4px;\\n   font-weight:400;\\n}\\n.hades .tp-tab-inner {\\n\\ttext-align:center;\\n}\\n\\n              ","thumbs":".hades .tp-thumb { \\n      opacity:1\\n  }\\n.hades .tp-thumb-img-wrap {\\n  border-radius:##radius##;\\n  padding:##border##px;\\n  display:inline-block;\\n  background-color:rgba(##bg##);\\n  width:100%;\\n  height:100%;\\n  position:relative;\\n  margin:0px;\\n  box-sizing:border-box;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n}\\n.hades .tp-thumb-image {\\n   padding:##border##px; \\n   border-radius:##radius##;\\n   display:block;\\n   box-sizing:border-box;\\n   position:relative;\\n    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n }  \\n\\n\\n.hades .tp-thumb:hover .tp-thumb-img-wrap,\\n.hades .tp-thumb.selected .tp-thumb-img-wrap {\\n  \\n\\n  background: -moz-linear-gradient(top, rgba(##ht##) 0%, rgba(##hb##) 100%);\\n  background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(##ht##)), color-stop(100%, rgba(##hb##)));\\n  background: -webkit-linear-gradient(top, rgba(##ht##) 0%, rgba(##hb##) 100%);\\n  background: -o-linear-gradient(top, rgba(##ht##) 0%, rgba(##hb##) 100%);\\n  background: -ms-linear-gradient(top, rgba(##ht##) 0%, rgba(##hb##) 100%);\\n  background: linear-gradient(to bottom, rgba(##ht##) 0%, rgba(##hb##) 100%);\\n }\\n\\n"}',
			'settings' => '{"width":{"thumbs":"70","arrows":"160","bullets":"160","tabs":"160"},"height":{"thumbs":"70","arrows":"160","bullets":"160","tabs":"160"},"original":{"css":{"arrows":".hades.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:#000;\\n\\tbackground:rgba(0,0,0,0.15);\\n\\twidth:100px;\\n\\theight:100px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n}\\n\\n.hades.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:30px;\\n\\tcolor:#fff;\\n\\tdisplay:block;\\n\\tline-height: 100px;\\n\\ttext-align: center;\\n  transition: background 0.3s, color 0.3s;\\n}\\n.hades.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e824\\\\\\";\\n}\\n.hades.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e825\\\\\\";\\n}\\n\\n.hades.tparrows:hover:before {\\n   color:#aaa;\\n   background:#fff;\\n   background:rgba(255,255,255,1);\\n }\\n.hades .tp-arr-allwrapper {\\n  position:absolute;\\n  left:100%;\\n  top:0px;\\n  background:#888; \\n  width:100px;height:100px;\\n  -webkit-transition: all 0.3s;\\n  transition: all 0.3s;\\n  -ms-filter: \\\\\\"progid:dximagetransform.microsoft.alpha(opacity=0)\\\\\\";\\n  filter: alpha(opacity=0);\\n  -moz-opacity: 0.0;\\n  -khtml-opacity: 0.0;\\n  opacity: 0.0;\\n  -webkit-transform: rotatey(-90deg);\\n  transform: rotatey(-90deg);\\n  -webkit-transform-origin: 0% 50%;\\n  transform-origin: 0% 50%;\\n}\\n.hades.tp-rightarrow .tp-arr-allwrapper {\\n   left:auto;\\n   right:100%;\\n   -webkit-transform-origin: 100% 50%;\\n  transform-origin: 100% 50%;\\n   -webkit-transform: rotatey(90deg);\\n  transform: rotatey(90deg);\\n}\\n\\n.hades:hover .tp-arr-allwrapper {\\n   -ms-filter: \\\\\\"progid:dximagetransform.microsoft.alpha(opacity=100)\\\\\\";\\n  filter: alpha(opacity=100);\\n  -moz-opacity: 1;\\n  -khtml-opacity: 1;\\n  opacity: 1;  \\n    -webkit-transform: rotatey(0deg);\\n  transform: rotatey(0deg);\\n\\n }\\n    \\n.hades .tp-arr-iwrapper {\\n}\\n.hades .tp-arr-imgholder {\\n  background-size:cover;\\n  position:absolute;\\n  top:0px;left:0px;\\n  width:100%;height:100%;\\n}\\n.hades .tp-arr-titleholder {\\n}\\n.hades .tp-arr-subtitleholder {\\n}\\n","bullets":".hades.tp-bullets {\\n}\\n.hades.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n}\\n.hades .tp-bullet {\\n\\twidth:3px;\\n\\theight:3px;\\n\\tposition:absolute;\\n\\tbackground:#888;\\t\\n\\tcursor: pointer;\\n    border:5px solid #fff;\\n\\tbox-sizing:content-box;\\n    box-shadow:0px 0px 3px 1px rgba(0,0,0,0.2);\\n    -webkit-perspective:400;\\n    perspective:400;\\n    -webkit-transform:translatez(0.01px);\\n    transform:translatez(0.01px);\\n}\\n.hades .tp-bullet:hover,\\n.hades .tp-bullet.selected {\\n\\tbackground:#555;\\n  \\n}\\n\\n.hades .tp-bullet-image {\\n  position:absolute;top:-80px; left:-60px;width:120px;height:60px;\\n  background-position:center center;\\n  background-size:cover;\\n  visibility:hidden;\\n  opacity:0;\\n  transition:all 0.3s;\\n  -webkit-transform-style:flat;\\n  transform-style:flat;\\n  perspective:600;\\n  -webkit-perspective:600;\\n  transform: rotatex(-90deg);\\n  -webkit-transform: rotatex(-90deg);\\n  box-shadow:0px 0px 3px 1px rgba(0,0,0,0.2);\\n  transform-origin:50% 100%;\\n  -webkit-transform-origin:50% 100%;\\n  \\n  \\n}\\n.hades .tp-bullet:hover .tp-bullet-image {\\n  display:block;\\n  opacity:1;\\n  transform: rotatex(0deg);\\n  -webkit-transform: rotatex(0deg);\\n  visibility:visible;\\n    }\\n.hades .tp-bullet-title {\\n}\\n","tabs":".hades .tp-tab {\\n  opacity:1;\\n }\\n    \\n.hades .tp-tab-title\\n {\\n      display:block;\\n      color:#333;\\n      font-weight:600;\\n      font-size:18px;\\n      text-align:center;\\n      line-height:25px;      \\n    } \\n.hades .tp-tab-price\\n {\\n\\tdisplay:block;\\n    text-align:center;\\n    color:#999;\\n    font-size:16px;\\n    margin-top:10px;\\n   line-height:20px\\n}\\n\\n.hades .tp-tab-button {\\n    display:inline-block;\\n    margin-top:15px;\\n    text-align:center;\\n\\tpadding:5px 15px;\\n  \\tcolor:#fff;\\n  \\tfont-size:14px;\\n  \\tbackground:#219bd7;\\n   \\tborder-radius:4px;\\n   font-weight:400;\\n}\\n.hades .tp-tab-inner {\\n\\ttext-align:center;\\n}\\n\\n              ","thumbs":".hades .tp-thumb { \\n      opacity:1\\n  }\\n.hades .tp-thumb-img-wrap {\\n  border-radius:50%;\\n  padding:3px;\\n  display:inline-block;\\nbackground:#000;\\n  background-color:rgba(0,0,0,0.25);\\n  width:100%;\\n  height:100%;\\n  position:relative;\\n  margin:0px;\\n  box-sizing:border-box;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n}\\n.hades .tp-thumb-image {\\n   padding:3px; \\n   border-radius:50%;\\n   display:block;\\n   box-sizing:border-box;\\n   position:relative;\\n    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n }  \\n\\n\\n.hades .tp-thumb:hover .tp-thumb-img-wrap,\\n.hades .tp-thumb.selected .tp-thumb-img-wrap {\\n  \\n   background: rgba(255,255,255,1);\\n  background: -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(255,255,255,1)), color-stop(100%, rgba(119,119,119,1)));\\n  background: -webkit-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -o-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -ms-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\\\\\\"#ffffff\\\\\\", endColorstr=\\\\\\"#777777\\\\\\", GradientType=0 );\\n \\n      }\\n.hades .tp-thumb-title:after {\\n        content:\\\\\\" \\\\\\";\\n        position:absolute;\\n        left:50%;\\n        margin-left:-8px;\\n        width: 0;\\n\\t\\theight: 0;\\n\\t\\tborder-style: solid;\\n\\t\\tborder-width: 8px 8px 0 8px;\\n\\t\\tborder-color: rgba(0,0,0,0.75) transparent transparent transparent;\\n        bottom:-8px;\\n   }\\n"},"markup":{"arrows":"<div class=\\\\\\"tp-arr-allwrapper\\\\\\">\\n\\t<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n<\\/div>","bullets":"<span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>","tabs":"<div class=\\\\\\"tp-tab-inner\\\\\\">\\n  <span class=\\\\\\"tp-tab-title\\\\\\">{{param1}}<\\/span>\\n  <span class=\\\\\\"tp-tab-price\\\\\\">{{param2}}<\\/span>\\n  <span class=\\\\\\"tp-tab-button\\\\\\">{{param3}}<\\/span>\\n<\\/div>","thumbs":"<span class=\\\\\\"tp-thumb-img-wrap\\\\\\">\\n  <span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<\\/span>\\n"}},"placeholders":[{"title":"Background","handle":"bg","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(0,0,0,0.25)"}},{"title":"Arrow","handle":"acolor","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#ffffff"}},{"title":"Hover-Arrow","handle":"harrow","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(0,0,0,0.5)"}},{"title":"Hover-Background","handle":"hbg","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#ffffff"}},{"title":"Size-Inner","handle":"innersize","type":"custom","nav-type":"bullets","data":{"custom":"3"}},{"title":"Color-Inner","handle":"colorinner","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#7f7f7f"}},{"title":"Size-Outer","handle":"outersize","type":"custom","nav-type":"bullets","data":{"custom":"5"}},{"title":"Color-Outer","handle":"outercolor","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#ffffff"}},{"title":"Hover-Outer","handle":"outerhover","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#ffffff"}},{"title":"Hover-Inner","handle":"innerhover","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#565656"}},{"title":"Image-Width","handle":"width","type":"custom","nav-type":"bullets","data":{"custom":"120"}},{"title":"Image-Height","handle":"height","type":"custom","nav-type":"bullets","data":{"custom":"60"}},{"title":"Horizontal-Offset","handle":"hoffset","type":"custom","nav-type":"bullets","data":{"custom":"0"}},{"title":"Vertical-Offset","handle":"voffset","type":"custom","nav-type":"bullets","data":{"custom":"-80"}},{"title":"Radius","handle":"radius","type":"custom","nav-type":"thumbs","data":{"custom":"50%"}},{"title":"Border","handle":"border","type":"custom","nav-type":"thumbs","data":{"custom":"3"}},{"title":"Background","handle":"bg","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"rgba(0,0,0,0.25)"}},{"title":"Hover-Top","handle":"ht","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"#ffffff"}},{"title":"Hover-Bottom","handle":"hb","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"#878787"}},{"title":"Param1","handle":"param1","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#333333"}},{"title":"Param1-Size","handle":"param1size","type":"custom","nav-type":"tabs","data":{"custom":"18"}},{"title":"Param2","handle":"param2","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#999999"}},{"title":"Param2-Size","handle":"p2size","type":"custom","nav-type":"tabs","data":{"custom":"16"}},{"title":"Param3","handle":"p3","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#ffffff"}},{"title":"Param3-Size","handle":"p3size","type":"custom","nav-type":"tabs","data":{"custom":"14"}},{"title":"Param3-BG","handle":"p3bg","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#219bd7"}}]}');
			
		$navigations[] = array(
			'id' => 5003,
			'default' => true,
			'name' => 'Ares',
			'handle' => 'preview2',
			'markup' => '{"arrows":"<div class=\\\\\\"tp-title-wrap\\\\\\">\\n\\t<span class=\\\\\\"tp-arr-titleholder\\\\\\">{{title}}<\\/span>\\n <\\/div>\\n","bullets":"<span class=\\\\\\"tp-bullet-title\\\\\\">{{title}}<\\/span>","tabs":"<div class=\\\\\\"tp-tab-content\\\\\\">\\n  <span class=\\\\\\"tp-tab-date\\\\\\">{{param1}}<\\/span>\\n  <span class=\\\\\\"tp-tab-title\\\\\\">{{param2}}<\\/span>\\n<\\/div>\\n<div class=\\\\\\"tp-tab-image\\\\\\"><\\/div>"}',
			'css' => '{"arrows":".ares.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:rgba(##bg-color##);\\n\\tmin-width:##bg-size##px;\\n    min-height:##bg-size##px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n    border-radius:50%;\\n}\\n.ares.tparrows:hover {\\n}\\n.ares.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:##arrow-size##px;\\n\\tcolor:rgba(##arrowcolor##);\\n\\tdisplay:block;\\n\\tline-height: ##bg-size##px;\\n\\ttext-align: center;\\n    -webkit-transition: color 0.3s;\\n    -moz-transition: color 0.3s;\\n    transition: color 0.3s;\\n    z-index:2;\\n    position:relative;\\n}\\n.ares.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e81f\\\\\\";\\n}\\n.ares.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e81e\\\\\\";\\n}\\n.ares.tparrows:hover:before {\\n color:rgb(##hover-arrow-color##);\\n      }\\n.tp-title-wrap { \\n  position:absolute;\\n  z-index:1;\\n  display:inline-block;\\n  background:rgba(##bg-color##);\\n  min-height:##bg-size##px;\\n  line-height:##bg-size##px;\\n  top:0px;\\n  margin-left:30px;\\n  border-radius:0px 30px 30px 0px;\\n  overflow:hidden;\\n  -webkit-transition: -webkit-transform 0.3s;\\n  transition: transform 0.3s;\\n  transform:scalex(0);  \\n  -webkit-transform:scalex(0);  \\n  transform-origin:0% 50%; \\n   -webkit-transform-origin:0% 50%;\\n}\\n .ares.tp-rightarrow .tp-title-wrap { \\n   right:0px;\\n   margin-right:30px;margin-left:0px;\\n   -webkit-transform-origin:100% 50%;\\nborder-radius:30px 0px 0px 30px;\\n }\\n.ares.tparrows:hover .tp-title-wrap {\\n\\ttransform:scalex(1) scaley(1);\\n  \\t-webkit-transform:scalex(1) scaley(1);\\n}\\n.ares .tp-arr-titleholder {\\n  position:relative;\\n  -webkit-transition: -webkit-transform 0.3s;\\n  transition: transform 0.3s;\\n  transform:translatex(200px);  \\n  text-transform:uppercase;\\n  color:rgb(##hover-title-color##);\\n  font-weight:400;\\n  font-size:14px;\\n  line-height:60px;\\n  white-space:nowrap;\\n  padding:0px 20px;\\n  margin-left:10px;\\n  opacity:0;\\n}\\n\\n.ares.tp-rightarrow .tp-arr-titleholder {\\n   transform:translatex(-200px); \\n   margin-left:0px; margin-right:10px;\\n      }\\n\\n.ares.tparrows:hover .tp-arr-titleholder {\\n   transform:translatex(0px);\\n   -webkit-transform:translatex(0px);\\n  transition-delay: 0.1s;\\n  opacity:1;\\n}","bullets":".ares.tp-bullets {\\n}\\n.ares.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n}\\n.ares .tp-bullet {\\n\\twidth:##bullet-size##px;\\n\\theight:##bullet-size##px;\\n\\tposition:absolute;\\n\\tbackground:rgba(##bullet-bg-color##);\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.ares .tp-bullet:hover,\\n.ares .tp-bullet.selected {\\n\\tbackground:rgba(##hover-bullet-bg-color##);\\n}\\n.ares .tp-bullet-title {\\n  position:absolute;\\n  color:##title-color##;\\n  font-size:##title-font-size##px;\\n  padding:0px 10px;\\n  font-weight:600;\\n  right:27px;\\n  top:-4px;  \\n  background:rgba(##title-bg-color##);\\n  visibility:hidden;\\n  transform:translatex(-20px);\\n  -webkit-transform:translatex(-20px);\\n  transition:transform 0.3s;\\n  -webkit-transition:transform 0.3s;\\n  line-height:20px;\\n  white-space:nowrap;\\n}     \\n\\n.ares .tp-bullet-title:after {\\n    width: 0px;\\n\\theight: 0px;\\n\\tborder-style: solid;\\n\\tborder-width: 10px 0 10px 10px;\\n\\tborder-color: transparent transparent transparent rgba(##title-bg-color##);\\n\\tcontent:\\\\\\" \\\\\\";\\n    position:absolute;\\n    right:-10px;\\n\\ttop:0px;\\n}\\n    \\n.ares .tp-bullet:hover .tp-bullet-title{\\n  visibility:visible;\\n   transform:translatex(0px);\\n  -webkit-transform:translatex(0px);\\n}\\n\\n.ares .tp-bullet.selected:hover .tp-bullet-title {\\n    background:rgba(##hover-bullet-bg-color##);}\\n.ares .tp-bullet.selected:hover .tp-bullet-title:after {\\n  border-color:transparent transparent transparent rgba(##hover-bullet-bg-color##);\\n}\\n.ares.tp-bullets:hover .tp-bullet-title {\\n  visibility:hidden;\\n  \\n}\\n.ares.tp-bullets:hover .tp-bullet:hover .tp-bullet-title {\\n    visibility:visible;\\n    transform:translateX(0px) translatey(0px);\\n  -webkit-transform:translateX(0px) translatey(0px);\\n}\\n\\n\\n\\/* VERTICAL *\\/\\n.ares.nav-dir-vertical.nav-pos-hor-left .tp-bullet-title { right:auto; left:27px;  transform:translatex(20px); -webkit-transform:translatex(20px);}  \\n.ares.nav-dir-vertical.nav-pos-hor-left .tp-bullet-title:after { \\n  border-width: 10px 10px 10px 0 !important;\\n  border-color: transparent rgba(##title-bg-color##) transparent transparent;\\n  right:auto !important;\\n  left:-10px !important;   \\n}\\n.ares.nav-dir-vertical.nav-pos-hor-left .tp-bullet.selected:hover .tp-bullet-title:after {\\n  border-color:  transparent rgba(##hover-bullet-bg-color##) transparent transparent !important;\\n}\\n\\n\\n\\n\\/* HORIZONTAL BOTTOM && CENTER *\\/\\n.ares.nav-dir-horizontal.nav-pos-ver-center .tp-bullet-title,\\n.ares.nav-dir-horizontal.nav-pos-ver-bottom .tp-bullet-title { top:-35px; left:50%; right:auto; transform: translateX(-50%) translateY(-10px);-webkit-transform: translateX(-50%) translateY(-10px); }  \\n\\n.ares.nav-dir-horizontal.nav-pos-ver-center .tp-bullet-title:after,\\n.ares.nav-dir-horizontal.nav-pos-ver-bottom .tp-bullet-title:after { \\n  border-width: 10px 10px 0px 10px;\\n  border-color: rgba(##title-bg-color##) transparent transparent transparent;\\n  right:auto;\\n  left:50%;\\n  margin-left:-10px;\\n  top:auto;\\n  bottom:-10px;\\n    \\n}\\n.ares.nav-dir-horizontal.nav-pos-ver-center .tp-bullet.selected:hover .tp-bullet-title:after,\\n.ares.nav-dir-horizontal.nav-pos-ver-bottom .tp-bullet.selected:hover .tp-bullet-title:after {\\n  border-color:  rgba(##hover-bullet-bg-color##) transparent transparent transparent;\\n}\\n\\n.ares.nav-dir-horizontal.nav-pos-ver-center .tp-bullet:hover .tp-bullet-title,\\n.ares.nav-dir-horizontal.nav-pos-ver-bottom .tp-bullet:hover .tp-bullet-title{\\n   transform:translateX(-50%) translatey(0px);\\n  -webkit-transform:translateX(-50%) translatey(0px);\\n}\\n\\n\\n\\/* HORIZONTAL TOP *\\/\\n.ares.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-title { top:25px; left:50%; right:auto; transform: translateX(-50%) translateY(10px);-webkit-transform: translateX(-50%) translateY(10px); }  \\n.ares.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-title:after { \\n  border-width: 0 10px 10px 10px;\\n  border-color:  transparent transparent rgba(##title-bg-color##) transparent;\\n  right:auto;\\n  left:50%;\\n  margin-left:-10px;\\n  bottom:auto;\\n  top:-10px;\\n    \\n}\\n.ares.nav-dir-horizontal.nav-pos-ver-top .tp-bullet.selected:hover .tp-bullet-title:after {\\n  border-color:  transparent transparent  rgba(##hover-bullet-bg-color##) transparent;\\n}\\n\\n.ares.nav-dir-horizontal.nav-pos-ver-top .tp-bullet:hover .tp-bullet-title{\\n   transform:translateX(-50%) translatey(0px);\\n  -webkit-transform:translateX(-50%) translatey(0px);\\n}\\n\\n","tabs":".ares .tp-tab { \\n  opacity:1;      \\n  padding:10px;\\n  box-sizing:border-box;\\n  font-family: \\\\\\"##font-family##\\\\\\", sans-serif;\\n  border-bottom: ##bottom-border-size##px solid rgb(##bottom-border-color##);\\n  background:rgb(##idle-bg-color);\\n }\\n.ares .tp-tab-image \\n{ \\n  width:##image-size##px;\\n  height:##image-size##px; max-height:100%; max-width:100%;\\n  position:relative;\\n  display:inline-block;\\n  float:left;\\n\\n}\\n.ares .tp-tab-content \\n{\\n    background:rgba(0,0,0,0); \\n    position:relative;\\n    padding:15px 15px 15px 85px;\\n left:0px;\\n overflow:hidden;\\n margin-top:-15px;\\n    box-sizing:border-box;\\n    color:#333;\\n    display: inline-block;\\n    width:100%;\\n    height:100%;\\n position:absolute; }\\n.ares .tp-tab-date\\n  {\\n  display:block;\\n  color: rgb(##param1-color##);\\n  font-weight:500;\\n  font-size:##param1-size##px;\\n  margin-bottom:0px;\\n  }\\n.ares .tp-tab-title \\n{\\n    display:block;\\t\\n    text-align:left;\\n    color:rgb(##param2-color##);\\n    font-size:##param2-size##px;\\n    font-weight:500;\\n    text-transform:none;\\n    line-height:17px;\\n}\\n.ares .tp-tab:hover,\\n.ares .tp-tab.selected {\\n\\tbackground:rgb(##hover-bg-color##); \\n}\\n\\n.ares .tp-tab-mask {\\n}\\n\\n\\/* media queries *\\/\\n@media only screen and (max-width: 960px) {\\n\\n}\\n@media only screen and (max-width: 768px) {\\n\\n}"}',
			'settings' => '{"width":{"tabs":"250"},"height":{"tabs":"80"},"original":{"css":{"arrows":".ares.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:#fff;\\n\\tmin-width:60px;\\n    min-height:60px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n    border-radius:50%;\\n}\\n.ares.tparrows:hover {\\n}\\n.ares.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:25px;\\n\\tcolor:#aaa;\\n\\tdisplay:block;\\n\\tline-height: 60px;\\n\\ttext-align: center;\\n    -webkit-transition: color 0.3s;\\n    -moz-transition: color 0.3s;\\n    transition: color 0.3s;\\n    z-index:2;\\n    position:relative;\\n}\\n.ares.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e81f\\\\\\";\\n}\\n.ares.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e81e\\\\\\";\\n}\\n.ares.tparrows:hover:before {\\n color:#000;\\n      }\\n.tp-title-wrap { \\n  position:absolute;\\n  z-index:1;\\n  display:inline-block;\\n  background:#fff;\\n  min-height:60px;\\n  line-height:60px;\\n  top:0px;\\n  margin-left:30px;\\n  border-radius:0px 30px 30px 0px;\\n  overflow:hidden;\\n  -webkit-transition: -webkit-transform 0.3s;\\n  transition: transform 0.3s;\\n  transform:scaleX(0);  \\n  -webkit-transform:scaleX(0);  \\n  transform-origin:0% 50%; \\n   -webkit-transform-origin:0% 50%;\\n}\\n .ares.tp-rightarrow .tp-title-wrap { \\n   right:0px;\\n   margin-right:30px;margin-left:0px;\\n   -webkit-transform-origin:100% 50%;\\nborder-radius:30px 0px 0px 30px;\\n }\\n.ares.tparrows:hover .tp-title-wrap {\\n\\ttransform:scaleX(1) scaleY(1);\\n  \\t-webkit-transform:scaleX(1) scaleY(1);\\n}\\n.ares .tp-arr-titleholder {\\n  position:relative;\\n  -webkit-transition: -webkit-transform 0.3s;\\n  transition: transform 0.3s;\\n  transform:translateX(200px);  \\n  text-transform:uppercase;\\n  color:#000;\\n  font-weight:400;\\n  font-size:14px;\\n  line-height:60px;\\n  white-space:nowrap;\\n  padding:0px 20px;\\n  margin-left:10px;\\n  opacity:0;\\n}\\n\\n.ares.tp-rightarrow .tp-arr-titleholder {\\n   transform:translateX(-200px); \\n   margin-left:0px; margin-right:10px;\\n      }\\n\\n.ares.tparrows:hover .tp-arr-titleholder {\\n   transform:translateX(0px);\\n   -webkit-transform:translateX(0px);\\n  transition-delay: 0.1s;\\n  opacity:1;\\n}","bullets":".ares.tp-bullets {\\n}\\n.ares.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n}\\n.ares .tp-bullet {\\n\\twidth:13px;\\n\\theight:13px;\\n\\tposition:absolute;\\n\\tbackground:#e5e5e5;\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.ares .tp-bullet:hover,\\n.ares .tp-bullet.selected {\\n\\tbackground:#fff;\\n}\\n.ares .tp-bullet-title {\\n  position:absolute;\\n  color:#888;\\n  font-size:12px;\\n  padding:0px 10px;\\n  font-weight:600;\\n  right:27px;\\n  top:-4px;\\n  background:#fff;\\n  background:rgba(255,255,255,0.75);\\n  visibility:hidden;\\n  transform:translateX(-20px);\\n  -webkit-transform:translateX(-20px);\\n  transition:transform 0.3s;\\n  -webkit-transition:transform 0.3s;\\n  line-height:20px;\\n  white-space:nowrap;\\n}     \\n\\n.ares .tp-bullet-title:after {\\n    width: 0px;\\n\\theight: 0px;\\n\\tborder-style: solid;\\n\\tborder-width: 10px 0 10px 10px;\\n\\tborder-color: transparent transparent transparent rgba(255,255,255,0.75);\\n\\tcontent:\\\\\\" \\\\\\";\\n    position:absolute;\\n    right:-10px;\\n\\ttop:0px;\\n}\\n    \\n.ares .tp-bullet:hover .tp-bullet-title{\\n  visibility:visible;\\n   transform:translateX(0px);\\n  -webkit-transform:translateX(0px);\\n}\\n\\n.ares .tp-bullet.selected:hover .tp-bullet-title {\\n    background:#fff;\\n        }\\n.ares .tp-bullet.selected:hover .tp-bullet-title:after {\\n  border-color:transparent transparent transparent #fff;\\n}\\n.ares.tp-bullets:hover .tp-bullet-title {\\n        visibility:hidden;\\n}\\n.ares.tp-bullets:hover .tp-bullet:hover .tp-bullet-title {\\n    visibility:visible;\\n      }","tabs":".ares .tp-tab { \\n  opacity:1;      \\n  padding:10px;\\n  box-sizing:border-box;\\n  font-family: \\\\\\"Roboto\\\\\\", sans-serif;\\n  border-bottom: 1px solid #e5e5e5;\\n }\\n.ares .tp-tab-image \\n{ \\n  width:60px;\\n  height:60px; max-height:100%; max-width:100%;\\n  position:relative;\\n  display:inline-block;\\n  float:left;\\n\\n}\\n.ares .tp-tab-content \\n{\\n    background:rgba(0,0,0,0); \\n    position:relative;\\n    padding:15px 15px 15px 85px;\\n left:0px;\\n overflow:hidden;\\n margin-top:-15px;\\n    box-sizing:border-box;\\n    color:#333;\\n    display: inline-block;\\n    width:100%;\\n    height:100%;\\n position:absolute; }\\n.ares .tp-tab-date\\n  {\\n  display:block;\\n  color: #aaa;\\n  font-weight:500;\\n  font-size:12px;\\n  margin-bottom:0px;\\n  }\\n.ares .tp-tab-title \\n{\\n    display:block;\\t\\n    text-align:left;\\n    color:#333;\\n    font-size:14px;\\n    font-weight:500;\\n    text-transform:none;\\n    line-height:17px;\\n}\\n.ares .tp-tab:hover,\\n.ares .tp-tab.selected {\\n\\tbackground:#eee; \\n}\\n\\n.ares .tp-tab-mask {\\n}\\n\\n\\/* MEDIA QUERIES *\\/\\n@media only screen and (max-width: 960px) {\\n\\n}\\n@media only screen and (max-width: 768px) {\\n\\n}"},"markup":{"arrows":"<div class=\\\\\\"tp-title-wrap\\\\\\">\\n\\t<span class=\\\\\\"tp-arr-titleholder\\\\\\">{{title}}<\\/span>\\n <\\/div>\\n","bullets":"<span class=\\\\\\"tp-bullet-title\\\\\\">{{title}}<\\/span>","tabs":"<div class=\\\\\\"tp-tab-content\\\\\\">\\n  <span class=\\\\\\"tp-tab-date\\\\\\">{{param1}}<\\/span>\\n  <span class=\\\\\\"tp-tab-title\\\\\\">{{param2}}<\\/span>\\n<\\/div>\\n<div class=\\\\\\"tp-tab-image\\\\\\"><\\/div>"}},"placeholders":[{"title":"BG-Color","handle":"bg-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#ffffff"}},{"title":"Size","handle":"bg-size","type":"custom","nav-type":"arrows","data":{"custom":"60"}},{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"arrows","data":{"custom":"25"}},{"title":"Hover-Arrow","handle":"hover-arrow-color","type":"color","nav-type":"arrows","data":{"color":"#000000"}},{"title":"Hover-Title","handle":"hover-title-color","type":"color","nav-type":"arrows","data":{"color":"#000000"}},{"title":"Bullet-Size","handle":"bullet-size","type":"custom","nav-type":"bullets","data":{"custom":"13"}},{"title":"Bullet-Background","handle":"bullet-bg-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#e5e5e5"}},{"title":"Hover-Bullet-BG","handle":"hover-bullet-bg-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#ffffff"}},{"title":"Title-Color","handle":"title-color","type":"color","nav-type":"bullets","data":{"color":"#888888"}},{"title":"Title-Font-Size","handle":"title-font-size","type":"custom","nav-type":"bullets","data":{"custom":"12"}},{"title":"Title-BG-Color","handle":"title-bg-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"rgba(255,255,255,0.75)"}},{"title":"Font-Family","handle":"font-family","type":"font-family","nav-type":"tabs","data":{"font_family":"Roboto"}},{"title":"Bottom-Border","handle":"bottom-border-color","type":"color","nav-type":"tabs","data":{"color":"#e5e5e5"}},{"title":"Bottom-Border-Size","handle":"bottom-border-size","type":"custom","nav-type":"tabs","data":{"custom":"1"}},{"title":"Image-Size","handle":"image-size","type":"custom","nav-type":"tabs","data":{"custom":"60"}},{"title":"Param-1-Color","handle":"param1-color","type":"color","nav-type":"tabs","data":{"color":"#aaaaaa"}},{"title":"Param-1-Size","handle":"param1-size","type":"custom","nav-type":"tabs","data":{"custom":"12"}},{"title":"Param-2-Color","handle":"param2-color","type":"color","nav-type":"tabs","data":{"color":"#333333"}},{"title":"Param-2-Size","handle":"param2-size","type":"custom","nav-type":"tabs","data":{"custom":"14"}},{"title":"Hover-Background","handle":"hover-bg-color","type":"color","nav-type":"tabs","data":{"color":"#eeeeee"}},{"title":"Idle-Background","handle":"idle-bg-color","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(0,0,0,0)"}},{"title":"Arrow-Color","handle":"arrowcolor","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#aaaaaa"}}]}');
			
		$navigations[] = array(
			'id' => 5004,
			'default' => true,
			'name' => 'Hebe',
			'handle' => 'preview3',
			'markup' => '{"arrows":"<div class=\\\\\\"tp-title-wrap\\\\\\">\\n\\t<span class=\\\\\\"tp-arr-titleholder\\\\\\">{{title}}<\\/span>\\n    <span class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/span>\\n <\\/div>\\n","bullets":"<span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>","tabs":"<div class=\\\\\\"tp-tab-title\\\\\\">{{param1}}<\\/div>\\n<div class=\\\\\\"tp-tab-desc\\\\\\">{{title}}<\\/div>"}',
			'css' => '{"arrows":".hebe.tparrows {\\n  cursor:pointer;\\n  background:rgba(##back-color##);\\n  min-width:##back-size##px;\\n  min-height:##back-size##px;\\n  position:absolute;\\n  display:block;\\n  z-index:100;\\n}\\n.hebe.tparrows:hover {\\n}\\n.hebe.tparrows:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:##arrow-size##px;\\n  color:rgba(##arrow-color##);\\n  display:block;\\n  line-height: ##back-size##px;\\n  text-align: center;\\n  -webkit-transition: color 0.3s;\\n  -moz-transition: color 0.3s;\\n  transition: color 0.3s;\\n  z-index:2;\\n  position:relative;\\n   background:rgba(##back-color##);\\n  min-width:##back-size##px;\\n    min-height:##back-size##px;\\n}\\n.hebe.tparrows.tp-leftarrow:before {\\n  content: \\\\\\"\\\\\\\e824\\\\\\";\\n}\\n.hebe.tparrows.tp-rightarrow:before {\\n  content: \\\\\\"\\\\\\\e825\\\\\\";\\n}\\n.hebe.tparrows:hover:before {\\n color:#000;\\n      }\\n.tp-title-wrap { \\n  position:absolute;\\n  z-index:0;\\n  display:inline-block;\\n  background:#000;\\n  background:rgba(##title-wrap-color##);\\n  min-height:60px;\\n  line-height:60px;\\n  top:-10px;\\n  margin-left:0px;\\n  -webkit-transition: -webkit-transform 0.3s;\\n  transition: transform 0.3s;\\n  transform:scalex(0);  \\n  -webkit-transform:scalex(0);  \\n  transform-origin:0% 50%; \\n   -webkit-transform-origin:0% 50%;\\n}\\n .hebe.tp-rightarrow .tp-title-wrap { \\n   right:0px;\\n   -webkit-transform-origin:100% 50%;\\n }\\n.hebe.tparrows:hover .tp-title-wrap {\\n  transform:scalex(1);\\n  -webkit-transform:scalex(1);\\n}\\n.hebe .tp-arr-titleholder {\\n  position:relative;\\n  text-transform:uppercase;\\n  color:rgb(##title-color##);\\n  font-weight:600;\\n  font-size:##title-size##px;\\n  line-height:##image-size##px;\\n  white-space:nowrap;\\n  padding:0px 20px 0px ##image-size##px;\\n}\\n\\n.hebe.tp-rightarrow .tp-arr-titleholder {\\n   margin-left:0px; \\n   padding:0px ##image-size##px 0px 20px;\\n }\\n\\n.hebe.tparrows:hover .tp-arr-titleholder {\\n   transform:translatex(0px);\\n   -webkit-transform:translatex(0px);\\n  transition-delay: 0.1s;\\n  opacity:1;\\n}\\n\\n.hebe .tp-arr-imgholder{\\n      width:##image-size##px;\\n      height:##image-size##px;\\n      position:absolute;\\n      left:100%;\\n      display:block;\\n      background-size:cover;\\n      background-position:center center;\\n  \\t top:0px; right:-##image-size##px;\\n    }\\n.hebe.tp-rightarrow .tp-arr-imgholder{\\n        right:auto;left:-##image-size##px;\\n      }","bullets":"\\n.hebe.tp-bullets:before {\\n  content:\\\\\\" \\\\\\";\\n  position:absolute;\\n  width:100%;\\n  height:100%;\\n  background:transparent;\\n  padding:10px;\\n  margin-left:-10px;margin-top:-10px;\\n  box-sizing:content-box;\\n}\\n\\n.hebe .tp-bullet {\\n  width:##bullet-back-size##px;\\n  height:##bullet-back-size##px;\\n  position:absolute;\\n  background:rgba(##bullet-back-color##);  \\n  cursor: pointer;\\n  border:##bullet-border-size##px solid rgba(##bullet-border-color##);\\n  border-radius:##bradius##;\\n  box-sizing:content-box;\\n  -webkit-perspective:400;\\n  perspective:400;\\n  -webkit-transform:translatez(0.01px);\\n  transform:translatez(0.01px);\\n   transition:all ##aspeed##s;\\n}\\n.hebe .tp-bullet:hover,\\n.hebe .tp-bullet.selected {\\n  background:rgba(##bullet-border-color##);\\n  border-color:rgba(##bullet-back-color##);\\n}\\n\\n.hebe .tp-bullet-image {\\n  position:absolute;\\n  width:##width##px;\\n  height:##height##px;\\n  background-position:center center;\\n  background-size:cover;\\n  visibility:hidden;\\n  opacity:0;\\n  bottom:##bullet-back-size##px;\\n  transition:all ##aspeed##s;\\n  -webkit-transform-style:flat;\\n  transform-style:flat;\\n  perspective:600;\\n  -webkit-perspective:600;\\n  transform: scale(0) translateX(-50%) translateY(0%);\\n  -webkit-transform: scale(0) translateX(-50%) translateY(0%);\\n  transform-origin:0% 100%;\\n  -webkit-transform-origin:0% 100%;\\n  margin-bottom:15px;\\n border-radius:##iradius##px;\\n}\\n.hebe .tp-bullet:hover .tp-bullet-image {\\n  display:block;\\n  opacity:1;\\n  transform: scale(1) translateX(-50%) translateY(0%);\\n  -webkit-transform: scale(1) translateX(-50%) translateY(0%);\\n  visibility:visible;\\n}\\n\\n\\n\\/* VERTICAL *\\/\\n\\n.hebe.nav-dir-vertical .tp-bullet-image {\\n  bottom:auto;\\n  margin-right:15px;\\n  margin-bottom:0px;\\n  right:##bullet-back-size##px;\\n  transform: scale(0) translateX(0px) translateY(-50%);\\n  -webkit-transform: scale(0) translateX(0px) translateY(-50%);\\n  transform-origin:100% 0%;\\n  -webkit-transform-origin:100% 0%;\\n}\\n\\n.hebe.nav-dir-vertical .tp-bullet:hover .tp-bullet-image {\\n  transform: scale(1) translateX(0px) translateY(-50%);\\n  -webkit-transform: scale(1) translateX(0px) translateY(-50%);\\n}\\n\\n\\/* VERTICAL LEFT *\\/\\n\\n.hebe.nav-dir-vertical.nav-pos-hor-left .tp-bullet-image {\\n  bottom:auto;\\n  margin-left:15px;\\n  margin-bottom:0px;\\n  left:##bullet-back-size##px;\\n  transform: scale(0) translateX(0px) translateY(-50%);\\n  -webkit-transform: scale(0) translateX(0px) translateY(-50%);\\n  transform-origin:0% 0%;\\n  -webkit-transform-origin:0% 0%;\\n}\\n\\n.hebe.nav-dir-vertical.nav-pos-hor-left .tp-bullet:hover .tp-bullet-image {\\n  transform: scale(1) translateX(0px) translateY(-50%);\\n  -webkit-transform: scale(1) translateX(0px) translateY(-50%);\\n}\\n\\n\\/* HORIZONTAL TOP *\\/\\n.hebe.nav-pos-ver-top.nav-dir-horizontal .tp-bullet-image {\\n  bottom:auto;\\n  top:##bullet-back-size##px;\\n  transform: scale(0) translateX(-50%) translateY(0%);\\n  -webkit-transform: scale(0) translateX(-50%) translateY(0%);\\n  transform-origin:0% 0%;\\n  -webkit-transform-origin:0% 0%;\\n  margin-top:15px;\\n  margin-bottom:0px;  \\n}\\n.hebe.nav-pos-ver-top.nav-dir-horizontal .tp-bullet:hover .tp-bullet-image {\\n  transform: scale(1) translateX(-50%) translateY(0%);\\n  -webkit-transform: scale(1) translateX(-50%) translateY(0%);\\n}","tabs":".hebe .tp-tab-title {\\n    color:rgb(##title-color##);\\n    font-size:##title-size##px;\\n    font-weight:700;\\n    text-transform:uppercase;\\n    font-family:\\\\\\"##title-font##\\\\\\"\\n    margin-bottom:5px;\\n}\\n\\n.hebe .tp-tab-desc {\\n\\tfont-size:##param1-size##px;\\n    font-weight:400;\\n    color:rgb(##param1-color##);\\n    line-height:25px;\\n\\tfont-family:\\\\\\"##param1-font##\\\\\\";\\n}\\n"}',
			'settings' => '{"original":{"css":{"arrows":".hebe.tparrows {\\n  cursor:pointer;\\n  background:#fff;\\n  min-width:70px;\\n    min-height:70px;\\n  position:absolute;\\n  display:block;\\n  z-index:100;\\n}\\n.hebe.tparrows:hover {\\n}\\n.hebe.tparrows:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:30px;\\n  color:#aaa;\\n  display:block;\\n  line-height: 70px;\\n  text-align: center;\\n  -webkit-transition: color 0.3s;\\n  -moz-transition: color 0.3s;\\n  transition: color 0.3s;\\n  z-index:2;\\n  position:relative;\\n   background:#fff;\\n  min-width:70px;\\n    min-height:70px;\\n}\\n.hebe.tparrows.tp-leftarrow:before {\\n  content: \\\\\\"\\\\\\\\e824\\\\\\";\\n}\\n.hebe.tparrows.tp-rightarrow:before {\\n  content: \\\\\\"\\\\\\\\e825\\\\\\";\\n}\\n.hebe.tparrows:hover:before {\\n color:#000;\\n      }\\n.tp-title-wrap { \\n  position:absolute;\\n  z-index:0;\\n  display:inline-block;\\n  background:#000;\\n  background:rgba(0,0,0,0.75);\\n  min-height:60px;\\n  line-height:60px;\\n  top:-10px;\\n  margin-left:0px;\\n  -webkit-transition: -webkit-transform 0.3s;\\n  transition: transform 0.3s;\\n  transform:scaleX(0);  \\n  -webkit-transform:scaleX(0);  \\n  transform-origin:0% 50%; \\n   -webkit-transform-origin:0% 50%;\\n}\\n .hebe.tp-rightarrow .tp-title-wrap { \\n   right:0px;\\n   -webkit-transform-origin:100% 50%;\\n }\\n.hebe.tparrows:hover .tp-title-wrap {\\n  transform:scaleX(1);\\n  -webkit-transform:scaleX(1);\\n}\\n.hebe .tp-arr-titleholder {\\n  position:relative;\\n  text-transform:uppercase;\\n  color:#fff;\\n  font-weight:600;\\n  font-size:12px;\\n  line-height:90px;\\n  white-space:nowrap;\\n  padding:0px 20px 0px 90px;\\n}\\n\\n.hebe.tp-rightarrow .tp-arr-titleholder {\\n   margin-left:0px; \\n   padding:0px 90px 0px 20px;\\n }\\n\\n.hebe.tparrows:hover .tp-arr-titleholder {\\n   transform:translateX(0px);\\n   -webkit-transform:translateX(0px);\\n  transition-delay: 0.1s;\\n  opacity:1;\\n}\\n\\n.hebe .tp-arr-imgholder{\\n      width:90px;\\n      height:90px;\\n      position:absolute;\\n      left:100%;\\n      display:block;\\n      background-size:cover;\\n      background-position:center center;\\n  \\t top:0px; right:-90px;\\n    }\\n.hebe.tp-rightarrow .tp-arr-imgholder{\\n        right:auto;left:-90px;\\n      }","bullets":".hebe.tp-bullets {\\n}\\n.hebe.tp-bullets:before {\\n  content:\\\\\\" \\\\\\";\\n  position:absolute;\\n  width:100%;\\n  height:100%;\\n  background:transparent;\\n  padding:10px;\\n  margin-left:-10px;margin-top:-10px;\\n  box-sizing:content-box;\\n}\\n\\n.hebe .tp-bullet {\\n  width:3px;\\n  height:3px;\\n  position:absolute;\\n  background:#fff;  \\n  cursor: pointer;\\n  border:5px solid #222;\\n  border-radius:50%;\\n  box-sizing:content-box;\\n  -webkit-perspective:400;\\n  perspective:400;\\n  -webkit-transform:translateZ(0.01px);\\n  transform:translateZ(0.01px);\\n   transition:all 0.3s;\\n}\\n.hebe .tp-bullet:hover,\\n.hebe .tp-bullet.selected {\\n  background:#222;\\n  border-color:#fff;\\n}\\n\\n.hebe .tp-bullet-image {\\n  position:absolute;\\n  top:-90px; left:-40px;\\n  width:70px;\\n  height:70px;\\n  background-position:center center;\\n  background-size:cover;\\n  visibility:hidden;\\n  opacity:0;\\n  transition:all 0.3s;\\n  -webkit-transform-style:flat;\\n  transform-style:flat;\\n  perspective:600;\\n  -webkit-perspective:600;\\n  transform: scale(0);\\n  -webkit-transform: scale(0);\\n  transform-origin:50% 100%;\\n  -webkit-transform-origin:50% 100%;\\nborder-radius:6px;\\n  \\n  \\n}\\n.hebe .tp-bullet:hover .tp-bullet-image {\\n  display:block;\\n  opacity:1;\\n  transform: scale(1);\\n  -webkit-transform: scale(1);\\n  visibility:visible;\\n    }\\n.hebe .tp-bullet-title {\\n}\\n","tabs":".hebe .tp-tab-title {\\n    color:#a8d8ee;\\n    font-size:13px;\\n    font-weight:700;\\n    text-transform:uppercase;\\n    font-family:\\\\\\"Roboto Slab\\\\\\"\\n    margin-bottom:5px;\\n}\\n\\n.hebe .tp-tab-desc {\\n\\tfont-size:18px;\\n    font-weight:400;\\n    color:#fff;\\n    line-height:25px;\\n\\tfont-family:\\\\\\"Roboto Slab\\\\\\";\\n}\\n"},"markup":{"arrows":"<div class=\\\\\\"tp-title-wrap\\\\\\">\\n\\t<span class=\\\\\\"tp-arr-titleholder\\\\\\">{{title}}<\\/span>\\n    <span class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/span>\\n <\\/div>\\n","bullets":"<span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>","tabs":"<div class=\\\\\\"tp-tab-title\\\\\\">{{param1}}<\\/div>\\n<div class=\\\\\\"tp-tab-desc\\\\\\">{{title}}<\\/div>"}},"placeholders":[{"title":"BG-Size","handle":"back-size","type":"custom","nav-type":"arrows","data":{"custom":"70"}},{"title":"Background","handle":"back-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#ffffff"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(0,0,0,0.5)"}},{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"arrows","data":{"custom":"30"}},{"title":"Image-Size","handle":"image-size","type":"custom","nav-type":"arrows","data":{"custom":"90"}},{"title":"Title-Wrap-BG-Color","handle":"title-wrap-color","type":"custom","nav-type":"arrows","data":{"custom":"0,0,0,0.75"}},{"title":"Title-Color","handle":"title-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},{"title":"Title-Size","handle":"title-size","type":"custom","nav-type":"arrows","data":{"custom":"12"}},{"title":"Bullet-Background","handle":"bullet-back-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#ffffff"}},{"title":"Bullet-Border-Color","handle":"bullet-border-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#000000"}},{"title":"Bullet-Border-Size","handle":"bullet-border-size","type":"custom","nav-type":"bullets","data":{"custom":"5"}},{"title":"Bullet-BG-Size","handle":"bullet-back-size","type":"custom","nav-type":"bullets","data":{"custom":"3"}},"","","",{"title":"Title-Color","handle":"title-color","type":"color","nav-type":"tabs","data":{"color":"#a8d8ee"}},{"title":"Title-Size","handle":"title-size","type":"custom","nav-type":"tabs","data":{"custom":"13"}},{"title":"Title-Font","handle":"title-font","type":"font-family","nav-type":"tabs","data":{"font_family":"Roboto Slab"}},{"title":"Param-1-Color","handle":"param1-color","type":"color","nav-type":"tabs","data":{"color":"#ffffff"}},{"title":"Param-1-Size","handle":"param1-size","type":"custom","nav-type":"tabs","data":{"custom":"18"}},{"title":"Param-1-Font","handle":"param1-font","type":"font-family","nav-type":"tabs","data":{"font_family":"Roboto Slab"}},{"title":"Image-Width","handle":"width","type":"custom","nav-type":"bullets","data":{"custom":"70"}},{"title":"Image-Height","handle":"height","type":"custom","nav-type":"bullets","data":{"custom":"70"}},{"title":"Image-Radius","handle":"iradius","type":"custom","nav-type":"bullets","data":{"custom":"6"}},{"title":"Bullet-Radius","handle":"bradius","type":"custom","nav-type":"bullets","data":{"custom":"50%"}},{"title":"Animation-Speed","handle":"aspeed","type":"custom","nav-type":"bullets","data":{"custom":"0.3"}}]}');
			
		$navigations[] = array(
			'id' => 5005,
			'default' => true,
			'name' => 'Hermes',
			'handle' => 'preview4',
			'markup' => '{"arrows":"<div class=\\\\\\"tp-arr-allwrapper\\\\\\">\\n\\t<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n\\t<div class=\\\\\\"tp-arr-titleholder\\\\\\">{{title}}<\\/div>\\t\\n<\\/div>","bullets":"","tabs":"<span class=\\\\\\"tp-tab-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-tab-content-wrapper\\\\\\">\\n<span class=\\\\\\"tp-tab-bg\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-tab-content\\\\\\">\\n\\t<span class=\\\\\\"tp-tab-date\\\\\\">{{param1}}<\\/span>\\n\\t<span class=\\\\\\"tp-tab-title\\\\\\">{{param2}}<\\/span>\\n<\\/span>\\n<\\/span>"}',
			'css' => '{"arrows":".hermes.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:rgba(##back-color##);\\n\\twidth:##width##px;\\n\\theight:##height##px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n}\\n\\n.hermes.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:##arrow-size##px;\\n\\tcolor:rgb(##arrow-color##);\\n\\tdisplay:block;\\n\\tline-height: ##height##px;\\n\\ttext-align: center;\\n    transform:translatex(0px);\\n    -webkit-transform:translatex(0px);\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n}\\n.hermes.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e824\\\\\\";\\n}\\n.hermes.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e825\\\\\\";\\n}\\n.hermes.tparrows.tp-leftarrow:hover:before {\\n    transform:translatex(-20px);\\n    -webkit-transform:translatex(-20px);\\n     opacity:0;\\n}\\n.hermes.tparrows.tp-rightarrow:hover:before {\\n    transform:translatex(20px);\\n    -webkit-transform:translatex(20px);\\n     opacity:0;\\n}\\n\\n.hermes .tp-arr-allwrapper {\\n    overflow:hidden;\\n    position:absolute;\\n\\twidth:##wrapper-width##px;\\n    height:##wrapper-height##px;\\n    top:0px;\\n    left:0px;\\n    visibility:hidden;\\n      -webkit-transition: -webkit-transform 0.3s 0.3s;\\n  transition: transform 0.3s 0.3s;\\n  -webkit-perspective: 1000px;\\n  perspective: 1000px;\\n    }\\n.hermes.tp-rightarrow .tp-arr-allwrapper {\\n   right:0px;left:auto;\\n      }\\n.hermes.tparrows:hover .tp-arr-allwrapper {\\n   visibility:visible;\\n          }\\n.hermes .tp-arr-imgholder {\\n  width:##wrapper-width##px;position:absolute;\\n  left:0px;top:0px;height:##height##px;\\n  transform:translatex(-##wrapper-width##px);\\n  -webkit-transform:translatex(-##wrapper-width##px);\\n  transition:all 0.3s;\\n  transition-delay:0.3s;\\n}\\n.hermes.tp-rightarrow .tp-arr-imgholder{\\n    transform:translatex(##wrapper-width##px);\\n  -webkit-transform:translatex(##wrapper-width##px);\\n      }\\n  \\n.hermes.tparrows:hover .tp-arr-imgholder {\\n   transform:translatex(0px);\\n   -webkit-transform:translatex(0px);            \\n}\\n.hermes .tp-arr-titleholder {\\n  top:##height##px;\\n  width:##wrapper-width##px;\\n  text-align:left; \\n  display:block;\\n  padding:0px 10px;\\n  line-height:30px; background:#000;\\n  background:rgba(##title-back-color##);\\n  color:rgb(##title-font-color##);\\n  font-weight:600; position:absolute;\\n  font-size:##title-size##px;\\n  white-space:nowrap;\\n  letter-spacing:1px;\\n  -webkit-transition: all 0.3s;\\n  transition: all 0.3s;\\n  -webkit-transform: rotatex(-90deg);\\n  transform: rotatex(-90deg);\\n  -webkit-transform-origin: 50% 0;\\n  transform-origin: 50% 0;\\n  box-sizing:border-box;\\n\\n}\\n.hermes.tparrows:hover .tp-arr-titleholder {\\n    -webkit-transition-delay: 0.6s;\\n  transition-delay: 0.6s;\\n  -webkit-transform: rotatex(0deg);\\n  transform: rotatex(0deg);\\n}\\n","bullets":".hermes.tp-bullets {\\n}\\n\\n.hermes .tp-bullet {\\n    overflow:hidden;\\n    border-radius:50%;\\n    width:##bullet-size##px;\\n    height:##bullet-size##px;\\n    background-color: rgba(0, 0, 0, 0);\\n    box-shadow: inset 0 0 0 ##border##px rgb(##bullet-color##);\\n    -webkit-transition: background 0.3s ease;\\n    transition: background 0.3s ease;\\n    position:absolute;\\n}\\n\\n.hermes .tp-bullet:hover {\\n\\t  background-color: rgba(##bullet-hover##);\\n}\\n.hermes .tp-bullet:after {\\n  content: \\\\\' \\\\\';\\n  position: absolute;\\n  bottom: 0;\\n  height: 0;\\n  left: 0;\\n  width: 100%;\\n  background-color: rgb(##bullet-color##);\\n  box-shadow: 0 0 1px rgb(##bullet-color##);\\n  -webkit-transition: height 0.3s ease;\\n  transition: height 0.3s ease;\\n}\\n.hermes .tp-bullet.selected:after {\\n  height:100%;\\n}\\n","tabs":".hermes .tp-tab { \\n  opacity:1;  \\n  box-sizing:border-box;\\n  padding-right:10px;\\n }\\n \\n.hermes .tp-tab-content-wrapper {\\n  position:absolute;\\n  width:100%;\\n  min-height:40%;\\n  bottom:0px;\\n  box-sizing:border-box;\\n  padding-right:10px;\\n  overflow:hidden;\\n}\\n.hermes .tp-tab-bg {\\n  position:absolute; \\n  top:0px;\\n  left:-10px; \\n  width:100%;height:100%;\\n  background:rgba(##back-color##); \\n}\\n.hermes .tp-tab-image \\n{ \\n  width:100%;\\n  height:60%;\\n  position:relative;\\n}\\n.hermes .tp-tab-content \\n{\\n  position:relative;\\n  padding:##padding##;\\n  box-sizing:border-box;\\n  display:block;\\n  width:100%;\\n }\\n.hermes .tp-tab-date\\n  {\\n  display:block;\\n  color:rgb(##param1-color##);\\n  font-weight:600;\\n  font-size:##param1-size##px;\\n  margin-bottom:10px;\\n  }\\n.hermes .tp-tab-title \\n{\\n    display:block;\\t\\n    color:rgb(##param2-color##);\\n    font-size:##param2-size##px;\\n    font-weight:800;\\n    text-transform:uppercase;\\n   line-height:##param2-size##px;\\n}\\n\\n.hermes .tp-tab.selected .tp-tab-content-wrapper:after {\\n  width: 0px;\\n\\theight: 0px;\\n\\tborder-style: solid;\\n\\tborder-width: 25px 0 25px 10px;\\n\\tborder-color: transparent transparent transparent ##back-color##;\\n\\tcontent:\\\\\\" \\\\\\";\\n    position:absolute;\\n    right:0px;\\n    bottom:50%;\\n    margin-bottom:-25px;\\n}\\n\\n\\n\\/* media queries *\\/\\n@media only screen and (max-width: 960px) {\\n  .hermes .tp-tab .tp-tab-title {font-size:14px;line-height:16px;}\\n  .hermes .tp-tab-date { font-size:11px; line-height:13px;margin-bottom:10px;}\\n  .hermes .tp-tab-content { padding:15px 15px 15px 25px;}\\n}\\n@media only screen and (max-width: 768px) {\\n  .hermes .tp-tab .tp-tab-title {font-size:12px;line-height:14px;}\\n  .hermes .tp-tab-date {font-size:10px; line-height:12px;margin-bottom:5px;}\\n  .hermes .tp-tab-content {padding:10px 10px 10px 20px;}\\n}\\n\\n\\/* BOTTOM HORIZONTAL *\\/\\n.hermes .nav-pos-ver-bottom.nav-dir-horizontal .tp-tab-image             { margin-top:40%; }\\n.hermes .nav-pos-ver-bottom.nav-dir-horizontal .tp-tab-content-wrapper   { position:absolute; bottom:auto;top:0px; padding-top:10px;}\\n.hermes .nav-pos-ver-bottom.nav-dir-horizontal                           { padding-right:0px; }\\n.hermes .nav-pos-ver-bottom.nav-dir-horizontal .tp-tab-bg                { left:0px; top:10px;}\\n.hermes .nav-pos-ver-bottom.nav-dir-horizontal.tp-tab.selected .tp-tab-content-wrapper:after {  \\n  border-width: 0px 25px 10px 25px;\\n  border-color: transparent transparent rgba(##back-color##) transparent;\\n  top:0px;\\n  left:50%;\\n  margin-left:0px 0px 0px -25px;\\n}\\n\\n\\/* CENTER HORIZONTAL *\\/\\n.hermes .nav-pos-ver-center.nav-dir-horizontal .tp-tab-image             { margin-top:40%; }\\n.hermes .nav-pos-ver-center.nav-dir-horizontal .tp-tab-content-wrapper   { position:absolute; bottom:auto;top:0px; padding-top:10px;}\\n.hermes .nav-pos-ver-center.nav-dir-horizontal                           { padding-right:0px; }\\n.hermes .nav-pos-ver-center.nav-dir-horizontal .tp-tab-bg                { left:0px; top:10px;}\\n.hermes .nav-pos-ver-center.nav-dir-horizontal.tp-tab.selected .tp-tab-content-wrapper:after {  \\n  border-width: 0px 25px 10px 25px;\\n  border-color: transparent transparent rgba(##back-color##) transparent;\\n  top:0px;\\n  left:50%;\\n  margin:0px 0px 0px -25px;\\n}\\n\\n\\/* BOTTOM HORIZONTAL *\\/\\n.hermes .nav-pos-ver-top.nav-dir-horizontal .tp-tab-content-wrapper   { padding-bottom:10px;}\\n.hermes .nav-pos-ver-top.nav-dir-horizontal                           { padding-right:0px; }\\n.hermes .nav-pos-ver-top.nav-dir-horizontal .tp-tab-bg                { left:0px; top:-10px;}\\n.hermes .nav-pos-ver-top.nav-dir-horizontal.tp-tab.selected .tp-tab-content-wrapper:after {  \\n  border-width: 10px 25px 0px 25px;\\n  border-color: rgba(##back-color##) transparent transparent transparent;\\n  bottom:0px;\\n  left:50%;\\n  margin:0px 0px 0px -25px;\\n}\\n\\n\\/* RIGHT VEERTICAL *\\/\\n.hermes .nav-pos-hor-right.nav-dir-vertical .tp-tab-content-wrapper { padding-left:10px; padding-right:0px; left:0px;} \\n.hermes .nav-pos-hor-right.nav-dir-vertical { padding-left:10px; padding-right:0px;}\\n.hermes .nav-pos-hor-right.nav-dir-vertical .tp-tab-bg  { left:10px;}\\n.hermes .nav-pos-hor-right.nav-dir-vertical.tp-tab.selected .tp-tab-content-wrapper:after {  \\n  border-width: 25px 10px 25px 0px;\\n  border-color:transparent  rgba(##back-color##) transparent transparent;\\n  right:auto;\\n  left:0px;    \\n}\\n\\n"}',
			'settings' => '{"width":{"thumbs":"160","arrows":"160","bullets":"160","tabs":"240"},"height":{"thumbs":"160","arrows":"160","bullets":"160","tabs":"260"},"original":{"css":{"arrows":".hermes.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:#000;\\n\\tbackground:rgba(0,0,0,0.5);\\n\\twidth:30px;\\n\\theight:110px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n}\\n\\n.hermes.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:15px;\\n\\tcolor:#fff;\\n\\tdisplay:block;\\n\\tline-height: 110px;\\n\\ttext-align: center;\\n    transform:translateX(0px);\\n    -webkit-transform:translateX(0px);\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n}\\n.hermes.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e824\\\\\\";\\n}\\n.hermes.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e825\\\\\\";\\n}\\n.hermes.tparrows.tp-leftarrow:hover:before {\\n    transform:translateX(-20px);\\n    -webkit-transform:translateX(-20px);\\n     opacity:0;\\n}\\n.hermes.tparrows.tp-rightarrow:hover:before {\\n    transform:translateX(20px);\\n    -webkit-transform:translateX(20px);\\n     opacity:0;\\n}\\n\\n.hermes .tp-arr-allwrapper {\\n    overflow:hidden;\\n    position:absolute;\\n\\twidth:180px;\\n    height:140px;\\n    top:0px;\\n    left:0px;\\n    visibility:hidden;\\n      -webkit-transition: -webkit-transform 0.3s 0.3s;\\n  transition: transform 0.3s 0.3s;\\n  -webkit-perspective: 1000px;\\n  perspective: 1000px;\\n    }\\n.hermes.tp-rightarrow .tp-arr-allwrapper {\\n   right:0px;left:auto;\\n      }\\n.hermes.tparrows:hover .tp-arr-allwrapper {\\n   visibility:visible;\\n          }\\n.hermes .tp-arr-imgholder {\\n  width:180px;position:absolute;\\n  left:0px;top:0px;height:110px;\\n  transform:translateX(-180px);\\n  -webkit-transform:translateX(-180px);\\n  transition:all 0.3s;\\n  transition-delay:0.3s;\\n}\\n.hermes.tp-rightarrow .tp-arr-imgholder{\\n    transform:translateX(180px);\\n  -webkit-transform:translateX(180px);\\n      }\\n  \\n.hermes.tparrows:hover .tp-arr-imgholder {\\n   transform:translateX(0px);\\n   -webkit-transform:translateX(0px);            \\n}\\n.hermes .tp-arr-titleholder {\\n  top:110px;\\n  width:180px;\\n  text-align:left; \\n  display:block;\\n  padding:0px 10px;\\n  line-height:30px; background:#000;\\n  background:rgba(0,0,0,0.75);color:#fff;\\n  font-weight:600; position:absolute;\\n  font-size:12px;\\n  white-space:nowrap;\\n  letter-spacing:1px;\\n  -webkit-transition: all 0.3s;\\n  transition: all 0.3s;\\n  -webkit-transform: rotateX(-90deg);\\n  transform: rotateX(-90deg);\\n  -webkit-transform-origin: 50% 0;\\n  transform-origin: 50% 0;\\n  box-sizing:border-box;\\n\\n}\\n.hermes.tparrows:hover .tp-arr-titleholder {\\n    -webkit-transition-delay: 0.6s;\\n  transition-delay: 0.6s;\\n  -webkit-transform: rotateX(0deg);\\n  transform: rotateX(0deg);\\n}\\n","bullets":".hermes.tp-bullets {\\n}\\n\\n.hermes .tp-bullet {\\n    overflow:hidden;\\n    border-radius:50%;\\n    width:16px;\\n    height:16px;\\n    background-color: rgba(0, 0, 0, 0);\\n    box-shadow: inset 0 0 0 2px #FFF;\\n    -webkit-transition: background 0.3s ease;\\n    transition: background 0.3s ease;\\n    position:absolute;\\n}\\n\\n.hermes .tp-bullet:hover {\\n\\t  background-color: rgba(0, 0, 0, 0.2);\\n}\\n.hermes .tp-bullet:after {\\n  content: \\\\\' \\\\\';\\n  position: absolute;\\n  bottom: 0;\\n  height: 0;\\n  left: 0;\\n  width: 100%;\\n  background-color: #FFF;\\n  box-shadow: 0 0 1px #FFF;\\n  -webkit-transition: height 0.3s ease;\\n  transition: height 0.3s ease;\\n}\\n.hermes .tp-bullet.selected:after {\\n  height:100%;\\n}\\n","tabs":".hermes .tp-tab { \\n  opacity:1;  \\n  padding-right:10px;\\n  box-sizing:border-box;\\n }\\n.hermes .tp-tab-image \\n{ \\n  width:100%;\\n  height:60%;\\n  position:relative;\\n}\\n.hermes .tp-tab-content \\n{\\n    background:rgb(54,54,54); \\n    position:absolute;\\n    padding:20px 20px 20px 30px;\\n    box-sizing:border-box;\\n    color:#fff;\\n  display:block;\\n  width:100%;\\n  min-height:40%;\\n  bottom:0px;\\n  left:-10px;\\n  }\\n.hermes .tp-tab-date\\n  {\\n  display:block;\\n  color:#888;\\n  font-weight:600;\\n  font-size:12px;\\n  margin-bottom:10px;\\n  }\\n.hermes .tp-tab-title \\n{\\n    display:block;\\t\\n    color:#fff;\\n    font-size:16px;\\n    font-weight:800;\\n    text-transform:uppercase;\\n   line-height:19px;\\n}\\n\\n.hermes .tp-tab.selected .tp-tab-title:after {\\n    width: 0px;\\n\\theight: 0px;\\n\\tborder-style: solid;\\n\\tborder-width: 30px 0 30px 10px;\\n\\tborder-color: transparent transparent transparent rgb(54,54,54);\\n\\tcontent:\\\\\\" \\\\\\";\\n    position:absolute;\\n    right:-9px;\\n    bottom:50%;\\n    margin-bottom:-30px;\\n}\\n.hermes .tp-tab-mask {\\n     padding-right:10px !important;\\n          }\\n\\n\\/* MEDIA QUERIES *\\/\\n@media only screen and (max-width: 960px) {\\n  .hermes .tp-tab .tp-tab-title {font-size:14px;line-height:16px;}\\n  .hermes .tp-tab-date { font-size:11px; line-height:13px;margin-bottom:10px;}\\n  .hermes .tp-tab-content { padding:15px 15px 15px 25px;}\\n}\\n@media only screen and (max-width: 768px) {\\n  .hermes .tp-tab .tp-tab-title {font-size:12px;line-height:14px;}\\n  .hermes .tp-tab-date {font-size:10px; line-height:12px;margin-bottom:5px;}\\n  .hermes .tp-tab-content {padding:10px 10px 10px 20px;}\\n}"},"markup":{"arrows":"<div class=\\\\\\"tp-arr-allwrapper\\\\\\">\\n\\t<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n\\t<div class=\\\\\\"tp-arr-titleholder\\\\\\">{{title}}<\\/div>\\t\\n<\\/div>","bullets":"","tabs":"<span class=\\\\\\"tp-tab-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-tab-content\\\\\\">\\n\\t<span class=\\\\\\"tp-tab-date\\\\\\">{{param1}}<\\/span>\\n\\t<span class=\\\\\\"tp-tab-title\\\\\\">{{param2}}<\\/span>\\n<\\/span>"}},"placeholders":[{"title":"Background","handle":"back-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(0,0,0,0.5)"}},{"title":"Width","handle":"width","type":"custom","nav-type":"arrows","data":{"custom":"30"}},{"title":"Height","handle":"height","type":"custom","nav-type":"arrows","data":{"custom":"110"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"arrows","data":{"custom":"15"}},{"title":"Wrapper-Height","handle":"wrapper-height","type":"custom","nav-type":"arrows","data":{"custom":"140"}},{"title":"Wrapper-Width","handle":"wrapper-width","type":"custom","nav-type":"arrows","data":{"custom":"180"}},{"title":"Title-Size","handle":"title-size","type":"custom","nav-type":"arrows","data":{"custom":"12"}},{"title":"Title-Background","handle":"title-back-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(0,0,0,0.75)"}},{"title":"Title-Font-Color","handle":"title-font-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},{"title":"Bullet-Size","handle":"bullet-size","type":"custom","nav-type":"bullets","data":{"custom":"16"}},{"title":"Bullet-Color","handle":"bullet-color","type":"color","nav-type":"bullets","data":{"color":"#ffffff"}},{"title":"Border-Thickness","handle":"border","type":"custom","nav-type":"bullets","data":{"custom":"2"}},{"title":"Hover-Bullet","handle":"bullet-hover","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"rgba(0,0,0,0.21)"}},{"title":"Background","handle":"back-color","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#000000"}},{"title":"Param-1-Color","handle":"param1-color","type":"color","nav-type":"tabs","data":{"color":"#888888"}},{"title":"Param-2-Color","handle":"param2-color","type":"color","nav-type":"tabs","data":{"color":"#ffffff"}},{"title":"Param-1-Size","handle":"param1-size","type":"custom","nav-type":"tabs","data":{"custom":"12"}},{"title":"Param-2-Size","handle":"param2-size","type":"custom","nav-type":"tabs","data":{"custom":"16"}},{"title":"Padding","handle":"padding","type":"custom","nav-type":"tabs","data":{"custom":"20px"}}]}');
		
		$navigations[] = array('id' => 5006,'default' => true,'name' => 'Custom','handle' => 'custom','markup' => '{"thumbs":"<span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>","arrows":"","bullets":"","tabs":"<span class=\\\\\\"tp-tab-image\\\\\\"><\\/span>"}','css' => '{"thumbs":"","arrows":".custom.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:#000;\\n\\tbackground:rgba(0,0,0,0.5);\\n\\twidth:40px;\\n\\theight:40px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n}\\n.custom.tparrows:hover {\\n\\tbackground:#000;\\n}\\n.custom.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:15px;\\n\\tcolor:#fff;\\n\\tdisplay:block;\\n\\tline-height: 40px;\\n\\ttext-align: center;\\n}\\n.custom.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e824\\\\\\";\\n}\\n.custom.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\e825\\\\\\";\\n}\\n\\n","bullets":".custom.tp-bullets {\\n}\\n.custom.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n}\\n.custom .tp-bullet {\\n\\twidth:12px;\\n\\theight:12px;\\n\\tposition:absolute;\\n\\tbackground:#aaa;\\n    background:rgba(125,125,125,0.5);\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.custom .tp-bullet:hover,\\n.custom .tp-bullet.selected {\\n\\tbackground:rgb(125,125,125);\\n}\\n.custom .tp-bullet-image {\\n}\\n.custom .tp-bullet-title {\\n}\\n","tabs":""}','settings' => '""');

		$navigations[] = array(
			'id' => 5007,
			'default' => true,
			'name' => 'Hephaistos',
			'handle' => 'round-old',
			'markup' => '{"arrows":"","bullets":""}',
			'css' => '{"arrows":".hephaistos.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:rgba(##back-color##);\\n\\twidth:##back-size##px;\\n\\theight:##back-size##px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n    border-radius:50%;\\n}\\n.hephaistos.tparrows:hover {\\n\\tbackground:rgba(##back-hover##);\\n}\\n.hephaistos.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:18px;\\n\\tcolor:rgb(##arrow-color##);\\n\\tdisplay:block;\\n\\tline-height: ##back-size##px;\\n\\ttext-align: center;\\n}\\n.hephaistos.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"##left-arrow##\\\\\\";\\n    margin-left:-2px;\\n  \\n}\\n.hephaistos.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"##right-arrow##\\\\\\";\\n    margin-right:-2px;\\n}\\n\\n","bullets":".hephaistos .tp-bullet {\\n\\twidth:##bullet-size##px;\\n\\theight:##bullet-size##px;\\n\\tposition:absolute;\\n\\tbackground:rgba(##back-color##);\\n\\tborder:##border-size##px solid rgba(##border-color##);\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n    box-shadow: 0px 0px 2px 1px rgba(130,130,130, 0.3);\\n}\\n.hephaistos .tp-bullet:hover,\\n.hephaistos .tp-bullet.selected {\\n\\tbackground:rgba(##back-hover-color##);\\n    border-color:rgba(##border-hover-color##);\\n}"}',
			'settings' => '{"width":{"thumbs":"160","arrows":"160","bullets":"161","tabs":"160"},"height":{"thumbs":"160","arrows":"160","bullets":"159","tabs":"160"},"original":{"css":{"arrows":".hephaistos.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:#000;\\n\\tbackground:rgba(0,0,0,0.5);\\n\\twidth:40px;\\n\\theight:40px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n    border-radius:50%;\\n}\\n.hephaistos.tparrows:hover {\\n\\tbackground:#000;\\n}\\n.hephaistos.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:18px;\\n\\tcolor:#fff;\\n\\tdisplay:block;\\n\\tline-height: 40px;\\n\\ttext-align: center;\\n}\\n.hephaistos.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"e82c\\\\\\";\\n  margin-left:-2px;\\n  \\n}\\n.hephaistos.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"e82d\\\\\\";\\n   margin-right:-2px;\\n}\\n\\n","bullets":".hephaistos.tp-bullets {\\n}\\n.hephaistos.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n}\\n.hephaistos .tp-bullet {\\n\\twidth:12px;\\n\\theight:12px;\\n\\tposition:absolute;\\n\\tbackground:#999;\\n\\tborder:3px solid #f5f5f5;\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n  box-shadow: 0px 0px 2px 1px rgba(130,130,130, 0.3);\\n\\n}\\n.hephaistos .tp-bullet:hover,\\n.hephaistos .tp-bullet.selected {\\n\\tbackground:#fff;\\n    border-color:#000;\\n}\\n.hephaistos .tp-bullet-image {\\n}\\n.hephaistos .tp-bullet-title {\\n}\\n"},"markup":{"arrows":"","bullets":""}},"placeholders":[{"title":"Background","handle":"back-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(0,0,0,0.5)"}},{"title":"BG-Size","handle":"back-size","type":"custom","nav-type":"arrows","data":{"custom":"40"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},{"title":"Hover-Background","handle":"back-hover","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#000000"}},{"title":"Bullet-Size","handle":"bullet-size","type":"custom","nav-type":"bullets","data":{"custom":"12"}},{"title":"Background","handle":"back-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#999999"}},{"title":"Border-Color","handle":"border-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"rgba(255,255,255,0.9)"}},{"title":"Border-Size","handle":"border-size","type":"custom","nav-type":"bullets","data":{"custom":"3"}},{"title":"Hover-Background","handle":"back-hover-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#ffffff"}},{"title":"Hover-Border","handle":"border-hover-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#000000"}},{"title":"Left-Arrow","handle":"left-arrow","type":"custom","nav-type":"arrows","data":{"custom":"\\\\\\\\e82c"}},{"title":"Right-Arrow","handle":"right-arrow","type":"custom","nav-type":"arrows","data":{"custom":"\\\\\\\\e82d"}}]}');
			
		$navigations[] = array(
			'id' => 5008,
			'default' => true,
			'name' => 'Persephone',
			'handle' => 'square-old',
			'markup' => '{"arrows":"","bullets":""}',
			'css' => '{"arrows":".persephone.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:rgba(##back-color##);\\n\\twidth:##back-size##px;\\n\\theight:##back-size##px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n    border:1px solid rgba(##border-rgba##);\\n}\\n.persephone.tparrows:hover {\\n\\tbackground:rgba(##back-hover##);\\n}\\n.persephone.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:##arrow-size##px;\\n\\tcolor: rgb(##arrow-color##);\\n\\tdisplay:block;\\n\\tline-height: ##back-size##px;\\n\\ttext-align: center;\\n}\\n.persephone.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"##left-arrow##\\\\\\";\\n}\\n.persephone.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"##right-arrow##\\n\\\\\\";\\n}\\n\\n","bullets":".persephone .tp-bullet {\\n\\twidth:##bullet-size##px;\\n\\theight:##bullet-size##px;\\n\\tposition:absolute;\\n\\tbackground:rgba(##back-color##);\\n\\tborder:1px solid rgba(##border-color##);\\t\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.persephone .tp-bullet:hover,\\n.persephone .tp-bullet.selected {\\n\\tbackground:rgba(##back-hover##);\\n}\\n\\n"}',
			'settings' => '{"original":{"css":{"arrows":".persephone.tparrows {\\n\\tcursor:pointer;\\n\\tbackground:#aaa;\\n\\tbackground:rgba(200,200,200,0.5);\\n\\twidth:40px;\\n\\theight:40px;\\n\\tposition:absolute;\\n\\tdisplay:block;\\n\\tz-index:100;\\n  border:1px solid #f5f5f5;\\n}\\n.persephone.tparrows:hover {\\n\\tbackground:#333;\\n}\\n.persephone.tparrows:before {\\n\\tfont-family: \\\\\\"revicons\\\\\\";\\n\\tfont-size:15px;\\n\\tcolor:#fff;\\n\\tdisplay:block;\\n\\tline-height: 40px;\\n\\ttext-align: center;\\n}\\n.persephone.tparrows.tp-leftarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e824\\\\\\";\\n}\\n.persephone.tparrows.tp-rightarrow:before {\\n\\tcontent: \\\\\\"\\\\\\\\e825\\\\\\";\\n}\\n\\n","bullets":".persephone.tp-bullets {\\n}\\n.persephone.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground:#transparent;\\n\\tpadding:10px;\\n\\tmargin-left:-10px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n}\\n.persephone .tp-bullet {\\n\\twidth:12px;\\n\\theight:12px;\\n\\tposition:absolute;\\n\\tbackground:#aaa;\\n\\tborder:1px solid #e5e5e5;\\t\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.persephone .tp-bullet:hover,\\n.persephone .tp-bullet.selected {\\n\\tbackground:#222;\\n}\\n.persephone .tp-bullet-image {\\n}\\n.persephone .tp-bullet-title {\\n}\\n"},"markup":{"arrows":"","bullets":""}},"placeholders":[{"title":"Background","handle":"back-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(201,201,201,0.75)"}},{"title":"Size","handle":"back-size","type":"custom","nav-type":"arrows","data":{"custom":"40"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"arrows","data":{"custom":"15"}},{"title":"Hover-Background","handle":"back-hover","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#000000"}},{"title":"Bullet-Size","handle":"bullet-size","type":"custom","nav-type":"bullets","data":{"custom":"12"}},{"title":"Background","handle":"back-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#aaaaaa"}},{"title":"Border-Color","handle":"border-color","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#e5e5e5"}},{"title":"Hover-Background","handle":"back-hover","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#000000"}},{"title":"Border","handle":"border-rgba","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#ffffff"}},{"title":"Left-Arrow","handle":"left-arrow","type":"custom","nav-type":"arrows","data":{"custom":"\\\\\\\\e824"}},{"title":"Right-Arrow","handle":"right-arrow","type":"custom","nav-type":"arrows","data":{"custom":"\\\\\\\\e825"}}]}');
			
		$navigations[] = array(
			'id' => 5009,
			'default' => true,
			'name' => 'Erinyen',
			'handle' => 'navbar-old',
			'markup' => '{"arrows":"<div class=\\\\\\"tp-title-wrap\\\\\\">\\n  \\t<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n    <div class=\\\\\\"tp-arr-img-over\\\\\\"><\\/div>\\n\\t<span class=\\\\\\"tp-arr-titleholder\\\\\\">{{title}}<\\/span>\\n <\\/div>\\n\\n","bullets":"","tabs":"<div class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/div>\\n<div class=\\\\\\"tp-tab-desc\\\\\\">{{description}}<\\/div>","thumbs":"<span class=\\\\\\"tp-thumb-over\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-title\\\\\\">{{title}}<\\/span>\\n<span class=\\\\\\"tp-thumb-more\\\\\\"><\\/span>"}',
			'css' => '{"arrows":".erinyen.tparrows {\\n  cursor:pointer;\\n  background:rgba(##back-color##);\\n  min-width:##back-size##px;\\n  min-height:##back-size##px;\\n  position:absolute;\\n  display:block;\\n  z-index:100;\\n  border-radius:50%;   \\n}\\n\\n.erinyen.tparrows:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:##arrow-size##px;\\n  color:rgb(##arrow-color##);\\n  display:block;\\n  line-height:##back-size##px;\\n  text-align: center;    \\n  z-index:2;\\n  position:relative;\\n}\\n.erinyen.tparrows.tp-leftarrow:before {\\n  content: \\\\\\"##leftarrow##\\\\\\";\\n}\\n.erinyen.tparrows.tp-rightarrow:before {\\n  content: \\\\\\"##right-arrow##\\\\\\";\\n}\\n\\n.erinyen .tp-title-wrap { \\n  position:absolute;\\n  z-index:1;\\n  display:inline-block;\\n  background:rgba(0,0,0,0.5);\\n  min-height:##back-size##px;\\n  line-height:##back-size##px;\\n  top:0px;\\n  margin-left:0px;\\n  border-radius:##title-wrap-border-radius##px;\\n  overflow:hidden; \\n  transition: opacity 0.3s;\\n  -webkit-transition:opacity 0.3s;\\n  -moz-transition:opacity 0.3s;\\n  -webkit-transform: scale(0);\\n  -moz-transform: scale(0);\\n  transform: scale(0);  \\n  visibility:hidden;\\n  opacity:0;\\n}\\n\\n.erinyen.tparrows:hover .tp-title-wrap{\\n  -webkit-transform: scale(1);\\n  -moz-transform: scale(1);\\n  transform: scale(1);\\n  opacity:1;\\n  visibility:visible;\\n}\\n        \\n .erinyen.tp-rightarrow .tp-title-wrap { \\n   right:0px;\\n   margin-right:0px;margin-left:0px;\\n   -webkit-transform-origin:100% 50%;\\n  border-radius:##title-wrap-border-radius##px;\\n  padding-right:20px;\\n  padding-left:10px;\\n }\\n\\n\\n.erinyen.tp-leftarrow .tp-title-wrap { \\n   padding-left:20px;\\n  padding-right:10px;\\n}\\n\\n.erinyen .tp-arr-titleholder {\\n  letter-spacing: ##letter-spacing##px;\\n   position:relative;\\n  -webkit-transition: -webkit-transform 0.3s;\\n  transition: transform 0.3s;\\n  transform:translatex(200px);  \\n  text-transform:uppercase;\\n  color:rgb(##arrow-color##);\\n  font-weight:600;\\n  font-size:##font-size##px;\\n  line-height:##back-size##px;\\n  white-space:nowrap;\\n  padding:0px 20px;\\n  margin-left:11px;\\n  opacity:0;  \\n}\\n\\n.erinyen .tp-arr-imgholder {\\n  width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  background-position:center center;\\n  background-size:cover;\\n    }\\n .erinyen .tp-arr-img-over {\\n   width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n   background:rgba(##overlay-rgba##);\\n }\\n.erinyen.tp-rightarrow .tp-arr-titleholder {\\n   transform:translatex(-200px); \\n   margin-left:0px; margin-right:11px;\\n      }\\n\\n.erinyen.tparrows:hover .tp-arr-titleholder {\\n   transform:translatex(0px);\\n   -webkit-transform:translatex(0px);\\n  transition-delay: 0.1s;\\n  opacity:1;\\n}","bullets":".erinyen.tp-bullets {\\n}\\n.erinyen.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n    background: -moz-linear-gradient(top,  rgb(##back-top##) 0%, rgb(##back-bottom##) 100%); \\/* ff3.6+ *\\/\\n    background: -webkit-linear-gradient(top,  rgb(##back-top##) 0%,rgb(##back-bottom##) 100%); \\/* chrome10+,safari5.1+ *\\/\\n    background: -o-linear-gradient(top,  rgb(##back-top##) 0%,rgb(##back-bottom##) 100%); \\/* opera 11.10+ *\\/\\n    background: -ms-linear-gradient(top,  rgb(##back-top##) 0%,rgb(##back-bottom##) 100%); \\/* ie10+ *\\/\\n    background: linear-gradient(to bottom,  rgb(##back-top##) 0%,rgb(##back-bottom##) 100%); \\/* w3c *\\/\\n\\n\\tpadding:10px 15px;\\n\\tmargin-left:-15px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n   border-radius:10px;\\n   box-shadow:0px 0px 2px 1px rgba(33,33,33,0.3);\\n}\\n.erinyen .tp-bullet {\\n\\twidth:##bullet-size##px;\\n\\theight:##bullet-size##px;\\n\\tposition:absolute;\\n\\tbackground:##bullet-back##;\\t\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.erinyen .tp-bullet:hover,\\n.erinyen .tp-bullet.selected {\\nbackground: -moz-linear-gradient(top,  ##bullet-top## 0%, ##bullet-bottom## 100%); \\/* ff3.6+ *\\/\\nbackground: -webkit-linear-gradient(top,  ##bullet-top## 0%,##bullet-bottom## 100%); \\/* chrome10+,safari5.1+ *\\/\\nbackground: -o-linear-gradient(top,  ##bullet-top## 0%,##bullet-bottom## 100%); \\/* opera 11.10+ *\\/\\nbackground: -ms-linear-gradient(top,  ##bullet-top## 0%,##bullet-bottom## 100%); \\/* ie10+ *\\/\\nbackground: linear-gradient(to bottom,  ##bullet-top## 0%,##bullet-bottom## 100%); \\/* w3c *\\/\\nborder:1px solid #555;\\nwidth:##bullet-size##px;\\nheight:##bullet-size##px;\\n}\\n\\n","tabs":".erinyen .tp-tab-title {\\n    color:rgb(##title-color##);\\n    font-size:##title-size##px;\\n    font-weight:##title-font-weight##;\\n    text-transform:uppercase;\\n    font-family:\\\\\\"##title-font##\\\\\\";\\n    margin-bottom:5px;\\n    line-height:##title-line-height##px;\\n}\\n\\n.erinyen .tp-tab-desc {\\n\\tfont-size:##desc-size##px;\\n    font-weight:##desc-font-weight##;\\n    color:rgb(##desc-color##);\\n    line-height:##desc-line-height##px;\\n\\tfont-family:\\\\\\"##desc-font##\\\\\\";\\n}\\n      ","thumbs":".erinyen .tp-thumb {\\nopacity:1\\n}\\n\\n.erinyen .tp-thumb-over {\\n  background:rgba(##overlay-color##);\\n  width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  z-index:1;\\n  -webkit-transition:all 0.3s;\\n  transition:all 0.3s;\\n}\\n\\n.erinyen .tp-thumb-more:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:##arrow-size##px;\\n  color:rgb(##arrow-color##);\\n  display:block;\\n  line-height: ##lineheight##px;\\n  text-align: left;    \\n  z-index:2;\\n  position:absolute;\\n  top:20px;\\n  right:20px;\\n  z-index:2;\\n}\\n.erinyen .tp-thumb-more:before {\\n  content: \\\\\\"\\\\\\\e825\\\\\\";\\n}\\n\\n.erinyen .tp-thumb-title {\\n  font-family:\\\\\\"##title-font##\\\\\\";\\n  letter-spacing:1px;\\n  font-size:##title-size##px;\\n  color:rgb(##title-color##);\\n  display:block;\\n  line-height: ##lineheight##px;\\n  text-align: left;    \\n  z-index:2;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  z-index:2;\\n  padding:##padding##;\\n  width:100%;\\n  height:100%;\\n  box-sizing:border-box;\\n  transition:all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  font-weight:500;\\n}\\n\\n.erinyen .tp-thumb.selected .tp-thumb-more:before,\\n.erinyen .tp-thumb:hover .tp-thumb-more:before {\\n color:rgb(##arrow-hover##);\\n}\\n\\n.erinyen .tp-thumb.selected .tp-thumb-over,\\n.erinyen .tp-thumb:hover .tp-thumb-over {\\n background:rgb(##back-hover##);\\n}\\n.erinyen .tp-thumb.selected .tp-thumb-title,\\n.erinyen .tp-thumb:hover .tp-thumb-title {\\n  color:rgb(##title-hover##);\\n\\n}\\n"}',
			'settings' => '{"width":{"thumbs":"200","arrows":"160","bullets":"160","tabs":"160"},"height":{"thumbs":"130","arrows":"160","bullets":"160","tabs":"160"},"original":{"css":{"arrows":".erinyen.tparrows {\\n  cursor:pointer;\\n  background:#000;\\n  background:rgba(0,0,0,0.5);\\n  min-width:70px;\\n  min-height:70px;\\n  position:absolute;\\n  display:block;\\n  z-index:100;\\n  border-radius:35px;   \\n}\\n\\n.erinyen.tparrows:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:20px;\\n  color:#fff;\\n  display:block;\\n  line-height: 70px;\\n  text-align: center;    \\n  z-index:2;\\n  position:relative;\\n}\\n.erinyen.tparrows.tp-leftarrow:before {\\n  content: \\\\\\"\\\\\\\\e824\\\\\\";\\n}\\n.erinyen.tparrows.tp-rightarrow:before {\\n  content: \\\\\\"\\\\\\\\e825\\\\\\";\\n}\\n\\n.erinyen .tp-title-wrap { \\n  position:absolute;\\n  z-index:1;\\n  display:inline-block;\\n  background:#000;\\n  background:rgba(0,0,0,0.5);\\n  min-height:70px;\\n  line-height:70px;\\n  top:0px;\\n  margin-left:0px;\\n  border-radius:35px;\\n  overflow:hidden; \\n  transition: opacity 0.3s;\\n  -webkit-transition:opacity 0.3s;\\n  -moz-transition:opacity 0.3s;\\n  -webkit-transform: scale(0);\\n  -moz-transform: scale(0);\\n  transform: scale(0);  \\n  visibility:hidden;\\n  opacity:0;\\n}\\n\\n.erinyen.tparrows:hover .tp-title-wrap{\\n  -webkit-transform: scale(1);\\n  -moz-transform: scale(1);\\n  transform: scale(1);\\n  opacity:1;\\n  visibility:visible;\\n}\\n        \\n .erinyen.tp-rightarrow .tp-title-wrap { \\n   right:0px;\\n   margin-right:0px;margin-left:0px;\\n   -webkit-transform-origin:100% 50%;\\n  border-radius:35px;\\n  padding-right:20px;\\n  padding-left:10px;\\n }\\n\\n\\n.erinyen.tp-leftarrow .tp-title-wrap { \\n   padding-left:20px;\\n  padding-right:10px;\\n}\\n\\n.erinyen .tp-arr-titleholder {\\n  letter-spacing: 3px;\\n   position:relative;\\n  -webkit-transition: -webkit-transform 0.3s;\\n  transition: transform 0.3s;\\n  transform:translateX(200px);  \\n  text-transform:uppercase;\\n  color:#fff;\\n  font-weight:600;\\n  font-size:13px;\\n  line-height:70px;\\n  white-space:nowrap;\\n  padding:0px 20px;\\n  margin-left:11px;\\n  opacity:0;  \\n}\\n\\n.erinyen .tp-arr-imgholder {\\n  width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  background-position:center center;\\n  background-size:cover;\\n    }\\n .erinyen .tp-arr-img-over {\\n   width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n   background:#000;\\n   background:rgba(0,0,0,0.5);\\n        }\\n.erinyen.tp-rightarrow .tp-arr-titleholder {\\n   transform:translateX(-200px); \\n   margin-left:0px; margin-right:11px;\\n      }\\n\\n.erinyen.tparrows:hover .tp-arr-titleholder {\\n   transform:translateX(0px);\\n   -webkit-transform:translateX(0px);\\n  transition-delay: 0.1s;\\n  opacity:1;\\n}","bullets":".erinyen.tp-bullets {\\n}\\n.erinyen.tp-bullets:before {\\n\\tcontent:\\\\\\" \\\\\\";\\n\\tposition:absolute;\\n\\twidth:100%;\\n\\theight:100%;\\n\\tbackground: #555555; \\/* old browsers *\\/\\n    background: -moz-linear-gradient(top,  #555555 0%, #222222 100%); \\/* ff3.6+ *\\/\\n    background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#555555), color-stop(100%,#222222)); \\/* chrome,safari4+ *\\/\\n    background: -webkit-linear-gradient(top,  #555555 0%,#222222 100%); \\/* chrome10+,safari5.1+ *\\/\\n    background: -o-linear-gradient(top,  #555555 0%,#222222 100%); \\/* opera 11.10+ *\\/\\n    background: -ms-linear-gradient(top,  #555555 0%,#222222 100%); \\/* ie10+ *\\/\\n    background: linear-gradient(to bottom,  #555555 0%,#222222 100%); \\/* w3c *\\/\\n    filter: progid:dximagetransform.microsoft.gradient( startcolorstr=\\\\\\"#555555\\\\\\", endcolorstr=\\\\\\"#222222\\\\\\",gradienttype=0 ); \\/* ie6-9 *\\/\\n\\tpadding:10px 15px;\\n\\tmargin-left:-15px;margin-top:-10px;\\n\\tbox-sizing:content-box;\\n   border-radius:10px;\\n   box-shadow:0px 0px 2px 1px rgba(33,33,33,0.3);\\n}\\n.erinyen .tp-bullet {\\n\\twidth:13px;\\n\\theight:13px;\\n\\tposition:absolute;\\n\\tbackground:#111;\\t\\n\\tborder-radius:50%;\\n\\tcursor: pointer;\\n\\tbox-sizing:content-box;\\n}\\n.erinyen .tp-bullet:hover,\\n.erinyen .tp-bullet.selected {\\n\\tbackground: #e5e5e5; \\/* old browsers *\\/\\nbackground: -moz-linear-gradient(top,  #e5e5e5 0%, #999999 100%); \\/* ff3.6+ *\\/\\nbackground: -webkit-gradient(linear, left top, left bottom, color-stop(0%,#e5e5e5), color-stop(100%,#999999)); \\/* chrome,safari4+ *\\/\\nbackground: -webkit-linear-gradient(top,  #e5e5e5 0%,#999999 100%); \\/* chrome10+,safari5.1+ *\\/\\nbackground: -o-linear-gradient(top,  #e5e5e5 0%,#999999 100%); \\/* opera 11.10+ *\\/\\nbackground: -ms-linear-gradient(top,  #e5e5e5 0%,#999999 100%); \\/* ie10+ *\\/\\nbackground: linear-gradient(to bottom,  #e5e5e5 0%,#999999 100%); \\/* w3c *\\/\\nfilter: progid:dximagetransform.microsoft.gradient( startcolorstr=\\\\\\"#e5e5e5\\\\\\", endcolorstr=\\\\\\"#999999\\\\\\",gradienttype=0 ); \\/* ie6-9 *\\/\\n  border:1px solid #555;\\n  width:12px;height:12px;\\n}\\n.erinyen .tp-bullet-image {\\n}\\n.erinyen .tp-bullet-title {\\n}\\n","tabs":".erinyen .tp-tab-title {\\n    color:#a8d8ee;\\n    font-size:13px;\\n    font-weight:700;\\n    text-transform:uppercase;\\n    font-family:\\\\\\"Roboto Slab\\\\\\"\\n    margin-bottom:5px;\\n}\\n\\n.erinyen .tp-tab-desc {\\n\\tfont-size:18px;\\n    font-weight:400;\\n    color:#fff;\\n    line-height:25px;\\n\\tfont-family:\\\\\\"Roboto Slab\\\\\\";\\n}\\n      ","thumbs":".erinyen .tp-thumb {\\nopacity:1\\n}\\n\\n.erinyen .tp-thumb-over {\\n  background:#000;\\n  background:rgba(0,0,0,0.25);\\n  width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  z-index:1;\\n  -webkit-transition:all 0.3s;\\n  transition:all 0.3s;\\n}\\n\\n.erinyen .tp-thumb-more:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:12px;\\n  color:#aaa;\\n  color:rgba(255,255,255,0.75);\\n  display:block;\\n  line-height: 12px;\\n  text-align: left;    \\n  z-index:2;\\n  position:absolute;\\n  top:20px;\\n  right:20px;\\n  z-index:2;\\n}\\n.erinyen .tp-thumb-more:before {\\n  content: \\\\\\"\\\\\\\\e825\\\\\\";\\n}\\n\\n.erinyen .tp-thumb-title {\\n  font-family:\\\\\\"Raleway\\\\\\";\\n  letter-spacing:1px;\\n  font-size:12px;\\n  color:#fff;\\n  display:block;\\n  line-height: 15px;\\n  text-align: left;    \\n  z-index:2;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  z-index:2;\\n  padding:20px 35px 20px 20px;\\n  width:100%;\\n  height:100%;\\n  box-sizing:border-box;\\n  transition:all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  font-weight:500;\\n}\\n\\n.erinyen .tp-thumb.selected .tp-thumb-more:before,\\n.erinyen .tp-thumb:hover .tp-thumb-more:before {\\n color:#aaa;\\n\\n}\\n\\n.erinyen .tp-thumb.selected .tp-thumb-over,\\n.erinyen .tp-thumb:hover .tp-thumb-over {\\n background:#fff;\\n}\\n.erinyen .tp-thumb.selected .tp-thumb-title,\\n.erinyen .tp-thumb:hover .tp-thumb-title {\\n  color:#000;\\n\\n}\\n"},"markup":{"arrows":"<div class=\\\\\\"tp-title-wrap\\\\\\">\\n  \\t<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n    <div class=\\\\\\"tp-arr-img-over\\\\\\"><\\/div>\\n\\t<span class=\\\\\\"tp-arr-titleholder\\\\\\">{{title}}<\\/span>\\n <\\/div>\\n\\n","bullets":"","tabs":"<div class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/div>\\n<div class=\\\\\\"tp-tab-desc\\\\\\">{{description}}<\\/div>","thumbs":"<span class=\\\\\\"tp-thumb-over\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-title\\\\\\">{{title}}<\\/span>\\n<span class=\\\\\\"tp-thumb-more\\\\\\"><\\/span>"}},"placeholders":[{"title":"BG-RGBA","handle":"back-color","type":"custom","nav-type":"arrows","data":{"custom":"0,0,0,0.5"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},{"title":"BG-Size","handle":"back-size","type":"custom","nav-type":"arrows","data":{"custom":"70"}},"",{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"arrows","data":{"custom":"20"}},{"title":"Title-Font-Size","handle":"font-size","type":"custom","nav-type":"arrows","data":{"custom":"13"}},{"title":"BG-Top","handle":"back-top","type":"color","nav-type":"bullets","data":{"color":"#545353"}},{"title":"BG-Bottom","handle":"back-bottom","type":"color","nav-type":"bullets","data":{"color":"#222222"}},{"title":"Bullet-BG-Top","handle":"bullet-top","type":"color","nav-type":"bullets","data":{"color":"#e5e5e5"}},{"title":"Bullet-BG-Bottom","handle":"bullet-bottom","type":"color","nav-type":"bullets","data":{"color":"#999999"}},{"title":"Bullet-BG","handle":"bullet-back","type":"color","nav-type":"bullets","data":{"color":"#111111"}},{"title":"Overlay","handle":"overlay-color","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"rgba(0,0,0,0.25)"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"thumbs","data":{"color":"#aaaaaa"}},{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"thumbs","data":{"custom":"12"}},{"title":"Title-Color","handle":"title-color","type":"color","nav-type":"thumbs","data":{"color":"#ffffff"}},{"title":"Title-Size","handle":"title-size","type":"custom","nav-type":"thumbs","data":{"custom":"12"}},{"title":"Hover-Arrow","handle":"arrow-hover","type":"color","nav-type":"thumbs","data":{"color":"#aaaaaa"}},{"title":"Hover-Background","handle":"back-hover","type":"color","nav-type":"thumbs","data":{"color":"#ffffff"}},{"title":"Hover-Title","handle":"title-hover","type":"color","nav-type":"thumbs","data":{"color":"#000000"}},{"title":"Title-Color","handle":"title-color","type":"color","nav-type":"tabs","data":{"color":"#a8d8ee"}},{"title":"Description-Color","handle":"desc-color","type":"color","nav-type":"tabs","data":{"color":"#ffffff"}},{"title":"Title-Size","handle":"title-size","type":"custom","nav-type":"tabs","data":{"custom":"13"}},{"title":"Description-Size","handle":"desc-size","type":"custom","nav-type":"tabs","data":{"custom":"18"}},{"title":"Title-Font","handle":"title-font","type":"font-family","nav-type":"tabs","data":{"font_family":"Roboto Slab"}},{"title":"Description-Font","handle":"desc-font","type":"font-family","nav-type":"tabs","data":{"font_family":"Roboto Slab"}},{"title":"Title-Wrap-Border-Radius","handle":"title-wrap-border-radius","type":"custom","nav-type":"arrows","data":{"custom":"35"}},{"title":"Title-Font-Family","handle":"title-font","type":"font-family","nav-type":"thumbs","data":{"font_family":"Raleway"}},{"title":"Left-Arrow","handle":"leftarrow","type":"custom","nav-type":"arrows","data":{"custom":"\\\\\\\\e824"}},{"title":"Right-Arrow","handle":"right-arrow","type":"custom","nav-type":"arrows","data":{"custom":"\\\\\\\\e825"}},{"title":"Letter-Spacing","handle":"letter-spacing","type":"custom","nav-type":"arrows","data":{"custom":"3"}},{"title":"Overlay","handle":"overlay-rgba","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(0,0,0,0.51)"}},{"title":"Bullet-Size","handle":"bullet-size","type":"custom","nav-type":"bullets","data":{"custom":"13"}},{"title":"Padding","handle":"padding","type":"custom","nav-type":"thumbs","data":{"custom":"20px 35px 20px 20px"}},{"title":"Title-Line-Height","handle":"title-line-height","type":"custom","nav-type":"tabs","data":{"custom":"15"}},{"title":"Desc-Line-Height","handle":"desc-line-height","type":"custom","nav-type":"tabs","data":{"custom":"25"}},{"title":"Title-Font-Weight","handle":"title-font-weight","type":"custom","nav-type":"tabs","data":{"custom":"700"}},{"title":"Desc-Font-Weight","handle":"desc-font-weight","type":"custom","nav-type":"tabs","data":{"custom":"400"}},{"title":"Line-Height","handle":"lineheight","type":"custom","nav-type":"thumbs","data":{"custom":"15"}}]}');
			
		$navigations[] = array(
			'id' => 5010,
			'default' => true,
			'name' => 'Zeus',
			'handle' => 'zeus',
			'markup' => '{"bullets":"<span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-bullet-imageoverlay\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-bullet-title\\\\\\">{{title}}<\\/span>","arrows":"<div class=\\\\\\"tp-title-wrap\\\\\\">\\n  \\t<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n <\\/div>\\n","thumbs":"<span class=\\\\\\"tp-thumb-over\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-title\\\\\\">{{title}}<\\/span>\\n<span class=\\\\\\"tp-thumb-more\\\\\\"><\\/span>","tabs":"<span class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/span>"}',
			'css' => '{"bullets":".zeus .tp-bullet {\\n     box-sizing:content-box; -webkit-box-sizing:content-box; border-radius:50%;\\n      background-color: rgba(0, 0, 0, 0);\\n      -webkit-transition: opacity 0.3s ease;\\n      transition: opacity 0.3s ease;\\n    width:##size##px;height:##size##px;\\n    border:2px solid rgb(##color##);\\n }\\n.zeus .tp-bullet:after {\\n  content: \\\\\\"\\\\\\";\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  left: 0;\\n  border-radius: 50%;\\n  background-color: rgb(##color##);\\n  -webkit-transform: scale(0);\\n  transform: scale(0);\\n  -webkit-transform-origin: 50% 50%;\\n  transform-origin: 50% 50%;\\n  -webkit-transition: -webkit-transform 0.3s ease;\\n  transition: transform 0.3s ease;\\n}\\n.zeus .tp-bullet:hover:after,\\n.zeus .tp-bullet.selected:after{\\n    -webkit-transform: scale(1.2);\\n  transform: scale(1.2);\\n}\\n  \\n .zeus .tp-bullet-image,\\n .zeus .tp-bullet-imageoverlay{\\n        width:##img-width##px;\\n        height:##img-height##px;\\n        position:absolute;\\n        background:#000;\\n        background:rgba(0,0,0,0.5);\\n        bottom:##size##px;\\n        margin-bottom:10px;\\n        transform:translateX(-50%);\\n       -webkit-transform:translateX(-50%);\\n        box-sizing:border-box;\\n        background-size:cover;\\n        background-position:center center;\\n        visibility:hidden;\\n        opacity:0;\\n         -webkit-backface-visibility: hidden; \\n        backface-visibility: hidden;\\n        -webkit-transform-origin: 50% 50%;\\n    transform-origin: 50% 50%;\\n      -webkit-transition: all 0.3s ease;\\n      transition: all 0.3s ease;\\n        border-radius:4px;\\n}\\n          \\n\\n.zeus .tp-bullet-title,\\n.zeus .tp-bullet-imageoverlay {\\n        z-index:2;\\n        -webkit-transition: all 0.5s ease;\\n      transition: all 0.5s ease;\\n        transform:translateX(-50%);\\n       -webkit-transform:translateX(-50%);\\n}     \\n.zeus .tp-bullet-title { \\n        color:rgb(##title-color##);\\n        text-align:center;\\n        line-height:##title-line-height##px;\\n        font-size:##title-font-size##px;\\n        font-weight:600;  \\n        z-index:3;\\n         visibility:hidden;\\n        opacity:0;\\n         -webkit-backface-visibility: hidden; \\n        backface-visibility: hidden;\\n        -webkit-transform-origin: 50% 50%;\\n    transform-origin: 50% 50%;\\n      -webkit-transition: all 0.3s ease;\\n      transition: all 0.3s ease;\\n        position:absolute;\\n        bottom:##tooltip-bottom##px;\\n        width:##img-width##px;\\n      vertical-align:middle;\\n       \\n}\\n      \\n.zeus .tp-bullet:hover .tp-bullet-title,\\n.zeus .tp-bullet:hover .tp-bullet-image,\\n.zeus .tp-bullet:hover .tp-bullet-imageoverlay{\\n      opacity:1;\\n      visibility:visible;\\n    -webkit-transform:translateY(0px) translateX(-50%);\\n      transform:translateY(0px) translateX(-50%);         \\n    }\\n\\n\\n\\n\\n\\/* VERTICAL RIGHT *\\/\\n\\n.zeus.nav-dir-vertical .tp-bullet-image,\\n.zeus.nav-dir-vertical .tp-bullet-imageoverlay{\\n  bottom:auto;\\n  margin-right:10px;\\n  margin-bottom:0px;\\n  right:##size##px;\\n  transform: translateX(0px) translateY(-50%);\\n  -webkit-transform:  translateX(0px) translateY(-50%);\\n  \\n}\\n\\n.zeus.nav-dir-vertical .tp-bullet:hover .tp-bullet-image {\\n  transform: translateX(0px) translateY(-50%);\\n  -webkit-transform: translateX(0px) translateY(-50%);\\n}\\n\\n\\n.zeus.nav-dir-vertical .tp-bullet-title,\\n.zeus.nav-dir-vertical .tp-bullet-imageoverlay {\\n        z-index:2;\\n        -webkit-transition: all 0.5s ease;\\n       transition: all 0.5s ease;\\n        transform: translateX(0px) translateY(-50%);\\n       -webkit-transform: translateX(0px) translateY(-50%);\\n}   \\n\\n\\n.zeus.nav-dir-vertical .tp-bullet-title {\\n     bottom:auto;\\n     right:100%;\\n     margin-right:10px;\\n}\\n\\n.zeus.nav-dir-vertical .tp-bullet:hover .tp-bullet-title,\\n.zeus.nav-dir-vertical .tp-bullet:hover .tp-bullet-image,\\n.zeus.nav-dir-vertical .tp-bullet:hover .tp-bullet-imageoverlay {\\n transform: translateX(0px) translateY(-50%);\\n  -webkit-transform: translateX(0px) translateY(-50%);\\n}\\n\\n\\n\\n\\/* VERTICAL LEFT *\\/\\n\\n.zeus.nav-dir-vertical.nav-pos-hor-left .tp-bullet-image,\\n.zeus.nav-dir-vertical.nav-pos-hor-left .tp-bullet-imageoverlay{\\n  bottom:auto;\\n  margin-left:10px;\\n  margin-bottom:0px;\\n  left:##size##px;\\n  transform:  translateX(0px) translateY(-50%);\\n  -webkit-transform:  translateX(0px) translateY(-50%);\\n  \\n}\\n\\n.zeus.nav-dir-vertical.nav-pos-hor-left .tp-bullet:hover .tp-bullet-image {\\n  transform: translateX(0px) translateY(-50%);\\n  -webkit-transform: translateX(0px) translateY(-50%);\\n}\\n\\n.zeus.nav-dir-vertical.nav-pos-hor-left .tp-bullet-title,\\n.zeus.nav-dir-vertical.nav-pos-hor-left .tp-bullet-imageoverlay {\\n        z-index:2;\\n        -webkit-transition: all 0.5s ease;\\n       transition: all 0.5s ease;\\n        transform:translateX(0px) translateY(-50%);\\n       -webkit-transform:translateX(0px) translateY(-50%);\\n}   \\n\\n\\n.zeus.nav-dir-vertical.nav-pos-hor-left .tp-bullet-title {\\n     bottom:auto;\\n     left:100%;\\n     margin-left:10px;\\n}\\n\\n\\/* HORIZONTAL TOP *\\/\\n\\n.zeus.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-image,\\n.zeus.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-imageoverlay{\\n  bottom:auto;\\n  top:##size##px;\\n  margin-top:10px;\\n  margin-bottom:0px;\\n  left:0px;\\n  transform:translateY(0px) translateX(-50%);\\n  -webkit-transform:translateX(0px) translateX(-50%);\\n  \\n}\\n\\n.zeus.nav-dir-horizontal.nav-pos-ver-top .tp-bullet:hover .tp-bullet-image {\\n  \\n  transform: scale(1) translateY(0px) translateX(-50%);\\n  -webkit-transform: scale(1) translateY(0px) translateX(-50%);\\n  \\n}\\n\\n.zeus.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-title,\\n.zeus.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-imageoverlay {\\n        z-index:2;\\n        -webkit-transition: all 0.5s ease;\\n       transition: all 0.5s ease;\\n        transform:translateY(0px) translateX(-50%);\\n       -webkit-transform:translateY(0px) translateX(-50%);\\n}   \\n\\n\\n.zeus.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-title {\\n     bottom:auto;\\n     top:##size##px;\\n     margin-top:20px;\\n}\\n","arrows":".zeus.tparrows {\\n  cursor:pointer;\\n  min-width:##bg-size##px;\\n  min-height:##bg-size##px;\\n  position:absolute;\\n  display:block;\\n  z-index:100;\\n  border-radius:50%;   \\n  overflow:hidden;\\n  background:rgba(##bg-color##);\\n}\\n\\n.zeus.tparrows:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:##arrow-size##px;\\n  color:rgb(##arrow-color##);\\n  display:block;\\n  line-height: ##bg-size##px;\\n  text-align: center;    \\n  z-index:2;\\n  position:relative;\\n}\\n.zeus.tparrows.tp-leftarrow:before {\\n  content: \\\\\\"\\\\\\\e824\\\\\\";\\n}\\n.zeus.tparrows.tp-rightarrow:before {\\n  content: \\\\\\"\\\\\\\e825\\\\\\";\\n}\\n\\n.zeus .tp-title-wrap {\\n  background:rgba(0,0,0,0.5);\\n  width:100%;\\n  height:100%;\\n  top:0px;\\n  left:0px;\\n  position:absolute;\\n  opacity:0;\\n  transform:scale(0);\\n  -webkit-transform:scale(0);\\n   transition: all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  -moz-transition:all 0.3s;\\n   border-radius:50%;\\n }\\n.zeus .tp-arr-imgholder {\\n  width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  background-position:center center;\\n  background-size:cover;\\n  border-radius:50%;\\n  transform:translatex(-100%);\\n  -webkit-transform:translatex(-100%);\\n   transition: all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  -moz-transition:all 0.3s;\\n\\n }\\n.zeus.tp-rightarrow .tp-arr-imgholder {\\n    transform:translatex(100%);\\n  -webkit-transform:translatex(100%);\\n      }\\n.zeus.tparrows:hover .tp-arr-imgholder {\\n  transform:translatex(0);\\n  -webkit-transform:translatex(0);\\n  opacity:1;\\n}\\n      \\n.zeus.tparrows:hover .tp-title-wrap {\\n  transform:scale(1);\\n  -webkit-transform:scale(1);\\n  opacity:1;\\n}\\n ","thumbs":".zeus .tp-thumb {\\nopacity:1\\n}\\n\\n.zeus .tp-thumb-over {\\n  background:rgba(##back-color##);\\n  width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  z-index:1;\\n  -webkit-transition:all 0.3s;\\n  transition:all 0.3s;\\n}\\n\\n.zeus .tp-thumb-more:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:##font-size##px;\\n  color:rgb(##title-color##);\\n  display:block;\\n  line-height: ##title-line-height##px;\\n  text-align: left;    \\n  z-index:2;\\n  position:absolute;\\n  top:20px;\\n  right:20px;\\n  z-index:2;\\n}\\n.zeus .tp-thumb-more:before {\\n  content: \\\\\\"\\\\\\\e825\\\\\\";\\n}\\n\\n.zeus .tp-thumb-title {\\n  font-family:\\\\\\"##font-family##\\\\\\";\\n  letter-spacing:1px;\\n  font-size: ##font-size##px;\\n  color:rgb(##title-color##);\\n  display:block;\\n  line-height: ##title-line-height##px;\\n  text-align: left;    \\n  z-index:2;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  z-index:2;\\n  padding:20px 35px 20px 20px;\\n  width:100%;\\n  height:100%;\\n  box-sizing:border-box;\\n  transition:all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  font-weight:500;\\n}\\n\\n.zeus .tp-thumb.selected .tp-thumb-more:before,\\n.zeus .tp-thumb:hover .tp-thumb-more:before {\\n color:rgb(##title-color##);\\n\\n}\\n\\n.zeus .tp-thumb.selected .tp-thumb-over,\\n.zeus .tp-thumb:hover .tp-thumb-over {\\n background:rgba(##back-hover##);\\n}\\n.zeus .tp-thumb.selected .tp-thumb-title,\\n.zeus .tp-thumb:hover .tp-thumb-title {\\n  color:rgb(##title-color##);\\n\\n}\\n","tabs":".zeus .tp-tab { \\n  opacity:1;      \\n  box-sizing:border-box;\\n}\\n\\n.zeus .tp-tab-title { \\ndisplay: block;\\ntext-align: center;\\nbackground: rgba(##bg-color##);\\nfont-family: \\\\\\"##title-font##\\\\\\", serif; \\nfont-weight: 700; \\nfont-size: ##font-size##px; \\nline-height: ##font-size##px;\\ncolor: rgb(##color##); \\npadding: ##padding##; }\\n\\n.zeus .tp-tab:hover .tp-tab-title,\\n.zeus .tp-tab.selected .tp-tab-title {\\n  color: rgb(##hover-color##);\\n  background:rgba(##back-hover##); \\n}"}',
			'settings' => '{"width":{"thumbs":"400","arrows":"160","bullets":"160","tabs":"160"},"height":{"thumbs":"130","arrows":"160","bullets":"160","tabs":"31"},"original":{"css":{"bullets":".zeus .tp-bullet {\\n     box-sizing:content-box; -webkit-box-sizing:content-box; border-radius:50%;\\n      background-color: rgba(0, 0, 0, 0);\\n      -webkit-transition: opacity 0.3s ease;\\n      transition: opacity 0.3s ease;\\n\\t  width:13px;height:13px;\\n\\t  border:2px solid #fff;\\n }\\n.zeus .tp-bullet:after {\\n  content: \\\\\\"\\\\\\";\\n  position: absolute;\\n  width: 100%;\\n  height: 100%;\\n  left: 0;\\n  border-radius: 50%;\\n  background-color: #FFF;\\n  -webkit-transform: scale(0);\\n  transform: scale(0);\\n  -webkit-transform-origin: 50% 50%;\\n  transform-origin: 50% 50%;\\n  -webkit-transition: -webkit-transform 0.3s ease;\\n  transition: transform 0.3s ease;\\n}\\n.zeus .tp-bullet:hover:after,\\n.zeus .tp-bullet.selected:after{\\n    -webkit-transform: scale(1.2);\\n  transform: scale(1.2);\\n}\\n  \\n .zeus .tp-bullet-image,\\n .zeus .tp-bullet-imageoverlay{\\n        width:135px;\\n        height:60px;\\n        position:absolute;\\n        background:#000;\\n        background:rgba(0,0,0,0.5);\\n        bottom:25px;\\n        left:50%;\\n        margin-left:-65px;\\n        box-sizing:border-box;\\n        background-size:cover;\\n        background-position:center center;\\n        visibility:hidden;\\n        opacity:0;\\n         -webkit-backface-visibility: hidden; \\n      \\tbackface-visibility: hidden;\\n        -webkit-transform-origin: 50% 50%;\\n\\t\\ttransform-origin: 50% 50%;\\n  \\t\\t-webkit-transition: all 0.3s ease;\\n  \\t\\ttransition: all 0.3s ease;\\n        border-radius:4px;\\n\\n}\\n          \\n\\n.zeus .tp-bullet-title,\\n.zeus .tp-bullet-imageoverlay {\\n        z-index:2;\\n        -webkit-transition: all 0.5s ease;\\n\\t  \\ttransition: all 0.5s ease;\\n}     \\n.zeus .tp-bullet-title { \\n        color:#fff;\\n        text-align:center;\\n        line-height:15px;\\n        font-size:13px;\\n        font-weight:600;  \\n        z-index:3;\\n         visibility:hidden;\\n        opacity:0;\\n         -webkit-backface-visibility: hidden; \\n      \\tbackface-visibility: hidden;\\n        -webkit-transform-origin: 50% 50%;\\n\\t\\ttransform-origin: 50% 50%;\\n  \\t\\t-webkit-transition: all 0.3s ease;\\n  \\t\\ttransition: all 0.3s ease;\\n        position:absolute;\\n        bottom:45px;\\n        width:135px;\\n    \\tvertical-align:middle;\\n        left:-57px;\\n}\\n      \\n.zeus .tp-bullet:hover .tp-bullet-title,\\n.zeus .tp-bullet:hover .tp-bullet-image,\\n.zeus .tp-bullet:hover .tp-bullet-imageoverlay{\\n      opacity:1;\\n      visibility:visible;\\n\\t  -webkit-transform:translateY(0px);\\n      transform:translateY(0px);         \\n    }","arrows":".zeus.tparrows {\\n  cursor:pointer;\\n  min-width:70px;\\n  min-height:70px;\\n  position:absolute;\\n  display:block;\\n  z-index:100;\\n  border-radius:35px;   \\n  overflow:hidden;\\n  background:rgba(0,0,0,0.10);\\n}\\n\\n.zeus.tparrows:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:20px;\\n  color:#fff;\\n  display:block;\\n  line-height: 70px;\\n  text-align: center;    \\n  z-index:2;\\n  position:relative;\\n}\\n.zeus.tparrows.tp-leftarrow:before {\\n  content: \\\\\\"\\\\\\\\e824\\\\\\";\\n}\\n.zeus.tparrows.tp-rightarrow:before {\\n  content: \\\\\\"\\\\\\\\e825\\\\\\";\\n}\\n\\n.zeus .tp-title-wrap {\\n  background:#000;\\n  background:rgba(0,0,0,0.5);\\n  width:100%;\\n  height:100%;\\n  top:0px;\\n  left:0px;\\n  position:absolute;\\n  opacity:0;\\n  transform:scale(0);\\n  -webkit-transform:scale(0);\\n   transition: all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  -moz-transition:all 0.3s;\\n   border-radius:50%;\\n }\\n.zeus .tp-arr-imgholder {\\n  width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  background-position:center center;\\n  background-size:cover;\\n  border-radius:50%;\\n  transform:translateX(-100%);\\n  -webkit-transform:translateX(-100%);\\n   transition: all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  -moz-transition:all 0.3s;\\n\\n }\\n.zeus.tp-rightarrow .tp-arr-imgholder {\\n    transform:translateX(100%);\\n  -webkit-transform:translateX(100%);\\n      }\\n.zeus.tparrows:hover .tp-arr-imgholder {\\n  transform:translateX(0);\\n  -webkit-transform:translateX(0);\\n  opacity:1;\\n}\\n      \\n.zeus.tparrows:hover .tp-title-wrap {\\n  transform:scale(1);\\n  -webkit-transform:scale(1);\\n  opacity:1;\\n}\\n ","thumbs":".zeus .tp-thumb {\\nopacity:1\\n}\\n\\n.zeus .tp-thumb-over {\\n  background:#000;\\n  background:rgba(0,0,0,0.25);\\n  width:100%;\\n  height:100%;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  z-index:1;\\n  -webkit-transition:all 0.3s;\\n  transition:all 0.3s;\\n}\\n\\n.zeus .tp-thumb-more:before {\\n  font-family: \\\\\\"revicons\\\\\\";\\n  font-size:12px;\\n  color:#aaa;\\n  color:rgba(255,255,255,0.75);\\n  display:block;\\n  line-height: 12px;\\n  text-align: left;    \\n  z-index:2;\\n  position:absolute;\\n  top:20px;\\n  right:20px;\\n  z-index:2;\\n}\\n.zeus .tp-thumb-more:before {\\n  content: \\\\\\"\\\\\\\\e825\\\\\\";\\n}\\n\\n.zeus .tp-thumb-title {\\n  font-family:\\\\\\"Raleway\\\\\\";\\n  letter-spacing:1px;\\n  font-size:12px;\\n  color:#fff;\\n  display:block;\\n  line-height: 15px;\\n  text-align: left;    \\n  z-index:2;\\n  position:absolute;\\n  top:0px;\\n  left:0px;\\n  z-index:2;\\n  padding:20px 35px 20px 20px;\\n  width:100%;\\n  height:100%;\\n  box-sizing:border-box;\\n  transition:all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  font-weight:500;\\n}\\n\\n.zeus .tp-thumb.selected .tp-thumb-more:before,\\n.zeus .tp-thumb:hover .tp-thumb-more:before {\\n color:#aaa;\\n\\n}\\n\\n.zeus .tp-thumb.selected .tp-thumb-over,\\n.zeus .tp-thumb:hover .tp-thumb-over {\\n background:#000;\\n}\\n.zeus .tp-thumb.selected .tp-thumb-title,\\n.zeus .tp-thumb:hover .tp-thumb-title {\\n  color:#fff;\\n\\n}\\n","tabs":".zeus .tp-tab { \\n  opacity:1;      \\n  box-sizing:border-box;\\n}\\n\\n.zeus .tp-tab-title { \\ndisplay: block;\\ntext-align: center;\\nbackground: rgba(0,0,0,0.25);\\nfont-family: \\\\\\"Roboto Slab\\\\\\", serif; \\nfont-weight: 700; \\nfont-size: 13px; \\nline-height: 13px;\\ncolor: #fff; \\npadding: 9px 10px; }\\n\\n.zeus .tp-tab:hover .tp-tab-title,\\n.zeus .tp-tab.selected .tp-tab-title {\\n color: #000;\\n  background:rgba(255,255,255,1); \\n}"},"markup":{"bullets":"<span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-bullet-imageoverlay\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-bullet-title\\\\\\">{{title}}<\\/span>","arrows":"<div class=\\\\\\"tp-title-wrap\\\\\\">\\n  \\t<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n <\\/div>\\n","thumbs":"<span class=\\\\\\"tp-thumb-over\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-image\\\\\\"><\\/span>\\n<span class=\\\\\\"tp-thumb-title\\\\\\">{{title}}<\\/span>\\n<span class=\\\\\\"tp-thumb-more\\\\\\"><\\/span>","tabs":"<span class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/span>"}},"placeholders":[{"title":"BG-Size","handle":"bg-size","type":"custom","nav-type":"arrows","data":{"custom":"70"}},"",{"title":"Background","handle":"bg-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(0,0,0,0.1)"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"arrows","data":{"custom":"20"}},{"title":"Bullet-Color","handle":"color","type":"color","nav-type":"bullets","data":{"color":"#ffffff"}},{"title":"Bullet-Size","handle":"size","type":"custom","nav-type":"bullets","data":{"custom":"13"}},{"title":"Image-Width","handle":"img-width","type":"custom","nav-type":"bullets","data":{"custom":"135"}},{"title":"Image-Height","handle":"img-height","type":"custom","nav-type":"bullets","data":{"custom":"60"}},{"title":"Tooltip-Title-Color","handle":"title-color","type":"color","nav-type":"bullets","data":{"color":"#ffffff"}},{"title":"Tooltip-Bottom","handle":"tooltip-bottom","type":"custom","nav-type":"bullets","data":{"custom":"45"}},{"title":"Overlay-Hover-RGBA","handle":"back-hover","type":"custom","nav-type":"thumbs","data":{"custom":"0,0,0,0.75"}},{"title":"Title-Color","handle":"title-color","type":"color","nav-type":"thumbs","data":{"color":"#ffffff"}},{"title":"Font-Size","handle":"font-size","type":"custom","nav-type":"thumbs","data":{"custom":"12"}},{"title":"BG-RGBA","handle":"bg-color","type":"custom","nav-type":"tabs","data":{"custom":"0,0,0,0.25"}},{"title":"Hover-Background","handle":"back-hover","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"#ffffff"}},{"title":"Title-Color","handle":"color","type":"color","nav-type":"tabs","data":{"color":"#ffffff"}},{"title":"Hover-Title-Color","handle":"hover-color","type":"color","nav-type":"tabs","data":{"color":"#000000"}},{"title":"Title-Size","handle":"font-size","type":"custom","nav-type":"tabs","data":{"custom":"13"}},{"title":"Font-Family","handle":"font-family","type":"font-family","nav-type":"thumbs","data":{"font_family":"Raleway"}},{"title":"Overlay","handle":"back-color","type":"color-rgba","nav-type":"thumbs","data":{"color-rgba":"rgba(0,0,0,0.25)"}},{"title":"Title-Font-Family","handle":"title-font","type":"font-family","nav-type":"tabs","data":{"font_family":"Roboto Slab"}},{"title":"Title-Font-Size","handle":"title-font-size","type":"custom","nav-type":"bullets","data":{"custom":"13"}},{"title":"Title-Line-Height","handle":"title-line-height","type":"custom","nav-type":"bullets","data":{"custom":"15"}},{"title":"Title-Line-Height","handle":"title-line-height","type":"custom","nav-type":"thumbs","data":{"custom":"14"}},{"title":"Padding","handle":"padding","type":"custom","nav-type":"tabs","data":{"custom":"9px 10px"}}]}');
			
		$navigations[] = array(
			'id' => 5011,
			'default' => true,
			'name' => 'Metis',
			'handle' => 'metis',
			'markup' => '{"bullets":"<span class=\\\\\\"tp-bullet-img-wrap\\\\\\">\\n  <span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>\\n<\\/span>\\n<span class=\\\\\\"tp-bullet-title\\\\\\">{{title}}<\\/span>","arrows":"","tabs":"<div class=\\\\\\"tp-tab-wrapper\\\\\\">\\n<div class=\\\\\\"tp-tab-number\\\\\\">{{param1}}<\\/div>\\n<div class=\\\\\\"tp-tab-divider\\\\\\"><\\/div>\\n<div class=\\\\\\"tp-tab-title-mask\\\\\\">\\n<div class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/div>\\n<\\/div>\\n<\\/div>"}',
			'css' => '{"bullets":".metis .tp-bullet { \\n    opacity:1;\\n    width:##size##px;\\n    height:##size##px;    \\n    padding:##border-width##px;\\n    background-color:rgba(##idlecolor##,0.25);\\n    margin:0px;\\n    box-sizing:border-box;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    border-radius:50%;\\n  }\\n\\n.metis .tp-bullet-image {\\n\\n   border-radius:50%;\\n   display:block;\\n   box-sizing:border-box;\\n   position:relative;\\n    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  width:100%;\\n  height:100%;\\n  background-size:cover;\\n  background-position:center center;\\n }  \\n.metis .tp-bullet-title { \\n     position:absolute; \\n     bottom:##size##px;\\n     margin-bottom:10px;\\n     display:inline-block;\\n     left:50%;\\n     background:#000;\\n     background:rgba(##idlecolor##,0.75);\\n     color:rgb(##tooltip-color##);\\n     padding:10px 30px;\\n     border-radius:4px;\\n   -webkit-border-radius:4px;\\n     opacity:0;\\n      transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    transform: translatez(0.001px) translatex(-50%) translatey(14px);\\n    transform-origin:50% 100%;\\n    -webkit-transform: translatez(0.001px) translatex(-50%) translatey(14px);\\n    -webkit-transform-origin:50% 100%;\\n    opacity:0;\\n    white-space:nowrap;\\n }\\n\\n.metis .tp-bullet:hover .tp-bullet-title {\\n     transform:rotatex(0deg) translatex(-50%);\\n    -webkit-transform:rotatex(0deg) translatex(-50%);\\n    opacity:1;\\n}\\n\\n.metis .tp-bullet.selected,\\n.metis .tp-bullet:hover  {\\nbackground: -moz-linear-gradient(top,  rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(##hovercolor##)), color-stop(100%, rgba(##hbgb##)));\\nbackground: -webkit-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -o-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -ms-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: linear-gradient(to bottom, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\n  }\\n.metis .tp-bullet-title:after {\\n    content:\\\\\\" \\\\\\";\\n    position:absolute;\\n    left:50%;\\n    margin-left:-8px;\\n    width: 0;\\n    height: 0;\\n    border-style: solid;\\n    border-width: 8px 8px 0 8px;\\n    border-color: rgba(##idlecolor##,0.75) transparent transparent transparent;\\n    bottom:-8px;\\n   }\\n\\n\\n\\n\\/* VERTICAL RIGHT *\\/\\n.metis.nav-dir-vertical.nav-pos-hor-right .tp-bullet-title { \\n   margin-bottom:0px; top:50%; right:##size##px; left:auto; bottom:auto; margin-right:10px;  transform: translateX(-10px) translateY(-50%);-webkit-transform: translateX(-10px) translateY(-50%); \\n}  \\n.metis.nav-dir-vertical.nav-pos-hor-right .tp-bullet-title:after { \\n  border-width: 10px 0 10px 10px;\\n  border-color:  transparent transparent transparent rgba(##idlecolor##,0.75) ;\\n  right:-10px;\\n  left:auto;  \\n  bottom:auto;\\n  top:10px;    \\n}\\n\\n\\n.metis.nav-dir-vertical.nav-pos-hor-right .tp-bullet:hover .tp-bullet-title{\\n   transform:translateY(-50%) translateX(0px);\\n  -webkit-transform:translateY(-50%) translateX(0px);\\n}\\n\\n\\/* VERTICAL LEFT && CENTER*\\/\\n.metis.nav-dir-vertical.nav-pos-hor-left .tp-bullet-title,\\n.metis.nav-dir-vertical.nav-pos-hor-center .tp-bullet-title { \\n   margin-bottom:0px; top:50%; left:##size##px; right:auto; bottom:auto; margin-left:10px;  transform: translateX(10px) translateY(-50%);-webkit-transform: translateX(10px) translateY(-50%); \\n}  \\n.metis.nav-dir-vertical.nav-pos-hor-left .tp-bullet-title:after,\\n.metis.nav-dir-vertical.nav-pos-hor-center .tp-bullet-title:after { \\n  border-width: 10px 10px 10px 0;\\n  border-color:  transparent rgba(##idlecolor##,0.75)  transparent transparent ;\\n  left:-2px;\\n  right:auto;  \\n  bottom:auto;\\n  top:10px;    \\n}\\n\\n\\n.metis.nav-dir-vertical.nav-pos-hor-left .tp-bullet:hover .tp-bullet-title,\\n.metis.nav-dir-vertical.nav-pos-hor-center .tp-bullet:hover .tp-bullet-title{\\n   transform:translateY(-50%) translateX(0px);\\n  -webkit-transform:translateY(-50%) translateX(0px);\\n}\\n\\n\\n\\/* HORIZONTAL TOP *\\/\\n.metis.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-title { \\n   margin-bottom:0px; top:##size##px; left:50%; bottom:auto; margin-top:10px; right:auto; transform: translateX(-50%) translateY(10px);-webkit-transform: translateX(-50%) translateY(10px); \\n}  \\n.metis.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-title:after { \\n  border-width: 0 10px 10px 10px;\\n  border-color:  transparent transparent rgba(##idlecolor##,0.75) transparent;\\n  right:auto;\\n  left:50%;\\n  margin-left:-10px;\\n  bottom:auto;\\n  top:-10px;\\n    \\n}\\n\\n\\n.metis.nav-dir-horizontal.nav-pos-ver-top .tp-bullet:hover .tp-bullet-title{\\n   transform:translateX(-50%) translatey(0px);\\n  -webkit-transform:translateX(-50%) translatey(0px);\\n}\\n\\n","arrows":".metis.tparrows {\\n  background:rgba(##bg-color##);\\n  padding:##padding##px;\\n  transition:all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  width:##size##px;\\n  height:##size##px;\\n  box-sizing:border-box;\\n }\\n \\n .metis.tparrows:hover {\\n   background:rgba(##bg-hover-color##);\\n }\\n \\n .metis.tparrows:before {\\n  color:rgb(##arrow-color##);  \\n   transition:all 0.3s;\\n  -webkit-transition:all 0.3s;\\n }\\n \\n .metis.tparrows:hover:before {\\n   transform:scale(1.5);\\n  }\\n ","tabs":".metis .tp-tab-number {\\n    color:rgb(##title-color##);\\n    font-size:##title-size##px;\\n    line-height:30px;\\n    font-weight:##title-weight##;\\n    font-family: \\\\\\"##font-family##\\\\\\";\\n    width: ##basicwidth##px;   \\n    display: inline-block;\\n\\tposition:absolute;\\n    text-align:center;\\n    box-sizing:border-box;\\n}\\n\\n\\n.metis .tp-tab-mask {\\n   left:0px;\\n   top:0px;\\n   max-width:##basicwidth## !important;   \\n   line-height:30px;\\n   transition:0.4s padding-left, 0.4s left, 0.4s max-width;\\n}\\n\\n.metis:hover .tp-tab-mask{\\n   left:15px;\\n   padding-left:0px;\\n   max-width:500px !important;\\n}\\n\\n.metis .tp-tab-divider { \\n\\tborder-right: 1px solid transparent;\\n    height: 30px;\\n    width: 1px;\\n    display: inline-block;\\n    position:absolute;\\n    left:##basicwidth##px;\\n    transition:0.4s all;\\n}\\n\\n.metis .tp-tab-title {\\n    color:rgb(##desc-color##);\\n    font-size:##desc-size##px;\\n    line-height:##desc-size##px;\\n    font-weight:##desc-font-weight##;\\n    font-family: \\\\\\"##font-family##\\\\\\";\\n    position:relative;\\n    line-height:30px;\\n    padding-left: 30px;\\n    display: inline-block;\\n    transform:translatex(-100%);\\n    transition:0.4s all;\\n}\\n\\n.metis .tp-tab-title-mask {\\n   position:absolute;\\n   overflow:hidden;\\n   left:##basicwidth##px; \\n}\\n\\n.metis:hover .tp-tab-title {\\n   transform:translatex(0);\\n }\\n\\n.metis .tp-tab { \\n\\topacity: 0.15;\\n    transition:0.4s all;\\n}\\n\\n.metis .tp-tab:hover,\\n.metis .tp-tab.selected {\\n    opacity: 1; \\n}\\n\\n.metis .tp-tab.selected .tp-tab-divider {\\n\\tborder-right: 1px solid #cdb083;\\n}\\n\\n.metis:hover .tp-tab-divider {\\n\\tmargin-left:15px;\\n}\\n\\n.metis.tp-tabs {\\n   max-width:##basicwidth##px !important;  \\n}\\n  \\n.metis.tp-tabs:before {\\n content:\\\\\\" \\\\\\";\\n height:100%;\\n width:##basicwidth##px; \\n border-right:1px solid rgba(255,255,255,0.10);\\n left:0px;\\n top:0px;\\n position:absolute;\\n transition:0.4s all;\\n background:rgba(##bgcolor##);\\n box-sizing:content-box !important;\\n padding:0px;\\n }\\n \\n .metis.tp-tabs:hover:before{\\n  width:##basicwidth##px;\\n  background:rgba(##bghovercolor##);\\n  padding:0px 15px;\\n }\\n     \\n @media (max-width:499px){\\n .metis.tp-tabs:before {\\n background:rgba(##handybg##);\\n }\\n }\\n "}',
			'settings' => '{"width":{"thumbs":"200","arrows":"160","bullets":"160","tabs":"300"},"height":{"thumbs":"130","arrows":"160","bullets":"160","tabs":"40"},"original":{"css":{"bullets":".metis .tp-bullet { \\n    opacity:1;\\n    width:50px;\\n    height:50px;    \\n    padding:3px;\\n    background:#000;\\n    background-color:rgba(0,0,0,0.25);\\n    margin:0px;\\n    box-sizing:border-box;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    border-radius:50%;\\n  }\\n\\n.metis .tp-bullet-image {\\n\\n   border-radius:50%;\\n   display:block;\\n   box-sizing:border-box;\\n   position:relative;\\n    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  width:44px;\\n  height:44px;\\n  background-size:cover;\\n  background-position:center center;\\n }  \\n.metis .tp-bullet-title { \\n     position:absolute; \\n\\t bottom:65px;\\n     display:inline-block;\\n     left:50%;\\n     background:#000;\\n     background:rgba(0,0,0,0.75);\\n     color:#fff;\\n     padding:10px 30px;\\n     border-radius:4px;\\n\\t -webkit-border-radius:4px;\\n     opacity:0;\\n      transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    transform: translateZ(0.001px) translateX(-50%) translateY(14px);\\n    transform-origin:50% 100%;\\n    -webkit-transform: translateZ(0.001px) translateX(-50%) translateY(14px);\\n    -webkit-transform-origin:50% 100%;\\n    opacity:0;\\n    white-space:nowrap;\\n }\\n\\n.metis .tp-bullet:hover .tp-bullet-title {\\n  \\t transform:rotateX(0deg) translateX(-50%);\\n    -webkit-transform:rotateX(0deg) translateX(-50%);\\n    opacity:1;\\n}\\n\\n.metis .tp-bullet.selected,\\n.metis .tp-bullet:hover  {\\n  \\n   background: rgba(255,255,255,1);\\n  background: -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(255,255,255,1)), color-stop(100%, rgba(119,119,119,1)));\\n  background: -webkit-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -o-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -ms-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\\\\\\"#ffffff\\\\\\", endColorstr=\\\\\\"#777777\\\\\\", GradientType=0 );\\n \\n      }\\n.metis .tp-bullet-title:after {\\n        content:\\\\\\" \\\\\\";\\n        position:absolute;\\n        left:50%;\\n        margin-left:-8px;\\n        width: 0;\\n\\t\\theight: 0;\\n\\t\\tborder-style: solid;\\n\\t\\tborder-width: 8px 8px 0 8px;\\n\\t\\tborder-color: rgba(0,0,0,0.75) transparent transparent transparent;\\n        bottom:-8px;\\n   }\\n","arrows":".metis.tparrows {\\n  background:#fff;\\n  padding:10px;\\n  transition:all 0.3s;\\n  -webkit-transition:all 0.3s;\\n  width:60px;\\n  height:60px;\\n  box-sizing:border-box;\\n }\\n \\n .metis.tparrows:hover {\\n   background:#fff;\\n   background:rgba(255,255,255,0.75);\\n }\\n \\n .metis.tparrows:before {\\n  color:#000;  \\n   transition:all 0.3s;\\n  -webkit-transition:all 0.3s;\\n }\\n \\n .metis.tparrows:hover:before {\\n   transform:scale(1.5);\\n  }\\n ","tabs":".metis .tp-tab-number {\\n    color:#fff;\\n    font-size:40px;\\n    line-height:30px;\\n    font-weight:400;\\n    font-family: \\\\\\"Playfair Display\\\\\\";\\n    width: 50px;\\n    margin-right: 17px;\\n    display: inline-block;\\n    float: left;\\n}\\n\\n\\n.metis .tp-tab-mask {\\n   padding-left:20px;\\n   left:0px;\\n   max-width:90px !important;\\n   transition:0.4s padding-left, 0.4s left, 0.4s max-width;\\n}\\n\\n.metis:hover .tp-tab-mask{\\n   padding-left:0px;\\n   left:50px;\\n   max-width:500px !important;\\n}\\n\\n.metis .tp-tab-divider { \\n\\tborder-right: 1px solid transparent;\\n    height: 30px;\\n    width: 1px;\\n    margin-top: 5px;\\n    display: inline-block;\\n    float: left;\\n}\\n\\n.metis .tp-tab-title {\\n    color:#fff;\\n    font-size:20px;\\n    line-height:20px;\\n    font-weight:400;\\n    font-family: \\\\\\"Playfair Display\\\\\\";\\n    position:relative;\\n    padding-top: 10px;\\n    padding-left: 30px;\\n    display: inline-block;\\n    transform:translateX(-100%);\\n    transition:0.4s all;\\n}\\n\\n.metis .tp-tab-title-mask {\\n   position:absolute;\\n   overflow:hidden;\\n   left:67px; \\n}\\n\\n.metis:hover .tp-tab-title {\\n   transform:translateX(0);\\n }\\n\\n.metis .tp-tab { \\n\\topacity: 0.15;\\n    transition:0.4s all;\\n}\\n\\n.metis .tp-tab:hover,\\n.metis .tp-tab.selected {\\n    opacity: 1; \\n}\\n\\n.metis .tp-tab.selected .tp-tab-divider {\\n\\tborder-right: 1px solid #cdb083;\\n}\\n\\n.metis.tp-tabs {\\n   max-width:118px !important;\\n   padding-left:50px;\\n}\\n  \\n.metis.tp-tabs:before {\\n content:\\\\\\" \\\\\\";\\n height:100%;\\n width:88px; \\n background:rgba(0,0,0,0.15);\\n border-right:1px solid rgba(255,255,255,0.10);\\n left:0px;\\n top:0px;\\n position:absolute;\\n transition:0.4s all;\\n }\\n \\n .metis.tp-tabs:hover:before{\\n  width:118px;\\n }\\n     \\n @media (max-width:499px){\\n .metis.tp-tabs:before {\\n background:rgba(0,0,0,0.75);\\n }\\n }"},"markup":{"bullets":"<span class=\\\\\\"tp-bullet-img-wrap\\\\\\">\\n  <span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>\\n<\\/span>\\n<span class=\\\\\\"tp-bullet-title\\\\\\">{{title}}<\\/span>","arrows":"","tabs":"<div class=\\\\\\"tp-tab-wrapper\\\\\\">\\n<div class=\\\\\\"tp-tab-number\\\\\\">{{param1}}<\\/div>\\n<div class=\\\\\\"tp-tab-divider\\\\\\"><\\/div>\\n<div class=\\\\\\"tp-tab-title-mask\\\\\\">\\n<div class=\\\\\\"tp-tab-title\\\\\\">{{title}}<\\/div>\\n<\\/div>\\n<\\/div>"}},"placeholders":[{"title":"Background","handle":"bg-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"#ffffff"}},{"title":"Size","handle":"size","type":"custom","nav-type":"arrows","data":{"custom":"60"}},{"title":"Padding","handle":"padding","type":"custom","nav-type":"arrows","data":{"custom":"10"}},{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"arrows","data":{"color":"#000000"}},{"title":"Hover-Background","handle":"bg-hover-color","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(255,255,255,0.75)"}},{"title":"Size","handle":"size","type":"custom","nav-type":"bullets","data":{"custom":"50"}},{"title":"Border-Width","handle":"border-width","type":"custom","nav-type":"bullets","data":{"custom":"3"}},"","","",{"title":"Font-Family","handle":"font-family","type":"font-family","nav-type":"tabs","data":{"font_family":"Playfair Display"}},{"title":"Title-Color","handle":"title-color","type":"color","nav-type":"tabs","data":{"color":"#ffffff"}},{"title":"Title-Font-Size","handle":"title-size","type":"custom","nav-type":"tabs","data":{"custom":"40"}},{"title":"Desc-Color","handle":"desc-color","type":"color","nav-type":"tabs","data":{"color":"#ffffff"}},{"title":"Desc-Font-Size","handle":"desc-size","type":"custom","nav-type":"tabs","data":{"custom":"20"}},"",{"title":"Tooltip-Color","handle":"tooltip-color","type":"color","nav-type":"bullets","data":{"color":"#ffffff"}},{"title":"Desc-Font-Weight","handle":"desc-font-weight","type":"custom","nav-type":"tabs","data":{"custom":"400"}},{"title":"Title-Weight","handle":"title-weight","type":"custom","nav-type":"tabs","data":{"custom":"400"}},{"title":"Idle-Color","handle":"idlecolor","type":"color","nav-type":"bullets","data":{"color":"#000000"}},{"title":"Hover-BG-Top","handle":"hovercolor","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#ffffff"}},{"title":"Basic-Width","handle":"basicwidth","type":"custom","nav-type":"tabs","data":{"custom":"80"}},{"title":"Background","handle":"bgcolor","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(0,0,0,0.15)"}},{"title":"Hover-Background","handle":"bghovercolor","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(0,0,0,0.25)"}},{"title":"Handy-Background","handle":"handybg","type":"color-rgba","nav-type":"tabs","data":{"color-rgba":"rgba(0,0,0,0.75)"}},"",{"title":"Hover-BG-Bottom","handle":"hbgb","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#777777"}}]}');
			
		$navigations[] = array(
			'id' => 5012,
			'default' => true,
			'name' => 'Dione',
			'handle' => 'dione',
			'markup' => '{"bullets":"<span class=\\\\\\"tp-bullet-img-wrap\\\\\\">\\n  <span class=\\\\\\"tp-bullet-image\\\\\\"><\\/span>\\n<\\/span>\\n<span class=\\\\\\"tp-bullet-title\\\\\\">{{title}}<\\/span>","arrows":"<div class=\\\\\\"tp-arr-imgwrapper\\\\\\">\\n<div class=\\\\\\"tp-arr-imgholder\\\\\\"><\\/div>\\n<\\/div>"}',
			'css' => '{"bullets":"\\n.dione .tp-bullet { \\n    opacity:1;\\n    width:##size##px;\\n    height:##size##px;    \\n    padding:##border-size##px;\\n    background-color:rgba(##idlecolor##,0.25);\\n    margin:0px;\\n    box-sizing:border-box;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n  }\\n\\n.dione .tp-bullet-image {\\n   display:block;\\n   box-sizing:border-box;\\n   position:relative;\\n    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  width:100%;\\n  height:100%;\\n  background-size:cover;\\n  background-position:center center;\\n }  \\n.dione .tp-bullet-title { \\n     position:absolute; \\n     bottom:##tooltip-offset##px;\\n     display:inline-block;\\n     left:50%;\\n     background:rgba(##idlecolor##,0.65);\\n     color:rgb(##tooltip-color##);\\n     padding:10px 30px;\\n     border-radius:4px;\\n   -webkit-border-radius:4px;\\n     opacity:0;\\n      transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    transform: translatez(0.001px) translatex(-50%) translatey(14px);\\n    transform-origin:50% 100%;\\n    -webkit-transform: translatez(0.001px) translatex(-50%) translatey(14px);\\n    -webkit-transform-origin:50% 100%;\\n    opacity:0;\\n    white-space:nowrap;\\n }\\n\\n.dione .tp-bullet:hover .tp-bullet-title {\\n     transform:rotatex(0deg) translatex(-50%);\\n    -webkit-transform:rotatex(0deg) translatex(-50%);\\n    opacity:1;\\n}\\n\\n.dione .tp-bullet.selected,\\n.dione .tp-bullet:hover  {\\n  background: -moz-linear-gradient(top,  rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(##hovercolor##)), color-stop(100%, rgba(##hbgb##)));\\nbackground: -webkit-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -o-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: -ms-linear-gradient(top, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\nbackground: linear-gradient(to bottom, rgba(##hovercolor##) 0%, rgba(##hbgb##) 100%);\\n}\\n.dione .tp-bullet-title:after {\\n        content:\\\\\\" \\\\\\";\\n        position:absolute;\\n        left:50%;\\n        margin-left:-8px;\\n        width: 0;\\n    height: 0;\\n    border-style: solid;\\n    border-width: 8px 8px 0 8px;\\n    border-color: rgba(##idlecolor##,0.65) transparent transparent transparent;\\n    bottom:-8px;\\n   }\\n\\n\\n\\/* VERTICAL RIGHT *\\/\\n.dione.nav-dir-vertical.nav-pos-hor-right .tp-bullet-title { \\n    top:50%; right:##size##px; left:auto; bottom:auto; margin-right:10px;  transform: translateX(-10px) translateY(-50%);-webkit-transform: translateX(-10px) translateY(-50%); \\n}  \\n.dione.nav-dir-vertical.nav-pos-hor-right .tp-bullet-title:after { \\n  border-width: 10px 0 10px 10px;\\n  border-color:  transparent transparent transparent rgba(##idlecolor##,0.65) ;\\n  right:-10px;\\n  left:auto;  \\n  bottom:auto;\\n  top:10px;    \\n}\\n\\n\\n.dione.nav-dir-vertical.nav-pos-hor-right .tp-bullet:hover .tp-bullet-title{\\n   transform:translateY(-50%) translateX(0px);\\n  -webkit-transform:translateY(-50%) translateX(0px);\\n}\\n\\n\\/* VERTICAL LEFT && CENTER*\\/\\n.dione.nav-dir-vertical.nav-pos-hor-left .tp-bullet-title,\\n.dione.nav-dir-vertical.nav-pos-hor-center .tp-bullet-title { \\n    top:50%; left:##size##px; right:auto; bottom:auto; margin-left:10px;  transform: translateX(10px) translateY(-50%);-webkit-transform: translateX(10px) translateY(-50%); \\n}  \\n.dione.nav-dir-vertical.nav-pos-hor-left .tp-bullet-title:after,\\n.dione.nav-dir-vertical.nav-pos-hor-center .tp-bullet-title:after { \\n  border-width: 10px 10px 10px 0;\\n  border-color:  transparent rgba(##idlecolor##,0.65)  transparent transparent ;\\n  left:-2px;\\n  right:auto;  \\n  bottom:auto;\\n  top:10px;    \\n}\\n\\n\\n.dione.nav-dir-vertical.nav-pos-hor-left .tp-bullet:hover .tp-bullet-title,\\n.dione.nav-dir-vertical.nav-pos-hor-center .tp-bullet:hover .tp-bullet-title{\\n   transform:translateY(-50%) translateX(0px);\\n  -webkit-transform:translateY(-50%) translateX(0px);\\n}\\n\\n\\n\\/* HORIZONTAL TOP *\\/\\n.dione.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-title { \\n    top:##size##px; left:50%; bottom:auto; margin-top:10px; right:auto; transform: translateX(-50%) translateY(10px);-webkit-transform: translateX(-50%) translateY(10px); \\n}  \\n.dione.nav-dir-horizontal.nav-pos-ver-top .tp-bullet-title:after { \\n  border-width: 0 10px 10px 10px;\\n  border-color:  transparent transparent rgba(##idlecolor##,0.65) transparent;\\n  right:auto;\\n  left:50%;\\n  margin-left:-10px;\\n  bottom:auto;\\n  top:-10px;\\n    \\n}\\n\\n\\n.dione.nav-dir-horizontal.nav-pos-ver-top .tp-bullet:hover .tp-bullet-title{\\n   transform:translateX(-50%) translatey(0px);\\n  -webkit-transform:translateX(-50%) translatey(0px);\\n}\\n\\n","arrows":".dione.tparrows {\\n  color:#000;\\n  height:100%;\\n  width:##width##px;\\n  background:transparent;\\n  background:rgba(##bg-color##,0);\\n  line-height:100%;\\n  transition:all 0.3s;\\n-webkit-transition:all 0.3s;\\n}\\n\\n.dione.tparrows:hover {\\n background:rgba(##bg-color##,0.45);\\n }\\n.dione .tp-arr-imgwrapper {\\n width:##width##px;\\n left:0px;\\n position:absolute;\\n height:100%;\\n top:0px;\\n overflow:hidden;\\n }\\n.dione.tp-rightarrow .tp-arr-imgwrapper {\\nleft:auto;\\nright:0px;\\n}\\n\\n.dione .tp-arr-imgholder {\\nbackground-position:center center;\\nbackground-size:cover;\\nwidth:##width##px;\\nheight:100%;\\ntop:0px;\\nvisibility:hidden;\\ntransform:translatex(-50px);\\n-webkit-transform:translatex(-50px);\\ntransition:all 0.3s;\\n-webkit-transition:all 0.3s;\\nopacity:0;\\nleft:0px;\\n}\\n\\n.dione.tparrows.tp-rightarrow .tp-arr-imgholder {\\n  right:0px;\\n  left:auto;\\n  transform:translatex(50px);\\n -webkit-transform:translatex(50px);\\n}\\n\\n.dione.tparrows:before {\\ncolor:##arrow-color##;\\nposition:absolute;\\nline-height:##arrow-size##px;\\nmargin-left:-22px;\\ntop:50%;\\nleft:50%;\\nfont-size:30px;\\nmargin-top:-15px;\\ntransition:all 0.3s;\\n-webkit-transition:all 0.3s;\\n}\\n\\n.dione.tparrows.tp-rightarrow:before {\\nmargin-left:6px;\\n}\\n\\n.dione.tparrows:hover:before {\\n  transform:translatex(-20px);\\n-webkit-transform:translatex(-20px);\\nopacity:0;\\n}\\n\\n.dione.tparrows.tp-rightarrow:hover:before {\\n  transform:translatex(20px);\\n-webkit-transform:translatex(20px);\\n}\\n\\n.dione.tparrows:hover .tp-arr-imgholder {\\n transform:translatex(0px);\\n-webkit-transform:translatex(0px);\\nopacity:1;\\nvisibility:visible;\\n}\\n\\n"}',
			'settings' => '{"width":{"thumbs":"200","arrows":"160","bullets":"160","tabs":"160"},"height":{"thumbs":"130","arrows":"160","bullets":"160","tabs":"31"},"original":{"css":{"bullets":".dione .tp-bullet { \\n    opacity:1;\\n    width:50px;\\n    height:50px;    \\n    padding:3px;\\n    background:#000;\\n    background-color:rgba(0,0,0,0.25);\\n    margin:0px;\\n    box-sizing:border-box;\\n    transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n\\n  }\\n\\n.dione .tp-bullet-image {\\n   display:block;\\n   box-sizing:border-box;\\n   position:relative;\\n    -webkit-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  -moz-box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  box-shadow: inset 5px 5px 10px 0px rgba(0,0,0,0.25);\\n  width:44px;\\n  height:44px;\\n  background-size:cover;\\n  background-position:center center;\\n }  \\n.dione .tp-bullet-title { \\n     position:absolute; \\n   bottom:65px;\\n     display:inline-block;\\n     left:50%;\\n     background:#000;\\n     background:rgba(0,0,0,0.75);\\n     color:#fff;\\n     padding:10px 30px;\\n     border-radius:4px;\\n   -webkit-border-radius:4px;\\n     opacity:0;\\n      transition:all 0.3s;\\n    -webkit-transition:all 0.3s;\\n    transform: translateZ(0.001px) translateX(-50%) translateY(14px);\\n    transform-origin:50% 100%;\\n    -webkit-transform: translateZ(0.001px) translateX(-50%) translateY(14px);\\n    -webkit-transform-origin:50% 100%;\\n    opacity:0;\\n    white-space:nowrap;\\n }\\n\\n.dione .tp-bullet:hover .tp-bullet-title {\\n     transform:rotateX(0deg) translateX(-50%);\\n    -webkit-transform:rotateX(0deg) translateX(-50%);\\n    opacity:1;\\n}\\n\\n.dione .tp-bullet.selected,\\n.dione .tp-bullet:hover  {\\n  \\n   background: rgba(255,255,255,1);\\n  background: -moz-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(255,255,255,1)), color-stop(100%, rgba(119,119,119,1)));\\n  background: -webkit-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -o-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: -ms-linear-gradient(top, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  background: linear-gradient(to bottom, rgba(255,255,255,1) 0%, rgba(119,119,119,1) 100%);\\n  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr=\\"#ffffff\\", endColorstr=\\"#777777\\", GradientType=0 );\\n \\n      }\\n.dione .tp-bullet-title:after {\\n        content:\\" \\";\\n        position:absolute;\\n        left:50%;\\n        margin-left:-8px;\\n        width: 0;\\n    height: 0;\\n    border-style: solid;\\n    border-width: 8px 8px 0 8px;\\n    border-color: rgba(0,0,0,0.75) transparent transparent transparent;\\n        bottom:-8px;\\n   }\\n","arrows":".dione.tparrows {\\n  height:100%;\\n  width:100px;\\n  background:transparent;\\n  background:rgba(0,0,0,0);\\n  line-height:100%;\\n  transition:all 0.3s;\\n-webkit-transition:all 0.3s;\\n}\\n\\n.dione.tparrows:hover {\\n background:rgba(0,0,0,0.45);\\n }\\n.dione .tp-arr-imgwrapper {\\n width:100px;\\n left:0px;\\n position:absolute;\\n height:100%;\\n top:0px;\\n overflow:hidden;\\n }\\n.dione.tp-rightarrow .tp-arr-imgwrapper {\\nleft:auto;\\nright:0px;\\n}\\n\\n.dione .tp-arr-imgholder {\\nbackground-position:center center;\\nbackground-size:cover;\\nwidth:100px;\\nheight:100%;\\ntop:0px;\\nvisibility:hidden;\\ntransform:translateX(-50px);\\n-webkit-transform:translateX(-50px);\\ntransition:all 0.3s;\\n-webkit-transition:all 0.3s;\\nopacity:0;\\nleft:0px;\\n}\\n\\n.dione.tparrows.tp-rightarrow .tp-arr-imgholder {\\n  right:0px;\\n  left:auto;\\n  transform:translateX(50px);\\n -webkit-transform:translateX(50px);\\n}\\n\\n.dione.tparrows:before {\\nposition:absolute;\\nline-height:30px;\\nmargin-left:-22px;\\ntop:50%;\\nleft:50%;\\nfont-size:30px;\\nmargin-top:-15px;\\ntransition:all 0.3s;\\n-webkit-transition:all 0.3s;\\n}\\n\\n.dione.tparrows.tp-rightarrow:before {\\nmargin-left:6px;\\n}\\n\\n.dione.tparrows:hover:before {\\n  transform:translateX(-20px);\\n-webkit-transform:translateX(-20px);\\nopacity:0;\\n}\\n\\n.dione.tparrows.tp-rightarrow:hover:before {\\n  transform:translateX(20px);\\n-webkit-transform:translateX(20px);\\n}\\n\\n.dione.tparrows:hover .tp-arr-imgholder {\\n transform:translateX(0px);\\n-webkit-transform:translateX(0px);\\nopacity:1;\\nvisibility:visible;\\n}\\n\\n"},"markup":{"bullets":"<span class=\\"tp-bullet-img-wrap\\">\\n  <span class=\\"tp-bullet-image\\"><\\/span>\\n<\\/span>\\n<span class=\\"tp-bullet-title\\">{{title}}<\\/span>","arrows":"<div class=\\"tp-arr-imgwrapper\\">\\n<div class=\\"tp-arr-imgholder\\"><\\/div>\\n<\\/div>"}},"placeholders":["","","",{"title":"Arrow-Color","handle":"arrow-color","type":"color","nav-type":"arrows","data":{"color":"#ffffff"}},"","","","","","","","","","","","","","","",{"title":"Arrow-Size","handle":"arrow-size","type":"custom","nav-type":"arrows","data":{"custom":"30"}},{"title":"BG-Color","handle":"bg-color","type":"color","nav-type":"arrows","data":{"color":"#000000"}},{"title":"Width","handle":"width","type":"custom","nav-type":"arrows","data":{"custom":"90"}},"","",{"title":"Size","handle":"size","type":"custom","nav-type":"bullets","data":{"custom":"50"}},{"title":"Border-Size","handle":"border-size","type":"custom","nav-type":"bullets","data":{"custom":"3"}},{"title":"Idle-Color","handle":"idlecolor","type":"color","nav-type":"bullets","data":{"color":"#000000"}},{"title":"Tooltip-Offset","handle":"tooltip-offset","type":"custom","nav-type":"bullets","data":{"custom":"65"}},{"title":"Tooltip-Color","handle":"tooltip-color","type":"color","nav-type":"bullets","data":{"color":"#ffffff"}},{"title":"Hover-Color-Top","handle":"hovercolor","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#ffffff"}},{"title":"Hover-Color-Bottom","handle":"hbgb","type":"color-rgba","nav-type":"bullets","data":{"color-rgba":"#777777"}}],"presets":[{"name":"tes","handle":"tes","type":"arrows","values":{"ph-dione-arrows-width-custom":"90"}}]}');
			
		$navigations[] = array(
			'id' => 5013,
			'default' => true,
			'name' => 'Uranus',
			'handle' => 'uranus',
			'markup' => '{"arrows":"","bullets":"<span class=\\\\\\"tp-bullet-inner\\\\\\"><\\/span>"}',
			'css' => '{"arrows":".uranus.tparrows {\\n  width:##width##px;\\n  height:##height##px;\\n  background:rgba(##background##);\\n }\\n .uranus.tparrows:before {\\n width:##width##px;\\n height:##height##px;\\n line-height:##height##px;\\n font-size:##font-size##px;\\n transition:all 0.3s;\\n-webkit-transition:all 0.3s;\\n }\\n \\n  .uranus.tparrows:hover:before {\\n    opacity:0.75;\\n  }","bullets":".uranus .tp-bullet{\\n  border-radius: 50%;\\n  box-shadow: 0 0 0 2px rgba(##color##, 0);\\n  -webkit-transition: box-shadow 0.3s ease;\\n  transition: box-shadow 0.3s ease;\\n  background:transparent;\\n  width:##size##px;\\n  height:##size##px;\\n}\\n.uranus .tp-bullet.selected,\\n.uranus .tp-bullet:hover {\\n  box-shadow: 0 0 0 2px rgba(##color##,1);\\n  border:none;\\n  border-radius: 50%;\\n  background:transparent;\\n}\\n\\n.uranus .tp-bullet-inner {\\n  -webkit-transition: background-color 0.3s ease, -webkit-transform 0.3s ease;\\n  transition: background-color 0.3s ease, transform 0.3s ease;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  outline: none;\\n  border-radius: 50%;\\n  background-color: rgb(##color##);\\n  background-color: rgba(##color##, 0.3);\\n  text-indent: -999em;\\n  cursor: pointer;\\n  position: absolute;\\n}\\n\\n.uranus .tp-bullet.selected .tp-bullet-inner,\\n.uranus .tp-bullet:hover .tp-bullet-inner{\\n transform: scale(0.4);\\n -webkit-transform: scale(0.4);\\n background-color:rgb(##color##);\\n}"}',
			'settings' => '{"width":{"thumbs":"200","arrows":"160","bullets":"160","tabs":"160"},"height":{"thumbs":"130","arrows":"160","bullets":"160","tabs":"31"},"original":{"css":{"arrows":".uranus.tparrows {\\n  width:50px;\\n  height:50px;\\n  background:transparent;\\n }\\n .uranus.tparrows:before {\\n width:50px;\\n height:50px;\\n line-height:50px;\\n font-size:40px;\\n transition:all 0.3s;\\n-webkit-transition:all 0.3s;\\n }\\n \\n  .uranus.tparrows:hover:before {\\n    opacity:0.75;\\n  }","bullets":".uranus .tp-bullet{\\n\\tborder-radius: 50%;\\n  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0);\\n  -webkit-transition: box-shadow 0.3s ease;\\n  transition: box-shadow 0.3s ease;\\n  background:transparent;\\n}\\n.uranus .tp-bullet.selected,\\n.uranus .tp-bullet:hover {\\n  box-shadow: 0 0 0 2px #FFF;\\n  border:none;\\n  border-radius: 50%;\\n\\n   background:transparent;\\n}\\n\\n\\n\\n.uranus .tp-bullet-inner {\\n  background-color: rgba(255, 255, 255, 0.7);\\n  -webkit-transition: background-color 0.3s ease, -webkit-transform 0.3s ease;\\n  transition: background-color 0.3s ease, transform 0.3s ease;\\n  top: 0;\\n  left: 0;\\n  width: 100%;\\n  height: 100%;\\n  outline: none;\\n  border-radius: 50%;\\n  background-color: #FFF;\\n  background-color: rgba(255, 255, 255, 0.3);\\n  text-indent: -999em;\\n  cursor: pointer;\\n  position: absolute;\\n}\\n\\n.uranus .tp-bullet.selected .tp-bullet-inner,\\n.uranus .tp-bullet:hover .tp-bullet-inner{\\n transform: scale(0.4);\\n -webkit-transform: scale(0.4);\\n background-color:#fff;\\n}"},"markup":{"arrows":"","bullets":"<span class=\\"tp-bullet-inner\\"><\\/span>"}},"placeholders":["",{"title":"Height","handle":"height","type":"custom","nav-type":"arrows","data":{"custom":"50"}},{"title":"Font-Size","handle":"font-size","type":"custom","nav-type":"arrows","data":{"custom":"40"}},{"title":"BG-Color","handle":"background","type":"color-rgba","nav-type":"arrows","data":{"color-rgba":"rgba(255,255,255,0)"}},{"title":"Size","handle":"size","type":"custom","nav-type":"bullets","data":{"custom":"15"}},{"title":"Color","handle":"color","type":"color","nav-type":"bullets","data":{"color":"#ffffff"}},{"title":"Width","handle":"width","type":"custom","nav-type":"arrows","data":{"custom":"50"}}],"presets":[{"name":"test","handle":"test","type":"arrows","values":{"ph-uranus-arrows-width-custom":"50","ph-uranus-arrows-background-color-rgba":"rgba(255,255,255,0)"}},{"name":"test2","handle":"test2","type":"arrows","values":{"ph-uranus-arrows-width-custom":"50"}}]}');
		
		return Mage::helper('nwdrevslider/framework')->apply_filters('revslider_mod_default_navigations', $navigations);
	}
	
	
	/**
	 * Translate Navigation for backwards compatibility
	 * @since: 5.0
	 **/
	public static function translate_navigation($handle){
		switch($handle){
			case 'round':
				$handle = 'hesperiden';
			break;
			case 'navbar':
				$handle = 'gyges';
			break;
			case 'preview1':
				$handle = 'hades';
			break;
			case 'preview2':
				$handle = 'ares';
			break;
			case 'preview3':
				$handle = 'hebe';
			break;
			case 'preview4':
				$handle = 'hermes';
			break;
			case 'custom':
				$handle = 'custom';
			break;
			case 'round-old':
				$handle = 'hephaistos';
			break;
			case 'square-old':
				$handle = 'persephone';
			break;
			case 'navbar-old':
				$handle = 'erinyen';
			break;
		}
		
		return $handle;
	}


    /**
     * Check if given Navigation is custom, if yes, export it
     * @since: 5.1.1
     **/
    public static function export_navigation($nav_handle){
        $navs = self::get_all_navigations(false, true);

        if(!is_array($nav_handle)) $nav_handle = array($nav_handle => true);


        $entries = array();
        if(!empty($nav_handle) && !empty($navs)){
            foreach($nav_handle as $handle => $u){
                foreach($navs as $n => $v){
                    if($v['handle'] == $handle){
                        $entries[$handle] = $navs[$n];
                        break;
                    }
                }
            }
            if(!empty($entries)) return $entries;
        }

        return false;
    }
    
    
    /**
     * Check the CSS for placeholders, replace them with correspinding values
     * @since: 5.2.0
     **/
    public function add_placeholder_modifications($css, $handle, $type, $settings, $slider, $output){
        $slider_preset = '';
        
        switch($type){
            case 'arrows':
                $slider_preset = 'navigation_arrows_preset';
            break;
            case 'bullets':
                $slider_preset = 'navigation_bullets_preset';
            break;
            case 'thumbs':
                $slider_preset = 'navigation_thumbs_preset';
            break;
            case 'tabs':
                $slider_preset = 'navigation_tabs_preset';
            break;
            default:
                return $css;
        }
        
        if(!is_array($settings)) $settings = json_decode($settings, true);
        //check if custom, default or an preset is choosen
        $preset = $slider->getParam($slider_preset, 'default');
        if(isset($settings['placeholders']) && is_array($settings['placeholders']) && !empty($settings['placeholders'])){
            
            switch($preset){
                case 'default': //we have no modifications, just wait after the switch where all undone ones are done
                break;
                case 'custom': //do all the modifications
                    foreach($settings['placeholders'] as $ph){
                        if(empty($ph)) continue;
                        if($ph['nav-type'] !== $type) continue;
                        
                        foreach($ph['data'] as $k => $d){
                            $def = $slider->getParam('ph-'.$handle.'-'.$type.'-'.$ph['handle'].'-'.$k.'-def', 'off');
                            $replace = ($def == 'off') ? $d : $slider->getParam('ph-'.$handle.'-'.$type.'-'.$ph['handle'].'-'.$k, $d);
                            
                            $replace = $this->check_css_convert($replace, $ph['type']);
                            
							$css = str_replace('##'.$ph['handle'].'##', $replace, $css);							
                        }
                    }
                break;
                default: //we have a selected preset, check for availability and do all of the checked in preset
                    if(isset($settings['presets']) && !empty($settings['presets'])){
                        foreach($settings['presets'] as $spreset){
                            if($preset !== $spreset['handle']) continue;
                            if($spreset['type'] !== $type) continue;
                            
                            foreach($settings['placeholders'] as $ph){
                                if(empty($ph)) continue;
                                if($ph['nav-type'] !== $type) continue;
                                
                                foreach($ph['data'] as $k => $d){
                                    if(isset($spreset['values']['ph-'.$handle.'-'.$type.'-'.$ph['handle'].'-'.$k])){
                                        $d = $this->check_css_convert($spreset['values']['ph-'.$handle.'-'.$type.'-'.$ph['handle'].'-'.$k], $ph['type']);
                                        
										$css = str_replace('##'.$ph['handle'].'##', $d, $css);										
                                    }
                                }
                            }
                            break;
                        }
                    }
                break;
            }
            
            
            foreach($settings['placeholders'] as $ph){ //for 'default' and default: //which is a preset (as a preset does not need to have all replaced, so we do the rest that is not selected
                if(empty($ph)) continue;
                if($ph['nav-type'] !== $type) continue;
                
                foreach($ph['data'] as $k => $d){
                    
                    $d = $this->check_css_convert($d, $ph['type']);
                    $css = str_replace('##'.$ph['handle'].'##', $d, $css);
                }
            }

            $css = str_replace('.'.$handle, '#'.$output->getSliderHtmlID().' .'.$handle, $css) . str_replace('.'.$handle, '#'.$output->getSliderHtmlID().'_wrapper .'.$handle, $css);
            
        }
        
        return $css;
    }
    
    
    /**
     * Check for example if the type is color or color-rgba and change hex and rgba values to fit our needs
     * @since: 5.2.0
     **/
    public function check_css_convert($css, $type){
        if($type == 'color' || $type == 'color-rgba'){
            //modify css
            $css = $this->convert_any_rgb_or_rgba($css, $type);
        }
        
        return $css;
    }
    
    /**
     * Parse css to array
	 **/
	public static function parse_css_to_array($css){
		
		while(strpos($css, '/*') !== false){
			if(strpos($css, '*/') === false) return false;
			$start = strpos($css, '/*');
			$end = strpos($css, '*/') + 2;
			$css = str_replace(substr($css, $start, $end - $start), '', $css);
		}
		
		//preg_match_all( '/(?ims)([a-z0-9\s\.\:#_\-@]+)\{([^\}]*)\}/', $css, $arr);
		preg_match_all( '/(?ims)([a-z0-9\,\s\.\:#_\-@]+)\{([^\}]*)\}/', $css, $arr);

		$result = array();
		foreach ($arr[0] as $i => $x){
			$selector = trim($arr[1][$i]);
			if(strpos($selector, '{') !== false || strpos($selector, '}') !== false) return false;
			$rules = explode(';', trim($arr[2][$i]));
			$result[$selector] = array();
			foreach ($rules as $strRule){
				if (!empty($strRule)){
					$rule = explode(":", $strRule);
					if(strpos($rule[0], '{') !== false || strpos($rule[0], '}') !== false || strpos($rule[1], '{') !== false || strpos($rule[1], '}') !== false) return false;
					
					//put back everything but not $rule[0];
					$key = trim($rule[0]);
					unset($rule[0]);
					$values = implode(':', $rule);
					
					$result[$selector][trim($key)] = trim(str_replace("'", '"', $values));
				}
			}
		}   
		return($result);
	}
	
	
	/**
	 * change rgb, rgba and hex to rgba like 120,130,50,0.5 (no () and rgb/rgba)
	 * @since: x.x.x
     **/
    public function convert_any_rgb_or_rgba($css, $type){
        if(strpos($css, 'rgb') !== false){
            //$css = explode(')', explode('(', $css)[1])[0];
            $css_1 = explode('(', $css);
            $css_2 = explode(')', $css_1[1]);
            $css = $css_2[0];

        }else{
            if($type === "color-rgba"){
                $css = RevSliderFunctions::hex2rgba($css, false, true);
            }else{
                $css = RevSliderFunctions::hex2rgba($css, false, true, true);
            }
        }

        if($type === "color-rgba" && count(explode(',', $css)) < 4) $css .= ',1';      

        return $css;
    }
    
    
    /**
     * Check the CSS for placeholders, replace them with correspinding values
	 * @since: x.x.x
     **/
    public function add_placeholder_sub_modifications($css, $handle, $type, $settings, $slide, $output){
        
        $c_css = '';
        
        if(!is_array($settings)) $settings = json_decode($settings, true);

        if(isset($settings['placeholders']) && is_array($settings['placeholders']) && !empty($settings['placeholders'])){
            
            //first check for media queries, generate more than one staple
            $marr = RevSliderCssParser::parse_media_blocks($css);
			
            if(!empty($marr)){//handle them separated
                foreach($marr as $media => $mr){
                    $css = str_replace($mr, '', $css);
                    
                    //clean @media query from $mr
                    $mr = RevSliderCssParser::clear_media_block($mr);
                    
                    //remove media query and bracket
                    $d = RevSliderCssParser::parseCssToArray($mr);
                    
                    $ret = $this->preset_return_array_css($d, $settings, $slide, $handle, $type, $output);
                    if(trim($ret) !== ''){
                        $c_css .= "\n".$media.' {'."\n";
                        $c_css .= $ret;
                        $c_css .= "\n".'}'."\n";
                    }
                }
            }
            
			$c = RevSliderCssParser::parseCssToArray($css);

            $c_css .= $this->preset_return_array_css($c, $settings, $slide, $handle, $type, $output);
        }

        return $c_css;
    }
    
    
    /**
     * Returns Array CSS modifications
     * @since: 5.2.0
     **/
    public function preset_return_array_css($c, $settings, $slide, $handle, $type, $output){
        $c_css = '';
        $array_css = array();
        
        if(!empty($c)){
            foreach($settings['placeholders'] as $ph){
                if(empty($ph)) continue;
                if($ph['nav-type'] !== $type) continue;
                
                foreach($ph['data'] as $k => $d){
                    $get_from = $slide->getParam('ph-'.$handle.'-'.$type.'-'.$ph['handle'].'-'.$k.'-slide', 'off');
                    if($get_from == 'on'){ //get from Slide
                        foreach($c as $class => $styles){
                            foreach($styles as $name => $val){
                                if(strpos($val, '##'.$ph['handle'].'##') !== false){
                                    $e = $this->check_css_convert($slide->getParam('ph-'.$handle.'-'.$type.'-'.$ph['handle'].'-'.$k), $ph['type']);
                                    $array_css[$class][$name] = str_replace('##'.$ph['handle'].'##', $e, $val);
                                }
                            }
                        }
                    }
                }
            }
            
            if(!empty($array_css)){
                foreach($array_css as $class => $styles){
                    if(!empty($styles)){
                        //class needs to get current slider and slide id
                        $slide_id = $slide->getID();
                        if($slide->getParam('slide_id','') !== '') $slide_id = Mage::helper('nwdrevslider/framework')->esc_attr($slide_id);
                        
                        $class = str_replace('.'.$handle, '#'.$output->getSliderHtmlID().'[data-slideactive="rs-'.$slide_id.'"] .'.$handle, $class);
                        
                        $c_css .= $class.'{'."\n";
                        foreach($styles as $style => $value){
                            $c_css .= $style.': '.$value.' !important;'."\n";
                        }
                        $c_css .= '}'."\n";
                    }
                }
            }
        }
        
        return $c_css;
    }
    
    
    /**
     * Add Navigation Preset to existing navigation
     * @since: 5.2.0
     **/
    public function add_preset($data){
        
        if(!isset($data['navigation'])) return false;
        
        $navs = self::get_all_navigations();
        
        foreach($navs as $nav){
            if($nav['handle'] == $data['navigation']){ //found the navigation, get ID and update settings
                
                //check if default, they cant have presets in the table
                if(isset($nav['default']) && $nav['default'] == true){
                    //check if we are a default preset, if yes return error
                    if(isset($nav['settings']['presets'])){
                        foreach($nav['settings']['presets'] as $prkey => $preset){
                            if($preset['handle'] == $data['handle']){
                                if(!isset($preset['editable'])){
                                    return __('Can\'t modify a default preset of default navigations');
                                }
                            }
                        }
                    }
                    
                    
                    //we want to add the preset somewhere
                    $overwrite = false;
                    $default_presets = Mage::helper('nwdrevslider/framework')->get_option('revslider-nav-preset-default', array());
                    if(!empty($default_presets) && isset($default_presets[$nav['id']])){
                        
                        foreach($default_presets[$nav['id']] as $prkey => $preset){
                            if($preset['handle'] == $data['handle']){
                                if($data['do_overwrite'] === false || $data['do_overwrite'] === 'false'){
                                    return __('Preset handle already exists, please choose a different name');
                                }
                                
                                $default_presets[$nav['id']][$prkey] = array(
                                    'name' => Mage::helper('nwdrevslider/framework')->esc_attr($data['name']),
                                    'handle' => Mage::helper('nwdrevslider/framework')->esc_attr($data['handle']),
                                    'type' => Mage::helper('nwdrevslider/framework')->esc_attr($data['type']),
                                    'values' => $data['values'],
                                    'editable' => true
                                );
                                
                                $overwrite = true;
                            }
                        }
                    }
                    
                    if($overwrite === false){
                        $default_presets[$nav['id']][] = array(
                            'name' => Mage::helper('nwdrevslider/framework')->esc_attr($data['name']),
                            'handle' => Mage::helper('nwdrevslider/framework')->esc_attr($data['handle']),
                            'type' => Mage::helper('nwdrevslider/framework')->esc_attr($data['type']),
                            'values' => $data['values'],
                            'editable' => true
                        );
                    }
                    
                    Mage::helper('nwdrevslider/framework')->update_option('revslider-nav-preset-default', $default_presets);

                }else{
                
                    $overwrite = false;
                    
                    if(isset($nav['settings']['presets'])){
                        foreach($nav['settings']['presets'] as $prkey => $preset){
                            if($preset['handle'] == $data['handle']){
                                if($data['do_overwrite'] === false || $data['do_overwrite'] === 'false'){
                                    return __('Preset handle already exists, please choose a different name');
                                }
                                
                                $nav['settings']['presets'][$prkey] = array(
                                    'name' => Mage::helper('nwdrevslider/framework')->esc_attr($data['name']),
                                    'handle' => Mage::helper('nwdrevslider/framework')->esc_attr($data['handle']),
                                    'type' => Mage::helper('nwdrevslider/framework')->esc_attr($data['type']),
                                    'values' => $data['values']
                                );
                                
                                $overwrite = true;
                            }
                        }
                    }else{
                        $nav['settings']['presets'] = array();
                    }
                    
                    if($overwrite === false){
                        $nav['settings']['presets'][] = array(
                            'name' => Mage::helper('nwdrevslider/framework')->esc_attr($data['name']),
                            'handle' => Mage::helper('nwdrevslider/framework')->esc_attr($data['handle']),
                            'type' => Mage::helper('nwdrevslider/framework')->esc_attr($data['type']),
                            'values' => $data['values']
                        );
                    }
                    
                    $wpdb = Mage::helper('nwdrevslider/query');
                    
                    //save this navigation
                    $response = $wpdb->update($wpdb->prefix.RevSliderGlobals::TABLE_NAVIGATION_NAME,
                        array(
                            'settings' => json_encode($nav['settings'])
                        ),
                        array('id' => $nav['id'])
                    );
                    
                    if($response == 0) $response = false;
                    
                }
                
                return true;
            }
        }
        
        return __('Navigation not found, could not add preset');
        
    }
    
    
    /**
     * Add Navigation Preset to existing navigation
     * @since: 5.2.0
     **/
    public function delete_preset($data){
        
        if(!isset($data['style_handle']) || !isset($data['handle']) || !isset($data['type'])) return false;
        
        $navs = self::get_all_navigations();
        
        foreach($navs as $nav){
            if($nav['handle'] == $data['style_handle']){ //found the navigation, get ID and update settings
                
                //check if default, they cant have presets
                if(isset($nav['default']) && $nav['default'] == true){
                    $default_presets = Mage::helper('nwdrevslider/framework')->get_option('revslider-nav-preset-default', array());
                    if(!empty($default_presets) && isset($default_presets[$nav['id']])){
                        
                        foreach($default_presets[$nav['id']] as $prkey => $preset){
                            if($preset['handle'] == $data['handle']){
                                unset($default_presets[$nav['id']][$prkey]);
                                
                                Mage::helper('nwdrevslider/framework')->update_option('revslider-nav-preset-default', $default_presets);
                                
                                return true;
                            }
                        }
                        return __('Can\'t delete default preset of default navigations');
                    }
                    return __('Preset not found in default navigations');
                }else{
                
                    if(isset($nav['settings']['presets'])){
                        foreach($nav['settings']['presets'] as $pkey =>$preset){
                            if($preset['handle'] == $data['handle']){
                                //delete
                                unset($nav['settings']['presets'][$pkey]);
                                
                                break;
                            }
                        }
                    }else{
                        return __('Preset not found');
                    }
                    
                    $wpdb = Mage::helper('nwdrevslider/query');
                    
                    //save this navigation
                    $response = $wpdb->update($wpdb->prefix.RevSliderGlobals::TABLE_NAVIGATION_NAME,
                        array(
                            'settings' => json_encode($nav['settings'])
                        ),
                        array('id' => $nav['id'])
                    );
                    
                    return $response;
                }
            }
        }
        
        return __('Navigation not found, could not delete preset');
        
    }
    
}