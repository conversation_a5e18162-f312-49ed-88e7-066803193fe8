<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @link      http://www.themepunch.com/
 * @copyright 2015 ThemePunch
 */
 
class RevSliderWidget extends WP_Widget {
	
    public function __construct(){
    	
        // widget actual processes
     	$widget_ops = array('classname' => 'widget_revslider', 'description' => __('Displays a revolution slider on the page') );
        parent::__construct('rev-slider-widget', __('Revolution Slider'), $widget_ops);
    }
 
    /**
     * 
     * the form
     */
    public function form($instance) {
		try {
            $slider = new RevSlider();
            $arrSliders = $slider->getArrSlidersShort();
        }catch(Exception $e){
			Mage::helper('nwdrevslider')->logException($e);
		}
          
		if(empty($arrSliders)){
			echo __("No sliders found, Please create a slider");
		}else{
			
			$field = "rev_slider";
			$fieldPages = "rev_slider_pages";
			$fieldCheck = "rev_slider_homepage";
			$fieldTitle = "rev_slider_title";
			
	    	$sliderID = RevSliderFunctions::getVal($instance, $field);
	    	$homepage = RevSliderFunctions::getVal($instance, $fieldCheck);
	    	$pagesValue = RevSliderFunctions::getVal($instance, $fieldPages);
	    	$title = RevSliderFunctions::getVal($instance, $fieldTitle);
	    	
			$fieldID = $this->get_field_id( $field );
			$fieldName = $this->get_field_name( $field );
			
			$select = RevSliderFunctions::getHTMLSelect($arrSliders,$sliderID,'name="'.$fieldName.'" id="'.$fieldID.'"',true);
			
			$fieldID_check = $this->get_field_id( $fieldCheck );
			$fieldName_check = $this->get_field_name( $fieldCheck );
			$checked = "";
			if($homepage == "on")
				$checked = "checked='checked'";

			$fieldPages_ID = $this->get_field_id( $fieldPages );
			$fieldPages_Name = $this->get_field_name( $fieldPages );
			
			$fieldTitle_ID = $this->get_field_id( $fieldTitle );
			$fieldTitle_Name = $this->get_field_name( $fieldTitle );
			
			?>
			<label for="<?php echo $fieldTitle_ID?>"><?php echo Mage::helper('nwdrevslider')->__( "Title")?>:</label>
			<input type="text" name="<?php echo $fieldTitle_Name?>" id="<?php echo $fieldTitle_ID?>" value="<?php echo $title?>" class="widefat">
			
			<br><br>
			
			<?php echo Mage::helper('nwdrevslider')->__( "Choose Slider")?>: <?php echo $select?>
			<div style="padding-top:10px;"></div>
			
			<label for="<?php echo $fieldID_check?>"><?php echo Mage::helper('nwdrevslider')->__( "Home Page Only")?>:</label>
			<input type="checkbox" name="<?php echo $fieldName_check?>" id="<?php echo $fieldID_check?>" <?php echo $checked?> >
			<br><br>
			<label for="<?php echo $fieldPages_ID?>"><?php echo Mage::helper('nwdrevslider')->__( "Pages: (example: 2,10)")?></label>
			<input type="text" name="<?php echo $fieldPages_Name?>" id="<?php echo $fieldPages_ID?>" value="<?php echo $pagesValue?>">
			
			<div style="padding-top:10px;"></div>
			<?php
		}	//else
    }
 
    /**
     * 
     * update
     */
    public function update($new_instance, $old_instance) {
    	
        return($new_instance);
    }

    
    /**
     * 
     * widget output
     */
    public function widget($args, $instance) {
    	
		$sliderID = RevSliderFunctions::getVal($instance, "rev_slider");
		$title = RevSliderFunctions::getVal($instance, "rev_slider_title");
		
		$homepageCheck = RevSliderFunctions::getVal($instance, "rev_slider_homepage");
		$homepage = "";
		if($homepageCheck == "on")
			$homepage = "homepage";
		
		$pages = RevSliderFunctions::getVal($instance, "rev_slider_pages");
		if(!empty($pages)){
			if(!empty($homepage))
				$homepage .= ",";
			$homepage .= $pages;
		}
				
		if(empty($sliderID))
			return(false);

        $slider = new RevSliderSlider();
        $slider->initByID($sliderID);
        $disable_on_mobile = $slider->getParam("disable_on_mobile","off");
        if($disable_on_mobile == 'on'){
            $mobile = (strstr($_SERVER['HTTP_USER_AGENT'],'Android') || strstr($_SERVER['HTTP_USER_AGENT'],'webOS') || strstr($_SERVER['HTTP_USER_AGENT'],'iPhone') ||strstr($_SERVER['HTTP_USER_AGENT'],'iPod') || strstr($_SERVER['HTTP_USER_AGENT'],'iPad') || strstr($_SERVER['HTTP_USER_AGENT'],'Windows Phone') || Mage::helper('nwdrevslider/framework')->wp_is_mobile()) ? true : false;
            if($mobile) return false;
        }

        //widget output
		$beforeWidget = RevSliderFunctions::getVal($args, "before_widget");
		$afterWidget = RevSliderFunctions::getVal($args, "after_widget");
		$beforeTitle = RevSliderFunctions::getVal($args, "before_title");
		$afterTitle = RevSliderFunctions::getVal($args, "after_title");
		
		echo $beforeWidget;
		
		if(!empty($title))
			echo $beforeTitle.$title.$afterTitle;
		
		RevSliderOutput::putSlider($sliderID,$homepage);

		Mage::helper('nwdrevslider/framework')->add_action('wp_head', array($this,'writeCSS'));
	    
		echo $afterWidget;						
    }

    public function writeCSS(){
    }
 
}

/**
 * old classname extends new one (old classnames will be obsolete soon)
 * @since: 5.0
 **/
class RevSlider_Widget extends RevSliderWidget {}