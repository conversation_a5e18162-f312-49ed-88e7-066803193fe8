<?xml version="1.0"?>
<!--
/**
 * Module configuration
 *
 * <AUTHOR> Web Development Ltd. <<EMAIL>>
 */
-->
<config>
    <modules>
        <Extensa_Econt>
            <version>1.5.0</version>
        </Extensa_Econt>
    </modules>
    <global>
        <models>
            <extensa_econt>
                <class>Extensa_Econt_Model</class>
                <resourceModel>econt_resource</resourceModel>
            </extensa_econt>
            <econt_resource>
                <class>Extensa_Econt_Model_Resource</class>
                <entities>
                    <city>
                        <table>extensa_econt_city</table>
                    </city>
                    <cityoffice>
                        <table>extensa_econt_city_office</table>
                    </cityoffice>
                    <country>
                        <table>extensa_econt_country</table>
                    </country>
                    <customer>
                        <table>extensa_econt_customer</table>
                    </customer>
                    <loading>
                        <table>extensa_econt_loading</table>
                    </loading>
                    <loadingtracking>
                        <table>extensa_econt_loading_tracking</table>
                    </loadingtracking>
                    <office>
                        <table>extensa_econt_office</table>
                    </office>
                    <order>
                        <table>extensa_econt_order</table>
                    </order>
                    <quarter>
                        <table>extensa_econt_quarter</table>
                    </quarter>
                    <region>
                        <table>extensa_econt_region</table>
                    </region>
                    <street>
                        <table>extensa_econt_street</table>
                    </street>
                    <zone>
                        <table>extensa_econt_zone</table>
                    </zone>
                </entities>
            </econt_resource>
        </models>
        <helpers>
            <extensa_econt>
                <class>Extensa_Econt_Helper</class>
            </extensa_econt>
        </helpers>
        <blocks>
            <extensa_econt>
                <class>Extensa_Econt_Block</class>
            </extensa_econt>
        </blocks>
        <resources>
            <extensa_econt_setup>
                <setup>
                    <module>Extensa_Econt</module>
                </setup>
            </extensa_econt_setup>
        </resources>
        <events>
            <sales_convert_order_to_quote>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>editOrderAdmin</method>
                    </extensa_econt>
                </observers>
            </sales_convert_order_to_quote>
            <core_block_abstract_to_html_after>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>injectEcontShippingMethodBlock</method>
                    </extensa_econt>
                </observers>
            </core_block_abstract_to_html_after>
        </events>
        <template>
            <email>
                <carriers_extensa_econt_email_template translate="label" module="extensa_econt">
                    <label>Econt Price Confirm</label>
                    <file>extensa/econt/econt_email_template.html</file>
                    <type>html</type>
                </carriers_extensa_econt_email_template>
            </email>
        </template>
    </global>
    <crontab>
        <jobs>
            <extensa_econt_update>
                <schedule><cron_expr>0 3 * * 0</cron_expr></schedule>
                <run><model>extensa_econt/observer::cronUpdateData</model></run>
            </extensa_econt_update>
            <extensa_econt_request_courier>
                <schedule><cron_expr>*/15 * * * 1-5</cron_expr></schedule>
                <run><model>extensa_econt/observer::requestCourier</model></run>
            </extensa_econt_request_courier>
        </jobs>
    </crontab>
    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <Extensa_Econt before="Mage_Adminhtml">Extensa_Econt_Adminhtml</Extensa_Econt>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <layout>
            <updates>
                <extensa_econt>
                    <file>extensa/econt.xml</file>
                </extensa_econt>
            </updates>
        </layout>
        <translate>
            <modules>
                <Extensa_Econt>
                    <files>
                        <default>Extensa_Econt.csv</default>
                    </files>
                </Extensa_Econt>
            </modules>
        </translate>
        <events>
            <core_block_abstract_to_html_before>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>beforeBlockToHtml</method>
                    </extensa_econt>
                </observers>
            </core_block_abstract_to_html_before>
            <sales_order_grid_collection_load_before>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>beforeCollectionLoad</method>
                    </extensa_econt>
                </observers>
            </sales_order_grid_collection_load_before>
            <core_block_abstract_prepare_layout_before>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>addEcontCourierAction</method>
                    </extensa_econt>
                </observers>
            </core_block_abstract_prepare_layout_before>
            <sales_order_place_after>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>newOrderAdmin</method>
                    </extensa_econt>
                </observers>
            </sales_order_place_after>
        </events>
    </adminhtml>
    <frontend>
        <routers>
            <extensa_econt>
                <use>standard</use>
                <args>
                    <module>Extensa_Econt</module>
                    <frontName>extensa_econt</frontName>
                </args>
            </extensa_econt>
        </routers>
        <events>
            <payment_method_is_active>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>restrictPaymentsCd</method>
                    </extensa_econt>
                </observers>
            </payment_method_is_active>
            <checkout_type_onepage_save_order_after>
                <observers>
                    <extensa_econt>
                        <class>extensa_econt/observer</class>
                        <method>saveOnepageOrder</method>
                    </extensa_econt>
                </observers>
            </checkout_type_onepage_save_order_after>
        </events>
        <layout>
            <updates>
                <extensa_econt>
                    <file>extensa/econt.xml</file>
                </extensa_econt>
            </updates>
        </layout>
        <translate>
            <modules>
                <Extensa_Econt>
                    <files>
                        <default>Extensa_Econt.csv</default>
                    </files>
                </Extensa_Econt>
            </modules>
        </translate>
    </frontend>
    <default>
        <carriers>
            <extensa_econt>
                <model>extensa_econt/shipping_carrier_econt</model>
                <title>Еконт Експрес</title>
                <shipping_from>DOOR</shipping_from>
                <cd>1</cd>
                <side>RECEIVER</side>
                <payment_method>CASH</payment_method>
                <currency>BGN</currency>
                <sallowspecific>1</sallowspecific>
                <specificcountry>BG</specificcountry>
                <econt_logged>0</econt_logged>
                <email_template>carriers_extensa_econt_email_template</email_template>
            </extensa_econt>
        </carriers>
        <payment>
            <extensa_econt>
                <model>extensa_econt/payment_method_econt</model>
                <title>Еконт Експрес наложен платеж</title>
                <allowspecific>1</allowspecific>
                <specificcountry>BG</specificcountry>
            </extensa_econt>
        </payment>
    </default>
</config>