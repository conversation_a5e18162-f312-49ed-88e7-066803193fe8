<?php
/**
 * Recaptcha
 *
 * @package Stenik_Captcha
 * <AUTHOR> Magento Team <<EMAIL>>
 * @see     github repo google/recaptcha
 */

/**
 * @method this setSecretKey(string)
 */
class Stenik_Captcha_Model_Recaptcha extends Mage_Core_Model_Abstract
{
    /**
     * Version of this client library.
     * @const string
     */
    const VERSION = 'php_1.1.2';

    /**
     * Retrieve request method
     *
     * @return Stenik_Captcha_Model_Recaptcha_RequestMethod_Interface
     */
    public function getRequestMethod()
    {
        if (!$this->hasData('request_method') ||
            !($this->getData('request_method') instanceof Stenik_Captcha_Model_Recaptcha_RequestMethod_Interface)
        ) {
            $this->setData('request_method', Mage::getModel('stenik_captcha/recaptcha_requestMethod_post'));
        }

        return $this->getData('request_method');
    }

    /**
     * Calls the reCAPTCHA siteverify API to verify whether the user passes
     * CAPTCHA test.
     *
     * @param string $response The value of 'g-recaptcha-response' in the submitted form.
     * @param string $remoteIp The end user's IP address.
     * @return Stenik_Captcha_Model_Recaptcha_Response Response from the service.
     */
    public function verify($response, $remoteIp = null)
    {
        // Discard empty solution submissions
        if (empty($response)) {
            $recaptchaResponse = Mage::getModel('stenik_captcha/recaptcha_response', array(false, array('missing-input-response')));
            return $recaptchaResponse;
        }

        if (!$this->getSecretKey()) {
            throw new RuntimeException('No secret provided.');
        }

        if (!is_string($this->getSecretKey())) {
            throw new \RuntimeException('The provided secret must be a string');
        }

        $params = Mage::getModel('stenik_captcha/recaptcha_requestParameters', array(
            'secret'    => $this->getSecretKey(),
            'response'  => $response,
            'remote_ip' => $remoteIp,
            'version'   => self::VERSION,
        ));

        $rawResponse = $this->getRequestMethod()->submit($params);
        return Stenik_Captcha_Model_Recaptcha_Response::fromJson($rawResponse);
    }
}