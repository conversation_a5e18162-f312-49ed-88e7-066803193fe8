<?php
/**
 * Product helper
 * 
 * @package Stenik_LeasingJetCredit
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_LeasingJetCredit_Helper_Product extends Mage_Core_Helper_Abstract
{
    /**
     * Attribute Good Type Id
     *
     * @var string
     */
    const ATTRIBUTE_CODE_GOOD_TYPE_ID = 'stenik_jetcredit_good_type_id';

    /**
     * Retrieve if is leasing available for product
     * 
     * @param  Varien_Object $product
     * @return boolean
     */
    public function isLeasingAvailable(Varien_Object $product)
    {
        return $product->getData(self::ATTRIBUTE_CODE_GOOD_TYPE_ID);
    }

    /**
     * Retrieve lease calculator url
     * 
     * @param  integer|Varien_Object $product
     * @param  integer|Varien_Object $child
     * @return boolean
     */
    public function getLeaseCalculatorUrl($product, $child = null)
    {
        if ($product instanceof Varien_Object)
            $product = $product->getId();

        if ($child instanceof Varien_Object)
            $child = $child->getId();

        return Mage::getUrl('stenik_leasingjetcredit/calculator/index', array('product' => $product, 'child' => $child));
    }
}