<?php
/**
 * @package Stenik_LeasingJetCredit
 * <AUTHOR> Magento Team <<EMAIL>>
 */
class Stenik_LeasingJetCredit_Model_Email extends Mage_Core_Model_Abstract {

    public function sendTransactional() {
        if(!($_order = $this->getOrder()) || !($this->getOrder() instanceof Mage_Sales_Model_Order)) {
            Mage::throwException('There has been an error processing your request.');
        }
        $storeId = $_order->getStoreId();

        $emailTemplate = Mage::getModel('core/email_template')->loadByCode(Stenik_LeasingJetCredit_Helper_Data::TRANSACTIONAL_EMAIL_CODE);
        $templateId = $emailTemplate->getId();

        $sender = array(
            'name'  => Mage::getStoreConfig('trans_email/ident_general/name', $storeId),
            'email' => Mage::getStoreConfig('trans_email/ident_general/email', $storeId)
        );

        $recepientEmail = Mage::getStoreConfig('stenik_leasingjetcredit/config/application_email', $storeId);
        $recepientName  = '';

        $productSkus  = array();
        foreach ($_order->getAllVisibleItems() as $_item) {
            $productSkus[] = $_item->getSku();
        }
        $itemsHtml = Mage::app()->getLayout()->createBlock('stenik_leasingjetcredit/product_mail')
            ->setItems($_order->getAllVisibleItems())
            ->toHtml();


        $loanInformation    = new Varien_Object;
        $paymentAdditionalInformation = $_order->getPayment()->getAdditionalInformation();
        $loanInformation->setData(isset($paymentAdditionalInformation['loan']) ? $paymentAdditionalInformation['loan'] : array());

        $paymentInformation = new Varien_Object;
        $paymentInformation->setData($_order->getPayment()->getAdditionalInformation());
        $paymentInformation->setData('loan', $loanInformation);

        $firstName = '';
        $lastName = '';
        if ($paymentInformation->getData('name')) {
            $namesFromPayment = explode(' ', $paymentInformation->getData('name'));
            if (count($namesFromPayment) > 1) {
                $firstName = array_shift($namesFromPayment);
                $lastName = end($namesFromPayment);
            }
        }

        if (!$firstName || !$lastName) {
            $firstName = $_order->getCustomerFirstname();
            $lastName = $_order->getCustomerLastname();
        }

        // Extract customer data from payment information
        $customerEGN = '';
        $customerPhone = '';
        $downPayment = '0.00';
        $paymentPeriod = '';
        $monthlyInstallment = '';
        $creditAmount = '';

        if (is_array($paymentInformation)) {
            // Extract EGN
            if (isset($paymentInformation['pin'])) {
                $customerEGN = $paymentInformation['pin'];
            } elseif (isset($paymentInformation['egn'])) {
                $customerEGN = $paymentInformation['egn'];
            }

            // Extract phone
            if (isset($paymentInformation['phone'])) {
                $customerPhone = $paymentInformation['phone'];
            }

            // Extract down payment
            if (isset($paymentInformation['downpayment'])) {
                $downPayment = number_format((float)$paymentInformation['downpayment'], 2);
            }

            // Extract loan information
            if (isset($paymentInformation['loan'])) {
                $loanInfo = $paymentInformation['loan'];
                if (isset($loanInfo['maturity'])) {
                    $paymentPeriod = $loanInfo['maturity'];
                }
                if (isset($loanInfo['installment_amount'])) {
                    $monthlyInstallment = number_format((float)$loanInfo['installment_amount'], 2);
                }
            }

            // Extract selected variant information
            if (isset($paymentInformation['selected_variant'])) {
                $selectedVariant = $paymentInformation['selected_variant'];
                if (isset($selectedVariant['maturity'])) {
                    $paymentPeriod = $selectedVariant['maturity'];
                }
                if (isset($selectedVariant['installment'])) {
                    $monthlyInstallment = number_format((float)$selectedVariant['installment'], 2);
                }
            }
        }

        // Calculate credit amount (total minus down payment)
        $totalAmount = $_order->getBaseSubtotalInclTax();
        $creditAmount = number_format($totalAmount - (float)str_replace(',', '', $downPayment), 2);

        // Build detailed order items list
        $orderItemsDetailed = $this->_buildDetailedOrderItems($_order);

        // Get merchant code from configuration
        $merchantCode = Mage::getStoreConfig('stenik_leasingjetcredit/config/merchant_code');
        if (!$merchantCode) {
            $merchantCode = '433147'; // Default fallback
        }

         // Set variables that can be used in email template
        $vars = array(
            'subtotal' => number_format($_order->getBaseSubtotalInclTax(),2),
            'items'    => implode(', ', $productSkus),
            'payment'  => $paymentInformation,
            'order'    => $_order,
            'order_items' => $itemsHtml,
            'customer_first_name' => $firstName,
            'customer_last_name' => $lastName,
            'customer_egn' => $customerEGN,
            'customer_phone' => $customerPhone,
            'down_payment' => $downPayment,
            'payment_period' => $paymentPeriod,
            'monthly_installment' => $monthlyInstallment,
            'credit_amount' => $creditAmount,
            'order_items_detailed' => $orderItemsDetailed,
            'merchant_code' => $merchantCode,
        );

        $translate  = Mage::getSingleton('core/translate');

        // Send Transactional Email

        Mage::getModel('core/email_template')
            ->sendTransactional($templateId, $sender, $recepientEmail, $recepientName, $vars, $storeId);

        if(!($sendCopyTo = Mage::getStoreConfig('stenik_leasingjetcredit/config/send_copy_to'))) {
            $sendCopyTo = array_filter(explode(',', $sendCopyTo));
            if(!empty($sendCopyTo)) {
                foreach ($sendCopyTo as $email) {
                    // Send Transactional Email
                    Mage::getModel('core/email_template')
                        ->sendTransactional($templateId, $sender, trim($email), $sender['name'], $vars, $storeId);
                }
            }
        }

        $translate->setTranslateInline(true);
    }

    /**
     * Build detailed order items list according to BNP format
     *
     * @param Mage_Sales_Model_Order $order
     * @return string
     */
    protected function _buildDetailedOrderItems($order)
    {
        $itemsHtml = '';

        foreach ($order->getAllVisibleItems() as $item) {
            $product = $item->getProduct();

            // Get product type/category
            $productType = 'Продукт'; // Default
            if ($product) {
                // Try to get category name
                $categoryIds = $product->getCategoryIds();
                if (!empty($categoryIds)) {
                    $category = Mage::getModel('catalog/category')->load($categoryIds[0]);
                    if ($category && $category->getId()) {
                        $productType = $category->getName();
                    }
                }
            }

            // Get brand/manufacturer
            $brand = '-';
            if ($product) {
                $manufacturer = $product->getAttributeText('manufacturer');
                if ($manufacturer) {
                    $brand = $manufacturer;
                } elseif ($product->getManufacturer()) {
                    $brand = $product->getManufacturer();
                }
            }

            // Format item details
            $unitPrice = number_format($item->getPriceInclTax(), 2);
            $qty = (int)$item->getQtyOrdered();
            $totalPrice = number_format($item->getRowTotalInclTax(), 2);

            $itemsHtml .= sprintf(
                "Тип стока: %s;\n" .
                "Марка: %s;\n" .
                "Единична цена в лева с ДДС: %s;\n" .
                "Брой стоки: %d;\n" .
                "Обща сума в лева с ДДС: %s;\n\n",
                $productType,
                $brand,
                $unitPrice,
                $qty,
                $totalPrice
            );
        }

        // Add shipping if present
        $shippingAmount = $order->getShippingInclTax();
        if ($shippingAmount > 0) {
            $itemsHtml .= sprintf(
                "Тип стока: доставка;\n" .
                "Марка: -;\n" .
                "Единична цена в лева с ДДС: %s;\n" .
                "Брой стоки: 1;\n" .
                "Обща сума в лева с ДДС: %s;\n\n",
                number_format($shippingAmount, 2),
                number_format($shippingAmount, 2)
            );
        } else {
            // Add free shipping entry
            $itemsHtml .= sprintf(
                "Тип стока: доставка;\n" .
                "Марка: -;\n" .
                "Единична цена в лева с ДДС: 0.00;\n" .
                "Брой стоки: 1;\n" .
                "Обща сума в лева с ДДС: 0.00;\n\n"
            );
        }

        return $itemsHtml;
    }
}