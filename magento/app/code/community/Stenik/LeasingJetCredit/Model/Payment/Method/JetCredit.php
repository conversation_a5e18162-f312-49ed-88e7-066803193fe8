<?php
/**
 * Payment method JetCredit
 * 
 * @package Stenik_LeasingJetCredit
 * <AUTHOR> Magento Team <<EMAIL>>
 */

class Stenik_LeasingJetCredit_Model_Payment_Method_JetCredit extends Mage_Payment_Model_Method_Abstract
{
    const CODE = 'stenik_leasingjetcredit';

    /**
     * Method Code
     * 
     * @var string
     */
    protected $_code = self::CODE;

    /**
     * Form Block
     * 
     * @var string
     */
    protected $_formBlockType = 'stenik_leasingjetcredit/form';

    /**
     * Info Block
     * 
     * @var string
     */
    protected $_infoBlockType = 'stenik_leasingjetcredit/info';

    /**
     * Available variants
     * 
     * @var array|null
     */
    protected $_availableVariants = null;

    /**
     * Check whether payment method can be used
     *
     * @param Mage_Sales_Model_Quote|null $quote
     * @return bool
     */
    public function isAvailable($quote = null)
    {
        if (!parent::isAvailable($quote)) {
            return false;
        }

        try {
            if ($quote === null) {
                $quote = $this->getInfoInstance()->getQuote();
            }

            if ($quote) {
                $total = $quote->getSubtotal();
                if (!Mage::helper('stenik_leasingjetcredit')->isPriceInRange($total)) {
                    return false;
                }
            }
        } catch (Exception $e) {
            return false;
        }

        return true;
    }

    /**
     * Assign data to info model instance
     *
     * @param   mixed $data
     * @return  self
     */
    public function assignData($data)
    {
        if (!($data instanceof Varien_Object)) {
            $data = new Varien_Object($data);
        }

        if ($data->getData(self::CODE)) {
            $data = new Varien_Object($data->getData(self::CODE));
        }

        $info = $this->getInfoInstance();

        /** @var Stenik_LeasingJetCredit_Helper_Service $_helper */
        $_helper = Mage::helper('stenik_leasingjetcredit/service');
        if ($_helper->getHideDownPayment()) {
            $downpayment = 0;
        } else {
            $downpayment = (float) $data->getDownpayment();
        }

        $info->setAdditionalInformation('variant_id',  $data->getVariantId());
        $info->setAdditionalInformation('downpayment', $downpayment);
        $info->setAdditionalInformation('names',       $data->getNames());
        $info->setAdditionalInformation('pin',         $data->getPin());
        $info->setAdditionalInformation('email',       $data->getEmail());
        $info->setAdditionalInformation('phone',       $data->getPhone());

        if ($data->getVariantId()) {
            $info->setAdditionalInformation('loan', null);

            $quote       = $info->getQuote();
            $goodTypeIds = Mage::helper('stenik_leasingjetcredit')->getQuoteGoodTypeIds($quote);

            $loan = Mage::helper('stenik_leasingjetcredit/service')->calculateLoan(
                $goodTypeIds, 
                Mage::helper('stenik_leasingjetcredit')->getQuotePrincipal($quote),
                $downpayment,
                $data->getVariantId()
            );

            $info->setAdditionalInformation('loan', $loan->getData());
        } else {
            Mage::throwException(Mage::helper('stenik_leasingjetcredit')->__('Please choose leasing variant.'));
        }
        return $this;
    }

    /**
     * Retrieve available variants
     *
     * @param Mage_Sales_Model_Quote $quote
     * @return array
     */
    public function getAvailableVariants($quote = null, $downpayment = 0.00)
    {
        if ($quote === null)
            $quote = $this->getInfoInstance()->getQuote();

        /** @var Stenik_LeasingJetCredit_Helper_Service $_helper */
        $_helper = Mage::helper('stenik_leasingjetcredit/service');
        if ($_helper->getHideDownPayment()) {
            $downpayment = 0.00;
        }

        if ($this->_availableVariants === null) {
            // First try to get variants from Go API stored data (admin panel context)
            $storedVariants = $this->_getVariantsFromStoredData($quote);
            if (!empty($storedVariants)) {
                Mage::log('BNP: Using variants from Go API stored data (' . count($storedVariants) . ' variants)', null, 'stenik_leasingjetcredit_admin.log');
                $this->_availableVariants = $storedVariants;
            } else {
                // Fallback to original Magento API call
                Mage::log('BNP: No stored variants found, falling back to Magento API call', null, 'stenik_leasingjetcredit_admin.log');
                $this->_availableVariants = Mage::helper('stenik_leasingjetcredit')->getQuoteAvailableVariants($quote, $downpayment);
            }
        }

        return $this->_availableVariants;
    }

    /**
     * Get variants from Go API stored data in payment additional information
     *
     * @param Mage_Sales_Model_Quote $quote
     * @return array
     */
    protected function _getVariantsFromStoredData($quote)
    {
        $variants = array();

        try {
            // Get payment additional information
            $payment = $quote->getPayment();
            if (!$payment || !$payment->getAdditionalInformation()) {
                return $variants;
            }

            $additionalInfo = $payment->getAdditionalInformation();
            if (empty($additionalInfo)) {
                return $variants;
            }

            // Look for numeric keys (0, 1, 2, etc.) that contain variant data
            $foundVariants = false;
            for ($i = 0; $i < 20; $i++) { // Check up to 20 variants
                $key = (string)$i;
                if (isset($additionalInfo[$key]) && is_array($additionalInfo[$key])) {
                    $variantData = $additionalInfo[$key];

                    // Validate that this looks like variant data
                    if (isset($variantData['id']) && isset($variantData['installment']) && isset($variantData['total_repayment'])) {
                        // Convert to Varien_Object format that Magento expects
                        $variants[] = new Varien_Object($variantData);
                        $foundVariants = true;
                    }
                }
            }

            if ($foundVariants) {
                Mage::log('BNP: Successfully extracted ' . count($variants) . ' variants from stored payment data', null, 'stenik_leasingjetcredit_admin.log');

                // Log first few variants for debugging
                foreach (array_slice($variants, 0, 3) as $i => $variant) {
                    Mage::log(sprintf('BNP: Variant %d: ID=%s, Installment=%s, Total=%s, Maturity=%s',
                        $i, $variant->getId(), $variant->getInstallment(), $variant->getTotalRepayment(), $variant->getMaturity()),
                        null, 'stenik_leasingjetcredit_admin.log');
                }
            }

        } catch (Exception $e) {
            Mage::log('BNP: Error extracting variants from stored data: ' . $e->getMessage(), null, 'stenik_leasingjetcredit_admin.log');
        }

        return $variants;
    }
}