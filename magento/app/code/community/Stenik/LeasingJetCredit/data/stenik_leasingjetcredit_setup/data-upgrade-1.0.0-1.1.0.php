<?php
$code         = Stenik_LeasingJetCredit_Helper_Data::TRANSACTIONAL_EMAIL_CODE;
$subject      = "Заявка за кредит към БНП Парибас - Поръчка {{var order.increment_id}}";
$templateText = '
{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td>
            <table cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td class="store-info">
                        <h3>Данни за потребителя:</h3>
                        <p>Собствено име: {{var customer_first_name}};</p>
                        <p>Фамилия: {{var customer_last_name}};</p>
                        <p>ЕГН: {{var customer_egn}};</p>
                        <p>Телефон за връзка: {{var customer_phone}};</p>
                        <p>Имейл адрес: {{var order.customer_email}};</p>

                        <h3>Данни за стоката:</h3>
                        {{var order_items_detailed}}

                        <h3>Данни за кредита:</h3>
                        <p>Размер на кредита в лева: {{var credit_amount}};</p>
                        <p>Срок на изплащане в месеца: {{var payment_period}};</p>
                        <p>Месечна вноска в лева: {{var monthly_installment}};</p>
                        <p>Първоначална вноска в лева: {{var down_payment}};</p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}';

$template = Mage::getModel('core/email_template')
    ->setTemplateCode($code)
    ->setTemplateSubject($subject)
    ->setTemplateText($templateText)
    ->setModifiedAt(Mage::getSingleton('core/date')->gmtDate())
    ->setTemplateType(Mage_Core_Model_Email_Template::TYPE_HTML)
    ->save();