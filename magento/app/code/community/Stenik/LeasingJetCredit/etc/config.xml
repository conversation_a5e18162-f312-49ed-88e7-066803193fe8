<?xml version="1.0"?>
<config>
    <modules>
        <Stenik_LeasingJetCredit>
            <version>1.1.3</version>
        </Stenik_LeasingJetCredit>
    </modules>

    <global>
        <helpers>
            <stenik_leasingjetcredit>
                <class>Stenik_LeasingJetCredit_Helper</class>
            </stenik_leasingjetcredit>
        </helpers>
        <models>
            <stenik_leasingjetcredit>
                <class>Stenik_LeasingJetCredit_Model</class>
                <resourceModel>stenik_leasingjetcredit_resource</resourceModel>
            </stenik_leasingjetcredit>
            <stenik_leasingjetcredit_resource>
                <class>Stenik_LeasingJetCredit_Model_Resource</class>
                <entities>
                    <goodType>
                        <table>stenik_leasingjetcredit_goodtype</table>
                    </goodType>
                </entities>
            </stenik_leasingjetcredit_resource>
        </models>
        <blocks>
            <stenik_leasingjetcredit>
                <class>Stenik_LeasingJetCredit_Block</class>
            </stenik_leasingjetcredit>
        </blocks>
        <resources>
            <stenik_leasingjetcredit_setup>
                <setup>
                    <module>Stenik_LeasingJetCredit</module>
                </setup>
            </stenik_leasingjetcredit_setup>
        </resources>
        <events>
            <sales_quote_config_get_product_attributes>
                <observers>
                    <stenik_leasingjetcredit>
                        <type>singleton</type>
                        <class>stenik_leasingjetcredit/observer</class>
                        <method>quoteGetProductAttributes</method>
                    </stenik_leasingjetcredit>
                </observers>
            </sales_quote_config_get_product_attributes>

            <core_block_abstract_prepare_layout_after>
                <observers>
                    <stenik_leasingjetcredit>
                        <type>singleton</type>
                        <class>stenik_leasingjetcredit/observer</class>
                        <method>prepareLayoutAfter</method>
                    </stenik_leasingjetcredit>
                </observers>
            </core_block_abstract_prepare_layout_after>
        </events>
    </global>

    <frontend>
        <routers>
            <stenik_leasingjetcredit>
                <use>standard</use>
                <args>
                    <module>Stenik_LeasingJetCredit</module>
                    <frontName>stenik_leasingjetcredit</frontName>
                </args>
            </stenik_leasingjetcredit>
        </routers>
        <layout>
            <updates>
                <stenik_leasingjetcredit>
                    <file>stenik_leasingjetcredit.xml</file>
                </stenik_leasingjetcredit>
            </updates>
        </layout>
        <translate>
            <modules>
                <Stenik_LeasingJetCredit>
                    <files>
                        <default>Stenik_LeasingJetCredit.csv</default>
                    </files>
                </Stenik_LeasingJetCredit>
            </modules>
        </translate>
    </frontend>

    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <stenik_leasingjetcredit before="Mage_Adminhtml">Stenik_LeasingJetCredit_Adminhtml</stenik_leasingjetcredit>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <adminhtml>
        <translate>
            <modules>
                <Stenik_LeasingJetCredit>
                    <files>
                        <default>Stenik_LeasingJetCredit.csv</default>
                    </files>
                </Stenik_LeasingJetCredit>
            </modules>
        </translate>
        <events>
            <adminhtml_widget_container_html_before>
                <observers>
                    <stenik_leasingjetcredit>
                        <class>stenik_leasingjetcredit/observer</class>
                        <type>singleton</type>
                        <method>adminhtmlWidgetContainerHtmlBefore</method>
                    </stenik_leasingjetcredit>
                </observers>
            </adminhtml_widget_container_html_before>
            <adminhtml_sales_order_create_process_data>
                <observers>
                    <stenik_leasingjetcredit>
                        <method>adminhtmlSalesOrderCreateProcessData</method>
                        <model>stenik_leasingjetcredit/observer</model>
                        <type>singleton</type>
                    </stenik_leasingjetcredit>
                </observers>
            </adminhtml_sales_order_create_process_data>
        </events>

    </adminhtml>

    <default>
        <stenik_leasingjetcredit>
            <config>
                <application_email><EMAIL></application_email>
                <merchant_code>433147</merchant_code>
            </config>
            <service>
                <!-- @pfg-modified -->
                <merchant_id></merchant_id>
                <production_url>https://ws.pbpf.bg/ServicesPricing/</production_url>
                <production_certificate_path>bnp-production-cert.pem</production_certificate_path>
                <production_key_path>bnp-production-key.pem</production_key_path>
                <production_key_password></production_key_password>
                <sandbox_mode>1</sandbox_mode>
                <test_url>https://ws-test.pbpf.bg/ServicesPricing/</test_url>
                <test_certificate_path>bnp-test-cert.pem</test_certificate_path>
                <test_key_path>bnp-test-key.pem</test_key_path>
                <test_key_password></test_key_password>
            </service>
        </stenik_leasingjetcredit>
        <payment>
            <stenik_leasingjetcredit>
                <active>0</active>
                <model>stenik_leasingjetcredit/payment_method_jetCredit</model>
                <title><![CDATA[JetCredit]]></title>
                <include_shipping_amount>0</include_shipping_amount>
                <min_order_total>150</min_order_total>
                <hide_down_payment>1</hide_down_payment>
            </stenik_leasingjetcredit>
        </payment>
    </default>

    <crontab>
        <jobs>
            <stenik_leasingjetcredit_>
                <schedule><cron_expr>30 1 * * *</cron_expr></schedule>
                <run><model>stenik_leasingjetcredit/observer::syncGoodTypes</model></run>
            </stenik_leasingjetcredit_>
        </jobs>
    </crontab>
</config>