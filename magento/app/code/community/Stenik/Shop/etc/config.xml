<?xml version="1.0"?>
<config>
    <modules>
        <Stenik_Shop>
            <version>1.0.15</version>
        </Stenik_Shop>
    </modules>

    <global>
        <helpers>
            <stenik_shop>
                <class>Stenik_Shop_Helper</class>
            </stenik_shop>
        </helpers>
        <blocks>
            <stenik_shop>
                <class>Stenik_Shop_Block</class>
            </stenik_shop>
        </blocks>
        <models>
            <stenik_shop>
                <class>Stenik_Shop_Model</class>
                <resourceModel>stenik_shop_resource</resourceModel>
            </stenik_shop>
            <stenik_shop_resource>
                <class>Stenik_Shop_Model_Resource</class>
                <entities>
                    <shop>
                        <table>stenik_shop_entity</table>
                    </shop>
                    <eav_attribute>
                        <table>stenik_shop_attribute</table>
                    </eav_attribute>
                    <shop_attribute_media_gallery>
                        <table>stenik_shop_entity_attribute_media_gallery</table>
                    </shop_attribute_media_gallery>
                    <shop_attribute_media_gallery_value>
                        <table>stenik_shop_entity_attribute_media_gallery_value</table>
                    </shop_attribute_media_gallery_value>
                </entities>
            </stenik_shop_resource>
        </models>
        <resources>
            <stenik_shop_setup>
                <setup>
                    <module>Stenik_Shop</module>
                    <class>Stenik_Shop_Model_Resource_Setup</class>
                </setup>
            </stenik_shop_setup>
        </resources>

        <events>
            <controller_front_init_routers>
                <observers>
                    <stenik_shop>
                        <class>Stenik_Shop_Controller_Router</class>
                        <method>initControllerRouters</method>
                    </stenik_shop>
                </observers>
            </controller_front_init_routers>

            <!-- Observer to sync store data to theme_praktis_store -->
            <stenik_shop_save_after>
                <observers>
                    <stenik_shop_sync_store_data>
                        <type>singleton</type>
                        <class>stenik_shop/observer</class>
                        <method>syncStoreDataToThemeTable</method>
                    </stenik_shop_sync_store_data>
                </observers>
            </stenik_shop_save_after>

            <!-- Observer to handle store deletions -->
            <stenik_shop_delete_after>
                <observers>
                    <stenik_shop_sync_after_delete>
                        <type>singleton</type>
                        <class>stenik_shop/observer</class>
                        <method>syncAfterStoreDelete</method>
                    </stenik_shop_sync_after_delete>
                </observers>
            </stenik_shop_delete_after>
        </events>
    </global>

    <frontend>
        <routers>
            <stenik_shop>
                <use>standard</use>
                <args>
                    <module>Stenik_Shop</module>
                    <frontName>stenik_shop</frontName>
                </args>
            </stenik_shop>
        </routers>
        <layout>
            <updates>
                <stenik_shop>
                    <file>stenik_shop.xml</file>
                </stenik_shop>
            </updates>
        </layout>
        <translate>
            <modules>
                <Stenik_Shop>
                    <files>
                        <stenik_shop>Stenik_Shop.csv</stenik_shop>
                    </files>
                </Stenik_Shop>
            </modules>
        </translate>
    </frontend>

    <admin>
        <routers>
            <stenik_shop>
                <use>admin</use>
                <args>
                    <module>Stenik_Shop</module>
                    <frontName>stenik_shop</frontName>
                </args>
            </stenik_shop>
        </routers>
    </admin>

    <adminhtml>
        <menu>
            <stenik_shop translate="title" module="stenik_shop">
                <title>Shops</title>
                <sort_order>70</sort_order>
                <action>stenik_shop/adminhtml_shop</action>
            </stenik_shop>
        </menu>
        <acl>
            <resources>
                <all>
                    <title>Allow Everything</title>
                </all>
                <admin>
                    <children>
                        <stenik_shop>
                            <title>Shops</title>
                            <sort_order>70</sort_order>
                        </stenik_shop>
                        <system>
                            <children>
                                <config>
                                    <children>
                                        <stenik_shop translate="title" module="stenik_shop">
                                            <title>Shops</title>
                                        </stenik_shop>
                                    </children>
                                </config>
                            </children>
                        </system>
                    </children>
                </admin>
            </resources>
        </acl>
        <layout>
            <updates>
                <stenik_shop>
                    <file>stenik_shop.xml</file>
                </stenik_shop>
            </updates>
        </layout>
        <translate>
            <modules>
                <Stenik_Shop>
                    <files>
                        <stenik_shop>Stenik_Shop.csv</stenik_shop>
                    </files>
                </Stenik_Shop>
            </modules>
        </translate>
    </adminhtml>

    <default>
        <stenik_shop>
            <settings>
                <business_hours_interval><![CDATA[30]]></business_hours_interval>
                <auto_sync_enabled><![CDATA[1]]></auto_sync_enabled>
            </settings>
            <shop_list>
                <page_title><![CDATA[Shops]]></page_title>
                <url_key><![CDATA[shops]]></url_key>
            </shop_list>
            <shop>
                <name_prefix><![CDATA[]]></name_prefix>
                <url_prefix><![CDATA[shop/]]></url_prefix>
                <url_suffix><![CDATA[.html]]></url_suffix>
                <!-- <marker_icon/> -->
            </shop>
        </stenik_shop>
    </default>
</config>
