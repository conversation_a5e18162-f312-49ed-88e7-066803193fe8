<?php
/**
 * @package Stenik_SeoBase
 * <AUTHOR> Magento Team <<EMAIL>>
 */
class Stenik_SeoBase_Helper_UrlRedirect extends Mage_Core_Helper_Abstract
{
    const XML_PATH_URLREDIRECT_REMOVEMULTIPLESLASHES    = 'stenik_seobase/url_redirect/remove_multiple_slashes';
    const XML_PATH_URLREDIRECT_ADDREMOVETRAILINGSLASH   = 'stenik_seobase/url_redirect/addremove_trailing_slash';
    const XML_PATH_URLREDIRECT_LOWERCASE                = 'stenik_seobase/url_redirect/lowercase';
    const XML_PATH_URLREDIRECT_LOWERCASE_EXCLUDES       = 'stenik_seobase/url_redirect/lowercase_excludes';
    const XML_PATH_URLREDIRECT_REMOVEINDEXPHP           = 'stenik_seobase/url_redirect/remove_indexphp';
    const XML_PATH_URLREDIRECT_HOMETOSLASH              = 'stenik_seobase/url_redirect/home_to_slash';
    const XML_PATH_URLREDIRECT_INDEXHTMLTOSLASH         = 'stenik_seobase/url_redirect/indexhtml_to_slash';
    const XML_PATH_URLREDIRECT_PRODUCTSYSTEMTOCANONICAL = 'stenik_seobase/url_redirect/catalog_product_redirect_system_to_canonical';

    /**
     * Force Redirect Code
     *
     * @var integer
     */
    protected $_forceRedirectCode = 0;

    /**
     * Uri Info
     * @var array of Varien_Object
     */
    protected $_uriInfo = array();

    /**
     * Retrieve forced redirect code
     *
     * @return null|integer
     */
    public function getForceRedirectCode()
    {
        return $this->_forceRedirectCode;
    }

    /**
     * Set forced redirect code
     *
     * @param integer $code
     * @return $this
     */
    public function setForceRedirectCode($code = 0)
    {
        $this->_forceRedirectCode = (int) $code;
        return $this;
    }

    /**
     * Retrieve remove multiple slashes
     *
     * @param  mixed $storeId
     * @return boolean
     */
    public function getRemoveMultipleSlashes($storeId = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_URLREDIRECT_REMOVEMULTIPLESLASHES, $storeId);
    }

    /**
     * Retrieve Add/Remove trailing slash
     *
     * @param  mixed $storeId
     * @return integer
     */
    public function getAddRemoveTrailingSlash($storeId = null)
    {
        return (int) Mage::getStoreConfig(self::XML_PATH_URLREDIRECT_ADDREMOVETRAILINGSLASH, $storeId);
    }

    /**
     * Retrieve lowercase
     *
     * @param  mixed $storeId
     * @return boolean
     */
    public function getLowercase($storeId = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_URLREDIRECT_LOWERCASE, $storeId);
    }

    /**
     * Retrieve lowercase exceptions
     *
     * @param  mixed $storeId
     * @return boolean
     */
    public function getLowercaseExcludes($storeId = null)
    {
        $excludes = Mage::getStoreConfig(self::XML_PATH_URLREDIRECT_LOWERCASE_EXCLUDES, $storeId);
        $excludes = explode("\n", $excludes);
        $excludes = array_filter($excludes);
        return $excludes;
    }

    /**
     * Retrieve remove index.php
     *
     * @param  mixed $storeId
     * @return boolean
     */
    public function getRemoveIndexphp($storeId = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_URLREDIRECT_REMOVEINDEXPHP, $storeId);
    }

    /**
     * Retrieve /home to slash
     *
     * @param  mixed $storeId
     * @return boolean
     */
    public function getHomeToSlash($storeId = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_URLREDIRECT_HOMETOSLASH, $storeId);
    }

    /**
     * Retrieve /index.html to slash
     *
     * @param  mixed $storeId
     * @return boolean
     */
    public function getIndexHtmlToSlash($storeId = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_URLREDIRECT_INDEXHTMLTOSLASH, $storeId);
    }

    /**
     * Retrieve Product System URL redirect to its Canonical
     *
     * @param  mixed $storeId
     * @return boolean
     */
    public function getProductSystemToCanonical($storeId = null)
    {
        return Mage::getStoreConfigFlag(self::XML_PATH_URLREDIRECT_PRODUCTSYSTEMTOCANONICAL, $storeId);
    }

    /**
     * Reformat URI and redirect if so
     *
     * @return void
     */
    public function reformatUri()
    {
        Varien_Profiler::start(__METHOD__);
        if ($this->canSafelyReformatUri()) {
            $uriInfo         = $this->_getUriInfo();
            $uri             = $uriInfo->getUri();
            $uriWithoutQuery = $uriInfo->getUriWithoutQuery();
            $uriQueryString  = $uriInfo->getUriQueryString();


            // Remove multiple slashes
            if ($this->getRemoveMultipleSlashes()) {
                if (preg_match('/\/\/+/', $uriWithoutQuery)) {
                    $uriWithoutQuery = preg_replace('/\/\/+/', '/', $uriWithoutQuery);
                }
            }

            // Remove/Add slash at the end of the uri without query
            switch ($this->getAddRemoveTrailingSlash()) {
                case Stenik_SeoBase_Model_System_Config_Source_Addremovedonothing::OPTION_ADD:
                    if (preg_match('/[^\/]$/', $uriWithoutQuery)) {
                        $uriWithoutQuery .= '/';
                    }
                    break;
                case Stenik_SeoBase_Model_System_Config_Source_Addremovedonothing::OPTION_REMOVE:
                    if (preg_match('/\/$/', $uriWithoutQuery)) {
                        $uriWithoutQuery = substr($uriWithoutQuery, 0, strlen($uriWithoutQuery) - 1);
                    }
                    break;
            }

            // Lower url's case
            if ($this->getLowercase()) {
                $shouldLowercase = true;
                $excludes = $this->getLowercaseExcludes();
                foreach ($excludes as $exclude) {
                    if (preg_match("/$exclude/", $uriWithoutQuery)) {
                        $shouldLowercase = false;
                        break;
                    }
                }
                if ($shouldLowercase) {
                    $uriWithoutQuery = strtolower($uriWithoutQuery);
                }
            }

            // Remove index.php
            if ($this->getRemoveIndexphp()) {
                $indexPhp = '/index.php';
                if (strpos($uriWithoutQuery, $indexPhp) === 0) {
                    $uriWithoutQuery = substr($uriWithoutQuery, strlen($indexPhp));
                }
            }

            // Remove /home
            if ($this->getHomeToSlash()) {
                if (preg_match('/^\/home\/?$/', $uriWithoutQuery)) {
                    $uriWithoutQuery = '/';
                }
            }

            // Remove /index.html
            if ($this->getIndexHtmlToSlash()) {
                if (preg_match('/^\/index\.html\/?$/', $uriWithoutQuery)) {
                    $uriWithoutQuery = '/';
                }
            }

            if (!$uriWithoutQuery) {
                $uriWithoutQuery = '/';
            }

            $newUri = $uriWithoutQuery;
            if ($uriQueryString) {
                $newUri .= '?' . $uriQueryString;
            }


            if ($newUri != $uri && urldecode($newUri) != urldecode($uri)) {
                $response = Mage::app()->getResponse();
                $response->setRedirect($newUri, 301);
                $response->sendResponse();
                Varien_Profiler::stop(__METHOD__);
                exit;
            }
        }

        Varien_Profiler::stop(__METHOD__);
    }

    /**
     * Reformat Product System URI and redirect if so
     *
     * @return void
     */
    public function reformatProductSystemUri()
    {
        Varien_Profiler::start(__METHOD__);
        if ($this->getProductSystemToCanonical() && $this->canSafelyReformatUri()) {
            $request = Mage::app()->getRequest();
            $uriWithoutQuery = $this->_getUriInfo()->getUriWithoutQuery();

            // If is system URI Redirect to product canonical non-system URL
            if (
                preg_match('/^\/catalog\/product\/view($|\/)/', $request->getPathInfo()) &&
                preg_match('/\/catalog\/product\/view($|\/)/', $uriWithoutQuery)
            ) {
                $product = Mage::registry('product');
                if (!$product) {
                    $productId  = (int) $request->getParam('id');
                    $product = Mage::getModel('catalog/product')
                        ->setStoreId(Mage::app()->getStore()->getId())
                        ->load($productId);
                }

                if ($product) {
                    $urlModel = $product->getUrlModel();
                    $productUrl = $urlModel->getProductUrl($product); //Also generates request_path

                    if ($product->getRequestPath() &&
                        !preg_match('/^\/?catalog\/product\/view($|\/)/', $product->getRequestPath()) &&
                        !preg_match('/\/catalog\/product\/view\//', $productUrl)
                    ) {
                        $params = array(
                            '_direct' => $product->getRequestPath(),
                            '_query'  => $request->getQuery()
                        );

                        $redirectException = new Stenik_SeoBase_Controller_Varien_Exception();
                        $redirectException->prepareRedirect('', $params);
                        $this->setForceRedirectCode(301);

                        Varien_Profiler::stop(__METHOD__);
                        throw $redirectException;
                    }
                }
            }

        }
        Varien_Profiler::stop(__METHOD__);
    }

    /**
     * Return if the URI can be safely reformatted
     *
     * @return bool
     */
    public function canSafelyReformatUri()
    {
        $request = Mage::app()->getRequest();

        if (
            !$request->isGet() ||
            $request->isXmlHttpRequest() ||
            $request->getParam('form_key') ||
            $request->getParam('formkey') ||
            $request->getParam('uenc') ||
            $this->_isAdminFrontNameMatched($request)
        ) {
            return false;
        }

        /**
         * The request's params may not be set if this function is called too early,
         * so - parse them as the standard controller parses them.
         */
        if ($path = trim($request->getPathInfo(), '/')) {
            $tmpRequest = new Zend_Controller_Request_Http();

            $p = explode('/', $path);

            // set parameters from pathinfo, @see Mage_Core_Controller_Varien_Router_Standard::match
            for ($i = 3, $l = sizeof($p); $i < $l; $i += 2) {
                $tmpRequest->setParam($p[$i], isset($p[$i+1]) ? urldecode($p[$i+1]) : '');
            }

            if (
                $tmpRequest->getParam('form_key') ||
                $tmpRequest->getParam('formkey') ||
                $tmpRequest->getParam('uenc')
            ) {
                return false;
            }
        }

        return true;
    }

    /**
     * Retrieve URI info
     *
     * @return Varien_Object
     */
    protected function _getUriInfo()
    {
        $request = Mage::app()->getRequest();
        $uri     = $request->getRequestUri();

        if (!isset($this->_uriInfo[$uri])) {
            $uriWithoutQuery = $uri;
            $uriQueryString  = '';

            if (strpos($uriWithoutQuery, '?') !== false) {
                $uriParts        = explode('?', $uriWithoutQuery);
                $uriWithoutQuery = $uriParts[0];
                $uriQueryString  = $uriParts[1];
            }

            $this->_uriInfo[$uri] = new Varien_Object(array(
                'uri'               => $uri,
                'uri_without_query' => $uriWithoutQuery,
                'uri_query_string'  => $uriQueryString,
            ));
        }

        return $this->_uriInfo[$uri];
    }

    /**
     * Check if requested path starts with one of the admin front names
     *
     * This method is copy of Mage_Core_Controller_Varien_Front::_isAdminFrontNameMatched
     *
     * @see Mage_Core_Controller_Varien_Front::_isAdminFrontNameMatched
     * @todo ! Make a model which extends Mage_Core_Controller_Varien_Front and make a public function ot call _isAdminFrontNameMatched
     *
     * @param Zend_Controller_Request_Http $request
     * @return boolean
     */
    protected function _isAdminFrontNameMatched($request)
    {
        $useCustomAdminPath = (bool)(string)Mage::getConfig()
            ->getNode(Mage_Adminhtml_Helper_Data::XML_PATH_USE_CUSTOM_ADMIN_PATH);
        $customAdminPath = (string)Mage::getConfig()->getNode(Mage_Adminhtml_Helper_Data::XML_PATH_CUSTOM_ADMIN_PATH);
        $adminPath = ($useCustomAdminPath) ? $customAdminPath : null;

        if (!$adminPath) {
            $adminPath = (string)Mage::getConfig()
                ->getNode(Mage_Adminhtml_Helper_Data::XML_PATH_ADMINHTML_ROUTER_FRONTNAME);
        }
        $adminFrontNames = array($adminPath);

        // Check for other modules that can use admin router (a lot of Magento extensions do that)
        $adminFrontNameNodes = Mage::getConfig()->getNode('admin/routers')
            ->xpath('*[not(self::adminhtml) and use = "admin"]/args/frontName');

        if (is_array($adminFrontNameNodes)) {
            foreach ($adminFrontNameNodes as $frontNameNode) {
                /** @var $frontNameNode SimpleXMLElement */
                array_push($adminFrontNames, (string)$frontNameNode);
            }
        }

        $pathPrefix = ltrim($request->getPathInfo(), '/');
        $urlDelimiterPos = strpos($pathPrefix, '/');
        if ($urlDelimiterPos) {
            $pathPrefix = substr($pathPrefix, 0, $urlDelimiterPos);
        }

        return in_array($pathPrefix, $adminFrontNames);
    }
}
