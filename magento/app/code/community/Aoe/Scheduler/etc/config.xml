<?xml version="1.0" ?>
<config>
    <modules>
        <Aoe_Scheduler>
            <version>1.5.2</version>
        </Aoe_Scheduler>
    </modules>

    <global>
        <blocks>
            <aoe_scheduler>
                <class>Aoe_Scheduler_Block</class>
            </aoe_scheduler>
        </blocks>

        <helpers>
            <aoe_scheduler>
                <class>Aoe_Scheduler_Helper</class>
            </aoe_scheduler>
        </helpers>

        <models>
            <aoe_scheduler>
                <class>Aoe_Scheduler_Model</class>
                <resourceModel>aoe_scheduler_resource</resourceModel>
            </aoe_scheduler>

            <aoe_scheduler_resource>
                <class>Aoe_Scheduler_Model_Resource</class>
            </aoe_scheduler_resource>

            <cron>
                <rewrite>
                    <observer>Aoe_Scheduler_Model_Observer</observer>
                    <schedule>Aoe_Scheduler_Model_Schedule</schedule>
                </rewrite>
            </cron>
            <cron_resource>
                <rewrite>
                    <schedule>Aoe_Scheduler_Model_Resource_Schedule</schedule>
                    <schedule_collection>Aoe_Scheduler_Model_Resource_Schedule_Collection</schedule_collection>
                </rewrite>
            </cron_resource>
        </models>

        <template>
            <email>
                <system_cron_error_email_template translate="label" module="aoe_scheduler">
                    <label>Cron error</label>
                    <file>aoe_scheduler/cron_error.html</file>
                    <type>text</type>
                </system_cron_error_email_template>
            </email>
        </template>

        <resources>
            <aoescheduler_setup>
                <setup>
                    <module>Aoe_Scheduler</module>
                </setup>
                <connection>
                    <use>core_setup</use>
                </connection>
            </aoescheduler_setup>
        </resources>

        <aoe_logviewer>
            <logs>
                <cron.log>
                    <label>cron.log</label>
                    <file_path>###LOGDIR###/cron.log</file_path>
                    <model>aoe_logviewer/log_file</model>
                    <sort_order>10</sort_order>
                    <!--<disabled>1</disabled>-->
                    <commands>
                        <tail>
                            <label>Last n lines (newest first)</label>
                            <command_string><![CDATA[tail -n %2$s '%1$s' | tac]]></command_string>
                            <model>aoe_logviewer/command_shell</model>
                            <sort_order>10</sort_order>
                            <!--<disabled>1</disabled>-->
                        </tail>
                        <tail_err>
                            <label>Last n error lines (newest first)</label>
                            <command_string><![CDATA[grep 'ERR (3)' '%1$s' | tail -n %2$s | tac]]></command_string>
                            <model>aoe_logviewer/command_shell</model>
                            <sort_order>20</sort_order>
                        </tail_err>
                        <duplicates>
                            <label>Find duplicates</label>
                            <command_string><![CDATA[cat '%1$s' | cut -c27- | sort | uniq -c | sort -rn | head -n %2$s]]></command_string>
                            <model>aoe_logviewer/command_shell</model>
                            <sort_order>30</sort_order>
                        </duplicates>
                    </commands>
                </cron.log>
            </logs>
        </aoe_logviewer>

    </global>

    <admin>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <Aoe_Scheduler before="Mage_Adminhtml">Aoe_Scheduler_Adminhtml</Aoe_Scheduler>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <adminhtml>
        <translate>
            <modules>
                <aoe_scheduler>
                    <files>
                        <default>Aoe_Scheduler.csv</default>
                    </files>
                </aoe_scheduler>
            </modules>
        </translate>
        <layout>
            <updates>
                <aoe_scheduler>
                    <file>aoe_scheduler/aoe_scheduler.xml</file>
                </aoe_scheduler>
            </updates>
        </layout>
    </adminhtml>

    <crontab>
        <jobs>
            <aoescheduler_testtask>
                <!--<schedule><cron_expr>*/5 * * * *</cron_expr></schedule>-->
                <run>
                    <model>aoe_scheduler/task_test::run</model>
                </run>
            </aoescheduler_testtask>

            <aoescheduler_heartbeat>
                <schedule>
                    <config_path>system/cron/scheduler_cron_expr_heartbeat</config_path>
                </schedule>
                <run>
                    <model>aoe_scheduler/task_heartbeat::run</model>
                </run>
                <groups>all</groups>
            </aoescheduler_heartbeat>
        </jobs>
    </crontab>

    <default>
        <system>
            <cron>
                <enable>1</enable>
                <mark_as_error_after>15</mark_as_error_after>
                <scheduler_cron_expr_heartbeat>*/5 * * * *</scheduler_cron_expr_heartbeat>
                <error_email_identity>general</error_email_identity>
                <error_email_template>system_cron_error_email_template</error_email_template>
                <logFile>cron.log</logFile>
                <enableRunNow>0</enableRunNow>
                <enableErrorLog>0</enableErrorLog>
                <errorLevel>-1</errorLevel>
                <errorLogFilename>###JOBCODE###.log</errorLogFilename>
                <listCodeFilterType>select</listCodeFilterType>
                <showCronUserMessage>1</showCronUserMessage>
                <killOnIncorrectUser>0</killOnIncorrectUser>
            </cron>
        </system>
    </default>

    <phpunit>
        <suite>
            <modules>
                <Aoe_Scheduler/>
            </modules>
        </suite>
    </phpunit>

</config>
