<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Directory
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Directory>
            <version>1.6.0.3</version>
        </Mage_Directory>
    </modules>
    <global>
        <currency>
            <import>
                <services>
                    <currencyconverterapi>
                        <name>CurrencyConverterAPI</name>
                        <model>directory/currency_import_currencyconverterapi</model>
                    </currencyconverterapi>
                    <fixerio>
                        <name>Fixer.IO</name>
                        <model>directory/currency_import_fixerio</model>
                    </fixerio>
                    <webservicex>
                        <name>Webservicex</name>
                        <model>directory/currency_import_webservicex</model>
                    </webservicex>
                </services>
            </import>
        </currency>
        <blocks>
            <directory>
                <class>Mage_Directory_Block</class>
            </directory>
        </blocks>
        <models>
            <directory>
                <class>Mage_Directory_Model</class>
                <resourceModel>directory_resource</resourceModel>
            </directory>
            <directory_resource>
                <class>Mage_Directory_Model_Resource</class>
                <deprecatedNode>directory_mysql4</deprecatedNode>
                <entities>
                    <country>
                        <table>directory_country</table>
                    </country>
                    <country_format>
                        <table>directory_country_format</table>
                    </country_format>
                    <country_region>
                        <table>directory_country_region</table>
                    </country_region>
                    <country_region_name>
                        <table>directory_country_region_name</table>
                    </country_region_name>
                    <currency_rate>
                        <table>directory_currency_rate</table>
                    </currency_rate>
                </entities>
            </directory_resource>
        </models>
        <resources>
            <directory_setup>
                <setup>
                    <module>Mage_Directory</module>
                </setup>
            </directory_setup>
        </resources>
        <template>
            <email>
                <currency_import_error_email_template translate="label" module="directory">
                    <label>Currency Update Warnings</label>
                    <file>currency_update_warning.html</file>
                    <type>text</type>
                </currency_import_error_email_template>
            </email>
        </template>
    </global>
    <frontend>
        <routers>
            <directory>
                <use>standard</use>
                <args>
                    <module>Mage_Directory</module>
                    <frontName>directory</frontName>
                </args>
            </directory>
        </routers>
        <translate>
            <modules>
                <Mage_Directory>
                    <files>
                        <default>Mage_Directory.csv</default>
                    </files>
                </Mage_Directory>
            </modules>
        </translate>
        <layout>
            <updates>
                <directory>
                    <file>directory.xml</file>
                </directory>
            </updates>
        </layout>
    </frontend>
    <adminhtml>
        <translate>
            <modules>
                <Mage_Directory>
                    <files>
                        <default>Mage_Directory.csv</default>
                    </files>
                </Mage_Directory>
            </modules>
        </translate>
    </adminhtml>
    <default>
        <system>
            <currency>
                <installed>AZN,AZM,AFN,ALL,DZD,AOA,ARS,AMD,AWG,AUD,BSD,BHD,BDT,BBD,BYR,BZD,BMD,BTN,BOB,BAM,BWP,BRL,GBP,BND,BGN,BUK,BIF,KHR,CAD,CVE,CZK,KYD,CLP,CNY,COP,KMF,CDF,CRC,HRK,CUP,DKK,DJF,DOP,XCD,EGP,SVC,GQE,ERN,EEK,ETB,EUR,FKP,FJD,GMD,GEK,GEL,GHS,GIP,GTQ,GNF,GYD,HTG,HNL,HKD,HUF,ISK,INR,IDR,IRR,IQD,ILS,JMD,JPY,JOD,KZT,KES,KWD,KGS,LAK,LVL,LBP,LSL,LRD,LYD,LTL,MOP,MKD,MGA,MWK,MYR,MVR,LSM,MRO,MUR,MXN,MDL,MNT,MAD,MZN,MMK,NAD,NPR,ANG,TRL,TRY,NZD,NIC,NGN,KPW,NOK,OMR,PKR,PAB,PGK,PYG,PEN,PHP,PLN,QAR,RHD,RON,ROL,RUB,RWF,SHP,STD,SAR,RSD,SCR,SLL,SGD,SKK,SBD,SOS,ZAR,KRW,LKR,SDG,SRD,SZL,SEK,CHF,SYP,TWD,TJS,TZS,THB,TOP,TTD,TND,TMM,USD,UGX,UAH,AED,UYU,UZS,VUV,VEB,VEF,VND,CHE,CHW,XOF,XPF,WST,YER,ZMK,ZWD</installed>
            </currency>
        </system>
        <currency>
            <options>
                <allow>USD,EUR</allow>
                <base>USD</base>
                <default>USD</default>
            </options>
            <currencyconverterapi>
                <timeout>100</timeout>
                <api_key backend_model="adminhtml/system_config_backend_encrypted"/>
                <active>1</active>
            </currencyconverterapi>
            <fixerio>
                <timeout>100</timeout>
                <api_key backend_model="adminhtml/system_config_backend_encrypted"/>
                <active>1</active>
            </fixerio>
            <webservicex>
                <timeout>100</timeout>
                <active>0</active>
            </webservicex>
            <import>
                <enabled>0</enabled>
                <error_email/>
                <error_email_identity>general</error_email_identity>
                <error_email_template>currency_import_error_email_template</error_email_template>
            </import>
        </currency>
        <general>
            <country>
                <optional_zip_countries>HK,IE,MO,PA</optional_zip_countries>
                <allow>AF,AL,DZ,AS,AD,AO,AI,AQ,AG,AR,AM,AW,AU,AT,AX,AZ,BS,BH,BD,BB,BY,BE,BZ,BJ,BM,BL,BT,BO,BA,BW,BV,BR,IO,VG,BN,BG,BF,BI,KH,CM,CA,CD,CV,KY,CF,TD,CL,CN,CX,CC,CO,KM,CG,CK,CR,HR,CU,CY,CZ,DK,DJ,DM,DO,EC,EG,SV,GQ,ER,EE,ET,FK,FO,FJ,FI,FR,GF,PF,TF,GA,GM,GE,DE,GG,GH,GI,GR,GL,GD,GP,GU,GT,GN,GW,GY,HT,HM,HN,HK,HU,IS,IM,IN,ID,IR,IQ,IE,IL,IT,CI,JE,JM,JP,JO,KZ,KE,KI,KW,KG,LA,LV,LB,LS,LR,LY,LI,LT,LU,ME,MF,MO,MK,MG,MW,MY,MV,ML,MT,MH,MQ,MR,MU,YT,FX,MX,FM,MD,MC,MN,MS,MA,MZ,MM,NA,NR,NP,NL,AN,NC,NZ,NI,NE,NG,NU,NF,KP,MP,NO,OM,PK,PW,PA,PG,PY,PE,PH,PN,PL,PS,PT,PR,QA,RE,RO,RS,RU,RW,SH,KN,LC,PM,VC,WS,SM,ST,SA,SN,SC,SL,SG,SK,SI,SB,SO,ZA,GS,KR,ES,LK,SD,SR,SJ,SZ,SE,CH,SY,TL,TW,TJ,TZ,TH,TG,TK,TO,TT,TN,TR,TM,TC,TV,VI,UG,UA,AE,GB,US,UM,UY,UZ,VU,VA,VE,VN,WF,EH,YE,ZM,ZW</allow>
                <default>US</default>
            </country>
            <local>
                <datetime_format_long>%A, %B %e %Y [%I:%M %p]</datetime_format_long>
                <datetime_format_medium>%a, %b %e %Y [%I:%M %p]</datetime_format_medium>
                <datetime_format_short>%m/%d/%y [%I:%M %p]</datetime_format_short>
                <date_format_long>%A, %B %e %Y</date_format_long>
                <date_format_medium>%a, %b %e %Y</date_format_medium>
                <date_format_short>%m/%d/%y</date_format_short>
                <language>en</language>
            </local>
        </general>
    </default>
    <crontab>
        <jobs>
            <currency_rates_update>
                <run>
                    <model>directory/observer::scheduledUpdateCurrencyRates</model>
                </run>
            </currency_rates_update>
        </jobs>
    </crontab>
</config>
