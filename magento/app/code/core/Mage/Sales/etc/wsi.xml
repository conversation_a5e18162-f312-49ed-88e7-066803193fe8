<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:typens="urn:{{var wsdl.name}}"
             xmlns:xsd="http://www.w3.org/2001/XMLSchema"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/"
             xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/"
             xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/"
             name="{{var wsdl.name}}"
             targetNamespace="urn:{{var wsdl.name}}">
    <wsdl:types>
        <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" targetNamespace="urn:{{var wsdl.name}}">
            <xsd:complexType name="salesOrderEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="subtotal" type="xsd:string" minOccurs="0" />
                    <xsd:element name="grand_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_paid" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_qty_ordered" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_online_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_offline_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_subtotal" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_grand_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_paid" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_qty_ordered" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_online_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_offline_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_address_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_address_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_to_base_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_to_order_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_to_global_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_to_order_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weight" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="remote_ip" type="xsd:string" minOccurs="0" />
                    <xsd:element name="status" type="xsd:string" minOccurs="0" />
                    <xsd:element name="state" type="xsd:string" minOccurs="0" />
                    <xsd:element name="applied_rule_ids" type="xsd:string" minOccurs="0" />
                    <xsd:element name="global_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_method" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_description" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_email" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="quote_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_virtual" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_group_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_note_notify" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_is_guest" type="xsd:string" minOccurs="0" />
                    <xsd:element name="email_sent" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_message_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_message" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_address" type="typens:salesOrderAddressEntity" minOccurs="0" />
                    <xsd:element name="billing_address" type="typens:salesOrderAddressEntity" minOccurs="0" />
                    <xsd:element name="items" type="typens:salesOrderItemEntityArray" minOccurs="0" />
                    <xsd:element name="payment" type="typens:salesOrderPaymentEntity" minOccurs="0" />
                    <xsd:element name="status_history" type="typens:salesOrderStatusHistoryEntityArray" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderListEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="subtotal" type="xsd:string" minOccurs="0" />
                    <xsd:element name="grand_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_paid" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_qty_ordered" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_online_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_offline_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_subtotal" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_grand_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_paid" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_qty_ordered" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_online_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_offline_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_address_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_address_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_to_base_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_to_order_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_to_global_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_to_order_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weight" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="remote_ip" type="xsd:string" minOccurs="0" />
                    <xsd:element name="status" type="xsd:string" minOccurs="0" />
                    <xsd:element name="state" type="xsd:string" minOccurs="0" />
                    <xsd:element name="applied_rule_ids" type="xsd:string" minOccurs="0" />
                    <xsd:element name="global_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_method" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_description" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_email" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="quote_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_virtual" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_group_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_note_notify" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_is_guest" type="xsd:string" minOccurs="0" />
                    <xsd:element name="email_sent" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_message_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="coupon_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="protect_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_discount_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_discount_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_discount_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_tax_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_subtotal_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_subtotal_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_subtotal_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_invoiced_cost" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_tax_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="subtotal_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="subtotal_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="subtotal_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="can_ship_partially" type="xsd:string" minOccurs="0" />
                    <xsd:element name="can_ship_partially_item" type="xsd:string" minOccurs="0" />
                    <xsd:element name="edit_increment" type="xsd:string" minOccurs="0" />
                    <xsd:element name="forced_do_shipment_with_invoice" type="xsd:string" minOccurs="0" />
                    <xsd:element name="payment_authorization_expiration" type="xsd:string" minOccurs="0" />
                    <xsd:element name="paypal_ipn_customer_notified" type="xsd:string" minOccurs="0" />
                    <xsd:element name="quote_address_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="adjustment_negative" type="xsd:string" minOccurs="0" />
                    <xsd:element name="adjustment_positive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_adjustment_negative" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_adjustment_positive" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_subtotal_incl_tax" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_total_due" type="xsd:string" minOccurs="0" />
                    <xsd:element name="payment_authorization_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="subtotal_incl_tax" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_due" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_dob" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_middlename" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_prefix" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_suffix" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_taxvat" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_description" type="xsd:string" minOccurs="0" />
                    <xsd:element name="ext_customer_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="ext_order_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="hold_before_state" type="xsd:string" minOccurs="0" />
                    <xsd:element name="hold_before_status" type="xsd:string" minOccurs="0" />
                    <xsd:element name="original_increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="relation_child_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="relation_child_real_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="relation_parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="relation_parent_real_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="x_forwarded_for" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_note" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_item_count" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_gender" type="xsd:string" minOccurs="0" />
                    <xsd:element name="hidden_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_hidden_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_hidden_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_hidden_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="hidden_tax_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_hidden_tax_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="hidden_tax_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_hidden_tax_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_incl_tax" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_incl_tax" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_customer_balance_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_balance_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_customer_balance_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_balance_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_customer_balance_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_balance_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_customer_balance_total_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="customer_balance_total_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_cards" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_gift_cards_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_cards_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_gift_cards_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_cards_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_gift_cards_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_cards_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="reward_points_balance" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_reward_currency_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="reward_currency_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_reward_currency_amount_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="reward_currency_amount_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_reward_currency_amount_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="reward_currency_amount_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="reward_points_balance_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="reward_points_balance_to_refund" type="xsd:string" minOccurs="0" />
                    <xsd:element name="reward_salesrule_points" type="xsd:string" minOccurs="0" />
                    <xsd:element name="firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="telephone" type="xsd:string" minOccurs="0" />
                    <xsd:element name="postcode" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderListEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderListEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderAddressEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="address_type" type="xsd:string" minOccurs="0" />
                    <xsd:element name="firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="company" type="xsd:string" minOccurs="0" />
                    <xsd:element name="street" type="xsd:string" minOccurs="0" />
                    <xsd:element name="city" type="xsd:string" minOccurs="0" />
                    <xsd:element name="region" type="xsd:string" minOccurs="0" />
                    <xsd:element name="postcode" type="xsd:string" minOccurs="0" />
                    <xsd:element name="country_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="telephone" type="xsd:string" minOccurs="0" />
                    <xsd:element name="fax" type="xsd:string" minOccurs="0" />
                    <xsd:element name="region_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="address_id" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderItemEntity">
                <xsd:sequence>
                    <xsd:element name="item_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="quote_item_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="product_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="product_type" type="xsd:string" minOccurs="0" />
                    <xsd:element name="product_options" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weight" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_virtual" type="xsd:string" minOccurs="0" />
                    <xsd:element name="sku" type="xsd:string" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="applied_rule_ids" type="xsd:string" minOccurs="0" />
                    <xsd:element name="free_shipping" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_qty_decimal" type="xsd:string" minOccurs="0" />
                    <xsd:element name="no_discount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="qty_canceled" type="xsd:string" minOccurs="0" />
                    <xsd:element name="qty_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="qty_ordered" type="xsd:string" minOccurs="0" />
                    <xsd:element name="qty_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="qty_shipped" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cost" type="xsd:string" minOccurs="0" />
                    <xsd:element name="price" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_price" type="xsd:string" minOccurs="0" />
                    <xsd:element name="original_price" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_original_price" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_percent" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_percent" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_discount_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="amount_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_amount_refunded" type="xsd:string" minOccurs="0" />
                    <xsd:element name="row_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_row_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="row_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_row_invoiced" type="xsd:string" minOccurs="0" />
                    <xsd:element name="row_weight" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_message_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_message" type="xsd:string" minOccurs="0" />
                    <xsd:element name="gift_message_available" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_before_discount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_before_discount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_applied" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_applied_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_applied_row_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_weee_tax_applied_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_weee_tax_applied_row_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_disposition" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_row_disposition" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_weee_tax_disposition" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_weee_tax_row_disposition" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderItemEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderItemEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="orderItemIdQty">
                <xsd:sequence>
                    <xsd:element name="order_item_id" type="xsd:int" />
                    <xsd:element name="qty" type="xsd:double" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="orderItemIdQtyArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:orderItemIdQty" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderPaymentEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="amount_ordered" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_amount_ordered" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="method" type="xsd:string" minOccurs="0" />
                    <xsd:element name="po_number" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cc_type" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cc_number_enc" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cc_last4" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cc_owner" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cc_exp_month" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cc_exp_year" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cc_ss_start_month" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cc_ss_start_year" type="xsd:string" minOccurs="0" />
                    <xsd:element name="payment_id" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderStatusHistoryEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_customer_notified" type="xsd:string" minOccurs="0" />
                    <xsd:element name="status" type="xsd:string" minOccurs="0" />
                    <xsd:element name="comment" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderStatusHistoryEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderStatusHistoryEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderShipmentEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_address_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="total_qty" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="items" type="typens:salesOrderShipmentItemEntityArray" minOccurs="0" />
                    <xsd:element name="tracks" type="typens:salesOrderShipmentTrackEntityArray" minOccurs="0" />
                    <xsd:element name="comments" type="typens:salesOrderShipmentCommentEntityArray" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderShipmentEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderShipmentEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderShipmentCommentEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="comment" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_customer_notified" type="xsd:string" minOccurs="0" />
                    <xsd:element name="comment_id" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderShipmentCommentEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderShipmentCommentEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderShipmentTrackEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="carrier_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="title" type="xsd:string" minOccurs="0" />
                    <xsd:element name="number" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="track_id" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderShipmentTrackEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderShipmentTrackEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderShipmentItemEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="sku" type="xsd:string" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_item_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="product_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weight" type="xsd:string" minOccurs="0" />
                    <xsd:element name="price" type="xsd:string" minOccurs="0" />
                    <xsd:element name="qty" type="xsd:string" minOccurs="0" />
                    <xsd:element name="item_id" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderShipmentItemEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderShipmentItemEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderInvoiceEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="global_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_currency_code" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_to_base_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="store_to_order_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_to_global_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_to_order_rate" type="xsd:string" minOccurs="0" />
                    <xsd:element name="subtotal" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_subtotal" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_grand_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_discount_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="shipping_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_shipping_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_address_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_firstname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="billing_lastname" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="state" type="xsd:string" minOccurs="0" />
                    <xsd:element name="grand_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="invoice_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="items" type="typens:salesOrderInvoiceItemEntityArray" minOccurs="0" />
                    <xsd:element name="comments" type="typens:salesOrderInvoiceCommentEntityArray" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderInvoiceEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderInvoiceEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderInvoiceItemEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_applied" type="xsd:string" minOccurs="0" />
                    <xsd:element name="qty" type="xsd:string" minOccurs="0" />
                    <xsd:element name="cost" type="xsd:string" minOccurs="0" />
                    <xsd:element name="price" type="xsd:string" minOccurs="0" />
                    <xsd:element name="tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="row_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_price" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_tax_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_row_total" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_weee_tax_applied_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_weee_tax_applied_row_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_applied_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_applied_row_amount" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_disposition" type="xsd:string" minOccurs="0" />
                    <xsd:element name="weee_tax_row_disposition" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_weee_tax_disposition" type="xsd:string" minOccurs="0" />
                    <xsd:element name="base_weee_tax_row_disposition" type="xsd:string" minOccurs="0" />
                    <xsd:element name="sku" type="xsd:string" minOccurs="0" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" />
                    <xsd:element name="order_item_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="product_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="item_id" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderInvoiceItemEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderInvoiceItemEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderInvoiceCommentEntity">
                <xsd:sequence>
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_active" type="xsd:string" minOccurs="0" />
                    <xsd:element name="comment" type="xsd:string" minOccurs="0" />
                    <xsd:element name="is_customer_notified" type="xsd:string" minOccurs="0" />
                    <xsd:element name="comment_id" type="xsd:string" minOccurs="0" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderInvoiceCommentEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderInvoiceCommentEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderCreditmemoEntity">
                <xsd:sequence>
                    <xsd:element name="updated_at" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="increment_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="transaction_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="global_currency_code" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_currency_code" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="order_currency_code" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="store_currency_code" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="cybersource_token" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="invoice_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="billing_address_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="shipping_address_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="state" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="creditmemo_status" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="email_sent" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="order_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="shipping_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_adjustment_positive" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_grand_total" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="adjustment" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="subtotal" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="discount_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_subtotal" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_adjustment" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_to_global_rate" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="store_to_base_rate" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_shipping_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="adjustment_negative" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="subtotal_incl_tax" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="shipping_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_subtotal_incl_tax" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_adjustment_negative" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="grand_total" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_discount_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_to_order_rate" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="store_to_order_rate" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_shipping_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="adjustment_positive" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="store_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="hidden_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_hidden_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="shipping_hidden_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_shipping_hidden_tax_amnt" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="shipping_incl_tax" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_shipping_incl_tax" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_customer_balance_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="customer_balance_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="bs_customer_bal_total_refunded" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="customer_bal_total_refunded" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_gift_cards_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gift_cards_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_base_price" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_price" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_items_base_price" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_items_price" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_card_base_price" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_card_price" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_base_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_items_base_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_items_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_card_base_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="gw_card_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_reward_currency_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="reward_currency_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="reward_points_balance" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="reward_points_balance_refund" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="creditmemo_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="items" type="typens:salesOrderCreditmemoItemEntityArray" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="comments" type="typens:salesOrderCreditmemoCommentEntityArray" minOccurs="0" maxOccurs="1" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderCreditmemoEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderCreditmemoEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderCreditmemoItemEntity">
                <xsd:sequence>
                    <xsd:element name="item_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="weee_tax_applied_row_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_price" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_weee_tax_row_disposition" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_weee_tax_applied_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="weee_tax_row_disposition" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_row_total" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="discount_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="row_total" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="weee_tax_applied_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_discount_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_weee_tax_disposition" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="price_incl_tax" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="weee_tax_disposition" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_price_incl_tax" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="qty" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_cost" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_weee_tax_applied_row_amount" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="price" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="base_row_total_incl_tax" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="row_total_incl_tax" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="product_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="order_item_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="additional_data" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="description" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="weee_tax_applied" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="sku" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="name" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="hidden_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="base_hidden_tax_amount" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderCreditmemoItemEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderCreditmemoItemEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderCreditmemoCommentEntity">
                <xsd:sequence>
                    <xsd:element name="parent_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="created_at" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="comment" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="is_customer_notified" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="comment_id" type="xsd:string" minOccurs="0" maxOccurs="1" />
                    <xsd:element name="is_visible_on_front" type="xsd:string" minOccurs="0" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderCreditmemoCommentEntityArray">
                <xsd:sequence>
                    <xsd:element minOccurs="0" maxOccurs="unbounded" name="complexObjectArray" type="typens:salesOrderCreditmemoCommentEntity" />
                </xsd:sequence>
            </xsd:complexType>
            <xsd:complexType name="salesOrderCreditmemoData">
                <xsd:sequence>
                    <xsd:element name="qtys" type="typens:orderItemIdQtyArray" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="shipping_amount" type="xsd:double" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="adjustment_positive" type="xsd:double" minOccurs="0" maxOccurs="1"/>
                    <xsd:element name="adjustment_negative" type="xsd:double" minOccurs="0" maxOccurs="1"/>
                </xsd:sequence>
            </xsd:complexType>

            <xsd:element name="salesOrderListRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="filters" type="typens:filters" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderListResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:salesOrderListEntityArray" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInfoRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="orderIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInfoResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:salesOrderEntity" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderAddCommentRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="orderIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="status" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="comment" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="notify" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderAddCommentResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderHoldRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="orderIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderHoldResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderUnholdRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="orderIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderUnholdResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCancelRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="orderIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCancelResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentListRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="filters" type="typens:filters" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentListResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:salesOrderShipmentEntityArray" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentInfoRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="shipmentIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentInfoResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:salesOrderShipmentEntity" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentCreateRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="orderIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="itemsQty" type="typens:orderItemIdQtyArray" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="comment" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="email" type="xsd:int" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="includeComment" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentCreateResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentAddTrackRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="shipmentIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="carrier" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="title" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="trackNumber" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentAddTrackResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentRemoveTrackRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="shipmentIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="trackId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentRemoveTrackResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentSendInfoRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="shipmentIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="comment" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentSendInfoResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentAddCommentRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="shipmentIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="comment" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="email" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="includeInEmail" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentAddCommentResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentGetCarriersRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="orderIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderShipmentGetCarriersResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:associativeArray" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceListRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="filters" type="typens:filters" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceListResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:salesOrderInvoiceEntityArray" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceInfoRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="invoiceIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceInfoResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:salesOrderInvoiceEntity" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceCreateRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="invoiceIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="itemsQty" type="typens:orderItemIdQtyArray" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="comment" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="email" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="includeComment" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceCreateResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceAddCommentRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="invoiceIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="comment" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="email" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="includeComment" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceAddCommentResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceCaptureRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="invoiceIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceCaptureResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceVoidRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="invoiceIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceVoidResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceCancelRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="invoiceIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderInvoiceCancelResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoListRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="filters" type="typens:filters" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoListResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:salesOrderCreditmemoEntityArray" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoInfoRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="creditmemoIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoInfoResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="typens:salesOrderCreditmemoEntity" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoCreateRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="orderIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="creditmemoData" type="typens:salesOrderCreditmemoData" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="comment" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="notifyCustomer" type="xsd:int" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="includeComment" type="xsd:int" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="refundToStoreCreditAmount" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoCreateResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoAddCommentRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="creditmemoIncrementId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="comment" type="xsd:string" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="notifyCustomer" type="xsd:int" />
                        <xsd:element minOccurs="0" maxOccurs="1" name="includeComment" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoAddCommentResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:int" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoCancelRequestParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="sessionId" type="xsd:string" />
                        <xsd:element minOccurs="1" maxOccurs="1" name="creditmemoIncrementId" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
            <xsd:element name="salesOrderCreditmemoCancelResponseParam">
                <xsd:complexType>
                    <xsd:sequence>
                        <xsd:element minOccurs="1" maxOccurs="1" name="result" type="xsd:string" />
                    </xsd:sequence>
                </xsd:complexType>
            </xsd:element>
        </xsd:schema>
    </wsdl:types>
    <wsdl:message name="salesOrderListRequest">
        <wsdl:part name="parameters" element="typens:salesOrderListRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderListResponse">
        <wsdl:part name="parameters" element="typens:salesOrderListResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInfoRequest">
        <wsdl:part name="parameters" element="typens:salesOrderInfoRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInfoResponse">
        <wsdl:part name="parameters" element="typens:salesOrderInfoResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderAddCommentRequest">
        <wsdl:part name="parameters" element="typens:salesOrderAddCommentRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderAddCommentResponse">
        <wsdl:part name="parameters" element="typens:salesOrderAddCommentResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderHoldRequest">
        <wsdl:part name="parameters" element="typens:salesOrderHoldRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderHoldResponse">
        <wsdl:part name="parameters" element="typens:salesOrderHoldResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderUnholdRequest">
        <wsdl:part name="parameters" element="typens:salesOrderUnholdRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderUnholdResponse">
        <wsdl:part name="parameters" element="typens:salesOrderUnholdResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCancelRequest">
        <wsdl:part name="parameters" element="typens:salesOrderCancelRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCancelResponse">
        <wsdl:part name="parameters" element="typens:salesOrderCancelResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentListRequest">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentListRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentListResponse">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentListResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentInfoRequest">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentInfoRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentInfoResponse">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentInfoResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentCreateRequest">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentCreateRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentCreateResponse">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentCreateResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentAddCommentRequest">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentAddCommentRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentAddCommentResponse">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentAddCommentResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentAddTrackRequest">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentAddTrackRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentAddTrackResponse">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentAddTrackResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentRemoveTrackRequest">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentRemoveTrackRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentRemoveTrackResponse">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentRemoveTrackResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentSendInfoRequest">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentSendInfoRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentSendInfoResponse">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentSendInfoResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentGetCarriersRequest">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentGetCarriersRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderShipmentGetCarriersResponse">
        <wsdl:part name="parameters" element="typens:salesOrderShipmentGetCarriersResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceListRequest">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceListRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceListResponse">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceListResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceInfoRequest">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceInfoRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceInfoResponse">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceInfoResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceCreateRequest">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceCreateRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceCreateResponse">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceCreateResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceAddCommentRequest">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceAddCommentRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceAddCommentResponse">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceAddCommentResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceCaptureRequest">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceCaptureRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceCaptureResponse">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceCaptureResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceVoidRequest">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceVoidRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceVoidResponse">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceVoidResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceCancelRequest">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceCancelRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderInvoiceCancelResponse">
        <wsdl:part name="parameters" element="typens:salesOrderInvoiceCancelResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoListRequest">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoListRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoListResponse">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoListResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoInfoRequest">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoInfoRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoInfoResponse">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoInfoResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoCreateRequest">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoCreateRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoCreateResponse">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoCreateResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoAddCommentRequest">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoAddCommentRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoAddCommentResponse">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoAddCommentResponseParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoCancelRequest">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoCancelRequestParam" />
    </wsdl:message>
    <wsdl:message name="salesOrderCreditmemoCancelResponse">
        <wsdl:part name="parameters" element="typens:salesOrderCreditmemoCancelResponseParam" />
    </wsdl:message>
    <wsdl:portType name="{{var wsdl.handler}}PortType">
        <wsdl:operation name="salesOrderList">
            <wsdl:documentation>Retrieve list of orders by filters</wsdl:documentation>
            <wsdl:input message="typens:salesOrderListRequest" />
            <wsdl:output message="typens:salesOrderListResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderInfo">
            <wsdl:documentation>Retrieve order information</wsdl:documentation>
            <wsdl:input message="typens:salesOrderInfoRequest" />
            <wsdl:output message="typens:salesOrderInfoResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderAddComment">
            <wsdl:documentation>Add comment to order</wsdl:documentation>
            <wsdl:input message="typens:salesOrderAddCommentRequest" />
            <wsdl:output message="typens:salesOrderAddCommentResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderHold">
            <wsdl:documentation>Hold order</wsdl:documentation>
            <wsdl:input message="typens:salesOrderHoldRequest" />
            <wsdl:output message="typens:salesOrderHoldResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderUnhold">
            <wsdl:documentation>Unhold order</wsdl:documentation>
            <wsdl:input message="typens:salesOrderUnholdRequest" />
            <wsdl:output message="typens:salesOrderUnholdResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderCancel">
            <wsdl:documentation>Cancel order</wsdl:documentation>
            <wsdl:input message="typens:salesOrderCancelRequest" />
            <wsdl:output message="typens:salesOrderCancelResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentList">
            <wsdl:documentation>Retrieve list of shipments by filters</wsdl:documentation>
            <wsdl:input message="typens:salesOrderShipmentListRequest" />
            <wsdl:output message="typens:salesOrderShipmentListResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentInfo">
            <wsdl:documentation>Retrieve shipment information</wsdl:documentation>
            <wsdl:input message="typens:salesOrderShipmentInfoRequest" />
            <wsdl:output message="typens:salesOrderShipmentInfoResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentCreate">
            <wsdl:documentation>Create new shipment for order</wsdl:documentation>
            <wsdl:input message="typens:salesOrderShipmentCreateRequest" />
            <wsdl:output message="typens:salesOrderShipmentCreateResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentAddComment">
            <wsdl:documentation>Add new comment to shipment</wsdl:documentation>
            <wsdl:input message="typens:salesOrderShipmentAddCommentRequest" />
            <wsdl:output message="typens:salesOrderShipmentAddCommentResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentAddTrack">
            <wsdl:documentation>Add new tracking number</wsdl:documentation>
            <wsdl:input message="typens:salesOrderShipmentAddTrackRequest" />
            <wsdl:output message="typens:salesOrderShipmentAddTrackResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentRemoveTrack">
            <wsdl:documentation>Remove tracking number</wsdl:documentation>
            <wsdl:input message="typens:salesOrderShipmentRemoveTrackRequest" />
            <wsdl:output message="typens:salesOrderShipmentRemoveTrackResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentSendInfo">
            <wsdl:documentation>Send shipment info</wsdl:documentation>
            <wsdl:input message="typens:salesOrderShipmentSendInfoRequest" />
            <wsdl:output message="typens:salesOrderShipmentSendInfoResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentGetCarriers">
            <wsdl:documentation>Retrieve list of allowed carriers for order</wsdl:documentation>
            <wsdl:input message="typens:salesOrderShipmentGetCarriersRequest" />
            <wsdl:output message="typens:salesOrderShipmentGetCarriersResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceList">
            <wsdl:documentation>Retrieve list of invoices by filters</wsdl:documentation>
            <wsdl:input message="typens:salesOrderInvoiceListRequest" />
            <wsdl:output message="typens:salesOrderInvoiceListResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceInfo">
            <wsdl:documentation>Retrieve invoice information</wsdl:documentation>
            <wsdl:input message="typens:salesOrderInvoiceInfoRequest" />
            <wsdl:output message="typens:salesOrderInvoiceInfoResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceCreate">
            <wsdl:documentation>Create new invoice for order</wsdl:documentation>
            <wsdl:input message="typens:salesOrderInvoiceCreateRequest" />
            <wsdl:output message="typens:salesOrderInvoiceCreateResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceAddComment">
            <wsdl:documentation>Add new comment to shipment</wsdl:documentation>
            <wsdl:input message="typens:salesOrderInvoiceAddCommentRequest" />
            <wsdl:output message="typens:salesOrderInvoiceAddCommentResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceCapture">
            <wsdl:documentation>Capture invoice</wsdl:documentation>
            <wsdl:input message="typens:salesOrderInvoiceCaptureRequest" />
            <wsdl:output message="typens:salesOrderInvoiceCaptureResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceVoid">
            <wsdl:documentation>Void invoice</wsdl:documentation>
            <wsdl:input message="typens:salesOrderInvoiceVoidRequest" />
            <wsdl:output message="typens:salesOrderInvoiceVoidResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceCancel">
            <wsdl:documentation>Cancel invoice</wsdl:documentation>
            <wsdl:input message="typens:salesOrderInvoiceCancelRequest" />
            <wsdl:output message="typens:salesOrderInvoiceCancelResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoList">
            <wsdl:documentation>Retrieve list of creditmemos by filters</wsdl:documentation>
            <wsdl:input message="typens:salesOrderCreditmemoListRequest" />
            <wsdl:output message="typens:salesOrderCreditmemoListResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoInfo">
            <wsdl:documentation>Retrieve creditmemo information</wsdl:documentation>
            <wsdl:input message="typens:salesOrderCreditmemoInfoRequest" />
            <wsdl:output message="typens:salesOrderCreditmemoInfoResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoCreate">
            <wsdl:documentation>Create new creditmemo for order</wsdl:documentation>
            <wsdl:input message="typens:salesOrderCreditmemoCreateRequest" />
            <wsdl:output message="typens:salesOrderCreditmemoCreateResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoAddComment">
            <wsdl:documentation>Add new comment to creditmemo</wsdl:documentation>
            <wsdl:input message="typens:salesOrderCreditmemoAddCommentRequest" />
            <wsdl:output message="typens:salesOrderCreditmemoAddCommentResponse" />
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoCancel">
            <wsdl:documentation>Cancel creditmemo</wsdl:documentation>
            <wsdl:input message="typens:salesOrderCreditmemoCancelRequest" />
            <wsdl:output message="typens:salesOrderCreditmemoCancelResponse" />
        </wsdl:operation>
    </wsdl:portType>
    <wsdl:binding name="{{var wsdl.handler}}Binding" type="typens:{{var wsdl.handler}}PortType">
        <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http" />
        <wsdl:operation name="salesOrderList">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderInfo">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderAddComment">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderHold">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderUnhold">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderCancel">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentList">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentInfo">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentCreate">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentAddComment">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentAddTrack">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentRemoveTrack">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentSendInfo">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderShipmentGetCarriers">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceList">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceInfo">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceCreate">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceAddComment">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceCapture">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceVoid">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderInvoiceCancel">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoList">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoInfo">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoCreate">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoAddComment">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
        <wsdl:operation name="salesOrderCreditmemoCancel">
            <soap:operation soapAction="" />
            <wsdl:input>
                <soap:body use="literal" />
            </wsdl:input>
            <wsdl:output>
                <soap:body use="literal" />
            </wsdl:output>
        </wsdl:operation>
    </wsdl:binding>
    <wsdl:service name="{{var wsdl.name}}Service">
        <wsdl:port name="{{var wsdl.handler}}Port" binding="typens:{{var wsdl.handler}}Binding">
            <soap:address location="{{var wsdl.url}}" />
        </wsdl:port>
    </wsdl:service>
</wsdl:definitions>
