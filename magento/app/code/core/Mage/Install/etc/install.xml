<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Install
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <wizard>
        <steps>
            <begin translate="code">
                <code>License Agreement</code>
                <controller>wizard</controller>
                <action>begin</action>
            </begin>
            <locale translate="code">
                <code>Localization</code>
                <controller>wizard</controller>
                <action>locale</action>
            </locale>
            <config translate="code">
                <code>Configuration</code>
                <controller>wizard</controller>
                <action>config</action>
            </config>
            <administrator translate="code">
                <code>Create Admin Account</code>
                <controller>wizard</controller>
                <action>administrator</action>
            </administrator>
            <end translate="code">
                <code>You're All Set!</code>
                <controller>wizard</controller>
                <action>end</action>
            </end>
        </steps>
    </wizard>
    <check>
        <filesystem>
            <writeable>
                <app_etc>
                    <path>/app/etc</path>
                    <existence>1</existence>
                    <recursive>0</recursive>
                </app_etc>
                <var>
                    <path>/var</path>
                    <existence>1</existence>
                    <recursive>1</recursive>
                </var>
                <media>
                    <path>/media</path>
                    <existence>1</existence>
                    <recursive>1</recursive>
                </media>
            </writeable>
        </filesystem>
        <php>
            <extensions>
                <spl/>
                <dom/>
                <simplexml/>
                <mcrypt/>
                <hash/>
                <curl/>
                <iconv/>
                <ctype/>
                <gd/>
                <soap/>
                <mbstring/>
                <mcrypt>
                    <openssl/>
                    <mcrypt/>
                </mcrypt>
            </extensions>
        </php>
    </check>
</config>
