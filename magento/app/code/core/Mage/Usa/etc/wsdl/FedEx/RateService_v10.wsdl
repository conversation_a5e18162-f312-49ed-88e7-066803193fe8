<definitions xmlns="http://schemas.xmlsoap.org/wsdl/" xmlns:ns="http://fedex.com/ws/rate/v10" xmlns:s1="http://schemas.xmlsoap.org/wsdl/soap/" targetNamespace="http://fedex.com/ws/rate/v10" name="RateServiceDefinitions">
  <types>
    <xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema" attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://fedex.com/ws/rate/v10">
      <xs:element name="RateReply" type="ns:RateReply"/>
      <xs:element name="RateRequest" type="ns:RateRequest"/>
      <xs:complexType name="AdditionalLabelsDetail">
        <xs:annotation>
          <xs:documentation>Specifies additional labels to be produced. All required labels for shipments will be produced without the need to request additional labels. These are only available as thermal labels.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:AdditionalLabelsType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The type of additional labels to return.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Count" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The number of this type label to return</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="AdditionalLabelsType">
        <xs:annotation>
          <xs:documentation>Identifies the type of additional labels.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER"/>
          <xs:enumeration value="CONSIGNEE"/>
          <xs:enumeration value="CUSTOMS"/>
          <xs:enumeration value="DESTINATION"/>
          <xs:enumeration value="MANIFEST"/>
          <xs:enumeration value="ORIGIN"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="SHIPPER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Address">
        <xs:annotation>
          <xs:documentation>Descriptive data for a physical location. May be used as an actual physical address (place to which one could go), or as a container of "address parts" which should be handled as a unit (such as a city-state-ZIP combination within the US).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="StreetLines" type="xs:string" minOccurs="0" maxOccurs="2">
            <xs:annotation>
              <xs:documentation>Combination of number, street name, etc. At least one line is required for a valid physical address; empty lines should not be included.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="City" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Name of city, town, etc.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="StateOrProvinceCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifying abbreviation for US state, Canada province, etc. Format and presence of this field will vary, depending on country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PostalCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identification of a region (usually small) for mail/package delivery. Format and presence of this field will vary, depending on country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UrbanizationCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Relevant only to addresses in Puerto Rico.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryCode" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The two-letter code used to identify a country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Residential" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether this address residential (as opposed to commercial).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="B13AFilingOptionType">
        <xs:annotation>
          <xs:documentation>
            Specifies which filing option is being exercised by the customer.
            Required for non-document shipments originating in Canada destined for any country other than Canada, the United States, Puerto Rico or the U.S. Virgin Islands.
          </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FILED_ELECTRONICALLY"/>
          <xs:enumeration value="MANUALLY_ATTACHED"/>
          <xs:enumeration value="NOT_REQUIRED"/>
          <xs:enumeration value="SUMMARY_REPORTING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="BarcodeSymbologyType">
        <xs:annotation>
          <xs:documentation>Identification of the type of barcode (symbology) used on FedEx documents and labels.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CODABAR"/>
          <xs:enumeration value="CODE128"/>
          <xs:enumeration value="CODE128B"/>
          <xs:enumeration value="CODE128C"/>
          <xs:enumeration value="CODE39"/>
          <xs:enumeration value="CODE93"/>
          <xs:enumeration value="I2OF5"/>
          <xs:enumeration value="MANUAL"/>
          <xs:enumeration value="PDF417"/>
          <xs:enumeration value="POSTNET"/>
          <xs:enumeration value="UCC128"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="CarrierCodeType">
        <xs:annotation>
          <xs:documentation>Identification of a FedEx operating company (transportation).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FDXC"/>
          <xs:enumeration value="FDXE"/>
          <xs:enumeration value="FDXG"/>
          <xs:enumeration value="FXCC"/>
          <xs:enumeration value="FXFR"/>
          <xs:enumeration value="FXSP"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CertificateOfOriginDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the Certificate of Origin ( e.g. whether or not to include the instructions, image type, etc ...)</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="DocumentFormat" type="ns:ShippingDocumentFormat" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerImageUsages" type="ns:CustomerImageUsage" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of customer supplied images to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ChargeBasisLevelType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CURRENT_PACKAGE"/>
          <xs:enumeration value="SUM_OF_PACKAGES"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ClearanceBrokerageType">
        <xs:annotation>
          <xs:documentation>Specifies the type of brokerage to be applied to a shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER_INCLUSIVE"/>
          <xs:enumeration value="BROKER_INCLUSIVE_NON_RESIDENT_IMPORTER"/>
          <xs:enumeration value="BROKER_SELECT"/>
          <xs:enumeration value="BROKER_SELECT_NON_RESIDENT_IMPORTER"/>
          <xs:enumeration value="BROKER_UNASSIGNED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ClientDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data for the client submitting a transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="AccountNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The FedEx account number associated with this transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MeterNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>This number is assigned by FedEx and identifies the unique device from which the request is originating</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="IntegratorId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used in transactions which require identification of the Fed Ex Office integrator.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Region" type="ns:ExpressRegionCode" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the region from which the transaction is submitted.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The language to be used for human-readable Notification.localizedMessages in responses to the request containing this ClientDetail object. Different requests from the same client may contain different Localization data. (Contrast with TransactionDetail.localization, which governs data payload language/translation.)</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CodAddTransportationChargeBasisType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COD_SURCHARGE"/>
          <xs:enumeration value="NET_CHARGE"/>
          <xs:enumeration value="NET_FREIGHT"/>
          <xs:enumeration value="TOTAL_CUSTOMER_CHARGE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CodAddTransportationChargesDetail">
        <xs:sequence>
          <xs:element name="RateTypeBasis" type="ns:RateTypeBasisType" minOccurs="0"/>
          <xs:element name="ChargeBasis" type="ns:CodAddTransportationChargeBasisType" minOccurs="0"/>
          <xs:element name="ChargeBasisLevel" type="ns:ChargeBasisLevelType" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CodCollectionType">
        <xs:annotation>
          <xs:documentation>Identifies the type of funds FedEx should collect upon shipment delivery.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ANY"/>
          <xs:enumeration value="CASH"/>
          <xs:enumeration value="GUARANTEED_FUNDS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CodDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data required for a FedEx COD (Collect-On-Delivery) shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="CodCollectionAmount" type="ns:Money" minOccurs="0"/>
          <xs:element name="AddTransportationChargesDetail" type="ns:CodAddTransportationChargesDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the details of the charges are to be added to the COD collect amount.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CollectionType" type="ns:CodCollectionType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the type of funds FedEx should collect upon package delivery</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodRecipient" type="ns:Party" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For Express this is the descriptive data that is used for the recipient of the FedEx Letter containing the COD payment. For Ground this is the descriptive data for the party to receive the payment that prints the COD receipt.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReferenceIndicator" type="ns:CodReturnReferenceIndicatorType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which type of reference information to include on the COD return shipping label.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CodReturnReferenceIndicatorType">
        <xs:annotation>
          <xs:documentation>Indicates which type of reference information to include on the COD return shipping label.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="INVOICE"/>
          <xs:enumeration value="PO"/>
          <xs:enumeration value="REFERENCE"/>
          <xs:enumeration value="TRACKING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CommercialInvoice">
        <xs:annotation>
          <xs:documentation>CommercialInvoice element is required for electronic upload of CI data. It will serve to create/transmit an Electronic Commercial Invoice through the FedEx Systems. Customers are responsible for printing their own Commercial Invoice.If you would likeFedEx to generate a Commercial Invoice and transmit it to Customs. for clearance purposes, you need to specify that in the ShippingDocumentSpecification element. If you would like a copy of the Commercial Invoice that FedEx generated returned to you in reply it needs to be specified in the ETDDetail/RequestedDocumentCopies element. Commercial Invoice support consists of maximum of 99 commodity line items.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Comments" type="xs:string" minOccurs="0" maxOccurs="99">
            <xs:annotation>
              <xs:documentation>Any comments that need to be communicated about this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Any freight charges that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TaxesOrMiscellaneousCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Any taxes or miscellaneous charges(other than Freight charges or Insurance charges) that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TaxesOrMiscellaneousChargeType" type="ns:TaxesOrMiscellaneousChargeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies which kind of charge is being recorded in the preceding field.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackingCosts" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Any packing costs that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HandlingCosts" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Any handling costs that are associated with this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialInstructions" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeclarationStatment" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PaymentTerms" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free-form text.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Purpose" type="ns:PurposeOfShipmentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The reason for the shipment. Note: SOLD is not a valid purpose for a Proforma Invoice.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerInvoiceNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer assigned Invoice number</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OriginatorName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Name of the International Expert that completed the Commercial Invoice different from Sender.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TermsOfSale" type="ns:TermsOfSaleType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Required for dutiable international Express or Ground shipment. This field is not applicable to an international PIB(document) or a non-document which does not require a Commercial Invoice</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CommercialInvoiceDetail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the Commercial Invoice( e.g. image type) Specifies characteristics of a shipping document to be produced.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0"/>
          <xs:element name="CustomerImageUsages" type="ns:CustomerImageUsage" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of a customer supplied image to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CommitDetail">
        <xs:annotation>
          <xs:documentation>Information about the transit time and delivery commitment date and time.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="CommodityName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The Commodity applicable to this commitment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ServiceType" type="ns:ServiceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The FedEx service type applicable to this commitment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AppliedOptions" type="ns:ServiceOptionType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Shows the specific combination of service options combined with the service type that produced this committment in the set returned to the caller.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AppliedSubOptions" type="ns:ServiceSubOptionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Supporting detail for applied options identified in preceding field.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CommitTimestamp" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>THe delivery commitment date/time. Express Only.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DayOfWeek" type="ns:DayOfWeekType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The delivery commitment day of the week.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransitTime" type="ns:TransitTimeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The number of transit days; applies to Ground and LTL Freight; indicates minimum transit time for SmartPost.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MaximumTransitTime" type="ns:TransitTimeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Maximum number of transit days, for SmartPost shipments.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationServiceArea" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The service area code for the destination of this shipment. Express only.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BrokerAddress" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The address of the broker to be used for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BrokerLocationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The FedEx location identifier for the broker.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BrokerCommitTimestamp" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The delivery commitment date/time the shipment will arrive at the border.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BrokerCommitDayOfWeek" type="ns:DayOfWeekType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The delivery commitment day of the week the shipment will arrive at the border.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BrokerToDestinationDays" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The number of days it will take for the shipment to make it from broker to destination</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProofOfDeliveryDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The delivery commitment date for shipment served by GSP (Global Service Provider)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProofOfDeliveryDayOfWeek" type="ns:DayOfWeekType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The delivery commitment day of the week for the shipment served by GSP (Global Service Provider)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CommitMessages" type="ns:Notification" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Messages concerning the ability to provide an accurate delivery commitment on an International commit quote. These could be messages providing information about why a commitment could not be returned or a successful message such as "REQUEST COMPLETED"</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryMessages" type="xs:string" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Messages concerning the delivery commitment on an International commit quote such as "0:00 A.M. IF NO CUSTOMS DELAY"</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DelayDetails" type="ns:DelayDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Information about why a shipment delivery is delayed and at what level (country/service etc.).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DocumentContent" type="ns:InternationalDocumentContentType" minOccurs="0"/>
          <xs:element name="RequiredDocuments" type="ns:RequiredShippingDocumentType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Required documentation for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightCommitDetail" type="ns:FreightCommitDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Freight origin and destination city center information and total distance between origin and destination city centers.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CommitmentDelayType">
        <xs:annotation>
          <xs:documentation>The type of delay this shipment will encounter.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="HOLIDAY"/>
          <xs:enumeration value="NON_WORKDAY"/>
          <xs:enumeration value="NO_CITY_DELIVERY"/>
          <xs:enumeration value="NO_HOLD_AT_LOCATION"/>
          <xs:enumeration value="NO_LOCATION_DELIVERY"/>
          <xs:enumeration value="NO_SERVICE_AREA_DELIVERY"/>
          <xs:enumeration value="NO_SERVICE_AREA_SPECIAL_SERVICE_DELIVERY"/>
          <xs:enumeration value="NO_SPECIAL_SERVICE_DELIVERY"/>
          <xs:enumeration value="NO_ZIP_DELIVERY"/>
          <xs:enumeration value="WEEKEND"/>
          <xs:enumeration value="WEEKEND_SPECIAL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Commodity">
        <xs:annotation>
          <xs:documentation>
            For international multiple piece shipments, commodity information must be passed in the Master and on each child transaction.
            If this shipment cotains more than four commodities line items, the four highest valued should be included in the first 4 occurances for this request.
          </xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Name" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>total number of pieces of this commodity</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NumberOfPieces" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>total number of pieces of this commodity</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Complete and accurate description of this commodity.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>450</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryOfManufacture" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Country code where commodity contents were produced or manufactured in their final form.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>2</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="HarmonizedCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Unique alpha/numeric representing commodity item.
                At least one occurrence is required for US Export shipments if the Customs Value is greater than $2500 or if a valid US Export license is required.
              </xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>14</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Weight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total weight of this commodity. 1 explicit decimal position. Max length 11 including decimal.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Quantity" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of units of a commodity in total number of pieces for this line item. Max length is 9</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="QuantityUnits" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Unit of measure used to express the quantity of this commodity line item.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>3</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="AdditionalMeasures" type="ns:Measure" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Contains only additional quantitative information other than weight and quantity to calculate duties and taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UnitPrice" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Value of each unit in Quantity. Six explicit decimal positions, Max length 18 including decimal.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomsValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Total customs value for this line item.
                It should equal the commodity unit quantity times commodity unit value.
                Six explicit decimal positions, max length 18 including decimal.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExciseConditions" type="ns:EdtExciseCondition" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Defines additional characteristic of commodity used to calculate duties and taxes</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportLicenseNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Applicable to US export shipping only.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>12</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportLicenseExpirationDate" type="xs:date" minOccurs="0"/>
          <xs:element name="CIMarksAndNumbers" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                An identifying mark or number used on the packaging of a shipment to help customers identify a particular shipment.
              </xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>15</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="NaftaDetail" type="ns:NaftaCommodityDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>All data required for this commodity in NAFTA Certificate of Origin.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ConfigurableLabelReferenceEntry">
        <xs:annotation>
          <xs:documentation>Defines additional data to print in the Configurable portion of the label, this allows you to print the same type information on the label that can also be printed on the doc tab.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ZoneNumber" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>1 of 12 possible zones to  position data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Header" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The identifiying text for the data in this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DataField" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A reference to a field in either the request or reply to print in this zone following the header.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LiteralValue" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A literal value to print after the header in this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Contact">
        <xs:annotation>
          <xs:documentation>The descriptive data for a point-of-contact person.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ContactId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Client provided identifier corresponding to this contact information.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PersonName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the contact person's name.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Title" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the contact person's title.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CompanyName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the company this contact is associated with.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhoneNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the phone number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhoneExtension" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the phone extension associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PagerNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the pager number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FaxNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the fax number associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailAddress" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the email address associated with this contact.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContactAndAddress">
        <xs:sequence>
          <xs:element name="Contact" type="ns:Contact" minOccurs="0"/>
          <xs:element name="Address" type="ns:Address" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ContentRecord">
        <xs:sequence>
          <xs:element name="PartNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="ItemNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="ReceivedQuantity" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CurrencyExchangeRate">
        <xs:annotation>
          <xs:documentation>Specifies the currency exchange performed on financial amounts for this rate.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FromCurrency" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The currency code for the original (converted FROM) currency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="IntoCurrency" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The currency code for the final (converted INTO) currency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rate" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Multiplier used to convert fromCurrency units to intoCurrency units.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomDeliveryWindowDetail">
        <xs:sequence>
          <xs:element name="Type" type="ns:CustomDeliveryWindowType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the type of custom delivery being requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestTime" type="xs:time" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Time by which delivery is requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestRange" type="ns:DateRange" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Range of dates for custom delivery request; only used if type is BETWEEN.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Date for custom delivery request; only used for types of ON, BETWEEN, or AFTER.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomDeliveryWindowType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="AFTER"/>
          <xs:enumeration value="BEFORE"/>
          <xs:enumeration value="BETWEEN"/>
          <xs:enumeration value="ON"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomDocumentDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce a custom-specified document, either at shipment or package level.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Common information controlling document production.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelPrintingOrientation" type="ns:LabelPrintingOrientationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Applicable only to documents produced on thermal printers with roll stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelRotation" type="ns:LabelRotationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Applicable only to documents produced on thermal printers with roll stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecificationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the formatting specification used to construct this custom document.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelBarcodeEntry">
        <xs:annotation>
          <xs:documentation>Constructed string, based on format and zero or more data fields, printed in specified barcode symbology.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Position" type="ns:CustomLabelPosition" minOccurs="0"/>
          <xs:element name="Format" type="xs:string" minOccurs="0"/>
          <xs:element name="DataFields" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="BarHeight" type="xs:int" minOccurs="0"/>
          <xs:element name="ThinBarWidth" type="xs:int" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Width of thinnest bar/space element in the barcode.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BarcodeSymbology" type="ns:BarcodeSymbologyType" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelBoxEntry">
        <xs:annotation>
          <xs:documentation>Solid (filled) rectangular area on label.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="TopLeftCorner" type="ns:CustomLabelPosition" minOccurs="1"/>
          <xs:element name="BottomRightCorner" type="ns:CustomLabelPosition" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomLabelCoordinateUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="MILS"/>
          <xs:enumeration value="PIXELS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomLabelDetail">
        <xs:sequence>
          <xs:element name="CoordinateUnits" type="ns:CustomLabelCoordinateUnits" minOccurs="0"/>
          <xs:element name="TextEntries" type="ns:CustomLabelTextEntry" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="GraphicEntries" type="ns:CustomLabelGraphicEntry" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="BoxEntries" type="ns:CustomLabelBoxEntry" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="BarcodeEntries" type="ns:CustomLabelBarcodeEntry" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelGraphicEntry">
        <xs:annotation>
          <xs:documentation>Image to be included from printer's memory, or from a local file for offline clients.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Position" type="ns:CustomLabelPosition" minOccurs="0"/>
          <xs:element name="PrinterGraphicId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Printer-specific index of graphic image to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FileGraphicFullName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Fully-qualified path and file name for graphic image to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelPosition">
        <xs:sequence>
          <xs:element name="X" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Horizontal position, relative to left edge of custom area.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Y" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Vertical position, relative to top edge of custom area.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomLabelTextEntry">
        <xs:annotation>
          <xs:documentation>Constructed string, based on format and zero or more data fields, printed in specified printer font (for thermal labels) or generic font/size (for plain paper labels).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Position" type="ns:CustomLabelPosition" minOccurs="0"/>
          <xs:element name="Format" type="xs:string" minOccurs="0"/>
          <xs:element name="DataFields" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="ThermalFontId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Printer-specific font name for use with thermal printer labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FontName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Generic font name for use with plain paper labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FontSize" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Generic font size for use with plain paper labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomerImageUsage">
        <xs:sequence>
          <xs:element name="Type" type="ns:CustomerImageUsageType" minOccurs="0"/>
          <xs:element name="Id" type="ns:ImageId" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomerImageUsageType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LETTER_HEAD"/>
          <xs:enumeration value="SIGNATURE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomerReference">
        <xs:annotation>
          <xs:documentation>Reference information to be associated with this package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="CustomerReferenceType" type="ns:CustomerReferenceType" minOccurs="1"/>
          <xs:element name="Value" type="xs:string" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="CustomerReferenceType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BILL_OF_LADING"/>
          <xs:enumeration value="CUSTOMER_REFERENCE"/>
          <xs:enumeration value="DEPARTMENT_NUMBER"/>
          <xs:enumeration value="ELECTRONIC_PRODUCT_CODE"/>
          <xs:enumeration value="INTRACOUNTRY_REGULATORY_REFERENCE"/>
          <xs:enumeration value="INVOICE_NUMBER"/>
          <xs:enumeration value="PACKING_SLIP_NUMBER"/>
          <xs:enumeration value="P_O_NUMBER"/>
          <xs:enumeration value="SHIPMENT_INTEGRITY"/>
          <xs:enumeration value="STORE_NUMBER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="CustomerSpecifiedLabelDetail">
        <xs:annotation>
          <xs:documentation>Allows customer-specified control of label content.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="DocTabContent" type="ns:DocTabContent" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If omitted, no doc tab will be produced (i.e. default = former NONE type).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomContent" type="ns:CustomLabelDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Defines any custom content to print on the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ConfigurableReferenceEntries" type="ns:ConfigurableLabelReferenceEntry" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Defines additional data to print in the Configurable portion of the label, this allows you to print the same type information on the label that can also be printed on the doc tab.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MaskedData" type="ns:LabelMaskableDataType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Controls which data/sections will be suppressed.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SecondaryBarcode" type="ns:SecondaryBarcodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For customers producing their own Ground labels, this field specifies which secondary barcode will be printed on the label; so that the primary barcode produced by FedEx has the corect SCNC.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TermsAndConditionsLocalization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The language to use when printing the terms and conditions on the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AdditionalLabels" type="ns:AdditionalLabelsDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Controls the number of additional copies of supplemental labels.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AirWaybillSuppressionCount" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This value reduces the default quantity of destination/consignee air waybill labels. A value of zero indicates no change to default. A minimum of one copy will always be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="CustomsClearanceDetail">
        <xs:sequence>
          <xs:element name="Broker" type="ns:Party" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Descriptive data identifying the Broker responsible for the shipmet.
                Required if BROKER_SELECT_OPTION is requested in Special Services.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClearanceBrokerage" type="ns:ClearanceBrokerageType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Interacts both with properties of the shipment and contractual relationship with the shipper.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ImporterOfRecord" type="ns:Party" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Applicable only for Commercial Invoice. If the consignee and importer are not the same, the Following importer fields are required.
                Importer/Contact/PersonName
                Importer/Contact/CompanyName
                Importer/Contact/PhoneNumber
                Importer/Address/StreetLine[0]
                Importer/Address/City
                Importer/Address/StateOrProvinceCode - if Importer Country Code is US or CA
                Importer/Address/PostalCode - if Importer Country Code is US or CA
                Importer/Address/CountryCode
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RecipientCustomsId" type="ns:RecipientCustomsId" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how the recipient is identified for customs purposes; the requirements on this information vary with destination country.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DutiesPayment" type="ns:Payment" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates how payment of duties for the shipment will be made.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DocumentContent" type="ns:InternationalDocumentContentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether this shipment contains documents only or non-documents.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomsValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total customs value for the shipment. This total will rrepresent th esum of the values of all commodities, and may include freight, miscellaneous, and insurance charges. Must contain 2 explicit decimal positions with a max length of 17 including the decimal. For Express International MPS, the Total Customs Value is in the master transaction and all child transactions</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightOnValue" type="ns:FreightOnValueType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies responsibilities with respect to loss, damage, etc.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="InsuranceCharges" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Documents amount paid to third party for coverage of shipment content.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PartiesToTransactionAreRelated" type="xs:boolean" minOccurs="0"/>
          <xs:element name="CommercialInvoice" type="ns:CommercialInvoice" minOccurs="0">
            <xs:annotation>
              <xs:documentation>CommercialInvoice element is required for electronic upload of CI data. It will serve to create/transmit an Electronic Commercial Invoice through FedEx System. Customers are responsible for printing their own Commercial Invoice. Commercial Invoice support consists of a maximum of 20 commodity line items.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Commodities" type="ns:Commodity" minOccurs="0" maxOccurs="99">
            <xs:annotation>
              <xs:documentation>
                For international multiple piece shipments, commodity information must be passed in the Master and on each child transaction.
                If this shipment cotains more than four commodities line items, the four highest valued should be included in the first 4 occurances for this request.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportDetail" type="ns:ExportDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Country specific details of an International shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RegulatoryControls" type="ns:RegulatoryControlType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>FOOD_OR_PERISHABLE is required by FDA/BTA; must be true for food/perishable items coming to US or PR from non-US/non-PR origin.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DangerousGoodsAccessibilityType">
        <xs:annotation>
          <xs:documentation>Identifies whether or not the products being shipped are required to be accessible during delivery.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCESSIBLE"/>
          <xs:enumeration value="INACCESSIBLE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DangerousGoodsDetail">
        <xs:annotation>
          <xs:documentation>The descriptive data required for a FedEx shipment containing dangerous goods (hazardous materials).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Accessibility" type="ns:DangerousGoodsAccessibilityType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies whether or not the products being shipped are required to be accessible during delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CargoAircraftOnly" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Shipment is packaged/documented for movement ONLY on cargo aircraft.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Options" type="ns:HazardousCommodityOptionType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates which kinds of hazardous content are in the current package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HazardousCommodities" type="ns:HazardousCommodityContent" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Documents the kinds and quantities of all hazardous commodities in the current package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Packaging" type="ns:HazardousCommodityPackagingDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Description of the packaging of this commodity, suitable for use on OP-900 and OP-950 forms.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EmergencyContactNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Telephone number to use for contact in the event of an emergency.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Offeror" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Offeror's name or contract number, per DOT regulation.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DateRange">
        <xs:sequence>
          <xs:element name="Begins" type="xs:date" minOccurs="1"/>
          <xs:element name="Ends" type="xs:date" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DayOfWeekType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FRI"/>
          <xs:enumeration value="MON"/>
          <xs:enumeration value="SAT"/>
          <xs:enumeration value="SUN"/>
          <xs:enumeration value="THU"/>
          <xs:enumeration value="TUE"/>
          <xs:enumeration value="WED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DelayDetail">
        <xs:annotation>
          <xs:documentation>Information about why a shipment delivery is delayed and at what level( country/service etc.).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Date" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The date of the delay</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DayOfWeek" type="ns:DayOfWeekType" minOccurs="0"/>
          <xs:element name="Level" type="ns:DelayLevelType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The attribute of the shipment that caused the delay(e.g. Country, City, LocationId, Zip, service area, special handling )</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Point" type="ns:DelayPointType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The point where the delay is occurring (e.g. Origin, Destination, Broker location)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Type" type="ns:CommitmentDelayType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The reason for the delay (e.g. holiday, weekend, etc.).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The name of the holiday in that country that is causing the delay.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DelayLevelType">
        <xs:annotation>
          <xs:documentation>The attribute of the shipment that caused the delay(e.g. Country, City, LocationId, Zip, service area, special handling )</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CITY"/>
          <xs:enumeration value="COUNTRY"/>
          <xs:enumeration value="LOCATION"/>
          <xs:enumeration value="POSTAL_CODE"/>
          <xs:enumeration value="SERVICE_AREA"/>
          <xs:enumeration value="SERVICE_AREA_SPECIAL_SERVICE"/>
          <xs:enumeration value="SPECIAL_SERVICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="DelayPointType">
        <xs:annotation>
          <xs:documentation>The point where the delay is occurring ( e.g. Origin, Destination, Broker location).</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER"/>
          <xs:enumeration value="DESTINATION"/>
          <xs:enumeration value="ORIGIN"/>
          <xs:enumeration value="ORIGIN_DESTINATION_PAIR"/>
          <xs:enumeration value="PROOF_OF_DELIVERY_POINT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DestinationControlDetail">
        <xs:annotation>
          <xs:documentation>Data required to complete the Destionation Control Statement for US exports.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="StatementTypes" type="ns:DestinationControlStatementType" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="DestinationCountries" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Comma-separated list of up to four country codes, required for DEPARTMENT_OF_STATE statement.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EndUser" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Name of end user, required for DEPARTMENT_OF_STATE statement.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DestinationControlStatementType">
        <xs:annotation>
          <xs:documentation>Used to indicate whether the Destination Control Statement is of type Department of Commerce, Department of State or both.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DEPARTMENT_OF_COMMERCE"/>
          <xs:enumeration value="DEPARTMENT_OF_STATE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Dimensions">
        <xs:annotation>
          <xs:documentation>The dimensions of this package and the unit type used for the measurements.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Length" type="xs:nonNegativeInteger" minOccurs="1"/>
          <xs:element name="Width" type="xs:nonNegativeInteger" minOccurs="1"/>
          <xs:element name="Height" type="xs:nonNegativeInteger" minOccurs="1"/>
          <xs:element name="Units" type="ns:LinearUnits" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Distance">
        <xs:annotation>
          <xs:documentation>Driving or other transportation distances, distinct from dimension measurements.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Value" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the distance quantity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Units" type="ns:DistanceUnits" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the unit of measure for the distance value.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DistanceUnits">
        <xs:restriction base="xs:string">
          <xs:enumeration value="KM"/>
          <xs:enumeration value="MI"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocTabContent">
        <xs:sequence>
          <xs:element name="DocTabContentType" type="ns:DocTabContentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The DocTabContentType options available.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Zone001" type="ns:DocTabContentZone001" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The DocTabContentType should be set to ZONE001 to specify additional Zone details.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Barcoded" type="ns:DocTabContentBarcoded" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The DocTabContentType should be set to BARCODED to specify additional BarCoded details.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="DocTabContentBarcoded">
        <xs:sequence>
          <xs:element name="Symbology" type="ns:BarcodeSymbologyType" minOccurs="0"/>
          <xs:element name="Specification" type="ns:DocTabZoneSpecification" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DocTabContentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BARCODED"/>
          <xs:enumeration value="MINIMUM"/>
          <xs:enumeration value="STANDARD"/>
          <xs:enumeration value="ZONE001"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocTabContentZone001">
        <xs:sequence>
          <xs:element name="DocTabZoneSpecifications" type="ns:DocTabZoneSpecification" minOccurs="1" maxOccurs="12"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DocTabZoneJustificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="LEFT"/>
          <xs:enumeration value="RIGHT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="DocTabZoneSpecification">
        <xs:sequence>
          <xs:element name="ZoneNumber" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Zone number can be between 1 and 12.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Header" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Header value on this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DataField" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Reference path to the element in the request/reply whose value should be printed on this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LiteralValue" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free form-text to be printed in this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Justification" type="ns:DocTabZoneJustificationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Justification for the text printed on this zone.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="DropoffType">
        <xs:annotation>
          <xs:documentation>Identifies the method by which the package is to be tendered to FedEx. This element does not dispatch a courier for package pickup.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BUSINESS_SERVICE_CENTER"/>
          <xs:enumeration value="DROP_BOX"/>
          <xs:enumeration value="REGULAR_PICKUP"/>
          <xs:enumeration value="REQUEST_COURIER"/>
          <xs:enumeration value="STATION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EMailLabelDetail">
        <xs:annotation>
          <xs:documentation>Specific information about the delivery of the email and options for the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="NotificationEMailAddress" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Email address to send the URL to.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NotificationMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A message to be inserted into the email.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="EMailNotificationDetail">
        <xs:annotation>
          <xs:documentation>Information describing email notifications that will be sent in relation to events that occur during package movement</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PersonalMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A message that will be included in the email notifications</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Recipients" type="ns:EMailNotificationRecipient" minOccurs="0" maxOccurs="6">
            <xs:annotation>
              <xs:documentation>Information describing the destination of the email, format of the email and events to be notified on</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EMailNotificationEventType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ON_DELIVERY"/>
          <xs:enumeration value="ON_EXCEPTION"/>
          <xs:enumeration value="ON_SHIPMENT"/>
          <xs:enumeration value="ON_TENDER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="EMailNotificationFormatType">
        <xs:annotation>
          <xs:documentation>The format of the email</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="HTML"/>
          <xs:enumeration value="TEXT"/>
          <xs:enumeration value="WIRELESS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EMailNotificationRecipient">
        <xs:annotation>
          <xs:documentation>The descriptive data for a FedEx email notification recipient.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="EMailNotificationRecipientType" type="ns:EMailNotificationRecipientType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the relationship this email recipient has to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailAddress" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The email address to send the notification to</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NotificationEventsRequested" type="ns:EMailNotificationEventType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The types of email notifications being requested for this recipient.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Format" type="ns:EMailNotificationFormatType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The format of the email notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The language/locale to be used in this email notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EMailNotificationRecipientType">
        <xs:annotation>
          <xs:documentation>Identifies the set of valid email notification recipient types. For SHIPPER, RECIPIENT and BROKER the email address asssociated with their definitions will be used, any email address sent with the email notification for these three email notification recipient types will be ignored.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="SHIPPER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EdtCommodityTax">
        <xs:sequence>
          <xs:element name="HarmonizedCode" type="xs:string" minOccurs="0"/>
          <xs:element name="Taxes" type="ns:EdtTaxDetail" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="EdtExciseCondition">
        <xs:sequence>
          <xs:element name="Category" type="xs:string" minOccurs="0"/>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-declared value, with data type and legal values depending on excise condition, used in defining the taxable value of the item.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EdtRequestType">
        <xs:annotation>
          <xs:documentation>Specifies the types of Estimated Duties and Taxes to be included in a rate quotation for an international shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ALL"/>
          <xs:enumeration value="NONE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EdtTaxDetail">
        <xs:sequence>
          <xs:element name="TaxType" type="ns:EdtTaxType" minOccurs="0"/>
          <xs:element name="EffectiveDate" type="xs:date" minOccurs="0"/>
          <xs:element name="Name" type="xs:string" minOccurs="0"/>
          <xs:element name="TaxableValue" type="ns:Money" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Formula" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="EdtTaxType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDITIONAL_TAXES"/>
          <xs:enumeration value="CONSULAR_INVOICE_FEE"/>
          <xs:enumeration value="CUSTOMS_SURCHARGES"/>
          <xs:enumeration value="DUTY"/>
          <xs:enumeration value="EXCISE_TAX"/>
          <xs:enumeration value="FOREIGN_EXCHANGE_TAX"/>
          <xs:enumeration value="GENERAL_SALES_TAX"/>
          <xs:enumeration value="IMPORT_LICENSE_FEE"/>
          <xs:enumeration value="INTERNAL_ADDITIONAL_TAXES"/>
          <xs:enumeration value="INTERNAL_SENSITIVE_PRODUCTS_TAX"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="SENSITIVE_PRODUCTS_TAX"/>
          <xs:enumeration value="STAMP_TAX"/>
          <xs:enumeration value="STATISTICAL_TAX"/>
          <xs:enumeration value="TRANSPORT_FACILITIES_TAX"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="EtdDetail">
        <xs:annotation>
          <xs:documentation>Electronic Trade document references used with the ETD special service.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RequestedDocumentCopies" type="ns:RequestedShippingDocumentType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates the types of shipping documents produced for the shipper by FedEx (see ShippingDocumentSpecification) which should be copied back to the shipper in the shipment result data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Documents" type="ns:UploadDocumentDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DocumentReferences" type="ns:UploadDocumentReferenceDetail" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExportDetail">
        <xs:annotation>
          <xs:documentation>Country specific details of an International shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="B13AFilingOption" type="ns:B13AFilingOptionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Specifies which filing option is being exercised by the customer.
                Required for non-document shipments originating in Canada destined for any country other than Canada, the United States, Puerto Rico or the U.S. Virgin Islands.
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExportComplianceStatement" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>General field for exporting-country-specific export data (e.g. B13A for CA, FTSR Exemption or AES Citation for US).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PermitNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This field is applicable only to Canada export non-document shipments of any value to any destination. No special characters allowed. </xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>10</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationControlDetail" type="ns:DestinationControlDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Department of Commerce/Department of State information about this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExpressFreightDetail">
        <xs:annotation>
          <xs:documentation>Details specific to an Express freight shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PackingListEnclosed" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether or nor a packing list is enclosed.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShippersLoadAndCount" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                Total shipment pieces.
                ie. 3 boxes and 3 pallets of 100 pieces each = Shippers Load and Count of 303.
                Applicable to International Priority Freight and International Economy Freight.
                Values must be in the range of 1 - 99999
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BookingConfirmationNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Required for International Freight shipping. Values must be 8- 12 characters in length.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>12</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReferenceLabelRequested" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BeforeDeliveryContact" type="ns:ExpressFreightDetailContact" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="UndeliverableContact" type="ns:ExpressFreightDetailContact" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Currently not supported.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ExpressFreightDetailContact">
        <xs:annotation>
          <xs:documentation>Currently not supported. Delivery contact information for an Express freight shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Name" type="xs:string" minOccurs="0"/>
          <xs:element name="Phone" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ExpressRegionCode">
        <xs:annotation>
          <xs:documentation>Indicates a FedEx Express operating region.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="APAC"/>
          <xs:enumeration value="CA"/>
          <xs:enumeration value="EMEA"/>
          <xs:enumeration value="LAC"/>
          <xs:enumeration value="US"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FedExLocationType">
        <xs:annotation>
          <xs:documentation>Identifies a kind of FedEx facility.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_EXPRESS_STATION"/>
          <xs:enumeration value="FEDEX_GROUND_TERMINAL"/>
          <xs:enumeration value="FEDEX_OFFICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FlatbedTrailerDetail">
        <xs:annotation>
          <xs:documentation>Specifies the optional features/characteristics requested for a Freight shipment utilizing a flatbed trailer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Options" type="ns:FlatbedTrailerOption" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FlatbedTrailerOption">
        <xs:restriction base="xs:string">
          <xs:enumeration value="OVER_DIMENSION"/>
          <xs:enumeration value="TARP"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightAccountPaymentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COLLECT"/>
          <xs:enumeration value="PREPAID"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightBaseCharge">
        <xs:annotation>
          <xs:documentation>Individual charge which contributes to the total base charge for the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FreightClass" type="ns:FreightClassType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Freight class for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RatedAsClass" type="ns:FreightClassType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Effective freight class used for rating this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NmfcCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>NMFC Code for commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-provided description for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Weight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Weight for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ChargeRate" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Rate or factor applied to this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ChargeBasis" type="ns:FreightChargeBasisType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the manner in which the chargeRate for this line item was applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExtendedAmount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The net or extended charge for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightBaseChargeCalculationType">
        <xs:annotation>
          <xs:documentation>Specifies the way in which base charges for a Freight shipment or shipment leg are calculated.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LINE_ITEMS"/>
          <xs:enumeration value="UNIT_PRICING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightChargeBasisType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CWT"/>
          <xs:enumeration value="FLAT"/>
          <xs:enumeration value="MINIMUM"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightClassType">
        <xs:annotation>
          <xs:documentation>These values represent the industry-standard freight classes used for FedEx Freight and FedEx National Freight shipment description. (Note: The alphabetic prefixes are required to distinguish these values from decimal numbers on some client platforms.)</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CLASS_050"/>
          <xs:enumeration value="CLASS_055"/>
          <xs:enumeration value="CLASS_060"/>
          <xs:enumeration value="CLASS_065"/>
          <xs:enumeration value="CLASS_070"/>
          <xs:enumeration value="CLASS_077_5"/>
          <xs:enumeration value="CLASS_085"/>
          <xs:enumeration value="CLASS_092_5"/>
          <xs:enumeration value="CLASS_100"/>
          <xs:enumeration value="CLASS_110"/>
          <xs:enumeration value="CLASS_125"/>
          <xs:enumeration value="CLASS_150"/>
          <xs:enumeration value="CLASS_175"/>
          <xs:enumeration value="CLASS_200"/>
          <xs:enumeration value="CLASS_250"/>
          <xs:enumeration value="CLASS_300"/>
          <xs:enumeration value="CLASS_400"/>
          <xs:enumeration value="CLASS_500"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightCommitDetail">
        <xs:annotation>
          <xs:documentation>Information about the Freight Service Centers associated with this shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="OriginDetail" type="ns:FreightServiceCenterDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Information about the origin Freight Service Center.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationDetail" type="ns:FreightServiceCenterDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Information about the destination Freight Service Center.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalDistance" type="ns:Distance" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The distance between the origin and destination FreightService Centers</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightGuaranteeDetail">
        <xs:sequence>
          <xs:element name="Type" type="ns:FreightGuaranteeType" minOccurs="0"/>
          <xs:element name="Date" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Date for all Freight guarantee types.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightGuaranteeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="GUARANTEED_DATE"/>
          <xs:enumeration value="GUARANTEED_MORNING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="FreightOnValueType">
        <xs:annotation>
          <xs:documentation>Identifies responsibilities with respect to loss, damage, etc.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CARRIER_RISK"/>
          <xs:enumeration value="OWN_RISK"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightRateDetail">
        <xs:annotation>
          <xs:documentation>Rate data specific to FedEx Freight or FedEx National Freight services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="QuoteNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A unique identifier for a specific rate quotation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BaseChargeCalculation" type="ns:FreightBaseChargeCalculationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how total base charge is determined.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BaseCharges" type="ns:FreightBaseCharge" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Freight charges which accumulate to the total base charge for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Notations" type="ns:FreightRateNotation" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Human-readable descriptions of additional information on this shipment rating.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightRateNotation">
        <xs:annotation>
          <xs:documentation>Additional non-monetary data returned with Freight rates.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Code" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Unique identifier for notation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Human-readable explanation of notation.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightServiceCenterDetail">
        <xs:annotation>
          <xs:documentation>This class describes the relationship between a customer-specified address and the FedEx Freight / FedEx National Freight Service Center that supports that address.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="InterlineCarrierCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Freight Industry standard non-FedEx carrier identification</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="InterlineCarrierName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The name of the Interline carrier.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AdditionalDays" type="xs:int" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Additional time it might take at the origin or destination to pickup or deliver the freight. This is usually due to the remoteness of the location. This time is included in the total transit time.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocalService" type="ns:ServiceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Service branding which may be used for local pickup or delivery, distinct from service used for line-haul of customer's shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocalDistance" type="ns:Distance" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Distance between customer address (pickup or delivery) and the supporting Freight / National Freight service center.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocalDuration" type="xs:duration" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Time to travel between customer address (pickup or delivery) and the supporting Freight / National Freight service center.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocalServiceScheduling" type="ns:FreightServiceSchedulingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies when/how the customer can arrange for pickup or delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LimitedServiceDays" type="ns:DayOfWeekType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies days of operation if localServiceScheduling is LIMITED.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GatewayLocationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Freight service center that is a gateway on the border of Canada or Mexico.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Location" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Alphabetical code identifying a Freight Service Center</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ContactAndAddress" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Freight service center Contact and Address</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightServiceSchedulingType">
        <xs:annotation>
          <xs:documentation>Specifies the type of service scheduling offered from a Freight or National Freight Service Center to a customer-supplied address.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LIMITED"/>
          <xs:enumeration value="STANDARD"/>
          <xs:enumeration value="WILL_CALL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightShipmentDetail">
        <xs:annotation>
          <xs:documentation>Data applicable to shipments using FEDEX_FREIGHT and FEDEX_NATIONAL_FREIGHT services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FedExFreightAccountNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Account number used with FEDEX_FREIGHT service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FedExFreightBillingContactAndAddress" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used for validating FedEx Freight account number and (optionally) identifying third party payment on the bill of lading.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FedExNationalFreightAccountNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Account number used with FEDEX_NATIONAL_FREIGHT service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FedExNationalFreightBillingContactAndAddress" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used for validating FedEx National Freight account number and (optionally) identifying third party payment on the bill of lading.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Role" type="ns:FreightShipmentRoleType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the role of the party submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PaymentType" type="ns:FreightAccountPaymentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Designates which of the requester's tariffs will be used for rating.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeclaredValuePerUnit" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the declared value for the shipment</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeclaredValueUnits" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the declared value units corresponding to the above defined declared value</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LiabilityCoverageDetail" type="ns:LiabilityCoverageDetail" minOccurs="0"/>
          <xs:element name="Coupons" type="xs:string" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Identifiers for promotional discounts offered to customers.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalHandlingUnits" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total number of individual handling units in the entire shipment (for unit pricing).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDiscountPercent" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Estimated discount rate provided by client for unsecured rate quote.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PalletWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total weight of pallets used in shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentDimensions" type="ns:Dimensions" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Overall shipment dimensions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Comment" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Description for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialServicePayments" type="ns:FreightSpecialServicePayment" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies which party will pay surcharges for any special services which support split billing.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LineItems" type="ns:FreightShipmentLineItem" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Details of the commodities in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="FreightShipmentLineItem">
        <xs:annotation>
          <xs:documentation>Description of an individual commodity or class of content in a shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FreightClass" type="ns:FreightClassType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Freight class for this line item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Packaging" type="ns:PhysicalPackagingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specification of handling-unit packaging for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-provided description for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Weight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Weight for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Dimensions" type="ns:Dimensions" minOccurs="0">
            <xs:annotation>
              <xs:documentation>FED EX INTERNAL USE ONLY - Individual line item dimensions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Volume" type="ns:Volume" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Volume (cubic measure) for this commodity or class line.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="FreightShipmentRoleType">
        <xs:annotation>
          <xs:documentation>Indicates the role of the party submitting the transaction.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSIGNEE"/>
          <xs:enumeration value="SHIPPER"/>
          <xs:enumeration value="THIRD_PARTY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="FreightSpecialServicePayment">
        <xs:annotation>
          <xs:documentation>Specifies which party will be responsible for payment of any surcharges for Freight special services for which split billing is allowed.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SpecialService" type="ns:ShipmentSpecialServiceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the special service.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PaymentType" type="ns:FreightAccountPaymentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates who will pay for the special service.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="GeneralAgencyAgreementDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce a General Agency Agreement document. Remaining content (business data) to be defined once requirements have been completed.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HazardousCommodityContent">
        <xs:annotation>
          <xs:documentation>Documents the kind and quantity of an individual hazardous commodity in a package.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Description" type="ns:HazardousCommodityDescription" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies and describes an individual hazardous commodity.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Quantity" type="ns:HazardousCommodityQuantityDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the amount of the commodity in alternate units.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Options" type="ns:HazardousCommodityOptionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customer-provided specifications for handling individual commodities.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HazardousCommodityDescription">
        <xs:annotation>
          <xs:documentation>Identifies and describes an individual hazardous commodity. For 201001 load, this is based on data from the FedEx Ground Hazardous Materials Shipping Guide.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Regulatory identifier for a commodity (e.g. "UN ID" value).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackingGroup" type="ns:HazardousCommodityPackingGroupType" minOccurs="0"/>
          <xs:element name="ProperShippingName" type="xs:string" minOccurs="0"/>
          <xs:element name="TechnicalName" type="xs:string" minOccurs="0"/>
          <xs:element name="HazardClass" type="xs:string" minOccurs="0"/>
          <xs:element name="SubsidiaryClasses" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="LabelText" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityLabelTextOptionType">
        <xs:annotation>
          <xs:documentation>Specifies how the commodity is to be labeled.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPEND"/>
          <xs:enumeration value="OVERRIDE"/>
          <xs:enumeration value="STANDARD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityOptionDetail">
        <xs:annotation>
          <xs:documentation>Customer-provided specifications for handling individual commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="LabelTextOption" type="ns:HazardousCommodityLabelTextOptionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how the customer wishes the label text to be handled for this commodity in this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerSuppliedLabelText" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Text used in labeling the commodity under control of the labelTextOption field.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityOptionType">
        <xs:annotation>
          <xs:documentation>Indicates which kind of hazardous content (as defined by DOT) is being reported.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="HAZARDOUS_MATERIALS"/>
          <xs:enumeration value="LITHIUM_BATTERY_EXCEPTION"/>
          <xs:enumeration value="ORM_D"/>
          <xs:enumeration value="REPORTABLE_QUANTITIES"/>
          <xs:enumeration value="SMALL_QUANTITY_EXCEPTION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityPackagingDetail">
        <xs:annotation>
          <xs:documentation>Identifies number and type of packaging units for hazardous commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Count" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of units of the type below.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Units" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Units in which the hazardous commodity is packaged.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HazardousCommodityPackingGroupType">
        <xs:annotation>
          <xs:documentation>Identifies DOT packing group for a hazardous commodity.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="I"/>
          <xs:enumeration value="II"/>
          <xs:enumeration value="III"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="HazardousCommodityQuantityDetail">
        <xs:annotation>
          <xs:documentation>Identifies amount and units for quantity of hazardous commodities.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Amount" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Number of units of the type below.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Units" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Units by which the hazardous commodity is measured.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HoldAtLocationDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data required for a FedEx shipment that is to be held at the destination FedEx location for pickup by the recipient.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PhoneNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contact phone number for recipient of shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocationContactAndAddress" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contact and address of FedEx facility at which shipment is to be held.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocationType" type="ns:FedExLocationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Type of facility at which package/shipment is to be held.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocationId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Location identification (for facilities identified by an alphanumeric location code).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocationNumber" type="xs:int" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Location identification (for facilities identified by an numeric location code).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="HomeDeliveryPremiumDetail">
        <xs:annotation>
          <xs:documentation>The descriptive data required by FedEx for home delivery services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="HomeDeliveryPremiumType" type="ns:HomeDeliveryPremiumType" minOccurs="1"/>
          <xs:element name="Date" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Required for Date Certain Home Delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PhoneNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Required for Date Certain and Appointment Home Delivery.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>15</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="HomeDeliveryPremiumType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="APPOINTMENT"/>
          <xs:enumeration value="DATE_CERTAIN"/>
          <xs:enumeration value="EVENING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ImageId">
        <xs:restriction base="xs:string">
          <xs:enumeration value="IMAGE_1"/>
          <xs:enumeration value="IMAGE_2"/>
          <xs:enumeration value="IMAGE_3"/>
          <xs:enumeration value="IMAGE_4"/>
          <xs:enumeration value="IMAGE_5"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="InternationalDocumentContentType">
        <xs:annotation>
          <xs:documentation>The type of International shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DOCUMENTS_ONLY"/>
          <xs:enumeration value="NON_DOCUMENTS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelFormatType">
        <xs:annotation>
          <xs:documentation>Specifies the type of label to be returned.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMMON2D"/>
          <xs:enumeration value="LABEL_DATA_ONLY"/>
          <xs:enumeration value="MAILROOM"/>
          <xs:enumeration value="NO_LABEL"/>
          <xs:enumeration value="OPERATIONAL_LABEL"/>
          <xs:enumeration value="PRE_COMMON2D"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelMaskableDataType">
        <xs:annotation>
          <xs:documentation>Names for data elements / areas which may be suppressed from printing on labels.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMS_VALUE"/>
          <xs:enumeration value="DIMENSIONS"/>
          <xs:enumeration value="DUTIES_AND_TAXES_PAYOR_ACCOUNT_NUMBER"/>
          <xs:enumeration value="FREIGHT_PAYOR_ACCOUNT_NUMBER"/>
          <xs:enumeration value="PACKAGE_SEQUENCE_AND_COUNT"/>
          <xs:enumeration value="SHIPPER_ACCOUNT_NUMBER"/>
          <xs:enumeration value="SUPPLEMENTAL_LABEL_DOC_TAB"/>
          <xs:enumeration value="TERMS_AND_CONDITIONS"/>
          <xs:enumeration value="TOTAL_WEIGHT"/>
          <xs:enumeration value="TRANSPORTATION_CHARGES_PAYOR_ACCOUNT_NUMBER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelPrintingOrientationType">
        <xs:annotation>
          <xs:documentation>This indicates if the top or bottom of the label comes out of the printer first.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BOTTOM_EDGE_OF_TEXT_FIRST"/>
          <xs:enumeration value="TOP_EDGE_OF_TEXT_FIRST"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="LabelRotationType">
        <xs:annotation>
          <xs:documentation>Relative to normal orientation for the printer.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="LEFT"/>
          <xs:enumeration value="NONE"/>
          <xs:enumeration value="RIGHT"/>
          <xs:enumeration value="UPSIDE_DOWN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LabelSpecification">
        <xs:annotation>
          <xs:documentation>Description of shipping label to be returned in the reply</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="LabelFormatType" type="ns:LabelFormatType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specify type of label to be returned</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ImageType" type="ns:ShippingDocumentImageType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>
                The type of image or printer commands the label is to be formatted in.
                DPL = Unimark thermal printer language
                EPL2 = Eltron thermal printer language
                PDF = a label returned as a pdf image
                PNG = a label returned as a png image
                ZPLII = Zebra thermal printer language
              </xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelStockType" type="ns:LabelStockType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For thermal printer lables this indicates the size of the label and the location of the doc tab if present.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelPrintingOrientation" type="ns:LabelPrintingOrientationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This indicates if the top or bottom of the label comes out of the printer first.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelRotation" type="ns:LabelRotationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Relative to normal orientation for the printer. RIGHT=90 degrees clockwise, UPSIDE_DOWN=180 degrees, LEFT=90 degrees counterclockwise.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PrintedLabelOrigin" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If present, this contact and address information will replace the return address information on the label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerSpecifiedDetail" type="ns:CustomerSpecifiedLabelDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Allows customer-specified control of label content.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LabelStockType">
        <xs:annotation>
          <xs:documentation>For thermal printer labels this indicates the size of the label and the location of the doc tab if present.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="PAPER_4X6"/>
          <xs:enumeration value="PAPER_4X8"/>
          <xs:enumeration value="PAPER_4X9"/>
          <xs:enumeration value="PAPER_7X4.75"/>
          <xs:enumeration value="PAPER_8.5X11_BOTTOM_HALF_LABEL"/>
          <xs:enumeration value="PAPER_8.5X11_TOP_HALF_LABEL"/>
          <xs:enumeration value="STOCK_4X6"/>
          <xs:enumeration value="STOCK_4X6.75_LEADING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X6.75_TRAILING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X8"/>
          <xs:enumeration value="STOCK_4X9_LEADING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X9_TRAILING_DOC_TAB"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LiabilityCoverageDetail">
        <xs:sequence>
          <xs:element name="CoverageType" type="ns:LiabilityCoverageType" minOccurs="0"/>
          <xs:element name="CoverageAmount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the Liability Coverage Amount. For Jan 2010 this value represents coverage amount per pound</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LiabilityCoverageType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="NEW"/>
          <xs:enumeration value="USED_OR_RECONDITIONED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="LinearMeasure">
        <xs:annotation>
          <xs:documentation>Represents a one-dimensional measurement in small units (e.g. suitable for measuring a package or document), contrasted with Distance, which represents a large one-dimensional measurement (e.g. distance between cities).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Value" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The numerical quantity of this measurement.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Units" type="ns:LinearUnits" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The units for this measurement.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="LinearUnits">
        <xs:annotation>
          <xs:documentation>CM = centimeters, IN = inches</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CM"/>
          <xs:enumeration value="IN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Localization">
        <xs:annotation>
          <xs:documentation>Identifies the representation of human-readable text.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="LanguageCode" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Two-letter code for language (e.g. EN, FR, etc.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocaleCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Two-letter code for the region (e.g. us, ca, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Measure">
        <xs:sequence>
          <xs:element name="Quantity" type="xs:decimal" minOccurs="0"/>
          <xs:element name="Units" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="MinimumChargeType">
        <xs:annotation>
          <xs:documentation>Internal FedEx use only.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="CUSTOMER_FREIGHT_WEIGHT"/>
          <xs:enumeration value="EARNED_DISCOUNT"/>
          <xs:enumeration value="MIXED"/>
          <xs:enumeration value="RATE_SCALE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Money">
        <xs:sequence>
          <xs:element name="Currency" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NaftaCertificateOfOriginDetail">
        <xs:annotation>
          <xs:documentation>Data required to produce a Certificate of Origin document. Remaining content (business data) to be defined once requirements have been completed.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0"/>
          <xs:element name="BlanketPeriod" type="ns:DateRange" minOccurs="0"/>
          <xs:element name="ImporterSpecification" type="ns:NaftaImporterSpecificationType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which Party (if any) from the shipment is to be used as the source of importer data on the NAFTA COO form.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureContact" type="ns:Contact" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contact information for "Authorized Signature" area of form.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProducerSpecification" type="ns:NaftaProducerSpecificationType" minOccurs="0"/>
          <xs:element name="Producers" type="ns:NaftaProducer" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="CustomerImageUsages" type="ns:CustomerImageUsage" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NaftaCommodityDetail">
        <xs:sequence>
          <xs:element name="PreferenceCriterion" type="ns:NaftaPreferenceCriterionCode" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Defined by NAFTA regulations.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProducerDetermination" type="ns:NaftaProducerDeterminationCode" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Defined by NAFTA regulations.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ProducerId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identification of which producer is associated with this commodity (if multiple producers are used in a single shipment).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetCostMethod" type="ns:NaftaNetCostMethodCode" minOccurs="0"/>
          <xs:element name="NetCostDateRange" type="ns:DateRange" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Date range over which RVC net cost was calculated.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NaftaImporterSpecificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="IMPORTER_OF_RECORD"/>
          <xs:enumeration value="RECIPIENT"/>
          <xs:enumeration value="UNKNOWN"/>
          <xs:enumeration value="VARIOUS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NaftaNetCostMethodCode">
        <xs:annotation>
          <xs:documentation>
            Net cost method used.
          </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="NC"/>
          <xs:enumeration value="NO"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NaftaPreferenceCriterionCode">
        <xs:annotation>
          <xs:documentation>See instructions for NAFTA Certificate of Origin for code definitions.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="A"/>
          <xs:enumeration value="B"/>
          <xs:enumeration value="C"/>
          <xs:enumeration value="D"/>
          <xs:enumeration value="E"/>
          <xs:enumeration value="F"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="NaftaProducer">
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0"/>
          <xs:element name="Producer" type="ns:Party" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NaftaProducerDeterminationCode">
        <xs:annotation>
          <xs:documentation>See instructions for NAFTA Certificate of Origin for code definitions.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="NO_1"/>
          <xs:enumeration value="NO_2"/>
          <xs:enumeration value="NO_3"/>
          <xs:enumeration value="YES"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="NaftaProducerSpecificationType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="AVAILABLE_UPON_REQUEST"/>
          <xs:enumeration value="MULTIPLE_SPECIFIED"/>
          <xs:enumeration value="SAME"/>
          <xs:enumeration value="SINGLE_SPECIFIED"/>
          <xs:enumeration value="UNKNOWN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Notification">
        <xs:annotation>
          <xs:documentation>The descriptive data regarding the result of the submitted transaction.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Severity" type="ns:NotificationSeverityType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The severity of this notification. This can indicate success or failure or some other information about the request. The values that can be returned are SUCCESS - Your transaction succeeded with no other applicable information. NOTE - Additional information that may be of interest to you about your transaction. WARNING - Additional information that you need to know about your transaction that you may need to take action on. ERROR - Information about an error that occurred while processing your transaction. FAILURE - FedEx was unable to process your transaction at this time due to a system failure. Please try again later</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Source" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the source of this notification. Combined with the Code it uniquely identifies this notification</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Code" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A code that represents this notification. Combined with the Source it uniquely identifies this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Message" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Human-readable text that explains this notification.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LocalizedMessage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The translated message. The language and locale specified in the ClientDetail. Localization are used to determine the representation. Currently only supported in a TrackReply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MessageParameters" type="ns:NotificationParameter" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>A collection of name/value pairs that provide specific data to help the client determine the nature of an error (or warning, etc.) witout having to parse the message string.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="NotificationParameter">
        <xs:sequence>
          <xs:element name="Id" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of data contained in Value (e.g. SERVICE_TYPE, PACKAGE_SEQUENCE, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The value of the parameter (e.g. PRIORITY_OVERNIGHT, 2, etc..).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="NotificationSeverityType">
        <xs:annotation>
          <xs:documentation>Identifies the set of severity values for a Notification.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ERROR"/>
          <xs:enumeration value="FAILURE"/>
          <xs:enumeration value="NOTE"/>
          <xs:enumeration value="SUCCESS"/>
          <xs:enumeration value="WARNING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Op900Detail">
        <xs:annotation>
          <xs:documentation>The instructions indicating how to print the OP-900 form for hazardous materials packages.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Format" type="ns:ShippingDocumentFormat" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Reference" type="ns:CustomerReferenceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies which reference type (from the package's customer references) is to be used as the source for the reference on this OP-900.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerImageUsages" type="ns:CustomerImageUsage" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the usage and identification of customer supplied images to be used on this document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureName" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Data field to be used when a name is to be printed in the document instead of (or in addition to) a signature image.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="OversizeClassType">
        <xs:annotation>
          <xs:documentation>The Oversize classification for a package.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="OVERSIZE_1"/>
          <xs:enumeration value="OVERSIZE_2"/>
          <xs:enumeration value="OVERSIZE_3"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PackageRateDetail">
        <xs:annotation>
          <xs:documentation>Data for a package's rates, as calculated per a specific rate type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Type used for this specific set of rate data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RatedWeightMethod" type="ns:RatedWeightMethod" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which weight was used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MinimumChargeType" type="ns:MinimumChargeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>INTERNAL FEDEX USE ONLY.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BillingWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The weight that was used to calculate the rate.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DimWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The dimensional weight of this package (if greater than actual).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OversizeWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The oversize weight of this package (if the package is oversize).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BaseCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The transportation charge only (prior to any discounts applied) for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalFreightDiscounts" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The sum of all discounts on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetFreight" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This package's baseCharge - totalFreightDiscounts.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalSurcharges" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The sum of all surcharges on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetFedExCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This package's netFreight + totalSurcharges (not including totalTaxes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The sum of all taxes on this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NetCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This package's netFreight + totalSurcharges + totalTaxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalRebates" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total sum of all rebates applied to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightDiscounts" type="ns:RateDiscount" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rate discounts that apply to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rebates" type="ns:Rebate" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rebates that apply to this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Surcharges" type="ns:Surcharge" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All surcharges that apply to this package (either because of characteristics of the package itself, or because it is carrying per-shipment surcharges for the shipment of which it is a part).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Taxes" type="ns:Tax" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All taxes applicable (or distributed to) this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingCharges" type="ns:VariableHandlingCharges" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The variable handling charges calculated based on the type variable handling charges requested.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PackageSpecialServiceType">
        <xs:annotation>
          <xs:documentation>Identifies the collection of special services offered by FedEx.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ALCOHOL"/>
          <xs:enumeration value="APPOINTMENT_DELIVERY"/>
          <xs:enumeration value="COD"/>
          <xs:enumeration value="DANGEROUS_GOODS"/>
          <xs:enumeration value="DRY_ICE"/>
          <xs:enumeration value="NON_STANDARD_CONTAINER"/>
          <xs:enumeration value="PRIORITY_ALERT"/>
          <xs:enumeration value="SIGNATURE_OPTION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PackageSpecialServicesRequested">
        <xs:annotation>
          <xs:documentation>These special services are available at the package level for some or all service types. If the shipper is requesting a special service which requires additional data, the package special service type must be present in the specialServiceTypes collection, and the supporting detail must be provided in the appropriate sub-object below.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SpecialServiceTypes" type="ns:PackageSpecialServiceType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The types of all special services requested for the enclosing shipment or package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodDetail" type="ns:CodDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For use with FedEx Ground services only; COD must be present in shipment's special services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DangerousGoodsDetail" type="ns:DangerousGoodsDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for a FedEx shipment containing dangerous materials. This element is required when SpecialServiceType.DANGEROUS_GOODS or HAZARDOUS_MATERIAL is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DryIceWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for a FedEx shipment containing dry ice. This element is required when SpecialServiceType.DRY_ICE is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureOptionDetail" type="ns:SignatureOptionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data required for FedEx signature services. This element is required when SpecialServiceType.SIGNATURE_OPTION is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PriorityAlertDetail" type="ns:PriorityAlertDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>To be filled.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PackagingType">
        <xs:annotation>
          <xs:documentation>Identifies the packaging used by the requestor for the package. See PackagingType for list of valid enumerated values.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_10KG_BOX"/>
          <xs:enumeration value="FEDEX_25KG_BOX"/>
          <xs:enumeration value="FEDEX_BOX"/>
          <xs:enumeration value="FEDEX_ENVELOPE"/>
          <xs:enumeration value="FEDEX_PAK"/>
          <xs:enumeration value="FEDEX_TUBE"/>
          <xs:enumeration value="YOUR_PACKAGING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Party">
        <xs:annotation>
          <xs:documentation>The descriptive data for a person or company entitiy doing business with FedEx.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="AccountNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the FedEx account number assigned to the customer.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>12</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Tins" type="ns:TaxpayerIdentification" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Descriptive data for taxpayer identification information.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Contact" type="ns:Contact" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the point-of-contact person.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Address" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data for a physical location.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Payment">
        <xs:annotation>
          <xs:documentation>The descriptive data for the monetary compensation given to FedEx for services rendered to the customer.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PaymentType" type="ns:PaymentType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the method of payment for a service. See PaymentType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Payor" type="ns:Payor" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the party responsible for payment for a service.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PaymentType">
        <xs:annotation>
          <xs:documentation>Identifies the method of payment for a service.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="SENDER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Payor">
        <xs:annotation>
          <xs:documentation>Descriptive data identifying the party responsible for payment for a service.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="AccountNumber" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the FedEx account number assigned to the payor.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>12</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="CountryCode" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the country of the payor.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="PendingShipmentDetail">
        <xs:annotation>
          <xs:documentation>This information describes the kind of pending shipment being requested.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:PendingShipmentType" minOccurs="1"/>
          <xs:element name="ExpirationDate" type="xs:date" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Date after which the pending shipment will no longer be available for completion.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EmailLabelDetail" type="ns:EMailLabelDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used with type of EMAIL.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PendingShipmentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EMAIL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PhysicalPackagingType">
        <xs:annotation>
          <xs:documentation>This enumeration rationalizes the former FedEx Express international "admissibility package" types (based on ANSI X.12) and the FedEx Freight packaging types. The values represented are those common to both carriers.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BAG"/>
          <xs:enumeration value="BARREL"/>
          <xs:enumeration value="BASKET"/>
          <xs:enumeration value="BOX"/>
          <xs:enumeration value="BUCKET"/>
          <xs:enumeration value="BUNDLE"/>
          <xs:enumeration value="CARTON"/>
          <xs:enumeration value="CASE"/>
          <xs:enumeration value="CONTAINER"/>
          <xs:enumeration value="CRATE"/>
          <xs:enumeration value="CYLINDER"/>
          <xs:enumeration value="DRUM"/>
          <xs:enumeration value="ENVELOPE"/>
          <xs:enumeration value="HAMPER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PAIL"/>
          <xs:enumeration value="PALLET"/>
          <xs:enumeration value="PIECE"/>
          <xs:enumeration value="REEL"/>
          <xs:enumeration value="ROLL"/>
          <xs:enumeration value="SKID"/>
          <xs:enumeration value="TANK"/>
          <xs:enumeration value="TUBE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PickupDetail">
        <xs:annotation>
          <xs:documentation>This class describes the pickup characteristics of a shipment (e.g. for use in a tag request).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ReadyDateTime" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="LatestPickupDateTime" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="CourierInstructions" type="xs:string" minOccurs="0"/>
          <xs:element name="RequestType" type="ns:PickupRequestType" minOccurs="0"/>
          <xs:element name="RequestSource" type="ns:PickupRequestSourceType" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PickupRequestSourceType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="AUTOMATION"/>
          <xs:enumeration value="CUSTOMER_SERVICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PickupRequestType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="FUTURE_DAY"/>
          <xs:enumeration value="SAME_DAY"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="PricingCodeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACTUAL"/>
          <xs:enumeration value="ALTERNATE"/>
          <xs:enumeration value="BASE"/>
          <xs:enumeration value="HUNDREDWEIGHT"/>
          <xs:enumeration value="HUNDREDWEIGHT_ALTERNATE"/>
          <xs:enumeration value="INTERNATIONAL_DISTRIBUTION"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY_SERVICE"/>
          <xs:enumeration value="LTL_FREIGHT"/>
          <xs:enumeration value="PACKAGE"/>
          <xs:enumeration value="SHIPMENT"/>
          <xs:enumeration value="SHIPMENT_FIVE_POUND_OPTIONAL"/>
          <xs:enumeration value="SHIPMENT_OPTIONAL"/>
          <xs:enumeration value="SPECIAL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="PriorityAlertDetail">
        <xs:annotation>
          <xs:documentation>Currently not supported.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Content" type="xs:string" minOccurs="0" maxOccurs="3"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="PurposeOfShipmentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="GIFT"/>
          <xs:enumeration value="NOT_SOLD"/>
          <xs:enumeration value="PERSONAL_EFFECTS"/>
          <xs:enumeration value="REPAIR_AND_RETURN"/>
          <xs:enumeration value="SAMPLE"/>
          <xs:enumeration value="SOLD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RateDimensionalDivisorType">
        <xs:annotation>
          <xs:documentation>Indicates the reason that a dim divisor value was chose.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COUNTRY"/>
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PRODUCT"/>
          <xs:enumeration value="WAIVED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RateDiscount">
        <xs:annotation>
          <xs:documentation>Identifies a discount applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RateDiscountType" type="ns:RateDiscountType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of discount applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The amount of the discount applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Percent" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The percentage of the discount applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RateDiscountType">
        <xs:annotation>
          <xs:documentation>Identifies the type of discount applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BONUS"/>
          <xs:enumeration value="COUPON"/>
          <xs:enumeration value="EARNED"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="VOLUME"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RateElementBasisType">
        <xs:annotation>
          <xs:documentation>Selects the value from a set of rate data to which the percentage is applied.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BASE_CHARGE"/>
          <xs:enumeration value="NET_CHARGE"/>
          <xs:enumeration value="NET_CHARGE_EXCLUDING_TAXES"/>
          <xs:enumeration value="NET_FREIGHT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RateReply">
        <xs:annotation>
          <xs:documentation>The response to a RateRequest. The Notifications indicate whether the request was successful or not.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="HighestSeverity" type="ns:NotificationSeverityType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>This indicates the highest level of severity of all the notifications returned in this reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Notifications" type="ns:Notification" minOccurs="1" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The descriptive data regarding the results of the submitted transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains the CustomerTransactionId that was sent in the request.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The version of this reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateReplyDetails" type="ns:RateReplyDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Each element contains all rate data for a single service. If service was specified in the request, there will be a single entry in this array; if service was omitted in the request, there will be a separate entry in this array for each service being compared.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RateReplyDetail">
        <xs:sequence>
          <xs:element name="ServiceType" type="ns:ServiceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the FedEx service to use in shipping the package. See ServiceType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackagingType" type="ns:PackagingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the packaging used by the requestor for the package. See PackagingType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AppliedOptions" type="ns:ServiceOptionType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Shows the specific combination of service options combined with the service type that produced this committment in the set returned to the caller.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AppliedSubOptions" type="ns:ServiceSubOptionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Supporting detail for applied options identified in preceding field.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryStation" type="xs:string" minOccurs="0"/>
          <xs:element name="DeliveryDayOfWeek" type="ns:DayOfWeekType" minOccurs="0"/>
          <xs:element name="DeliveryTimestamp" type="xs:dateTime" minOccurs="0"/>
          <xs:element name="CommitDetails" type="ns:CommitDetail" minOccurs="0" maxOccurs="unbounded"/>
          <xs:element name="DestinationAirportId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identification of an airport, using standard three-letter abbreviations.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="IneligibleForMoneyBackGuarantee" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates whether or not this shipment is eligible for a money back guarantee.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OriginServiceArea" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Commitment code for the origin.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DestinationServiceArea" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Commitment code for the destination.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransitTime" type="ns:TransitTimeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Time in transit from pickup to delivery.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MaximumTransitTime" type="ns:TransitTimeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Maximum expected transit time</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureOption" type="ns:SignatureOptionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The signature option for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ActualRateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The actual rate type of the charges for this package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RatedShipmentDetails" type="ns:RatedShipmentDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Each element contains all rate data for a single rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RateRequest">
        <xs:annotation>
          <xs:documentation>Descriptive data sent to FedEx by a customer in order to rate a package/shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="WebAuthenticationDetail" type="ns:WebAuthenticationDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data to be used in authentication of the sender's identity (and right to use FedEx web services).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ClientDetail" type="ns:ClientDetail" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the client submitting the transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TransactionDetail" type="ns:TransactionDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Version" type="ns:VersionId" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReturnTransitAndCommit" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Allows the caller to specify that the transit time and commit data are to be returned in the reply.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CarrierCodes" type="ns:CarrierCodeType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Candidate carriers for rate-shopping use case. This field is only considered if requestedShipment/serviceType is omitted.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableOptions" type="ns:ServiceOptionType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Contains zero or more service options whose combinations are to be considered when replying with available services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestedShipment" type="ns:RequestedShipment" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The shipment for which a rate quote (or rate-shopping comparison) is desired.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RateRequestType">
        <xs:annotation>
          <xs:documentation>Indicates the type of rates to be returned.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCOUNT"/>
          <xs:enumeration value="LIST"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RateTypeBasisType">
        <xs:annotation>
          <xs:documentation>Select the type of rate from which the element is to be selected.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACCOUNT"/>
          <xs:enumeration value="LIST"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RatedPackageDetail">
        <xs:annotation>
          <xs:documentation>If requesting rates using the PackageDetails element (one package at a time) in the request, the rates for each package will be returned in this element. Currently total piece total weight rates are also retuned in this element.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="TrackingIds" type="ns:TrackingId" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Echoed from the corresponding package in the rate request (if provided).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GroupNumber" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used with request containing PACKAGE_GROUPS, to identify which group of identical packages was used to produce a reply item.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EffectiveNetDiscount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The difference between "list" and "account" net charge.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AdjustedCodCollectionAmount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Ground COD is shipment level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="OversizeClass" type="ns:OversizeClassType" minOccurs="0"/>
          <xs:element name="PackageRateDetail" type="ns:PackageRateDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Rate data that are tied to a specific package and rate type combination.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RatedShipmentDetail">
        <xs:annotation>
          <xs:documentation>This class groups the shipment and package rating data for a specific rate type for use in a rating reply, which groups result data by rate type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="EffectiveNetDiscount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The difference between "list" and "account" total net charge.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="AdjustedCodCollectionAmount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Express COD is shipment level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentRateDetail" type="ns:ShipmentRateDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The shipment-level totals for this rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RatedPackages" type="ns:RatedPackageDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The package-level data for this rate type.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RatedWeightMethod">
        <xs:annotation>
          <xs:documentation>The method used to calculate the weight to be used in rating the package..</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ACTUAL"/>
          <xs:enumeration value="AVERAGE_PACKAGE_WEIGHT_MINIMUM"/>
          <xs:enumeration value="BALLOON"/>
          <xs:enumeration value="DIM"/>
          <xs:enumeration value="FREIGHT_MINIMUM"/>
          <xs:enumeration value="MIXED"/>
          <xs:enumeration value="OVERSIZE"/>
          <xs:enumeration value="OVERSIZE_1"/>
          <xs:enumeration value="OVERSIZE_2"/>
          <xs:enumeration value="OVERSIZE_3"/>
          <xs:enumeration value="PACKAGING_MINIMUM"/>
          <xs:enumeration value="WEIGHT_BREAK"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Rebate">
        <xs:annotation>
          <xs:documentation>Identifies a discount applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RebateType" type="ns:RebateType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The amount of the discount applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Percent" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The percentage of the discount applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RebateType">
        <xs:annotation>
          <xs:documentation>Identifies the type of discount applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BONUS"/>
          <xs:enumeration value="EARNED"/>
          <xs:enumeration value="OTHER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RecipientCustomsId">
        <xs:annotation>
          <xs:documentation>Specifies how the recipient is identified for customs purposes; the requirements on this information vary with destination country.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Type" type="ns:RecipientCustomsIdType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the kind of identification being used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains the actual ID value, of the type specified above.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RecipientCustomsIdType">
        <xs:annotation>
          <xs:documentation>Type of Brazilian taxpayer identifier provided in Recipient/TaxPayerIdentification/Number. For shipments bound for Brazil this overrides the value in Recipient/TaxPayerIdentification/TinType</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMPANY"/>
          <xs:enumeration value="INDIVIDUAL"/>
          <xs:enumeration value="PASSPORT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RegulatoryControlType">
        <xs:annotation>
          <xs:documentation>FOOD_OR_PERISHABLE is required by FDA/BTA; must be true for food/perishable items coming to US or PR from non-US/non-PR origin</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EU_CIRCULATION"/>
          <xs:enumeration value="FOOD_OR_PERISHABLE"/>
          <xs:enumeration value="NAFTA"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="RequestedPackageLineItem">
        <xs:annotation>
          <xs:documentation>This class rationalizes RequestedPackage and RequestedPackageSummary from previous interfaces. The way in which it is uses within a RequestedShipment depends on the RequestedPackageDetailType value specified for that shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SequenceNumber" type="xs:positiveInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used only with INDIVIDUAL_PACKAGE, as a unique identifier of each requested package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GroupNumber" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used only with PACKAGE_GROUPS, as a unique identifier of each group of identical packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GroupPackageCount" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used only with PACKAGE_GROUPS, as a count of packages within a group of identical packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingChargeDetail" type="ns:VariableHandlingChargeDetail" minOccurs="0"/>
          <xs:element name="InsuredValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used for INDIVIDUAL_PACKAGES and PACKAGE_GROUPS. Ignored for PACKAGE_SUMMARY, in which case totalInsuredValue and packageCount on the shipment will be used to determine this value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Weight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Only used for INDIVIDUAL_PACKAGES and PACKAGE_GROUPS. Ignored for PACKAGE_SUMMARY, in which case totalweight and packageCount on the shipment will be used to determine this value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Dimensions" type="ns:Dimensions" minOccurs="0"/>
          <xs:element name="PhysicalPackaging" type="ns:PhysicalPackagingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Provides additional detail on how the customer has physically packaged this item. As of June 2009, required for packages moving under international and SmartPost services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ItemDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Human-readable text describing the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomerReferences" type="ns:CustomerReference" minOccurs="0" maxOccurs="3"/>
          <xs:element name="SpecialServicesRequested" type="ns:PackageSpecialServicesRequested" minOccurs="0"/>
          <xs:element name="ContentRecords" type="ns:ContentRecord" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Only used for INDIVIDUAL_PACKAGES and PACKAGE_GROUPS.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="RequestedShipment">
        <xs:annotation>
          <xs:documentation>The descriptive data for the shipment being tendered to FedEx.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ShipTimestamp" type="xs:dateTime" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the date and time the package is tendered to FedEx. Both the date and time portions of the string are expected to be used. The date should not be a past date or a date more than 10 days in the future. The time is the local time of the shipment based on the shipper's time zone. The date component must be in the format: YYYY-MM-DD (e.g. 2006-06-26). The time component must be in the format: HH:MM:SS using a 24 hour clock (e.g. 11:00 a.m. is 11:00:00, whereas 5:00 p.m. is 17:00:00). The date and time parts are separated by the letter T (e.g. 2006-06-26T17:00:00). There is also a UTC offset component indicating the number of hours/mainutes from UTC (e.g 2006-06-26T17:00:00-0400 is defined form June 26, 2006 5:00 pm Eastern Time).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DropoffType" type="ns:DropoffType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the method by which the package is to be tendered to FedEx. This element does not dispatch a courier for package pickup. See DropoffType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ServiceType" type="ns:ServiceType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the FedEx service to use in shipping the package. See ServiceType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackagingType" type="ns:PackagingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the packaging used by the requestor for the package. See PackagingType for list of valid enumerated values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the total weight of the shipment being conveyed to FedEx.This is only applicable to International shipments and should only be used on the first package of a mutiple piece shipment.This value contains 1 explicit decimal position</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalInsuredValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total insured amount.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Shipper" type="ns:Party" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the party responsible for shipping the package. Shipper and Origin should have the same address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Recipient" type="ns:Party" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Descriptive data identifying the party receiving the package.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RecipientLocationNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>A unique identifier for a recipient location</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>10</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Origin" type="ns:ContactAndAddress" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Physical starting address for the shipment, if different from shipper's address.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShippingChargesPayment" type="ns:Payment" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data indicating the method and means of payment to FedEx for providing shipping services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialServicesRequested" type="ns:ShipmentSpecialServicesRequested" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data regarding special services requested by the shipper for this shipment. If the shipper is requesting a special service which requires additional data (e.g. COD), the special service type must be present in the specialServiceTypes collection, and the supporting detail must be provided in the appropriate sub-object. For example, to request COD, "COD" must be included in the SpecialServiceTypes collection and the CodDetail object must contain the required data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ExpressFreightDetail" type="ns:ExpressFreightDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Details specific to an Express freight shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightShipmentDetail" type="ns:FreightShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Data applicable to shipments using FEDEX_FREIGHT and FEDEX_NATIONAL_FREIGHT services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DeliveryInstructions" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used with Ground Home Delivery and Freight.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingChargeDetail" type="ns:VariableHandlingChargeDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Details about how to calculate variable handling charges at the shipment level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomsClearanceDetail" type="ns:CustomsClearanceDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Customs clearance data, used for both international and intra-country shipping.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PickupDetail" type="ns:PickupDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For use in "process tag" transaction.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SmartPostDetail" type="ns:SmartPostShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the characteristics of a shipment pertaining to SmartPost services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="BlockInsightVisibility" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>If true, only the shipper/payor will have visibility of this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LabelSpecification" type="ns:LabelSpecification" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Details about the image format and printer type the label is to returned in.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShippingDocumentSpecification" type="ns:ShippingDocumentSpecification" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Contains data used to create additional (non-label) shipping documents.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateRequestTypes" type="ns:RateRequestType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies whether and what kind of rates the customer wishes to have quoted on this shipment. The reply will also be constrained by other data on the shipment and customer.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EdtRequestType" type="ns:EdtRequestType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies whether the customer wishes to have Estimated Duties and Taxes provided with the rate quotation on this shipment. Only applies with shipments moving under international services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PackageCount" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total number of packages in the entire shipment (even when the shipment spans multiple transactions.)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentOnlyFields" type="ns:ShipmentOnlyFieldsType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies which package-level data values are provided at the shipment-level only. The package-level data values types specified here will not be provided at the package-level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RequestedPackageLineItems" type="ns:RequestedPackageLineItem" minOccurs="0" maxOccurs="999">
            <xs:annotation>
              <xs:documentation>One or more package-attribute descriptions, each of which describes an individual package, a group of identical packages, or (for the total-piece-total-weight case) common characteristics all packages in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="RequestedShippingDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="COMMERCIAL_INVOICE"/>
          <xs:enumeration value="CUSTOMER_SPECIFIED_LABELS"/>
          <xs:enumeration value="GENERAL_AGENCY_AGREEMENT"/>
          <xs:enumeration value="LABEL"/>
          <xs:enumeration value="NAFTA_CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="PRO_FORMA_INVOICE"/>
          <xs:enumeration value="RETURN_INSTRUCTIONS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="RequiredShippingDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CANADIAN_B13A"/>
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="COMMERCIAL_INVOICE"/>
          <xs:enumeration value="INTERNATIONAL_AIRWAY_BILL"/>
          <xs:enumeration value="MAIL_SERVICE_AIRWAY_BILL"/>
          <xs:enumeration value="SHIPPERS_EXPORT_DECLARATION"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ReturnEMailAllowedSpecialServiceType">
        <xs:annotation>
          <xs:documentation>These values are used to control the availability of certain special services at the time when a customer uses the e-mail label link to create a return shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="SATURDAY_DELIVERY"/>
          <xs:enumeration value="SATURDAY_PICKUP"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ReturnEMailDetail">
        <xs:sequence>
          <xs:element name="MerchantPhoneNumber" type="xs:string" minOccurs="0"/>
          <xs:element name="AllowedSpecialServices" type="ns:ReturnEMailAllowedSpecialServiceType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Identifies the allowed (merchant-authorized) special services which may be selected when the subsequent shipment is created. Only services represented in EMailLabelAllowedSpecialServiceType will be controlled by this list.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ReturnShipmentDetail">
        <xs:annotation>
          <xs:documentation>Information relating to a return shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ReturnType" type="ns:ReturnType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>The type of return shipment that is being requested. At present the only type of retrun shipment that is supported is PRINT_RETURN_LABEL. With this option you can print a return label to insert into the box of an outbound shipment. This option can not be used to print an outbound label.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rma" type="ns:Rma" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Return Merchant Authorization</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReturnEMailDetail" type="ns:ReturnEMailDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specific information about the delivery of the email and options for the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ReturnType">
        <xs:annotation>
          <xs:documentation>The type of return shipment that is being requested.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FEDEX_TAG"/>
          <xs:enumeration value="PENDING"/>
          <xs:enumeration value="PRINT_RETURN_LABEL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ReturnedRateType">
        <xs:annotation>
          <xs:documentation>The "PAYOR..." rates are expressed in the currency identified in the payor's rate table(s). The "RATED..." rates are expressed in the currency of the origin country. Former "...COUNTER..." values have become "...RETAIL..." values, except for PAYOR_COUNTER and RATED_COUNTER, which have been removed.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="PAYOR_ACCOUNT_PACKAGE"/>
          <xs:enumeration value="PAYOR_ACCOUNT_SHIPMENT"/>
          <xs:enumeration value="PAYOR_LIST_PACKAGE"/>
          <xs:enumeration value="PAYOR_LIST_SHIPMENT"/>
          <xs:enumeration value="RATED_ACCOUNT_PACKAGE"/>
          <xs:enumeration value="RATED_ACCOUNT_SHIPMENT"/>
          <xs:enumeration value="RATED_LIST_PACKAGE"/>
          <xs:enumeration value="RATED_LIST_SHIPMENT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Rma">
        <xs:annotation>
          <xs:documentation>Return Merchant Authorization</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Number" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Return Merchant Authorization Number</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>20</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Reason" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The reason for the return.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>60</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SecondaryBarcodeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMMON_2D"/>
          <xs:enumeration value="NONE"/>
          <xs:enumeration value="SSCC_18"/>
          <xs:enumeration value="USPS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ServiceOptionType">
        <xs:annotation>
          <xs:documentation>These values control the optional features of service that may be combined in a commitment/rate comparision transaction.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FREIGHT_GUARANTEE"/>
          <xs:enumeration value="SATURDAY_DELIVERY"/>
          <xs:enumeration value="SMART_POST_ALLOWED_INDICIA"/>
          <xs:enumeration value="SMART_POST_HUB_ID"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ServiceSubOptionDetail">
        <xs:annotation>
          <xs:documentation>Supporting detail for applied options identified in a rate quote.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FreightGuarantee" type="ns:FreightGuaranteeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of Freight Guarantee applied, if FREIGHT_GUARANTEE is applied to the rate quote.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SmartPostHubId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the smartPostHubId used during rate quote, if SMART_POST_HUB_ID is a variable option on the rate request.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SmartPostIndicia" type="ns:SmartPostIndiciaType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the indicia used during rate quote, if SMART_POST_ALLOWED_INDICIA is a variable option on the rate request.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ServiceType">
        <xs:annotation>
          <xs:documentation>Identifies the FedEx service to use in shipping the package. See ServiceType for list of valid enumerated values.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EUROPE_FIRST_INTERNATIONAL_PRIORITY"/>
          <xs:enumeration value="FEDEX_1_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_2_DAY"/>
          <xs:enumeration value="FEDEX_2_DAY_AM"/>
          <xs:enumeration value="FEDEX_2_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_3_DAY_FREIGHT"/>
          <xs:enumeration value="FEDEX_EXPRESS_SAVER"/>
          <xs:enumeration value="FEDEX_FIRST_FREIGHT"/>
          <xs:enumeration value="FEDEX_FREIGHT_ECONOMY"/>
          <xs:enumeration value="FEDEX_FREIGHT_PRIORITY"/>
          <xs:enumeration value="FEDEX_GROUND"/>
          <xs:enumeration value="FIRST_OVERNIGHT"/>
          <xs:enumeration value="GROUND_HOME_DELIVERY"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY"/>
          <xs:enumeration value="INTERNATIONAL_ECONOMY_FREIGHT"/>
          <xs:enumeration value="INTERNATIONAL_FIRST"/>
          <xs:enumeration value="INTERNATIONAL_PRIORITY"/>
          <xs:enumeration value="INTERNATIONAL_PRIORITY_FREIGHT"/>
          <xs:enumeration value="PRIORITY_OVERNIGHT"/>
          <xs:enumeration value="SMART_POST"/>
          <xs:enumeration value="STANDARD_OVERNIGHT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentDryIceDetail">
        <xs:annotation>
          <xs:documentation>Shipment-level totals of dry ice data across all packages.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PackageCount" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total number of packages in the shipment that contain dry ice.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total shipment dry ice weight for all packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShipmentLegRateDetail">
        <xs:annotation>
          <xs:documentation>Data for a single leg of a shipment's total/summary rates, as calculated per a specific rate type.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="LegDescription" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Human-readable text describing the shipment leg.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LegOrigin" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Origin for this leg.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="LegDestination" type="ns:Address" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Destination for this leg.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Type used for this specific set of rate data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateScale" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the rate scale used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateZone" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the rate zone used (based on origin and destination).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PricingCode" type="ns:PricingCodeType" minOccurs="0"/>
          <xs:element name="RatedWeightMethod" type="ns:RatedWeightMethod" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which weight was used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MinimumChargeType" type="ns:MinimumChargeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>INTERNAL FEDEX USE ONLY.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CurrencyExchangeRate" type="ns:CurrencyExchangeRate" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the currency exchange performed on financial amounts for this rate.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialRatingApplied" type="ns:SpecialRatingAppliedType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates which special rating cases applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DimDivisor" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="DimDivisorType" type="ns:RateDimensionalDivisorType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of dim divisor that was applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FuelSurchargePercent" type="xs:decimal" minOccurs="0"/>
          <xs:element name="TotalBillingWeight" type="ns:Weight" minOccurs="0"/>
          <xs:element name="TotalDimWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Sum of dimensional weights for all packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalBaseCharge" type="ns:Money" minOccurs="0"/>
          <xs:element name="TotalFreightDiscounts" type="ns:Money" minOccurs="0"/>
          <xs:element name="TotalNetFreight" type="ns:Money" minOccurs="0"/>
          <xs:element name="TotalSurcharges" type="ns:Money" minOccurs="0"/>
          <xs:element name="TotalNetFedExCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetFreight + totalSurcharges (not including totalTaxes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total of the transportation-based taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetCharge" type="ns:Money" minOccurs="0"/>
          <xs:element name="TotalRebates" type="ns:Money" minOccurs="0"/>
          <xs:element name="TotalDutiesAndTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total of all values under this shipment's dutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetChargeWithDutiesAndTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetCharge + totalDutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment AND duties, taxes and transportation charges are all paid by the same sender's account.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightRateDetail" type="ns:FreightRateDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Rate data specific to FedEx Freight and FedEx National Freight services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightDiscounts" type="ns:RateDiscount" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rate discounts that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rebates" type="ns:Rebate" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rebates that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Surcharges" type="ns:Surcharge" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All surcharges that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Taxes" type="ns:Tax" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All transportation-based taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DutiesAndTaxes" type="ns:EdtCommodityTax" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All commodity-based duties and taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingCharges" type="ns:VariableHandlingCharges" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The "order level" variable handling charges.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalVariableHandlingCharges" type="ns:VariableHandlingCharges" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total of all variable handling charges at both shipment (order) and package level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShipmentOnlyFieldsType">
        <xs:annotation>
          <xs:documentation>These values identify which package-level data values will be provided at the shipment-level.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DIMENSIONS"/>
          <xs:enumeration value="INSURED_VALUE"/>
          <xs:enumeration value="WEIGHT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentRateDetail">
        <xs:annotation>
          <xs:documentation>Data for a shipment's total/summary rates, as calculated per a specific rate type. The "total..." fields may differ from the sum of corresponding package data for Multiweight or Express MPS.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RateType" type="ns:ReturnedRateType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Type used for this specific set of rate data.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateScale" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the rate scale used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateZone" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the rate zone used (based on origin and destination).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PricingCode" type="ns:PricingCodeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates the type of pricing used for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RatedWeightMethod" type="ns:RatedWeightMethod" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Indicates which weight was used.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="MinimumChargeType" type="ns:MinimumChargeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>INTERNAL FEDEX USE ONLY.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CurrencyExchangeRate" type="ns:CurrencyExchangeRate" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the currency exchange performed on financial amounts for this rate.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SpecialRatingApplied" type="ns:SpecialRatingAppliedType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates which special rating cases applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DimDivisor" type="xs:nonNegativeInteger" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The value used to calculate the weight based on the dimensions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DimDivisorType" type="ns:RateDimensionalDivisorType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the type of dim divisor that was applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FuelSurchargePercent" type="xs:decimal" minOccurs="0"/>
          <xs:element name="TotalBillingWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The weight used to calculate these rates.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalDimWeight" type="ns:Weight" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Sum of dimensional weights for all packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalBaseCharge" type="ns:Money" minOccurs="0"/>
          <xs:element name="TotalFreightDiscounts" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total discounts used in the rate calculation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetFreight" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The freight charge minus discounts.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalSurcharges" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total amount of all surcharges applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetFedExCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetFreight + totalSurcharges (not including totalTaxes).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total of the transportation-based taxes.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The net charge after applying all discounts and surcharges.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalRebates" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total sum of all rebates applied to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalDutiesAndTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Total of all values under this shipment's dutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalNetChargeWithDutiesAndTaxes" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This shipment's totalNetCharge + totalDutiesAndTaxes; only provided if estimated duties and taxes were calculated for this shipment AND duties, taxes and transportation charges are all paid by the same sender's account.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentLegRateDetails" type="ns:ShipmentLegRateDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Identifies the Rate Details per each leg in a Freight Shipment</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightRateDetail" type="ns:FreightRateDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Rate data specific to FedEx Freight and FedEx National Freight services.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FreightDiscounts" type="ns:RateDiscount" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rate discounts that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Rebates" type="ns:Rebate" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All rebates that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Surcharges" type="ns:Surcharge" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All surcharges that apply to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Taxes" type="ns:Tax" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All transportation-based taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="DutiesAndTaxes" type="ns:EdtCommodityTax" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>All commodity-based duties and taxes applicable to this shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="VariableHandlingCharges" type="ns:VariableHandlingCharges" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The "order level" variable handling charges.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalVariableHandlingCharges" type="ns:VariableHandlingCharges" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The total of all variable handling charges at both shipment (order) and package level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShipmentSpecialServiceType">
        <xs:annotation>
          <xs:documentation>Identifies the collection of special service offered by FedEx.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BROKER_SELECT_OPTION"/>
          <xs:enumeration value="CALL_BEFORE_DELIVERY"/>
          <xs:enumeration value="COD"/>
          <xs:enumeration value="CUSTOM_DELIVERY_WINDOW"/>
          <xs:enumeration value="DANGEROUS_GOODS"/>
          <xs:enumeration value="DO_NOT_BREAK_DOWN_PALLETS"/>
          <xs:enumeration value="DO_NOT_STACK_PALLETS"/>
          <xs:enumeration value="DRY_ICE"/>
          <xs:enumeration value="EAST_COAST_SPECIAL"/>
          <xs:enumeration value="ELECTRONIC_TRADE_DOCUMENTS"/>
          <xs:enumeration value="EMAIL_NOTIFICATION"/>
          <xs:enumeration value="EXTREME_LENGTH"/>
          <xs:enumeration value="FOOD"/>
          <xs:enumeration value="FREIGHT_GUARANTEE"/>
          <xs:enumeration value="FUTURE_DAY_SHIPMENT"/>
          <xs:enumeration value="HOLD_AT_LOCATION"/>
          <xs:enumeration value="HOME_DELIVERY_PREMIUM"/>
          <xs:enumeration value="INSIDE_DELIVERY"/>
          <xs:enumeration value="INSIDE_PICKUP"/>
          <xs:enumeration value="LIFTGATE_DELIVERY"/>
          <xs:enumeration value="LIFTGATE_PICKUP"/>
          <xs:enumeration value="LIMITED_ACCESS_DELIVERY"/>
          <xs:enumeration value="LIMITED_ACCESS_PICKUP"/>
          <xs:enumeration value="PENDING_SHIPMENT"/>
          <xs:enumeration value="POISON"/>
          <xs:enumeration value="PROTECTION_FROM_FREEZING"/>
          <xs:enumeration value="RETURN_SHIPMENT"/>
          <xs:enumeration value="SATURDAY_DELIVERY"/>
          <xs:enumeration value="SATURDAY_PICKUP"/>
          <xs:enumeration value="TOP_LOAD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShipmentSpecialServicesRequested">
        <xs:annotation>
          <xs:documentation>These special services are available at the shipment level for some or all service types. If the shipper is requesting a special service which requires additional data (such as the COD amount), the shipment special service type must be present in the specialServiceTypes collection, and the supporting detail must be provided in the appropriate sub-object below.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SpecialServiceTypes" type="ns:ShipmentSpecialServiceType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>The types of all special services requested for the enclosing shipment (or other shipment-level transaction).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CodDetail" type="ns:CodDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for a FedEx COD (Collect-On-Delivery) shipment. This element is required when SpecialServiceType.COD is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HoldAtLocationDetail" type="ns:HoldAtLocationDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for a FedEx shipment that is to be held at the destination FedEx location for pickup by the recipient. This element is required when SpecialServiceType.HOLD_AT_LOCATION is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailNotificationDetail" type="ns:EMailNotificationDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Descriptive data required for FedEx to provide email notification to the customer regarding the shipment. This element is required when SpecialServiceType.EMAIL_NOTIFICATION is present in the SpecialServiceTypes collection.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ReturnShipmentDetail" type="ns:ReturnShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data required for FedEx Printed Return Label. This element is required when SpecialServiceType.PRINTED_RETURN_LABEL is present in the SpecialServiceTypes collection</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PendingShipmentDetail" type="ns:PendingShipmentDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>This field should be populated for pending shipments (e.g. e-mail label) It is required by a PENDING_SHIPMENT special service type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ShipmentDryIceDetail" type="ns:ShipmentDryIceDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The number of packages with dry ice and the total weight of the dry ice.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="HomeDeliveryPremiumDetail" type="ns:HomeDeliveryPremiumDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The descriptive data required for FedEx Home Delivery options. This element is required when SpecialServiceType.HOME_DELIVERY_PREMIUM is present in the SpecialServiceTypes collection</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="FlatbedTrailerDetail" type="ns:FlatbedTrailerDetail" minOccurs="0"/>
          <xs:element name="FreightGuaranteeDetail" type="ns:FreightGuaranteeDetail" minOccurs="0"/>
          <xs:element name="EtdDetail" type="ns:EtdDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Electronic Trade document references.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomDeliveryWindowDetail" type="ns:CustomDeliveryWindowDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specification for date or range of dates on which delivery is to be attempted.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentDispositionDetail">
        <xs:annotation>
          <xs:documentation>Each occurrence of this class specifies a particular way in which a kind of shipping document is to be produced and provided.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="DispositionType" type="ns:ShippingDocumentDispositionType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Values in this field specify how to create and return the document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Grouping" type="ns:ShippingDocumentGroupingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how to organize all documents of this type.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="EMailDetail" type="ns:ShippingDocumentEMailDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how to e-mail document images.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PrintDetail" type="ns:ShippingDocumentPrintDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how a queued document is to be printed.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentDispositionType">
        <xs:annotation>
          <xs:documentation>Specifies how to return a shipping document to the caller.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONFIRMED"/>
          <xs:enumeration value="DEFERRED_RETURNED"/>
          <xs:enumeration value="DEFERRED_STORED"/>
          <xs:enumeration value="EMAILED"/>
          <xs:enumeration value="QUEUED"/>
          <xs:enumeration value="RETURNED"/>
          <xs:enumeration value="STORED"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentEMailDetail">
        <xs:annotation>
          <xs:documentation>Specifies how to e-mail shipping documents.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="EMailRecipients" type="ns:ShippingDocumentEMailRecipient" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Provides the roles and email addresses for e-mail recipients.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Grouping" type="ns:ShippingDocumentEMailGroupingType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the convention by which documents are to be grouped as e-mail attachments.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentEMailGroupingType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="BY_RECIPIENT"/>
          <xs:enumeration value="NONE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentEMailRecipient">
        <xs:annotation>
          <xs:documentation>Specifies an individual recipient of e-mailed shipping document(s).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="RecipientType" type="ns:EMailNotificationRecipientType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the relationship of this recipient in the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Address" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Address to which the document is to be sent.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentFormat">
        <xs:annotation>
          <xs:documentation>Specifies characteristics of a shipping document to be produced.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Dispositions" type="ns:ShippingDocumentDispositionDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies how to create, organize, and return the document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TopOfPageOffset" type="ns:LinearMeasure" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies how far down the page to move the beginning of the image; allows for printing on letterhead and other pre-printed stock.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="ImageType" type="ns:ShippingDocumentImageType" minOccurs="0"/>
          <xs:element name="StockType" type="ns:ShippingDocumentStockType" minOccurs="0"/>
          <xs:element name="ProvideInstructions" type="xs:boolean" minOccurs="0">
            <xs:annotation>
              <xs:documentation>For those shipping document types which have both a "form" and "instructions" component (e.g. NAFTA Certificate of Origin and General Agency Agreement), this field indicates whether to provide the instructions.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Governs the language to be used for this individual document, independently from other content returned for the same shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentGroupingType">
        <xs:annotation>
          <xs:documentation>Specifies how to organize all shipping documents of the same type.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CONSOLIDATED_BY_DOCUMENT_TYPE"/>
          <xs:enumeration value="INDIVIDUAL"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="ShippingDocumentImageType">
        <xs:annotation>
          <xs:documentation>Specifies the image format used for a shipping document.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="DPL"/>
          <xs:enumeration value="EPL2"/>
          <xs:enumeration value="PDF"/>
          <xs:enumeration value="PNG"/>
          <xs:enumeration value="ZPLII"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="ShippingDocumentPrintDetail">
        <xs:annotation>
          <xs:documentation>Specifies printing options for a shipping document.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="PrinterId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Provides environment-specific printer identification.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="ShippingDocumentSpecification">
        <xs:annotation>
          <xs:documentation>Contains all data required for additional (non-label) shipping documents to be produced in conjunction with a specific shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ShippingDocumentTypes" type="ns:RequestedShippingDocumentType" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Indicates the types of shipping documents requested by the shipper.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CertificateOfOrigin" type="ns:CertificateOfOriginDetail" minOccurs="0"/>
          <xs:element name="CommercialInvoiceDetail" type="ns:CommercialInvoiceDetail" minOccurs="0"/>
          <xs:element name="CustomPackageDocumentDetail" type="ns:CustomDocumentDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the production of each package-level custom document (the same specification is used for all packages).</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="CustomShipmentDocumentDetail" type="ns:CustomDocumentDetail" minOccurs="0" maxOccurs="unbounded">
            <xs:annotation>
              <xs:documentation>Specifies the production of a shipment-level custom document.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="GeneralAgencyAgreementDetail" type="ns:GeneralAgencyAgreementDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Details pertaining to the GAA.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="NaftaCertificateOfOriginDetail" type="ns:NaftaCertificateOfOriginDetail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Details pertaining to NAFTA COO.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Op900Detail" type="ns:Op900Detail" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Specifies the production of the OP-900 document for hazardous materials packages.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="ShippingDocumentStockType">
        <xs:annotation>
          <xs:documentation>Specifies the type of paper (stock) on which a document will be printed.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="OP_900_LG_B"/>
          <xs:enumeration value="OP_900_LL_B"/>
          <xs:enumeration value="PAPER_4X6"/>
          <xs:enumeration value="PAPER_LETTER"/>
          <xs:enumeration value="STOCK_4X6"/>
          <xs:enumeration value="STOCK_4X6.75_LEADING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X6.75_TRAILING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X8"/>
          <xs:enumeration value="STOCK_4X9_LEADING_DOC_TAB"/>
          <xs:enumeration value="STOCK_4X9_TRAILING_DOC_TAB"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SignatureOptionDetail">
        <xs:annotation>
          <xs:documentation>The descriptive data required for FedEx delivery signature services.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="OptionType" type="ns:SignatureOptionType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the delivery signature services option selected by the customer for this shipment. See OptionType for the list of valid values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="SignatureReleaseNumber" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the delivery signature release authorization number.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>10</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SignatureOptionType">
        <xs:annotation>
          <xs:documentation>Identifies the delivery signature services options offered by FedEx.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADULT"/>
          <xs:enumeration value="DIRECT"/>
          <xs:enumeration value="INDIRECT"/>
          <xs:enumeration value="NO_SIGNATURE_REQUIRED"/>
          <xs:enumeration value="SERVICE_DEFAULT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SmartPostAncillaryEndorsementType">
        <xs:annotation>
          <xs:documentation>These values are mutually exclusive; at most one of them can be attached to a SmartPost shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDRESS_CORRECTION"/>
          <xs:enumeration value="CARRIER_LEAVE_IF_NO_RESPONSE"/>
          <xs:enumeration value="CHANGE_SERVICE"/>
          <xs:enumeration value="FORWARDING_SERVICE"/>
          <xs:enumeration value="RETURN_SERVICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SmartPostIndiciaType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="MEDIA_MAIL"/>
          <xs:enumeration value="PARCEL_RETURN"/>
          <xs:enumeration value="PARCEL_SELECT"/>
          <xs:enumeration value="PRESORTED_BOUND_PRINTED_MATTER"/>
          <xs:enumeration value="PRESORTED_STANDARD"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="SmartPostShipmentDetail">
        <xs:annotation>
          <xs:documentation>Data required for shipments handled under the SMART_POST and GROUND_SMART_POST service types.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Indicia" type="ns:SmartPostIndiciaType" minOccurs="0"/>
          <xs:element name="AncillaryEndorsement" type="ns:SmartPostAncillaryEndorsementType" minOccurs="0"/>
          <xs:element name="HubId" type="xs:string" minOccurs="0"/>
          <xs:element name="CustomerManifestId" type="xs:string" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SpecialRatingAppliedType">
        <xs:annotation>
          <xs:documentation>Indicates which special rating cases applied to this shipment.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="FIXED_FUEL_SURCHARGE"/>
          <xs:enumeration value="IMPORT_PRICING"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Surcharge">
        <xs:annotation>
          <xs:documentation>Identifies each surcharge applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="SurchargeType" type="ns:SurchargeType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The type of surcharge applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Level" type="ns:SurchargeLevelType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The amount of the surcharge applied to the shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="SurchargeLevelType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="PACKAGE"/>
          <xs:enumeration value="SHIPMENT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="SurchargeType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="ADDITIONAL_HANDLING"/>
          <xs:enumeration value="ANCILLARY_FEE"/>
          <xs:enumeration value="APPOINTMENT_DELIVERY"/>
          <xs:enumeration value="BROKER_SELECT_OPTION"/>
          <xs:enumeration value="CANADIAN_DESTINATION"/>
          <xs:enumeration value="CLEARANCE_ENTRY_FEE"/>
          <xs:enumeration value="COD"/>
          <xs:enumeration value="CUT_FLOWERS"/>
          <xs:enumeration value="DANGEROUS_GOODS"/>
          <xs:enumeration value="DELIVERY_AREA"/>
          <xs:enumeration value="DELIVERY_CONFIRMATION"/>
          <xs:enumeration value="DOCUMENTATION_FEE"/>
          <xs:enumeration value="DRY_ICE"/>
          <xs:enumeration value="EMAIL_LABEL"/>
          <xs:enumeration value="EUROPE_FIRST"/>
          <xs:enumeration value="EXCESS_VALUE"/>
          <xs:enumeration value="EXHIBITION"/>
          <xs:enumeration value="EXPORT"/>
          <xs:enumeration value="EXTREME_LENGTH"/>
          <xs:enumeration value="FEDEX_TAG"/>
          <xs:enumeration value="FICE"/>
          <xs:enumeration value="FLATBED"/>
          <xs:enumeration value="FREIGHT_GUARANTEE"/>
          <xs:enumeration value="FREIGHT_ON_VALUE"/>
          <xs:enumeration value="FUEL"/>
          <xs:enumeration value="HOLD_AT_LOCATION"/>
          <xs:enumeration value="HOME_DELIVERY_APPOINTMENT"/>
          <xs:enumeration value="HOME_DELIVERY_DATE_CERTAIN"/>
          <xs:enumeration value="HOME_DELIVERY_EVENING"/>
          <xs:enumeration value="INSIDE_DELIVERY"/>
          <xs:enumeration value="INSIDE_PICKUP"/>
          <xs:enumeration value="INSURED_VALUE"/>
          <xs:enumeration value="INTERHAWAII"/>
          <xs:enumeration value="LIFTGATE_DELIVERY"/>
          <xs:enumeration value="LIFTGATE_PICKUP"/>
          <xs:enumeration value="LIMITED_ACCESS_DELIVERY"/>
          <xs:enumeration value="LIMITED_ACCESS_PICKUP"/>
          <xs:enumeration value="METRO_DELIVERY"/>
          <xs:enumeration value="METRO_PICKUP"/>
          <xs:enumeration value="NON_MACHINABLE"/>
          <xs:enumeration value="OFFSHORE"/>
          <xs:enumeration value="ON_CALL_PICKUP"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="OUT_OF_DELIVERY_AREA"/>
          <xs:enumeration value="OUT_OF_PICKUP_AREA"/>
          <xs:enumeration value="OVERSIZE"/>
          <xs:enumeration value="OVER_DIMENSION"/>
          <xs:enumeration value="PIECE_COUNT_VERIFICATION"/>
          <xs:enumeration value="PRE_DELIVERY_NOTIFICATION"/>
          <xs:enumeration value="PRIORITY_ALERT"/>
          <xs:enumeration value="PROTECTION_FROM_FREEZING"/>
          <xs:enumeration value="REGIONAL_MALL_DELIVERY"/>
          <xs:enumeration value="REGIONAL_MALL_PICKUP"/>
          <xs:enumeration value="RESIDENTIAL_DELIVERY"/>
          <xs:enumeration value="RESIDENTIAL_PICKUP"/>
          <xs:enumeration value="RETURN_LABEL"/>
          <xs:enumeration value="SATURDAY_DELIVERY"/>
          <xs:enumeration value="SATURDAY_PICKUP"/>
          <xs:enumeration value="SIGNATURE_OPTION"/>
          <xs:enumeration value="TARP"/>
          <xs:enumeration value="THIRD_PARTY_CONSIGNEE"/>
          <xs:enumeration value="TRANSMART_SERVICE_FEE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Tax">
        <xs:annotation>
          <xs:documentation>Identifies each tax applied to the shipment.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="TaxType" type="ns:TaxType" minOccurs="0"/>
          <xs:element name="Description" type="xs:string" minOccurs="0"/>
          <xs:element name="Amount" type="ns:Money" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TaxType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXPORT"/>
          <xs:enumeration value="GST"/>
          <xs:enumeration value="HST"/>
          <xs:enumeration value="INTRACOUNTRY"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PST"/>
          <xs:enumeration value="VAT"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TaxesOrMiscellaneousChargeType">
        <xs:annotation>
          <xs:documentation>Specifice the kind of tax or miscellaneous charge being reported on a Commercial Invoice.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="COMMISSIONS"/>
          <xs:enumeration value="DISCOUNTS"/>
          <xs:enumeration value="HANDLING_FEES"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="ROYALTIES_AND_LICENSE_FEES"/>
          <xs:enumeration value="TAXES"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TaxpayerIdentification">
        <xs:annotation>
          <xs:documentation>The descriptive data for taxpayer identification information.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="TinType" type="ns:TinType" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the category of the taxpayer identification number. See TinType for the list of values.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Number" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the taxpayer identification number.</xs:documentation>
              <xs:appinfo>
                <xs:MaxLength>18</xs:MaxLength>
              </xs:appinfo>
            </xs:annotation>
          </xs:element>
          <xs:element name="Usage" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Identifies the usage of Tax Identification Number in Shipment processing</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TermsOfSaleType">
        <xs:annotation>
          <xs:documentation>
            Required for dutiable international express or ground shipment. This field is not applicable to an international PIB (document) or a non-document which does not require a commercial invoice express shipment.
            CFR_OR_CPT (Cost and Freight/Carriage Paid TO)
            CIF_OR_CIP (Cost Insurance and Freight/Carraige Insurance Paid)
            DDP (Delivered Duty Paid)
            DDU (Delivered Duty Unpaid)
            EXW (Ex Works)
            FOB_OR_FCA (Free On Board/Free Carrier)
          </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CFR_OR_CPT"/>
          <xs:enumeration value="CIF_OR_CIP"/>
          <xs:enumeration value="DDP"/>
          <xs:enumeration value="DDU"/>
          <xs:enumeration value="EXW"/>
          <xs:enumeration value="FOB_OR_FCA"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="TinType">
        <xs:annotation>
          <xs:documentation>Identifies the category of the taxpayer identification number.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="BUSINESS_NATIONAL"/>
          <xs:enumeration value="BUSINESS_STATE"/>
          <xs:enumeration value="PERSONAL_NATIONAL"/>
          <xs:enumeration value="PERSONAL_STATE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TrackingId">
        <xs:sequence>
          <xs:element name="TrackingIdType" type="ns:TrackingIdType" minOccurs="0"/>
          <xs:element name="FormId" type="xs:string" minOccurs="0"/>
          <xs:element name="TrackingNumber" type="xs:string" minOccurs="1"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TrackingIdType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="EXPRESS"/>
          <xs:enumeration value="FEDEX"/>
          <xs:enumeration value="GROUND"/>
          <xs:enumeration value="USPS"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="TransactionDetail">
        <xs:annotation>
          <xs:documentation>Descriptive data for this customer transaction. The TransactionDetail from the request is echoed back to the caller in the corresponding reply.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="CustomerTransactionId" type="xs:string" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Free form text to be echoed back in the reply. Used to match requests and replies.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Localization" type="ns:Localization" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Governs data payload language/translations (contrasted with ClientDetail.localization, which governs Notification.localizedMessage language selection).</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="TransitTimeType">
        <xs:annotation>
          <xs:documentation>Time in transit from pickup to delivery.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="EIGHTEEN_DAYS"/>
          <xs:enumeration value="EIGHT_DAYS"/>
          <xs:enumeration value="ELEVEN_DAYS"/>
          <xs:enumeration value="FIFTEEN_DAYS"/>
          <xs:enumeration value="FIVE_DAYS"/>
          <xs:enumeration value="FOURTEEN_DAYS"/>
          <xs:enumeration value="FOUR_DAYS"/>
          <xs:enumeration value="NINETEEN_DAYS"/>
          <xs:enumeration value="NINE_DAYS"/>
          <xs:enumeration value="ONE_DAY"/>
          <xs:enumeration value="SEVENTEEN_DAYS"/>
          <xs:enumeration value="SEVEN_DAYS"/>
          <xs:enumeration value="SIXTEEN_DAYS"/>
          <xs:enumeration value="SIX_DAYS"/>
          <xs:enumeration value="TEN_DAYS"/>
          <xs:enumeration value="THIRTEEN_DAYS"/>
          <xs:enumeration value="THREE_DAYS"/>
          <xs:enumeration value="TWELVE_DAYS"/>
          <xs:enumeration value="TWENTY_DAYS"/>
          <xs:enumeration value="TWO_DAYS"/>
          <xs:enumeration value="UNKNOWN"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="UploadDocumentDetail">
        <xs:sequence>
          <xs:element name="LineNumber" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="CustomerReference" type="xs:string" minOccurs="0"/>
          <xs:element name="DocumentProducer" type="ns:UploadDocumentProducerType" minOccurs="0"/>
          <xs:element name="DocumentType" type="ns:UploadDocumentType" minOccurs="0"/>
          <xs:element name="FileName" type="xs:string" minOccurs="0"/>
          <xs:element name="DocumentContent" type="xs:base64Binary" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="UploadDocumentIdProducer">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="FEDEX_CSHP"/>
          <xs:enumeration value="FEDEX_GTM"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:simpleType name="UploadDocumentProducerType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUSTOMER"/>
          <xs:enumeration value="FEDEX_CLS"/>
          <xs:enumeration value="FEDEX_GTM"/>
          <xs:enumeration value="OTHER"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="UploadDocumentReferenceDetail">
        <xs:sequence>
          <xs:element name="LineNumber" type="xs:nonNegativeInteger" minOccurs="0"/>
          <xs:element name="CustomerReference" type="xs:string" minOccurs="0"/>
          <xs:element name="DocumentProducer" type="ns:UploadDocumentProducerType" minOccurs="0"/>
          <xs:element name="DocumentType" type="ns:UploadDocumentType" minOccurs="0"/>
          <xs:element name="DocumentId" type="xs:string" minOccurs="0"/>
          <xs:element name="DocumentIdProducer" type="ns:UploadDocumentIdProducer" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="UploadDocumentType">
        <xs:restriction base="xs:string">
          <xs:enumeration value="CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="COMMERCIAL_INVOICE"/>
          <xs:enumeration value="ETD_LABEL"/>
          <xs:enumeration value="NAFTA_CERTIFICATE_OF_ORIGIN"/>
          <xs:enumeration value="OTHER"/>
          <xs:enumeration value="PRO_FORMA_INVOICE"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="VariableHandlingChargeDetail">
        <xs:annotation>
          <xs:documentation>This definition of variable handling charge detail is intended for use in Jan 2011 corp load.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="FixedValue" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Used with Variable handling charge type of FIXED_VALUE. Contains the amount to be added to the freight charge. Contains 2 explicit decimal positions with a total max length of 10 including the decimal.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="PercentValue" type="xs:decimal" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Actual percentage (10 means 10%, which is a mutiplier of 0.1)</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateElementBasis" type="ns:RateElementBasisType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Select the value from a set of rate data to which the percentage is applied.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="RateTypeBasis" type="ns:RateTypeBasisType" minOccurs="0">
            <xs:annotation>
              <xs:documentation>Select the type of rate from which the element is to be selected.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="VariableHandlingCharges">
        <xs:annotation>
          <xs:documentation>The variable handling charges calculated based on the type variable handling charges requested.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="VariableHandlingCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The variable handling charge amount calculated based on the requested variable handling charge detail.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="TotalCustomerCharge" type="ns:Money" minOccurs="0">
            <xs:annotation>
              <xs:documentation>The calculated varibale handling charge plus the net charge.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="Volume">
        <xs:annotation>
          <xs:documentation>Three-dimensional volume/cubic measurement.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Units" type="ns:VolumeUnits" minOccurs="0"/>
          <xs:element name="Value" type="xs:decimal" minOccurs="0"/>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="VolumeUnits">
        <xs:annotation>
          <xs:documentation>Units of three-dimensional volume/cubic measure.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="CUBIC_FT"/>
          <xs:enumeration value="CUBIC_M"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="Weight">
        <xs:annotation>
          <xs:documentation>The descriptive data for the heaviness of an object.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Units" type="ns:WeightUnits" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the unit of measure associated with a weight value.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Value" type="xs:decimal" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the weight value of a package/shipment.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:simpleType name="WeightUnits">
        <xs:annotation>
          <xs:documentation>Identifies the unit of measure associated with a weight value. See WeightUnits for the list of valid enumerated values.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
          <xs:enumeration value="KG"/>
          <xs:enumeration value="LB"/>
        </xs:restriction>
      </xs:simpleType>
      <xs:complexType name="WebAuthenticationDetail">
        <xs:annotation>
          <xs:documentation>Used in authentication of the sender's identity.</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="UserCredential" type="ns:WebAuthenticationCredential" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Credential used to authenticate a specific software application. This value is provided by FedEx after registration.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="WebAuthenticationCredential">
        <xs:annotation>
          <xs:documentation>Two part authentication string used for the sender's identity</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="Key" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifying part of authentication credential. This value is provided by FedEx after registration</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Password" type="xs:string" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Secret part of authentication key. This value is provided by FedEx after registration.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
      <xs:complexType name="VersionId">
        <xs:annotation>
          <xs:documentation>Identifies the version/level of a service operation expected by a caller (in each request) and performed by the callee (in each reply).</xs:documentation>
        </xs:annotation>
        <xs:sequence>
          <xs:element name="ServiceId" type="xs:string" fixed="crs" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies a system or sub-system which performs an operation.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Major" type="xs:int" fixed="10" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service business level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Intermediate" type="xs:int" fixed="0" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service interface level.</xs:documentation>
            </xs:annotation>
          </xs:element>
          <xs:element name="Minor" type="xs:int" fixed="0" minOccurs="1">
            <xs:annotation>
              <xs:documentation>Identifies the service code level.</xs:documentation>
            </xs:annotation>
          </xs:element>
        </xs:sequence>
      </xs:complexType>
    </xs:schema>
  </types>
  <message name="RateRequest">
    <part name="RateRequest" element="ns:RateRequest"/>
  </message>
  <message name="RateReply">
    <part name="RateReply" element="ns:RateReply"/>
  </message>
  <portType name="RatePortType">
    <operation name="getRates" parameterOrder="RateRequest">
      <input message="ns:RateRequest"/>
      <output message="ns:RateReply"/>
    </operation>
  </portType>
  <binding name="RateServiceSoapBinding" type="ns:RatePortType">
    <s1:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <operation name="getRates">
      <s1:operation soapAction="getRates" style="document"/>
      <input>
        <s1:body use="literal"/>
      </input>
      <output>
        <s1:body use="literal"/>
      </output>
    </operation>
  </binding>
  <service name="RateService">
    <port name="RateServicePort" binding="ns:RateServiceSoapBinding">
      <s1:address location="https://wsbeta.fedex.com:443/web-services/rate"/>
    </port>
  </service>
</definitions>