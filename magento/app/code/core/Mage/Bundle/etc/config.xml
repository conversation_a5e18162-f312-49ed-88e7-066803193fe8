<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Bundle
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
    <modules>
        <Mage_Bundle>
            <version>1.6.0.0.1</version>
        </Mage_Bundle>
    </modules>
    <global>
        <models>
            <bundle>
                <class>Mage_Bundle_Model</class>
                <resourceModel>bundle_resource</resourceModel>
            </bundle>
            <bundle_resource>
                <class>Mage_Bundle_Model_Resource</class>
                <deprecatedNode>bundle_mysql4</deprecatedNode>
                <entities>
                    <option>
                        <table>catalog_product_bundle_option</table>
                    </option>
                    <option_value>
                        <table>catalog_product_bundle_option_value</table>
                    </option_value>
                    <selection>
                        <table>catalog_product_bundle_selection</table>
                    </selection>
                    <selection_price>
                        <table>catalog_product_bundle_selection_price</table>
                    </selection_price>
                    <price_index>
                        <table>catalog_product_bundle_price_index</table>
                    </price_index>
                    <stock_index>
                        <table>catalog_product_bundle_stock_index</table>
                    </stock_index>
                    <price_indexer_idx>
                        <table>catalog_product_index_price_bundle_idx</table>
                    </price_indexer_idx>
                    <price_indexer_tmp>
                        <table>catalog_product_index_price_bundle_tmp</table>
                    </price_indexer_tmp>
                    <selection_indexer_idx>
                        <table>catalog_product_index_price_bundle_sel_idx</table>
                    </selection_indexer_idx>
                    <selection_indexer_tmp>
                        <table>catalog_product_index_price_bundle_sel_tmp</table>
                    </selection_indexer_tmp>
                    <option_indexer_idx>
                        <table>catalog_product_index_price_bundle_opt_idx</table>
                    </option_indexer_idx>
                    <option_indexer_tmp>
                        <table>catalog_product_index_price_bundle_opt_tmp</table>
                    </option_indexer_tmp>
                </entities>
            </bundle_resource>
        </models>
        <resources>
            <bundle_setup>
                <setup>
                    <module>Mage_Bundle</module>
                    <class>Mage_Catalog_Model_Resource_Setup</class>
                </setup>
            </bundle_setup>
        </resources>
        <blocks>
            <bundle>
                <class>Mage_Bundle_Block</class>
            </bundle>
        </blocks>
        <catalog>
            <product>
                <type>
                    <bundle translate="label" module="bundle">
                        <label>Bundle Product</label>
                        <model>bundle/product_type</model>
                        <composite>1</composite>
                        <allowed_selection_types>
                            <simple/>
                            <virtual/>
                        </allowed_selection_types>
                        <price_model>bundle/product_price</price_model>
                        <index_data_retreiver>bundle/catalogIndex_data_bundle</index_data_retreiver>
                        <index_priority>40</index_priority>
                        <price_indexer>bundle/indexer_price</price_indexer>
                        <stock_indexer>bundle/indexer_stock</stock_indexer>
                    </bundle>
                </type>
                <options>
                    <bundle>
                        <types>
                            <select translate="label" module="bundle">
                                <label>Drop-down</label>
                            </select>
                            <radio translate="label" module="bundle">
                                <label>Radio Buttons</label>
                            </radio>
                            <checkbox translate="label" module="bundle">
                                <label>Checkbox</label>
                            </checkbox>
                            <multi translate="label" module="bundle">
                                <label>Multiple Select</label>
                            </multi>
                        </types>
                    </bundle>
                </options>
            </product>
        </catalog>
        <sales>
            <quote>
                <item>
                    <product_attributes>
                        <price_view/>
                        <price_type/>
                        <shipment_type/>
                        <weight_type/>
                        <sku_type/>
                    </product_attributes>
                </item>
            </quote>
        </sales>
        <pdf>
            <invoice>
                <bundle>bundle/sales_order_pdf_items_invoice</bundle>
            </invoice>
            <shipment>
                <bundle>bundle/sales_order_pdf_items_shipment</bundle>
            </shipment>
            <creditmemo>
                <bundle>bundle/sales_order_pdf_items_creditmemo</bundle>
            </creditmemo>
        </pdf>
        <events>
            <catalogindex_plain_reindex_after>
                <observers>
                    <bundle>
                        <class>bundle/observer</class>
                        <method>catalogIndexPlainReindexAfter</method>
                    </bundle>
                </observers>
            </catalogindex_plain_reindex_after>
        </events>
    </global>
    <frontend>
        <translate>
            <modules>
                <Mage_Bundle>
                    <files>
                        <default>Mage_Bundle.csv</default>
                    </files>
                </Mage_Bundle>
            </modules>
        </translate>
        <layout>
            <updates>
                <bundle>
                    <file>bundle.xml</file>
                </bundle>
            </updates>
        </layout>
        <product>
            <collection>
                <attributes>
                    <price_view/>
                    <price_type/>
                </attributes>
            </collection>
        </product>
        <events>
            <catalog_product_upsell>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>appendUpsellProducts</method>
                    </bundle_observer>
                </observers>
            </catalog_product_upsell>
            <sales_convert_quote_item_to_order_item>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>appendBundleSelectionData</method>
                    </bundle_observer>
                </observers>
            </sales_convert_quote_item_to_order_item>
            <catalog_product_collection_load_after>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>loadProductOptions</method>
                    </bundle_observer>
                </observers>
            </catalog_product_collection_load_after>
            <product_option_renderer_init>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>initOptionRenderer</method>
                    </bundle_observer>
                </observers>
            </product_option_renderer_init>
        </events>
    </frontend>
    <admin>
        <fieldsets>
            <catalog_product_dataflow>
                <is_in_stock>
                    <product_type>
                        <bundle/>
                    </product_type>
                </is_in_stock>
                <manage_stock>
                    <product_type>
                        <bundle/>
                    </product_type>
                </manage_stock>
            </catalog_product_dataflow>
        </fieldsets>
        <routers>
            <adminhtml>
                <args>
                    <modules>
                        <Mage_Bundle before="Mage_Adminhtml">Mage_Bundle_Adminhtml</Mage_Bundle>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>
    <adminhtml>
        <translate>
            <modules>
                <Mage_Bundle>
                    <files>
                        <default>Mage_Bundle.csv</default>
                    </files>
                </Mage_Bundle>
            </modules>
        </translate>
        <layout>
            <updates>
                <bundle>
                    <file>bundle.xml</file>
                </bundle>
            </updates>
        </layout>
        <sales>
            <order>
                <create>
                    <available_product_types>
                        <bundle/>
                    </available_product_types>
                </create>
            </order>
        </sales>
        <events>
            <catalog_product_prepare_save>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>prepareProductSave</method>
                    </bundle_observer>
                </observers>
            </catalog_product_prepare_save>
            <sales_convert_quote_item_to_order_item>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>appendBundleSelectionData</method>
                    </bundle_observer>
                </observers>
            </sales_convert_quote_item_to_order_item>
            <catalog_model_product_duplicate>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>duplicateProduct</method>
                    </bundle_observer>
                </observers>
            </catalog_model_product_duplicate>
            <catalog_product_edit_action>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>setAttributeTabBlock</method>
                    </bundle_observer>
                </observers>
            </catalog_product_edit_action>
            <catalog_product_new_action>
                <observers>
                    <bundle_observer>
                        <class>bundle/observer</class>
                        <method>setAttributeTabBlock</method>
                    </bundle_observer>
                </observers>
            </catalog_product_new_action>
        </events>
    </adminhtml>
</config>
