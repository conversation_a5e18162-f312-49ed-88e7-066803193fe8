<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    Mage
 * @package     Mage_Paypal
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
-->
<config>
   <sections>
        <payment>
            <groups>
                <paypal_notice translate="label" module="paypal">
                    <frontend_model>paypal/adminhtml_system_config_fieldset_hint</frontend_model>
                    <help_link>https://www.paypal-marketing.com/emarketing/partner/na/merchantlineup/home.page#mainTab=checkoutlineup&amp;subTab=newlineup</help_link>
                    <sort_order>0</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                </paypal_notice>
                <account translate="label" module="paypal">
                    <label>Merchant Location</label>
                    <fieldset_css>paypal-config</fieldset_css>
                    <expanded>1</expanded>
                    <frontend_type>text</frontend_type>
                    <sort_order>5</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <frontend_model>paypal/adminhtml_system_config_fieldset_location</frontend_model>
                    <fields>
                        <merchant_country translate="label comment">
                            <label>Merchant Country</label>
                            <config_path>paypal/general/merchant_country</config_path>
                            <comment>If not specified, Default Country from General Config will be used</comment>
                            <frontend_type>select</frontend_type>
                            <frontend_model>paypal/adminhtml_system_config_field_country</frontend_model>
                            <source_model>paypal/system_config_source_merchantCountry</source_model>
                            <backend_model>paypal/system_config_backend_merchantCountry</backend_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <sort_order>5</sort_order>
                        </merchant_country>
                    </fields>
                </account>

                <paypal_group_all_in_one translate="label comment paypal_title" module="paypal">
                    <label><![CDATA[PayPal All-in-One Payment Solutions&nbsp;&nbsp;<i>Accept and process credit cards and PayPal payments.</i>]]></label>
                    <expanded>1</expanded>
                    <sort_order>10</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <frontend_class>complex</frontend_class>
                    <frontend_model>paypal/adminhtml_system_config_fieldset_group</frontend_model>
                    <comment>Choose a secure bundled payment solution for your business.</comment>
                    <help_url>https://www.paypal-marketing.com/emarketing/partner/na/merchantlineup/home.page#mainTab=checkoutlineup&amp;subTab=newlineup</help_url>
                    <fields/>
                </paypal_group_all_in_one>
                <paypal_payment_gateways translate="label comment paypal_title" module="paypal">
                    <label>PayPal Payment Gateways</label>
                    <sort_order>15</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <frontend_class>complex</frontend_class>
                    <frontend_model>paypal/adminhtml_system_config_fieldset_group</frontend_model>
                    <comment>Process payments using your own internet merchant account.</comment>
                    <help_url>https://merchant.paypal.com/cgi-bin/marketingweb?cmd=_render-content&amp;content_ID=merchant/payment_gateway</help_url>
                    <fields/>
                </paypal_payment_gateways>
                <paypal_alternative_payment_methods translate="label comment paypal_title" module="paypal">
                    <label>PayPal Express Checkout</label>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <expanded>1</expanded>
                    <frontend_class>complex</frontend_class>
                    <frontend_model>paypal/adminhtml_system_config_fieldset_group</frontend_model>
                    <comment>Add another payment method to your existing solution or as a stand-alone option.</comment>
                    <paypal_title>Select a Payment Solution</paypal_title>
                    <help_url>https://merchant.paypal.com/cgi-bin/marketingweb?cmd=_render-content&amp;content_ID=merchant/express_checkout</help_url>
                    <fields/>
                </paypal_alternative_payment_methods>
                <paypal_payment_solutions translate="label comment paypal_title" module="paypal">
                    <label>PayPal Payment Solutions</label>
                    <sort_order>10</sort_order>
                    <expanded>1</expanded>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <frontend_class>complex</frontend_class>
                    <frontend_model>paypal/adminhtml_system_config_fieldset_group</frontend_model>
                    <comment>Add another payment method to your existing solution or as a stand-alone option.</comment>
                    <paypal_title>Select a Payment Solution</paypal_title>
                    <help_url>https://www.paypal-marketing.com/emarketing/partner/na/merchantlineup/home.page#mainTab=checkoutlineup&amp;subTab=newlineup</help_url>
                    <fields/>
                </paypal_payment_solutions>

                <paypal_payments>
                    <payflow_advanced type="group" translate="label comment">
                        <label>Payments Advanced (Includes Express Checkout)</label>
                        <frontend_type>text</frontend_type>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>30</sort_order>
                        <frontend_class>pp-method-general</frontend_class>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_payment</frontend_model>
                        <learn_more_link>https://www.paypal.com/webapps/mpp/referral/paypal-payments-advanced?partner_id=NB9WWHYEMVUMS</learn_more_link>
                        <comment>Accept payments with a PCI-compliant checkout that keeps customers on your site.</comment>
                        <group>paypal_group_all_in_one</group>
                        <activity_path>payment/payflow_advanced/active</activity_path>
                        <backend_config>
                            <US>
                                <include>1</include>
                            </US>
                        </backend_config>
                        <fields>
                            <required_settings type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>10</sort_order>
                                <fields>
                                    <payments_advanced type="group" translate="label">
                                        <label>Payments Advanced</label>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>10</sort_order>
                                        <fields>
                                            <partner translate="label">
                                                <label>Partner</label>
                                                <config_path>payment/payflow_advanced/partner</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>10</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </partner>
                                            <vendor translate="label">
                                                <label>Vendor</label>
                                                <config_path>payment/payflow_advanced/vendor</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </vendor>
                                            <user translate="label comment tooltip">
                                                <label>User</label>
                                                <comment>PayPal recommends that you set up an additional User on your account at manager.paypal.com</comment>
                                                <tooltip>PayPal recommends you set up an additional User on your account at manager.paypal.com, instead of entering your admin username and password here. This will enhance your security and prevent service interruptions if you later change your password. If you do not want to set up an additional User, you can re-enter your Merchant Login here.</tooltip>
                                                <frontend_type>text</frontend_type>
                                                <config_path>payment/payflow_advanced/user</config_path>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </user>
                                            <pwd translate="label">
                                                <label>Password</label>
                                                <config_path>payment/payflow_advanced/pwd</config_path>
                                                <frontend_type>obscure</frontend_type>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </pwd>
                                            <sandbox_flag translate="label">
                                                <label>Test Mode</label>
                                                <config_path>payment/payflow_advanced/sandbox_flag</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </sandbox_flag>
                                            <use_proxy translate="label">
                                                <label>Use Proxy</label>
                                                <config_path>payment/payflow_advanced/use_proxy</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </use_proxy>
                                            <proxy_host translate="label">
                                                <label>Proxy Host</label>
                                                <config_path>payment/payflow_advanced/proxy_host</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                            </proxy_host>
                                            <proxy_port translate="label">
                                                <label>Proxy Port</label>
                                                <config_path>payment/payflow_advanced/proxy_port</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>80</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                            </proxy_port>
                                            <payflow_advanced_info>
                                                <frontend_model>paypal/adminhtml_system_config_payflowlink_advanced</frontend_model>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>90</sort_order>
                                            </payflow_advanced_info>
                                        </fields>
                                    </payments_advanced>
                                    <express type="group" translate="label">
                                        <label>Express Checkout</label>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>20</sort_order>
                                        <fields>
                                            <business_account translate="label comment tooltip">
                                                <label>Email Associated with PayPal Merchant Account</label>
                                                <comment><![CDATA[<a href="http://www.magentocommerce.com/paypal">Start accepting payments via PayPal!</a>]]></comment>
                                                <tooltip>Don't have a PayPal account? Simply enter your email address.</tooltip>
                                                <config_path>paypal/general/business_account</config_path>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>10</sort_order>
                                                <validate>validate-email</validate>
                                                <shared>1</shared>
                                            </business_account>
                                            <api_authentication translate="label">
                                                <label>API Authentication Methods</label>
                                                <config_path>paypal/wpp/api_authentication</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>paypal/config::getApiAuthenticationMethods</source_model>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </api_authentication>
                                            <api_username translate="label">
                                                <label>API Username</label>
                                                <config_path>paypal/wpp/api_username</config_path>
                                                <frontend_type>obscure</frontend_type>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </api_username>
                                            <api_password translate="label">
                                                <label>API Password</label>
                                                <config_path>paypal/wpp/api_password</config_path>
                                                <frontend_type>obscure</frontend_type>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </api_password>
                                            <api_signature translate="label">
                                                <label>API Signature</label>
                                                <config_path>paypal/wpp/api_signature</config_path>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <frontend_type>obscure</frontend_type>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><api_authentication>0</api_authentication></depends>
                                                <shared>1</shared>
                                            </api_signature>
                                            <api_cert translate="label">
                                                <label>API Certificate</label>
                                                <config_path>paypal/wpp/api_cert</config_path>
                                                <frontend_type>file</frontend_type>
                                                <backend_model>paypal/system_config_backend_cert</backend_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><api_authentication>1</api_authentication></depends>
                                                <shared>1</shared>
                                            </api_cert>
                                            <api_wizard translate="button_label sandbox_button_label">
                                                <label></label>
                                                <button_label>Get Credentials from PayPal</button_label>
                                                <button_url><![CDATA[https://www.paypal.com/us/cgi-bin/webscr?cmd=_login-api-run]]></button_url>
                                                <sandbox_button_label>Sandbox Credentials</sandbox_button_label>
                                                <sandbox_button_url><![CDATA[https://www.sandbox.paypal.com/us/cgi-bin/webscr?cmd=_login-api-run]]></sandbox_button_url>
                                                <frontend_model>paypal/adminhtml_system_config_apiWizard</frontend_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </api_wizard>
                                            <sandbox_flag translate="label">
                                                <label>Sandbox Mode</label>
                                                <config_path>paypal/wpp/sandbox_flag</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>80</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </sandbox_flag>
                                            <use_proxy translate="label">
                                                <label>API Uses Proxy</label>
                                                <config_path>paypal/wpp/use_proxy</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>90</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </use_proxy>
                                            <proxy_host translate="label">
                                                <label>Proxy Host</label>
                                                <config_path>paypal/wpp/proxy_host</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>100</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                                <shared>1</shared>
                                            </proxy_host>
                                            <proxy_port translate="label">
                                                <label>Proxy Port</label>
                                                <config_path>paypal/wpp/proxy_port</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>110</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                                <shared>1</shared>
                                            </proxy_port>
                                        </fields>
                                    </express>
                                    <enable_payflow_advanced translate="label comment">
                                        <label>Enable this Solution</label>
                                        <config_path>payment/payflow_advanced/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <comment>Both section above must be complete. </comment>
                                        <requires>express,payments_advanced</requires>
                                        <frontend_class>paypal-enabler</frontend_class>
                                    </enable_payflow_advanced>
                                    <enable_express_checkout_bml extends="//paypal_payments/express_checkout//enable_express_checkout_bml">
                                        <comment><![CDATA[PayPal Payments Advanced lets you give customers access to financing through Paypal Credit&#174; - at no additional cost to you.
                                        You get paid up front, even though customers have more time to pay. A pre-integrated payment button lets customers pay quickly with Paypal Credit&#174;.
                                        <a href="https://www.paypal.com/webapps/mpp/promotional-financing" target="_blank">Learn More</a>]]>
                                        </comment>
                                        <sort_order>31</sort_order>
                                        <requires>required_settings_enable_payflow_advanced</requires>
                                    </enable_express_checkout_bml>
                                    <advanced_advertise_bml type="group" translate="label comment">
                                        <label>Advertise Paypal Credit</label>
                                        <comment>
                                            <![CDATA[<a href="https://financing.paypal.com/ppfinportal/content/whyUseFinancing" target="_blank">Why Advertise Financing?</a><br/>
                                            <strong>Give your sales a boost when you advertise financing.</strong><br/>PayPal helps turn browsers into buyers with financing
                                            from Paypal Credit&#174;. Your customers have more time to pay, while you get paid up front – at no additional cost to you.
                                            Use PayPal’s free banner ads that let you advertise Paypal Credit&#174; financing as a payment option when your customers check out with PayPal.
                                            The PayPal Advertising Program has been shown to generate additional purchases as well as increase consumer's average purchase sizes by 15%
                                            or more. <a href="https://financing.paypal.com/ppfinportal/content/forrester" target="_blank">See Details</a>.]]>
                                        </comment>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <sort_order>32</sort_order>
                                        <fields>
                                            <bml_publisher_id extends="//paypal_payments/express_checkout//advertise_bml//bml_publisher_id" />
                                            <bml_wizard extends="//paypal_payments/express_checkout//advertise_bml//bml_wizard" />
                                            <advanced_settings_bml_homepage type="group" translate="label">
                                                <label>Home Page</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>20</sort_order>
                                                <fields>
                                                    <bml_homepage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_display"/>
                                                    <advanced_bml_homepage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_position"/>
                                                    <advanced_bml_homepage_size1 translate="label">
                                                        <label>Size</label>
                                                        <config_path>payment/paypal_express_bml/homepage_size</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPH</source_model>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <sort_order>30</sort_order>
                                                        <shared>1</shared>
                                                        <depends><advanced_bml_homepage_position>0</advanced_bml_homepage_position></depends>
                                                    </advanced_bml_homepage_size1>
                                                    <advanced_bml_homepage_size2 extends="//paypal_payments/payflow_advanced//advanced_bml_homepage_size1">
                                                        <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPS</source_model>
                                                        <depends><advanced_bml_homepage_position>1</advanced_bml_homepage_position></depends>
                                                    </advanced_bml_homepage_size2>
                                                </fields>
                                            </advanced_settings_bml_homepage>
                                            <advanced_settings_bml_categorypage type="group" translate="label">
                                                <label>Catalog Category Page</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>30</sort_order>
                                                <fields>
                                                    <bml_categorypage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_display"/>
                                                    <advanced_bml_categorypage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_position" />
                                                    <advanced_bml_categorypage_size1 translate="label">
                                                        <label>Size</label>
                                                        <config_path>payment/paypal_express_bml/categorypage_size</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPC</source_model>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <sort_order>30</sort_order>
                                                        <shared>1</shared>
                                                        <depends><advanced_bml_categorypage_position>0</advanced_bml_categorypage_position></depends>
                                                    </advanced_bml_categorypage_size1>
                                                    <advanced_bml_categorypage_size2 extends="//paypal_payments/payflow_advanced//advanced_bml_categorypage_size1">
                                                        <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPS</source_model>
                                                        <depends><advanced_bml_categorypage_position>1</advanced_bml_categorypage_position></depends>
                                                    </advanced_bml_categorypage_size2>
                                                </fields>

                                            </advanced_settings_bml_categorypage>
                                            <advanced_settings_bml_productpage type="group" translate="label">
                                                <label>Catalog Product Page</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>40</sort_order>
                                                <fields>
                                                    <bml_productpage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_display" />
                                                    <advanced_bml_productpage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_position" />
                                                    <advanced_bml_productpage_size1 translate="label">
                                                        <label>Size</label>
                                                        <config_path>payment/paypal_express_bml/productpage_size</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPC</source_model>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <sort_order>30</sort_order>
                                                        <shared>1</shared>
                                                        <depends><advanced_bml_productpage_position>0</advanced_bml_productpage_position></depends>
                                                    </advanced_bml_productpage_size1>
                                                    <advanced_bml_productpage_size2 extends="//paypal_payments/payflow_advanced//advanced_bml_productpage_size1">
                                                        <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPN</source_model>
                                                        <depends><advanced_bml_productpage_position>1</advanced_bml_productpage_position></depends>
                                                    </advanced_bml_productpage_size2>
                                                </fields>

                                            </advanced_settings_bml_productpage>
                                            <advanced_settings_bml_checkout type="group" translate="label">
                                                <label>Checkout Cart Page</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>50</sort_order>
                                                <fields>
                                                    <bml_checkout_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_display" />
                                                    <advanced_bml_checkout_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_position" />
                                                    <advanced_bml_checkout_size1 translate="label">
                                                        <label>Size</label>
                                                        <config_path>payment/paypal_express_bml/checkout_size</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutC</source_model>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <sort_order>30</sort_order>
                                                        <shared>1</shared>
                                                        <depends><advanced_bml_checkout_position>0</advanced_bml_checkout_position></depends>
                                                    </advanced_bml_checkout_size1>
                                                    <advanced_bml_checkout_size2 extends="//paypal_payments/payflow_advanced//advanced_bml_checkout_size1">
                                                        <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutN</source_model>
                                                        <shared>1</shared>
                                                        <depends><advanced_bml_checkout_position>1</advanced_bml_checkout_position></depends>
                                                    </advanced_bml_checkout_size2>
                                                </fields>

                                            </advanced_settings_bml_checkout>
                                        </fields>
                                    </advanced_advertise_bml>
                                </fields>
                            </required_settings>

                            <settings_payments_advanced type="group" translate="label">
                                <label>Basic Settings - PayPal Payments Advanced</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title translate="label comment">
                                        <label>Title</label>
                                        <comment>It is recommended to set this value to "Debit or Credit Card" per store views.</comment>
                                        <config_path>payment/payflow_advanced/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/payflow_advanced/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/payflow_advanced/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </payment_action>
                                    <settings_payments_advanced_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>40</sort_order>
                                        <fields>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/payflow_advanced/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/payflow_advanced/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <sort_order>25</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                            </specificcountry>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/payflow_advanced/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/payflow_advanced/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>35</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </verify_peer>
                                            <csc_editable translate="label">
                                                <label>CVV Entry is Editable</label>
                                                <config_path>payment/payflow_advanced/csc_editable</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </csc_editable>
                                            <csc_required translate="label">
                                                <label>Require CVV Entry</label>
                                                <config_path>payment/payflow_advanced/csc_required</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><csc_editable>1</csc_editable></depends>
                                            </csc_required>
                                            <email_confirmation translate="label">
                                                <label>Send Email Confirmation</label>
                                                <config_path>payment/payflow_advanced/email_confirmation</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </email_confirmation>
                                            <url_method translate="label">
                                                <label>URL method for Cancel URL and Return URL</label>
                                                <config_path>payment/payflow_advanced/url_method</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>paypal/system_config_source_urlMethod</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </url_method>
                                            <mobile_optimized translate="label">
                                                <label>Mobile Optimized</label>
                                                <config_path>payment/payflow_advanced/mobile_optimized</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>75</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </mobile_optimized>
                                            <billing_agreement type="group" translate="label">
                                                <label>PayPal Billing Agreement Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>80</sort_order>
                                                <fields>
                                                    <active translate="label comment">
                                                        <label>Enabled</label>
                                                        <comment><![CDATA[Will appear as a payment option only for customers who have at least one active billing agreement.]]></comment>
                                                        <config_path>payment/paypal_billing_agreement/active</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                                        <sort_order>10</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </active>
                                                    <title translate="label">
                                                        <label>Title</label>
                                                        <config_path>payment/paypal_billing_agreement/title</config_path>
                                                        <frontend_type>text</frontend_type>
                                                        <sort_order>20</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <shared>1</shared>
                                                    </title>
                                                    <sort_order translate="label">
                                                        <label>Sort Order</label>
                                                        <config_path>payment/paypal_billing_agreement/sort_order</config_path>
                                                        <frontend_type>text</frontend_type>
                                                        <sort_order>30</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <frontend_class>validate-number</frontend_class>
                                                        <shared>1</shared>
                                                    </sort_order>
                                                    <payment_action translate="label">
                                                        <label>Payment Action</label>
                                                        <config_path>payment/paypal_billing_agreement/payment_action</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                                        <sort_order>40</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </payment_action>
                                                    <allowspecific translate="label">
                                                        <label>Payment Applicable From</label>
                                                        <config_path>payment/paypal_billing_agreement/allowspecific</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                        <sort_order>50</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </allowspecific>
                                                    <specificcountry translate="label">
                                                        <label>Countries Payment Applicable From</label>
                                                        <config_path>payment/paypal_billing_agreement/specificcountry</config_path>
                                                        <frontend_type>multiselect</frontend_type>
                                                        <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                        <sort_order>60</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <depends><allowspecific>1</allowspecific></depends>
                                                        <shared>1</shared>
                                                    </specificcountry>
                                                    <debug translate="label">
                                                        <label>Debug Mode</label>
                                                        <config_path>payment/paypal_billing_agreement/debug</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                                        <sort_order>70</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </debug>
                                                    <verify_peer translate="label">
                                                        <label>Enable SSL verification</label>
                                                        <config_path>payment/paypal_billing_agreement/verify_peer</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                                        <sort_order>75</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </verify_peer>
                                                    <line_items_enabled translate="label">
                                                        <label>Transfer Cart Line Items</label>
                                                        <config_path>payment/paypal_billing_agreement/line_items_enabled</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                                        <sort_order>80</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </line_items_enabled>
                                                    <allow_billing_agreement_wizard translate="label">
                                                        <label>Allow in Billing Agreement Wizard</label>
                                                        <config_path>payment/paypal_billing_agreement/allow_billing_agreement_wizard</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                                        <sort_order>90</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </allow_billing_agreement_wizard>
                                                </fields>
                                            </billing_agreement>
                                            <settlement_report type="group" translate="label">
                                                <label>Settlement Report Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>90</sort_order>
                                                <fields>
                                                    <heading_sftp translate="label">
                                                        <label>SFTP Credentials</label>
                                                        <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                                        <sort_order>10</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                    </heading_sftp>
                                                    <settlement_reports_ftp_login translate="label">
                                                        <label>Login</label>
                                                        <config_path>paypal/fetch_reports/ftp_login</config_path>
                                                        <frontend_type>obscure</frontend_type>
                                                        <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                        <sort_order>20</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </settlement_reports_ftp_login>
                                                    <settlement_reports_ftp_password translate="label">
                                                        <label>Password</label>
                                                        <config_path>paypal/fetch_reports/ftp_password</config_path>
                                                        <frontend_type>obscure</frontend_type>
                                                        <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                        <sort_order>30</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </settlement_reports_ftp_password>
                                                    <settlement_reports_ftp_sandbox translate="label">
                                                        <label>Sandbox Mode</label>
                                                        <config_path>paypal/fetch_reports/ftp_sandbox</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                                        <sort_order>40</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </settlement_reports_ftp_sandbox>
                                                    <settlement_reports_ftp_ip translate="label comment tooltip">
                                                        <label>Custom Endpoint Hostname or IP-Address</label>
                                                        <comment>By default it is "reports.paypal.com".</comment>
                                                        <tooltip>Use colon to specify port. For example: "test.example.com:5224".</tooltip>
                                                        <config_path>paypal/fetch_reports/ftp_ip</config_path>
                                                        <frontend_type>text</frontend_type>
                                                        <sort_order>50</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <depends><settlement_reports_ftp_sandbox>0</settlement_reports_ftp_sandbox></depends>
                                                        <shared>1</shared>
                                                    </settlement_reports_ftp_ip>
                                                    <settlement_reports_ftp_path translate="label comeent">
                                                        <label>Custom Path</label>
                                                        <comment>By default it is "/ppreports/outgoing".</comment>
                                                        <config_path>paypal/fetch_reports/ftp_path</config_path>
                                                        <frontend_type>text</frontend_type>
                                                        <sort_order>60</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <depends><settlement_reports_ftp_sandbox>0</settlement_reports_ftp_sandbox></depends>
                                                        <shared>1</shared>
                                                    </settlement_reports_ftp_path>
                                                    <heading_schedule translate="label">
                                                        <label>Scheduled Fetching</label>
                                                        <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                                        <sort_order>70</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </heading_schedule>
                                                    <settlement_reports_active translate="label">
                                                        <label>Enable Automatic Fetching</label>
                                                        <config_path>paypal/fetch_reports/active</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                                        <sort_order>80</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <shared>1</shared>
                                                    </settlement_reports_active>
                                                    <settlement_reports_schedule translate="label comment">
                                                        <label>Schedule</label>
                                                        <comment>PayPal retains reports for 45 days.</comment>
                                                        <config_path>paypal/fetch_reports/schedule</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>paypal/system_config_source_fetchingSchedule</source_model>
                                                        <sort_order>90</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <shared>1</shared>
                                                    </settlement_reports_schedule>
                                                    <settlement_reports_time translate="label">
                                                        <label>Time of Day</label>
                                                        <config_path>paypal/fetch_reports/time</config_path>
                                                        <frontend_type>time</frontend_type>
                                                        <sort_order>100</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <shared>1</shared>
                                                    </settlement_reports_time>
                                                </fields>
                                            </settlement_report>
                                            <frontend type="group" translate="label">
                                                <label>Frontend Experience Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>100</sort_order>
                                                <fields>
                                                    <logo translate="label comment">
                                                        <label>PayPal Product Logo</label>
                                                        <comment>Displays on catalog pages and homepage.</comment>
                                                        <config_path>paypal/style/logo</config_path>
                                                        <frontend_type>select</frontend_type>
                                                        <source_model>paypal/system_config_source_logo</source_model>
                                                        <sort_order>10</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <shared>1</shared>
                                                    </logo>
                                                    <paypal_pages translate="label">
                                                        <label>PayPal Merchant Pages Style</label>
                                                        <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                                        <sort_order>20</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <shared>1</shared>
                                                    </paypal_pages>
                                                    <page_style translate="label tooltip">
                                                        <label>Page Style</label>
                                                        <config_path>paypal/style/page_style</config_path>
                                                        <tooltip><![CDATA[Allowable values: "paypal", "primary" (default), your_custom_value (a custom payment page style from your merchant account profile).]]></tooltip>
                                                        <frontend_type>text</frontend_type>
                                                        <sort_order>30</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <shared>1</shared>
                                                    </page_style>
                                                    <paypal_hdrimg translate='label tooltip'>
                                                        <label>Header Image URL</label>
                                                        <config_path>paypal/style/paypal_hdrimg</config_path>
                                                        <tooltip><![CDATA[The image at the top left of the checkout page. Max size is 750x90-pixel. <strong style="color:red">https</strong> is highly encouraged.]]></tooltip>
                                                        <sort_order>40</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <shared>1</shared>
                                                    </paypal_hdrimg>
                                                    <paypal_hdrbackcolor translate="label tooltip">
                                                        <label>Header Background Color</label>
                                                        <config_path>paypal/style/paypal_hdrbackcolor</config_path>
                                                        <tooltip><![CDATA[The background color for the header of the checkout page. Case-insensitive six-character HTML hexadecimal color code in ASCII.]]></tooltip>
                                                        <sort_order>50</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <shared>1</shared>
                                                    </paypal_hdrbackcolor>
                                                    <paypal_hdrbordercolor translate="label tooltip">
                                                        <label>Header Border Color</label>
                                                        <config_path>paypal/style/paypal_hdrbordercolor</config_path>
                                                        <tooltip>2-pixel perimeter around the header space.</tooltip>
                                                        <sort_order>60</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <shared>1</shared>
                                                    </paypal_hdrbordercolor>
                                                    <paypal_payflowcolor translate="label tooltip">
                                                        <label>Page Background Color</label>
                                                        <config_path>paypal/style/paypal_payflowcolor</config_path>
                                                        <tooltip><![CDATA[The background color for the checkout page around the header and payment form.]]></tooltip>
                                                        <sort_order>70</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <shared>1</shared>
                                                    </paypal_payflowcolor>
                                                </fields>
                                            </frontend>
                                        </fields>
                                    </settings_payments_advanced_advanced>
                                </fields>
                            </settings_payments_advanced>
                            <settings_express_checkout type="group" translate="label">
                                <label>Basic Settings - PayPal Express Checkout</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>30</sort_order>
                                <fields>
                                    <title translate="label comment">
                                        <label>Title</label>
                                        <comment>It is recommended to set this value to "PayPal" per store views.</comment>
                                        <config_path>payment/paypal_express/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <shared>1</shared>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/paypal_express/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                        <shared>1</shared>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/paypal_express/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions_express</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <shared>1</shared>
                                    </payment_action>
                                    <visible_on_product translate="label">
                                        <label>Display on Product Details Page</label>
                                        <config_path>payment/paypal_express/visible_on_product</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>40</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <shared>1</shared>
                                    </visible_on_product>
                                    <authorization_honor_period translate="label comment">
                                        <label>Authorization Honor Period (days)</label>
                                        <comment>Specifies what the Authorization Honor Period is on the merchant’s PayPal account. It must mirror the setting in PayPal.</comment>
                                        <config_path>payment/paypal_express/authorization_honor_period</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>50</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <shared>1</shared>
                                        <depends><payment_action>Order</payment_action></depends>
                                    </authorization_honor_period>
                                    <order_valid_period translate="label comment">
                                        <label>Order Valid Period (days)</label>
                                        <comment>Specifies what the Order Valid Period is on the merchant’s PayPal account. It must mirror the setting in PayPal.</comment>
                                        <config_path>payment/paypal_express/order_valid_period</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>60</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <shared>1</shared>
                                        <depends><payment_action>Order</payment_action></depends>
                                    </order_valid_period>
                                    <child_authorization_number translate="label comment">
                                        <label>Number of Child Authorizations</label>
                                        <comment>The default number of child authorizations in your PayPal account is 1. To do multiple authorizations please contact PayPal to request an increase.</comment>
                                        <config_path>payment/paypal_express/child_authorization_number</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>70</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <shared>1</shared>
                                        <depends><payment_action>Order</payment_action></depends>
                                    </child_authorization_number>
                                    <settings_express_checkout_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>80</sort_order>
                                        <fields>
                                            <visible_on_cart translate="label comment">
                                                <label>Display on Shopping Cart</label>
                                                <config_path>payment/paypal_express/visible_on_cart</config_path>
                                                <comment>Also affects mini-shopping cart.</comment>
                                                <frontend_type>select</frontend_type>
                                                <source_model>paypal/system_config_source_yesnoShortcut</source_model>
                                                <sort_order>5</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <shared>1</shared>
                                            </visible_on_cart>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/paypal_express/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <sort_order>10</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/paypal_express/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                                <shared>1</shared>
                                            </specificcountry>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/paypal_express/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/paypal_express/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>35</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </verify_peer>
                                            <line_items_enabled translate="label">
                                                <label>Transfer Cart Line Items</label>
                                                <config_path>payment/paypal_express/line_items_enabled</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </line_items_enabled>
                                            <transfer_shipping_options translate="label tooltip comment">
                                                <label>Transfer Shipping Options</label>
                                                <config_path>payment/paypal_express/transfer_shipping_options</config_path>
                                                <tooltip>If this option is enabled, customer can change shipping address and shipping method on PayPal website. In live mode works via HTTPS protocol only.</tooltip>
                                                <comment>Notice that PayPal can handle up to 10 shipping options. That is why Magento will transfer only first 10 cheapest shipping options if there are more than 10 available.</comment>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><line_items_enabled>1</line_items_enabled></depends>
                                                <shared>1</shared>
                                            </transfer_shipping_options>
                                            <button_flavor translate="label">
                                                <label>Shortcut Buttons Flavor</label>
                                                <config_path>paypal/wpp/button_flavor</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>paypal/config::getExpressCheckoutButtonFlavors</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <shared>1</shared>
                                            </button_flavor>
                                            <solution_type translate="label comment">
                                                <label>Enable PayPal Guest Checkout</label>
                                                <comment>Ability for buyer to purchase without PayPal account.</comment>
                                                <config_path>payment/paypal_express/solution_type</config_path>
                                                <frontend_type>select</frontend_type>
                                                <frontend_model>paypal/adminhtml_system_config_field_solutionType</frontend_model>
                                                <source_model>paypal/config::getExpressCheckoutSolutionTypes</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </solution_type>
                                            <require_billing_address translate="label comment">
                                                <label>Require Customer's Billing Address</label>
                                                <comment>This feature needs be enabled first for the merchant account through PayPal technical support.</comment>
                                                <config_path>payment/paypal_express/require_billing_address</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>paypal/system_config_source_requireBillingAddress</source_model>
                                                <sort_order>80</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </require_billing_address>
                                            <allow_ba_signup translate="label comment tooltip">
                                                <label>Billing Agreement Signup</label>
                                                <comment>Whether to create a billing agreement, if there are no active billing agreements available.</comment>
                                                <tooltip><![CDATA[Merchants need to apply to PayPal for enabling billing agreements feature. Do not enable this option until PayPal confirms that billing agreements are enabled for your merchant account.]]></tooltip>
                                                <config_path>payment/paypal_express/allow_ba_signup</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>paypal/config::getExpressCheckoutBASignupOptions</source_model>
                                                <sort_order>90</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </allow_ba_signup>
                                            <skip_order_review_step translate="label comment">
                                                <label>Skip Order Review Step</label>
                                                <config_path>payment/paypal_express/skip_order_review_step</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>95</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                            </skip_order_review_step>
                                        </fields>
                                    </settings_express_checkout_advanced>
                                </fields>
                            </settings_express_checkout>
                        </fields>
                    </payflow_advanced>
                    <wpp type="group" translate="label comment">
                        <label>Website Payments Pro (Includes Express Checkout)</label>
                        <frontend_type>text</frontend_type>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>40</sort_order>
                        <group>paypal_payment_solutions</group>
                        <frontend_class>pp-method-general</frontend_class>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_payment</frontend_model>
                        <comment>Accept payments with a completely customizable checkout.</comment>
                        <activity_path>payment/paypal_direct/active</activity_path>
                        <learn_more_link>https://www.paypal.com/webapps/mpp/referral/website-payments-pro?partner_id=NB9WWHYEMVUMS</learn_more_link>
                        <backend_config>
                                <CA>
                                    <include>1</include>
                                    <sort_order>10</sort_order>
                                </CA>
                                <GB translate="label comment">
                                    <include>1</include>
                                    <group>paypal_group_all_in_one</group>
                                    <frontend_class>pp-general-uk</frontend_class>
                                    <sort_order>20</sort_order>
                                    <learn_more_link>https://www.paypal-business.co.uk/process-online-payments-with-paypal/index.htm</learn_more_link>
                                    <demo_link>http://www.youtube.com/watch?v=LBe-TW87eGI&amp;list=PLF18B1094ABCD7CE8&amp;index=1&amp;feature=plpp_video</demo_link>
                                    <comment>Accept payments with a completely customizable checkout page.</comment>
                                </GB>
                                <US translate="label">
                                    <label>Payments Pro (Includes Express Checkout)</label>
                                    <include>1</include>
                                    <group>paypal_group_all_in_one</group>
                                    <fields>
                                        <wpp_required_settings>
                                            <fields>
                                                <wpp_and_express_checkout translate="label">
                                                    <label>Payments Pro and Express Checkout</label>
                                                </wpp_and_express_checkout>
                                                <enable_express_checkout_bml  extends="//paypal_payments/express_checkout//enable_express_checkout_bml">
                                                    <comment><![CDATA[Website Payments Pro lets you give customers access to financing through Paypal Credit&#174; - at no additional cost to you.
                                        You get paid up front, even though customers have more time to pay. A pre-integrated payment button lets customers pay quickly with Paypal Credit&#174;.
                                        <a href="https://www.paypal.com/webapps/mpp/promotional-financing" target="_blank">Learn More</a>]]>
                                                    </comment>
                                                    <sort_order>21</sort_order>
                                                    <requires>wpp_required_settings_enable_wpp</requires>
                                                </enable_express_checkout_bml>
                                                <wpp_advertise_bml type="group" translate="label comment">
                                                    <label>Advertise Paypal Credit</label>
                                                    <comment>
                                                        <![CDATA[<a href="https://financing.paypal.com/ppfinportal/content/whyUseFinancing" target="_blank">Why Advertise Financing?</a><br/>
                                            <strong>Give your sales a boost when you advertise financing.</strong><br/>PayPal helps turn browsers into buyers with financing
                                            from Paypal Credit&#174;. Your customers have more time to pay, while you get paid up front – at no additional cost to you.
                                            Use PayPal’s free banner ads that let you advertise Paypal Credit&#174; financing as a payment option when your customers check out with PayPal.
                                            The PayPal Advertising Program has been shown to generate additional purchases as well as increase consumer's average purchase sizes by 15%
                                            or more. <a href="https://financing.paypal.com/ppfinportal/content/forrester" target="_blank">See Details</a>.]]>
                                                    </comment>
                                                    <frontend_type>text</frontend_type>
                                                    <show_in_default>1</show_in_default>
                                                    <show_in_website>1</show_in_website>
                                                    <sort_order>22</sort_order>
                                                    <fields>
                                                        <bml_publisher_id extends="//paypal_payments/express_checkout//advertise_bml//bml_publisher_id" />
                                                        <bml_wizard extends="//paypal_payments/express_checkout//advertise_bml//bml_wizard" />
                                                        <wpp_settings_bml_homepage type="group" translate="label">
                                                            <label>Home Page</label>
                                                            <frontend_type>text</frontend_type>
                                                            <show_in_default>1</show_in_default>
                                                            <show_in_website>1</show_in_website>
                                                            <show_in_store>1</show_in_store>
                                                            <sort_order>20</sort_order>
                                                            <fields>
                                                                <bml_homepage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_display"/>
                                                                <wpp_bml_homepage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_position"/>
                                                                <wpp_bml_homepage_size1 translate="label">
                                                                    <label>Size</label>
                                                                    <config_path>payment/paypal_express_bml/homepage_size</config_path>
                                                                    <frontend_type>select</frontend_type>
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPH</source_model>
                                                                    <show_in_default>1</show_in_default>
                                                                    <show_in_website>1</show_in_website>
                                                                    <sort_order>30</sort_order>
                                                                    <shared>1</shared>
                                                                    <depends><wpp_bml_homepage_position>0</wpp_bml_homepage_position></depends>
                                                                </wpp_bml_homepage_size1>
                                                                <wpp_bml_homepage_size2 extends="//paypal_payments/wpp//wpp_bml_homepage_size1">
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPS</source_model>
                                                                    <depends><wpp_bml_homepage_position>1</wpp_bml_homepage_position></depends>
                                                                </wpp_bml_homepage_size2>
                                                            </fields>
                                                        </wpp_settings_bml_homepage>
                                                        <wpp_settings_bml_categorypage type="group" translate="label">
                                                            <label>Catalog Category Page</label>
                                                            <frontend_type>text</frontend_type>
                                                            <show_in_default>1</show_in_default>
                                                            <show_in_website>1</show_in_website>
                                                            <show_in_store>1</show_in_store>
                                                            <sort_order>30</sort_order>
                                                            <fields>
                                                                <bml_categorypage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_display"/>
                                                                <wpp_bml_categorypage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_position" />
                                                                <wpp_bml_categorypage_size1 translate="label">
                                                                    <label>Size</label>
                                                                    <config_path>payment/paypal_express_bml/categorypage_size</config_path>
                                                                    <frontend_type>select</frontend_type>
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPC</source_model>
                                                                    <show_in_default>1</show_in_default>
                                                                    <show_in_website>1</show_in_website>
                                                                    <sort_order>30</sort_order>
                                                                    <shared>1</shared>
                                                                    <depends><wpp_bml_categorypage_position>0</wpp_bml_categorypage_position></depends>
                                                                </wpp_bml_categorypage_size1>
                                                                <wpp_bml_categorypage_size2 extends="//paypal_payments/wpp//wpp_bml_categorypage_size1">
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPS</source_model>
                                                                    <depends><wpp_bml_categorypage_position>1</wpp_bml_categorypage_position></depends>
                                                                </wpp_bml_categorypage_size2>
                                                            </fields>

                                                        </wpp_settings_bml_categorypage>
                                                        <wpp_settings_bml_productpage type="group" translate="label">
                                                            <label>Catalog Product Page</label>
                                                            <frontend_type>text</frontend_type>
                                                            <show_in_default>1</show_in_default>
                                                            <show_in_website>1</show_in_website>
                                                            <show_in_store>1</show_in_store>
                                                            <sort_order>40</sort_order>
                                                            <fields>
                                                                <bml_productpage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_display" />
                                                                <wpp_bml_productpage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_position" />
                                                                <wpp_bml_productpage_size1 translate="label">
                                                                    <label>Size</label>
                                                                    <config_path>payment/paypal_express_bml/productpage_size</config_path>
                                                                    <frontend_type>select</frontend_type>
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPC</source_model>
                                                                    <show_in_default>1</show_in_default>
                                                                    <show_in_website>1</show_in_website>
                                                                    <sort_order>30</sort_order>
                                                                    <shared>1</shared>
                                                                    <depends><wpp_bml_productpage_position>0</wpp_bml_productpage_position></depends>
                                                                </wpp_bml_productpage_size1>
                                                                <wpp_bml_productpage_size2 extends="//paypal_payments/wpp//wpp_bml_productpage_size1">
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPN</source_model>
                                                                    <depends><wpp_bml_productpage_position>1</wpp_bml_productpage_position></depends>
                                                                </wpp_bml_productpage_size2>
                                                            </fields>

                                                        </wpp_settings_bml_productpage>
                                                        <wpp_settings_bml_checkout type="group" translate="label">
                                                            <label>Checkout Cart Page</label>
                                                            <frontend_type>text</frontend_type>
                                                            <show_in_default>1</show_in_default>
                                                            <show_in_website>1</show_in_website>
                                                            <show_in_store>1</show_in_store>
                                                            <sort_order>50</sort_order>
                                                            <fields>
                                                                <bml_checkout_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_display" />
                                                                <wpp_bml_checkout_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_position" />
                                                                <wpp_bml_checkout_size1 translate="label">
                                                                    <label>Size</label>
                                                                    <config_path>payment/paypal_express_bml/checkout_size</config_path>
                                                                    <frontend_type>select</frontend_type>
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutC</source_model>
                                                                    <show_in_default>1</show_in_default>
                                                                    <show_in_website>1</show_in_website>
                                                                    <sort_order>30</sort_order>
                                                                    <shared>1</shared>
                                                                    <depends><wpp_bml_checkout_position>0</wpp_bml_checkout_position></depends>
                                                                </wpp_bml_checkout_size1>
                                                                <wpp_bml_checkout_size2 extends="//paypal_payments/wpp//wpp_bml_checkout_size1">
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutN</source_model>
                                                                    <shared>1</shared>
                                                                    <depends><wpp_bml_checkout_position>1</wpp_bml_checkout_position></depends>
                                                                </wpp_bml_checkout_size2>
                                                            </fields>

                                                        </wpp_settings_bml_checkout>
                                                    </fields>
                                                </wpp_advertise_bml>
                                            </fields>
                                        </wpp_required_settings>
                                        <wpp_settings translate="label">
                                            <label>Basic Settings - PayPal Payments Pro</label>
                                        </wpp_settings>
                                    </fields>
                                </US>
                            </backend_config>
                        <fields>
                            <wpp_required_settings type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>10</sort_order>
                                <fields>
                                    <wpp_and_express_checkout type="group" translate="label">
                                        <label>Website Payments Pro and Express Checkout</label>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>10</sort_order>
                                        <fields>
                                            <business_account extends="//paypal_payments/payflow_advanced//business_account" />
                                            <api_authentication extends="//paypal_payments/payflow_advanced//api_authentication" />
                                            <api_username extends="//paypal_payments/payflow_advanced//api_username" />
                                            <api_password extends="//paypal_payments/payflow_advanced//api_password" />
                                            <api_signature extends="//paypal_payments/payflow_advanced//api_signature" />
                                            <api_cert extends="//paypal_payments/payflow_advanced//api_cert" />
                                            <api_wizard extends="//paypal_payments/payflow_advanced//api_wizard" />
                                            <sandbox_flag extends="//paypal_payments/payflow_advanced//express//sandbox_flag" />
                                            <use_proxy extends="//paypal_payments/payflow_advanced//express//use_proxy" />
                                            <proxy_host extends="//paypal_payments/payflow_advanced//express//proxy_host" />
                                            <proxy_port extends="//paypal_payments/payflow_advanced//express//proxy_port" />
                                        </fields>
                                    </wpp_and_express_checkout>

                                    <enable_wpp translate="label">
                                        <label>Enable this Solution</label>
                                        <config_path>payment/paypal_direct/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <requires>wpp_and_express_checkout</requires>
                                        <frontend_class>paypal-enabler</frontend_class>
                                    </enable_wpp>

                                </fields>
                            </wpp_required_settings>

                            <wpp_settings type="group" translate="label">
                                <label>Basic Settings - PayPal Website Payments Pro</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title translate="label comment">
                                        <label>Title</label>
                                        <comment>It is recommended to set this value to "Debit or Credit Card" per store views.</comment>
                                        <config_path>payment/paypal_direct/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/paypal_direct/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/paypal_direct/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </payment_action>
                                    <heading_cc translate="label">
                                        <label>Credit Card Settings</label>
                                        <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                        <sort_order>40</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </heading_cc>
                                    <cctypes translate="label comment">
                                        <label>Allowed Credit Card Types</label>
                                        <comment><![CDATA[3D Secure validation is required for Maestro cards. Supporting of American Express cards require additional agreement. Learn more at <a href="http://www.paypal.com/amexupdate">http://www.paypal.com/amexupdate</a>.]]></comment>
                                        <config_path>payment/paypal_direct/cctypes</config_path>
                                        <frontend_type>multiselect</frontend_type>
                                        <source_model>paypal/config::getWppCcTypesAsOptionArray</source_model>
                                        <sort_order>50</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </cctypes>
                                    <wpp_settings_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>60</sort_order>
                                        <fields>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/paypal_direct/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/paypal_direct/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <sort_order>25</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                            </specificcountry>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/paypal_direct/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/paypal_direct/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>35</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </verify_peer>
                                            <line_items_enabled translate="label">
                                                <label>Transfer Cart Line Items</label>
                                                <config_path>payment/paypal_direct/line_items_enabled</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </line_items_enabled>
                                            <useccv translate="label">
                                                <label>Require CVV Entry</label>
                                                <config_path>payment/paypal_direct/useccv</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </useccv>
                                            <heading_3dsecure translate="label">
                                                <label>3D Secure</label>
                                                <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                                <sort_order>55</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </heading_3dsecure>
                                            <centinel translate="label">
                                                <label>3D Secure Card Validation</label>
                                                <config_path>payment/paypal_direct/centinel</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </centinel>
                                            <centinel_is_mode_strict translate="label comment">
                                                <label>Severe 3D Secure Card Validation</label>
                                                <config_path>payment/paypal_direct/centinel_is_mode_strict</config_path>
                                                <comment>Severe Validation Removes Chargeback Liability on Merchant</comment>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>65</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><centinel>1</centinel></depends>
                                            </centinel_is_mode_strict>
                                            <centinel_api_url translate="label comment">
                                                <label>Centinel Custom API URL</label>
                                                <config_path>payment/paypal_direct/centinel_api_url</config_path>
                                                <comment>If empty, a default value will be used. Custom URL may be provided by CardinalCommerce agreement.</comment>
                                                <frontend_type>text</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><centinel>1</centinel></depends>
                                            </centinel_api_url>
                                            <wpp_billing_agreement type="group" translate="label">
                                                <label>PayPal Billing Agreement Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>80</sort_order>
                                                <fields>
                                                    <active extends="//paypal_payments/payflow_advanced//billing_agreement/*/active" />
                                                    <title extends="//paypal_payments/payflow_advanced//billing_agreement/*/title" />
                                                    <sort_order extends="//paypal_payments/payflow_advanced//billing_agreement/*/sort_order" />
                                                    <payment_action extends="//paypal_payments/payflow_advanced//billing_agreement/*/payment_action" />
                                                    <allowspecific extends="//paypal_payments/payflow_advanced//billing_agreement/*/allowspecific" />
                                                    <specificcountry extends="//paypal_payments/payflow_advanced//billing_agreement/*/specificcountry" />
                                                    <debug extends="//paypal_payments/payflow_advanced//billing_agreement/*/debug" />
                                                    <verify_peer extends="//paypal_payments/payflow_advanced//billing_agreement/*/verify_peer" />
                                                    <line_items_enabled extends="//paypal_payments/payflow_advanced//billing_agreement/*/line_items_enabled" />
                                                    <allow_billing_agreement_wizard extends="//paypal_payments/payflow_advanced//billing_agreement/*/allow_billing_agreement_wizard" />
                                                </fields>
                                            </wpp_billing_agreement>
                                            <wpp_settlement_report type="group" translate="label">
                                                <label>Settlement Report Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>90</sort_order>
                                                <fields>
                                                    <heading_sftp extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_sftp" />
                                                    <settlement_reports_ftp_login extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_login" />
                                                    <settlement_reports_ftp_password extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_password" />
                                                    <settlement_reports_ftp_sandbox extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_sandbox" />
                                                    <settlement_reports_ftp_ip extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_ip" />
                                                    <settlement_reports_ftp_path extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_path" />
                                                    <heading_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_schedule" />
                                                    <settlement_reports_active extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_active" />
                                                    <settlement_reports_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_schedule" />
                                                    <settlement_reports_time extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_time" />
                                                </fields>
                                            </wpp_settlement_report>
                                            <wpp_frontend type="group" translate="label">
                                                <label>Frontend Experience Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>100</sort_order>
                                                <fields>
                                                    <logo extends="//paypal_payments/payflow_advanced//frontend/*/logo" />
                                                    <paypal_pages extends="//paypal_payments/payflow_advanced//frontend/*/paypal_pages" />
                                                    <page_style extends="//paypal_payments/payflow_advanced//frontend/*/page_style" />
                                                    <paypal_hdrimg extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrimg" />
                                                    <paypal_hdrbackcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbackcolor" />
                                                    <paypal_hdrbordercolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbordercolor" />
                                                    <paypal_payflowcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_payflowcolor" />
                                                </fields>
                                            </wpp_frontend>
                                        </fields>
                                    </wpp_settings_advanced>
                                </fields>
                            </wpp_settings>

                            <wpp_settings_express_checkout type="group" translate="label">
                                <label>Basic Settings - PayPal Express Checkout</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>30</sort_order>
                                <fields>
                                    <title extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/title" />
                                    <sort_order extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/sort_order" />
                                    <payment_action extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/payment_action" />
                                    <visible_on_product extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/visible_on_product" />
                                    <authorization_honor_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/authorization_honor_period" />
                                    <order_valid_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/order_valid_period" />
                                    <child_authorization_number extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/child_authorization_number" />

                                    <wpp_settings_express_checkout_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>80</sort_order>
                                        <fields>
                                            <visible_on_cart extends="//paypal_payments/payflow_advanced//settings_express_checkout//visible_on_cart" />
                                            <allowspecific extends="//paypal_payments/payflow_advanced//settings_express_checkout//allowspecific" />
                                            <specificcountry extends="//paypal_payments/payflow_advanced//settings_express_checkout//specificcountry" />
                                            <debug extends="//paypal_payments/payflow_advanced//settings_express_checkout//debug" />
                                            <verify_peer extends="//paypal_payments/payflow_advanced//settings_express_checkout//verify_peer" />
                                            <line_items_enabled extends="//paypal_payments/payflow_advanced//settings_express_checkout//line_items_enabled" />
                                            <transfer_shipping_options extends="//paypal_payments/payflow_advanced//settings_express_checkout//transfer_shipping_options" />
                                            <button_flavor extends="//paypal_payments/payflow_advanced//settings_express_checkout//button_flavor" />
                                            <solution_type extends="//paypal_payments/payflow_advanced//settings_express_checkout//solution_type" />
                                            <require_billing_address extends="//paypal_payments/payflow_advanced//settings_express_checkout//require_billing_address" />
                                            <allow_ba_signup extends="//paypal_payments/payflow_advanced//settings_express_checkout//allow_ba_signup" />
                                            <skip_order_review_step extends="//paypal_payments/payflow_advanced//settings_express_checkout//skip_order_review_step" />
                                        </fields>
                                    </wpp_settings_express_checkout_advanced>
                                </fields>
                            </wpp_settings_express_checkout>
                        </fields>
                    </wpp>
                    <wps type="group" translate="label comment">
                        <label>Website Payments Standard</label>
                        <frontend_type>text</frontend_type>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>50</sort_order>
                        <group>paypal_payment_solutions</group>
                        <include>1</include>
                        <frontend_class>pp-method-general</frontend_class>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_deprecated</frontend_model>
                        <was_enabled_path>payment/paypal_standard/was_active</was_enabled_path>
                        <comment>Accept credit card and PayPal payments securely.</comment>
                        <activity_path>payment/paypal_standard/active</activity_path>
                        <learn_more_link>https://www.paypal.com/webapps/mpp/referral/website-payments-standard?partner_id=NB9WWHYEMVUMS</learn_more_link>
                        <backend_config>
                            <AU>
                                <sort_order>20</sort_order>
                            </AU>
                            <CA>
                                <sort_order>20</sort_order>
                            </CA>
                            <US translate="label">
                                <label>Payments Standard</label>
                                <group>paypal_group_all_in_one</group>
                                <fields>
                                    <settings_payments_standart translate="label">
                                        <label>Basic Settings - PayPal Payments Standard</label>
                                    </settings_payments_standart>
                                </fields>
                            </US>
                            <GB translate="comment">
                                <group>paypal_group_all_in_one</group>
                                <frontend_class>pp-general-uk</frontend_class>
                                <sort_order>10</sort_order>
                                <learn_more_link>https://www.paypal-business.co.uk/accept-online-payments-with-paypal/index.htm</learn_more_link>
                                <demo_link>http://www.youtube.com/watch?v=mdPjvziH_rk&amp;list=PLF18B1094ABCD7CE8&amp;index=6&amp;feature=plpp_video</demo_link>
                                <comment>Accept credit cards, debit cards and PayPal payments securely.</comment>
                            </GB>
                        </backend_config>
                        <fields>
                            <wps_required_settings type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>10</sort_order>
                                <fields>
                                    <business_account extends="//paypal_payments/payflow_advanced//business_account" />
                                    <enable_wps translate="label">
                                        <label>Enable this Solution</label>
                                        <config_path>payment/paypal_standard/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <requires>wps_required_settings_business_account</requires>
                                        <frontend_class>paypal-enabler paypal-ec-conflicts</frontend_class>
                                    </enable_wps>
                                </fields>
                            </wps_required_settings>
                            <settings_payments_standart type="group" translate="label">
                                <label>Basic Settings - PayPal Website Payments Standard</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title translate="label comment">
                                        <label>Title</label>
                                        <comment>It is recommended to set this value to "PayPal" per store views.</comment>
                                        <config_path>payment/paypal_standard/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/paypal_standard/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/paypal_standard/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </payment_action>
                                    <settings_payments_standart_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <sort_order>40</sort_order>
                                        <fields>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/paypal_standard/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <sort_order>10</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/paypal_standard/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                            </specificcountry>
                                            <sandbox_flag translate="label">
                                                <label>Sandbox Mode</label>
                                                <config_path>payment/paypal_standard/sandbox_flag</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </sandbox_flag>
                                            <line_items_enabled translate="label">
                                                <label>Transfer Cart Line Items</label>
                                                <config_path>payment/paypal_standard/line_items_enabled</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </line_items_enabled>
                                            <line_items_summary translate="label comment">
                                                <label>Summary Text for Aggregated Cart</label>
                                                <config_path>payment/paypal_standard/line_items_summary</config_path>
                                                <comment>Uses store frontend name by default.</comment>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <depends><line_items_enabled>0</line_items_enabled></depends>
                                            </line_items_summary>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/paypal_standard/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/paypal_standard/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </verify_peer>
                                        </fields>
                                    </settings_payments_standart_advanced>
                                </fields>
                            </settings_payments_standart>
                        </fields>
                    </wps>
                    <wps_express type="group" translate="label comment">
                        <label>Website Payments Standard (Includes Express Checkout)</label>
                        <frontend_type>text</frontend_type>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>50</sort_order>
                        <group>paypal_payment_solutions</group>
                        <include>1</include>
                        <frontend_class>pp-method-general</frontend_class>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_pathDependent</frontend_model>
                        <hide_case_path>payment/paypal_standard/active</hide_case_path>
                        <comment>Add PayPal as an additional payment method to your checkout page.</comment>
                        <activity_path>payment/paypal_wps_express/active</activity_path>
                        <learn_more_link>https://www.paypal.com/webapps/mpp/referral/paypal-express-checkout?partner_id=NB9WWHYEMVUMS</learn_more_link>
                        <backend_config>
                            <AU>
                                <sort_order>20</sort_order>
                            </AU>
                            <CA>
                                <sort_order>20</sort_order>
                            </CA>
                            <US translate="label">
                                <label>Payments Standard (Includes Express Checkout)</label>
                                <group>paypal_group_all_in_one</group>
                                <fields>
                                    <wps_express_checkout_required>
                                        <fields>
                                            <wps_express_checkout_required_express_checkout>
                                                <label>Payments Standard</label>
                                            </wps_express_checkout_required_express_checkout>
                                            <enable_wps_express_checkout_bml extends="//paypal_payments/express_checkout//enable_express_checkout_bml">
                                                <requires>wps_express_checkout_required_enable_wps_express_checkout</requires>
                                            </enable_wps_express_checkout_bml>
                                            <wps_express_advertise_bml type="group" translate="label comment">
                                                <label>Advertise Paypal Credit</label>
                                                <comment>
                                                    <![CDATA[<a href="https://financing.paypal.com/ppfinportal/content/whyUseFinancing" target="_blank">Why Advertise Financing?</a><br/>
                                            <strong>Give your sales a boost when you advertise financing.</strong><br/>PayPal helps turn browsers into buyers with financing
                                            from Paypal Credit&#174;. Your customers have more time to pay, while you get paid up front – at no additional cost to you.
                                            Use PayPal’s free banner ads that let you advertise Paypal Credit&#174; financing as a payment option when your customers check out with PayPal.
                                            The PayPal Advertising Program has been shown to generate additional purchases as well as increase consumer's average purchase sizes by 15%
                                            or more. <a href="https://financing.paypal.com/ppfinportal/content/forrester" target="_blank">See Details</a>.]]>
                                                </comment>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>32</sort_order>
                                                <fields>
                                                    <bml_publisher_id extends="//paypal_payments/express_checkout//advertise_bml//bml_publisher_id" />
                                                    <bml_wizard extends="//paypal_payments/express_checkout//advertise_bml//bml_wizard" />
                                                    <wps_express_settings_bml_homepage type="group" translate="label">
                                                        <label>Home Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>20</sort_order>
                                                        <fields>
                                                            <bml_homepage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_display"/>
                                                            <wps_express_bml_homepage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_position"/>
                                                            <wps_express_bml_homepage_size1 translate="label" extends="//paypal_payments/express_checkout//advertise_bml//_bml_homepage_size1">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/homepage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPH</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><wps_express_bml_homepage_position>0</wps_express_bml_homepage_position></depends>
                                                            </wps_express_bml_homepage_size1>
                                                            <wps_express_bml_homepage_size2 extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_size2">
                                                                <depends><wps_express_bml_homepage_position>1</wps_express_bml_homepage_position></depends>
                                                            </wps_express_bml_homepage_size2>
                                                        </fields>
                                                    </wps_express_settings_bml_homepage>
                                                    <wps_express_settings_bml_categorypage type="group" translate="label">
                                                        <label>Catalog Category Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>30</sort_order>
                                                        <fields>
                                                            <bml_categorypage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_display"/>
                                                            <wps_express_bml_categorypage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_position" />
                                                            <wps_express_bml_categorypage_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/categorypage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><wps_express_bml_categorypage_position>0</wps_express_bml_categorypage_position></depends>
                                                            </wps_express_bml_categorypage_size1>
                                                            <wps_express_bml_categorypage_size2 extends="//paypal_payments/wps_express//wps_express_bml_categorypage_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPS</source_model>
                                                                <depends><wps_express_bml_categorypage_position>1</wps_express_bml_categorypage_position></depends>
                                                            </wps_express_bml_categorypage_size2>
                                                        </fields>

                                                    </wps_express_settings_bml_categorypage>
                                                    <wps_express_settings_bml_productpage type="group" translate="label">
                                                        <label>Catalog Product Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>40</sort_order>
                                                        <fields>
                                                            <bml_productpage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_display" />
                                                            <wps_express_bml_productpage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_position" />
                                                            <wps_express_bml_productpage_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/productpage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><wps_express_bml_productpage_position>0</wps_express_bml_productpage_position></depends>
                                                            </wps_express_bml_productpage_size1>
                                                            <wps_express_bml_productpage_size2 extends="//paypal_payments/wps_express//wps_express_bml_productpage_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPN</source_model>
                                                                <depends><wps_express_bml_productpage_position>1</wps_express_bml_productpage_position></depends>
                                                            </wps_express_bml_productpage_size2>
                                                        </fields>

                                                    </wps_express_settings_bml_productpage>
                                                    <wps_express_settings_bml_checkout type="group" translate="label">
                                                        <label>Checkout Cart Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>50</sort_order>
                                                        <fields>
                                                            <bml_checkout_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_display" />
                                                            <wps_express_bml_checkout_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_position" />
                                                            <wps_express_bml_checkout_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/checkout_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><wps_express_bml_checkout_position>0</wps_express_bml_checkout_position></depends>
                                                            </wps_express_bml_checkout_size1>
                                                            <wps_express_bml_checkout_size2 extends="//paypal_payments/wps_express//wps_express_bml_checkout_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutN</source_model>
                                                                <shared>1</shared>
                                                                <depends><wps_express_bml_checkout_position>1</wps_express_bml_checkout_position></depends>
                                                            </wps_express_bml_checkout_size2>
                                                        </fields>
                                                    </wps_express_settings_bml_checkout>
                                                </fields>
                                            </wps_express_advertise_bml>
                                        </fields>
                                    </wps_express_checkout_required>
                                    <settings_wps_express translate="label">
                                        <label>Basic Settings - PayPal Payments Standard</label>
                                    </settings_wps_express>
                                </fields>
                            </US>
                            <GB translate="comment">
                                <learn_more_link>https://www.paypal-business.co.uk/add-paypal-to-online-shop/index.htm</learn_more_link>
                                <demo_link>http://www.youtube.com/watch?v=WgnfZ2Bj6hQ</demo_link>
                                <comment>Add PayPal as an additional payment method to your checkout page to increase your sales.</comment>
                            </GB>
                        </backend_config>
                        <fields>
                            <wps_express_checkout_required type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <sort_order>10</sort_order>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <fields>
                                    <wps_express_checkout_required_express_checkout type="group" translate="label">
                                        <label>PayPal Website Payments Standard</label>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>10</sort_order>
                                        <fields>
                                            <business_account extends="//paypal_payments/payflow_advanced//business_account" />
                                            <api_authentication extends="//paypal_payments/payflow_advanced//api_authentication" />
                                            <api_username extends="//paypal_payments/payflow_advanced//api_username" />
                                            <api_password extends="//paypal_payments/payflow_advanced//api_password" />
                                            <api_signature extends="//paypal_payments/payflow_advanced//api_signature" />
                                            <api_cert extends="//paypal_payments/payflow_advanced//api_cert" />
                                            <api_wizard extends="//paypal_payments/payflow_advanced//api_wizard" />
                                            <sandbox_flag extends="//paypal_payments/payflow_advanced//express//sandbox_flag" />
                                            <use_proxy extends="//paypal_payments/payflow_advanced//express//use_proxy" />
                                            <proxy_host extends="//paypal_payments/payflow_advanced//express//proxy_host" />
                                            <proxy_port extends="//paypal_payments/payflow_advanced//express//proxy_port" />
                                        </fields>
                                    </wps_express_checkout_required_express_checkout>
                                    <enable_wps_express_checkout translate="label">
                                        <label>Enable this Solution</label>
                                        <config_path>payment/paypal_wps_express/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <shared>1</shared>
                                        <requires>wps_express_checkout_required_express_checkout</requires>
                                        <frontend_class>paypal-enabler</frontend_class>
                                    </enable_wps_express_checkout>
                                </fields>
                            </wps_express_checkout_required>
                            <settings_wps_express type="group" translate="label">
                                <label>Basic Settings - PayPal Website Payments Standard</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/title" />
                                    <sort_order extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/sort_order" />
                                    <payment_action extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/payment_action" />
                                    <visible_on_product extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/visible_on_product" />
                                    <authorization_honor_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/authorization_honor_period" />
                                    <order_valid_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/order_valid_period" />
                                    <child_authorization_number extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/child_authorization_number" />

                                    <settings_wps_express_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>80</sort_order>
                                        <fields>
                                            <visible_on_cart extends="//paypal_payments/payflow_advanced//settings_express_checkout//visible_on_cart" />
                                            <allowspecific extends="//paypal_payments/payflow_advanced//settings_express_checkout//allowspecific" />
                                            <specificcountry extends="//paypal_payments/payflow_advanced//settings_express_checkout//specificcountry" />
                                            <debug extends="//paypal_payments/payflow_advanced//settings_express_checkout//debug" />
                                            <verify_peer extends="//paypal_payments/payflow_advanced//settings_express_checkout//verify_peer" />
                                            <line_items_enabled extends="//paypal_payments/payflow_advanced//settings_express_checkout//line_items_enabled" />
                                            <transfer_shipping_options extends="//paypal_payments/payflow_advanced//settings_express_checkout//transfer_shipping_options" />
                                            <button_flavor extends="//paypal_payments/payflow_advanced//settings_express_checkout//button_flavor" />
                                            <solution_type extends="//paypal_payments/payflow_advanced//settings_express_checkout//solution_type" />
                                            <require_billing_address extends="//paypal_payments/payflow_advanced//settings_express_checkout//require_billing_address" />
                                            <allow_ba_signup extends="//paypal_payments/payflow_advanced//settings_express_checkout//allow_ba_signup" />
                                            <skip_order_review_step extends="//paypal_payments/payflow_advanced//settings_express_checkout//skip_order_review_step" />

                                            <wps_express_billing_agreement type="group" translate="label">
                                                <label>PayPal Billing Agreement Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>100</sort_order>
                                                <fields>
                                                    <active extends="//paypal_payments/payflow_advanced//billing_agreement/*/active" />
                                                    <title extends="//paypal_payments/payflow_advanced//billing_agreement/*/title" />
                                                    <sort_order extends="//paypal_payments/payflow_advanced//billing_agreement/*/sort_order" />
                                                    <payment_action extends="//paypal_payments/payflow_advanced//billing_agreement/*/payment_action" />
                                                    <allowspecific extends="//paypal_payments/payflow_advanced//billing_agreement/*/allowspecific" />
                                                    <specificcountry extends="//paypal_payments/payflow_advanced//billing_agreement/*/specificcountry" />
                                                    <debug extends="//paypal_payments/payflow_advanced//billing_agreement/*/debug" />
                                                    <verify_peer extends="//paypal_payments/payflow_advanced//billing_agreement/*/verify_peer" />
                                                    <line_items_enabled extends="//paypal_payments/payflow_advanced//billing_agreement/*/line_items_enabled" />
                                                    <allow_billing_agreement_wizard extends="//paypal_payments/payflow_advanced//billing_agreement/*/allow_billing_agreement_wizard" />
                                                </fields>
                                            </wps_express_billing_agreement>
                                            <wps_express_settlement_report type="group" translate="label">
                                                <label>Settlement Report Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>110</sort_order>
                                                <fields>
                                                    <heading_sftp extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_sftp" />
                                                    <settlement_reports_ftp_login extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_login" />
                                                    <settlement_reports_ftp_password extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_password" />
                                                    <settlement_reports_ftp_sandbox extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_sandbox" />
                                                    <settlement_reports_ftp_ip extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_ip" />
                                                    <settlement_reports_ftp_path extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_path" />
                                                    <heading_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_schedule" />
                                                    <settlement_reports_active extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_active" />
                                                    <settlement_reports_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_schedule" />
                                                    <settlement_reports_time extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_time" />
                                                </fields>
                                            </wps_express_settlement_report>
                                            <wps_express_frontend type="group" translate="label">
                                                <label>Frontend Experience Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>120</sort_order>
                                                <fields>
                                                    <logo extends="//paypal_payments/payflow_advanced//frontend/*/logo" />
                                                    <paypal_pages extends="//paypal_payments/payflow_advanced//frontend/*/paypal_pages" />
                                                    <page_style extends="//paypal_payments/payflow_advanced//frontend/*/page_style" />
                                                    <paypal_hdrimg extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrimg" />
                                                    <paypal_hdrbackcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbackcolor" />
                                                    <paypal_hdrbordercolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbordercolor" />
                                                    <paypal_payflowcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_payflowcolor" />
                                                </fields>
                                            </wps_express_frontend>
                                        </fields>
                                    </settings_wps_express_advanced>
                                </fields>
                            </settings_wps_express>
                        </fields>
                    </wps_express>
                    <paypal_verisign type="group" translate="label comment">
                        <label>Payflow Pro</label>
                        <frontend_type>text</frontend_type>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_payment</frontend_model>
                        <frontend_class>pp-method-payflow</frontend_class>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>10</sort_order>
                        <group>paypal_payment_solutions</group>
                        <comment>Connect your merchant account with a fully customizable gateway that lets customers pay without leaving your site.</comment>
                        <activity_path>payment/verisign/active</activity_path>
                        <learn_more_link>https://www.paypal.com/webapps/mpp/referral/paypal-payflow-pro?partner_id=NB9WWHYEMVUMS</learn_more_link>
                        <paypal_ec_separate>1</paypal_ec_separate>
                        <backend_config>
                                <CA>
                                    <include>1</include>
                                    <sort_order>30</sort_order>
                                </CA>
                                <AU>
                                    <include>1</include>
                                    <sort_order>20</sort_order>
                                </AU>
                                <NZ>
                                    <include>1</include>
                                    <sort_order>20</sort_order>
                                </NZ>
                            </backend_config>
                        <fields>
                            <paypal_payflow_required type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>10</sort_order>
                                <fields>
                                    <paypal_payflow_api_settings type="group" translate="label">
                                        <label>Payflow Pro</label>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>10</sort_order>
                                        <fields>
                                            <partner translate="label">
                                                <label>Partner</label>
                                                <config_path>payment/verisign/partner</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </partner>
                                            <user translate="label">
                                                <label>User</label>
                                                <config_path>payment/verisign/user</config_path>
                                                <frontend_type>obscure</frontend_type>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </user>
                                            <vendor translate="label">
                                                <label>Vendor</label>
                                                <config_path>payment/verisign/vendor</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </vendor>
                                            <pwd translate="label">
                                                <label>Password</label>
                                                <config_path>payment/verisign/pwd</config_path>
                                                <frontend_type>obscure</frontend_type>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </pwd>
                                            <sandbox_flag translate="label">
                                                <label>Test Mode</label>
                                                <config_path>payment/verisign/sandbox_flag</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </sandbox_flag>
                                            <use_proxy translate="label">
                                                <label>Use Proxy</label>
                                                <config_path>payment/verisign/use_proxy</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </use_proxy>
                                            <proxy_host translate="label">
                                                <label>Proxy Host</label>
                                                <config_path>payment/verisign/proxy_host</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>80</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                            </proxy_host>
                                            <proxy_port translate="label">
                                                <label>Proxy Port</label>
                                                <config_path>payment/verisign/proxy_port</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>90</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                            </proxy_port>
                                        </fields>
                                    </paypal_payflow_api_settings>
                                    <enable_paypal_payflow translate="label">
                                        <label>Enable this Solution</label>
                                        <config_path>payment/verisign/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <requires>paypal_payflow_api_settings</requires>
                                        <frontend_class>paypal-enabler paypal-ec-separate</frontend_class>
                                    </enable_paypal_payflow>
                                </fields>
                            </paypal_payflow_required>
                            <settings_paypal_payflow type="group" translate="label">
                                <label>Basic Settings - PayPal Payflow Pro</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title translate="label comment">
                                        <label>Title</label>
                                        <comment>It is recommended to set this value to "Debit or Credit Card" per store views.</comment>
                                        <config_path>payment/verisign/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/verisign/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/verisign/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </payment_action>
                                    <heading_cc translate="label">
                                        <label>Credit Card Settings</label>
                                        <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                        <sort_order>40</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </heading_cc>
                                    <cctypes translate="label comment">
                                        <label>Allowed Credit Card Types</label>
                                        <comment><![CDATA[Supporting of American Express cards require additional agreement. Learn more at <a href="http://www.paypal.com/amexupdate">http://www.paypal.com/amexupdate</a>.]]></comment>
                                        <config_path>payment/verisign/cctypes</config_path>
                                        <frontend_type>multiselect</frontend_type>
                                        <source_model>paypal/config::getPayflowproCcTypesAsOptionArray</source_model>
                                        <sort_order>50</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </cctypes>
                                    <settings_paypal_payflow_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>60</sort_order>
                                        <fields>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/verisign/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <sort_order>10</sort_order>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/verisign/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <sort_order>20</sort_order>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                            </specificcountry>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/verisign/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/verisign/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>35</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </verify_peer>
                                            <useccv translate="label">
                                                <label>Require CVV Entry</label>
                                                <config_path>payment/verisign/useccv</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </useccv>
                                            <heading_3dsecure translate="label">
                                                <label>3D Secure</label>
                                                <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </heading_3dsecure>
                                            <centinel translate="label">
                                                <label>3D Secure Card Validation</label>
                                                <config_path>payment/verisign/centinel</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </centinel>
                                            <centinel_is_mode_strict translate="label comment">
                                                <label>Severe 3D Secure Card Validation</label>
                                                <config_path>payment/verisign/centinel_is_mode_strict</config_path>
                                                <comment>Severe validation removes chargeback liability on merchant.</comment>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><centinel>1</centinel></depends>
                                            </centinel_is_mode_strict>
                                            <centinel_api_url translate="label comment">
                                                <label>Centinel API URL</label>
                                                <config_path>payment/verisign/centinel_api_url</config_path>
                                                <comment>A value is required for live mode. Refer to your CardinalCommerce agreement.</comment>
                                                <frontend_type>text</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>80</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><centinel>1</centinel></depends>
                                            </centinel_api_url>
                                            <paypal_payflow_settlement_report type="group" translate="label">
                                                <label>Settlement Report Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>90</sort_order>
                                                <fields>
                                                    <heading_sftp extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_sftp" />
                                                    <settlement_reports_ftp_login extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_login" />
                                                    <settlement_reports_ftp_password extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_password" />
                                                    <settlement_reports_ftp_sandbox extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_sandbox" />
                                                    <settlement_reports_ftp_ip extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_ip" />
                                                    <settlement_reports_ftp_path extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_path" />
                                                    <heading_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_schedule" />
                                                    <settlement_reports_active extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_active" />
                                                    <settlement_reports_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_schedule" />
                                                    <settlement_reports_time extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_time" />
                                                </fields>
                                            </paypal_payflow_settlement_report>
                                        </fields>
                                    </settings_paypal_payflow_advanced>
                                </fields>
                            </settings_paypal_payflow>
                        </fields>
                    </paypal_verisign>
                    <paypal_verisign_with_express_checkout type="group" translate="label comment" extends="//paypal_payments/paypal_verisign">
                        <label>Payflow Pro (Includes Express Checkout)</label>
                        <paypal_ec_separate>0</paypal_ec_separate>
                        <backend_config>
                            <AU>
                                <include>0</include>
                            </AU>
                            <CA>
                                <include>0</include>
                            </CA>
                            <US>
                                <include>1</include>
                                <group>paypal_payment_gateways</group>
                                <fields>
                                    <paypal_payflow_required>
                                        <fields>
                                            <paypal_payflow_api_settings>
                                                <fields>
                                                    <business_account extends="//paypal_payments/payflow_advanced//business_account" translate="label">
                                                        <sort_order>10</sort_order>
                                                        <frontend_class>not-required</frontend_class>
                                                        <label>Email Associated with PayPal Merchant Account (Optional)</label>
                                                    </business_account>
                                                </fields>
                                            </paypal_payflow_api_settings>
                                            <enable_paypal_payflow>
                                                <frontend_class>paypal-enabler paypal-ec-pe</frontend_class>
                                            </enable_paypal_payflow>
                                            <enable_express_checkout_bml_uk>
                                                <label>Enable Paypal Credit</label>
                                                <comment><![CDATA[PayPal Express Checkout Payflow Edition lets you give customers access to financing through Paypal Credit&#174; - at no additional cost to you.
                                                You get paid up front, even though customers have more time to pay. A pre-integrated payment button lets customers pay quickly with Paypal Credit&#174;.
                                                <a href="https://www.paypal.com/webapps/mpp/promotional-financing" target="_blank">Learn More</a>]]>
                                                </comment>
                                                <config_path>payment/paypaluk_express_bml/active</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>21</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                                <frontend_class>paypal-bml</frontend_class>
                                                <requires>paypal_payflow_required_enable_paypal_payflow</requires>
                                            </enable_express_checkout_bml_uk>
                                            <paypal_payflow_advertise_bml type="group" translate="label comment">
                                                <label>Advertise Paypal Credit</label>
                                                <comment>
                                                    <![CDATA[<a href="https://financing.paypal.com/ppfinportal/content/whyUseFinancing" target="_blank">Why Advertise Financing?</a><br/>
                                                    <strong>Give your sales a boost when you advertise financing.</strong><br/>PayPal helps turn browsers into buyers with financing
                                                    from Paypal Credit&#174;. Your customers have more time to pay, while you get paid up front – at no additional cost to you.
                                                    Use PayPal’s free banner ads that let you advertise Paypal Credit&#174; financing as a payment option when your customers check out with PayPal.
                                                    The PayPal Advertising Program has been shown to generate additional purchases as well as increase consumer's average purchase sizes by 15%
                                                    or more. <a href="https://financing.paypal.com/ppfinportal/content/forrester" target="_blank">See Details</a>.]]>
                                                </comment>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>22</sort_order>
                                                <fields>
                                                    <bml_publisher_id extends="//paypal_payments/express_checkout//advertise_bml//bml_publisher_id" />
                                                    <bml_wizard extends="//paypal_payments/express_checkout//advertise_bml//bml_wizard" />
                                                    <paypal_payflow_settings_bml_homepage type="group" translate="label">
                                                        <label>Home Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>20</sort_order>
                                                        <fields>
                                                            <bml_homepage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_display"/>
                                                            <paypal_payflow_bml_homepage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_position"/>
                                                            <paypal_payflow_bml_homepage_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/homepage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPH</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><paypal_payflow_bml_homepage_position>0</paypal_payflow_bml_homepage_position></depends>
                                                            </paypal_payflow_bml_homepage_size1>
                                                            <paypal_payflow_bml_homepage_size2 extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_bml_homepage_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPS</source_model>
                                                                <depends><paypal_payflow_bml_homepage_position>1</paypal_payflow_bml_homepage_position></depends>
                                                            </paypal_payflow_bml_homepage_size2>
                                                        </fields>
                                                    </paypal_payflow_settings_bml_homepage>
                                                    <paypal_payflow_settings_bml_categorypage type="group" translate="label">
                                                        <label>Catalog Category Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>30</sort_order>
                                                        <fields>
                                                            <bml_categorypage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_display"/>
                                                            <paypal_payflow_bml_categorypage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_position" />
                                                            <paypal_payflow_bml_categorypage_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/categorypage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><paypal_payflow_bml_categorypage_position>0</paypal_payflow_bml_categorypage_position></depends>
                                                            </paypal_payflow_bml_categorypage_size1>
                                                            <paypal_payflow_bml_categorypage_size2 extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_bml_categorypage_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPS</source_model>
                                                                <depends><paypal_payflow_bml_categorypage_position>1</paypal_payflow_bml_categorypage_position></depends>
                                                            </paypal_payflow_bml_categorypage_size2>
                                                        </fields>

                                                    </paypal_payflow_settings_bml_categorypage>
                                                    <paypal_payflow_settings_bml_productpage type="group" translate="label">
                                                        <label>Catalog Product Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>40</sort_order>
                                                        <fields>
                                                            <bml_productpage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_display" />
                                                            <paypal_payflow_bml_productpage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_position" />
                                                            <paypal_payflow_bml_productpage_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/productpage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><paypal_payflow_bml_productpage_position>0</paypal_payflow_bml_productpage_position></depends>
                                                            </paypal_payflow_bml_productpage_size1>
                                                            <paypal_payflow_bml_productpage_size2 extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_bml_productpage_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPN</source_model>
                                                                <depends><paypal_payflow_bml_productpage_position>1</paypal_payflow_bml_productpage_position></depends>
                                                            </paypal_payflow_bml_productpage_size2>
                                                        </fields>

                                                    </paypal_payflow_settings_bml_productpage>
                                                    <paypal_payflow_settings_bml_checkout type="group" translate="label">
                                                        <label>Checkout Cart Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>50</sort_order>
                                                        <fields>
                                                            <bml_checkout_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_display" />
                                                            <paypal_payflow_bml_checkout_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_position" />
                                                            <paypal_payflow_bml_checkout_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/checkout_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><paypal_payflow_bml_checkout_position>0</paypal_payflow_bml_checkout_position></depends>
                                                            </paypal_payflow_bml_checkout_size1>
                                                            <paypal_payflow_bml_checkout_size2 extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_bml_checkout_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutN</source_model>
                                                                <shared>1</shared>
                                                                <depends><paypal_payflow_bml_checkout_position>1</paypal_payflow_bml_checkout_position></depends>
                                                            </paypal_payflow_bml_checkout_size2>
                                                        </fields>

                                                    </paypal_payflow_settings_bml_checkout>
                                                </fields>
                                            </paypal_payflow_advertise_bml>

                                        </fields>
                                    </paypal_payflow_required>
                                </fields>
                            </US>
                            <NZ>
                                <include>0</include>
                            </NZ>
                        </backend_config>
                        <fields>
                            <paypal_payflow_required type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <sort_order>10</sort_order>
                                <fields>
                                    <paypal_payflow_api_settings type="group" translate="label">
                                        <label>Payflow Pro and Express Checkout</label>
                                    </paypal_payflow_api_settings>
                                </fields>
                            </paypal_payflow_required>
                            <settings_paypal_payflow type="group" translate="label">
                                <fields>
                                    <settings_paypal_payflow_advanced type="group" translate="label">
                                        <fields>
                                            <paypal_payflow_frontend type="group" translate="label">
                                                <label>Frontend Experience Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>100</sort_order>
                                                <fields>
                                                    <logo extends="//paypal_payments/payflow_advanced//frontend/*/logo" />
                                                    <paypal_pages extends="//paypal_payments/payflow_advanced//frontend/*/paypal_pages" />
                                                    <page_style extends="//paypal_payments/payflow_advanced//frontend/*/page_style" />
                                                    <paypal_hdrimg extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrimg" />
                                                    <paypal_hdrbackcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbackcolor" />
                                                    <paypal_hdrbordercolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbordercolor" />
                                                    <paypal_payflowcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_payflowcolor" />
                                                </fields>
                                            </paypal_payflow_frontend>
                                        </fields>
                                    </settings_paypal_payflow_advanced>
                                </fields>
                            </settings_paypal_payflow>
                            <paypal_payflow_express_checkout type="group" translate="label">
                                <label>Basic Settings - PayPal Express Checkout</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>30</sort_order>
                                <fields>
                                    <enable_express_pe>
                                        <config_path>payment/paypaluk_express/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <frontend_model>paypal/adminhtml_system_config_field_hidden</frontend_model>
                                        <frontend_class>paypal-ec-payflow-enabler</frontend_class>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </enable_express_pe>
                                    <title translate="label">
                                        <label>Title</label>
                                        <config_path>payment/paypaluk_express/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/paypaluk_express/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/paypaluk_express/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </payment_action>
                                    <visible_on_product translate="label">
                                        <label>Display on Product Details Page</label>
                                        <config_path>payment/paypaluk_express/visible_on_product</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>50</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </visible_on_product>
                                    <paypal_payflow_express_checkout_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <sort_order>60</sort_order>
                                        <fields>
                                            <visible_on_cart translate="label comment">
                                                <label>Display on Shopping Cart</label>
                                                <comment>Also affects mini-shopping cart.</comment>
                                                <config_path>payment/paypaluk_express/visible_on_cart</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>paypal/system_config_source_yesnoShortcut</source_model>
                                                <sort_order>5</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                            </visible_on_cart>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/paypaluk_express/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <sort_order>10</sort_order>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/paypaluk_express/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <sort_order>20</sort_order>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                            </specificcountry>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/paypaluk_express/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/paypaluk_express/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>35</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </verify_peer>
                                            <line_items_enabled translate="label">
                                                <label>Transfer Cart Line Items</label>
                                                <config_path>payment/paypaluk_express/line_items_enabled</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </line_items_enabled>
                                        </fields>
                                    </paypal_payflow_express_checkout_advanced>
                                </fields>
                            </paypal_payflow_express_checkout>
                        </fields>
                    </paypal_verisign_with_express_checkout>
                    <payflow_link type="group" translate="label comment">
                        <label>Payflow Link (Includes Express Checkout)</label>
                        <frontend_type>text</frontend_type>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>20</sort_order>
                        <group>paypal_payment_solutions</group>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_payment</frontend_model>
                        <frontend_class>pp-method-payflow</frontend_class>
                        <comment>Connect your merchant account with a PCI-compliant gateway that lets customers pay without leaving your site.</comment>
                        <activity_path>payment/payflow_link/active</activity_path>
                        <learn_more_link>https://www.paypal.com/webapps/mpp/referral/paypal-payflow-link?partner_id=NB9WWHYEMVUMS</learn_more_link>
                        <backend_config>
                                <CA>
                                    <include>1</include>
                                    <sort_order>40</sort_order>
                                </CA>
                                <US>
                                    <include>1</include>
                                    <group>paypal_payment_gateways</group>
                                    <fields>
                                        <payflow_link_required>
                                            <fields>
                                                <enable_express_checkout_bml  extends="//paypal_payments/express_checkout//enable_express_checkout_bml">
                                                    <comment><![CDATA[Payflow Link lets you give customers access to financing through Paypal Credit&#174; - at no additional cost to you.
                                        You get paid up front, even though customers have more time to pay. A pre-integrated payment button lets customers pay quickly with Paypal Credit&#174;.
                                        <a href="https://www.paypal.com/webapps/mpp/promotional-financing" target="_blank">Learn More</a>]]>
                                                    </comment>
                                                    <sort_order>41</sort_order>
                                                    <requires>payflow_link_required_enable_express_checkout</requires>
                                                </enable_express_checkout_bml>
                                                <payflow_link_advertise_bml type="group" translate="label comment">
                                                    <label>Advertise Paypal Credit</label>
                                                    <comment>
                                                        <![CDATA[<a href="https://financing.paypal.com/ppfinportal/content/whyUseFinancing" target="_blank">Why Advertise Financing?</a><br/>
                                            <strong>Give your sales a boost when you advertise financing.</strong><br/>PayPal helps turn browsers into buyers with financing
                                            from Paypal Credit&#174;. Your customers have more time to pay, while you get paid up front – at no additional cost to you.
                                            Use PayPal’s free banner ads that let you advertise Paypal Credit&#174; financing as a payment option when your customers check out with PayPal.
                                            The PayPal Advertising Program has been shown to generate additional purchases as well as increase consumer's average purchase sizes by 15%
                                            or more. <a href="https://financing.paypal.com/ppfinportal/content/forrester" target="_blank">See Details</a>.]]>
                                                    </comment>
                                                    <frontend_type>text</frontend_type>
                                                    <show_in_default>1</show_in_default>
                                                    <show_in_website>1</show_in_website>
                                                    <sort_order>42</sort_order>
                                                    <fields>
                                                        <bml_publisher_id extends="//paypal_payments/express_checkout//advertise_bml//bml_publisher_id" />
                                                        <bml_wizard extends="//paypal_payments/express_checkout//advertise_bml//bml_wizard" />
                                                        <payflow_link_settings_bml_homepage type="group" translate="label">
                                                            <label>Home Page</label>
                                                            <frontend_type>text</frontend_type>
                                                            <show_in_default>1</show_in_default>
                                                            <show_in_website>1</show_in_website>
                                                            <show_in_store>1</show_in_store>
                                                            <sort_order>20</sort_order>
                                                            <fields>
                                                                <bml_homepage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_display"/>
                                                                <payflow_link_bml_homepage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_homepage_position"/>
                                                                <payflow_link_bml_homepage_size1 translate="label">
                                                                    <label>Size</label>
                                                                    <config_path>payment/paypal_express_bml/homepage_size</config_path>
                                                                    <frontend_type>select</frontend_type>
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPH</source_model>
                                                                    <show_in_default>1</show_in_default>
                                                                    <show_in_website>1</show_in_website>
                                                                    <sort_order>30</sort_order>
                                                                    <shared>1</shared>
                                                                    <depends><payflow_link_bml_homepage_position>0</payflow_link_bml_homepage_position></depends>
                                                                </payflow_link_bml_homepage_size1>
                                                                <payflow_link_bml_homepage_size2 extends="//paypal_payments/payflow_link//payflow_link_bml_homepage_size1">
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPS</source_model>
                                                                    <depends><payflow_link_bml_homepage_position>1</payflow_link_bml_homepage_position></depends>
                                                                </payflow_link_bml_homepage_size2>
                                                            </fields>
                                                        </payflow_link_settings_bml_homepage>
                                                        <payflow_link_settings_bml_categorypage type="group" translate="label">
                                                            <label>Catalog Category Page</label>
                                                            <frontend_type>text</frontend_type>
                                                            <show_in_default>1</show_in_default>
                                                            <show_in_website>1</show_in_website>
                                                            <show_in_store>1</show_in_store>
                                                            <sort_order>30</sort_order>
                                                            <fields>
                                                                <bml_categorypage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_display"/>
                                                                <payflow_link_bml_categorypage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_categorypage_position" />
                                                                <payflow_link_bml_categorypage_size1 translate="label">
                                                                    <label>Size</label>
                                                                    <config_path>payment/paypal_express_bml/categorypage_size</config_path>
                                                                    <frontend_type>select</frontend_type>
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPC</source_model>
                                                                    <show_in_default>1</show_in_default>
                                                                    <show_in_website>1</show_in_website>
                                                                    <sort_order>30</sort_order>
                                                                    <shared>1</shared>
                                                                    <depends><payflow_link_bml_categorypage_position>0</payflow_link_bml_categorypage_position></depends>
                                                                </payflow_link_bml_categorypage_size1>
                                                                <payflow_link_bml_categorypage_size2 extends="//paypal_payments/payflow_link//payflow_link_bml_categorypage_size1">
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPS</source_model>
                                                                    <depends><payflow_link_bml_categorypage_position>1</payflow_link_bml_categorypage_position></depends>
                                                                </payflow_link_bml_categorypage_size2>
                                                            </fields>

                                                        </payflow_link_settings_bml_categorypage>
                                                        <payflow_link_settings_bml_productpage type="group" translate="label">
                                                            <label>Catalog Product Page</label>
                                                            <frontend_type>text</frontend_type>
                                                            <show_in_default>1</show_in_default>
                                                            <show_in_website>1</show_in_website>
                                                            <show_in_store>1</show_in_store>
                                                            <sort_order>40</sort_order>
                                                            <fields>
                                                                <bml_productpage_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_display" />
                                                                <payflow_link_bml_productpage_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_productpage_position" />
                                                                <payflow_link_bml_productpage_size1 translate="label">
                                                                    <label>Size</label>
                                                                    <config_path>payment/paypal_express_bml/productpage_size</config_path>
                                                                    <frontend_type>select</frontend_type>
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPC</source_model>
                                                                    <show_in_default>1</show_in_default>
                                                                    <show_in_website>1</show_in_website>
                                                                    <sort_order>30</sort_order>
                                                                    <shared>1</shared>
                                                                    <depends><payflow_link_bml_productpage_position>0</payflow_link_bml_productpage_position></depends>
                                                                </payflow_link_bml_productpage_size1>
                                                                <payflow_link_bml_productpage_size2 extends="//paypal_payments/payflow_link//payflow_link_bml_productpage_size1">
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPN</source_model>
                                                                    <depends><payflow_link_bml_productpage_position>1</payflow_link_bml_productpage_position></depends>
                                                                </payflow_link_bml_productpage_size2>
                                                            </fields>

                                                        </payflow_link_settings_bml_productpage>
                                                        <payflow_link_settings_bml_checkout type="group" translate="label">
                                                            <label>Checkout Cart Page</label>
                                                            <frontend_type>text</frontend_type>
                                                            <show_in_default>1</show_in_default>
                                                            <show_in_website>1</show_in_website>
                                                            <show_in_store>1</show_in_store>
                                                            <sort_order>50</sort_order>
                                                            <fields>
                                                                <bml_checkout_display translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_display" />
                                                                <payflow_link_bml_checkout_position translate="label" extends="//paypal_payments/express_checkout//advertise_bml//bml_checkout_position" />
                                                                <payflow_link_bml_checkout_size1 translate="label">
                                                                    <label>Size</label>
                                                                    <config_path>payment/paypal_express_bml/checkout_size</config_path>
                                                                    <frontend_type>select</frontend_type>
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutC</source_model>
                                                                    <show_in_default>1</show_in_default>
                                                                    <show_in_website>1</show_in_website>
                                                                    <sort_order>30</sort_order>
                                                                    <shared>1</shared>
                                                                    <depends><payflow_link_bml_checkout_position>0</payflow_link_bml_checkout_position></depends>
                                                                </payflow_link_bml_checkout_size1>
                                                                <payflow_link_bml_checkout_size2 extends="//paypal_payments/payflow_link//payflow_link_bml_checkout_size1">
                                                                    <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutN</source_model>
                                                                    <shared>1</shared>
                                                                    <depends><payflow_link_bml_checkout_position>1</payflow_link_bml_checkout_position></depends>
                                                                </payflow_link_bml_checkout_size2>
                                                            </fields>

                                                        </payflow_link_settings_bml_checkout>
                                                    </fields>
                                                </payflow_link_advertise_bml>
                                            </fields>
                                        </payflow_link_required>
                                    </fields>
                                </US>
                            </backend_config>
                        <fields>
                            <payflow_link_required type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>10</sort_order>
                                <fields>
                                    <payflow_link_payflow_link type="group" translate="label">
                                        <label>Payflow Link</label>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>10</sort_order>
                                        <fields>
                                            <partner translate="label">
                                                <label>Partner</label>
                                                <config_path>payment/payflow_link/partner</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>10</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </partner>
                                            <vendor translate="label">
                                                <label>Vendor</label>
                                                <config_path>payment/payflow_link/vendor</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </vendor>
                                            <user translate="label comment">
                                                <label>User</label>
                                                <comment>If you do not have multiple users set up on your account, please re-enter your Vendor/Merchant Login here.</comment>
                                                <frontend_type>text</frontend_type>
                                                <config_path>payment/payflow_link/user</config_path>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </user>
                                            <pwd translate="label">
                                                <label>Password</label>
                                                <config_path>payment/payflow_link/pwd</config_path>
                                                <frontend_type>obscure</frontend_type>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </pwd>
                                            <sandbox_flag translate="label">
                                                <label>Test Mode</label>
                                                <config_path>payment/payflow_link/sandbox_flag</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </sandbox_flag>
                                            <use_proxy translate="label">
                                                <label>Use Proxy</label>
                                                <config_path>payment/payflow_link/use_proxy</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </use_proxy>
                                            <proxy_host translate="label">
                                                <label>Proxy Host</label>
                                                <config_path>payment/payflow_link/proxy_host</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                            </proxy_host>
                                            <proxy_port translate="label">
                                                <label>Proxy Port</label>
                                                <config_path>payment/payflow_link/proxy_port</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>80</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                            </proxy_port>
                                            <payflowlink_info>
                                                <frontend_model>paypal/adminhtml_system_config_payflowlink_info</frontend_model>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>90</sort_order>
                                            </payflowlink_info>
                                        </fields>
                                    </payflow_link_payflow_link>
                                    <enable_payflow_link translate="label">
                                        <label>Enable Payflow Link</label>
                                        <config_path>payment/payflow_link/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <requires>payflow_link_payflow_link</requires>
                                        <frontend_class>paypal-enabler paypal-ec-independent</frontend_class>
                                    </enable_payflow_link>
                                    <payflow_link_express_checkout type="group" translate="label">
                                        <label>Express Checkout</label>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>30</sort_order>
                                        <fields>
                                            <business_account extends="//paypal_payments/payflow_advanced//business_account" />
                                            <api_authentication extends="//paypal_payments/payflow_advanced//api_authentication" />
                                            <api_username extends="//paypal_payments/payflow_advanced//api_username" />
                                            <api_password extends="//paypal_payments/payflow_advanced//api_password" />
                                            <api_signature extends="//paypal_payments/payflow_advanced//api_signature" />
                                            <api_cert extends="//paypal_payments/payflow_advanced//api_cert" />
                                            <api_wizard extends="//paypal_payments/payflow_advanced//api_wizard" />
                                            <sandbox_flag extends="//paypal_payments/payflow_advanced//express//sandbox_flag" />
                                            <use_proxy extends="//paypal_payments/payflow_advanced//express//use_proxy" />
                                            <proxy_host extends="//paypal_payments/payflow_advanced//express//proxy_host" />
                                            <proxy_port extends="//paypal_payments/payflow_advanced//express//proxy_port" />
                                        </fields>
                                    </payflow_link_express_checkout>
                                    <enable_express_checkout translate="label">
                                        <label>Enable Express Checkout</label>
                                        <config_path>payment/paypal_express/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>40</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <shared>1</shared>
                                        <requires>payflow_link_required_enable_payflow_link,payflow_link_express_checkout</requires>
                                        <frontend_class>paypal-enabler paypal-ec-enabler</frontend_class>
                                    </enable_express_checkout>

                                </fields>
                            </payflow_link_required>
                            <settings_payflow_link type="group" translate="label">
                                <label>Basic Settings - PayPal Payflow Link</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title translate="label comment">
                                        <label>Title</label>
                                        <comment>It is recommended to set this value to "Debit or Credit Card" per store views.</comment>
                                        <config_path>payment/payflow_link/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/payflow_link/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/payflow_link/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </payment_action>
                                    <settings_payflow_link_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>40</sort_order>
                                        <fields>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/payflow_link/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <sort_order>10</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/payflow_link/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                            </specificcountry>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/payflow_link/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/payflow_link/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>35</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </verify_peer>
                                            <csc_editable translate="label">
                                                <label>CVV Entry is Editable</label>
                                                <config_path>payment/payflow_link/csc_editable</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </csc_editable>
                                            <csc_required translate="label">
                                                <label>Require CVV Entry</label>
                                                <config_path>payment/payflow_link/csc_required</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><csc_editable>1</csc_editable></depends>
                                            </csc_required>
                                            <email_confirmation translate="label">
                                                <label>Send Email Confirmation</label>
                                                <config_path>payment/payflow_link/email_confirmation</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </email_confirmation>
                                            <url_method translate="label">
                                                <label>URL method for Cancel URL and Return URL</label>
                                                <config_path>payment/payflow_link/url_method</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>paypal/system_config_source_urlMethod</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </url_method>
                                            <mobile_optimized translate="label">
                                                <label>Mobile Optimized</label>
                                                <config_path>payment/payflow_link/mobile_optimized</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>75</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </mobile_optimized>
                                            <payflow_link_settlement_report type="group" translate="label">
                                                <label>Settlement Report Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>80</sort_order>
                                                <fields>
                                                    <heading_sftp extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_sftp" />
                                                    <settlement_reports_ftp_login extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_login" />
                                                    <settlement_reports_ftp_password extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_password" />
                                                    <settlement_reports_ftp_sandbox extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_sandbox" />
                                                    <settlement_reports_ftp_ip extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_ip" />
                                                    <settlement_reports_ftp_path extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_path" />
                                                    <heading_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_schedule" />
                                                    <settlement_reports_active extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_active" />
                                                    <settlement_reports_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_schedule" />
                                                    <settlement_reports_time extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_time" />
                                                </fields>
                                            </payflow_link_settlement_report>
                                            <payflow_link_frontend type="group" translate="label">
                                                <label>Frontend Experience Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>90</sort_order>
                                                <fields>
                                                    <logo extends="//paypal_payments/payflow_advanced//frontend/*/logo" />
                                                    <paypal_pages extends="//paypal_payments/payflow_advanced//frontend/*/paypal_pages" />
                                                    <page_style extends="//paypal_payments/payflow_advanced//frontend/*/page_style" />
                                                    <paypal_hdrimg extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrimg" />
                                                    <paypal_hdrbackcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbackcolor" />
                                                    <paypal_hdrbordercolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbordercolor" />
                                                    <paypal_payflowcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_payflowcolor" />
                                                </fields>
                                            </payflow_link_frontend>
                                        </fields>
                                    </settings_payflow_link_advanced>
                                </fields>
                            </settings_payflow_link>
                            <settings_payflow_link_express_checkout type="group" translate="label">
                                <label>Basic Settings - PayPal Express Checkout</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>30</sort_order>
                                <fields>
                                    <title extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/title" />
                                    <sort_order extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/sort_order" />
                                    <payment_action extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/payment_action" />
                                    <visible_on_product extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/visible_on_product" />
                                    <authorization_honor_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/authorization_honor_period" />
                                    <order_valid_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/order_valid_period" />
                                    <child_authorization_number extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/child_authorization_number" />

                                    <settings_payflow_link_express_checkout_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>80</sort_order>
                                        <fields>
                                            <visible_on_cart extends="//paypal_payments/payflow_advanced//settings_express_checkout//visible_on_cart" />
                                            <allowspecific extends="//paypal_payments/payflow_advanced//settings_express_checkout//allowspecific" />
                                            <specificcountry extends="//paypal_payments/payflow_advanced//settings_express_checkout//specificcountry" />
                                            <debug extends="//paypal_payments/payflow_advanced//settings_express_checkout//debug" />
                                            <verify_peer extends="//paypal_payments/payflow_advanced//settings_express_checkout//verify_peer" />
                                            <line_items_enabled extends="//paypal_payments/payflow_advanced//settings_express_checkout//line_items_enabled" />
                                            <transfer_shipping_options extends="//paypal_payments/payflow_advanced//settings_express_checkout//transfer_shipping_options" />
                                            <button_flavor extends="//paypal_payments/payflow_advanced//settings_express_checkout//button_flavor" />
                                            <solution_type extends="//paypal_payments/payflow_advanced//settings_express_checkout//solution_type" />
                                            <require_billing_address extends="//paypal_payments/payflow_advanced//settings_express_checkout//require_billing_address" />
                                            <allow_ba_signup extends="//paypal_payments/payflow_advanced//settings_express_checkout//allow_ba_signup" />
                                            <skip_order_review_step extends="//paypal_payments/payflow_advanced//settings_express_checkout//skip_order_review_step" />

                                            <payflow_link_billing_agreement type="group" translate="label">
                                                <label>PayPal Billing Agreement Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>100</sort_order>
                                                <fields>
                                                    <active extends="//paypal_payments/payflow_advanced//billing_agreement/*/active" />
                                                    <title extends="//paypal_payments/payflow_advanced//billing_agreement/*/title" />
                                                    <sort_order extends="//paypal_payments/payflow_advanced//billing_agreement/*/sort_order" />
                                                    <payment_action extends="//paypal_payments/payflow_advanced//billing_agreement/*/payment_action" />
                                                    <allowspecific extends="//paypal_payments/payflow_advanced//billing_agreement/*/allowspecific" />
                                                    <specificcountry extends="//paypal_payments/payflow_advanced//billing_agreement/*/specificcountry" />
                                                    <debug extends="//paypal_payments/payflow_advanced//billing_agreement/*/debug" />
                                                    <verify_peer extends="//paypal_payments/payflow_advanced//billing_agreement/*/verify_peer" />
                                                    <line_items_enabled extends="//paypal_payments/payflow_advanced//billing_agreement/*/line_items_enabled" />
                                                    <allow_billing_agreement_wizard extends="//paypal_payments/payflow_advanced//billing_agreement/*/allow_billing_agreement_wizard" />
                                                </fields>
                                            </payflow_link_billing_agreement>
                                        </fields>
                                    </settings_payflow_link_express_checkout_advanced>
                                </fields>
                            </settings_payflow_link_express_checkout>
                        </fields>
                    </payflow_link>
                    <payments_pro_payflow_edition type="group" translate="label comment">
                        <label>Website Payments Pro Payflow Edition (Includes Express Checkout)</label>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>40</sort_order>
                        <group>paypal_group_all_in_one</group>
                        <frontend_class>pp-general-uk</frontend_class>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_deprecated</frontend_model>
                        <was_enabled_path>payment/paypaluk_direct/was_active</was_enabled_path>
                        <activity_path>payment/paypaluk_direct/active</activity_path>
                        <comment>Accept payments in your shopping cart. PayPal will process your card payments through the Payflow Gateway.</comment>
                        <backend_config>
                            <GB>
                                <include>1</include>
                            </GB>
                        </backend_config>
                        <fields>
                            <pp_pe_required_settings type="group" translate="label">
                                <label>Required Settings</label>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>10</sort_order>
                                <fields>
                                    <pp_pe_api_settings type="group" translate="label">
                                        <label>Website Payments Pro Payflow Edition and Express Checkout</label>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>10</sort_order>
                                        <fields>
                                            <business_account extends="//paypal_payments/payflow_advanced//business_account" translate="label">
                                                <sort_order>10</sort_order>
                                                <frontend_class>not-required</frontend_class>
                                                <label>Email Associated with PayPal Merchant Account (Optional)</label>
                                            </business_account>
                                            <partner translate="label">
                                                <label>Partner</label>
                                                <config_path>paypal/wpuk/partner</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </partner>
                                            <user translate="label">
                                                <label>User</label>
                                                <frontend_type>obscure</frontend_type>
                                                <config_path>paypal/wpuk/user</config_path>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </user>
                                            <vendor translate="label">
                                                <label>Vendor</label>
                                                <config_path>paypal/wpuk/vendor</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </vendor>
                                            <pwd translate="label">
                                                <label>Password</label>
                                                <config_path>paypal/wpuk/pwd</config_path>
                                                <frontend_type>obscure</frontend_type>
                                                <backend_model>adminhtml/system_config_backend_encrypted</backend_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </pwd>
                                            <sandbox_flag translate="label">
                                                <label>Test Mode</label>
                                                <config_path>paypal/wpuk/sandbox_flag</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </sandbox_flag>
                                            <use_proxy translate="label">
                                                <label>API Uses Proxy</label>
                                                <config_path>paypal/wpuk/use_proxy</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </use_proxy>
                                            <proxy_host translate="label">
                                                <label>Proxy Host</label>
                                                <config_path>paypal/wpuk/proxy_host</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>80</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                            </proxy_host>
                                            <proxy_port translate="label">
                                                <label>Proxy Port</label>
                                                <config_path>paypal/wpuk/proxy_port</config_path>
                                                <frontend_type>text</frontend_type>
                                                <sort_order>90</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><use_proxy>1</use_proxy></depends>
                                            </proxy_port>
                                        </fields>
                                    </pp_pe_api_settings>
                                    <enable_pp_pe translate="label">
                                        <label>Enable this Solution</label>
                                        <config_path>payment/paypaluk_direct/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <requires>pp_pe_api_settings</requires>
                                        <frontend_class>paypal-enabler paypal-ec-pe</frontend_class>
                                    </enable_pp_pe>

                                </fields>
                            </pp_pe_required_settings>
                            <settings_pp_pe type="group" translate="label">
                                <label>Basic Settings - PayPal Website Payments Pro Payflow Edition</label>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title translate="label comment">
                                        <label>Title</label>
                                        <comment>It is recommended to set this value to "Debit or Credit Card" per store views.</comment>
                                        <config_path>payment/paypaluk_direct/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/paypaluk_direct/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/paypaluk_direct/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </payment_action>
                                    <heading_cc translate="label">
                                        <label>Credit Card Settings</label>
                                        <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                        <sort_order>40</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </heading_cc>
                                    <cctypes translate="label">
                                        <label>Allowed Credit Card Types</label>
                                        <comment><![CDATA[3D Secure validation is required for Maestro cards.]]></comment>
                                        <config_path>payment/paypaluk_direct/cctypes</config_path>
                                        <frontend_type>multiselect</frontend_type>
                                        <source_model>paypal/config::getWppPeCcTypesAsOptionArray</source_model>
                                        <sort_order>50</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </cctypes>
                                    <settings_pp_pe_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>60</sort_order>
                                        <fields>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/paypaluk_direct/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <sort_order>10</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/paypaluk_direct/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                            </specificcountry>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/paypaluk_direct/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/paypaluk_direct/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>35</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </verify_peer>
                                            <line_items_enabled translate="label">
                                                <label>Transfer Cart Line Items</label>
                                                <config_path>payment/paypaluk_direct/line_items_enabled</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>37</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </line_items_enabled>
                                            <useccv translate="label">
                                                <label>Require CVV Entry</label>
                                                <config_path>payment/paypaluk_direct/useccv</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>40</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </useccv>
                                            <heading_3dsecure translate="label">
                                                <label>3D Secure</label>
                                                <frontend_model>adminhtml/system_config_form_field_heading</frontend_model>
                                                <sort_order>50</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </heading_3dsecure>
                                            <centinel translate="label">
                                                <label>3D Secure Card Validation</label>
                                                <config_path>payment/paypaluk_direct/centinel</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>60</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </centinel>
                                            <centinel_is_mode_strict translate="label comment">
                                                <label>Severe 3D Secure Card Validation</label>
                                                <config_path>payment/paypaluk_direct/centinel_is_mode_strict</config_path>
                                                <comment>Severe Validation Removes Chargeback Liability on Merchant</comment>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>70</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><centinel>1</centinel></depends>
                                            </centinel_is_mode_strict>
                                            <centinel_api_url translate="label comment">
                                                <label>Centinel API URL</label>
                                                <config_path>payment/paypaluk_direct/centinel_api_url</config_path>
                                                <comment>If empty, a default value will be used. Custom URL may be provided by CardinalCommerce agreement.</comment>
                                                <frontend_type>text</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>80</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><centinel>1</centinel></depends>
                                            </centinel_api_url>
                                            <pp_pe_settlement_report type="group" translate="label">
                                                <label>Settlement Report Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>90</sort_order>
                                                <fields>
                                                    <heading_sftp extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_sftp" />
                                                    <settlement_reports_ftp_login extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_login" />
                                                    <settlement_reports_ftp_password extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_password" />
                                                    <settlement_reports_ftp_sandbox extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_sandbox" />
                                                    <settlement_reports_ftp_ip extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_ip" />
                                                    <settlement_reports_ftp_path extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_path" />
                                                    <heading_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_schedule" />
                                                    <settlement_reports_active extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_active" />
                                                    <settlement_reports_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_schedule" />
                                                    <settlement_reports_time extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_time" />
                                                </fields>
                                            </pp_pe_settlement_report>
                                            <pp_pe_frontend type="group" translate="label">
                                                <label>Frontend Experience Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>100</sort_order>
                                                <fields>
                                                    <logo extends="//paypal_payments/payflow_advanced//frontend/*/logo" />
                                                    <paypal_pages extends="//paypal_payments/payflow_advanced//frontend/*/paypal_pages" />
                                                    <page_style extends="//paypal_payments/payflow_advanced//frontend/*/page_style" />
                                                    <paypal_hdrimg extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrimg" />
                                                    <paypal_hdrbackcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbackcolor" />
                                                    <paypal_hdrbordercolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbordercolor" />
                                                    <paypal_payflowcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_payflowcolor" />
                                                </fields>
                                            </pp_pe_frontend>
                                        </fields>
                                    </settings_pp_pe_advanced>
                                </fields>
                            </settings_pp_pe>
                            <settings_pp_pe_express_checkout type="group" translate="label">
                                <label>Basic Settings - PayPal Express Checkout</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>30</sort_order>
                                <fields>
                                    <enable_express_pe extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_express_checkout/*/enable_express_pe" />
                                    <title extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_express_checkout/*/title" />
                                    <sort_order extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_express_checkout/*/sort_order" />
                                    <payment_action extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_express_checkout/*/payment_action" />
                                    <visible_on_product extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_express_checkout/*/visible_on_product" />
                                    <pp_pe_express_checkout_advanced type="group" extends="//paypal_payments/paypal_verisign_with_express_checkout//paypal_payflow_express_checkout_advanced" />
                                </fields>
                            </settings_pp_pe_express_checkout>
                        </fields>
                    </payments_pro_payflow_edition>
                    <payments_pro_hosted_solution type="group" translate="label comment">
                        <label>Website Payments Pro Hosted Solution</label>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>20</sort_order>
                        <group>paypal_payment_solutions</group>
                        <frontend_class>pp-method-general</frontend_class>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_payment</frontend_model>
                        <activity_path>payment/hosted_pro/active</activity_path>
                        <comment>Accept payments with a PCI compliant checkout that keeps customers on your site.</comment>
                        <paypal_ec_separate>1</paypal_ec_separate>
                        <backend_config>
                            <JP translate="label">
                                <label>Website Payments Plus</label>
                                <include>1</include>
                                <fields>
                                    <pphs_required_settings>
                                        <fields>
                                            <pphs_required_settings_pphs translate="label">
                                                <label>Website Payments Plus</label>
                                            </pphs_required_settings_pphs>
                                        </fields>
                                    </pphs_required_settings>
                                    <pphs_settings translate="label">
                                        <label>Basic Settings - PayPal Website Payments Plus</label>
                                    </pphs_settings>
                                </fields>
                            </JP>
                            <FR translate="label">
                                <label>Integral Evolution</label>
                                <include>1</include>
                                <fields>
                                    <pphs_required_settings>
                                        <fields>
                                            <pphs_required_settings_pphs translate="label">
                                                <label>Integral Evolution</label>
                                            </pphs_required_settings_pphs>
                                        </fields>
                                    </pphs_required_settings>
                                    <pphs_settings translate="label">
                                        <label>Basic Settings - PayPal Integral Evolution</label>
                                    </pphs_settings>
                                </fields>
                            </FR>
                            <IT translate="label">
                                <label>Pro</label>
                                <include>1</include>
                                <fields>
                                    <pphs_required_settings>
                                        <fields>
                                            <pphs_required_settings_pphs translate="label">
                                                <label>PayPal Pro</label>
                                            </pphs_required_settings_pphs>
                                        </fields>
                                    </pphs_required_settings>
                                    <pphs_settings translate="label">
                                        <label>Basic Settings - PayPal Pro</label>
                                    </pphs_settings>
                                </fields>
                            </IT>
                            <ES translate="label">
                                <label>Pasarela integral</label>
                                <include>1</include>
                                <fields>
                                    <pphs_required_settings>
                                        <fields>
                                            <pphs_required_settings_pphs translate="label">
                                                <label>Pasarela integral</label>
                                            </pphs_required_settings_pphs>
                                        </fields>
                                    </pphs_required_settings>
                                    <pphs_settings translate="label">
                                        <label>Basic Settings - PayPal Pasarela integral</label>
                                    </pphs_settings>
                                </fields>
                            </ES>
                            <HK>
                                <include>1</include>
                            </HK>
                            <AU>
                                <include>1</include>
                                <sort_order>30</sort_order>
                            </AU>
                        </backend_config>
                        <fields>
                            <pphs_required_settings type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>10</sort_order>
                                <fields>
                                    <pphs_required_settings_pphs type="group" translate="label">
                                        <label>Payments Pro Hosted Solution</label>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>10</sort_order>
                                        <fields>
                                            <business_account extends="//paypal_payments/payflow_advanced//business_account" />
                                            <api_authentication extends="//paypal_payments/payflow_advanced//api_authentication" />
                                            <api_username extends="//paypal_payments/payflow_advanced//api_username" />
                                            <api_password extends="//paypal_payments/payflow_advanced//api_password" />
                                            <api_signature extends="//paypal_payments/payflow_advanced//api_signature" />
                                            <api_cert extends="//paypal_payments/payflow_advanced//api_cert" />
                                            <api_wizard extends="//paypal_payments/payflow_advanced//api_wizard" />
                                            <sandbox_flag extends="//paypal_payments/payflow_advanced//express//sandbox_flag" />
                                            <use_proxy extends="//paypal_payments/payflow_advanced//express//use_proxy" />
                                            <proxy_host extends="//paypal_payments/payflow_advanced//express//proxy_host" />
                                            <proxy_port extends="//paypal_payments/payflow_advanced//express//proxy_port" />
                                        </fields>
                                    </pphs_required_settings_pphs>
                                    <pphs_enable translate="label">
                                        <label>Enable this Solution</label>
                                        <config_path>payment/hosted_pro/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <requires>pphs_required_settings_pphs</requires>
                                        <frontend_class>paypal-enabler paypal-ec-separate</frontend_class>
                                    </pphs_enable>
                                </fields>
                            </pphs_required_settings>
                            <pphs_settings type="group" translate="label">
                                <label>Basic Settings - PayPal Payments Pro Hosted Solution</label>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title translate="label comment">
                                        <label>Title</label>
                                        <comment>It is recommended to set this value to "PayPal" per store views.</comment>
                                        <config_path>payment/hosted_pro/title</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>10</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                    </title>
                                    <sort_order translate="label">
                                        <label>Sort Order</label>
                                        <config_path>payment/hosted_pro/sort_order</config_path>
                                        <frontend_type>text</frontend_type>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <frontend_class>validate-number</frontend_class>
                                    </sort_order>
                                    <payment_action translate="label">
                                        <label>Payment Action</label>
                                        <config_path>payment/hosted_pro/payment_action</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>paypal/system_config_source_paymentActions</source_model>
                                        <sort_order>30</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </payment_action>
                                    <display_ec translate="label">
                                        <label>Display Express Checkout in the Payment Information step</label>
                                        <config_path>payment/hosted_pro/display_ec</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>40</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                    </display_ec>
                                    <pphs_settings_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>50</sort_order>
                                        <fields>
                                            <allowspecific translate="label">
                                                <label>Payment Applicable From</label>
                                                <config_path>payment/hosted_pro/allowspecific</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_payment_allspecificcountries</source_model>
                                                <sort_order>10</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </allowspecific>
                                            <specificcountry translate="label">
                                                <label>Countries Payment Applicable From</label>
                                                <config_path>payment/hosted_pro/specificcountry</config_path>
                                                <frontend_type>multiselect</frontend_type>
                                                <source_model>paypal/system_config_source_buyerCountry</source_model>
                                                <sort_order>20</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <depends><allowspecific>1</allowspecific></depends>
                                            </specificcountry>
                                            <debug translate="label">
                                                <label>Debug Mode</label>
                                                <config_path>payment/hosted_pro/debug</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>30</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </debug>
                                            <verify_peer translate="label">
                                                <label>Enable SSL verification</label>
                                                <config_path>payment/hosted_pro/verify_peer</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>35</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </verify_peer>
                                            <mobile_optimized translate="label">
                                                <label>Mobile Optimized</label>
                                                <config_path>payment/hosted_pro/mobile_optimized</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>37</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                            </mobile_optimized>
                                            <pphs_settlement_report type="group" translate="label">
                                                <label>Settlement Report Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>45</sort_order>
                                                <fields>
                                                    <heading_sftp extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_sftp" />
                                                    <settlement_reports_ftp_login extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_login" />
                                                    <settlement_reports_ftp_password extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_password" />
                                                    <settlement_reports_ftp_sandbox extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_sandbox" />
                                                    <settlement_reports_ftp_ip extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_ip" />
                                                    <settlement_reports_ftp_path extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_path" />
                                                    <heading_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_schedule" />
                                                    <settlement_reports_active extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_active" />
                                                    <settlement_reports_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_schedule" />
                                                    <settlement_reports_time extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_time" />
                                                </fields>
                                            </pphs_settlement_report>
                                        </fields>
                                    </pphs_settings_advanced>
                                </fields>
                            </pphs_settings>
                        </fields>
                    </payments_pro_hosted_solution>
                    <payments_pro_hosted_solution_with_express_checkout type="group" translate="label comment" extends="//paypal_payments/payments_pro_hosted_solution">
                        <label>Website Payments Pro Hosted Solution (Includes Express Checkout)</label>
                        <sort_order>30</sort_order>
                        <group>paypal_group_all_in_one</group>
                        <frontend_class>pp-general-uk</frontend_class>
                        <learn_more_link>https://cms.paypal.com/cms_content/GB/en_GB/files/developer/HostedSolution.pdf</learn_more_link>
                        <paypal_ec_separate>0</paypal_ec_separate>
                        <backend_config>
                            <JP>
                                <include>0</include>
                            </JP>
                            <FR>
                                <include>0</include>
                            </FR>
                            <IT>
                                <include>0</include>
                            </IT>
                            <ES>
                                <include>0</include>
                            </ES>
                            <HK>
                                <include>0</include>
                            </HK>
                            <AU>
                                <include>0</include>
                            </AU>
                            <GB>
                                <include>1</include>
                                <fields>
                                    <pphs_required_settings>
                                        <fields>
                                            <pphs_enable>
                                                <frontend_class>paypal-enabler</frontend_class>
                                            </pphs_enable>
                                        </fields>
                                    </pphs_required_settings>
                                </fields>
                            </GB>
                        </backend_config>
                        <fields>
                            <pphs_required_settings type="group">
                                <fields>
                                    <pphs_required_settings_pphs type="group" translate="label">
                                        <label>Website Payments Pro Hosted Solution and Express Checkout</label>
                                    </pphs_required_settings_pphs>
                                </fields>
                            </pphs_required_settings>
                            <pphs_settings type="group" translate="label">
                                <label>Basic Settings - PayPal Website Payments Pro Hosted Solution</label>
                                <fields>
                                    <pphs_settings_advanced type="group" translate="label">
                                        <fields>
                                            <pphs_billing_agreement type="group" translate="label">
                                                <label>PayPal Billing Agreement Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>40</sort_order>
                                                <fields>
                                                    <active extends="//paypal_payments/payflow_advanced//billing_agreement/*/active" />
                                                    <title extends="//paypal_payments/payflow_advanced//billing_agreement/*/title" />
                                                    <sort_order extends="//paypal_payments/payflow_advanced//billing_agreement/*/sort_order" />
                                                    <payment_action extends="//paypal_payments/payflow_advanced//billing_agreement/*/payment_action" />
                                                    <allowspecific extends="//paypal_payments/payflow_advanced//billing_agreement/*/allowspecific" />
                                                    <specificcountry extends="//paypal_payments/payflow_advanced//billing_agreement/*/specificcountry" />
                                                    <debug extends="//paypal_payments/payflow_advanced//billing_agreement/*/debug" />
                                                    <verify_peer extends="//paypal_payments/payflow_advanced//billing_agreement/*/verify_peer" />
                                                    <line_items_enabled extends="//paypal_payments/payflow_advanced//billing_agreement/*/line_items_enabled" />
                                                    <allow_billing_agreement_wizard extends="//paypal_payments/payflow_advanced//billing_agreement/*/allow_billing_agreement_wizard" />
                                                </fields>
                                            </pphs_billing_agreement>
                                            <pphs_frontend type="group" translate="label">
                                                <label>Frontend Experience Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>50</sort_order>
                                                <fields>
                                                    <logo extends="//paypal_payments/payflow_advanced//frontend/*/logo" />
                                                    <paypal_pages extends="//paypal_payments/payflow_advanced//frontend/*/paypal_pages" />
                                                    <page_style extends="//paypal_payments/payflow_advanced//frontend/*/page_style" />
                                                    <paypal_hdrimg extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrimg" />
                                                    <paypal_hdrbackcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbackcolor" />
                                                    <paypal_hdrbordercolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbordercolor" />
                                                    <paypal_payflowcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_payflowcolor" />
                                                </fields>
                                            </pphs_frontend>
                                        </fields>
                                    </pphs_settings_advanced>
                                </fields>
                            </pphs_settings>
                            <pphs_settings_express_checkout type="group" translate="label">
                                <label>Basic Settings - PayPal Express Checkout</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>30</sort_order>
                                <fields>
                                    <title extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/title" />
                                    <sort_order extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/sort_order" />
                                    <payment_action extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/payment_action" />
                                    <visible_on_product extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/visible_on_product" />
                                    <authorization_honor_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/authorization_honor_period" />
                                    <order_valid_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/order_valid_period" />
                                    <child_authorization_number extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/child_authorization_number" />

                                    <pphs_settings_express_checkout_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>80</sort_order>
                                        <fields>
                                            <visible_on_cart extends="//paypal_payments/payflow_advanced//settings_express_checkout//visible_on_cart" />
                                            <allowspecific extends="//paypal_payments/payflow_advanced//settings_express_checkout//allowspecific" />
                                            <specificcountry extends="//paypal_payments/payflow_advanced//settings_express_checkout//specificcountry" />
                                            <debug extends="//paypal_payments/payflow_advanced//settings_express_checkout//debug" />
                                            <verify_peer extends="//paypal_payments/payflow_advanced//settings_express_checkout//verify_peer" />
                                            <line_items_enabled extends="//paypal_payments/payflow_advanced//settings_express_checkout//line_items_enabled" />
                                            <transfer_shipping_options extends="//paypal_payments/payflow_advanced//settings_express_checkout//transfer_shipping_options" />
                                            <button_flavor extends="//paypal_payments/payflow_advanced//settings_express_checkout//button_flavor" />
                                            <solution_type extends="//paypal_payments/payflow_advanced//settings_express_checkout//solution_type" />
                                            <require_billing_address extends="//paypal_payments/payflow_advanced//settings_express_checkout//require_billing_address" />
                                            <allow_ba_signup extends="//paypal_payments/payflow_advanced//settings_express_checkout//allow_ba_signup" />
                                            <skip_order_review_step extends="//paypal_payments/payflow_advanced//settings_express_checkout//skip_order_review_step" />
                                        </fields>
                                    </pphs_settings_express_checkout_advanced>
                                </fields>
                            </pphs_settings_express_checkout>
                        </fields>
                    </payments_pro_hosted_solution_with_express_checkout>
                    <express_checkout type="group" translate="label comment">
                        <label>Express Checkout</label>
                        <frontend_type>text</frontend_type>
                        <show_in_default>1</show_in_default>
                        <show_in_website>1</show_in_website>
                        <show_in_store>1</show_in_store>
                        <sort_order>60</sort_order>
                        <group>paypal_payment_solutions</group>
                        <frontend_model>paypal/adminhtml_system_config_fieldset_payment</frontend_model>
                        <frontend_class>pp-method-express</frontend_class>
                        <include>1</include>
                        <comment>Add PayPal as an additional payment method to your checkout page.</comment>
                        <activity_path>payment/paypal_express/active</activity_path>
                        <learn_more_link>https://www.paypal.com/webapps/mpp/referral/paypal-express-checkout?partner_id=NB9WWHYEMVUMS</learn_more_link>
                        <backend_config>
                            <US>
                                <group>paypal_alternative_payment_methods</group>
                                <fields>
                                    <express_checkout_required>
                                        <fields>
                                            <enable_express_checkout_bml translate="label comment">
                                                <label>Enable Paypal Credit</label>
                                                <comment><![CDATA[PayPal Express Checkout lets you give customers access to financing through Paypal Credit&#174; - at no additional cost to you.
                                        You get paid up front, even though customers have more time to pay. A pre-integrated payment button lets customers pay quickly with Paypal Credit&#174;.
                                        <a href="https://www.paypal.com/webapps/mpp/promotional-financing" target="_blank">Learn More</a>]]>
                                                </comment>
                                                <config_path>payment/paypal_express_bml/active</config_path>
                                                <frontend_type>select</frontend_type>
                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                <sort_order>21</sort_order>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <shared>1</shared>
                                                <requires>express_checkout_required_enable_express_checkout</requires>
                                                <frontend_class>paypal-bml</frontend_class>
                                            </enable_express_checkout_bml>
                                            <advertise_bml type="group" translate="label comment">
                                                <label>Advertise Paypal Credit</label>
                                                <comment>
                                                    <![CDATA[<a href="https://financing.paypal.com/ppfinportal/content/whyUseFinancing" target="_blank">Why Advertise Financing?</a><br/>
                                            <strong>Give your sales a boost when you advertise financing.</strong><br/>PayPal helps turn browsers into buyers with financing
                                            from Paypal Credit&#174;. Your customers have more time to pay, while you get paid up front – at no additional cost to you.
                                            Use PayPal’s free banner ads that let you advertise Paypal Credit&#174; financing as a payment option when your customers check out with PayPal.
                                            The PayPal Advertising Program has been shown to generate additional purchases as well as increase consumer's average purchase sizes by 15%
                                            or more. <a href="https://financing.paypal.com/ppfinportal/content/forrester" target="_blank">See Details</a>.]]>
                                                </comment>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>30</sort_order>
                                                <fields>
                                                    <bml_publisher_id translate="label comment tooltip">
                                                        <label>Publisher ID</label>
                                                        <comment><![CDATA[Required to display a banner]]></comment>
                                                        <config_path>payment/paypal_express_bml/publisher_id</config_path>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <sort_order>10</sort_order>
                                                        <shared>1</shared>
                                                    </bml_publisher_id>
                                                    <bml_wizard translate="button_label">
                                                        <label></label>
                                                        <button_label>Get Publisher ID from PayPal</button_label>
                                                        <button_url><![CDATA[https://financing.paypal.com/ppfinportal/cart/index?dcp=4eff8563b9cc505e0b9afaff3256705081553c79]]></button_url>
                                                        <frontend_model>paypal/adminhtml_system_config_bmlApiWizard</frontend_model>
                                                        <sort_order>15</sort_order>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                    </bml_wizard>
                                                    <settings_bml_homepage type="group" translate="label">
                                                        <label>Home Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>20</sort_order>
                                                        <fields>
                                                            <bml_homepage_display translate="label">
                                                                <label>Display</label>
                                                                <config_path>payment/paypal_express_bml/homepage_display</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>10</sort_order>
                                                                <shared>1</shared>
                                                            </bml_homepage_display>
                                                            <bml_homepage_position translate="label">
                                                                <label>Position</label>
                                                                <config_path>payment/paypal_express_bml/homepage_position</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlPosition::getBmlPositionsHP</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>20</sort_order>
                                                                <shared>1</shared>
                                                            </bml_homepage_position>
                                                            <bml_homepage_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/homepage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPH</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><bml_homepage_position>0</bml_homepage_position></depends>
                                                            </bml_homepage_size1>
                                                            <bml_homepage_size2 extends="//paypal_payments/express_checkout//bml_homepage_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeHPS</source_model>
                                                                <depends><bml_homepage_position>1</bml_homepage_position></depends>
                                                            </bml_homepage_size2>
                                                        </fields>
                                                    </settings_bml_homepage>
                                                    <settings_bml_categorypage type="group" translate="label">
                                                        <label>Catalog Category Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>30</sort_order>
                                                        <fields>
                                                            <bml_categorypage_display translate="label">
                                                                <label>Display</label>
                                                                <config_path>payment/paypal_express_bml/categorypage_display</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>10</sort_order>
                                                                <shared>1</shared>
                                                            </bml_categorypage_display>
                                                            <bml_categorypage_position translate="label">
                                                                <label>Position</label>
                                                                <config_path>payment/paypal_express_bml/categorypage_position</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlPosition::getBmlPositionsCCP</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>20</sort_order>
                                                                <shared>1</shared>
                                                            </bml_categorypage_position>
                                                            <bml_categorypage_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/categorypage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><bml_categorypage_position>0</bml_categorypage_position></depends>
                                                            </bml_categorypage_size1>
                                                            <bml_categorypage_size2 extends="//paypal_payments/express_checkout//bml_categorypage_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCCPS</source_model>
                                                                <depends><bml_categorypage_position>1</bml_categorypage_position></depends>
                                                            </bml_categorypage_size2>
                                                        </fields>
                                                    </settings_bml_categorypage>
                                                    <settings_bml_productpage type="group" translate="label">
                                                        <label>Catalog Product Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>40</sort_order>
                                                        <fields>
                                                            <bml_productpage_display translate="label">
                                                                <label>Display</label>
                                                                <config_path>payment/paypal_express_bml/productpage_display</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>10</sort_order>
                                                                <shared>1</shared>
                                                            </bml_productpage_display>
                                                            <bml_productpage_position translate="label">
                                                                <label>Position</label>
                                                                <config_path>payment/paypal_express_bml/productpage_position</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlPosition::getBmlPositionsCPP</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>20</sort_order>
                                                                <shared>1</shared>
                                                            </bml_productpage_position>
                                                            <bml_productpage_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/productpage_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><bml_productpage_position>0</bml_productpage_position></depends>
                                                            </bml_productpage_size1>
                                                            <bml_productpage_size2 extends="//paypal_payments/express_checkout//bml_productpage_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCPPN</source_model>
                                                                <depends><bml_productpage_position>1</bml_productpage_position></depends>
                                                            </bml_productpage_size2>
                                                        </fields>
                                                    </settings_bml_productpage>
                                                    <settings_bml_checkout type="group" translate="label">
                                                        <label>Checkout Cart Page</label>
                                                        <frontend_type>text</frontend_type>
                                                        <show_in_default>1</show_in_default>
                                                        <show_in_website>1</show_in_website>
                                                        <show_in_store>1</show_in_store>
                                                        <sort_order>50</sort_order>
                                                        <fields>
                                                            <bml_checkout_display translate="label">
                                                                <label>Display</label>
                                                                <config_path>payment/paypal_express_bml/checkout_display</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>adminhtml/system_config_source_yesno</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>10</sort_order>
                                                                <shared>1</shared>
                                                            </bml_checkout_display>
                                                            <bml_checkout_position translate="label">
                                                                <label>Position</label>
                                                                <config_path>payment/paypal_express_bml/checkout_position</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlPosition::getBmlPositionsCheckout</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>20</sort_order>
                                                                <shared>1</shared>
                                                            </bml_checkout_position>
                                                            <bml_checkout_size1 translate="label">
                                                                <label>Size</label>
                                                                <config_path>payment/paypal_express_bml/checkout_size</config_path>
                                                                <frontend_type>select</frontend_type>
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutC</source_model>
                                                                <show_in_default>1</show_in_default>
                                                                <show_in_website>1</show_in_website>
                                                                <sort_order>30</sort_order>
                                                                <shared>1</shared>
                                                                <depends><bml_checkout_position>0</bml_checkout_position></depends>
                                                            </bml_checkout_size1>
                                                            <bml_checkout_size2 extends="//paypal_payments/express_checkout//bml_checkout_size1">
                                                                <source_model>paypal/system_config_source_bmlSize::getBmlSizeCheckoutN</source_model>
                                                                <shared>1</shared>
                                                                <depends><bml_checkout_position>1</bml_checkout_position></depends>
                                                            </bml_checkout_size2>
                                                        </fields>
                                                    </settings_bml_checkout>
                                                </fields>
                                            </advertise_bml>
                                        </fields>
                                    </express_checkout_required>
                                </fields>
                            </US>
                            <GB translate="comment">
                                <group>paypal_alternative_payment_methods</group>
                                <learn_more_link>https://www.paypal-business.co.uk/add-paypal-to-online-shop/index.htm</learn_more_link>
                                <demo_link>http://www.youtube.com/watch?v=WgnfZ2Bj6hQ</demo_link>
                                <comment>Add PayPal as an additional payment method to your checkout page to increase your sales.</comment>
                            </GB>
                        </backend_config>
                        <fields>
                            <express_checkout_required type="group" translate="label">
                                <label>Required PayPal Settings</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <sort_order>10</sort_order>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <fields>
                                    <express_checkout_required_express_checkout type="group" translate="label">
                                        <label>Express Checkout</label>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                        <sort_order>10</sort_order>
                                        <fields>
                                            <business_account extends="//paypal_payments/payflow_advanced//business_account" />
                                            <api_authentication extends="//paypal_payments/payflow_advanced//api_authentication" />
                                            <api_username extends="//paypal_payments/payflow_advanced//api_username" />
                                            <api_password extends="//paypal_payments/payflow_advanced//api_password" />
                                            <api_signature extends="//paypal_payments/payflow_advanced//api_signature" />
                                            <api_cert extends="//paypal_payments/payflow_advanced//api_cert" />
                                            <api_wizard extends="//paypal_payments/payflow_advanced//api_wizard" />
                                            <sandbox_flag extends="//paypal_payments/payflow_advanced//express//sandbox_flag" />
                                            <use_proxy extends="//paypal_payments/payflow_advanced//express//use_proxy" />
                                            <proxy_host extends="//paypal_payments/payflow_advanced//express//proxy_host" />
                                            <proxy_port extends="//paypal_payments/payflow_advanced//express//proxy_port" />
                                        </fields>
                                    </express_checkout_required_express_checkout>
                                    <enable_express_checkout translate="label">
                                        <label>Enable this Solution</label>
                                        <config_path>payment/paypal_express/active</config_path>
                                        <frontend_type>select</frontend_type>
                                        <source_model>adminhtml/system_config_source_yesno</source_model>
                                        <sort_order>20</sort_order>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <shared>1</shared>
                                        <requires>express_checkout_required_express_checkout</requires>
                                        <frontend_class>paypal-enabler paypal-ec-enabler</frontend_class>
                                    </enable_express_checkout>

                                </fields>
                            </express_checkout_required>
                            <settings_ec type="group" translate="label">
                                <label>Basic Settings - PayPal Express Checkout</label>
                                <frontend_type>text</frontend_type>
                                <show_in_default>1</show_in_default>
                                <show_in_website>1</show_in_website>
                                <show_in_store>1</show_in_store>
                                <frontend_model>paypal/adminhtml_system_config_fieldset_expanded</frontend_model>
                                <sort_order>20</sort_order>
                                <fields>
                                    <title extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/title" />
                                    <sort_order extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/sort_order" />
                                    <payment_action extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/payment_action" />
                                    <visible_on_product extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/visible_on_product" />
                                    <authorization_honor_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/authorization_honor_period" />
                                    <order_valid_period extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/order_valid_period" />
                                    <child_authorization_number extends="//paypal_payments/payflow_advanced//settings_express_checkout/*/child_authorization_number" />

                                    <settings_ec_advanced type="group" translate="label">
                                        <label>Advanced Settings</label>
                                        <frontend_class>config-advanced</frontend_class>
                                        <frontend_type>text</frontend_type>
                                        <show_in_default>1</show_in_default>
                                        <show_in_website>1</show_in_website>
                                        <show_in_store>1</show_in_store>
                                        <sort_order>80</sort_order>
                                        <fields>
                                            <visible_on_cart extends="//paypal_payments/payflow_advanced//settings_express_checkout//visible_on_cart" />
                                            <allowspecific extends="//paypal_payments/payflow_advanced//settings_express_checkout//allowspecific" />
                                            <specificcountry extends="//paypal_payments/payflow_advanced//settings_express_checkout//specificcountry" />
                                            <debug extends="//paypal_payments/payflow_advanced//settings_express_checkout//debug" />
                                            <verify_peer extends="//paypal_payments/payflow_advanced//settings_express_checkout//verify_peer" />
                                            <line_items_enabled extends="//paypal_payments/payflow_advanced//settings_express_checkout//line_items_enabled" />
                                            <transfer_shipping_options extends="//paypal_payments/payflow_advanced//settings_express_checkout//transfer_shipping_options" />
                                            <button_flavor extends="//paypal_payments/payflow_advanced//settings_express_checkout//button_flavor" />
                                            <solution_type extends="//paypal_payments/payflow_advanced//settings_express_checkout//solution_type" />
                                            <require_billing_address extends="//paypal_payments/payflow_advanced//settings_express_checkout//require_billing_address" />
                                            <allow_ba_signup extends="//paypal_payments/payflow_advanced//settings_express_checkout//allow_ba_signup" />
                                            <skip_order_review_step extends="//paypal_payments/payflow_advanced//settings_express_checkout//skip_order_review_step" />

                                            <express_checkout_billing_agreement type="group" translate="label">
                                                <label>PayPal Billing Agreement Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>100</sort_order>
                                                <fields>
                                                    <active extends="//paypal_payments/payflow_advanced//billing_agreement/*/active" />
                                                    <title extends="//paypal_payments/payflow_advanced//billing_agreement/*/title" />
                                                    <sort_order extends="//paypal_payments/payflow_advanced//billing_agreement/*/sort_order" />
                                                    <payment_action extends="//paypal_payments/payflow_advanced//billing_agreement/*/payment_action" />
                                                    <allowspecific extends="//paypal_payments/payflow_advanced//billing_agreement/*/allowspecific" />
                                                    <specificcountry extends="//paypal_payments/payflow_advanced//billing_agreement/*/specificcountry" />
                                                    <debug extends="//paypal_payments/payflow_advanced//billing_agreement/*/debug" />
                                                    <verify_peer extends="//paypal_payments/payflow_advanced//billing_agreement/*/verify_peer" />
                                                    <line_items_enabled extends="//paypal_payments/payflow_advanced//billing_agreement/*/line_items_enabled" />
                                                    <allow_billing_agreement_wizard extends="//paypal_payments/payflow_advanced//billing_agreement/*/allow_billing_agreement_wizard" />
                                                </fields>
                                            </express_checkout_billing_agreement>
                                            <express_checkout_settlement_report type="group" translate="label">
                                                <label>Settlement Report Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <sort_order>110</sort_order>
                                                <fields>
                                                    <heading_sftp extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_sftp" />
                                                    <settlement_reports_ftp_login extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_login" />
                                                    <settlement_reports_ftp_password extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_password" />
                                                    <settlement_reports_ftp_sandbox extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_sandbox" />
                                                    <settlement_reports_ftp_ip extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_ip" />
                                                    <settlement_reports_ftp_path extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_ftp_path" />
                                                    <heading_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/heading_schedule" />
                                                    <settlement_reports_active extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_active" />
                                                    <settlement_reports_schedule extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_schedule" />
                                                    <settlement_reports_time extends="//paypal_payments/payflow_advanced//settlement_report/*/settlement_reports_time" />
                                                </fields>
                                            </express_checkout_settlement_report>
                                            <express_checkout_frontend type="group" translate="label">
                                                <label>Frontend Experience Settings</label>
                                                <frontend_type>text</frontend_type>
                                                <show_in_default>1</show_in_default>
                                                <show_in_website>1</show_in_website>
                                                <show_in_store>1</show_in_store>
                                                <sort_order>120</sort_order>
                                                <fields>
                                                    <logo extends="//paypal_payments/payflow_advanced//frontend/*/logo" />
                                                    <paypal_pages extends="//paypal_payments/payflow_advanced//frontend/*/paypal_pages" />
                                                    <page_style extends="//paypal_payments/payflow_advanced//frontend/*/page_style" />
                                                    <paypal_hdrimg extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrimg" />
                                                    <paypal_hdrbackcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbackcolor" />
                                                    <paypal_hdrbordercolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_hdrbordercolor" />
                                                    <paypal_payflowcolor extends="//paypal_payments/payflow_advanced//frontend/*/paypal_payflowcolor" />
                                                </fields>
                                            </express_checkout_frontend>
                                        </fields>
                                    </settings_ec_advanced>
                                </fields>
                            </settings_ec>
                        </fields>
                    </express_checkout>
                </paypal_payments>
            </groups>
        </payment>
    </sections>
</config>
