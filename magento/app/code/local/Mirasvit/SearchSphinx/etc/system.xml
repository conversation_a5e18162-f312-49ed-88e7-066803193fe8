<?xml version="1.0" encoding="UTF-8"?>
<config>
    <sections>
        <searchsphinx translate="label" module="searchsphinx">
            <label>Sphinx Search</label>
            <tab>mstcore</tab>
            <frontend_type>text</frontend_type>
            <sort_order>101</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
                <general>
                    <label>Configuration</label>
                    <show_in_default>1</show_in_default>
                    <show_in_website>0</show_in_website>
                    <show_in_store>0</show_in_store>
                    <sort_order>10</sort_order>
                    <frontend_model>mstcore/system_config_form_fieldset</frontend_model>
                    <fields>
                        <search_engine translate="label">
                            <label>Search Engine</label>
                            <frontend_type>select</frontend_type>
                            <source_model>searchsphinx/system_config_source_searchEngine</source_model>
                            <sort_order>5</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </search_engine>

                        <host translate="label">
                            <label>Sphinx Host</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx</search_engine>
                            </depends>
                        </host>
                        <port translate="label">
                            <label>Sphinx Port</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx</search_engine>
                            </depends>
                        </port>
                        <bin_path translate="label">
                            <label>Sphinx Bin Path</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx</search_engine>
                            </depends>
                        </bin_path>
                        <indexer_expr translate="label">
                            <label>Cron expression for Full Reindex Schedule</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx</search_engine>
                            </depends>
                        </indexer_expr>
                        <indexer_delta_expr translate="label">
                            <label>Cron expression for Delta Reindex Schedule</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx</search_engine>
                            </depends>
                        </indexer_delta_expr>
                        <reindex>
                            <label></label>
                            <button_label>Run Full Reindex</button_label>
                            <button_action>reindex</button_action>
                            <frontend_model>searchsphinx/adminhtml_system_btnAction</frontend_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx</search_engine>
                            </depends>
                        </reindex>
                        <restart>
                            <label></label>
                            <button_label>Restart Sphinx daemon</button_label>
                            <button_action>stopstart</button_action>
                            <frontend_model>searchsphinx/adminhtml_system_btnAction</frontend_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx</search_engine>
                            </depends>
                        </restart>

                        <external_path>
                            <label>Base Path</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>20</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx_external</search_engine>
                            </depends>
                        </external_path>
                        <external_host translate="label">
                            <label>Sphinx Host</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx_external</search_engine>
                            </depends>
                        </external_host>
                        <external_port translate="label">
                            <label>Sphinx Port</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx_external</search_engine>
                            </depends>
                        </external_port>
                        <generate>
                            <button_label>Generate Config file</button_label>
                            <button_action>generate</button_action>
                            <frontend_model>searchsphinx/adminhtml_system_btnAction</frontend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <depends>
                                <search_engine>sphinx_external</search_engine>
                            </depends>
                        </generate>
                    </fields>
                </general>
                <advanced>
                    <label>Advanced Settings</label>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <sort_order>30</sort_order>
                    <frontend_model>mstcore/system_config_form_fieldset</frontend_model>
                    <fields>
                        <match_mode translate="label">
                            <label>Match Mode</label>
                            <frontend_type>select</frontend_type>
                            <source_model>searchsphinx/system_config_source_matchMode</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </match_mode>
                        <wildcard translate="label">
                            <label>Enable wildcard search</label>
                            <frontend_type>select</frontend_type>
                            <source_model>searchsphinx/system_config_source_wildcard</source_model>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </wildcard>
                        <snippets translate="label">
                            <label>Enable Google search snippets</label>
                            <frontend_type>select</frontend_type>
                            <source_model>searchsphinx/system_config_source_googleSitelinksSearchBox</source_model>
                            <sort_order>92</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </snippets>
                        <wildcard_exception translate="label">
                            <label>Wildcard Exceptions</label>
                            <frontend_model>searchsphinx/adminhtml_system_wildcardException</frontend_model>
                            <backend_model>adminhtml/system_config_backend_serialized_array</backend_model>
                            <sort_order>35</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </wildcard_exception>
                        <result_limit translate="label">
                            <label>Max number of items in the result</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>3</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </result_limit>
                        <min_relevance translate="label">
                            <label>Min relevance value to display</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>4</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </min_relevance>

                        <!-- <stopword translate="label">
                            <label>Stopwords</label>
                            <frontend_model>searchsphinx/adminhtml_system_stopword</frontend_model>
                            <backend_model>adminhtml/system_config_backend_serialized_array</backend_model>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                            <comment>After change run 'Reindex'</comment>
                        </stopword> -->
                        <replace_word translate="label">
                            <label>Replace words in search query</label>
                            <frontend_model>searchsphinx/adminhtml_system_wordReplace</frontend_model>
                            <backend_model>adminhtml/system_config_backend_serialized_array</backend_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </replace_word>
                        <notword translate="label">
                            <label>"Not" words</label>
                            <frontend_model>searchsphinx/adminhtml_system_notwords</frontend_model>
                            <backend_model>adminhtml/system_config_backend_serialized_array</backend_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>0</show_in_website>
                            <show_in_store>0</show_in_store>
                        </notword>
                        <single_result translate="label">
                            <label>Redirect if Single Result</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_enabledisable</source_model>
                            <sort_order>90</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </single_result>
                        <terms_highlighting>
                            <label>Enable search terms highlighting</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_enabledisable</source_model>
                            <sort_order>110</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </terms_highlighting>
                        <related_terms translate="label">
                            <label>Display Related Search Terms</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_enabledisable</source_model>
                            <sort_order>100</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </related_terms>
                    </fields>
                </advanced>
            </groups>
        </searchsphinx>
    </sections>
</config>
