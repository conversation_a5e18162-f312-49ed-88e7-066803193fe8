<?php

class Praktis_CatalogRule_Model_Rewrite_SalesRule_Rule_Condition_Product extends Mage_SalesRule_Model_Rule_Condition_Product
{
    public function validate(Varien_Object $object)
    {
        /** @var Mage_Catalog_Model_Product $product */
        $product = ($object instanceof Mage_Catalog_Model_Product) ? $object : $object->getProduct();
        if (!($product instanceof Mage_Catalog_Model_Product)) {
            $product = Mage::getModel('catalog/product')->load($object->getProductId());
        }

        $product
            ->setQuoteItemQty($object->getQty())
            ->setQuoteItemPrice($object->getPrice()) // possible bug: need to use $object->getBasePrice()
            ->setQuoteItemRowTotal($object->getBaseRowTotal());

        return $this->pseudoParentValidate($product);
    }

    protected function pseudoParentValidate($object)
    {
        $attrCode = $this->getAttribute();
        if (!($object instanceof Mage_Catalog_Model_Product)) {
            $object = Mage::getModel('catalog/product')->load($object->getId());
        }

        if ('category_ids' == $attrCode) {
            return $this->validateAttribute($object->getCategoryIds());

        } elseif ('special_price' == $attrCode) {
            if (!$object->getResource()) {
                return false;
            }
            $specialPrice = $object->getData($attrCode);
            $this->setValue($specialPrice);
            $this->setValueParsed($specialPrice);
            $resultPrice = $this->validateAttribute($specialPrice);

            $time = Mage::app()->getLocale()->storeTimeStamp(Mage::app()->getStore()->getId());
            $dateTime = new DateTime(date('Y-m-d 00:00:00', $time));
            $time = $dateTime->format('U');

            $this->setValue($time);
            $this->setValueParsed($time);
            $this->setOperator('>=');
            $resultSpecialToDate = $this->validateAttribute(strtotime($object->getData('special_to_date')));
            $this->setOperator('<=');
            $resultSpecialFromDate = $this->validateAttribute(strtotime($object->getData('special_from_date')));

            $currentResult = ($resultPrice && $resultSpecialToDate)
                && ($resultPrice && $resultSpecialFromDate);

            if ($currentResult) {
                return $currentResult;
            }

            // check for catalog price rule
            $catalogRuleProductTable = Mage::getSingleton('core/resource')->getTableName('catalogrule/rule_product');
            $catalogRuleProductSelect = Mage::getSingleton('core/resource')->getConnection('core_read')->select();
            $catalogRuleProductSelect->from($catalogRuleProductTable)
                ->where('product_id = ?', $object->getId())
                ->where('from_time <= ?', $time)
                ->where('to_time >= ?', $time);
            $queryResultsRow = Mage::getSingleton('core/resource')->getConnection('core_read')->fetchRow($catalogRuleProductSelect);
            if (
                is_array($queryResultsRow) && isset($queryResultsRow['product_id'])
                && $queryResultsRow['product_id'] == $object->getId()
            ) {
                return true;
            }

            return $currentResult;
        } elseif ('zeron_bannedfordiscount' == $attrCode) {
            $stockItem = $object->getStockItem();
            $isStockManaged = true;
            if (is_object($stockItem)) {
                $isStockManaged = (bool)$stockItem->getManageStock();
            }

            $attributeValidation = Mage_Rule_Model_Condition_Abstract::validate($object);
            return $isStockManaged && $attributeValidation;
        } elseif (!isset($this->_entityAttributeValues[$object->getId()])) {
            if (!$object->getResource()) {
                return false;
            }
            $attr = $object->getResource()->getAttribute($attrCode);
            if ($attrCode == 'praktis_shipping_discount') {
                //check if free shipping is activated
                if ($object->getData($attrCode) == 1) {
                    $todayDate = strtotime(Mage::getModel('core/date')->gmtDate('Y-m-d H:i:s'));
                    $freeShippingStartDate = strtotime($object->getPraktisDiscountStart());
                    $freeShippingEndDate = strtotime($object->getPraktisDiscountEnd());
                    //check if free shipping date is valid
                    if ($freeShippingStartDate <= $todayDate && $freeShippingEndDate >= $todayDate) {
                        return $this->validateAttribute(1);
                    } else {
                        return $this->validateAttribute(0);
                    }
                }
            }

            if ($attr && $attr->getBackendType() == 'datetime' && !is_int($this->getValue())) {
                $this->setValue(strtotime($this->getValue()));
                $value = strtotime($object->getData($attrCode));
                return $this->validateAttribute($value);
            }

            if ($attr && $attr->getFrontendInput() == 'multiselect') {
                $value = $object->getData($attrCode);
                $value = strlen($value) ? explode(',', $value) : array();
                return $this->validateAttribute($value);
            }

            return Mage_Rule_Model_Condition_Abstract::validate($object);
        } else {
            $result = false; // any valid value will set it to TRUE
            // remember old attribute state
            $oldAttrValue = $object->hasData($attrCode) ? $object->getData($attrCode) : null;

            foreach ($this->_entityAttributeValues[$object->getId()] as $storeId => $value) {
                $attr = $object->getResource()->getAttribute($attrCode);
                if ($attr && $attr->getBackendType() == 'datetime') {
                    $value = strtotime($value);
                } else {
                    if ($attr && $attr->getFrontendInput() == 'multiselect') {
                        $value = strlen($value) ? explode(',', $value) : array();
                    }
                }

                $object->setData($attrCode, $value);
                $result |= Mage_Rule_Model_Condition_Abstract::validate($object);

                if ($result) {
                    break;
                }
            }

            if (is_null($oldAttrValue)) {
                $object->unsetData($attrCode);
            } else {
                $object->setData($attrCode, $oldAttrValue);
            }

            return (bool)$result;
        }
    }
}
