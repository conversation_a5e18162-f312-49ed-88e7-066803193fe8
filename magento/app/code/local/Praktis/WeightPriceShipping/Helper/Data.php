<?php

class Praktis_WeightPriceShipping_Helper_Data extends Mage_Core_Helper_Abstract
{
    const SHIPPING_METHOD_NAME = 'praktis_weightpriceshipping_weight_price';
    protected $pricesMappings;

    public function getPricePerWeight($weight)
    {
        $weight = (float)$weight;
        $pricesMapping = $this->getPriceMapping();
        $variance = 0.0001;
        foreach ($pricesMapping as $priceMapping) {
            $weightFrom = (float)$priceMapping['weight_from'];
            $weightTo = (float)$priceMapping['weight_to'];
            if ($weight > $weightFrom && $weight < $weightTo
                || (
                    abs($weight - $weightFrom) < $variance
                    || abs($weight - $weightTo) < $variance
                )
            ) {
                return $priceMapping['price'];
            }
        }

        return false;
    }

    public function getPriceMapping()
    {
        if (!$this->pricesMappings) {
            $this->pricesMappings = [];
            $mappings = Mage::getStoreConfig('carriers/praktis_weightpriceshipping/pricemap');
            if (!$mappings) {
                return [];
            }

            $mappings = unserialize($mappings);
            if (!is_array($mappings)) {
                return [];
            }

            foreach ($mappings as $mapping) {
                if (
                    !isset($mapping['weight_from'])
                    || !isset($mapping['weight_to']) || !$mapping['weight_to'] // weight to can't be 0
                    || !isset($mapping['price']) || !$mapping['price'] // price can't be 0
                ) {
                    continue;
                }

                $rangeCode = $this->getRangeCode($mapping['weight_from'], $mapping['weight_to']);
                $this->pricesMappings[$rangeCode] = $mapping;
            }
        }

        return $this->pricesMappings;
    }

    protected function getRangeCode($weightFrom, $weightTo)
    {
        return $weightFrom . '-' . $weightTo;
    }

    /**
     * @return string
     */
    public function getShippingMethod()
    {
        return self::SHIPPING_METHOD_NAME;
    }
}