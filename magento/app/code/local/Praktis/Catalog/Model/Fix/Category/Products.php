<?php

class Praktis_Catalog_Model_Fix_Category_Products
{
    protected $_zeronGroupMagentoCategoryIdsMap;
    protected $_magentoCategoryToZeronGroupIdsMap;
    protected $_isShell = true;

    public function execute($categories, $isShell = true)
    {
        $this->_isShell = $isShell;

        /** @var Mage_Core_Model_Resource $resource */
        $resource        = Mage::getSingleton('core/resource');
        $writeConnection = $resource->getConnection('core_write');

        $productTable          = $resource->getTableName('catalog/product');
        $categoryProductsTable = $resource->getTableName('catalog/category_product');

        $writeConnection->query("CREATE TABLE  IF NOT EXISTS catalog_category_product_bkp LIKE catalog_category_product;");

        $this->_initCategoryIdsToZeronGroupsMap();

        // Categories which to FIX
        $zeronGroupsForCategories = array();

        if (empty($categories)) {
            $zeronGroupsForCategories = $this->_magentoCategoryToZeronGroupIdsMap;
        } else {
            foreach ($categories as $category) {
                if (array_key_exists($category, $this->_magentoCategoryToZeronGroupIdsMap)) {
                    $zeronGroupsForCategories[$category] = $this->_magentoCategoryToZeronGroupIdsMap[$category];
                } else {
                    $this->_message("Warning! => Category {$category} not found!");
                }
            }
        }

        $productsCategories = array();
        $categoryProducts   = array();

        foreach ($zeronGroupsForCategories as $category => $zeronGroups) {

            if (!in_array($category, $categories) && !empty($categories)) {
                continue;
            }

            if (!empty($zeronGroups)) {
                /** @var Mage_Catalog_Model_Resource_Product_Collection $products */
                $products = Mage::getModel('catalog/product')->getCollection();
                $products->addFieldToFilter('zeron_group_id', array('in' => $zeronGroups));

                foreach ($products as $product) {
                    $zeronGroupId = (int)$product->getZeronGroupId();
                    if (isset($this->_zeronGroupMagentoCategoryIdsMap[$zeronGroupId])) {
                        foreach ($this->_zeronGroupMagentoCategoryIdsMap[$zeronGroupId] as $categoryId) {
                            $productsCategories[$product->getId()][$categoryId] = $categoryId;
                            $categoryProducts[$categoryId][$product->getId()]   = $product->getId();
                        }
                    }
                }
            }

            $category = Mage::getModel('catalog/category')->load($category);

            /** @var Mage_Catalog_Model_Resource_Product_Collection $products */
            $products = Mage::getModel('catalog/product')->getCollection();
            $products->addAttributeToSelect('zeron_group_id');
            $products->addCategoryFilter($category);

            foreach ($products as $product) {
                $zeronGroupId = (int)$product->getZeronGroupId();
                if (!$zeronGroupId) {
                    continue;
                }
                if (isset($this->_zeronGroupMagentoCategoryIdsMap[$zeronGroupId])) {
                    foreach ($this->_zeronGroupMagentoCategoryIdsMap[$zeronGroupId] as $categoryId) {
                        $productsCategories[$product->getId()][$categoryId] = $categoryId;
                        $categoryProducts[$categoryId][$product->getId()]   = $product->getId();
                    }
                }
            }
        }

        if (empty($productsCategories) || empty($categoryProducts)) {
            $this->_message(PHP_EOL . "Finished script with nothing to do, no products were found");
            return;
        }

        $writeConnection->query("TRUNCATE catalog_category_product_bkp;");
        $writeConnection->query("INSERT catalog_category_product_bkp SELECT * FROM {$categoryProductsTable};");

        $productIds = array_keys($productsCategories);
        $this->_message("------------------------------");
        $this->_message("Deleting products category relation");
        $writeConnection->delete($categoryProductsTable, array('product_id IN(?)' => $productIds));
        $this->_message("Finished deleting category products");

        foreach ($categoryProducts as $category => $products) {
            $this->_message("------------------------------");
            $this->_message("Assigning products to Category ID: {$category}");

            $select = $writeConnection->select();
            /** @var $select Varien_Db_Select */
            $select->from(
                array('a' => $productTable),
                array(
                    'category_id' => new Zend_Db_Expr($category),
                    'product_id'  => 'entity_id',
                    'position'    => new Zend_Db_Expr('0'),
                ))
                ->where('a.entity_id IN (?)', $products);

            $query = $select->insertFromSelect(
                $categoryProductsTable,
                array('category_id', 'product_id', 'position'),
                false
            );

            $writeConnection->query($query);
            if ($this->_isShell) {
                $this->_message("Finished assigning products to {$category}" . PHP_EOL . PHP_EOL);
            } else {
                $this->_message("Finished assigning products to {$category}");
            }

        }

        $this->_message(PHP_EOL . "Running indexer");
        $this->_message("Start catalog_category_product index");
        $process = Mage::getModel('index/indexer')->getProcessByCode('catalog_category_product');
        $process->reindexAll();
        $this->_message("Finished catalog_category_product index");
        $this->_message("Start catalog_url index");
        $process = Mage::getModel('index/indexer')->getProcessByCode('catalog_url');
        $process->reindexAll();
        $this->_message("Finished catalog_url index");
        $this->_message("Finished running indexer");
    }

    protected function _initCategoryIdsToZeronGroupsMap()
    {
        /** @var Mage_Catalog_Model_Resource_Category_Collection $categoryCollection */
        $categoryCollection = Mage::getModel('catalog/category')->getCollection();
        $adapter            = $categoryCollection->getConnection();

        $categoryCollection->getSelect()->reset(Zend_Db_Select::COLUMNS)
            ->columns(array('entity_id'));

        $categoryCollection->addAttributeToSelect('zeron_product_groups', 'left');

        $magentoZeronStringMap = $adapter->fetchPairs($categoryCollection->getSelect());

        foreach ($magentoZeronStringMap as $magentoCategoryId => $zeronGroupIdsString) {
            if ($zeronGroupIdsString != '') {
                $zeronGroupIds = explode(',', $zeronGroupIdsString);
            } else {
                $zeronGroupIds = null;
            }

            $this->_magentoCategoryToZeronGroupIdsMap[$magentoCategoryId] = $zeronGroupIds;

            if (!empty($zeronGroupIds)) {
                foreach ($zeronGroupIds as $groupId) {
                    $this->_zeronGroupMagentoCategoryIdsMap[$groupId][] = $magentoCategoryId;
                }
            }
        }
    }

    protected function _message($message)
    {
        if ($this->_isShell) {
            echo $message . PHP_EOL;
        } else {
            $message = str_replace(PHP_EOL, '<br>', $message);
            echo $message . '<br>';
        }
    }

}