<?php

class Praktis_Econt_Adminhtml_EcontshipmentController extends Mage_Adminhtml_Controller_Action
{
    public function edit_addressAction()
    {
        try {
            Mage::register('current_order', $this->getOrder());
            $this
                ->loadLayout()
                ->renderLayout();
        } catch (Exception $e) {
            Mage::logException($e);
            $this->handleError($e->getMessage(), $this->getRequest()->getParam('order_id'));
        }
    }

    public function create_shipmentAction()
    {
        $request = $this->getRequest();
        try {
            $order = $this->getOrder();
            $order->setData('shipment_form_data', $this->_getShipmentFormData());

            Mage::helper('praktis_econt/order')->createShipmentForOrder($order);
        } catch (Exception $e) {
            Mage::logException($e);
            if ($request->isAjax()) {
                return $this->handleAjaxError($e->getMessage());
            }

            return $this->handleShipmentError($e->getMessage(), $request->getParam('order_id'));
        }

        if ($request->isAjax()) {
            return $this->handleAjaxSuccess([], $this->__('Shipment created'));
        }

        return $this->handleSuccess($this->__('Shipment created'), $request->getParam('order_id'));
    }

    protected function handleAjaxError($message)
    {
        return $this->getResponse()->setBody(Mage::helper('core')->jsonEncode([
            'message' => $message,
            'status' => 'error',
            'data' => [],
        ]));
    }

    protected function handleAjaxSuccess($data, $message = false)
    {
        return $this->getResponse()->setBody(Mage::helper('core')->jsonEncode([
            'message' => $message,
            'status' => 'success',
            'data' => $data,
        ]));
    }

    protected function _getShipmentFormData(): Varien_Object
    {
        $request = $this->getRequest();
        $econtIframeData = $request->getPost('econt_iframe_data');
        if (!is_array($econtIframeData)) {
            $econtIframeData = Mage::helper('core')->jsonDecode($econtIframeData);
            if (!is_array($econtIframeData)) {
                $econtIframeData = [];
            }
        }

        return new Varien_Object($econtIframeData);
    }

    /**
     * @throws Mage_Core_Exception
     */
    protected function getOrder(): Mage_Sales_Model_Order
    {
        $orderId = $this->getRequest()->getParam('order_id');
        if (!$orderId) {
            Mage::throwException($this->__('No order ID provided.'));
        }

        $order = Mage::getModel('sales/order')->load($orderId);
        if (!$order->getId()) {
            Mage::throwException($this->__('Could not load order by order ID %s.', $orderId));
        }

        return $order;
    }

    protected function handleSuccess($message, $orderId)
    {
        Mage::getSingleton('adminhtml/session')->addSuccess($message);

        return $this->_redirect('adminhtml/sales_order/view', ['order_id' => $orderId]);
    }

    protected function handleError($message, $orderId = null)
    {
        Mage::getSingleton('adminhtml/session')->addError($message);
        if ($orderId) {
            return $this->_redirect('adminhtml/sales_order/view', ['order_id' => $orderId]);
        }

        return $this->_redirect('adminhtml/sales_order/index');
    }

    protected function handleShipmentError($message, $orderId = null)
    {
        if (!$orderId) {
            return $this->handleError($message);
        }

        Mage::getSingleton('adminhtml/session')->addError($message);

        return $this->_redirect('*/*/edit_address', ['order_id' => $orderId]);
    }
}
