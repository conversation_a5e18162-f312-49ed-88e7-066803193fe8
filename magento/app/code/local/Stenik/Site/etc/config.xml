<?xml version="1.0"?>
<!--
/**
 * @package  Stenik_Site
 * <AUTHOR> Magento Team <<EMAIL>>
 */
 -->
<config>
    <modules>
        <Stenik_Site>
            <version>1.0.6</version>
        </Stenik_Site>
    </modules>

    <global>
        <helpers>
            <stenik_site>
                <class>Stenik_Site_Helper</class>
            </stenik_site>
        </helpers>

        <blocks>
            <stenik_site>
            <class>Stenik_Site_Block</class>
            </stenik_site>
        </blocks>

        <models>
            <stenik_site>
                <class>Stenik_Site_Model</class>
            </stenik_site>
        </models>

        <resources>
            <stenik_site_setup>
                <setup>
                    <module>Stenik_Site</module>
                    <class>Mage_Eav_Model_Entity_Setup</class>
                </setup>
            </stenik_site_setup>
        </resources>

        <events>
            <controller_action_predispatch_customer_account_loginPost>
                <observers>
                    <stenik_site>
                        <type>singleton</type>
                        <model>stenik_site/observer</model>
                        <method>controllerActionPredispatchCustomerAccountLoginPost</method>
                    </stenik_site>
                </observers>
            </controller_action_predispatch_customer_account_loginPost>
        </events>
    </global>

    <admin>
        <routers>
            <stenik_site>
                <use>admin</use>
                <args>
                    <module>Stenik_Site</module>
                    <frontName>stenik_site</frontName>
                </args>
            </stenik_site>
            <adminhtml>
                <args>
                    <modules>
                        <stenik_site before="Mage_Adminhtml">Stenik_Site_Adminhtml</stenik_site>
                    </modules>
                </args>
            </adminhtml>
        </routers>
    </admin>

    <adminhtml>
        <layout>
            <updates>
                <stenik_site>
                    <file>stenik_site.xml</file>
                </stenik_site>
            </updates>
        </layout>

        <translate>
            <modules>
                <Stenik_Site>
                    <files>
                        <stenik_site>Stenik_Site.csv</stenik_site>
                    </files>
                </Stenik_Site>
            </modules>
        </translate>

        <events>
            <stenik_admin_stenik_partner_grid_prepare_columns>
                <observers>
                    <stenik_site>
                        <class>stenik_site/adminhtml_observer</class>
                        <method>stenikAdminStenikPartnerGridPrepareColumns</method>
                    </stenik_site>
                </observers>
            </stenik_admin_stenik_partner_grid_prepare_columns>

            <stenik_admin_stenik_partner_grid_prepare_columns_order>
                <observers>
                    <stenik_site>
                        <class>stenik_site/adminhtml_observer</class>
                        <method>stenikAdminStenikPartnerGridPrepareColumnsOrder</method>
                    </stenik_site>
                </observers>
            </stenik_admin_stenik_partner_grid_prepare_columns_order>

            <stenik_admin_stenik_partner_grid_prepare_collection>
                <observers>
                    <stenik_site>
                        <class>stenik_site/adminhtml_observer</class>
                        <method>stenikAdminStenikPartnerGridPrepareCollection</method>
                    </stenik_site>
                </observers>
            </stenik_admin_stenik_partner_grid_prepare_collection>

            <stenik_admin_promo_catalog_grid_prepare_columns>
                <observers>
                    <stenik_site>
                        <class>stenik_site/adminhtml_observer</class>
                        <method>stenikAdminPromoCatalogGridPrepareColumns</method>
                    </stenik_site>
                </observers>
            </stenik_admin_promo_catalog_grid_prepare_columns>

            <stenik_admin_promo_catalog_grid_prepare_columns_order>
                <observers>
                    <stenik_site>
                        <class>stenik_site/adminhtml_observer</class>
                        <method>stenikAdminPromoCatalogGridPrepareColumnsOrder</method>
                    </stenik_site>
                </observers>
            </stenik_admin_promo_catalog_grid_prepare_columns_order>

            <cms_wysiwyg_config_prepare>
                <observers>
                    <stenik_site>
                        <class>stenik_site/adminhtml_observer</class>
                        <method>cmsWysiwygConfigPrepare</method>
                    </stenik_site>
                </observers>
            </cms_wysiwyg_config_prepare>


            <stenik_admin_order_grid_prepare_columns>
                <observers>
                    <stenik_site>
                        <class>stenik_site/adminhtml_observer</class>
                        <method>stenikAdminOrderGridPrepareColumns</method>
                    </stenik_site>
                </observers>
            </stenik_admin_order_grid_prepare_columns>

            <stenik_admin_order_grid_prepare_columns_order>
                <observers>
                    <stenik_site>
                        <class>stenik_site/adminhtml_observer</class>
                        <method>stenikAdminOrderGridPrepareColumnsOrder</method>
                    </stenik_site>
                </observers>
            </stenik_admin_order_grid_prepare_columns_order>
        </events>
    </adminhtml>
</config>
