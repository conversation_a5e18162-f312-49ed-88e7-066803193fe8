<?php
/**
 * @package Stenik_Zeron
 * <AUTHOR> Magento Team <<EMAIL>>
 * @method Stenik_Zeron_Model_Resource_Catalog_Product getResource
 */
class Stenik_Zeron_Model_Catalog_Product extends Stenik_Zeron_Model_Catalog_Product_Abstract implements Stenik_Sync_Model_Interface_Product
{
    /**
     * Zeron group id => magento category ids Map
     *
     * @var null|array
     */
    protected $_zeronGroupMagentoCategoryIdsMap = null;

    /**
     * Magento ProductId => Category Id map
     *
     * @var null|array
     */
    protected $_magentoProductIdCategoryIdsMap = null;

    protected ?array $_brandOptions = null;

    /**
     * Init.
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('stenik_zeron/catalog_product');
    }

    /**
     * Retrieve collection
     *
     * @param  Zend_Date|null $since
     * @return Varien_Data_Collection
     */
    public function getCollection(Zend_Date $since = null)
    {
        $logger = Mage::helper('stenik_zeron/log')->factory('product');
        $logger->addLogMessagePrefix('[PRODUCT] ');
        $logger->logInfo();
        $logger->logInfo('Start sync.');

        $resultCollection = new Varien_Data_Collection();

        $this->getResource()->setUseCache($this->getUseCache());

        try {
            $zeronProductCollection = $this->getResource()->getItems($since);
            $logger->logInfo('%s potential items found for create/update.', count($zeronProductCollection));
            $resultCollection = $this->_parseZeronProductCollection($zeronProductCollection);

        } catch (Mage_Core_Exception $e) {
            Mage::logException($e);
            $logger->logAlert($e->getMessages());
        }

        $logger->logInfo('%s products gathered for create/update.', count($resultCollection));
        $logger->removeLogMessagePrefix('[PRODUCT] ');

        return $resultCollection;
    }

    /**
     * Retrieve single item
     *
     * @param  Mage_Catalog_Model_Product $product
     * @return boolean|Varien_Object
     */
    public function getItem(Mage_Catalog_Model_Product $product)
    {
        $zeronProductCollection = $this->getResource()->getItem($product);

        $this->_magentoProductIdDataMap = array($product->getId() => $product->getData());

        $resultCollection       = $this->_parseZeronProductCollection($zeronProductCollection);

        $this->_magentoProductIdDataMap = null;

        return $resultCollection->getItemById($product->getSku());
    }

    private function _getAttributeOptions(): array
    {
        if ($this->_brandOptions === null) {
            $this->_brandOptions = [];
            $attribute = Mage::getSingleton('eav/config')
                ->getAttribute(Mage_Catalog_Model_Product::ENTITY, 'brand');

            $options = $attribute->getSource()->getAllOptions(false);

            /** @var Stenik_Zeron_Helper_Product $helper */
            $helper = Mage::helper('stenik_zeron/product');

            foreach ($options as $option) {
                $this->_brandOptions[$helper->getBrandHashKey($option['label'])] = $option;
            }
        }

        return $this->_brandOptions;
    }

    private function _getBrandValue(string $tradeMark): string
    {
        if (empty($tradeMark)) {
            return '';
        }

        /** @var Stenik_Zeron_Helper_Product $helper */
        $helper = Mage::helper('stenik_zeron/product');
        $key = $helper->getBrandHashKey($tradeMark);

        $result = $tradeMark;
        if (isset($this->_getAttributeOptions()[$key])) {
            $result = (string) $this->_getAttributeOptions()[$key]['label'];
        }

        return $result;
    }

    /**
     * Parse Zeron Product Collection
     * @param  Varien_Data_Collection $zeronProductCollection
     * @return Varien_Data_Collection
     */
    protected function _parseZeronProductCollection(Varien_Data_Collection $zeronProductCollection)
    {
        $resultCollection = new Varien_Data_Collection();
        $logger = Mage::helper('stenik_zeron/log')->factory('product');

        $magentoProductSkuIdMap          = $this->_getMagentoSkuIdMap();
        $magentoProductIdDataMap         = $this->_getMagentoProductIdDataMap();
        $magentoProductDisabledIds       = $this->_getMagentoDisabledForZeronProductIds();
        $magentoProductIdCategoryIdsMap  = $this->_getMagentoProductIdCategoryIdsMap();
        $zeronGroupMagentoCategoryIdsMap = $this->_getZeronGroupMagentoCategoryIdsMap();

        foreach ($zeronProductCollection as $zeronProductItem) {
            $magentoId = null;
            if (isset($magentoProductSkuIdMap[$zeronProductItem->getInvCode()])) {
                $magentoId = $magentoProductSkuIdMap[$zeronProductItem->getInvCode()];
            }

            $productExistsInMagento = (bool) $magentoId;

            if (isset($magentoProductDisabledIds[$magentoId])) {
                continue;
            }

            $addInResult = false;
            $item = new Varien_Object();
            $item->setId($zeronProductItem->getInvCode())
                ->setMagentoId($magentoId)
                ->setMagentoSku($zeronProductItem->getInvCode())
                ->setZeronGroupID($zeronProductItem->getGroupID())
                ->setZeronUpdatedAt(Mage::getModel('core/date')->gmtDate())
                ->setZeronInventoryRawData($zeronProductItem->getRawData())
                ->setZeronBaseMeasure($zeronProductItem->getMeasure())
                ->setZeronVat($zeronProductItem->getVatPercent())
                ->setZeronSiteStatus($zeronProductItem->getSiteStatus())
                ->setZeronInPromotion($zeronProductItem->getInPromotion() ? 1 : 0)
                ->setBrand($this->_getBrandValue($zeronProductItem->getTradeMark() ?: ''))
                ->setZeronRecommend($zeronProductItem->getRecommend() ? 1 : 0)
                ->setZeronWarranty($zeronProductItem->getWarranty())
                ->setZeronNew($zeronProductItem->getNew() ? 1 : 0)
                ->setZeronBestselling($zeronProductItem->getBestSelling() ? 1 : 0)
                ->setZeronBannedfordiscount($zeronProductItem->getBannedForDiscount() ? 1 : 0)
                ->setZeronBlockedforsale($zeronProductItem->getBlockedForSale() ? 1 : 0)
                ->setZeronBlockeddelivery($zeronProductItem->getBlockedDelivery() ? 1 : 0)
                ->setZeronRelatedProducts($zeronProductItem->getRelatedItemsCodes() ? $zeronProductItem->getRelatedItemsCodes() : '')
                ->setZeronCrossSellProducts($zeronProductItem->getReplacementItemsCodes() ? $zeronProductItem->getReplacementItemsCodes() : '');

            /* Update categories */
            $categoryIds        = array();
            $currentCategoryIds = array();

            if (isset($zeronGroupMagentoCategoryIdsMap[$zeronProductItem->getGroupId()])) {
                $categoryIds = $zeronGroupMagentoCategoryIdsMap[$zeronProductItem->getGroupId()];
            }

            if (isset($magentoProductIdCategoryIdsMap[$magentoId])) {
                $currentCategoryIds = $magentoProductIdCategoryIdsMap[$magentoId];
            }

            if (count(array_diff($currentCategoryIds, $categoryIds)) ||
                count(array_diff($categoryIds, $currentCategoryIds))
            ) {
                /* Temporary - Disable category change for existing products! */
                if (!$productExistsInMagento) {
                    $item->setCategories($categoryIds);
                    $addInResult = true;
                }
            }

            // Check if update is required
            if ($productExistsInMagento && isset($magentoProductIdDataMap[$magentoId])) {
                $magentoProductData = $magentoProductIdDataMap[$magentoId];

                $attrsToCheckForDiff = array(
                    'brand',
                    'zeron_base_measure',
                    'zeron_group_id',
                    'zeron_vat',
                    'zeron_site_status',
                    'zeron_in_promotion',
                    'zeron_recommend',
                    'zeron_new',
                    'zeron_bestselling',
                    'zeron_warranty',
                    'zeron_bannedfordiscount',
                    'zeron_blockedforsale',
                    'zeron_blockeddelivery',
                    'zeron_related_products',
                    'zeron_cross_sell_products',
                );

                $decimalAttrs = array('zeron_vat');

                foreach ($attrsToCheckForDiff as $attributeCode) {
                    $match = false;

                    switch (true) {
                        case in_array($attributeCode, $decimalAttrs): // Decimal compare
                            $match = (bool) (round($magentoProductData[$attributeCode], 8) == round($item->getData($attributeCode), 8));
                            break;
                        default:
                            $match = (bool) ($magentoProductData[$attributeCode] == $item->getData($attributeCode));
                            break;
                    }

                    if (!$match) {
                        $addInResult = true;
                    } else {
                        $item->unsetData($attributeCode);
                    }
                }
            }

            if (!$productExistsInMagento) {
                $addInResult = true;

                $name = $zeronProductItem->getWebInvName() ? $zeronProductItem->getWebInvName() : $zeronProductItem->getInvName();
                $shortDescription = $name;
                $description = $name;
                $characteristics = '';

                if ($zeronProductItem->getShortDescriptName())
                    $shortDescription = $zeronProductItem->getShortDescriptName();
                if ($zeronProductItem->getLongDescriptName())
                    $description = $zeronProductItem->getLongDescriptName();
                if ($zeronProductItem->getItemTechData())
                    $characteristics = $zeronProductItem->getItemTechData();

                $item->setStatus(Mage_Catalog_Model_Product_Status::STATUS_DISABLED)
                    ->setName($name)
                    ->setDescription($description)
                    ->setShortDescription($shortDescription)
                    ->setCharacteristics($characteristics)
                    ->setTypeId(Mage_Catalog_Model_Product_Type::TYPE_SIMPLE)
                    ->setVisibility(Mage_Catalog_Model_Product_Visibility::VISIBILITY_BOTH)
                    ->setPrice(0)
                    ->setTaxClassId(0)
                    ->setAttributeSetId($this->_getDefaultAttributeSetId())
                ;
                if ($zeronProductItem->getWeight())
                    $item->setWeight($zeronProductItem->getWeight());
                if ($zeronProductItem->getLength())
                    $item->setSizeLength($zeronProductItem->getLength());
                if ($zeronProductItem->getWidth())
                    $item->setSizeWidth($zeronProductItem->getWidth());
                if ($zeronProductItem->getHeight())
                    $item->setSizeHeight($zeronProductItem->getHeight());
            }

            if ($addInResult) {
                try {
                    $resultCollection->addItem($item);
                } catch (Exception $e) {
                    $logger->logError($e->getMessages());
                }
            }
        }

        return $resultCollection;
    }

    /**
     * Retrieve default attribute set ID
     *
     * @return integer
     */
    protected function _getDefaultAttributeSetId()
    {
        return Mage::getSingleton('catalog/product')->getDefaultAttributeSetId();
    }

    /**
     * Retrieve zeron group id => magento category ids map
     *
     * @return array
     */
    protected function _getZeronGroupMagentoCategoryIdsMap()
    {
        if ($this->_zeronGroupMagentoCategoryIdsMap === null) {
            $categoryCollection = Mage::getModel('catalog/category')->getCollection();
            $adapter = $categoryCollection->getConnection();

            $categoryCollection->getSelect()->reset(Zend_Db_Select::COLUMNS);
            $categoryCollection->getSelect()->columns(array('entity_id'));
            $categoryCollection->addAttributeToSelect('zeron_product_groups', 'left');

            $categoryCollection->addAttributeToFilter('zeron_product_groups', array('notnull' => true));
            $categoryCollection->addAttributeToFilter('zeron_product_groups', array('neq' => ''));

            $magentoZeronStringMap = $adapter->fetchPairs($categoryCollection->getSelect());

            $zeronMagentoMap = array();
            foreach ($magentoZeronStringMap as $magentoCategoryId => $zeronGroupIdsString) {
                $zeronGroupIds = explode(',', $zeronGroupIdsString);
                if (count($zeronGroupIds)) {
                    foreach ($zeronGroupIds as $zeronGroupId) {
                        if (!isset($zeronMagentoMap[$zeronGroupId])) {
                            $zeronMagentoMap[$zeronGroupId] = array();
                        }
                        $zeronMagentoMap[$zeronGroupId][] = $magentoCategoryId;
                    }
                }
            }

            $this->_zeronGroupMagentoCategoryIdsMap = $zeronMagentoMap;
        }

        return $this->_zeronGroupMagentoCategoryIdsMap;
    }

    /**
     * Retrieve magento product id => category ids map
     *
     * @return array
     */
    protected function _getMagentoProductIdCategoryIdsMap()
    {
        if ($this->_magentoProductIdCategoryIdsMap === null) {
            $adapter = Mage::getModel('core/resource')->getConnection('core_read');

            $select = $adapter->select()->from(
                Mage::getModel('core/resource')->getTableName('catalog/category_product'),
                array('product_id', new Zend_Db_Expr('GROUP_CONCAT(category_id SEPARATOR ",")'))
            )->group('product_id');

            $this->_magentoProductIdCategoryIdsMap = $adapter->fetchPairs($select);
            foreach ($this->_magentoProductIdCategoryIdsMap as $productId => $categoryIds) {
                $this->_magentoProductIdCategoryIdsMap[$productId] = explode(',', $categoryIds);
            }
        }

        return $this->_magentoProductIdCategoryIdsMap;
    }

    /**
     * Retrieve magento product id => data map
     *
     * @return array
     */
    protected function _getMagentoProductIdDataMap()
    {
        if ($this->_magentoProductIdDataMap === null) {
            $productCollection = Mage::getModel('catalog/product')->getCollection();
            $adapter = $productCollection->getConnection();

            $productCollection->getSelect()->reset(Zend_Db_Select::COLUMNS);
            $productCollection->getSelect()->columns(array('entity_id'));
            $productCollection->addAttributeToSelect(array(
                'zeron_base_measure',
                'zeron_group_id',
                'zeron_vat',
                'zeron_site_status',
                'zeron_in_promotion',
                'zeron_recommend',
                'zeron_new',
                'zeron_bestselling',
                'zeron_bannedfordiscount',
                'zeron_blockedforsale',
                'zeron_blockeddelivery',
                'zeron_warranty',
            ), 'left');

            $this->_magentoProductIdDataMap = $adapter->fetchAssoc($productCollection->getSelect());
        }

        return $this->_magentoProductIdDataMap;
    }
}
