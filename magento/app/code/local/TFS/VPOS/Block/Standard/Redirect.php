<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 *
 * @category   Payment throw TFS Virtual POS
 * @package    TFS_VPOS
 * <AUTHOR>
 * @copyright  Copyright (c) 2015  TFS JSC
 * @copyright  Copyright (c) 2015  (http://www.transcard.bg)
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

class TFS_VPOS_Block_Standard_Redirect extends Mage_Core_Block_Template {
    protected $order = null;
    protected $_allowCurrencyCode = array( 'BGN' );
	
    protected function _construct() {
    
        parent::_construct();
    	
        //Getting order data
        $this->tfs = Mage::getModel('vpos/paymentMethod');
        $order = Mage::getModel('sales/order');

        $this->order = $order->loadByIncrementId($this->tfs->getCheckout()->getLastRealOrderId());
    }
    
    protected function _toHtml() {
        $urlReturn  = Mage::getUrl('vpos/standard/response_ok');

        $params = $this->make_request_params();
        
        ob_start();
        ?>
            <p><?php echo $this->__('You will be redirected to trans.card secure payment page when you place an order.'); ?></p>

            <form id="payment-form" action="<?php echo $this->getPostUrl(); ?>" method="POST" target="_self">
                <?php foreach ($params['request_params'] as $key => $value): ?>
                    <input type="hidden" name="<?php echo $key; ?>" value="<?php echo $value; ?>" />
                <?php endforeach ?>
            </form>

            <script type="text/javascript">
                document.getElementById("payment-form").submit();
            </script>
        <?php 

        return ob_get_clean();
    }

    protected function make_request_params() {
        //Get TFS model
        $tfs_lib = Mage::getModel('vpos/TFSlib');
        $parameters = array();
        $vpos_attrs = array();

        $sign_params = array(
            'MIN' => $this->tfs->getConfigData('trader_number'),
            'INVOICE' => $this->getOrderId(),
            'AMOUNT' => number_format($this->getOrderGrantTotal(), 2, '.', ''),
            'DESCR' => $this->__('Payment request from - ') . $this->getStoreName(),
            'ENCODING' => 'UTF-8',
            'CURRENCY' => 'BGN',
            'STOCKINFO' => $this->getStockInfo(),
            'VERSION' => '2.0'
        );

        $parameters['PAGE'] = 'paylogin'; //'PAYCHECK'

        foreach ($sign_params as $key => $value) {
            if ($value != '') {
                $vpos_attrs[] = sprintf("%s=%s", $key, $value);
            }
        }

        $parameters['ENCODED'] = $tfs_lib->make_base($vpos_attrs);
        
        $parameters['CHECKSUM'] = $tfs_lib->sign_data($this->tfs->getConfigData('priv_key'), NULL, $parameters['ENCODED']);

        $parameters['URL_OK'] = Mage::getUrl('vpos/standard/response_ok');
        $parameters['URL_CANCEL'] = Mage::getUrl('vpos/standard/response_ok');
        $parameters['LANG'] = substr(Mage::app()->getLocale()->getLocaleCode(), 0, 2); 

        return array(
            'request_params' => $parameters,
            'sign_params' => $sign_params
        );
    }

    protected function getPostUrl() {
        return $this->tfs->getTFSPayUrl();
    }
    
    protected function getOrderId() {
        $orderData = $this->order->getData();
        return $orderData['increment_id'];
    }

    protected function getOrderGrantTotal() {
        $orderData = $this->order->getData();

        return $orderData['grand_total'];
    }

    protected function getOrderSubTotal() {
        $orderData = $this->order->getData();

        return $orderData['subtotal'];
    }

    protected function getStoreName() {
        $storeId = Mage::app()->getStore()->getId();
        
        $store = new Mage_Adminhtml_Model_System_Store();
        $storeName = $store->getStoreName($storeId);
        
        return $storeName;
    }

    protected function getStockInfo() {
        $group_products = array(
            'first' => 0.0,
            'second' => 0.0,
            'total' => 0.0
        );

        $products_total = number_format($this->getOrderSubTotal(), 2, '.', '');
        $other_total = number_format($this->getOrderGrantTotal() - $products_total, 2, '.', '');

        $stock_info = '1:' . $products_total . ':2:' . $other_total;

        if (!$this->tfs->getConfigData('allow_cashback')) {
            foreach ($this->order->getItemsCollection() as $item) {
                $current_item_qty = $item->getQtyOrdered();

                if (!$item->getProduct()->getData('special_price')) {
                    $group_products['first'] = $group_products['first'] +  ($item->getProduct()->getPrice() * $current_item_qty);
                } else {
                    $group_products['second'] = $group_products['second'] +  ($item->getProduct()->getData('special_price') * $current_item_qty); 
                }
            }

            $stock_info = '1:' . number_format($group_products['first'], 2, '.', '') . ':2:' . number_format($group_products['second'] + $other_total, 2, '.', '');
        }

        return $stock_info;
    }
}