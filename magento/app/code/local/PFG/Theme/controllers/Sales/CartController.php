<?php

class PFG_Theme_Sales_CartController extends PFG_Theme_Controller_AbstractController
{
    public function calculateTotalsAction(): void
    {
        try {
            $data = $this->getBody();
            $quote = $this->_getQuoteByUUID($data['cartId']);
            $this->_collectTotals($quote);
            $this->_sendOk();
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    public function getShippingMethodsAction(): void
    {
        try {
            $data = $this->getBody();
            $quote = $this->_getQuoteByUUID($data['cartId']);
            $quote->getShippingAddress()->setCollectShippingRates(true);
            $shippingMethods = $quote->getShippingAddress()->collectShippingRates()
                ->getGroupedAllShippingRates();
            $onlyStorePickup = Mage::helper('praktis_catalog/siteStatus')->allowOnlyStorePickup($quote);
            $result = [];
            $i = 1;
            foreach ($shippingMethods as $shippingMethod) {
                if (is_array($shippingMethod)) {
                    foreach ($shippingMethod as $method) {
                        if ($onlyStorePickup) {
                            if ($method->getCode() != Stenik_SiteShipping_Model_Observer::SITE_SHIPPING_METHOD) {
                                continue;
                            }
                        }

                        $result["method_".$i] = [
                            'code'  => $method->getCode(),
                            'title' => $method->getCarrierTitle(),
                            'price' => $method->getPrice(),
                        ];
                    }
                } else {
                    if ($onlyStorePickup) {
                        if ($shippingMethod->getCode() != Stenik_SiteShipping_Model_Observer::SITE_SHIPPING_METHOD) {
                            continue;
                        }
                    }
                    $result["method_".$i] = [
                        'code'  => $shippingMethod->getCode(),
                        'title' => $shippingMethod->getCarrierTitle(),
                        'price' => $shippingMethod->getPrice(),
                    ];
                }
            }

            $this->_sendOk($result);
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    public function addItemAction(): void
    {
        try {
            $addToCart = $this->getBody();
            $quote = $this->_getQuoteByUUID($addToCart['cartId']);
            $product = $this->_getProduct(
                $addToCart['sku'],
                $quote->getStoreId() ?? Mage::app()->getDefaultStoreView()->getId(),
            );
            if ($product->getId()) {
                if ($product->isAvailable()) {
                    $qty = $addToCart['qty'] ?? $product->getStockItem()->getMinSaleQty();
                    $quote->addProduct($product, new Varien_Object(['qty' => $qty]));

                    $this->_collectTotals($quote);
                } else {
                    throw new Exception('Product is not available');
                }
            } else {
                throw new Exception("Invalid product: " . $addToCart['sku']);
            }

            $this->_sendOk();
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    private function _getProduct(string $sku, int $storeId): Mage_Catalog_Model_Product
    {
        $product = Mage::getModel('catalog/product')
            ->setStoreId($storeId);
        $productID = $product->getIdBySku($sku);
        $product->load($productID);
        return $product;
    }

    public function removeItemAction()
    {
        try {
            $params = $this->getBody();
            /** @var Mage_Sales_Model_Quote $quote */
            $quote = $this->_getQuoteByUUID($params['cartId']);

            $removeItem = 0;
            foreach ($quote->getAllVisibleItems() as $item) {
                if ($item->getSku() == $params['sku']) {
                    $removeItem = $item->getId();
                }
            }

            if (!$removeItem) {
                throw new Exception("cant find sku: " . $params['sku']);
            }

            $this->_collectTotals($quote->removeItem($removeItem));

            $this->_sendOk();
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    public function updateItemAction()
    {
        try {
            $params = $this->getBody();
            /** @var Mage_Sales_Model_Quote $quote */
            $quote = $this->_getQuoteByUUID($params['cartId']);

            $updated = false;
            $qty = $params['qty'] ?? 0;
            foreach ($quote->getAllVisibleItems() as $item) {
                if ($item->getSku() == $params['sku']) {
                    $updated = true;
                    $item->setQty($qty)->save();
                    break;
                }
            }

            if (!$updated) {
                throw new Exception("Invalid product: " . $params['sku']);
            }

            $quote->collectTotals()->save();
            $this->_collectTotals($quote);

            $this->_sendOk();
        } catch (Exception $e) {
            $this->_handleException($e);
        }
    }

    public function applyCouponCodeAction()
    {
        $fileName = 'PFG_Theme_Sales_CartController';
        $functionName = 'applyCouponCodeAction';
        $helper = Mage::helper('pfgzeron');

        try {
            $data = $this->getBody();
            $cartId = $data['cartId'] ?? 'unknown';
            $promoCode = $data['promoCode'] ?? '';

            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "External API request to apply coupon - Cart ID: {$cartId}, Promo Code: '{$promoCode}'",
                    $fileName,
                    $functionName
                );
            }

            $quote = $this->_getQuoteByUUID($cartId);
            $customerId = $quote->getCustomerId();

            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Quote loaded - Quote ID: {$quote->getId()}, Customer ID: {$customerId}",
                    $fileName,
                    $functionName
                );
            }

            $processedPromoCode = $this->_getPromoCode($promoCode);

            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Processed promo code: '{$processedPromoCode}'",
                    $fileName,
                    $functionName
                );
            }

            $this->_collectTotals($quote->setCouponCode($processedPromoCode));

            $finalCouponCode = $quote->getCouponCode();

            // Check address coupon codes for debugging
            if ($helper->isCouponDebugEnabled()) {
                $addresses = $quote->getAllAddresses();
                foreach ($addresses as $address) {
                    $addressCoupon = $address->getCouponCode();
                    $addressType = $address->getAddressType();
                    $helper->logCouponDebug(
                        "Address coupon check - Type: {$addressType}, Coupon: '{$addressCoupon}', Has coupon: " . ($address->hasCouponCode() ? 'YES' : 'NO'),
                        $fileName,
                        $functionName
                    );
                }

                $helper->logCouponDebug(
                    "Final coupon code on quote: '{$finalCouponCode}' - " .
                    ($finalCouponCode === $processedPromoCode ? 'APPLIED SUCCESSFULLY' : 'APPLICATION FAILED'),
                    $fileName,
                    $functionName
                );
            }

            // Check if coupon was actually applied
            if (!empty($processedPromoCode) && empty($finalCouponCode)) {
                // Coupon was processed but not applied - this means it was rejected during validation
                if ($helper->isCouponDebugEnabled()) {
                    $helper->logCouponDebug(
                        "Coupon application failed - processed code '{$processedPromoCode}' but final code is empty",
                        $fileName,
                        $functionName
                    );
                }

                // Check if this was due to price group restrictions
                $errorMessage = $this->_determineCouponRejectionReason($processedPromoCode, $quote);
                throw new Exception($errorMessage);
            }

            $this->_sendOk();
        } catch (Exception $e) {
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Exception during coupon application: " . $e->getMessage(),
                    $fileName,
                    $functionName
                );
            }
            $this->_handleException($e);
        }
    }

    private function _getPromoCode(string $couponCode): string
    {
        $fileName = 'PFG_Theme_Sales_CartController';
        $functionName = '_getPromoCode';
        $helper = Mage::helper('pfgzeron');

        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Processing promo code: '{$couponCode}'",
                $fileName,
                $functionName
            );
        }

        if (!empty($couponCode)) {
            if ($couponCode === '_pfg_remove_coupon') {
                if ($helper->isCouponDebugEnabled()) {
                    $helper->logCouponDebug(
                        "Special remove coupon code detected - clearing coupon",
                        $fileName,
                        $functionName
                    );
                }
                $couponCode = '';
            } else {
                $trimmedCode = trim($couponCode);

                if ($helper->isCouponDebugEnabled()) {
                    $helper->logCouponDebug(
                        "Validating coupon code: '{$trimmedCode}'",
                        $fileName,
                        $functionName
                    );
                }

                // First check for Magento coupons
                /** @var Mage_SalesRule_Model_Coupon $oCoupon */
                $oCoupon = Mage::getModel('salesrule/coupon')->load($trimmedCode, 'code');
                $ruleId = $oCoupon->getRuleId();

                if ($helper->isCouponDebugEnabled()) {
                    $helper->logCouponDebug(
                        "Magento coupon validation - Coupon ID: {$oCoupon->getId()}, Rule ID: {$ruleId}",
                        $fileName,
                        $functionName
                    );
                }

                // If not found in Magento coupons, check Zeron coupons
                if ($ruleId < 1) {
                    /** @var PFG_Zeron_Model_PromoCode $zeronCoupon */
                    $zeronCoupon = Mage::getModel('pfgzeron/promoCode')->loadCode($trimmedCode);

                    if ($helper->isCouponDebugEnabled()) {
                        $helper->logCouponDebug(
                            "Zeron coupon validation - Coupon ID: {$zeronCoupon->getId()}, Card Type: {$zeronCoupon->getCardType()}, Status: {$zeronCoupon->getCardStatus()}",
                            $fileName,
                            $functionName
                        );
                    }

                    if ($zeronCoupon->getId()) {
                        // Zeron coupon found - this is valid
                        if ($helper->isCouponDebugEnabled()) {
                            $helper->logCouponDebug(
                                "Zeron coupon validation PASSED - found valid Zeron coupon",
                                $fileName,
                                $functionName
                            );
                        }
                    } else {
                        // Neither Magento nor Zeron coupon found
                        if ($helper->isCouponDebugEnabled()) {
                            $helper->logCouponDebug(
                                "Coupon validation FAILED - not found in Magento or Zeron systems",
                                $fileName,
                                $functionName
                            );
                        }
                        throw new Exception('Невалиден промо код');
                    }
                } else {
                    // Magento coupon found
                    if ($helper->isCouponDebugEnabled()) {
                        $helper->logCouponDebug(
                            "Magento coupon validation PASSED - associated with rule {$ruleId}",
                            $fileName,
                            $functionName
                        );
                    }
                }

                $couponCode = $trimmedCode;
            }

            return $couponCode;
        }

        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Empty coupon code provided",
                $fileName,
                $functionName
            );
        }

        return "";
    }

    private function _collectTotals(Mage_Sales_Model_Quote $quote)
    {
        $quote->getShippingAddress()
            ->setCollectShippingRates(true)
            ->collectShippingRates();

        $quote->setTotalsCollectedFlag(false);
        $quote->collectTotals()->save();
    }

    /**
     * Determine the reason why a coupon was rejected and return appropriate error message
     *
     * @param string $couponCode
     * @param Mage_Sales_Model_Quote $quote
     * @return string
     */
    private function _determineCouponRejectionReason($couponCode, $quote)
    {
        $fileName = 'PFG_Theme_Sales_CartController';
        $functionName = '_determineCouponRejectionReason';
        $helper = Mage::helper('pfgzeron');

        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Analyzing rejection reason for coupon: '{$couponCode}'",
                $fileName,
                $functionName
            );
        }

        // Check if this is a Zeron coupon
        $zeronCoupon = Mage::getModel('pfgzeron/promoCode')->loadCode($couponCode);
        if ($zeronCoupon->getId()) {
            return $this->_analyzeZeronCouponRejection($zeronCoupon, $quote, $helper, $fileName, $functionName);
        }

        // Check if this is a Magento coupon
        $magentoCoupon = Mage::getModel('salesrule/coupon')->load($couponCode, 'code');
        if ($magentoCoupon->getId()) {
            return $this->_analyzeMagentoCouponRejection($magentoCoupon, $quote, $helper, $fileName, $functionName);
        }

        // Coupon doesn't exist in either system
        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Coupon not found in either Magento or Zeron systems - returning invalid coupon error",
                $fileName,
                $functionName
            );
        }

        return 'Невалиден промо код';
    }

    /**
     * Analyze why a Zeron coupon was rejected
     *
     * @param PFG_Zeron_Model_PromoCode $zeronCoupon
     * @param Mage_Sales_Model_Quote $quote
     * @param PFG_Zeron_Helper_Data $helper
     * @param string $fileName
     * @param string $functionName
     * @return string
     */
    private function _analyzeZeronCouponRejection($zeronCoupon, $quote, $helper, $fileName, $functionName)
    {
        $customerId = $quote->getCustomerId();

        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Analyzing Zeron coupon rejection - Coupon ID: {$zeronCoupon->getId()}, Customer ID: {$customerId}",
                $fileName,
                $functionName
            );
        }

        // First check if the coupon is fundamentally valid (basic validation)
        $isBasicallyValid = $zeronCoupon->isValid($customerId ?? 0);

        if (!$isBasicallyValid) {
            // Coupon failed basic validation (expired, no usage count, partner code mismatch, etc.)
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Zeron coupon failed basic validation - returning invalid coupon error",
                    $fileName,
                    $functionName
                );
            }
            return 'Невалиден промо код';
        }

        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Zeron coupon passed basic validation - analyzing business rule failures",
                $fileName,
                $functionName
            );
        }

        // Coupon is valid but failed to apply - check specific business rules
        $zeronRuleId = Mage::helper('pfgzeron/promo')->getPromoRuleForZeronCodes();
        $rule = Mage::getModel('salesrule/rule')->load($zeronRuleId);

        if ($rule->getId()) {
            // Check for price group restrictions
            $priceGroupError = $this->_checkPriceGroupRestrictions($rule, $quote, $helper, $fileName, $functionName);
            if ($priceGroupError) {
                return $priceGroupError;
            }

            // Check other business rule failures
            $businessRuleError = $this->_checkOtherBusinessRules($rule, $quote, $helper, $fileName, $functionName);
            if ($businessRuleError) {
                return $businessRuleError;
            }
        }

        // Coupon is valid but failed for unknown business rule reasons
        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Zeron coupon is valid but failed for unknown business rule reasons",
                $fileName,
                $functionName
            );
        }

        return 'Промо кодът е валиден, но не може да бъде приложен';
    }

    /**
     * Analyze why a Magento coupon was rejected
     *
     * @param Mage_SalesRule_Model_Coupon $magentoCoupon
     * @param Mage_Sales_Model_Quote $quote
     * @param PFG_Zeron_Helper_Data $helper
     * @param string $fileName
     * @param string $functionName
     * @return string
     */
    private function _analyzeMagentoCouponRejection($magentoCoupon, $quote, $helper, $fileName, $functionName)
    {
        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Analyzing Magento coupon rejection - Coupon ID: {$magentoCoupon->getId()}, Rule ID: {$magentoCoupon->getRuleId()}",
                $fileName,
                $functionName
            );
        }

        $rule = Mage::getModel('salesrule/rule')->load($magentoCoupon->getRuleId());

        if (!$rule->getId()) {
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Magento coupon has no associated rule - returning invalid coupon error",
                    $fileName,
                    $functionName
                );
            }
            return 'Невалиден промо код';
        }

        // Check if rule is active and within date range
        if (!$rule->getIsActive()) {
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Magento rule is not active - returning invalid coupon error",
                    $fileName,
                    $functionName
                );
            }
            return 'Невалиден промо код';
        }

        // Check date validity
        $now = Mage::getModel('core/date')->timestamp(time());
        $fromDate = $rule->getFromDate() ? strtotime($rule->getFromDate()) : null;
        $toDate = $rule->getToDate() ? strtotime($rule->getToDate()) : null;

        if (($fromDate && $now < $fromDate) || ($toDate && $now > $toDate)) {
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Magento rule is outside valid date range - returning invalid coupon error",
                    $fileName,
                    $functionName
                );
            }
            return 'Невалиден промо код';
        }

        // Check usage limits
        if ($this->_checkMagentoCouponUsageLimits($magentoCoupon, $quote, $helper, $fileName, $functionName)) {
            return 'Невалиден промо код';
        }

        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Magento coupon passed basic validation - analyzing business rule failures",
                $fileName,
                $functionName
            );
        }

        // Coupon is valid but failed to apply - check specific business rules
        // Check for price group restrictions
        $priceGroupError = $this->_checkPriceGroupRestrictions($rule, $quote, $helper, $fileName, $functionName);
        if ($priceGroupError) {
            return $priceGroupError;
        }

        // Check other business rule failures
        $businessRuleError = $this->_checkOtherBusinessRules($rule, $quote, $helper, $fileName, $functionName);
        if ($businessRuleError) {
            return $businessRuleError;
        }

        // Coupon is valid but failed for unknown business rule reasons
        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Magento coupon is valid but failed for unknown business rule reasons",
                $fileName,
                $functionName
            );
        }

        return 'Промо кодът е валиден, но не може да бъде приложен';
    }

    /**
     * Check if coupon rejection was due to price group restrictions
     *
     * @param Mage_SalesRule_Model_Rule $rule
     * @param Mage_Sales_Model_Quote $quote
     * @param PFG_Zeron_Helper_Data $helper
     * @param string $fileName
     * @param string $functionName
     * @return string|null
     */
    private function _checkPriceGroupRestrictions($rule, $quote, $helper, $fileName, $functionName)
    {
        $skippedPriceGroupsConfig = $rule->getData('zeron_skipped_price_groups');

        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Checking price group restrictions for rule {$rule->getId()} - Skipped groups: '{$skippedPriceGroupsConfig}'",
                $fileName,
                $functionName
            );
        }

        if (!$skippedPriceGroupsConfig) {
            return null; // No price group restrictions
        }

        $skippedPriceGroups = explode(',', $skippedPriceGroupsConfig);
        $skippedPriceGroups = array_map('trim', $skippedPriceGroups);

        // Check if all items in cart are from skipped price groups
        $allItemsSkipped = true;
        $items = $quote->getAllVisibleItems();

        foreach ($items as $item) {
            $product = $item->getProduct();
            $priceGroup = Mage::helper('pfgzeron/promo')->getProductPriceGroup($product);

            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Item {$item->getId()} (Product {$product->getId()}) has price group: {$priceGroup}",
                    $fileName,
                    $functionName
                );
            }

            if ($priceGroup < 1 || !in_array($priceGroup, $skippedPriceGroups)) {
                $allItemsSkipped = false;
                break;
            }
        }

        if ($allItemsSkipped) {
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "All items are from skipped price groups - returning specific price group error message",
                    $fileName,
                    $functionName
                );
            }
            return 'Някои продукти не са приложими за този промо код';
        }

        return null; // No price group issues
    }

    /**
     * Check Magento coupon usage limits
     *
     * @param Mage_SalesRule_Model_Coupon $coupon
     * @param Mage_Sales_Model_Quote $quote
     * @param PFG_Zeron_Helper_Data $helper
     * @param string $fileName
     * @param string $functionName
     * @return bool True if usage limits are exceeded (coupon is invalid)
     */
    private function _checkMagentoCouponUsageLimits($coupon, $quote, $helper, $fileName, $functionName)
    {
        // Check overall usage limit
        if ($coupon->getUsageLimit() && $coupon->getTimesUsed() >= $coupon->getUsageLimit()) {
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Magento coupon usage limit exceeded - Used: {$coupon->getTimesUsed()}, Limit: {$coupon->getUsageLimit()}",
                    $fileName,
                    $functionName
                );
            }
            return true;
        }

        // Check per-customer usage limit
        $customerId = $quote->getCustomerId();
        if ($customerId && $coupon->getUsagePerCustomer()) {
            $couponUsage = new Varien_Object();
            Mage::getResourceModel('salesrule/coupon_usage')->loadByCustomerCoupon(
                $couponUsage, $customerId, $coupon->getId()
            );

            if ($couponUsage->getCouponId() &&
                $couponUsage->getTimesUsed() >= $coupon->getUsagePerCustomer()) {
                if ($helper->isCouponDebugEnabled()) {
                    $helper->logCouponDebug(
                        "Magento coupon per-customer usage limit exceeded - Customer {$customerId} used: {$couponUsage->getTimesUsed()}, Limit: {$coupon->getUsagePerCustomer()}",
                        $fileName,
                        $functionName
                    );
                }
                return true;
            }
        }

        return false; // Usage limits are fine
    }

    /**
     * Check other business rules that might cause coupon rejection
     *
     * @param Mage_SalesRule_Model_Rule $rule
     * @param Mage_Sales_Model_Quote $quote
     * @param PFG_Zeron_Helper_Data $helper
     * @param string $fileName
     * @param string $functionName
     * @return string|null
     */
    private function _checkOtherBusinessRules($rule, $quote, $helper, $fileName, $functionName)
    {
        if ($helper->isCouponDebugEnabled()) {
            $helper->logCouponDebug(
                "Checking other business rules for rule {$rule->getId()}",
                $fileName,
                $functionName
            );
        }

        // Check minimum order amount
        $subtotal = $quote->getSubtotal();
        if ($rule->getConditionsSerialized()) {
            // Rule has conditions - check if cart meets minimum requirements
            $conditions = unserialize($rule->getConditionsSerialized());
            if (isset($conditions['conditions'])) {
                foreach ($conditions['conditions'] as $condition) {
                    if (isset($condition['attribute']) && $condition['attribute'] === 'base_subtotal') {
                        $minAmount = isset($condition['value']) ? (float)$condition['value'] : 0;
                        if ($subtotal < $minAmount) {
                            if ($helper->isCouponDebugEnabled()) {
                                $helper->logCouponDebug(
                                    "Cart subtotal {$subtotal} is below minimum required amount {$minAmount}",
                                    $fileName,
                                    $functionName
                                );
                            }
                            return 'Промо кодът е валиден, но не може да бъде приложен';
                        }
                    }
                }
            }
        }

        // Check customer group restrictions
        $customerGroupId = $quote->getCustomerGroupId();
        $ruleCustomerGroups = $rule->getCustomerGroupIds();
        if ($ruleCustomerGroups && !in_array($customerGroupId, $ruleCustomerGroups)) {
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Customer group {$customerGroupId} is not allowed for this rule",
                    $fileName,
                    $functionName
                );
            }
            return 'Промо кодът е валиден, но не може да бъде приложен';
        }

        // Check website restrictions
        $websiteId = $quote->getStore()->getWebsiteId();
        $ruleWebsites = $rule->getWebsiteIds();
        if ($ruleWebsites && !in_array($websiteId, $ruleWebsites)) {
            if ($helper->isCouponDebugEnabled()) {
                $helper->logCouponDebug(
                    "Website {$websiteId} is not allowed for this rule",
                    $fileName,
                    $functionName
                );
            }
            return 'Промо кодът е валиден, но не може да бъде приложен';
        }

        return null; // No other business rule issues found
    }
}
