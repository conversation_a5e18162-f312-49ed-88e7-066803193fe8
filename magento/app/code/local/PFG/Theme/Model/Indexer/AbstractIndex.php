<?php

abstract class PFG_Theme_Model_Indexer_AbstractIndex extends Mage_Index_Model_Indexer_Abstract
{
    private bool $_echo = false;
    protected string $_indexLogFile = 'praktis_theme_index.log';

    public function setEchoOutput(bool $true)
    {
        $this->_echo = $true;
    }

    protected function _getHelper(): PFG_Theme_Helper_Data
    {
        return Mage::helper('pfg_theme');
    }

    protected function _log(string $message): void
    {
        if ($this->_echo) {
            echo $message . PHP_EOL;
        }

        Mage::log($message, null, $this->_indexLogFile, true);
    }

    protected function _getConnection(): Varien_Db_Adapter_Interface
    {
        return Mage::getSingleton('core/resource')->getConnection('core_write');
    }
}
