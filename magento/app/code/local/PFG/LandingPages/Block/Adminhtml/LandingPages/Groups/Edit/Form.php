<?php

class PFG_LandingPages_Block_Adminhtml_LandingPages_Groups_Edit_Form extends Mage_Adminhtml_Block_Widget_Form
{
    /**
     * @return Praktis_Theme_Block_Adminhtml_Offerings_Edit_Form
     */
    protected function _prepareForm()
    {
        $model = $this->_getInstance();

        $form = new Varien_Data_Form(
            array(
                'id' => 'edit_form',
                'action' => $this->getData('action'),
                'method' => 'post',
                'enctype' => 'multipart/form-data'
            )
        );
        $form->setHtmlIdPrefix('block_');

        $fieldset = $form->addFieldset('base_fieldset',
            array('legend' => $this->__('Landing Page Group Configuration'), 'class' => 'fieldset-wide')
        );

        if ($model->getId()) {
            $fieldset->addField('id', 'hidden', array(
                'name' => 'id',
            ));
        }

        $fieldset->addField('page_id', 'select', [
            'name' => 'page_id',
            'label' => $this->__('Assign to Landing Page'),
            'title' => $this->__('Assign to Landing Page'),
            'required' => true,
            'options' => ['' => ''] + Mage::getModel('pfg_landingpages/pages')->getCollection()->toOptionHash(),
        ]);

        $fieldset->addField('name', 'text', array(
            'name' => 'name',
            'label' => $this->__('Name'),
            'title' => $this->__('Name'),
            'required' => true,
        ));

        $fieldset->addField('position', 'text', array(
            'name' => 'position',
            'label' => $this->__('Position'),
            'title' => $this->__('Position'),
            'required' => true,
        ));

        $fieldset->addField('active', 'select', array(
            'name' => 'active',
            'label' => $this->__('Is Active'),
            'title' => $this->__('Is Active'),
            'required' => true,
            'options' => [
                0 => $this->__('No'),
                1 => $this->__('Yes'),
            ],
        ));

        $form->setValues($model->getData());
        $form->setUseContainer(true);
        $this->setForm($form);

        return parent::_prepareForm();
    }

    /**
     * @return Praktis_Theme_Model_Offering_Block
     */
    protected function _getInstance()
    {
        return Mage::registry('current_landing_page_group');
    }
}
