<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2017 Amasty (https://www.amasty.com)
 * @package Amasty_Autoshipping
 */
-->
<config>
    <sections>
        <amautoshipping translate="label" module="amautoshipping">
            <label>Auto Shipping</label>
            <tab>amasty</tab>
            <frontend_type>text</frontend_type>
            <sort_order>12119</sort_order>
            <show_in_default>1</show_in_default>
            <show_in_website>1</show_in_website>
            <show_in_store>1</show_in_store>
            <groups>
                <general translate="label">
                    <label>General</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>1</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <enable translate="label comment">
                            <label>Enable</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </enable>
                        <shipping_method>
                            <label>Default shipping method</label>
                            <frontend_type>select</frontend_type>
                            <source_model>amautoshipping/allmethods</source_model>
                            <sort_order>60</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </shipping_method>
                        <select_method>
                            <label>If default method is not available</label>
                            <frontend_type>select</frontend_type>
                            <source_model>amautoshipping/selectMethods</source_model>
                            <sort_order>70</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </select_method>
                        <not_auto_fill>
                            <label>Not Auto-fill Fields</label>
                            <frontend_type>multiselect</frontend_type>
                            <source_model>amautoshipping/source_allowedFields</source_model>
                            <sort_order>80</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </not_auto_fill>
                    </fields>
                </general>
                <address translate="label">
                    <label>Default Address</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <country_id translate="label">
                            <label>Country</label>
                            <frontend_type>select</frontend_type>
                            <sort_order>20</sort_order>
                            <source_model>adminhtml/system_config_source_country</source_model>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                            <can_be_empty>1</can_be_empty>
                        </country_id>
                        <region translate="label comment">
                            <label>State</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>30</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </region>
                        <city translate="label">
                            <label>City</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>40</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </city>
                        <postcode translate="label comment">
                            <label>ZIP</label>
                            <frontend_type>text</frontend_type>
                            <sort_order>50</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>1</show_in_store>
                        </postcode>
                    </fields>
                </address>
                <geoip translate="label">
                    <label>Geolocation</label>
                    <frontend_type>text</frontend_type>
                    <sort_order>20</sort_order>
                    <show_in_default>1</show_in_default>
                    <show_in_website>1</show_in_website>
                    <show_in_store>1</show_in_store>
                    <fields>
                        <use translate="label comment">
                            <label>Use Geo IP</label>
                            <frontend_type>select</frontend_type>
                            <source_model>adminhtml/system_config_source_yesno</source_model>
                            <frontend_model>amautoshipping/adminhtml_settings_geolocation</frontend_model>
                            <sort_order>10</sort_order>
                            <show_in_default>1</show_in_default>
                            <show_in_website>1</show_in_website>
                            <show_in_store>0</show_in_store>
                        </use>
                    </fields>
                </geoip>
            </groups>
        </amautoshipping>
    </sections>
</config>
