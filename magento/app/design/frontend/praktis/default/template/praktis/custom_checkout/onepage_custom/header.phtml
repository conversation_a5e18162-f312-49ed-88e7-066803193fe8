<?php /** @var $this Praktis_CustomCheckout_Block_Checkout_Onepage_Header */ ?>
<div class="row">
    <div class="col-12 col-lg-10 mx-auto">
        <div class="row">
            <div class="col-12 col-md-6 text-center text-md-left my-2 my-md-0">
                <span class="border badge-light p-2 text-14"><a class="text-dark"
                                                                          id="checkout-go-back-button"
                                                                          href="<?php echo $this->getUrl('checkout/cart'); ?>"><i
                                class="fas fa-backward mr-2"></i><?php echo $this->__('Back to cart'); ?></a></span>
            </div>
            <div class="col-12 col-md-6 text-center text-md-right my-2 my-md-0">
                <img src="<?php echo $this->getSkinUrl('images/payment-providers.svg'); ?>"
                     alt="<?php echo $this->__('Payment Providers'); ?>">
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <div class="steps">
                    <ul class="steps-container">
                        <li style="width:33%;" class="activated">
                            <a class="step d-block" href="javascript:void(0);">
                                <div class="step-image" data-select_step="1"><span></span></div>
                                <div class="step-description"><?php echo $this->__('Client details') ?></div>
                            </a>
                        </li>
                        <li style="width:33%;">
                            <a class="step d-block" href="javascript:void(0);">
                                <div class="step-image" data-select_step="2"><span></span></div>
                                <div class="step-description"><?php echo $this->__('Shipping details') ?></div>
                            </a>
                        </li>
                        <li style="width:33%;">
                            <a class="step d-block" href="javascript:void(0);">
                                <div class="step-image" data-select_step="3"><span></span></div>
                                <div class="step-description"><?php echo $this->__('Payment') ?></div>
                            </a>
                        </li>
                    </ul>
                    <div class="step-bar" style="width: 33%;"></div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $generalError = $this->getGeneralError(); ?>
<div class="row is-invalid general-error-block d-none" id="checkout-general-error-top">
    <div class="col-12 col-lg-10 mx-auto my-3 text-center">
        <div class="alert alert-danger inner-most-wrapper rounded-0" role="alert">
            <?php echo $generalError ?>
        </div>
    </div>
</div>
<script type="text/javascript">
  jQuery(document).ready(function () {
    jQuery(".steps .step .step-image").click(function (event) {
      event.preventDefault();
      var currentStepElement = jQuery(".steps .steps-container li.activated .step-image").last();
      var currentStepNumber = parseInt(currentStepElement.data('select_step'));
      if (currentStepNumber === 1) {
        return false;
      }
      var stepToGoTo = parseInt(jQuery(this).data('select_step'));
      if (currentStepNumber <= stepToGoTo) {
        return false;
      }

      /* Show loader */
      jQuery('body').addClass('overlay');

      jQuery("#checkout_requested_step").val(stepToGoTo);
      jQuery.ajax({
        method: "POST",
        url: '<?php echo $this->getGoToStepUrl() ?>',
        data: jQuery("#praktis-onepage-form").serialize(),
        dataType: 'json',
        success: function (successResult) {
          /* Hide loader */
          jQuery('body').removeClass('overlay');

          if (successResult.hasOwnProperty('current_step')) {
            jQuery("#checkout_current_step").val(successResult.current_step);
          }

          if (successResult.hasOwnProperty('step_html')) {
            jQuery("#custom_checkout_steps_holder").html(successResult.step_html);
            jQuery(successResult.step).find("script").each(function () {
              eval(jQuery(this).text());
            });
            stepBindings();
          }

          if (successResult.hasOwnProperty('review')) {
            jQuery("#custom_checkout_review").html(successResult.review);
            jQuery(successResult.review).find("script").each(function () {
              eval(jQuery(this).text());
              stepBindings();
            });
          }

          if (successResult.hasOwnProperty('title')) {
            jQuery("#custom_checkout_steps_title h1").text(successResult.title);
          }

          if (successResult.hasOwnProperty('current_step')) {
            if (parseInt(successResult.current_step) === 3) {
              jQuery('#praktis-custom-checkout-next-step').removeClass('btn-primary').addClass('btn-success').text(Translator.translate('Finish Order'));
            } else {
              jQuery('#praktis-custom-checkout-next-step').removeClass('btn-success').addClass('btn-primary').text(Translator.translate('Continue'));
            }
            jQuery("#checkout_current_step").change();
          }

          if (successResult.hasOwnProperty('current_step')) {
            var stepsProgressElements = jQuery(".steps-container li");
            stepsProgressElements.removeClass('activated');
            var stepProgressElement = stepsProgressElements.eq(parseInt(successResult.current_step) - 1);
            if (stepProgressElement.length > 0) {
              for (var s = 0; s < parseInt(successResult.current_step); s++) {
                stepsProgressElements.eq(s).addClass('activated');
              }
              var progressBarFilling = parseInt(successResult.current_step) * 33;
              if (progressBarFilling === 99) {
                progressBarFilling = 100;
              }
              jQuery('.step-bar').first().css('width', progressBarFilling + '%');
            }
          }
        },
        error: function (errorResult) {
          /* Hide loader */
          jQuery('body').removeClass('overlay');

          if (errorResult.hasOwnProperty('responseText')) {
            var jsonResponse = JSON.parse(errorResult.responseText);
            if (
              jsonResponse.hasOwnProperty('error') && jsonResponse.error
              && jsonResponse.hasOwnProperty('messages') && !jQuery.isEmptyObject(jsonResponse.messages)
            ) {
              jQuery.each(jsonResponse.messages, function (fieldName, errorMessage) {
                jQuery("input[name=" + fieldName + "]").before("<div class='alert alert-danger' role='alert'>" + errorMessage + "</div>");
              });
            }
          }
        }
      });
    });
  });
</script>
