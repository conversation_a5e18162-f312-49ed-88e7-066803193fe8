<?php
/** @var $this Praktis_Checkout_Block_Shipping_Custom */
?>
<?php
/** @var Praktis_Checkout_Helper_Shipping $_helper */
$_helper = Mage::helper('praktis_checkout/shipping');

$econtMethodValue = Praktis_Checkout_Helper_Shipping::METHOD_ECONT;
$praktisTableRateMethodValue = Praktis_Checkout_Helper_Shipping::METHOD_PRAKTIS_TABLE_RATE;
$storePickupMethodValue = Praktis_Checkout_Helper_Shipping::METHOD_PRAKRIS_STORE;

$customFormPrefix = Praktis_Checkout_Helper_Shipping::PREFIX;

?>
<?php $shippingData = $this->getShippingData(); ?>
<div id="praktis-shipping-form">
    <input type="hidden" name="shipping_method_name" value="praktis_custom"/>
    <?php if ($this->getShippingError()): ?>
        <p class="error-msg">
            <?php echo $this->escapeHtml($this->getShippingError()); ?>
        </p>
    <?php endif; ?>
    <ul class="form-list">
        <li class="control">
            <div>
                <div class="input-box">
                    <label for="shipping_carrier"><?php echo $this->__('Shipping'); ?></label>
                    <select id="shipping_carrier"
                            @change="changeShippingCarrier"
                            class="select please-select-shipping"
                            name="<?php echo $customFormPrefix ?>[shipping_carrier]">
                        <option value="-1"
                                <?php if ($shippingData->getCarrierId() < 1): ?>selected<?php endif; ?>
                                disabled>
                            <?php echo $this->__('Please select'); ?>
                        </option>
                        <?php if ($this->isEcontEnabled()): ?>
                            <option value="<?php echo $econtMethodValue; ?>"
                                    <?php if ($shippingData->isEcont()): ?>selected<?php endif; ?>
                                    <?php if ($this->allowOnlyStorePickUp()): ?>disabled<?php endif; ?>
                            >
                                <?php echo $this->__('Econt'); ?>
                            </option>
                        <?php endif; ?>
                        <?php if ($this->isPraktisTableRateEnabled()): ?>
                            <option value="<?php echo $praktisTableRateMethodValue; ?>"
                                    <?php if ($shippingData->isPraktisTableRate()): ?>selected<?php endif; ?>
                                    <?php if ($this->allowOnlyStorePickUp()): ?>disabled<?php endif; ?>
                            >
                                <?php echo Mage::getStoreConfig('carriers/praktis_tablerate/title'); ?>
                            </option>
                        <?php endif; ?>
                        <option value="<?php echo $storePickupMethodValue; ?>">
                            <?php echo $this->__('pick from store'); ?>
                        </option>
                    </select>
                    <input type="hidden" v-model="shippingCarrier"/>
                </div>
            </div>
        </li>
        <template v-if="showCourierAddressFields">
            <li>
                <div>
                    <div class="input-box">
                        <label for="<?php echo $customFormPrefix ?>:shipping_type"><?php echo $this->__('Select shipping type'); ?></label>
                        <select id="<?php echo $customFormPrefix ?>:shipping_type"
                                name="<?php echo $customFormPrefix ?>[shipping_type]"
                                @change="changeShippingType"
                                class="select">
                            <option value="-1"
                                    <?php if (!$shippingData->isShipToDoor() && !$shippingData->isShipToOffice()): ?>selected<?php endif; ?>
                                    disabled>
                                <?php echo $this->__('Please select'); ?>
                            </option>
                            <option
                                <?php if ($shippingData->isShipToDoor()): ?>selected<?php endif; ?>
                                value="<?php echo Praktis_Checkout_Helper_Shipping::TYPE_TO_DORE; ?>">
                                <?php echo $this->__('to door'); ?>
                            </option>
                            <option
                                <?php if ($shippingData->isShipToOffice()): ?>selected<?php endif; ?>
                                value="<?php echo Praktis_Checkout_Helper_Shipping::TYPE_TO_OFFICE; ?>">
                                <?php echo $this->__('to office'); ?>
                            </option>
                        </select>
                        <input type="hidden" v-model="shippingType"/>
                    </div>
                </div>
            </li>
            <template v-if="shippingTypeSelected">
                <li>
                    <div class="input-box">
                        <label for="<?php echo $customFormPrefix ?>:country_id"><?php echo $this->__('Country'); ?></label>
                        <select title="<?php echo $this->__('Country'); ?>"
                                id="<?php echo $customFormPrefix ?>:country_id"
                                name="<?php echo $customFormPrefix ?>[country_id]"
                                @change="setAddressCountry"
                                class="select">
                            <option value="BG"><?php echo $this->__('Bulgaria'); ?></option>
                        </select>
                    </div>
                    <div class="input-box">
                        <label><?php echo $this->__('City'); ?><em>*</em></label>
                    </div>
                </li>
                <li>
                    <v-select
                            placeholder="<?php echo $this->__('Search for a city'); ?>"
                            id="<?php echo $customFormPrefix ?>:city"
                            :options="suggestedCities"
                            v-model="address.city"
                            label="name"
                            @search="searchForCity"
                            :filterable="false"
                    >
                        <template slot="no-options">
                            <?php echo $this->__('No available city suggestions.'); ?>
                        </template>
                        <template slot="option" slot-scope="option">
                            {{ option.type }} {{ option.name }} (<?php echo $this->__('ZIP/Postal Code'); ?> {{
                            option.postcode }})
                        </template>
                    </v-select>
                    <!--                    <input type="hidden" class="validate-required-city" />-->
                </li>
                <li>
                    <div class="input-box">
                        <label for="<?php echo $customFormPrefix ?>:postcode"><?php echo $this->__('ZIP/Postal Code'); ?>
                            <em>*</em></label>
                        <input type="text" title="<?php echo $this->__('ZIP/Postal Code'); ?>"
                               id="<?php echo $customFormPrefix ?>:postcode"
                               name="<?php echo $customFormPrefix ?>[postcode]"
                               v-model="address.postcode"
                               class="input-text validate-required-post"/>
                    </div>
                </li>
                <li v-if="shipToDoor">
                    <?php // Ship to door ?>
                    <template v-if="isEcont">
                <li>
                    <div class="input-box">
                        <label for="<?php echo $customFormPrefix ?>:additional_info"><?php echo $this->__('Address(quarter, street, number, block)'); ?></label>
                        <input type="text" title="<?php echo $this->__('Address'); ?>"
                               id="<?php echo $customFormPrefix ?>:additional_info"
                               name="<?php echo $customFormPrefix ?>[additional_info]"
                               v-model="address.additionalInfo"
                               class="input-text validate-full-address"/>
                    </div>
                </li>
            </template>
            <template v-else>
                <div class="input-box">
                    <label for="<?php echo $customFormPrefix ?>:neighborhood"><?php echo $this->__('Neighborhood'); ?></label>
                    <input type="text" title="<?php echo $this->__('Neighborhood'); ?>"
                           id="<?php echo $customFormPrefix ?>:neighborhood"
                           name="<?php echo $customFormPrefix ?>[neighborhood]"
                           class="input-text validate-full-address"
                           v-model="address.neighborhood"
                    />
                </div>
                <div class="input-box">
                    <label for="<?php echo $customFormPrefix ?>:street"><?php echo $this->__('Street'); ?></label>
                    <input type="text" title="<?php echo $this->__('Street'); ?>"
                           id="<?php echo $customFormPrefix ?>:street"
                           name="<?php echo $customFormPrefix ?>[street]"
                           class="input-text validate-full-address"
                           v-model="address.street"
                    />
                </div>
            </template>
            </li>
            <li v-else>
                <?php // Ship to office ?>
                <div class="input-box" v-if="availableOffices.length > 0">
                    <label for="<?php echo $customFormPrefix ?>:office_id"><?php echo $this->__('Office'); ?></label>
                    <select title="<?php echo $this->__('Office'); ?>"
                            id="<?php echo $customFormPrefix ?>:office_id"
                            name="<?php echo $customFormPrefix ?>[office_id]"
                            class="select validate-required-office"
                            @change="selectOffice">
                        <option v-for="office in availableOffices"
                                :selected="address.officeId == office.id"
                                :value="office.id">
                            {{ office.label }}
                        </option>
                    </select>
                </div>
                <div class="input-box" v-else>
                    <div class="input-box">
                        <label><?php echo $this->__('Enter a city for list of offices') ?></label>
                    </div>
                </div>

                <div class="input-box">
                    <label for="<?php echo $customFormPrefix ?>:additional_info"><?php echo $this->__('Address(quarter, street, number, block)'); ?></label>
                    <input type="text" title="<?php echo $this->__('Address'); ?>"
                           id="<?php echo $customFormPrefix ?>:additional_info"
                           name="<?php echo $customFormPrefix ?>[additional_info]"
                           v-model="address.additionalInfo"
                           class="input-text validate-full-address"/>
                </div>
            </li>
        </template>
        </template>
        <template v-if="showShippingTableRateAddressFields">
            <li>
                <div>
                    <div class="input-box">
                        <label for="<?php echo $customFormPrefix ?>:shipping_type"><?php echo $this->__('Select shipping type'); ?></label>
                        <select id="<?php echo $customFormPrefix ?>:shipping_type"
                                name="<?php echo $customFormPrefix ?>[shipping_type]"
                                @change="changeShippingType"
                                class="select">
                            <option value="-1"
                                    <?php if (!$shippingData->isShipToDoor()): ?>selected<?php endif; ?>
                                    disabled>
                                <?php echo $this->__('Please select'); ?>
                            </option>
                            <option
                                <?php if ($shippingData->isShipToDoor()): ?>selected<?php endif; ?>
                                value="<?php echo Praktis_Checkout_Helper_Shipping::TYPE_TO_DORE; ?>">
                                <?php echo $this->__('to door'); ?>
                            </option>
                        </select>
                        <input type="hidden" v-model="shippingType"/>
                    </div>
                </div>
            </li>
            <template v-if="shippingTypeSelected">
                <li>
                    <div class="input-box">
                        <label for="<?php echo $customFormPrefix ?>:country_id"><?php echo $this->__('Country'); ?></label>
                        <select title="<?php echo $this->__('Country'); ?>"
                                id="<?php echo $customFormPrefix ?>:country_id"
                                name="<?php echo $customFormPrefix ?>[country_id]"
                                @change="setAddressCountry"
                                class="select">
                            <option value="BG"><?php echo $this->__('Bulgaria'); ?></option>
                        </select>
                    </div>
                    <div class="input-box">
                        <label><?php echo $this->__('City'); ?><em>*</em></label>
                    </div>
                </li>
                <li>
                    <v-select
                            placeholder="<?php echo $this->__('Search for a city'); ?>"
                            id="<?php echo $customFormPrefix ?>:city"
                            :options="suggestedCities"
                            v-model="address.city"
                            label="name"
                            @search="searchForCity"
                            :filterable="false"
                    >
                        <template slot="no-options">
                            <?php echo $this->__('No available city suggestions.'); ?>
                        </template>
                        <template slot="option" slot-scope="option">
                            {{ option.type }} {{ option.name }} (<?php echo $this->__('ZIP/Postal Code'); ?> {{
                            option.postcode }})
                        </template>
                    </v-select>
                    <input type="hidden" class="validate-required-city"/>
                </li>
                <li>
                    <div class="input-box">
                        <label for="<?php echo $customFormPrefix ?>:postcode"><?php echo $this->__('ZIP/Postal Code'); ?>
                            <em>*</em></label>
                        <input type="text" title="<?php echo $this->__('ZIP/Postal Code'); ?>"
                               id="<?php echo $customFormPrefix ?>:postcode"
                               name="<?php echo $customFormPrefix ?>[postcode]"
                               v-model="address.postcode"
                               class="input-text validate-required-post"/>
                    </div>
                </li>
                <li v-if="shipToDoor">
                    <?php // Ship to door ?>
                    <ul class="form-list">
                        <li>
                            <div class="input-box">
                                <label for="<?php echo $customFormPrefix ?>:additional_info"><?php echo $this->__('Address(quarter, street, number, block)'); ?></label>
                                <input type="text" title="<?php echo $this->__('Address'); ?>"
                                       id="<?php echo $customFormPrefix ?>:additional_info"
                                       name="<?php echo $customFormPrefix ?>[additional_info]"
                                       v-model="address.additionalInfo"
                                       class="input-text validate-full-address"/>
                            </div>
                        </li>
                        <li>
                            <input type="hidden" class="validate-full-address"/>
                        </li>
                    </ul>
                </li>
            </template>
        </template>
    </ul>
    <div class="buttons-set" v-if="shippingTypeSelected">
        <p class="click-to-calculate">
            <strong><?php echo $this->__('Click Calculate Price after you enter/modify the data.'); ?></strong>
        </p>
        <div class="clear"></div>
        <button type="button"
                class="button shipping-action-button"
                :class="{'recalculation-required': recalculationRequired}"
                @click="calculateShipping"
        >
            <?php echo $this->__('Calculate Price'); ?>
        </button>
        <div class="selected-address">
            <p id="selected-city"><?php echo $this->__('City : '); ?><span></span></p>
            <p id="selected-post-code"><?php echo $this->__('Post Code : '); ?><span></span></p>
            <p id="selected-address"><?php echo $this->__('Address : '); ?><span></span></p>
            <p id="selected-office"><?php echo $this->__('Office : '); ?><span></span></p>
        </div>
        <button type="button" class="button change-address-button"
                @click="showAddress">
            <?php echo $this->__('Change Address'); ?>
        </button>

    </div>
</div>

<script type="text/javascript">
    //<![CDATA[
    (function ($) {
        $.fn.goTo = function () {
            $('html, body').animate({
                scrollTop: $(this).offset().top + 'px'
            }, 'fast');
            return this; // for chaining...
        };
    })(jQuery);

    function getAddressObject() {
        return {
            countryId: '<?php echo 'BG'; ?>',
            postcode: null,
            city: null,
            neighborhood: null,
            street: null,
            streetNumber: null,
            blockNumber: null,
            entranceNumber: null,
            floorNumber: null,
            apartmentNumber: null,
            officeId: null,
            priorityTimeType: null,
            priorityTimeHour: null,
            additionalInfo: null
        }
    }

    function debounce(func, wait, immediate) {
        var timeout;
        return function () {
            var context = this, args = arguments;
            var later = function () {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    var loadCities = debounce(function (loading, search, carrier, country, component) {
        component.suggestedCities = [];
        axios.post('<?php echo $_helper->getAvailableCitiesUrl(); ?>', jQuery.param({
            shipping_carrier: carrier,
            city: search,
            country_id: country
        })).then(function (response) {
            var data = response.data;
            if (data.error) {
                loading(false);
                return;
            }

            component.suggestedCities = data.items;
            if (component.suggestedCities.length < 1) {
                component.address.city.name = '';
            }

            component.$nextTick(function () {
                loading(false);
            });
        }).catch(function (error) {
            console.log(error);
            loading(false);
        });
    }, 400, false);

    var loadNeighborhoods = debounce(function (loading, search, city, component) {
        component.suggestedNeighborhoods = [];
        axios.post('<?php echo $_helper->getAvailableNeighborhoodsUrl(); ?>', jQuery.param({
            city_id: city,
            neighborhood: search
        })).then(function (response) {
            var data = response.data;
            if (data.error) {
                component.handleError(data.message);
                loading(false);
                return;
            }

            component.suggestedNeighborhoods = data.items;
            if (component.suggestedNeighborhoods.length < 1) {
                component.address.neighborhood = '';
            }

            component.$nextTick(function () {
                loading(false);
            });
        }).catch(function (error) {
            console.log(error);
            loading(false);
        });
    }, 400, false);

    var loadStreets = debounce(function (loading, search, city, component) {
        component.suggestedStreets = [];
        axios.post('<?php echo $_helper->getAvailableStreetsUrl(); ?>', jQuery.param({
            city_id: city,
            neighborhood: search
        })).then(function (response) {
            var data = response.data;
            if (data.error) {
                component.handleError(data.message);
                loading(false);
                return;
            }

            component.suggestedStreets = data.items;
            if (component.suggestedStreets.length < 1) {
                component.address.street = '';
            }

            component.$nextTick(function () {
                loading(false);
            });
        }).catch(function (error) {
            console.log(error);
            loading(false);
        });
    }, 400, false);

    Vue.component('v-select', VueSelect.VueSelect);
    window.shippingForm = new Vue({
        el: '#praktis-shipping-form',
        data: {
            shippingCarrier: '<?php echo $shippingData->getCarrierId(); ?>',
            shippingType: '<?php echo $shippingData->getShippingType(); ?>',
            shipping_method_name: 'praktis_custom',
            address: {
                countryId: '<?php echo $shippingData->getAddress()->getCountryId(); ?>',
                postcode: '<?php echo $shippingData->getAddress()->getPostcode(); ?>',
                city: null,
                neighborhood: null,
                street: null,
                streetNumber: null,
                block: null,
                entrance: null,
                floor: null,
                apartment: null,
                officeId: null,
                priorityTimeType: null,
                priorityTimeHour: null,
                additionalInfo: null
            },
            availableOffices: <?php echo Zend_Json::encode($shippingData->getAvailableOffices()); ?>,
            suggestedCities: [],
            suggestedNeighborhoods: [],
            suggestedStreets: [],
            lastSelectedCityId: null,
            deliverAtExactTime: false,
            recalculationRequired: true
        },
        watch: {
            'address': {
                handler: function (val, oldVal) {
                    if (val.city) {
                        if (this.lastSelectedCityId != val.city.id) {
                            this.updateCity(val.city);
                        }
                        this.lastSelectedCityId = val.city.id;
                    }

                    if (!this.recalculationRequired) {
                        this.$nextTick(function () {
                            this.resetShippingInfo(function () {
                            })
                        });
                    }

                    this.recalculationRequired = true;
                },
                deep: true
            }
        },
        computed: {
            // a computed getter
            showCourierAddressFields: function () {
                return this.isEcont;
            },
            showShippingTableRateAddressFields: function () {
                return this.isTableRate;
            },
            shippingTypeSelected: function () {
                return this.shipToDoor || this.shipToOffice;
            },
            shipToDoor: function () {
                return this.shippingType == <?php echo Praktis_Checkout_Helper_Shipping::TYPE_TO_DORE; ?>;
            },
            shipToOffice: function () {
                return this.shippingType == <?php echo Praktis_Checkout_Helper_Shipping::TYPE_TO_OFFICE; ?>;
            },
            isEcont: function () {
                return this.shippingCarrier == <?php echo $econtMethodValue; ?>;
            },
            isTableRate: function () {
                return this.shippingCarrier == <?php echo $praktisTableRateMethodValue; ?>;
            },
            disableNeighborhoodSearch: function () {
                return !this.address.city || this.address.city.id == null
            },
            disableStreetSearch: function () {
                return !this.address.city || this.address.city.id == null
            }
        },
        methods: {
            hideAddress: function (e) {
                jQuery('#praktis-shipping-form li').hide();
                jQuery('.click-to-calculate').hide();
                jQuery('.change-address-button').show();
                jQuery('.shipping-action-button').hide();
                jQuery('.selected-address').show();

                if (this.address.city.name) {
                    jQuery('#selected-city span').html(this.address.city.name);
                    jQuery('#selected-city').show();
                }
                if (this.address.postcode) {
                    jQuery('#selected-post-code span').html(this.address.postcode);
                    jQuery('#selected-post-code').show();
                }
                if (this.address.additionalInfo) {
                    jQuery('#selected-address span').html(this.address.additionalInfo);
                    jQuery('#selected-address').show();

                }
                if (this.address.officeId) {
                    jQuery('#selected-office span').html(this.address.officeId);
                    jQuery('#selected-office').show();
                }

            },
            showAddress: function (e) {
                jQuery('#praktis-shipping-form li').show();
                jQuery('.click-to-calculate').show();
                jQuery('.change-address-button').hide();
                jQuery('.shipping-action-button').show();
                jQuery('.selected-address').hide();
                jQuery('.selected-address p span').html('');


            },
            changeShippingCarrier: function (e) {
                var type = parseInt(e.target.value);
                this.shippingCarrier = type;
                var shippingType = this.shippingType;
                this.shippingType = null;
                this.updateShippingTypeSelect(-1);
                axios.post('<?= Mage::helper('praktis_abandonedcarts')->getSaveUrl(); ?>', [
                    {
                        changedAttr: 'shipping-carrier',
                        value: type
                    }
                ],
                    {
                        headers: {
                            'X_REQUESTED_WITH': 'XMLHttpRequest',
                        }
                    });

                this.resetShippingInfo(function (vue) {
                    vue.recalculationRequired = true;
                    vue.resetAddress();
                    vue.formResetErrors();
                    if (type == <?= $storePickupMethodValue ?>) {
                        vue.recalculationRequired = false;
                        updateCheckout("shipping", function () {
                            $('s_method_stenik_siteshipping_from_store_stenik_siteshipping_from_store').click();
                            $('shipping_form_stenik_siteshipping_from_store_stenik_siteshipping_from_store').show();
                            updateCheckout("shipping_method");
                        });
                    } else {
                        if (type == <?= $praktisTableRateMethodValue ?> && shippingType) {
                            vue.shippingType = 1;
                            vue.updateShippingTypeSelect(1);
                            vue.changeShippingType({target: {value: 1}});
                        } else if (shippingType) {
                            vue.shippingType = shippingType;
                            vue.updateShippingTypeSelect(shippingType);
                            vue.changeShippingType({target: {value: shippingType}});
                        }
                    }
                });
            },
            updateShippingTypeSelect: function (value) {
                var typeSelect = document.getElementById('<?php echo $customFormPrefix ?>:shipping_type');
                if (typeSelect) {
                    typeSelect.value = value;
                }
            },
            changeShippingType: function (e) {
                this.shippingType = parseInt(e.target.value);

                if (!this.recalculationRequired) {
                    this.$nextTick(function () {
                        this.resetShippingInfo(function () {
                        })
                    });
                }

                this.recalculationRequired = true;

                this.address.officeId = null;
                this.formResetErrors();
            },
            selectOffice: function (e) {
                this.address.officeId = e.target.value;
            },
            updateCity: function (city) {
                if (!city || !city.id) {
                    this.resetAddress();
                    return;
                }

                this.lastSelectedCityId = city.id;
                this.address.postcode = city.postcode;
                this.availableOffices = {};

                if (this.shipToOffice) {
                    this.loadAvailableOffices();
                }

                this.formResetErrors();
            },
            searchForCity: function (search, loading) {
                if (search.length > 2) {
                    if (!this.address.city) {
                        this.address.city = {};
                    }
                    this.address.city.name = search;
                    loading(true);
                    loadCities(loading, search, this.shippingCarrier, this.address.countryId, this);
                }
            },
            searchNeighborhoods: function (search, loading) {
                if (search.length > 2) {
                    loading(true);
                    loadNeighborhoods(loading, search, this.address.city.id, this);
                }
            },
            searchStreets: function (search, loading) {
                if (search.length > 2) {
                    loading(true);
                    loadStreets(loading, search, this.address.city.id, this);
                }
            },
            setAddressCountry: function (e) {
                this.address.countryId = e.target.value;
            },
            resetShippingInfo: function (callback) {
                var self = this;
                showLoading();
                axios.get('<?php echo $_helper->getResetShippingDataUrl(); ?>')
                    .then(function (response) {
                        self.formResetErrors();
                        updateCheckout('shipping', function () {
                            if (callback) {
                                callback(self);
                            }
                            hideLoading();
                        });
                    }).catch(function (error) {
                    self.handleError('<?php echo $this->__('Error resetting shipping.'); ?>');
                    self.resetAddress();
                    hideLoading();
                });
            },
            loadAvailableOffices: function (e) {
                var self = this;
                showLoading();
                axios.post('<?php echo $_helper->getOfficesUrl(); ?>', jQuery.param({
                    shipping_carrier: self.shippingCarrier,
                    city_id: self.address.city.id
                })).then(function (response) {
                    var data = response.data;
                    if (data.error) {
                        self.handleError(data.message);
                        self.resetAddress();
                        hideLoading();
                        return;
                    }

                    self.availableOffices = data.items;
                    self.formResetErrors();
                    hideLoading();
                }).catch(function (error) {
                    self.formResetErrors();
                    console.log(error);
                    self.handleError("Error getting shipping data, Please try again later");
                    self.resetAddress();
                    hideLoading();
                });
            },
            calculateShipping: function () {
                var self = this;
                self.recalculationRequired = false;

                this.$nextTick(function () {
                    if (this.validate()) {
                        showLoading();
                        axios.post('<?= Mage::helper('praktis_abandonedcarts')->getSaveUrl(); ?>', [
                            {
                                changedAttr: 'shipping-price',
                                value: true
                            }
                        ],
                            {
                                headers: {
                                    'X_REQUESTED_WITH': 'XMLHttpRequest',
                                }
                            });
                        axios.post('<?php echo $_helper->getStoreShippingDataUrl(); ?>', jQuery.param({
                            shipping_carrier: self.shippingCarrier,
                            shipping_type: self.shippingType,
                            shipping_method: self.shippingMethod,
                            address: self.address,
                            customer: self.getCustomerData()
                        })).then(function (response) {
                            var data = response.data;
                            if (data.error) {
                                self.handleError(data.message);
                                self.resetAddress();
                                hideLoading();
                                return;
                            }

                            updateCheckout('shipping', function () {

                                if (self.isEcont || self.isTableRate) {
                                    self.selectCheapestMethod();
                                }

                                hideLoading();
                                self.hideAddress();
                            });
                        }).catch(function (error) {
                            alert('<?php echo $this->__('Error calculating the shipping price.'); ?>');
                            self.resetAddress();
                            hideLoading();
                        });
                    } else {
                        self.recalculationRequired = true;
                    }
                });
            },
            selectCheapestMethod: function (ignoreSelected) {
                var cheapestMethodInput = $('cheapest-method');
                if (cheapestMethodInput) {
                    var hasPreselected = cheapestMethodInput.readAttribute('data-has-selected');
                    if (ignoreSelected) {
                        hasPreselected = 0;
                    }

                    if (cheapestMethodInput.value && hasPreselected == 0) {
                        $('s_method_' + cheapestMethodInput.value).click();
                        updateCheckout("shipping_method");
                    }
                }
            },
            getCustomerData: function () {
                <?php if($this->getLoggedCustomer()): ?>
                var email = '<?php echo $this->getLoggedCustomerEmail() ?>';
                <?php else:?>
                var email = $F('amasty-scheckout-login-email');
                <?php endif;?>
                return {
                    firstname: $F('billing\:firstname'),
                    lastname: $F('billing\:lastname'),
                    telephone: $F('billing\:telephone'),
                    email: email
                };
            },
            validate: function () {
                var checkboxes = jQuery('.checkout-agreements .required-entry');
                checkboxes.removeClass('required-entry');
                var validator = new Validation('amscheckout-main');
                var isValid = validator && validator.validate();
                checkboxes.addClass('required-entry');

                return isValid;
            },
            formResetErrors: function () {
                var validator = new Validation('amscheckout-main');
                return validator && validator.reset();
            },
            handleError: function (errorMessage) {
                alert(errorMessage);
            },
            resetAddress: function () {
                this.recalculationRequired = true;
                this.address = getAddressObject();
                this.availableOffices = {};
                this.suggestedCities = [];
                this.lastSelectedCityId = null;
            }
        }
    });

    Validation.addAllThese([
        ['please-select-shipping', '<?php echo $this->jsQuoteEscape($this->__('Please select a shipping method.')); ?>', function (v) {
            var carrier = parseInt(window.shippingForm.shippingCarrier);
            var type = parseInt(window.shippingForm.shippingType);

            return (carrier != -1 && type != -1) || (carrier == 2 && type == -1);
        }],
        ['validate-required-post', '<?php echo $this->jsQuoteEscape($this->__('Please enter a Zip/Post code.')); ?>', function (v) {
            return !Validation.get('IsEmpty').test(v);
        }],
        ['validate-required-city', '<?php echo $this->jsQuoteEscape($this->__('Please select a city.')); ?>', function (v) {
            var valid = false;
            var address = window.shippingForm.address;
            if (address.city && address.city.id) {
                valid = true
            }

            return valid;
        }],
        ['validate-full-address', '<?php echo $this->jsQuoteEscape($this->__('Please enter address')); ?>', function (v) {
            var address = window.shippingForm.address;
            var valid = false;
            if (address) {
                if (address.street && address.street.length > 0) {
                    valid = true;
                } else if (address.neighborhood && address.neighborhood.length > 0) {
                    valid = true;
                } else if (address.additionalInfo && address.additionalInfo.length > 0) {
                    valid = true;
                } else if (!window.shippingForm.shipToDoor) {
                    valid = true;
                }
            }

            return valid;
        }],
        ['phone-number-digits', '<?php echo $this->jsQuoteEscape($this->__('Please enter a valid phone number, only digits and + are allowed.')); ?>', function (v) {
            var reg = new RegExp('^\\d+$');
            var phone = v.replace('+', '');

            return reg.test(phone) && phone.length > 6;
        }],
        ['validate-required-office', '<?php echo $this->jsQuoteEscape($this->__('Please select an office.')); ?>', function (v) {
            return !Validation.get('IsEmpty').test($F('<?php echo $customFormPrefix ?>:office_id'));
        }],
        ['get-offices', '<?php echo $this->jsQuoteEscape($this->__('Please select an office.')); ?>', function (v) {
            return false;
        }],
        ['required-street-number', '<?php echo $this->jsQuoteEscape($this->__('Please enter a street number.')); ?>', function (v) {
            var address = window.shippingForm.address;
            return address.streetNumber && address.streetNumber.length > 0;
        }],
        ['recalculation-required', '<?php echo $this->jsQuoteEscape($this->__('Please recalculate the shipping price.')); ?>', function (v) {
            return false;
        }]
    ]);
    //]]>
</script>
