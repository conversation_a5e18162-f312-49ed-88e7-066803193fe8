<?php
$productId = Mage::registry('current_product')->getId();
?>

<div class="amloading" id="ajax-payment-loading-jetcredit" style="display: none;">
    <div class="text-center mt-3">
        <img src="<?= $this->getSkinUrl('images/ajax-loader.gif'); ?>">
    </div>
</div>

<div id="jetcredit-payment-wrapper" data-load-calculator="true"></div>

<div id="form_product_leasing_jetcredit" style="display:none">
    <script type="text/javascript">
      jQuery("#init-credit-calculators").click(function () {
        // show modal
        console.log('init-credit-calculators');
        const calculatorWrapper = jQuery('#jetcredit-payment-wrapper');
        const loader = document.getElementById('ajax-payment-loading-jetcredit');
        if (calculatorWrapper.data('load-calculator')) {
          loader.style.display = 'block'
          let url = '<?php echo $this->getUrl('stenik_leasingjetcredit/calculator/productViewCalculator', ['product_id' => $productId, 'qty' => '_qty_']); ?>';
          const qty = $F('qty') || 1;
          url = url.replace('_qty_', qty);

          new Ajax.Request(
            url,
            {
              method: 'get',
              onSuccess: function (transport) {
                if (transport.responseText.isJSON()) {
                  var response = transport.responseText.evalJSON();
                  if (response.html) {
                    calculatorWrapper.html(response.html);
                    console.log(calculatorWrapper.find('script').html());
                    eval(calculatorWrapper.find('script').html());
                    loader.style.display = 'none'
                    calculatorWrapper.data('load-calculator', 'false');
                    window.calcJetLoaded = true;
                  }
                }
              },
              onFailure: function () {
                loader.style.display = 'none'
                alert('<?php echo $this->__('Sorry you cannot use this payment method right now.'); ?>');
              }
            }
          );
        }
      });
    </script>
</div>

