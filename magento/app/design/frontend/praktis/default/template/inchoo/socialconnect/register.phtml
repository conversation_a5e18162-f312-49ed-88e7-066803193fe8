<?php
/**
* Inchoo
*
* NOTICE OF LICENSE
*
* This source file is subject to the Open Software License (OSL 3.0)
* that is bundled with this package in the file LICENSE.txt.
* It is also available through the world-wide-web at this URL:
* http://opensource.org/licenses/osl-3.0.php
* If you did not receive a copy of the license and are unable to
* obtain it through the world-wide-web, please send an email
* to <EMAIL> so we can send you a copy immediately.
*
* DISCLAIMER
*
* Please do not edit or add to this file if you wish to upgrade
* Magento or this extension to newer versions in the future.
** Inchoo *give their best to conform to
* "non-obtrusive, best Magento practices" style of coding.
* However,* Inchoo *guarantee functional accuracy of
* specific extension behavior. Additionally we take no responsibility
* for any possible issue(s) resulting from extension usage.
* We reserve the full right not to provide any kind of support for our free extensions.
* Thank you for your understanding.
*
* @category Inchoo
* @package SocialConnect
* <AUTHOR> <marko.mart<PERSON><PERSON>@inchoo.net>
* @copyright Copyright (c) Inchoo (http://inchoo.net/)
* @license http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
*/
?>
<div class="inchoo-socialconnect-register">   
    <div class="<?php echo $this->_getColSet(); ?>">
        <?php if($this->_googleEnabled()): ?>
        <div class="<?php echo $this->_getCol(); ?> inchoo-socialconnect-register-google">
            <div class="fieldset">
                <h2 class="legend"><?php echo $this->__('Google Connect') ?></h2>
                <p><?php echo $this->__('You can register using your Google account.') ?></p>
                <p><?php echo $this->getChildHtml('inchoo_socialconnect_checkout_google_button')?></p>
            </div>
        </div>
        <?php endif; ?>
        <?php if($this->_facebookEnabled()): ?>
        <div class="<?php echo $this->_getCol(); ?> inchoo-socialconnect-register-facebook">
            <div class="fieldset">
                <h2 class="legend"><?php echo $this->__('Facebook Connect') ?></h2>
                <p><?php echo $this->__('You can register using your Facebook account.') ?></p>
                <p><?php echo $this->getChildHtml('inchoo_socialconnect_checkout_facebook_button')?></p>
            </div>
        </div>
        <?php endif; ?>
        <?php if($this->_twitterEnabled()): ?>
        <div class="<?php echo $this->_getCol(); ?> inchoo-socialconnect-register-twitter">
            <div class="fieldset">
                <h2 class="legend"><?php echo $this->__('Twitter Connect') ?></h2>
                <p><?php echo $this->__('You can register using your Twitter account.') ?></p>
                <p><?php echo $this->getChildHtml('inchoo_socialconnect_checkout_twitter_button')?></p>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>