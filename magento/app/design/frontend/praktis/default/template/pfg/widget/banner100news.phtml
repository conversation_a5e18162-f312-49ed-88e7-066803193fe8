<?php $items = $this->getLatestNewsItemsHomepage() ?>
<?php if (count($items)): ?>

<?php $sliderUniqueId = 'productSlider_cplsd' . md5(uniqid('', true)); ?>

    <div class="banner100 banner-news" id="<?= $sliderUniqueId; ?>">
        <div class="banner-wrapper">
            <div class="banner-title">
                <p>
                    <?php echo $this->getData('title'); ?>
                    <span class="readAll">
                        <a href="<?php echo $this->getUrl(Mage::helper('clnews')->getRoute()) ?>" class="seeAll"><?php echo $this->__('See all') ?></a>
                    </span>
                </p>
            </div>
            <div class="banner-carousel news-slider owl-carousel">
                <?php foreach ($items as $item):?>
                    <div class="newsBox">
                        <?php if ($item->getImageShortContentShow() && $item->getImageShortContent()): ?>
                            <?php $imageSize = $this->getShortImageSize($item) ?>
                            <a href="<?php echo $item->getUrl($this->getCategory()) ?>" class="newsImg">
                                <img class="owl-lazy"
                                     data-src="<?php echo Mage::helper('clnews')->resizeImage(str_replace('clnews/', '', $item->getImageShortContent()), '267', '162', 'clnews'); ?>"
                                     alt="<?php echo $this->getData('title'); ?>"/>
                            </a>
                        <?php endif; ?>
                        <div class="newsBoxInfo">
                            <?php if (Mage::helper('clnews')->showDate()): ?>
                                <span class="newsDate"><?php echo Mage::helper('clnews')->formatDate($item->getNewsTime()) ?></span>
                            <?php endif ?>
                            <a class="newsBoxTitle" href="<?php echo $item->getUrl($this->getCategory()) ?>" ><?php echo $item->getTitle();?></a>
                            <div class="newsBoxText"><?php echo mb_strimwidth(strip_tags(Mage::helper('clnews')->contentFilter($item->getShortContent())), 0, 100, "...","UTF-8"); ?></div>
                            <?php if(Mage::helper('clnews')->enableLinkRoute()): ?>
                                <?php if($link = Mage::helper('clnews')->getLinkRoute()): ?>
                                    <a href="<?php echo $item->getUrl($this->getCategory()) ?>" class="readMoreLink"><?php echo $this->__('Read more') ;?></a>
                                <?php else: ?>
                                    <a href="<?php echo $item->getUrl($this->getCategory()) ?>" class="readMoreLink"><?php echo $this->__('Read more') ;?></a>
                                <?php endif; ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        jQuery(function($){
            $("#<?= $sliderUniqueId ?> .news-slider").owlCarousel({
	            lazyLoad:true,
	            loop:true,
                items: 4,
                slideBy: 1,
                nav: false,
                navText: false,
                slideSpeed: 500,
                autoplay: false,
                autoplayTimeout: 5000,
                autoplayHoverPause: true,
                addClassActive: true,
                dots: false,
                stagePadding: 0,
                responsive : {
                    0 : { items: 2 },
                    600 : { items: 3 },
                    1024 : { items: 4}
                }
            });
        });
    </script>

<?php endif ?>
