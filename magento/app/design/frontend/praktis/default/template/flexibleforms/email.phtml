<?php
$field_id	 = $this->field['field_id'];
$field_title     = $this->field['title'];
$required	 = $this->field['required'];
$field_class     = $this->field['field_class'];
$field_note	 = $this->field['field_note'];
$tooltip	 = $this->field['tooltip'];
//To retrive field value on unsuccess submission
$field_value = Mage::helper('flexibleforms');
if($required){
    $custom_message = $this->field['custom_validation'];
    $custom_class = ' required-entry-'.$this->field['field_id'];
    $required_class = (!empty($custom_message)) ? $custom_class : ' required-entry';
}

if($this->field['type']=='email'): ?>
    <div class="form-group">
        <label <?php echo $required ? ' class="required mb-0" ' : ' class="mb-0"' ?> for="options_<?php echo $field_id; ?>"><?php echo $this->__($field_title); ?> <?php echo $required ? ' <em>*</em> ' : '' ?></label>
        <?php if($tooltip): ?>
            <span id="label-info">
                <img src="<?php echo $this->getSkinUrl("css/flexibleforms/images/help.png"); ?>" alt="<?php echo $this->__('Help'); ?>" />
                <span class="label-info-tooltip"><?php echo $tooltip; ?></span>
            </span>
        <?php endif; ?>
        <div class="input-box">
            <input type="text" class="input-text form-control validate-email<?php echo $required_class ? $required_class : '' ?>" title="<?php echo $field_title; ?>" id="options_<?php echo $field_id; ?>" name="options[<?php echo $field_id; ?>]" value="<?php echo $field_value->getFieldValue($field_id);?>" />
            <?php if($field_note): ?>
                <p class="note"><?php echo $field_note; ?></p>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>