<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2020 Phoenix Medien GmbH & Co. KG (http://www.phoenix-medien.de)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

-->
<layout version="0.1.0">
    <moneybookers_processing_payment>
        <reference name="root">
            <action method="setTemplate"><template>page/1column.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="moneybookers/payment" name="moneybookers_payment" template="moneybookers/payment.phtml" />
        </reference>
    </moneybookers_processing_payment>

    <moneybookers_processing_placeform>
        <reference name="root">
            <action method="setTemplate"><template>moneybookers/blank.phtml</template></action>
        </reference>
        <reference name="content">
            <block type="moneybookers/placeform" name="moneybookers_placeform" template="moneybookers/placeform.phtml" />
        </reference>
    </moneybookers_processing_placeform>
</layout>
