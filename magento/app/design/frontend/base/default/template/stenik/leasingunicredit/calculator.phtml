<?php
/**
 * UniCredit Calculator
 *
 * @package base_default
 * <AUTHOR> Magento Team <<EMAIL>>
 *
 * @see  Stenik_LeasingUniCredit_Block_Calculator
 */
?>

<?php
    $variants = $this->getAvailableVariants();

    $isPromoVariantAvailable = false;
    foreach ($variants as $variant) {
        if ($variant->getApr() == 0) {
            $isPromoVariantAvailable = true;
            $variant->setIsPromo(true);
        }
    }

    $config = $this->getConfig();
    $uniqid = uniqid();
?>

<section class="uniCredit leaseCalculator" id="lease_calculator_<?php echo $uniqid ?>">
    <div class="title"><?php echo $this->__('Calculate your installments') ?></div>

    <div class="textInfo">
        <?php echo $this->getChildHtml('textInfo') ?>
    </div>

    <div class="principalInfo">
        <label><?php echo $this->__('Principal') ?>:</label>
        <span class="principalAmount amount">
            <?php echo Mage::helper('core')->formatPrice($this->getPrincipal()); ?>
        </span>
    </div>

    <div>
        <?php $downpaymentInputName = $config->getInputPrefix() . ($config->getDownpaymentInputName() ? $config->getDownpaymentInputName() : 'downpayment') . $config->getInputSuffix(); ?>
        <div class="form-list">
            <label for="unicredit_downpayment_<?php echo $uniqid ?>" class="downpayment-label">
                <?php echo $this->__('Downpayment') ?>
            </label>
            <input type="text"
                   name="<?php echo $downpaymentInputName ?>"
                   id="unicredit_downpayment_<?php echo $uniqid ?>"
                   class="input-text downpayment payment-unicredit-downpayment"
                   value="<?php if ($this->getDownpayment()) echo $this->getDownpayment() ?>"
                   <?php if ($config->getChooseInputFormId()): ?>form="<?php echo $config->getChooseInputFormId() ?>"<?php endif; ?>
            >
            <a class="recalc recalcBtn" href="javascript:;"><?php echo $this->__('Recalculate') ?></a>
            <i class="recalcLoader" style="display: none"></i>
        </div>
    </div>

    <?php if ($isPromoVariantAvailable): ?>
        <div class="promoOffer">
            <p>
                <?php foreach ($variants as $variant): ?>
                    <?php if ($variant->getIsPromo()): ?>
                        <?php
                            echo $this->__('Choose %s months offer with required downpayment to get NIR 0.00%% and APR 0.00%%.',
                                $variant->getMaturity()
                            );
                        ?>
                    <?php endif ?>
                <?php endforeach ?>
            </p>
        </div>
    <?php endif ?>

    <?php if (count($variants)): ?>
        <table class="variants">
            <thead>
                <tr>
                    <th><img src="<?php echo $this->getSkinUrl('images/uniCreditLogo.jpg') ?>" alt="<?php echo $this->__('UniCredit') ?>" class="bankLogo"></th>
                    <?php foreach ($variants as $variant): ?>
                        <th<?php if ($variant->getIsPromo()): ?> class="promo"<?php endif;?>>
                            <div class="months">
                                <?php
                                    $maturity = $variant->getMaturity();

                                    echo str_replace(' ', '&nbsp;',
                                        $this->__('%s month' . ($maturity == 1 ? '' : 's'), $maturity)
                                    );
                                ?>
                            </div>
                        </th>
                    <?php endforeach; ?>
                </tr>
            </thead>

            <tbody>
                <tr>
                    <td class="first"><?php echo $this->__('Installment Amount') ?></td>
                    <?php foreach ($variants as $variant): ?>
                        <td<?php if ($variant->getIsPromo()): ?> class="promo"<?php endif;?>>
                            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                                <label for="<?php echo $config->getChooseInputIdPrefix(), $variant->getId() ?>">
                            <?php endif; ?>

                            <span class="details">
                                <span class="detail installment amount">
                                    <?php echo Mage::helper('core')->formatPrice($variant->getInstallment()); ?>
                                </span>
                            </span>

                            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                                </label>
                            <?php endif; ?>
                        </td>
                    <?php endforeach; ?>
                </tr>

                <tr>
                    <td class="first"><?php echo $this->__('APR') ?></td>
                    <?php foreach ($variants as $variant): ?>
                        <td<?php if ($variant->getIsPromo()): ?> class="promo"<?php endif;?>>
                            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                                <label for="<?php echo $config->getChooseInputIdPrefix(), $variant->getId() ?>">
                            <?php endif; ?>

                            <span class="details">
                                <span class="detail apr percent">
                                    <?php echo number_format($variant->getApr(), 2, '.', '') ?>%
                                </span>
                            </span>

                            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                                </label>
                            <?php endif; ?>
                        </td>
                    <?php endforeach; ?>
                </tr>

                <tr>
                    <td class="first"><?php echo $this->__('NIR') ?></td>
                    <?php foreach ($variants as $variant): ?>
                        <td<?php if ($variant->getIsPromo()): ?> class="promo"<?php endif;?>>
                            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                                <label for="<?php echo $config->getChooseInputIdPrefix(), $variant->getId() ?>">
                            <?php endif; ?>

                            <span class="details">
                                <span class="detail nir percent">
                                    <?php echo number_format($variant->getNir(), 2, '.', '') ?>%
                                </span>
                            </span>

                            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                                </label>
                            <?php endif; ?>
                        </td>
                    <?php endforeach; ?>
                </tr>

                <tr>
                    <td class="first"><?php echo $this->__('Total Repayment Amount') ?></td>
                    <?php foreach ($variants as $variant): ?>
                        <td<?php if ($variant->getIsPromo()): ?> class="promo"<?php endif;?>>
                            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                                <label for="<?php echo $config->getChooseInputIdPrefix(), $variant->getId() ?>">
                            <?php endif; ?>

                            <span class="details">
                                <span class="detail totalRepayment amount">
                                    <?php echo Mage::helper('core')->formatPrice($variant->getTotalRepayment()) ?>
                                </span>
                            </span>

                            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                                </label>
                            <?php endif; ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
            </tbody>

            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                <tfoot>
                    <tr>
                        <td class="first">
                            <?php $variantToUseForValidationAdvice = current($variants); ?>
                            <span class="validation-advice"
                                   id="advice-validate-one-required-by-name-<?php echo $config->getChooseInputIdPrefix(), $variantToUseForValidationAdvice->getId() ?>"
                                   style="display:none"
                            >
                                <?php echo Mage::helper('core')->__('Please select one of the options.') ?>
                            </span>
                        </td>
                        <?php $chooseInputName = $config->getInputPrefix() . $config->getChooseInputName() . $config->getInputSuffix() ?>
                        <?php foreach ($variants as $variant): ?>
                            <td>
                                <input type="radio"
                                       <?php if ($config->getChooseInputFormId()): ?>form="<?php echo $config->getChooseInputFormId() ?>"<?php endif; ?>
                                       class="maturity matiruty payment-unicredit-matiruty payment-unicredit-maturity input-radio validate-one-required-by-name"
                                       name="<?php echo $chooseInputName ?>"
                                       data-downpayment="<?php echo (float) $variant->getDownpayment() ?>"
                                       id="<?php echo $config->getChooseInputIdPrefix(), $variant->getId() ?>"
                                       value="<?php echo $variant->getId() ?>"
                                >
                                <?php if ($variant->getId() != $variantToUseForValidationAdvice->getId()): ?>
                                    <span id="advice-validate-one-required-by-name-<?php echo $config->getChooseInputIdPrefix(), $variant->getId() ?>"
                                          style="display:none; position: absolute;"
                                    ></span>
                                <?php endif ?>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                </tfoot>
            <?php endif; ?>
        </table>
    <?php else: ?>
        <div class="variants">
            <ul class="messages">
                <li class="error-msg">
                    <ul>
                        <li>
                            <span><?php echo $this->__('There are no available offers with this price and downpayment.') ?></span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    <?php endif ?>

    <?php if ($config->getCanAddApplicationFormInputs()): ?>
        <div class="clearH"></div>
        <div class="application">
            <div class="title"><?php echo $this->__('Application Form') ?></div>
            <ul class="form-list">
                <li class="fields">
                    <div class="field">
                        <?php $namesInputName = $config->getInputPrefix() . ($config->getNamesInputName() ? $config->getNamesInputName() : 'names') . $config->getInputSuffix(); ?>
                        <label for="<?php echo $config->getChooseInputIdPrefix()?>names" class="required">
                            <em>*</em><?php echo $this->__('Names') ?>
                        </label>
                        <div class="input-box">
                            <input type="text"
                                   class="input-text required-entry"
                                   name="<?php echo $namesInputName ?>"
                                   id="<?php echo $config->getChooseInputIdPrefix()?>names"
                                   <?php if ($config->getChooseInputFormId()): ?>form="<?php echo $config->getChooseInputFormId() ?>"<?php endif; ?>
                            >
                        </div>
                    </div>
                    <div class="field">
                        <?php $pinInputName = $config->getInputPrefix() . ($config->getPinInputName() ? $config->getPinInputName() : 'pin') . $config->getInputSuffix(); ?>
                        <label for="<?php echo $config->getChooseInputIdPrefix()?>pin" class="required">
                            <em>*</em><?php echo $this->__('PIN') ?>
                        </label>
                        <div class="input-box">
                            <input type="text"
                                   class="input-text required-entry"
                                   name="<?php echo $pinInputName ?>"
                                   id="<?php echo $config->getChooseInputIdPrefix()?>pin"
                                   <?php if ($config->getChooseInputFormId()): ?>form="<?php echo $config->getChooseInputFormId() ?>"<?php endif; ?>
                            >
                        </div>
                    </div>
                </li>
                <li class="fields">
                    <div class="field">
                        <?php $emailInputName = $config->getInputPrefix() . ($config->getEmailInputName() ? $config->getEmailInputName() : 'email') . $config->getInputSuffix(); ?>
                        <label for="<?php echo $config->getChooseInputIdPrefix()?>email" class="required">
                            <em>*</em><?php echo $this->__('E-mail') ?>
                        </label>
                        <div class="input-box">
                            <input type="text"
                                   class="input-text required-entry validate-email"
                                   name="<?php echo $emailInputName ?>"
                                   id="<?php echo $config->getChooseInputIdPrefix()?>email"
                                   <?php if ($config->getChooseInputFormId()): ?>form="<?php echo $config->getChooseInputFormId() ?>"<?php endif; ?>
                            >
                        </div>
                    </div>
                    <div class="field">
                        <?php $phoneInputName = $config->getInputPrefix() . ($config->getPhoneInputName() ? $config->getPhoneInputName() : 'phone') . $config->getInputSuffix(); ?>
                        <label for="<?php echo $config->getChooseInputIdPrefix()?>phone" class="required">
                            <em>*</em><?php echo $this->__('Phone') ?>
                        </label>
                        <div class="input-box">
                            <input type="text"
                                   class="input-text required-entry"
                                   name="<?php echo $phoneInputName ?>"
                                   id="<?php echo $config->getChooseInputIdPrefix()?>phone"
                                   <?php if ($config->getChooseInputFormId()): ?>form="<?php echo $config->getChooseInputFormId() ?>"<?php endif; ?>
                            >
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    <?php endif; ?>

    <?php if ($config->getCanAddChooseInputs() || $config->getCanAddApplicationFormInputs()): ?>
        <p class="placeOrderHelpMessage"><?php echo $this->__('After filling out the fields please click the "Continue" button.') ?></p>
    <?php endif ?>

    <input type="hidden" name="config_json" value="<?php echo $this->escapeHtml(json_encode($config->getData())) ?>">
    <script>
        jQuery(function(){
            jQuery('.uniCredit.leaseCalculator .recalcBtn').click(function(){
                if (jQuery(this).hasClass('disabled'))
                    return false;

                $container = jQuery(this).parents('.leaseCalculator');

                jQuery(this).prop('disabled', true);
                jQuery(this).addClass('disabled');

                $container.find('.recalcLoader').show();
                jQuery.ajax('<?php echo $this->getRecalcUrl(); ?>', {
                    data: $container.find('input,textarea').serialize()
                }).done(function(html) {
                    $container.replaceWith(html);
                    $container.find('input,textarea').change();
                }).always(function(){
                    $container.find('.recalcLoader').hide();
                });

                return false;
            });

            <?php if ($config->getCanAddChooseInputs() && $config->getChooseInputName()): ?>
                jQuery('.leaseCalculator input.downpayment').change(function(){
                    var $maturityInputs = jQuery(this).parents('.leaseCalculator').find('input.matiruty');
                    $maturityInputs.attr('checked', false);
                    $maturityInputs.prop('checked', false);
                });

                jQuery('.leaseCalculator input.matiruty').change(function(){
                    var $downpaymentInput = jQuery(this).parents('.leaseCalculator').find('input.downpayment');
                    var maturityDownpayment = jQuery(this).data('downpayment');
                    if ($downpaymentInput.val() != maturityDownpayment) {
                        $downpaymentInput.val(maturityDownpayment ? maturityDownpayment : null).focus();
                    }
                });
            <?php endif; ?>
        });
    </script>
</section>
