<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
/**
 * @see Mage_Catalog_Block_Product_View
 */
$_product = $this->getProduct();
$_tierPrices = $this->getTierPrices();
$_finalPriceInclTax = $this->helper('tax')->getPrice($_product, $_product->getFinalPrice(), true);

/** @var $_catalogHelper Mage_Catalog_Helper_Data */
$_catalogHelper = Mage::helper('catalog');

$_weeeTaxAmount = Mage::helper('weee')->getAmountForDisplay($_product);
if (Mage::helper('weee')->typeOfDisplay($_product, array(1,2,4))) {
    $_weeeTaxAttributes = Mage::helper('weee')->getProductWeeeAttributesForDisplay($_product);
}

?>
<?php if (count($_tierPrices) > 0): ?>
    <ul class="<?php echo ($this->getInGrouped() ? 'tier-prices-grouped product-pricing-grouped' : 'tier-prices product-pricing'); ?>">
    <?php if ($this->getInGrouped()): ?>
        <?php $_tierPrices = $this->getTierPrices($_product); ?>
    <?php endif; ?>
    <?php Mage::helper('weee')->processTierPrices($_product, $_tierPrices); ?>
    <?php foreach ($_tierPrices as $_index => $_price): ?>
        <li class="tier-price tier-<?php echo $_index; ?>">
        <?php if ($_catalogHelper->canApplyMsrp($_product)): ?>
            <?php if ($this->getInGrouped()): ?>
                <?php echo $this->__('Buy %1$s for', $_price['price_qty']) ?>:
            <?php else: ?>
                <?php echo $this->__('Buy %1$s', $_price['price_qty']) ?>
            <?php endif; ?>
        <?php else: ?>

        <?php if ($this->helper('tax')->displayBothPrices()): ?>
            <?php if (Mage::helper('weee')->typeOfDisplay($_product, 0)): ?>
                <?php echo $this->__('Buy %1$s for %2$s (%3$s incl. tax) each', $_price['price_qty'], $_price['formated_price_incl_weee_only'], $_price['formated_price_incl_weee']) ?>
            <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 1)): ?>
                    <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price_incl_weee_only']); ?>
                    <?php if ($_weeeTaxAttributes): ?>
                    (<small>
                    <?php echo $this->__('%1$s incl tax.', $_price['formated_price_incl_weee']); ?>
                    <?php $separator = ' + '; foreach ($_weeeTaxAttributes as $_attribute): ?>
                        <?php echo $separator; ?>
                        <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()); ?>
                    <?php endforeach; ?>
                    </small>)
                    <?php endif; ?>
                    <?php echo $this->__('each') ?>
            <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 4)): ?>
                    <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price_incl_weee_only']); ?>
                    <?php if ($_weeeTaxAttributes): ?>
                    (<small>
                    <?php echo $this->__('%1$s incl tax.', $_price['formated_price_incl_weee']); ?>
                    <?php $separator = ' + '; foreach ($_weeeTaxAttributes as $_attribute): ?>
                        <?php echo $separator; ?>
                        <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()+$_attribute->getTaxAmount()); ?>
                    <?php endforeach; ?>
                    </small>)
                    <?php endif; ?>
                    <?php echo $this->__('each') ?>
            <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 2)): ?>
                    <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price']); ?>
                    <?php if ($_weeeTaxAttributes): ?>
                    (<small>
                    <?php foreach ($_weeeTaxAttributes as $_attribute): ?>
                        <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()); ?>
                    <?php endforeach; ?>
                    <?php echo $this->__('Total incl. Tax: %1$s', $_price['formated_price_incl_weee']); ?>
                    </small>)
                    <?php endif; ?>
                    <?php echo $this->__('each') ?>
            <?php else: ?>
                    <?php echo $this->__('Buy %1$s for %2$s (%3$s incl. tax) each', $_price['price_qty'], $_price['formated_price'], $_price['formated_price_incl_tax']) ?>
            <?php endif; ?>
        <?php else: ?>
            <?php if ($this->helper('tax')->displayPriceIncludingTax()): ?>
                <?php if (Mage::helper('weee')->typeOfDisplay($_product, 0)): ?>
                        <?php echo $this->__('Buy %1$s for %2$s each', $_price['price_qty'], $_price['formated_price_incl_weee']) ?>
                <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 1)): ?>
                        <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price_incl_weee']); ?>
                        <?php if ($_weeeTaxAttributes): ?>
                        (<small>
                        <?php $separator = ''; foreach ($_weeeTaxAttributes as $_attribute): ?>
                            <?php echo $separator; ?>
                            <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()); ?>
                        <?php $separator = ' + '; endforeach; ?>
                        </small>)
                        <?php endif; ?>
                        <?php echo $this->__('each') ?>
                <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 4)): ?>
                        <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price_incl_weee']); ?>
                        <?php if ($_weeeTaxAttributes): ?>
                        (<small>
                        <?php $separator = ''; foreach ($_weeeTaxAttributes as $_attribute): ?>
                            <?php echo $separator; ?>
                            <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()+$_attribute->getTaxAmount()); ?>
                        <?php $separator = ' + '; endforeach; ?>
                        </small>)
                        <?php endif; ?>
                        <?php echo $this->__('each') ?>
                <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 2)): ?>
                        <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price_incl_tax']); ?>
                        <?php if ($_weeeTaxAttributes): ?>
                        (<small>
                        <?php foreach ($_weeeTaxAttributes as $_attribute): ?>
                            <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()); ?>
                        <?php endforeach; ?>
                        <?php echo $this->__('Total incl. Tax: %1$s', $_price['formated_price_incl_weee']); ?>
                        </small>)
                        <?php endif; ?>
                        <?php echo $this->__('each') ?>
                <?php else: ?>
                        <?php echo $this->__('Buy %1$s for %2$s each', $_price['price_qty'], $_price['formated_price_incl_tax']) ?>
                <?php endif; ?>
            <?php else: ?>
                <?php if (Mage::helper('weee')->typeOfDisplay($_product, 0)): ?>
                        <?php echo $this->__('Buy %1$s for %2$s each', $_price['price_qty'], $_price['formated_price_incl_weee_only']) ?>
                <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 1)): ?>
                        <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price_incl_weee_only']); ?>
                        <?php if ($_weeeTaxAttributes): ?>
                        (<small>
                        <?php $separator = ''; foreach ($_weeeTaxAttributes as $_attribute): ?>
                            <?php echo $separator; ?>
                            <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()); ?>
                        <?php $separator = ' + '; endforeach; ?>
                        </small>)
                        <?php endif; ?>
                        <?php echo $this->__('each') ?>
                <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 4)): ?>
                        <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price_incl_weee_only']); ?>
                        <?php if ($_weeeTaxAttributes): ?>
                        (<small>
                        <?php $separator = ''; foreach ($_weeeTaxAttributes as $_attribute): ?>
                            <?php echo $separator; ?>
                            <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()+$_attribute->getTaxAmount()); ?>
                        <?php $separator = ' + '; endforeach; ?>
                        </small>)
                        <?php endif; ?>
                        <?php echo $this->__('each') ?>
                <?php elseif(Mage::helper('weee')->typeOfDisplay($_product, 2)): ?>
                        <?php echo $this->__('Buy %1$s for %2$s', $_price['price_qty'], $_price['formated_price']); ?>
                        <?php if ($_weeeTaxAttributes): ?>
                        (<small>
                        <?php foreach ($_weeeTaxAttributes as $_attribute): ?>
                            <?php echo $_attribute->getName(); ?>: <?php echo Mage::helper('core')->currency($_attribute->getAmount()); ?>
                        <?php endforeach; ?>
                        <?php echo $this->__('Total incl. Tax: %1$s', $_price['formated_price_incl_weee_only']); ?>
                        </small>)
                        <?php endif; ?>
                        <?php echo $this->__('each') ?>
                <?php else: ?>
                        <?php echo $this->__('Buy %1$s for %2$s each', $_price['price_qty'], $_price['formated_price']) ?>
                <?php endif; ?>
            <?php endif; ?>
        <?php endif; ?>

        <?php endif; // Can apply MSRP ?>

        <?php if (!$this->getInGrouped()): ?>
            <?php if(($_product->getPrice() == $_product->getFinalPrice() && $_product->getPrice() > $_price['price'])
            || ($_product->getPrice() != $_product->getFinalPrice() &&  $_product->getFinalPrice() > $_price['price'])): ?>
                <?php echo $this->__('and') ?>&nbsp;<strong class="benefit"><?php echo $this->__('save')?>&nbsp;<span class="percent tier-<?php echo $_index;?>"><?php echo $_price['savePercent']?></span>%
            <?php endif ?></strong>
        <?php endif; ?>

        <?php if ($_catalogHelper->isShowPriceOnGesture($_product)):?>
            <?php $popupId = 'msrp-popup-' . $_product->getId() . $this->helper('core')->getRandomString(20); ?>
            <a href="#" id="<?php echo($popupId);?>"><?php echo $this->__('Click for price'); ?></a>
            <script type="text/javascript">
            <?php
                    $addToCartUrl = $this->getProduct()->isSalable()
                        ? $this->getAddToCartUrlCustom($_product, array('qty' => $_price['price_qty']), false)
                        : '';
            ?>
            <?php if (!$this->getInGrouped()): ?>
                var newLink = {
                    url: "<?php echo $addToCartUrl; ?>",
                    qty: "<?php echo $_price['price_qty']?>"
                };
            <?php else: ?>
                var newLink = {
                    url: "<?php echo $addToCartUrl; ?>",
                    notUseForm: true
                };
            <?php endif ?>
                Catalog.Map.addHelpLink(
                    $('<?php echo $popupId ?>'),
                    "<?php echo $_product->getName() ?>",
                    <?php echo json_encode($_price['real_price_html']) ?>,
                    "<?php echo $this->helper('core')->currency($_product->getMsrp(),true,false) ?>",
                    newLink
                );
            </script>
        <?php else: ?>
            <span class="msrp-price-hide-message">
                <?php echo $_catalogHelper->getMsrpPriceMessage($_product) ?>
            </span>
        <?php endif; ?>


        </li>

    <?php endforeach ?>
    </ul>
<?php endif;?>
