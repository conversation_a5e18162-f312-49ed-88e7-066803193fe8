<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     base_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Shopping cart template
 *
 * @see Mage_Checkout_Block_Cart
 */
?>
<div class="cart">
    <div class="page-title title-buttons">
        <h1><?php echo $this->__('Shopping Cart') ?></h1>
        <?php if(!$this->hasError()): ?>
        <ul class="checkout-types">
        <?php foreach ($this->getMethods('top_methods') as $method): ?>
            <?php if ($methodHtml = $this->getMethodHtml($method)): ?>
            <li><?php echo $methodHtml; ?></li>
            <?php endif; ?>
        <?php endforeach; ?>
        </ul>
        <?php endif; ?>
    </div>
    <?php echo $this->getMessagesBlock()->toHtml() ?>
    <?php echo $this->getChildHtml('form_before') ?>
    <form action="<?php echo $this->getFormActionUrl() ?>" method="post">
        <?php echo $this->getBlockHtml('formkey'); ?>
        <fieldset>
            <table id="shopping-cart-table" class="data-table cart-table">
                <col width="1" />
                <col />
                <col width="1" />
            <?php if ($this->helper('wishlist')->isAllowInCart()) : ?>
                <col width="1" />
            <?php endif ?>
            <?php if ($this->helper('tax')->displayCartPriceExclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
                <col width="1" />
            <?php endif; ?>
            <?php if ($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
                <col width="1" />
            <?php endif; ?>
                <col width="1" />
            <?php if ($this->helper('tax')->displayCartPriceExclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
                <col width="1" />
            <?php endif; ?>
            <?php if ($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
                <col width="1" />
            <?php endif; ?>
                <col width="1" />

            <?php $mergedCells = ($this->helper('tax')->displayCartBothPrices() ? 2 : 1); ?>
                <thead>
                    <tr>
                        <th rowspan="<?php echo $mergedCells; ?>">&nbsp;</th>
                        <th rowspan="<?php echo $mergedCells; ?>"><span class="nobr"><?php echo $this->__('Product Name') ?></span></th>
                        <th rowspan="<?php echo $mergedCells; ?>"></th>
                        <?php if ($this->helper('wishlist')->isAllowInCart()) : ?>
                        <th rowspan="<?php echo $mergedCells; ?>" class="a-center"><span class="nobr"><?php echo $this->__('Move to Wishlist') ?></span></th>
                        <?php endif ?>
                        <th class="a-center" colspan="<?php echo $mergedCells; ?>"><span class="nobr"><?php echo $this->__('Unit Price') ?></span></th>
                        <th rowspan="<?php echo $mergedCells; ?>" class="a-center"><?php echo $this->__('Qty') ?></th>
                        <th class="a-center" colspan="<?php echo $mergedCells; ?>"><?php echo $this->__('Subtotal') ?></th>
                        <th rowspan="<?php echo $mergedCells; ?>" class="a-center">&nbsp;</th>
                    </tr>
                    <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
                    <tr>
                        <th class="a-right"><?php echo $this->helper('tax')->getIncExcTaxLabel(false) ?></th>
                        <th><?php echo $this->helper('tax')->getIncExcTaxLabel(true) ?></th>
                        <th class="a-right"><?php echo $this->helper('tax')->getIncExcTaxLabel(false) ?></th>
                        <th><?php echo $this->helper('tax')->getIncExcTaxLabel(true) ?></th>
                    </tr>
                    <?php endif; ?>
                </thead>
                <tfoot>
                    <tr>
                        <td colspan="50" class="a-right">
                            <?php if($this->getContinueShoppingUrl()): ?>
                                <button type="button" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Continue Shopping')) ?>" class="button btn-continue" onclick="setLocation('<?php echo Mage::helper('core')->quoteEscape($this->getContinueShoppingUrl()) ?>')"><span><span><?php echo $this->__('Continue Shopping') ?></span></span></button>
                            <?php endif; ?>
                            <button type="submit" name="update_cart_action"  value="update_qty" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Update Shopping Cart')); ?>" class="button btn-update"><span><span><?php echo $this->__('Update Shopping Cart'); ?></span></span></button>
                            <button type="submit" name="update_cart_action" value="empty_cart" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Clear Shopping Cart')); ?>" class="button btn-empty" id="empty_cart_button"><span><span><?php echo $this->__('Clear Shopping Cart'); ?></span></span></button>
                            <!--[if lt IE 8]>
                            <input type="hidden" id="update_cart_action_container" />
                            <script type="text/javascript">
                            //<![CDATA[
                                Event.observe(window, 'load', function()
                                {
                                    // Internet Explorer (lt 8) does not support value attribute in button elements
                                    $emptyCartButton = $('empty_cart_button');
                                    $cartActionContainer = $('update_cart_action_container');
                                    if ($emptyCartButton && $cartActionContainer) {
                                        Event.observe($emptyCartButton, 'click', function()
                                        {
                                            $emptyCartButton.setAttribute('name', 'update_cart_action_temp');
                                            $cartActionContainer.setAttribute('name', 'update_cart_action');
                                            $cartActionContainer.setValue('empty_cart');
                                        });
                                    }

                                });
                            //]]>
                            </script>
                            <![endif]-->
                        </td>
                    </tr>
                </tfoot>
                <tbody>
                <?php foreach($this->getItems() as $_item): ?>
                    <?php echo $this->getItemHtml($_item) ?>
                <?php endforeach ?>
                </tbody>
            </table>
            <script type="text/javascript">decorateTable('shopping-cart-table')</script>
        </fieldset>
    </form>
    <div class="cart-collaterals">
        <div class="col2-set">
            <div class="col-1">
                <?php echo $this->getChildHtml('crosssell') ?>
            </div>
            <div class="col-2">
                <?php /* Extensions placeholder */ ?>
                <?php echo $this->getChildHtml('checkout.cart.extra') ?>
                <?php echo $this->getChildHtml('coupon') ?>
                <?php if (!$this->getIsVirtual()): echo $this->getChildHtml('shipping'); endif; ?>
            </div>
        </div>
        <div class="totals">
            <?php echo $this->getChildHtml('totals'); ?>
            <?php if(!$this->hasError()): ?>
            <ul class="checkout-types">
            <?php foreach ($this->getMethods('methods') as $method): ?>
                <?php if ($methodHtml = $this->getMethodHtml($method)): ?>
                <li><?php echo $methodHtml; ?></li>
                <?php endif; ?>
            <?php endforeach; ?>
            </ul>
            <?php endif; ?>
        </div>
    </div>
</div>
