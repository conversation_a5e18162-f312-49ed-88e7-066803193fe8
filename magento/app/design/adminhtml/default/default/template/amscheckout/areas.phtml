<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2017 Amasty (https://www.amasty.com)
 * @package Amasty_Scheckout
 */
    $areas = $this->getAreas();
    $layoutTypes = $this->getLayoutTypes();
    
    print $this->getLayout()->createBlock("adminhtml/store_switcher")->toHtml();
?>

<div id="product_info_tabs_group_7_content" style="">
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-edit-form fieldset-legend"><?php
                echo $this->__("Specify checkout block names and choose layout")
            ?></h4>
        </div>
        <div class="fieldset fieldset-wide" id="group_fields7">
            <div class="hor-scroll">
                <table cellspacing="0" class="form-list">
                    <?php
                        foreach($areas as $order => $area){
                            $areaId = $area['area_id'];
                    ?>

                        <tr area_id="<?php print $areaId;?>" id="area_content">
                            <td class="label">
                                <?php
                                    print $this->__($this->htmlEscape($area['default_area_label']));
                                ?>
                            </td>
                            <td class="value">
                                <input class="input-text" type="text" id="area_label" value="<?php
                                    print $this->htmlEscape($area['area_label']);
                                ?>"/>
                            </td>
                            <td class="value use-default">
                                <?php
                                    if ($this->getData("store_id") != NULL){
                                ?>
                                    <input type="checkbox" rel="area_use_default" id="area_use_default_<?php print $areaId;?>" value="1" <?php print $area["area_store_id"] == NULL ? 'checked' : '';?>/>
                                    <label for="area_use_default_<?php print $areaId;?>"><?php print Mage::helper('amscheckout')->__('Use Default Value');?></label>
                                <?php
                                    }
                                ?>
                            </td>
                        </tr>



                    <?php
                        }
                    ?>
                    <tr>
                        <td class="label">
                            <label for="layout_mode"><?php print Mage::helper('amscheckout')->__('Layout');?><label/>
                        </td>
                        <td class="value">
                            <select name="layoutMode" >
                                <?php
                                    foreach($layoutTypes as $layoutType){
                                ?>                                
                                    <option <?php print ($layoutType['active'] ? 'selected' : '')?> value="<?php print $layoutType['name']?>" id="layout_mode_<?php print $layoutType['name']?>"><?php print $layoutType['value']?></option>
                                <?php
                                    }
                                ?>
                            </select>
                        </td>
                        <td class="value use-default">
                            
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
</div>



