<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">
//<![CDATA[
Event.observe(window, 'load', recalculateTax);
Event.observe(window, 'load', registerTaxRecalcs);

function registerTaxRecalcs()
{
    if (typeof dynamicTaxes == 'undefined') {
        return;
    }

    for (var i=0; i<dynamicTaxes.length; i++) {
        Event.observe($(dynamicTaxes[i]), 'change', recalculateTax);
    }
    Event.observe($('tax_class_id'), 'change', recalculateTax);
}

var priceFormat = <?php echo $this->helper('tax')->getPriceFormat($this->getStore()); ?>;
var taxRates = <?php echo $this->helper('tax')->getAllRatesByProductClass($this->getStore()); ?>;

function recalculateTax()
{
    if (typeof dynamicTaxes == 'undefined') {
        return;
    }

    for (var i=0; i<dynamicTaxes.length; i++) {
        var code = dynamicTaxes[i];
        var span = $('dynamic-tax-' + code);
        var input = $(code);

        if (!input.value) {
            span.innerHTML = '';
            continue;
        }

        var rate = 0;
        var taxClass = $('tax_class_id').options[$('tax_class_id').selectedIndex].value;
        eval('var value = taxRates.value_' + taxClass);

        if (value != undefined) {
            rate = value;
        }

        var spanValue = '';
        if (rate != 0) {
            spanValue = ' ' + formatCurrency(input.value/(100+rate)*rate, priceFormat);
        }
        span.innerHTML = spanValue;
    }
}

// Bind tab changes
function bindActiveProductTab(event) {
    if(event.tab && event.tab.name && $('store_switcher')){
        $('store_switcher').switchParams = 'active_tab/'+event.tab.name+'/';
    }
}
varienGlobalEvents.attachEventHandler('showTab', bindActiveProductTab);

// bind active tab
<?php if($tabsBlock = $this->getLayout()->getBlock('product_tabs')): ?>
if(<?php echo $tabsBlock->getJsObjectName() ?> && <?php echo $tabsBlock->getJsObjectName() ?>.activeTab && $('store_switcher')){
    $('store_switcher').switchParams = 'active_tab/'+<?php echo $tabsBlock->getJsObjectName() ?>.activeTab.name+'/';
}
<?php endif; ?>
//]]>
</script>
