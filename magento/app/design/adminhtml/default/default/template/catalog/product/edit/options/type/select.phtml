<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<script type="text/javascript">
//<![CDATA[
OptionTemplateSelect = '<table class="border" cellpadding="0" cellspacing="0">'+
        '<input type="hidden" class="required-option-select-type-rows" name="validation_{{option_id}}_result" value="" >'+
        '<thead>'+
        '<tr class="headings">'+
            '<th class="type-title"><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('Title')) ?> <span class="required">*</span></th>'+
            <?php if ($this->getCanReadPrice() !== false) : ?>
            '<th class="type-price"><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('Price')) ?></th>' +
            '<th class="type-type"><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('Price Type')) ?></th>' +
            <?php endif; ?>
            '<th class="type-sku"><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('SKU')) ?></th>'+
            '<th class="type-order"><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('Sort Order')) ?></th>'+
            '<th class="type-butt last">&nbsp;</th>'+
        '</tr>'+
        '</thead>'+
        '<tbody id="select_option_type_row_{{option_id}}">'+
        '</tbody>'+
        '<tfoot>'+
        '<tr>'+
        '<td colspan="100" class="a-right"><?php echo $this->getAddButtonHtml() ?></td>'+
        '</tr>'+
        '</tfoot>'+
    '</table>';

OptionTemplateSelectRow = '<tr id="product_option_{{id}}_select_{{select_id}}">'+
            '<td>'+
            '<input type="hidden" name="product[options][{{id}}][values][{{select_id}}][option_type_id]" value="{{option_type_id}}">'+
            '<input type="hidden" id="product_option_{{id}}_select_{{select_id}}_is_delete" name="product[options][{{id}}][values][{{select_id}}][is_delete]" value="">'+
            '<input type="text" class="required-entry input-text select-type-title" id="product_option_{{id}}_select_{{select_id}}_title" name="product[options][{{id}}][values][{{select_id}}][title]" value="{{title}}">{{checkboxScopeTitle}}</td>'+
            <?php if ($this->getCanReadPrice() !== false) : ?>
            '<td><input type="text" class="input-text validate-number product-option-price" id="product_option_{{id}}_select_{{select_id}}_price" name="product[options][{{id}}][values][{{select_id}}][price]" value="{{price}}"<?php if ($this->getCanEditPrice() === false) : ?> disabled="disabled"<?php endif; ?>></td>' +
            '<td><?php echo $this->getPriceTypeSelectHtml() ?>{{checkboxScopePrice}}</td>' +
            <?php else : ?>
            '<input type="hidden" id="product_option_{{id}}_select_{{select_id}}_price" name="product[options][{{id}}][values][{{select_id}}][price]">' +
            '<input type="hidden" name="product[options][{{id}}][values][{{select_id}}][price_type]" id="product_option_{{id}}_select_{{select_id}}_price_type">' +
            <?php endif; ?>
            '<td><input type="text" class="input-text" name="product[options][{{id}}][values][{{select_id}}][sku]" value="{{sku}}"></td>'+
            '<td><input type="text" class="validate-zero-or-greater input-text" name="product[options][{{id}}][values][{{select_id}}][sort_order]" value="{{sort_order}}"></td>'+
            '<td class="last"><span title="Delete row"><?php echo $this->getDeleteButtonHtml() ?></span></td>'+
        '</tr>';

selectOptionType = {
    div : 'select_option_type_row',
    itemCount : 0,
    templateSyntax : /(^|.|\r|\n)({{(\w+)}})/,
    templateText : OptionTemplateSelectRow,
    add : function(data) {

        this.template = new Template(this.templateText, this.templateSyntax);

        if (data.target || data.srcElement) {//data is Event (work in IE and Firefox)
            element = $(Event.findElement(data, 'button'));
            optionId = element.readAttribute('id').sub('add_select_row_button_', '');
            data = {};
            data.option_type_id = '-1';
            data.select_id = this.itemCount;
        } else {
            optionId = data.option_id;
            data.select_id = data.option_type_id;
            this.itemCount = data.item_count;
        }

        data.id  = optionId;

        Element.insert($(this.div+'_'+data.id), {'bottom':this.template.evaluate(data)});

        if (data.checkboxScopeTitle) {
            //set disabled
            if ($('product_option_'+data.id+'_select_'+data.select_id+'_title') && data.scopeTitleDisabled) {
                $('product_option_'+data.id+'_select_'+data.select_id+'_title').disable();
            }
        }
        if (data.checkboxScopePrice) {
            //set disabled
            if ($('product_option_'+data.id+'_select_'+data.select_id+'_price') && data.scopePriceDisabled) {
                $('product_option_'+data.id+'_select_'+data.select_id+'_price').disable();
                $('product_option_'+data.id+'_select_'+data.select_id+'_price_type').disable();
            }
        }

        if (data.price_type) {
            $A($('product_option_'+data.id+'_select_'+data.select_id+'_price_type').options).each(function(option){
                if (option.value==data.price_type) option.selected = true;
            });
        }

        this.itemCount++;
        this.bindRemoveButtons();
    },
    remove : function(event){
        var element = $(Event.findElement(event, 'tr'));

        if(element){
            $(element.readAttribute('id')+'_is_delete').value = '1';
            element.addClassName('no-display');
            element.addClassName('ignore-validate');
            element.hide();
        }
    },
    bindRemoveButtons : function(){
        var buttons = $$('.delete-select-row');
        for(var i=0;i<buttons.length;i++){
            if(!$(buttons[i]).binded){
                $(buttons[i]).binded = true;
                Event.observe(buttons[i], 'click', this.remove.bind(this));
            }
        }
    },
    bindAddButton : function()
    {
        var buttons = $$('.add-select-row');
        for(var i=0;i<buttons.length;i++){
            if(!$(buttons[i]).binded){
                $(buttons[i]).binded = true;
                Event.observe(buttons[i], 'click', this.add.bind(this));
            }
        }
    }
}

if ($('option_panel_type_select')) {
    $('option_panel_type_select').remove();
}

selectOptionType.bindRemoveButtons();

Validation.addAllThese([
    ['required-option-select-type-rows', <?php echo $this->helper('core')->jsonEncode(Mage::helper('catalog')->__('Please add rows to option.')); ?>, function(v, elm) {
            var optionContainerElm = elm.up('div.grid');
            var selectTypesFlag = false;
            selectTypeElements = $$('#'+optionContainerElm.id+' .select-type-title');
            selectTypeElements.each(function(elm){
                if (elm.id && elm.up('tr').visible()) {
                    selectTypesFlag = true;
                }
            });
            elm.advaiceContainer = optionContainerElm.id+'_advice';
        return selectTypesFlag;
}]]);

if($('add_select_row_button')){
    Event.observe('add_select_row_button', 'click', selectOptionType.add.bind(selectOptionType));
}
//]]>
</script>
