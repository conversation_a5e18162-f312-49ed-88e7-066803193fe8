<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php echo $this->getTemplatesHtml() ?>

<script type="text/javascript">
//<![CDATA[
var firstStepTemplate = '<div class="option-box" id="option_{{id}}">'+
            '<table id="<?php echo $this->getFieldId() ?>_{{id}}" class="option-header" cellpadding="0" cellspacing="0">'+
            '<input type="hidden" id="<?php echo $this->getFieldId() ?>_{{id}}_is_delete" name="<?php echo $this->getFieldName() ?>[{{id}}][is_delete]" value="" />'+
            '<input type="hidden" id="<?php echo $this->getFieldId() ?>_{{id}}_previous_type" name="<?php echo $this->getFieldName() ?>[{{id}}][previous_type]" value="{{type}}" />'+
            '<input type="hidden" id="<?php echo $this->getFieldId() ?>_{{id}}_previous_group" name="<?php echo $this->getFieldName() ?>[{{id}}][previous_group]" value="" />'+
            '<input type="hidden" id="<?php echo $this->getFieldId() ?>_{{id}}_id" name="<?php echo $this->getFieldName() ?>[{{id}}][id]" value="{{id}}" />'+
            '<input type="hidden" id="<?php echo $this->getFieldId() ?>_{{id}}_option_id" name="<?php echo $this->getFieldName() ?>[{{id}}][option_id]" value="{{option_id}}" />'+
            '<thead>'+
                '<tr>'+
                    '<th class="opt-title"><?php echo $this->jsQuoteEscape(Mage::helper('catalog')->__('Title')) ?> <span class="required">*</span></th>'+
                    '<th class="opt-type"><?php echo $this->jsQuoteEscape(Mage::helper('catalog')->__('Input Type')) ?> <span class="required">*</span></th>'+
                    '<th class="opt-req"><?php echo $this->jsQuoteEscape(Mage::helper('catalog')->__('Is Required')) ?></th>'+
                    '<th class="opt-order"><?php echo $this->jsQuoteEscape(Mage::helper('catalog')->__('Sort Order')) ?></th>'+
                    '<th class="a-right"><?php echo $this->jsQuoteEscape($this->jsQuoteEscape($this->getDeleteButtonHtml())) ?></th>'+
                '</tr>'+
            '</thead>'+
            '<tr>'+
                '<td><input type="text" class="required-entry input-text" id="<?php echo $this->getFieldId() ?>_{{id}}_title" name="<?php echo $this->getFieldName() ?>[{{id}}][title]" value="{{title}}">{{checkboxScopeTitle}}</td>'+
                '<td><?php echo $this->getTypeSelectHtml() ?></td>'+
                '<td class="opt-req"><?php echo $this->getRequireSelectHtml() ?></td>'+
                '<td><input type="text" class="validate-zero-or-greater input-text" name="<?php echo $this->getFieldName() ?>[{{id}}][sort_order]" value="{{sort_order}}"></td>'+
                '<td>&nbsp;</td>'+
            '</tr></table></div>';

var productOption = {
    div : $('product_options_container_top'),
    templateSyntax : /(^|.|\r|\n)({{(\w+)}})/,
    templateText : firstStepTemplate,
    itemCount : 1,
    add : function(data) {
        this.template = new Template(this.templateText, this.templateSyntax);

        if(!data.id){
            data = {};
            data.id  = this.itemCount;
            data.type = '';
            data.option_id = 0;
        } else {
            this.itemCount = data.item_count;
        }

        Element.insert(this.div, {'after':this.template.evaluate(data)});

        //set selected type
        if (data.type) {
            $A($('<?php echo $this->getFieldId() ?>_'+data.id+'_type').options).each(function(option){
                if (option.value==data.type) option.selected = true;
            });
        }

        //set selected is_require
        if (data.is_require) {
            $A($('<?php echo $this->getFieldId() ?>_'+data.id+'_is_require').options).each(function(option){
                if (option.value==data.is_require) option.selected = true;
            });
        }

        if (data.checkboxScopeTitle) {
            //set disabled
            if ($('<?php echo $this->getFieldId() ?>_'+data.option_id+'_title') && data.scopeTitleDisabled) {
                $('<?php echo $this->getFieldId() ?>_'+data.option_id+'_title').disable();
            }
        }

        this.itemCount++;
        this.bindRemoveButtons();
        productOptionType.bindSelectInputType();
    },
    remove : function(event){
        var element = $(Event.findElement(event, 'div'));
        if(element){
            $('product_'+element.readAttribute('id')+'_'+'is_delete').value = '1';
            element.addClassName('no-display');
            element.addClassName('ignore-validate');
            element.hide();
        }
    },
    bindRemoveButtons : function(){
        var buttons = $$('div.product-custom-options .delete-product-option');
        for(var i=0;i<buttons.length;i++){
            if(!$(buttons[i]).binded){
                $(buttons[i]).binded = true;
                Event.observe(buttons[i], 'click', this.remove.bind(this));
            }
        }
        var inputs = $$('div.product-custom-options button', 'div.product-custom-options input', 'div.product-custom-options select', 'div.product-custom-options textarea');
        <?php if ($this->isReadonly()):?>
        for (var i=0, l = inputs.length; i < l; i ++) {
            inputs[i].disabled = true;
            if (inputs[i].tagName.toLowerCase()=='button') {
                inputs[i].addClassName('disabled');
            }
        }
        <?php else: ?>
            inputs.each(function(el) { Event.observe(el, 'change', el.setHasChanges.bind(el));  } )
        <?php endif;?>
    }
}


var productOptionType = {
    templateSyntax : /(^|.|\r|\n)({{(\w+)}})/,
    loadStepTwo : function(event){
        var element = $(Event.findElement(event, 'select'));

        var group = '';
        var previousGroupElm = $(element.readAttribute('id').sub('_type', '_previous_group'));

        switch(element.getValue()){
            case 'field':
            case 'area':
                template = OptionTemplateText;
                group = 'text';
                break;
            case 'file':
                template = OptionTemplateFile;
                group = 'file';
                break;
            case 'drop_down':
            case 'radio':
            case 'checkbox':
            case 'multiple':
                template = OptionTemplateSelect;
                group = 'select';
                break;
            case 'date':
            case 'date_time':
            case 'time':
                template = OptionTemplateDate;
                group = 'date';
                break;
            default:
                template = '';
                group = 'unknown';
                break;
        }

        if (previousGroupElm.getValue() != group) {
            if ($(element.readAttribute('id')+'_'+previousGroupElm.getValue())) {
                formElm = $(element.readAttribute('id')+'_'+previousGroupElm.getValue()).descendants();
                formElm.each(function(elm){
                    if (elm.tagName == 'input' || elm.tagName == 'select') {
                        elm.name = '__delete__'+elm.readAttribute('name');
                    }
                });

                $(element.readAttribute('id')+'_'+previousGroupElm.getValue()).addClassName('no-display');
                $(element.readAttribute('id')+'_'+previousGroupElm.getValue()).addClassName('ignore-validate');
                $(element.readAttribute('id')+'_'+previousGroupElm.getValue()).hide();
            }

            previousGroupElm.value = group;

            if ($(element.readAttribute('id')+'_'+group)) {
                formElm = $(element.readAttribute('id')+'_'+group).descendants();
                formElm.each(function(elm){
                    if (elm.match('input') || elm.match('select')) {
                        elm.name = elm.readAttribute('name').sub('__delete__', '');
                    }
                });
                $(element.readAttribute('id')+'_'+group).removeClassName('no-display');
                $(element.readAttribute('id')+'_'+group).removeClassName('ignore-validate');
                $(element.readAttribute('id')+'_'+group).show();

            } else {
                template = '<div id="'+element.readAttribute('id')+'_'+group+'" class="grid tier form-list">'+template+'</div><div id="'+element.readAttribute('id')+'_'+group+'_advice"></div';
                this.secondTemplate = new Template(template, this.templateSyntax);

                data = {};
                if (!data.option_id) {
                    data = {};
                    data.option_id = $(element.readAttribute('id').sub('_type', '_id')).getValue();
                }

                Element.insert(element.readAttribute('id').sub('_type', ''), {'after':this.secondTemplate.evaluate(data)});

                switch(element.getValue()){
                    case 'drop_down':
                    case 'radio':
                    case 'checkbox':
                    case 'multiple':
                        selectOptionType.bindAddButton();
                        break;
                }
            }
        }
    },
    addDataToValues : function(data){

        switch(data.type){
            case 'field':
            case 'area':
                template = OptionTemplateText;
                group = 'text';
                break;
            case 'file':
                template = OptionTemplateFile;
                group = 'file';
                break;
            case 'drop_down':
            case 'radio':
            case 'checkbox':
            case 'multiple':
                template = OptionTemplateSelect;
                group = 'select';
                break;
            case 'date':
            case 'date_time':
            case 'time':
                template = OptionTemplateDate;
                group = 'date';
                break;
        }

        $('<?php echo $this->getFieldId() ?>_'+data.id+'_previous_group').value = group;

        template = '<div id="<?php echo $this->getFieldId() ?>_{{id}}_type_'+group+'" class="grid tier form-list">'+template+'</div><div id="<?php echo $this->getFieldId() ?>_{{id}}_type_'+group+'_advice"></div>';

        this.secondTemplate = new Template(template, this.templateSyntax);

        Element.insert($('<?php echo $this->getFieldId() ?>_'+data.option_id), {'after':this.secondTemplate.evaluate(data)});

        if (data.checkboxScopePrice) {
            //set disabled
            if ($('<?php echo $this->getFieldId() ?>_'+data.option_id+'_price') && data.scopePriceDisabled) {
                $('<?php echo $this->getFieldId() ?>_'+data.option_id+'_price').disable();
                $('<?php echo $this->getFieldId() ?>_'+data.option_id+'_price_type').disable();
            }
        }

        switch(data.type){
            case 'drop_down':
            case 'radio':
            case 'checkbox':
            case 'multiple':
                data.optionValues.each(function(value) {
                    selectOptionType.add(value);
                });
                selectOptionType.bindAddButton();
                break;
        }

        if (data.price_type) {
            $A($('<?php echo $this->getFieldId() ?>_'+data.option_id+'_price_type').options).each(function(option){
                if (option.value==data.price_type) option.selected = true;
            });
        }

    },
    bindSelectInputType : function(){
        var types = $$('.select-product-option-type');
        for(var i=0;i<types.length;i++){
            if(!$(types[i]).binded){
                $(types[i]).binded = true;
                Event.observe(types[i], 'change', function(event){
                    productOptionType.loadStepTwo(event);
                });
            }
        }
    }
}

var productOptionScope = {
    addScope : function(event){
        var element = $(Event.element(event));
        fieldToDisable = $(element.readAttribute('id').sub('_use_default', ''));

        if (fieldToDisable.disabled) {
            if (fieldToDisable.hasClassName('product-option-price')) {//need change to cheking value of element
                $(fieldToDisable.readAttribute('id')+'_type').enable();
            }
            fieldToDisable.enable();
        } else {
            if (fieldToDisable.hasClassName('product-option-price')) {//need change to cheking value of element
                $(fieldToDisable.readAttribute('id')+'_type').disable();
            }
            fieldToDisable.disable();
        }
    },
    bindScopeCheckbox : function(){
        var checkboxes = $$('.product-option-scope-checkbox');
        for (var i=0;i<checkboxes.length;i++) {
            if (!$(checkboxes[i]).binded) {
                $(checkboxes[i]).binded = true;
                Event.observe(checkboxes[i], 'click', this.addScope.bind(this));
            }
        }
    }
}

if($('option_panel')){
    $('option_panel').remove();
}

productOption.bindRemoveButtons();

if($('<?php echo $this->getAddButtonId() ?>')){
    Event.observe('<?php echo $this->getAddButtonId() ?>', 'click', productOption.add.bind(productOption));
}

//validation for selected input type
Validation.addAllThese([
    ['required-option-select', <?php echo $this->helper('core')->jsonEncode(Mage::helper('catalog')->__('Select type of option')) ?>, function(v, elm) {
        if (elm.getValue() == '') {
            return false;
        }
        return true;
}]]);

//adding data to templates
<?php foreach ($this->getOptionValues() as $_value): ?>
    productOption.add(<?php echo $_value->toJson() ?>);
    productOptionType.addDataToValues(<?php echo $_value->toJson() ?>);
<?php endforeach; ?>

//bind scope checkboxes
productOptionScope.bindScopeCheckbox();
//]]>
</script>

<div><?php if (!$this->isReadonly()):?><input type="hidden" name="affect_product_custom_options" value="1" /><?php endif;?></div>
