<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
 ?>

<?php /* @var $this Mage_Adminhtml_Block_Catalog_Product_Composite_Fieldset_Grouped */ ?>
<?php $_skipSaleableCheck = Mage::helper('catalog/product')->getSkipSaleableCheck(); ?>
<div id="catalog_product_composite_configure_fields_grouped" class="grid <?php echo $this->getIsLastFieldset() ? 'last-fieldset' : '' ?>">
    <h4><?php echo Mage::helper('catalog')->__('Associated Products') ?></h4>
    <div class="product-options">
        <?php $_product = $this->getProduct(); ?>
        <?php $this->setPreconfiguredValue(); ?>
        <?php $_associatedProducts = $this->getAssociatedProducts(); ?>
        <?php $_hasAssociatedProducts = count($_associatedProducts) > 0; ?>
        <?php if ((!$_product->isAvailable() && !$_skipSaleableCheck) || !$_hasAssociatedProducts): ?>
            <p class="availability out-of-stock"><?php echo Mage::helper('catalog')->__('Availability:') ?> <span><?php echo Mage::helper('catalog')->__('Out of stock') ?></span></p>
        <?php endif; ?>
        <table class="data-table grouped-items-table" id="super-product-table">
            <col />
            <col />
            <col />
            <col />
            <col width="1" />
            <thead>
                <tr "class="headings">
                    <th><?php echo Mage::helper('catalog')->__('ID') ?></th>
                    <th><?php echo Mage::helper('catalog')->__('SKU') ?></th>
                    <th><?php echo Mage::helper('catalog')->__('Product Name') ?></th>
                    <?php if ($this->getCanShowProductPrice($_product)): ?>
                    <th class="a-right"><?php echo Mage::helper('catalog')->__('Price') ?></th>
                    <?php endif; ?>
                    <?php if ($_product->isSaleable() || $_skipSaleableCheck): ?>
                    <th class="a-center"><?php echo Mage::helper('catalog')->__('Qty') ?></th>
                    <?php endif; ?>
                </tr>
            </thead>
            <tbody>
            <?php if ($_hasAssociatedProducts): ?>
                <?php $i = 0 ?>
            <?php foreach ($_associatedProducts as $_item): ?>
                <?php $_finalPriceInclTax = $this->helper('tax')->getPrice($_item, $_item->getFinalPrice(), true) ?>
                <tr class="<?php echo (++$i % 2) ? 'even' : 'odd' ?>">
                    <td><?php echo $_item->getId() ?></td>
                    <td><?php echo $this->escapeHtml($_item->getSku()) ?></td>
                    <td><?php echo $this->escapeHtml($_item->getName()) ?></td>
                    <?php if ($this->getCanShowProductPrice($_product)): ?>
                    <td class="a-right">
                        <?php if ($this->getCanShowProductPrice($_item)): ?>
                        <?php echo $this->getPriceHtml($_item, true) ?>
                        <?php endif; ?>
                    </td>
                    <?php endif; ?>
                    <?php if ($_product->isSaleable() || $_skipSaleableCheck): ?>
                    <td class="a-center">
                    <?php if ($_item->isSaleable() || $_skipSaleableCheck) : ?>
                        <input type="text" name="super_group[<?php echo $_item->getId() ?>]" id="super_group[<?php echo $_item->getId() ?>]" maxlength="12" value="<?php echo $_item->getQty()*1 ?>" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('catalog')->__('Qty')) ?>" class="input-text qty" />
                        <input type="hidden" value="1" price="<?php echo $this->getCurrencyPrice($_item->getPrice()) ?>" qtyId="super_group[<?php echo $_item->getId() ?>]" />
                    <?php else: ?>
                        <p class="availability out-of-stock"><span><?php echo $this->__('Out of stock') ?></span></p>
                    <?php endif; ?>
                    </td>
                    <?php endif; ?>
                </tr>
            <?php endforeach; ?>
            <?php else: ?>
               <tr>
                   <td colspan="<?php if ($_product->isSaleable() || $_skipSaleableCheck): ?>4<?php else : ?>3<?php endif; ?>"><?php echo Mage::helper('catalog')->__('No options of this product are available.') ?></td>
               </tr>
            <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>
