<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Adminhtml_Block_Catalog_Product_Edit_Tab_Price_Tier */
?>
<?php $_htmlId      = $this->getElement()->getHtmlId() ?>
<?php $_htmlClass   = $this->getElement()->getClass() ?>
<?php $_htmlName    = $this->getElement()->getName() ?>
<?php $_readonly    = $this->getElement()->getReadonly() ?>
<?php $_showWebsite = $this->isShowWebsiteColumn(); ?>
<?php $_editWebsite = $this->isAllowChangeWebsite(); ?>
<?php $_priceValueValidation = $this->getPriceValidation('validate-greater-than-zero'); ?>


<?php $_showWebsite = $this->isShowWebsiteColumn(); ?>
<?php $_showWebsite= $this->isMultiWebsites(); ?>
<tr>
    <td class="label"><?php echo $this->getElement()->getLabel() ?></td>
    <td colspan="10" class="grid tier">
    <table cellspacing="0" class="data border" id="tiers_table">
        <?php if ($_showWebsite): ?>
        <col width="135" />
        <?php endif; ?>
        <col width="120" />
        <col width="95" />
        <col />
        <col width="1" />
        <thead>
            <tr class="headings">
                <th <?php if (!$_showWebsite): ?>style="display:none"<?php endif; ?>><?php echo Mage::helper('sales')->__('Website') ?></th>
                <th><?php echo Mage::helper('catalog')->__('Customer Group') ?></th>
                <th><?php echo Mage::helper('catalog')->__('Qty') ?></th>
                <th><?php echo $this->getPriceColumnHeader(Mage::helper('catalog')->__('Price')) ?></th>
                <th class="last"><?php echo Mage::helper('catalog')->__('Action') ?></th>
            </tr>
        </thead>
        <tbody id="<?php echo $_htmlId ?>_container"></tbody>
        <tfoot>
            <tr>
                <td <?php if (!$_showWebsite): ?>style="display:none"<?php endif; ?>></td>
                <td colspan="4" class="a-right"><?php echo $this->getAddButtonHtml() ?></td>
            </tr>
        </tfoot>
    </table>

<script type="text/javascript">
//<![CDATA[
var tierPriceRowTemplate = '<tr>'
    + '<td<?php if (!$_showWebsite): ?> style="display:none"<?php endif; ?>>'
    + '<select class="<?php echo $_htmlClass ?> required-entry" name="<?php echo $_htmlName ?>[{{index}}][website_id]" id="tier_price_row_{{index}}_website">'
    <?php foreach ($this->getWebsites() as $_websiteId => $_info): ?>
    + '<option value="<?php echo $_websiteId ?>"><?php echo $this->jsQuoteEscape($this->escapeHtml($_info['name'])) ?><?php if (!empty($_info['currency'])): ?> [<?php echo $this->escapeHtml($_info['currency']) ?>]<?php endif; ?></option>'
    <?php endforeach ?>
    + '</select></td>'
    + '<td><select class="<?php echo $_htmlClass ?> custgroup required-entry" name="<?php echo $_htmlName ?>[{{index}}][cust_group]" id="tier_price_row_{{index}}_cust_group">'
    <?php foreach ($this->getCustomerGroups() as $_groupId=>$_groupName): ?>
    + '<option value="<?php echo $_groupId ?>"><?php echo $this->jsQuoteEscape($this->escapeHtml($_groupName)) ?></option>'
    <?php endforeach ?>
    + '</select></td>'
    + '<td class="nobr"><input class="<?php echo $_htmlClass ?> qty required-entry validate-greater-than-zero" type="text" name="<?php echo $_htmlName ?>[{{index}}][price_qty]" value="{{qty}}" id="tier_price_row_{{index}}_qty" />'
    + ' <small class="nobr"><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('and above')) ?></small></td>'
    + '<td><input class="<?php echo $_htmlClass ?> required-entry <?php echo $_priceValueValidation ?>" type="text" name="<?php echo $_htmlName ?>[{{index}}][price]" value="{{price}}" id="tier_price_row_{{index}}_price" /></td>'
    + '<td class="last"><input type="hidden" name="<?php echo $_htmlName ?>[{{index}}][delete]" class="delete" value="" id="tier_price_row_{{index}}_delete" />'
    + '<button title="<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__("Delete Tier")) ?>" type="button" class="scalable delete icon-btn delete-product-option" id="tier_price_row_{{index}}_delete_button" onclick="return tierPriceControl.deleteItem(event);">'
    + '<span><span><span><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__("Delete")) ?></span></span></span></button></td>'
    + '</tr>';

var tierPriceControl = {
    template: new Template(tierPriceRowTemplate, new RegExp('(^|.|\\r|\\n)({{\\s*(\\w+)\\s*}})', "")),
    itemsCount: 0,
    addItem : function () {
        <?php if ($_readonly): ?>
        if (arguments.length < 4) {
            return;
        }
        <?php endif; ?>
        var data = {
            website_id: '<?php echo $this->getDefaultWebsite() ?>',
            group: '<?php echo $this->getDefaultCustomerGroup() ?>',
            qty: '',
            price: '',
            readOnly: false,
            index: this.itemsCount++
        };

        if(arguments.length >= 4) {
            data.website_id = arguments[0];
            data.group      = arguments[1];
            data.qty        = arguments[2];
            data.price      = arguments[3];
        }
        if (arguments.length == 5) {
            data.readOnly = arguments[4];
        }

        Element.insert($('<?php echo $_htmlId ?>_container'), {
            bottom : this.template.evaluate(data)
        });

        $('tier_price_row_' + data.index + '_cust_group').value = data.group;
        $('tier_price_row_' + data.index + '_website').value    = data.website_id;

        <?php if ($this->isShowWebsiteColumn() && !$this->isAllowChangeWebsite()):?>
        var wss = $('tier_price_row_' + data.index + '_website');
        var txt = wss.options[wss.selectedIndex].text;

        wss.insert({after:'<span class="website-name">' + txt + '</span>'});
        wss.hide();
        <?php endif;?>

        if (data.readOnly == '1') {
            ['website', 'cust_group', 'qty', 'price', 'delete'].each(function(idx){
                $('tier_price_row_'+data.index+'_'+idx).disabled = true;
            });
            $('tier_price_row_'+data.index+'_delete_button').hide();
        }

        <?php if ($_readonly): ?>
        $('<?php echo $_htmlId ?>_container').select('input', 'select').each(this.disableElement);
        $('<?php echo $_htmlId ?>_container').up('table').select('button').each(this.disableElement);
        <?php else: ?>
        $('<?php echo $_htmlId ?>_container').select('input', 'select').each(function(el){ Event.observe(el, 'change', el.setHasChanges.bind(el)); });
        <?php endif; ?>
    },
    disableElement: function(el) {
        el.disabled = true;
        el.addClassName('disabled');
    },
    deleteItem: function(event) {
        var tr = Event.findElement(event, 'tr');
        if (tr) {
            Element.select(tr, '.delete').each(function(elem){elem.value='1'});
            Element.select(tr, ['input', 'select']).each(function(elem){elem.hide()});
            Element.hide(tr);
            Element.addClassName(tr, 'no-display template');
        }
        return false;
    }
};
<?php foreach ($this->getValues() as $_item): ?>
tierPriceControl.addItem('<?php echo $_item['website_id'] ?>', '<?php echo $_item['cust_group'] ?>', '<?php echo $_item['price_qty']*1 ?>', '<?php echo sprintf('%.2f', $_item['price']) ?>', <?php echo (int)!empty($_item['readonly'])?>);
<?php endforeach; ?>
<?php if ($_readonly): ?>
$('<?php echo $_htmlId ?>_container').up('table').select('button')
    .each(tierPriceControl.disableElement);
<?php endif; ?>
//]]>
</script>
</td></tr>
