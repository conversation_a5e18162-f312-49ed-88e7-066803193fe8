<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
 ?>

<?php /* @var $this Mage_Adminhtml_Block_Catalog_Product_Composite_Fieldset_Configurable */ ?>
<?php $_product = $this->getProduct(); ?>
<?php $_attributes = Mage::helper('core')->decorateArray($this->getAllowAttributes()); ?>
<?php $_skipSaleableCheck = Mage::helper('catalog/product')->getSkipSaleableCheck(); ?>
<?php if (($_product->isSaleable() || $_skipSaleableCheck) && count($_attributes)):?>
<div id="catalog_product_composite_configure_fields_configurable" class="<?php echo $this->getIsLastFieldset() ? 'last-fieldset' : '' ?>">
    <h4><?php echo Mage::helper('catalog')->__('Associated Products') ?></h4>
    <div class="product-options">
        <dl>
        <?php foreach($_attributes as $_attribute): ?>
            <dt><label class="required"><em>*</em><?php echo $this->escapeHtml($_attribute->getLabel()) ?></label></dt>
            <dd<?php if ($_attribute->decoratedIsLast){?> class="last"<?php }?>>
                <div class="input-box">
                    <select name="super_attribute[<?php echo $_attribute->getAttributeId() ?>]" id="attribute<?php echo $_attribute->getAttributeId() ?>" class="required-entry super-attribute-select">
                        <option><?php echo Mage::helper('catalog')->__('Choose an Option...') ?></option>
                    </select>
                </div>
            </dd>
        <?php endforeach; ?>
        </dl>
    </div>
</div>
<script type="text/javascript">
    var config = <?php echo $this->getJsonConfig() ?>;
    if (window.productConfigure) {
        config.containerId = window.productConfigure.blockFormFields.id;
        if (window.productConfigure.restorePhase) {
            config.inputsInitialized = true;
        }
    }
    ProductConfigure.spConfig = new Product.Config(config);
</script>
<?php endif;?>
