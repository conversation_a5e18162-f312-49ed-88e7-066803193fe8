<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
 ?>
<div id="popup-window-mask" style="display:none;"></div>
<div id="product_composite_configure" class="product-configure-popup" style="display:none;">
    <iframe name="product_composite_configure_iframe" id="product_composite_configure_iframe" src="#" style="width:0; height:0; border:0px solid #fff; position:absolute; top:-1000px; left:-1000px" onload="window.productConfigure && productConfigure.onLoadIFrame()"></iframe>
    <form action="" method="post" id="product_composite_configure_form" enctype="multipart/form-data" onsubmit="productConfigure.onConfirmBtn(); return false;" target="product_composite_configure_iframe">
        <div class="entry-edit">
            <div class="entry-edit-head">
                <h4 class="icon-head fieldset-legend"><?php echo Mage::helper('catalog')->__('Configure Product') ?></h4>
            </div>
            <div id="product_composite_configure_messages" style="display: none;" >
                <ul class="messages"><li class="error-msg"></li></ul>
            </div>
            <div id="product_composite_configure_form_fields" class="content"></div>
            <div id="product_composite_configure_form_additional" style="display:none;"></div>
            <div id="product_composite_configure_form_confirmed" style="display:none;"></div>
            <div class="buttons-set a-right">
                <button type="button" class="scalable" onclick="productConfigure.onCancelBtn()" id="product_composite_configure_form_cancel"><span><span><span><?php echo Mage::helper('catalog')->__('Cancel') ?></span></span></span></button>
                <button type="submit" class="scalable"><span><span><span><?php echo Mage::helper('catalog')->__('OK') ?></span></span></span></button>
            </div>
        </div>
        <input type="hidden" name="as_js_varname" value="iFrameResponse" />
        <input type="hidden" name="form_key" value="<?php echo $this->getFormKey() ?>" />
    </form>
    <div id="product_composite_configure_confirmed" style="display:none;"></div>

    <script type="text/javascript">
        var productCompositeConfigureForm = new varienForm('product_composite_configure_form');
    </script>
</div>
