<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Template for Mage_Adminhtml_Block_Catalog_Category_Cointainer
 */
?>

<div id="category-edit-container" class="category-content">
    <?php echo $this->getChildHtml('form') ?>
</div>
<script type="text/javascript">
//<![CDATA[

    function categoryReset(url,useAjax){
        if(useAjax){
            var params = {active_tab_id:false};
            updateContent(url, params);
        }else{
            location.href = url;
        }
    }

    /**
    * Delete some category
    * This routine get categoryId explicitly, so even if currently selected tree node is out of sync
    * with this form, we surely delete same category in the tree and at backend
    */
    function categoryDelete(url, useAjax, categoryId) {
        if (confirm('<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('Are you sure you want to delete this category?')) ?>')){
            if (useAjax){
                tree.nodeForDelete = categoryId;
                updateContent(url, {}, true);
            } else {
                location.href = url;
            }
        }
    }

    /**
     * Update category content area
     */
    function updateContent(url, params, refreshTree) {
        if (!params) {
            params = {};
        }
        if (!params.form_key) {
            params.form_key = FORM_KEY;
        }

        toolbarToggle.stop();

       /*if(params.node_name)
       {
           var currentNode = tree.getNodeById(tree.currentNodeId);
           currentNode.setText(params.node_name);
       }*/

        var categoryContainer = $('category-edit-container');
        var messagesContainer = $('messages');
        var thisObj = this;
        new Ajax.Request(url + (url.match(new RegExp('\\?')) ? '&isAjax=true' : '?isAjax=true' ), {
            parameters:  params,
            evalScripts: true,
            onComplete: function () {
                /**
                 * This func depends on variables, that came in response, and were eval'ed in onSuccess() callback.
                 * Since prototype's Element.update() evals javascripts in 10 msec, we should exec our func after it.
                 */
                setTimeout(function() {
                    try {
                        if (refreshTree) {
                            thisObj.refreshTreeArea();
                        }
                        toolbarToggle.start();
                    } catch (e) {
                        alert(e.message);
                    };
                }, 25);
            },
            onSuccess: function(transport) {
                try {
                    if (transport.responseText.isJSON()) {
                        var response = transport.responseText.evalJSON();
                        var needUpdate = true;
                        if (response.error) {
                            alert(response.message);
                            needUpdate = false;
                        }
                        if(response.ajaxExpired && response.ajaxRedirect) {
                            setLocation(response.ajaxRedirect);
                            needUpdate = false;
                        }
                        if (needUpdate){
                            if (response.content){
                                $(categoryContainer).update(response.content);
                            }
                            if (response.messages){
                                $(messagesContainer).update(response.messages);
                            }
                        }
                    } else {
                        $(categoryContainer).update(transport.responseText);
                    }
                }
                catch (e) {
                    $(categoryContainer).update(transport.responseText);
                }
            }
        });
    }

    /**
     * Refresh tree nodes after saving or deleting a category
     */
    function refreshTreeArea(transport)
    {
        if (tree && window.editingCategoryBreadcrumbs) {
            // category deleted - delete its node
            if (tree.nodeForDelete) {
                var node = tree.getNodeById(tree.nodeForDelete);
                tree.nodeForDelete = false;

                if (node) { // Check maybe tree became somehow not synced with ajax and we're trying to delete unknown node
                    node.parentNode.removeChild(node);
                    tree.currentNodeId = false;
                }
            }
            // category created - add its node
            else if (tree.addNodeTo) {
                var parent = tree.getNodeById(tree.addNodeTo);
                tree.addNodeTo = false;

                if (parent) { // Check maybe tree became somehow not synced with ajax and we're trying to add to unknown node
                    var node = new Ext.tree.AsyncTreeNode(editingCategoryBreadcrumbs[editingCategoryBreadcrumbs.length - 1]);
                    node.loaded = true;
                    tree.currentNodeId = node.id;
                    parent.appendChild(node);

                    if (parent.expanded) {
                        tree.selectCurrentNode();
                    } else {
                        var timer;
                        parent.expand();
                        var f = function(){
                            if(parent.expanded){ // done expanding
                                clearInterval(timer);
                                tree.selectCurrentNode();
                            }
                        };
                        timer = setInterval(f, 200);
                    }
                }
            }

            // update all affected categories nodes names
            for (var i = 0; i < editingCategoryBreadcrumbs.length; i++) {
                var node = tree.getNodeById(editingCategoryBreadcrumbs[i].id);
                if (node) {
                    node.setText(editingCategoryBreadcrumbs[i].text);
                }
            }
        }
    }

    function displayLoadingMask()
    {
       var loaderArea = $$('#html-body .wrapper')[0]; // Blocks all page
        Position.clone($(loaderArea), $('loading-mask'), {offsetLeft:-2});
        toggleSelectsUnderBlock($('loading-mask'), false);
        Element.show('loading-mask');
    }
//]]>
</script>
