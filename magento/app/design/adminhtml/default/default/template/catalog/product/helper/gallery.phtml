<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Template for block Mage_Adminhtml_Block_Catalog_Product_Helper_Form_Gallery_Content
 */
?>
<?php
$_block = $this;
/* @var $_block Mage_Adminhtml_Block_Catalog_Product_Helper_Form_Gallery_Content */
?>
<div id="<?php echo $_block->getHtmlId() ?>" >
<ul class="messages">
    <li class="notice-msg">
        <ul>
            <li>
            <?php echo Mage::helper('catalog')->__('Image type and information need to be specified for each store view.'); ?>
            </li>
        </ul>
    </li>
</ul>
<div class="grid">
<table cellspacing="0" class="data border" id="<?php echo $_block->getHtmlId() ?>_grid" width="100%">
    <col width="1" />
    <col />
    <col width="70" />
    <?php foreach ($_block->getImageTypes() as $typeId=>$type): ?>
    <col />
    <?php endforeach; ?>
    <col width="70" />
    <col width="70" />
    <thead>
        <tr class="headings">
            <th><?php echo Mage::helper('catalog')->__('Image') ?></th>
            <th><?php echo Mage::helper('catalog')->__('Label') ?></th>
            <th><?php echo Mage::helper('catalog')->__('Sort Order') ?></th>
            <?php foreach ($_block->getImageTypes() as $typeId => $type): ?>
                <th><?php echo $this->escapeHtml($type['label'], array('br')); ?></th>
            <?php endforeach; ?>
            <th><?php echo Mage::helper('catalog')->__('Exclude') ?></th>
            <th class="last"><?php echo Mage::helper('catalog')->__('Remove') ?></th>
        </tr>
    </thead>
    <tbody id="<?php echo $_block->getHtmlId() ?>_list">
        <tr id="<?php echo $_block->getHtmlId() ?>_template" class="template no-display">
                <td class="cell-image"><div class="place-holder" onmouseover="<?php echo $_block->getJsObjectName(); ?>.loadImage('__file__')"><span><?php echo Mage::helper('catalog')->__('Roll Over for preview') ?></span></div><img src="<?php echo $this->getSkinUrl('images/spacer.gif')?>" width="100" style="display:none;" alt="" /></td>
                <td class="cell-label"><input type="text" <?php if($_block->getElement()->getReadonly()):?> disabled="disabled"<?php endif;?> class="input-text" onkeyup="<?php echo $_block->getJsObjectName(); ?>.updateImage('__file__')" onchange="<?php echo $_block->getJsObjectName(); ?>.updateImage('__file__')" /></td>
                <td class="cell-position"><input type="text" <?php if($_block->getElement()->getReadonly()):?> disabled="disabled"<?php endif;?> class="input-text validate-number" onkeyup="<?php echo $_block->getJsObjectName(); ?>.updateImage('__file__')" onchange="<?php echo $_block->getJsObjectName(); ?>.updateImage('__file__')" /></td>
                <?php foreach ($_block->getImageTypes() as $typeId=>$type): ?>
                <td class="cell-<?php echo $typeId ?> a-center"><input <?php if($_block->getElement()->getAttributeReadonly($typeId)) :?> disabled="disabled" <?php endif;?> type="radio" name="<?php echo $type['field'] ?>" onclick="<?php echo $_block->getJsObjectName(); ?>.setProductImages('__file__')" value="__file__" /></td>
                <?php endforeach; ?>
                <td class="cell-disable a-center"><input type="checkbox" <?php if($_block->getElement()->getReadonly()):?> disabled="disabled"<?php endif;?> onclick="<?php echo $_block->getJsObjectName(); ?>.updateImage('__file__')" /></td>
                <td class="cell-remove a-center last"><input type="checkbox" <?php if($_block->getElement()->getReadonly()):?> disabled="disabled"<?php endif;?> onclick="<?php echo $_block->getJsObjectName(); ?>.updateImage('__file__')" /></td>
        </tr>
        <?php if($_block->hasUseDefault()): ?>
        <tr id="<?php echo $_block->getHtmlId() ?>_default">
                <td><?php echo Mage::helper('catalog')->__('Use Default Value') ?></td>
                <td>&nbsp;</td>
                <td>&nbsp;</td>
                <?php foreach ($_block->getMediaAttributes() as $_attribute): ?>
                <td class="a-center">
                <?php if($_block->getElement()->canDisplayUseDefault($_attribute)): ?>
                <input class="default-checkbox" name="use_default[]" type="checkbox" <?php if($_block->getElement()->getAttributeReadonly($_attribute->getAttributeCode())):?> disabled="disabled" <?php endif;?>  onclick="<?php echo $_block->getJsObjectName(); ?>.updateUseDefault()"
<?php if($_block->getElement()->usedDefault($_attribute)): ?>checked<?php endif; ?> value="<?php echo $_attribute->getAttributeCode() ?>" />
                <?php endif ?>
                </td>
                <?php endforeach; ?>
                <td>&nbsp;</td>
                <td class="last">&nbsp;</td>
        </tr>
    <?php endif ?>
        <tr id="<?php echo $_block->getHtmlId() ?>-image-0">
                <td class="cell-image"><?php echo Mage::helper('catalog')->__('No image') ?></td>
                <td class="cell-label"><input type="hidden" />&nbsp;</td>
                <td class="cell-position"><input type="hidden" />&nbsp;</td>
                <?php foreach ($_block->getImageTypes() as $typeId=>$type): ?>
                <td class="cell-<?php echo $typeId ?> a-center"><input type="radio" <?php if($_block->getElement()->getAttributeReadonly($typeId)) :?> disabled="disabled" <?php endif;?> name="<?php echo $type['field'] ?>" onclick="<?php echo $_block->getJsObjectName(); ?>.setProductImages('no_selection')" value="no_selection" /></td>
                <?php endforeach; ?>
                <td class="cell-disable"><input type="hidden" />&nbsp;</td>
                <td class="cell-remove last"><input type="hidden" />&nbsp;</td>
        </tr>
    </tbody>
<?php if (!$_block->getElement()->getReadonly()):?>
    <tfoot>
        <tr>
            <td colspan="100" class="last" style="padding:8px">
                <?php echo Mage::helper('catalog')->__('Maximum width and height dimension for upload image is %s.', Mage::getStoreConfig(Mage_Catalog_Helper_Image::XML_NODE_PRODUCT_MAX_DIMENSION)); ?>
                <?php echo $_block->getUploaderHtml() ?>
            </td>
        </tr>
    </tfoot>
<?php endif;?>
</table>
</div>
</div>
<input type="hidden" id="<?php echo $_block->getHtmlId() ?>_save" name="<?php echo $_block->getElement()->getName() ?>[images]" value="<?php echo $_block->escapeHtml($_block->getImagesJson()) ?>" />
<input type="hidden" id="<?php echo $_block->getHtmlId() ?>_save_image" name="<?php echo $_block->getElement()->getName() ?>[values]" value="<?php echo $_block->escapeHtml($_block->getImagesValuesJson()) ?>" />
<script type="text/javascript">
//<![CDATA[
var <?php echo $_block->getJsObjectName(); ?> = new Product.Gallery('<?php echo $_block->getHtmlId() ?>', <?php echo $_block->getImageTypesJson() ?>);
//]]>
</script>
