<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="entry-edit custom-options product-custom-options">
    <div id="dynamic-price-warrning" style="display:none">
        <ul class="messages">
            <li class="error-msg">
                <ul>
                    <li><?php echo $this->__('Bundle with dynamic pricing cannot include custom defined options. Options will not be saved.') ?></li>
                </ul>
            </li>
        </ul>
    </div>
    <div class="entry-edit-head">
        <h4><?php echo Mage::helper('catalog')->__('Custom Options') ?></h4>
        <div class="right"><?php echo $this->getAddButtonHtml() ?></div>
    </div>

    <div id="product_options_container" class="box">
        <div id="product_options_container_top"></div>
        <?php echo $this->getOptionsBoxHtml() ?>
    </div>
</div>

<script type="text/javascript">
// re-bind form elements onchange
varienWindowOnload(true);
//show error message
if ($('price_type')) {
    if ($('price_type').value == '0' && $('dynamic-price-warrning')) {
        $('dynamic-price-warrning').show();
    }
}
</script>
