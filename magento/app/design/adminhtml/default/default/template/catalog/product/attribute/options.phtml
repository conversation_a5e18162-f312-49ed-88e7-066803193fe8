<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * Attribute options control
 *
 * @deprecated after 1.7.0.2 The file is moved
 * @see Mage_Adminhtml_Block_Catalog_Product_Attribute_Edit_Tab_Options
 */
?>
<div>
<ul class="messages">
    <li class="notice-msg">
        <ul>
            <li><?php echo Mage::helper('catalog')->__('If you do not specify an option value for a specific store view then the default (Admin) value will be used.') ?></li>
        </ul>
    </li>
</ul>
</div>

<div class="entity-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo Mage::helper('catalog')->__('Manage Titles (Size, Color, etc.)') ?></h4>
    </div>
    <div class="box">
            <div class="hor-scroll">
            <table class="dynamic-grid" cellspacing="0" id="attribute-labels-table">
                <tr>
                <?php foreach ($this->getStores() as $_store): ?>
                    <th><?php echo $this->escapeHtml($_store->getName()); ?></th>
                <?php endforeach; ?>
                </tr>
                <tr>
                    <?php $_labels = $this->getLabelValues() ?>
                    <?php foreach ($this->getStores() as $_store): ?>
                    <td>
                        <input class="input-text<?php if($_store->getId()==0): ?> required-option<?php endif; ?>" type="text" name="frontend_label[<?php echo $_store->getId() ?>]" value="<?php echo $this->escapeHtml($_labels[$_store->getId()]) ?>"<?php if ($this->getReadOnly()):?> disabled="disabled"<?php endif;?>/>
                    </td>
                    <?php endforeach; ?>
                </tr>
            </table>
            </div>
    </div>
</div>
<br/>
<div class="entity-edit" id="matage-options-panel">
    <div class="entry-edit-head">
    <h4 class="icon-head head-edit-form fieldset-legend"><?php echo Mage::helper('catalog')->__('Manage Options (values of your attribute)') ?></h4>
    </div>
    <div class="box">
        <div class="hor-scroll">
            <table class="dynamic-grid" cellspacing="0"  cellpadding="0">
                <tr id="attribute-options-table">
                    <?php foreach ($this->getStores() as $_store): ?>
                        <th><?php echo $this->escapeHtml($_store->getName()); ?></th>
                    <?php endforeach; ?>
                        <th><?php echo Mage::helper('catalog')->__('Position') ?></th>
                        <th class="nobr a-center"><?php echo Mage::helper('catalog')->__('Is Default') ?></th>
                        <th>
                            <?php if (!$this->getReadOnly()):?>
                                <?php echo $this->getAddNewButtonHtml() ?>
                            <?php endif;?>
                        </th>
                    </tr>
                    <tr class="no-display template" id="row-template">
                        <?php foreach ($this->getStores() as $_store): ?>
                        <td><input name="option[value][{{id}}][<?php echo $_store->getId() ?>]" value="{{store<?php echo $_store->getId() ?>}}" class="input-text<?php if($_store->getId()==0): ?> required-option<?php endif; ?>" type="text" <?php if ($this->getReadOnly()):?> disabled="disabled"<?php endif;?>/></td>
                        <?php endforeach; ?>
                        <td class="a-center"><input class="input-text" type="text" name="option[order][{{id}}]" value="{{sort_order}}" <?php if ($this->getReadOnly()):?> disabled="disabled"<?php endif;?>/></td>
                        <td><input class="input-radio" type="radio" name="default[]" value="{{id}}" <?php if ($this->getReadOnly()):?> disabled="disabled"<?php endif;?>/></td>
                        <td class="a-left">
                            <input type="hidden" class="delete-flag" name="option[delete][{{id}}]" value="" />
                            <?php if (!$this->getReadOnly()):?>
                                <?php echo $this->getDeleteButtonHtml() ?>
                            <?php endif;?>
                        </td>
                    </tr>
            </table>
        </div>
        <input type="hidden" id="option-count-check" value="" />
    </div>
</div>
<script type="text/javascript">
//<![CDATA[
var optionDefaultInputType = 'radio';

// IE removes quotes from element.innerHTML whenever it thinks they're not needed, which breaks html.
var templateText =
        '<tr class="option-row">'+
<?php foreach ($this->getStores() as $_store): ?>
            '<td><input name="option[value][{{id}}][<?php echo $_store->getId() ?>]" value="{{store<?php echo $_store->getId() ?>}}" class="input-text<?php if($_store->getId()==0): ?> required-option<?php endif; ?>" type="text" <?php if ($this->getReadOnly()):?> disabled="disabled"<?php endif;?>/><\/td>'+
<?php endforeach; ?>
            '<td><input class="input-text" type="text" name="option[order][{{id}}]" value="{{sort_order}}" <?php if ($this->getReadOnly()):?> disabled="disabled"<?php endif;?>/><\/td>'+
            '<td class="a-center"><input class="input-radio" type="{{intype}}" name="default[]" value="{{id}}" {{checked}} <?php if ($this->getReadOnly()):?> disabled="disabled"<?php endif;?>/><\/td>'+
            '<td class="a-left" id="delete_button_container_{{id}}">'+
                '<input type="hidden" class="delete-flag" name="option[delete][{{id}}]" value="" />'+
                <?php if (!$this->getReadOnly()):?>
                    '<?php echo Mage::helper('core')->jsQuoteEscape($this->getDeleteButtonHtml()) ?>'+
                <?php endif;?>
            '<\/td>'+
        '<\/tr>';

var attributeOption = {
    table : $('attribute-options-table'),
    templateSyntax : /(^|.|\r|\n)({{(\w+)}})/,
    templateText : templateText,
    itemCount : 0,
    totalItems : 0,
    isReadOnly: <?php echo (int)$this->getReadOnly(); ?>,
    add : function(data) {
        this.template = new Template(this.templateText, this.templateSyntax);
        var isNewOption = false;
        if(!data.id){
            data = {};
            data.id  = 'option_'+this.itemCount;
            isNewOption = true;
        }
        if (!data.intype)
            data.intype = optionDefaultInputType;
        Element.insert(this.table, {after: this.template.evaluate(data)});
        if (isNewOption && !this.isReadOnly) {
            this.enableNewOptionDeleteButton(data.id);
        }
        this.bindRemoveButtons();
        this.itemCount++;
        this.totalItems++;
        this.updateItemsCountField();
    },
    remove : function(event){
        var element = $(Event.findElement(event, 'tr')); // !!! Button already
                                                               // have table parent in safari
        // Safari workaround
        element.ancestors().each(function(parentItem){
           if (parentItem.hasClassName('option-row')) {
               element = parentItem;
               throw $break;
           } else if (parentItem.hasClassName('box')) {
               throw $break;
           }
        });


        if(element){
            var elementFlags = element.getElementsByClassName('delete-flag');
            if(elementFlags[0]){
                elementFlags[0].value=1;
            }

            element.addClassName('no-display');
            element.addClassName('template');
            element.hide();
            this.totalItems--;
            this.updateItemsCountField();
        }
    },
    updateItemsCountField: function() {
        if (this.totalItems > 0) {
            $('option-count-check').value = '1';
        } else {
            $('option-count-check').value = '';
        }
    },
    enableNewOptionDeleteButton: function(id) {
        $$('#delete_button_container_' + id + ' button').each(function(button) {
            button.enable();
            button.removeClassName('disabled');
        });
    },
    bindRemoveButtons : function(){
        var buttons = $$('.delete-option');
        for(var i=0;i<buttons.length;i++){
            if(!$(buttons[i]).binded){
                $(buttons[i]).binded = true;
                Event.observe(buttons[i], 'click', this.remove.bind(this));
            }
        }
    }

}
if($('row-template')){
    $('row-template').remove();
}
attributeOption.bindRemoveButtons();

if($('add_new_option_button')){
    Event.observe('add_new_option_button', 'click', attributeOption.add.bind(attributeOption));
}
Validation.addAllThese([
    ['required-option', '<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('Failed')) ?>', function(v) {
        return !Validation.get('IsEmpty').test(v);
}]]);
Validation.addAllThese([
    ['required-options-count', '<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('catalog')->__('Options is required')) ?>', function(v) {
        return !Validation.get('IsEmpty').test(v);
}]]);
<?php foreach ($this->getOptionValues() as $_value): ?>
    attributeOption.add(<?php echo $_value->toJson() ?>);
<?php endforeach; ?>
//]]>
</script>
