<?php
/**
 * MageWorx
 * Admin Order Editor extension
 *
 * @category   MageWorx
 * @package    MageWorx_OrdersEdit
 * @copyright  Copyright (c) 2016 MageWorx (http://www.mageworx.com/)
 */
?>
<?php $_order = $this->getOrder() ?>
<div class="grid np">
    <div class="hor-scroll">
        <table cellspacing="0" class="data order-tables">
            <col />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <col width="1" />
            <thead>
            <tr class="headings">
                <th><?php echo $this->helper('sales')->__('Product') ?></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Item Status') ?></span></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Original Price') ?></span></th>
                <th><?php echo $this->helper('sales')->__('Price') ?></th>
                <th class="a-center"><?php echo $this->helper('sales')->__('Qty') ?></th>
                <th><?php echo $this->helper('sales')->__('Subtotal') ?></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Tax Amount') ?></span></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Tax Percent') ?></span></th>
                <th><span class="nobr"><?php echo $this->helper('sales')->__('Discount Amount') ?></span></th>
                <th class="last"><span class="nobr"><?php echo $this->helper('sales')->__('Row Total') ?></span></th>
            </tr>
            </thead>
            <?php $_items = $this->getItemsCollection() ?>
            <?php $i=0;foreach ($_items as $_item):?>
                <?php if ($_item->getParentItem()) continue; else $i++;?>
                <tbody class="<?php echo $i%2?'even':'odd' ?>">
                <?php echo $this->getItemHtml($_item) ?>
                <?php echo $this->getItemExtraInfoHtml($_item) ?>
                </tbody>
            <?php endforeach; ?>
        </table>
    </div>
</div>
<br />
