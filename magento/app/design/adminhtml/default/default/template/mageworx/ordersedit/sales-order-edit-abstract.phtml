<?php
/**
 * MageWorx
 * Admin Order Editor extension
 *
 * @category   MageWorx
 * @package    MageWorx_OrdersEdit
 * @copyright  Copyright (c) 2016 MageWorx (http://www.mageworx.com/)
 */
?>
<?php if (Mage::helper('mageworx_ordersedit')->isShippingPriceEditEnabled()) : ?>
<div class="entry-edit">
    <div class="entry-edit-head">
        <div style="float: right;"><?php echo $this->getButtonsHtml() ?></div>
        <h4 class="fieldset-legend <?php echo ($this->getHeaderCssClass()) ? $this->getHeaderCssClass().' icon-head' : '' ?>"><?php echo $this->getHeaderText() ?></h4>
    </div>
    <div class="fieldset">
    <?php 
        echo $this->getChildHtml('', true, true); ?>
            <div align="right">
                <?php
                    $quote = $this->getQuote();
                    if (is_object($quote)) {
                        $address = $quote->getShippingAddress();
                    } else {
                        $address = Mage::getSingleton('adminhtml/session_quote')->getQuote()->getShippingAddress();
                    }
                    if ($address) {
                        $store = Mage::getSingleton('adminhtml/session_quote')->getStore();
                        if (Mage::helper('tax')->shippingPriceIncludesTax($store)) {
                            $baseShippingAmount = $address->getBaseShippingInclTax();
                        } else {
                            $baseShippingAmount = $address->getBaseShippingAmount();
                        }
                    } else {
                        $baseShippingAmount = 0;
                    }
                    $customPriceFlag = false;
//                    if (is_null(Mage::getSingleton('adminhtml/session_quote')->getBaseShippingCustomPrice())) $customPriceFlag = false; else $customPriceFlag = true;
                ?>
                <label><input type="checkbox" onclick="if (this.checked) $('div_shipping_price').show(); else {$('div_shipping_price').hide();}" name="order[shipping][price_checkbox]" value="1" <?php if ($customPriceFlag) { ?>checked="checked"<?php } ?> /> <?php echo Mage::helper('mageworx_ordersedit')->__('Custom Price') ?></label>
                <div id="div_shipping_price" style="padding-top:2px; <?php if (!$customPriceFlag) { ?>display:none;<?php } ?>"><input type="text" class="input-text validate-zero-or-greater" style="width:83px;" value="<?php echo number_format($baseShippingAmount, 2, '.', ''); ?>" name="order[shipping][price]" /></div>
            </div>
    </div>
</div>
<?php endif; ?>