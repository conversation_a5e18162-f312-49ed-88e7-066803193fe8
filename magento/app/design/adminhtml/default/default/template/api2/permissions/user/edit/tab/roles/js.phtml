<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<script type="text/javascript">
//<![CDATA[
var activeRestRole = getActiveRestRole();

/* Retrieve current active rest role */
function getActiveRestRole() {
    var role;
    api2_roles_sectionJsObject.rows.each(function(row) {
        var radiobox = $(row).getElementsByClassName('radio')[0];
        if ('undefined' != typeof radiobox && radiobox.checked) {
            role = radiobox;
        }
    });
    return role;
}

/* Callback function for rest role row click */
function restRolesRowClick(grid, event) {
    var newRestRole = getActiveRestRole();
    if (newRestRole.value !== activeRestRole.value) {
        if (!confirm("<?php echo Mage::helper('core')->jsQuoteEscape($this->__('Warning!\r\nThis action will remove this user from already assigned role\r\nAre you sure?')) ?>")) {
            newRestRole.checked = !newRestRole.checked;
            activeRestRole.checked = true;
        } else {
            activeRestRole = newRestRole;
        }
    }
}

api2_roles_sectionJsObject.rowClickCallback = restRolesRowClick;
//]]>
</script>
