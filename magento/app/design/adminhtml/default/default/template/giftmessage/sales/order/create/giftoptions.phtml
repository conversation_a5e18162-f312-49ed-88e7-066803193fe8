<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php $_item = $this->getItem() ?>
<?php if ($_item): ?>
    <?php $_childHtml = trim($this->getChildHtml('', false));?>
    <?php if ($_childHtml): ?>
        <tr class="border">
            <td class="first">
                <div>
                    <strong><a class="action-link" href="#" id="gift_options_link_<?php echo $_item->getId() ?>"><?php echo Mage::helper('sales')->__('Gift Options') ?></a></strong><br/>
                </div>
                <script type="text/javascript">
                    giftOptionsTooltip.addTargetLink('gift_options_link_<?php echo $_item->getId() ?>', <?php echo $_item->getId() ?>);
                </script>
                <div id="gift_options_data_<?php echo $_item->getId() ?>">
                    <?php echo $_childHtml ?>
                </div>
            </td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td></td>
            <td class="last"></td>
        </tr>
    <?php endif ?>
<?php endif ?>
