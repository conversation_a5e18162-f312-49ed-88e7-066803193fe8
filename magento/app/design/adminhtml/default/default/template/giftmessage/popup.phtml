<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php if ($this->getChildHtml()) :?>
<div id="gift_options_window_mask" class="popup-window-mask" style="display: none;"></div>
<div id="gift_options_configure_new" class="gift_options-popup product-configure-popup" style="display: none;">
    <div id="gift_options_form_contents">
        <div class="entry-edit">
            <div class="entry-edit-head">
                <h4 class="icon-head fieldset-legend"><?php echo Mage::helper('giftmessage')->__('Gift Options for'); ?> <span id="gift_options_configure_title"></span></h4>
            </div>
            <div class="content">
                <?php echo $this->getChildHtml();?>
            </div>
            <div class="buttons-set a-right">
                <button type="button" class="scalable" id="gift_options_cancel_button"><span><span><span><?php echo Mage::helper('giftmessage')->__('Cancel'); ?></span></span></span></button>
                <button type="button" class="scalable" id="gift_options_ok_button"><span><span><span><?php echo Mage::helper('giftmessage')->__('OK'); ?></span></span></span></button>
            </div>
        </div>
    </div>
</div>
<div id="giftoptions_tooltip_window" class="gift-options-tooltip" style="display:none;">
    <div id="giftoptions_tooltip_window_content">&nbsp;</div>
</div>

<script type="text/javascript">
//<![CDATA[
_giftOptions = new GiftOptionsPopup();
giftOptionsTooltip.setTooltipWindow('giftoptions_tooltip_window','giftoptions_tooltip_window_content');
//]]>
</script>
<?php endif;?>
