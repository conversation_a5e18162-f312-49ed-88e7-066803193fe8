<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php echo $this->helper('core/js')->includeScript('mage/adminhtml/giftmessage.js') ?>
<?php if(!$this->getSaveMode()): ?>
<form action="<?php echo $this->getSaveUrl() ?>" id="message-form" method="post" class="a-left">
    <?php echo $this->getBlockHtml('formkey')?>
    <?php echo $this->getFormHtml(); ?>
    <?php echo $this->getSaveButtonHtml() ?>
    <?php echo $this->getCancelButtonHtml() ?>
    <?php if($this->getMessage()->getGiftMessageId()): ?>
    <?php echo $this->getRemoveButtonHtml() ?>
    <?php endif; ?>
</form>
<?php elseif($this->getSaveMode()=='save'): ?>
<h3><?php echo Mage::helper('giftmessage')->__('The gift message has been saved') ?></h3>
<br /><?php echo $this->getCloseButtonHtml() ?>
<?php else: ?>
<h3><?php echo Mage::helper('giftmessage')->__('The gift message has been removed') ?></h3>
<br /><?php echo $this->getCloseButtonHtml() ?>
<?php endif; ?>
<script type="text/javascript">
<!--
<?php if(!$this->getSaveMode()): ?>
    var giftMessageWindowObject = new GiftMessageWindow('<?php echo $this->getUniqueId() ?>','message-form', '<?php echo $this->getRemoveUrl() ?>');
    giftMessageWindowObject.confirmMessage = '<?php echo $this->getEscapedForJs(Mage::helper('giftmessage')->__('Are you sure?')) ?>';
<?php else: ?>
    var giftMessageWindowObject = new GiftMessageWindow('<?php echo $this->getUniqueId() ?>', null, '<?php echo $this->getRemoveUrl() ?>');
    Event.observe(window, 'load', function(evt) {
        giftMessageWindowObject.updateParent('<?php echo $this->getEditUrl() ?>', '<?php echo $this->getButtonUrl() ?>');
    });
<?php endif; ?>
//-->
</script>
