<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php if ($this->canDisplayGiftmessageForm()): ?>
<div id="gift_options_giftmessage">
    <div class="giftcard-form">
        <div class="giftcard-send-form">
            <h4><?php echo Mage::helper('giftmessage')->__('Gift Message'); ?></h4>
            <ul class="form-list">
                <li class="fields">
                    <div class="field">
                        <label for="current_item_giftmessage_sender"><?php echo Mage::helper('giftmessage')->__('From'); ?></label>
                        <div class="input-box">
                            <input type="text" class="input-text" name="current_item_giftmessage_sender" id="current_item_giftmessage_sender" />
                        </div>
                    </div>
                </li>
                <li class="fields">
                    <div class="field">
                        <label for="current_item_giftmessage_recipient"><?php echo Mage::helper('giftmessage')->__('To'); ?></label>
                        <div class="input-box">
                            <input type="text" class="input-text" name="current_item_giftmessage_recipient" id="current_item_giftmessage_recipient" />
                        </div>
                    </div>
                </li>
                <li class="fields">
                    <div class="field">
                        <label for="current_item_giftmessage_message"><?php echo Mage::helper('giftmessage')->__('Message'); ?></label>
                        <div class="input-box">
                            <textarea class="textarea" cols="15" rows="2" name="current_item_giftmessage_message" id="current_item_giftmessage_message"></textarea>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>

<script type="text/javascript">
//<![CDATA[
var giftMessageSet = new GiftMessageSet();
//]]>
</script>
<?php endif; ?>
