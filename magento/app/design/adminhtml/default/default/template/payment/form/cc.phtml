<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_code=$this->getMethodCode() ?>
<ul id="payment_form_<?php echo $_code ?>" style="display:none">
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_cc_type"><?php echo Mage::helper('payment')->__('Credit Card Type') ?> <span class="required">*</span></label><br/>
            <select id="<?php echo $_code ?>_cc_type" name="payment[cc_type]" class="required-entry validate-cc-type-select">
            <?php $_ccType = $this->getInfoData('cc_type') ?>
                <option value=""></option>
            <?php foreach ($this->getCcAvailableTypes() as $_typeCode => $_typeName): ?>
                <option value="<?php echo $_typeCode ?>" <?php if($_typeCode==$_ccType): ?>selected="selected"<?php endif ?>><?php echo $_typeName ?></option>
            <?php endforeach ?>
            </select>
        </div>
    </li>
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_cc_number"><?php echo Mage::helper('payment')->__('Credit Card Number') ?> <span class="required">*</span></label><br/>
            <input type="text" id="<?php echo $_code ?>_cc_number" name="payment[cc_number]" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('payment')->__('Credit Card Number')) ?>" class="input-text validate-cc-number" value="<?php echo $this->getInfoData('cc_number')?>"/>
        </div>
    </li>
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_expiration"><?php echo Mage::helper('payment')->__('Expiration Date') ?> <span class="required">*</span></label><br/>
            <select id="<?php echo $_code ?>_expiration" style="width:140px;" name="payment[cc_exp_month]" class="validate-cc-exp required-entry">
            <?php $_ccExpMonth = $this->getInfoData('cc_exp_month') ?>
            <?php foreach ($this->getCcMonths() as $k=>$v): ?>
                <option value="<?php echo $k ?>" <?php if($k==$_ccExpMonth): ?>selected="selected"<?php endif ?>><?php echo $v ?></option>
            <?php endforeach ?>
            </select>
            <?php $_ccExpYear = $this->getInfoData('cc_exp_year') ?>
            <select id="<?php echo $_code ?>_expiration_yr" style="width:103px;" name="payment[cc_exp_year]" class="required-entry">
            <?php foreach ($this->getCcYears() as $k=>$v): ?>
                <option value="<?php echo $k ? $k : '' ?>" <?php if($k==$_ccExpYear): ?>selected="selected"<?php endif ?>><?php echo $v ?></option>
            <?php endforeach ?>
            </select>
        </div>
    </li>
    <?php if($this->hasVerification()): ?>
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_cc_cid"><?php echo Mage::helper('payment')->__('Card Verification Number') ?> <span class="required">*</span></label><br/>
            <input type="text" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('payment')->__('Card Verification Number')) ?>" class="required-entry input-text validate-cc-cvn" id="<?php echo $_code ?>_cc_cid" name="payment[cc_cid]" style="width:3em;" value="<?php echo $this->getInfoData('cc_cid')?>"/>
        </div>
    </li>
    <?php endif; ?>

    <?php if ($this->hasSsCardType()): ?>
    <li id="<?php echo $_code ?>_cc_type_ss_div">
        <ul class="inner-form">
            <li class="form-alt"><label for="<?php echo $_code ?>_cc_issue" class="required"><em>*</em><?php echo $this->__('Switch/Solo/Maestro Only') ?></label></li>
            <li>
                <label for="<?php echo $_code ?>_cc_issue"><?php echo $this->__('Issue Number') ?>:</label>
                <span class="input-box">
                    <input type="text" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Issue Number')) ?>" class="input-text validate-cc-ukss cvv" id="<?php echo $_code ?>_cc_issue" name="payment[cc_ss_issue]" value="" />
                </span>
            </li>

            <li>
                <label for="<?php echo $_code ?>_start_month"><?php echo $this->__('Start Date') ?>:</label>
                <div class="input-box">
                        <select id="<?php echo $_code ?>_start_month" name="payment[cc_ss_start_month]" class="validate-cc-ukss month">
                        <?php foreach ($this->getCcMonths() as $k=>$v): ?>
                            <option value="<?php echo $k?$k:'' ?>"<?php if($k==$this->getInfoData('cc_ss_start_month')): ?> selected="selected"<?php endif ?>><?php echo $v ?></option>
                        <?php endforeach ?>
                        </select>
                        <select id="<?php echo $_code ?>_start_year" name="payment[cc_ss_start_year]" class="validate-cc-ukss year">
                        <?php foreach ($this->getSsStartYears() as $k=>$v): ?>
                            <option value="<?php echo $k?$k:'' ?>"<?php if($k==$this->getInfoData('cc_ss_start_year')): ?> selected="selected"<?php endif ?>><?php echo $v ?></option>
                        <?php endforeach ?>
                        </select>
                </div>
            </li>
            <li class="adv-container">&nbsp;</li>
        </ul>
        <script type="text/javascript">
        //<![CDATA[
        var SSChecked<?php echo $_code ?> = function() {
            var elm = $('<?php echo $_code ?>_cc_type');
            if (['SS','SM','SO'].indexOf(elm.value) != -1) {
                $('<?php echo $_code ?>_cc_type_ss_div').show();
            } else {
                $('<?php echo $_code ?>_cc_type_ss_div').hide();
            }
        };
        Event.observe($('<?php echo $_code ?>_cc_type'), 'change', SSChecked<?php echo $_code ?>);
        SSChecked<?php echo $_code ?>();
        //]]>
        </script>
    </li>
    <?php endif; ?>
</ul>
