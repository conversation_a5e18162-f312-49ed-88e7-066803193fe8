<?php
/**
 * @package Stenik_Zeron
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<div id="stenik_zeron_package_createsubmit_mask" class="popup-window-mask" style="display:none;"></div>
<div id="stenik_zeron_package_createsubmit_window" class="packed-window" style="display:none;">
    <form action="<?php echo $this->getUrl('stenik_zeron/adminhtml_order/createAndSubmitPackage', array('order_id' => $this->getOrder()->getId())) ?>" method="post" id="stenik_zeron_package_createsubmit_form">
        <input type="hidden" id="stenik_zeron_package_createsubmit_form_key" name="form_key" value="<?php echo $this->getFormKey() ?>">
        <input type="hidden" id="stenik_zeron_package_createsubmit_warehouse_id" name="warehouse_id">
        <div class="entry-edit">
            <div class="entry-edit-head"><h4><?php echo $this->__('Package Information') ?></h4></div>
            <fieldset>
                <ul>
                    <li class="field-row">
                        <label for="stenik_zeron_package_createsubmit_readiness_date"><?php echo $this->__('Readiness Date');?></label>
                        <?php $dateToday = Mage::app()->getLocale()->date() ?>
                        <input type="text"
                               name="readiness_date"
                               id="stenik_zeron_package_createsubmit_readiness_date"
                               value="<?php echo $dateToday->toString('y-MM-dd') ?>"
                        >
                        <img src="<?php echo $this->getSkinUrl('images/grid-cal.gif') ?>"
                             alt=""
                             class="v-middle"
                             id="stenik_zeron_package_createsubmit_readiness_date_trig"
                             title="<?php echo $this->__('Select Date');?>"
                        >
                    </li>
                    <li class="field-row">
                        <input type="checkbox" name="fragile" id="stenik_zeron_package_createsubmit_fragile" value="1" checked="checked">
                        <label for="stenik_zeron_package_createsubmit_fragile"><?php echo $this->__('Fragile');?></label>
                    </li>
                    <li class="field-row">
                        <label for="stenik_zeron_package_createsubmit_package_count"><?php echo $this->__('Package Count');?></label>
                        <input type="text" name="package_count" id="stenik_zeron_package_createsubmit_package_count" value="1">
                    </li>
                    <li class="field-row">
                        <label for="stenik_zeron_package_createsubmit_type"><?php echo $this->__('Package Type');?></label>
                        <?php $options = Mage::getModel('stenik_zeron/package_attribute_source_type')->getOptionArray(); ?>
                        <select name="type" id="stenik_zeron_package_createsubmit_type">
                            <?php foreach ($options as $value => $label): ?>
                                <option value="<?php echo $value ?>">
                                    <?php echo $this->escapeHtml($label) ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </li>
                </ul>

                <div class="clear"></div>
                <div class="button-set">
                    <div class="f-left">
                        <button onclick="stenikZeronPackage.closeWindow(); return false;" type="button" class="scalable close">
                            <span><span><span><?php echo $this->__('Close'); ?></span></span></span>
                        </button>
                    </div>

                    <div class="f-right">
                        <button onclick="$('stenik_zeron_package_createsubmit_form').submit()" type="button" class="scalable save">
                            <span><span><span><?php echo $this->__('Send'); ?></span></span></span>
                        </button>
                    </div>
                </div>
            </fieldset>
        </div>
    </form>
</div>

<script>

    /** Checks date and time equality */

    var origDateEqualsTo = Date.prototype.equalsTo;
    Date.prototype.equalsTo = function(date) {
        return typeof date == 'undefined' ? false : origDateEqualsTo(date);
    };

    $('stenik_zeron_package_createsubmit_readiness_date_trig').observe('click', function() {
        var params = {
            inputField: $('stenik_zeron_package_createsubmit_readiness_date'),
            ifFormat: "%Y-%m-%e",
            showsTime: false,
            button: "stenik_zeron_package_createsubmit_readiness_date_trig",
            align: "Bl",
            singleClick : true
        };
        function param_default(pname, def) {
            if (typeof params[pname] == "undefined") {
                params[pname] = def;
            }
        };
        param_default("inputField", null);
        param_default("displayArea", null);
        param_default("button", null);
        param_default("eventName", "click");
        param_default("ifFormat", "%Y/%m/%d");
        param_default("daFormat", "%Y/%m/%d");
        param_default("singleClick", true);
        param_default("disableFunc", null);
        param_default("dateStatusFunc", params["disableFunc"]);
        param_default("dateText", null);
        param_default("firstDay", null);
        param_default("align", "Br");
        param_default("range", [1900, 2999]);
        param_default("weekNumbers", true);
        param_default("flat", null);
        param_default("flatCallback", null);
        param_default("onSelect", null);
        param_default("onClose", null);
        param_default("onUpdate", null);
        param_default("date", null);
        param_default("showsTime", false);
        param_default("timeFormat", "24");
        param_default("electric", true);
        param_default("step", 2);
        param_default("position", null);
        param_default("cache", false);
        param_default("showOthers", false);
        param_default("multiple", null);

        function onSelect(cal) {
            var p = cal.params;
            var update = (cal.dateClicked || p.electric);
            if (update && p.inputField) {
                p.inputField.value = cal.date.print(p.ifFormat);
                if (typeof p.inputField.onchange == "function") p.inputField.onchange();
                if (typeof fireEvent == 'function') fireEvent(p.inputField, "change");
            }
            if (update && p.displayArea) p.displayArea.innerHTML = cal.date.print(p.daFormat);
            if (update && typeof p.onUpdate == "function") p.onUpdate(cal);
            if (update && p.flat) {
                if (typeof p.flatCallback == "function") p.flatCallback(cal);
            }
            if (update && p.singleClick && cal.dateClicked) cal.callCloseHandler();
        };

        var dateEl = params.inputField || params.displayArea;
        var dateFmt = params.inputField ? params.ifFormat : params.daFormat;
        var mustCreate = false;
        var cal = window.calendar;
        if (cal) {
            cal.hide();
        }
        if (dateEl) params.date = Date.parseDate(dateEl.value || dateEl.innerHTML, dateFmt);
        if (!(cal && params.cache)) {
            window.calendar = cal = new Calendar(params.firstDay, params.date, params.onSelect || onSelect, params.onClose || function(cal) {
                cal.hide();
            });
            cal.showsTime = params.showsTime;
            cal.time24 = (params.timeFormat == "24");
            cal.weekNumbers = params.weekNumbers;
            mustCreate = true;
        } else {
            if (params.date) cal.setDate(params.date);
            cal.hide();
        }
        if (params.multiple) {
            cal.multiple = {};
            for (var i = params.multiple.length; --i >= 0;) {
                var d = params.multiple[i];
                var ds = d.print("%Y%m%d");
                cal.multiple[ds] = d;
            }
        }
        cal.showsOtherMonths = params.showOthers;
        cal.yearStep = params.step;
        cal.setRange(params.range[0], params.range[1]);
        cal.params = params;
        cal.setDateStatusHandler(params.dateStatusFunc);
        cal.getDateText = params.dateText;
        cal.setDateFormat(dateFmt);
        if (mustCreate) cal.create($('stenik_zeron_package_createsubmit_window'));
        cal.refresh();
        if (!params.position) cal.showAtElement(params.button || params.displayArea || params.inputField, params.align);
        else cal.showAt(params.position[0], params.position[1]);
        return false;
    });


    stenikZeronPackage = {
        window: $('stenik_zeron_package_createsubmit_window'),
        windowMask: $('stenik_zeron_package_createsubmit_mask'),
        showWindow: function(warehouseId) {
            $('stenik_zeron_package_createsubmit_warehouse_id').value = warehouseId;

            this.window.show().setStyle({
                'marginLeft': -this.window.getDimensions().width/2 + 'px'
            });
            this.windowMask.setStyle({
                height: $('html-body').getHeight() + 'px'
            }).show();
        },
        closeWindow: function() {
            this.window.hide();
            this.windowMask.hide();
        }
    }
</script>