<?php
/** @var Mage_Payment_Block_Form $this */

/** @var Mage_Payment_Model_Method_Abstract $_method */
$_method = $this->getMethod();
$_code = $this->getMethodCode();

/** @var Mage_Sales_Model_Quote $_quote */
$_quote = $_method->getInfoInstance()->getQuote();

$_info = $_quote->getPayment()->getMethod() == $_code ? $_method->getInfoInstance()->getAdditionalInformation() : [];

$_principal = $_quote->getGrandTotal();
$_downpayment = array_key_exists('downpayment', $_info) ? $_info['downpayment'] : 0.00;

$_curr = Mage::app()->getStore()->getCurrentCurrency();

$_variants = $_method->getAvailableVariants(null, $_downpayment);
?>
<div id="payment_form_<?php echo $_code ?>" style="display:none; padding-top: 10px;">

    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="fieldset-legend"><?php echo $this->escapeHtml($this->__('Repayment Schedule')) ?></h4>
        </div>
        <div class="fieldset " id="main">
            <div class="hor-scroll">
                <table class="form-list">
                    <tbody>
                    <tr>
                        <td class="label a-right"><label
                                    for="<?php echo $_code ?>_principal"><strong><?php echo $this->escapeHtml($this->__('Principal')) ?></strong></label>
                        </td>
                        <td class="value">
                            <strong><?php echo $this->escapeHtml($_curr->formatPrecision($_principal, 2, [], false, false)) ?></strong>
                        </td>
                    </tr>
                    <tr>
                        <td class="label a-right" rowspan="2"><label
                                    for="<?php echo $_code ?>_downpayment"><?php echo $this->escapeHtml($this->__('Downpayment')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_downpayment"
                                                 name="<?php echo "payment[{$_code}][downpayment]" ?>"
                                                 value="<?php echo $this->quoteEscape($_curr->formatPrecision($_downpayment, 2, ['display' => Zend_Currency::NO_SYMBOL], false, false)) ?>"
                                                 type="text"
                                                 style="width: 100px;">
                            <button title="Преизчисли" type="button" class="scalable" onclick="order.itemsUpdate()">
                                <span><?php echo $this->escapeHtml($this->__('Recalculate')) ?></span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php if (empty($_variants)): ?>
        <ul class="messages"><li class="error-msg"><ul><li><span><?php echo $this->escapeHtml($this->__('No repayment schedules available for this order.')) ?></span></li></ul></li></ul>
    <?php else: ?>

        <div class="grid hor-scroll" id="<?php echo $_code ?>-variants" style="margin: -16px 0 15px 0;">
            <div class="hor-scroll">
                <table cellspacing="0" class="data">
                    <thead>
                    <tr class="headings">
                        <th class="no-link"></th>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <th class="no-link a-center">
                                <?php
                                // Use selected_variant data as fallback if variant data is missing
                                $maturityValue = isset($varient['maturity']) && $varient['maturity'] > 0
                                    ? $varient['maturity']
                                    : (isset($_info['selected_variant']['maturity']) ? $_info['selected_variant']['maturity'] : 0);

                                // Determine if this is a promotional variant based on APR and scheme
                                $aprValue = isset($varient['apr']) && $varient['apr'] !== null
                                    ? (float)$varient['apr']
                                    : (isset($_info['selected_variant']['apr']) ? (float)$_info['selected_variant']['apr'] : 0);

                                $schemeName = isset($varient['pricing_scheme_name'])
                                    ? $varient['pricing_scheme_name']
                                    : (isset($_info['selected_variant']['pricing_scheme_name']) ? $_info['selected_variant']['pricing_scheme_name'] : '');

                                // Check if this is a promotional scheme
                                $isPromo = ($aprValue <= 0.1) || (strpos($schemeName, '0%') !== false) || (strpos($schemeName, 'Вземи сега') !== false);

                                echo $this->escapeHtml($maturityValue);
                                echo $this->escapeHtml($this->__('m.'));

                                // Add promo label for promotional variants
                                if ($isPromo) {
                                    echo '<br><span style="color: #ff6600; font-weight: bold; font-size: 10px;">ПРОМО</span>';
                                }
                                ?>
                            </th>
                        <?php endforeach; ?>
                    </tr>
                    </thead>
                    <tbody class="even">
                    <tr>
                        <td class="first a-right">
                            <strong><?php echo $this->escapeHtml($this->__('Installment Amount')) ?></strong></td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="no-link a-center"><?php
                                // Use selected_variant data as fallback if variant data is missing or zero
                                $installmentAmount = isset($varient['installment']) && $varient['installment'] > 0
                                    ? $varient['installment']
                                    : (isset($_info['selected_variant']['installment']) ? $_info['selected_variant']['installment'] : 0);
                                echo $this->escapeHtml($_curr->formatTxt($installmentAmount));
                            ?></td>
                        <?php endforeach; ?>
                    </tr>
                    </tbody>
                    <tbody class="odd">
                    <tr>
                        <td class="first a-right"><strong><?php echo $this->escapeHtml($this->__('APR')) ?></strong>
                        </td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="no-link a-center"><?php
                                // Use selected_variant data as fallback if variant data is missing (allow 0 values for promotional variants)
                                $aprValue = isset($varient['apr']) && $varient['apr'] !== null && $varient['apr'] !== ''
                                    ? $varient['apr']
                                    : (isset($_info['selected_variant']['apr']) ? $_info['selected_variant']['apr'] : 0);
                                echo $this->escapeHtml($_curr->formatPrecision($aprValue, 2, ['display' => Zend_Currency::NO_SYMBOL], false, false));
                            ?>
                                &percnt;
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    </tbody>
                    <tbody class="even">
                    <tr>
                        <td class="first a-right"><strong><?php echo $this->escapeHtml($this->__('NIR')) ?></strong>
                        </td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="no-link a-center"><?php
                                // Use selected_variant data as fallback if variant data is missing (allow 0 values for promotional variants)
                                $nirValue = isset($varient['nir']) && $varient['nir'] !== null && $varient['nir'] !== ''
                                    ? $varient['nir']
                                    : (isset($_info['selected_variant']['nir']) ? $_info['selected_variant']['nir'] : 0);
                                echo $this->escapeHtml($_curr->formatPrecision($nirValue, 2, ['display' => Zend_Currency::NO_SYMBOL], false, false));
                            ?>
                                &percnt;
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    </tbody>
                    <tbody class="odd">
                    <tr>
                        <td class="first a-right">
                            <strong><?php echo $this->escapeHtml($this->__('Total Repayment Amount')) ?></strong></td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="no-link a-center"><?php
                                // Use selected_variant data as fallback if variant data is missing or zero
                                $totalRepaymentAmount = isset($varient['total_repayment']) && $varient['total_repayment'] > 0
                                    ? $varient['total_repayment']
                                    : (isset($_info['selected_variant']['total_repayment']) ? $_info['selected_variant']['total_repayment'] : 0);
                                echo $this->escapeHtml($_curr->formatTxt($totalRepaymentAmount));
                            ?></td>
                        <?php endforeach; ?>
                    </tr>
                    </tbody>
                    <tfoot>
                    <tr>
                        <td>&nbsp;</td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="a-center"><input name="<?php echo "payment[{$_code}][variant_id]" ?>"
                                                        type="radio" class=""
                                                        value="<?php echo $this->quoteEscape($id) ?>"
                                    <?php if (array_key_exists('variant_id', $_info) && $_info['variant_id'] == $id): ?> checked="checked"<?php endif ?>/>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>

    <?php endif ?>

    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="fieldset-legend"><?php echo $this->escapeHtml($this->__('Application Information')) ?></h4>
        </div>
        <div class="fieldset " id="main">
            <div class="hor-scroll">
                <table class="form-list">
                    <tbody>
                    <tr>
                        <td class="label"><label
                                    for="<?php echo $_code ?>_name"><?php echo $this->escapeHtml($this->__('Name')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_name"
                                                 name="<?php echo "payment[{$_code}][names]" ?>"
                                                 value="<?php if (array_key_exists('name', $_info)): echo $this->quoteEscape($_info['name']); endif; ?>"
                                                 class="input-text required-entry" type="text"></td>
                    </tr>
                    <tr>
                        <td class="label"><label
                                    for="<?php echo $_code ?>_pin"><?php echo $this->escapeHtml($this->__('PIN')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_pin"
                                                 name="<?php echo "payment[{$_code}][pin]" ?>"
                                                 value="<?php if (array_key_exists('pin', $_info)): echo $this->quoteEscape($_info['pin']); endif; ?>"
                                                 class="input-text required-entry" type="text"></td>
                    </tr>
                    <tr>
                        <td class="label"><label
                                    for="<?php echo $_code ?>_names"><?php echo $this->escapeHtml($this->__('Email')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_email"
                                                 name="<?php echo "payment[{$_code}][email]" ?>"
                                                 value="<?php if (array_key_exists('email', $_info)): echo $this->quoteEscape($_info['email']); endif; ?>"
                                                 class="input-text required-entry" type="text"></td>
                    </tr>
                    <tr>
                        <td class="label"><label
                                    for="<?php echo $_code ?>_phone"><?php echo $this->escapeHtml($this->__('Phone')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_phone"
                                                 name="<?php echo "payment[{$_code}][phone]" ?>"
                                                 value="<?php if (array_key_exists('phone', $_info)): echo $this->quoteEscape($_info['phone']); endif; ?>"
                                                 class="input-text required-entry" type="text"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
