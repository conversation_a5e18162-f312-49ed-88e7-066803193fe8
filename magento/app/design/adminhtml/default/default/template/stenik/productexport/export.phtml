<?php
/**
 * @package Stenik_ProductExport
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>
<style>
    #exportproducts .filters strong {
        color: red;
    }
    #exportproducts .filters {
        width: 100%;
    }
    #exportproducts .filters .filterLine {
        vertical-align: top;
    }
    #exportproducts .filters .savedFilters {
        padding: 20px;
        width: 50%;
        border: 1px dotted gray;
    }
    #exportproducts .filters .savedFilters .saveCurrentFilters {
        float: right;
    }
    #exportproducts .filters .savedFilters .filterLinkWrapper {
        padding: 5px;
        display: inline-block;
    }
    #exportproducts .filters .savedFilters .filterLinkWrapper .filterLink {
        font-size: 18px;
    }
    #exportproducts .filters .ms-container .search-input {
        width: 50%;
        margin-bottom: 10px;
    }
</style>
<form action="#" method="post" id="exportproducts">
    <div class="container_12">
        <div class="grid_12 col" id="exportproductsContainer">
            <h3>Филтри:</h3>
            <table class="filters">
                <tr class="filterLine">
                    <td>
                        <label for="export:store_code">Store View:</label>
                        <?php $stores = Mage::app()->getStores(); ?>
                        <?php $counter = 0; ?>
                        <select name="store_code" id="export:store_code">
                            <option value="">Admin</option>
                            <?php foreach ($stores as $store): ?>
                                <option value="<?php echo (++$counter == 1 ? 'admin,' : '') . $store->getCode() ?>">
                                    <?php echo $store->getName() ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </td>
                    <td rowspan="3" class="savedFilters">
                        <h4>Запазени филтри <button class="saveCurrentFilters">Запази текущите филтри</button></h4>
                    </td>
                </tr>
                <tr class="filterLine">
                    <td>
                        <label for="export:attribute_set"><?php echo $this->__('Attribute Set');?>:</label>
                        <?php
                            $attributeSetCollection = Mage::getModel('eav/entity_attribute_set')->getCollection()
                                ->addFieldToFilter('entity_type_id', Mage::getSingleton('eav/config')->getEntityType(Mage_Catalog_Model_Product::ENTITY)->getId())
                                ->addOrder('sort_order')
                            ;
                        ?>
                        <select name="attribute_set" id="export:attribute_set">
                            <option value="">Всички</option>
                            <?php foreach ($attributeSetCollection as $attributeSet): ?>
                                <option value="<?php echo $attributeSet->getId() ?>">
                                    <?php echo $attributeSet->getAttributeSetName(); ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </td>
                </tr>
                <tr class="filterLine">
                    <td>
                        <label for="export:category">По категория:</label>
                        <?php
                            $categoryCollection = Mage::getModel('catalog/category')->getCollection()
                                ->addAttributeToSelect('name')
                                ->addAttributeToSort('path')
                                ->addAttributeToSort('position');
                            ;
                        ?>
                        <select name="category" id="export:category">
                            <option value="">Всички</option>
                            <?php foreach ($categoryCollection as $category): ?>
                                <?php if ($category->getLevel() < 2) continue; ?>
                                <option value="<?php echo $category->getId() ?>">
                                    <?php echo str_repeat('&nbsp;', 3 * ($category->getLevel() - 2)) ?>
                                    <?php echo $category->getName(); ?>
                                </option>
                            <?php endforeach ?>
                        </select>
                    </td>
                </tr>
                <tr class="filterLine columnsFilter">
                    <td colspan="2">
                        <label for="export:attributes">Колони:</label>
                        <?php $attributesCollection = Mage::getResourceModel('catalog/product_attribute_collection'); ?>
                        <select name="columns[]" id="export:attributes" multiple>
                            <optgroup label='Kолони'>
                                <option value="store" selected="selected" class="disabled">Store View - (store)</option>
                                <option value="id">Product ID - (id)</option>
                                <option value="created_at">Created At GMT<?php echo Mage::app()->getLocale()->date()->toString('ZZZZ') ?> - (created_at)</option>
                                <option value="qty">Qty - (qty)</option>
                                <option value="manage_stock">Manage Stock - (manage_stock)</option>
                                <option value="use_config_manage_stock">Use Config Manage Stock - (use_config_manage_stock)</option>
                                <option value="is_in_stock">Is In Stock - (is_in_stock)</option>
                                <option value="type">Type - (type)</option>
                                <option value="attribute_set">Attribute Set - (attribute_set)</option>
                                <option value="categories">Категории - (categories)</option>
                            </optgroup>
                            <optgroup label='Атрибути'>
                                <?php
                                $hideAttributes = array(
                                    /*'thumbnail', 'small_image', 'media_gallery', 'image',*/ 'gallery',
                                    /*'recurring_profile', 'is_recurring', 'custom_design',
                                    'enable_googlecheckout', 'custom_layout_update',
                                    'custom_design_to', 'custom_design_from', */
                                );
                                ?>
                                <?php $attributesCollection->setOrder('attribute_code'); ?>
                                <?php foreach ($attributesCollection as $attribute): ?>
                                    <?php if (!$attribute->getIsVisible()) continue; ?>
                                    <?php if (in_array($attribute->getAttributeCode(), $hideAttributes)) continue; ?>
                                    <option value="attribute_<?php echo $attribute->getAttributeCode() ?>"
                                        <?php if ($attribute->getAttributeCode() == 'sku'): ?>selected="selected" class="disabled"<?php endif; ?>
                                    >
                                        <?php echo $attribute->getFrontend()->getLabel(); ?>
                                        - (<?php echo $attribute->getAttributeCode() ?>)
                                    </option>
                                <?php endforeach ?>
                            </optgroup>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <input type="checkbox" name="include_image_labels" id="include_image_labels" value="1">
                        <label for="include_image_labels">Include image labels for image columns.</label>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <input type="checkbox" name="use_urls_in_images" id="use_urls_in_images" value="1">
                        <label for="use_urls_in_images">Use URLs for image columns.</label>
                        <strong>Warning: Magmi Import may not work with URLs.</strong>
                    </td>
                </tr>
                <tr><td colspan="2">&nbsp;</td></tr>
            </table>

            <h3>Изтегляне на файл</h3>
            <div class="formline">
                <p>От тук можете да изтеглите файл с продукти.</p>
                <button type="submit" onclick="return stenikStartExportFile();">Изтегли</button>
            </div>
        </div>
        <script>
            function stenikStartExportFile() {
                var container = document.getElementById('exportproductsContainer');
                iframe = document.createElement('iframe');
                iframe.frameborder = 0;
                iframe.src = <?php echo json_encode($this->getUrl('*/*/export')) ?> + '?' + jQuery('#exportproducts').serialize();
                // iframe.src = 'about:blank';
                iframe.style.width = '100%';
                iframe.style.height = '100px';
                // form = jQuery('#exportproducts').clone().css('display', 'none');
                container.appendChild(iframe);
                // form.appendTo(jQuery('iframe').contents().find('body'));
                // jQuery(iframe).contents().find('form').submit();

                jQuery('#exportproducts').find('input, button, select, .ms-elem-selectable, .ms-elem-selection').prop('disabled', true).addClass('disabled');
                return false;
            }
            (function() {
                var containerId = null;
                jQuery('#export\\:attributes').multiSelect({
                    keepOrder: true,
                    selectableOptgroup: false,
                    selectableHeader: "<h4>Възможни колони</h4> <input type='text' class='search-input' autocomplete='off' placeholder='Search..'>",
                    selectionHeader: "<h4>Избрани колони</h4> <input type='text' class='search-input' autocomplete='off' placeholder='Search..'>",
                    afterInit: function(ms){
                        jQuery(ms[0]).find('.ms-elem-selectable, .ms-elem-selection').each(function(){
                            jQuery(this).html(jQuery(this).html().replace(/(.*)- \((.*)\)/, '<small style="float: right;">($2)</small>$1'));
                        });

                        var that = jQuery('#export\\:attributes').data('multiselect') || this;
                        containerId = containerId || that.$container.attr('id');

                        var $selectableSearch = that.$selectableUl.prev(),
                            $selectionSearch = that.$selectionUl.prev(),
                            selectableSearchString = '#'+ containerId.split(':').join('\\:')+' .ms-elem-selectable:not(.ms-selected)',
                            selectionSearchString = '#'+ containerId.split(':').join('\\:')+' .ms-elem-selection.ms-selected';

                        that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
                        .on('keydown', function(e){
                          if (e.which == 40){
                            that.$selectableUl.focus();
                            return false;
                          }
                          if (e.which == 27){
                            return false;
                          }
                        });

                        that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
                        .on('keydown', function(e){
                          if (e.which == 40){
                            that.$selectionUl.focus();
                            return false;
                          }
                          if (e.which == 27){
                            return false;
                          }
                        });
                    },
                    afterSelect: function(){
                        this.qs1.cache();
                        this.qs2.cache();
                    },
                    afterDeselect: function(){
                        this.qs1.cache();
                        this.qs2.cache();
                    }
                });


                var $savedFiltersContainer = jQuery('#exportproducts .savedFilters');

                if(typeof(Storage) !== "undefined") {

                    var currentFilters = localStorage.getItem('stenik_export_saved_filters');
                    if (currentFilters) {
                        currentFilters = JSON.parse(currentFilters);
                    }

                    if (!currentFilters) {
                        currentFilters = [];
                    }

                    for (var i = currentFilters.length - 1; i >= 0; i--) {
                        if (!currentFilters[i]) {
                            delete currentFilters[i];
                        }
                    }
                    console.log(currentFilters);

                    var addFilterLink = function(filter) {
                        if (!filter) return;

                        $linkWrapper = jQuery('<span class="filterLinkWrapper"></span>');

                        var $link = jQuery('<a class="filterLink" href="javascript:;"></a>');
                        $link.text(filter.name);
                        $link.click(function() {
                            jQuery('#exportproducts [type="checkbox"]').prop('checked', false);
                            jQuery('#exportproducts [type="radio"]').prop('checked', false);
                            for (name in filter.valuesByName) {
                                var $input = jQuery('#exportproducts').find('[name="' + name + '"]');
                                if ($input.prop('type') == 'select-one') {
                                    $input.val(filter.valuesByName[name]);
                                } else if ($input.prop('type') == 'select-multiple') {
                                    var currVals = $input.val();
                                    $input.val(filter.valuesByName[name]);
                                    if ($input.data('multiselect')) {
                                        jQuery('#export\\:attributes').multiSelect('deselect', currVals);
                                        jQuery('#export\\:attributes').multiSelect('select', filter.valuesByName[name]);
                                    }
                                    $input.val(filter.valuesByName[name]);
                                } else if ($input.prop('type') == 'checkbox' || $input.prop('type') == 'radio') {
                                    $input.val(filter.valuesByName[name]);
                                    $input.prop('checked', true);
                                }

                                console.log($input.prop('type'));
                            }
                        });
                        $link.appendTo($linkWrapper);

                        $linkWrapper.append('&nbsp;[');
                        var $edit = jQuery('<a class="filterLinkEdit ui-icon ui-icon-pencil" href="javascript:;">edit</a>');
                        $edit.click(function() {
                            var newName = prompt('Please enter new name', jQuery(this).parents('.filterLinkWrapper').first().find('.filterLink').text());
                            if (!newName) {
                                return false;
                            }

                            for (var i = currentFilters.length - 1; i >= 0; i--) {
                                if (currentFilters[i] && currentFilters[i].id == filter.id) {
                                    currentFilters[i].name = newName;
                                    break;
                                }
                            }

                            localStorage.setItem('stenik_export_saved_filters', JSON.stringify(currentFilters));
                            jQuery(this).parents('.filterLinkWrapper').first().find('.filterLink').text(newName);
                        });
                        $edit.appendTo($linkWrapper);

                        $linkWrapper.append(',');
                        var $del = jQuery('<a class="filterLinkDelete ui-icon ui-icon-closethick" href="javascript:;">del</a>');
                        $del.click(function() {
                            if (!confirm('Are you sure you want to delete this?')) {
                                return false;
                            }

                            for (var i = currentFilters.length - 1; i >= 0; i--) {
                                if (currentFilters[i] && currentFilters[i].id == filter.id) {
                                    delete currentFilters[i];
                                    break;
                                }
                            }

                            localStorage.setItem('stenik_export_saved_filters', JSON.stringify(currentFilters));
                            jQuery(this).parents('.filterLinkWrapper').first().remove();
                        });
                        $del.appendTo($linkWrapper);
                        $linkWrapper.append(']');


                        $linkWrapper.appendTo($savedFiltersContainer);
                    }

                    for (var i = currentFilters.length - 1; i >= 0; i--) {
                        addFilterLink(currentFilters[i]);
                    };

                    $savedFiltersContainer.find('.saveCurrentFilters').click(function() {
                        var values = jQuery('#exportproducts').serializeArray();
                        var valuesByName = {};
                        for (var i = values.length - 1; i >= 0; i--) {
                            val = values[i];

                            if (typeof valuesByName[val.name] == 'undefined') {
                                valuesByName[val.name] = [];
                            }

                            valuesByName[val.name].push(val.value);
                        }

                        var filter = {
                            id: Date.now(),
                            'name': 'Untitled',
                            valuesByName: valuesByName
                        };
                        currentFilters.push(filter);
                        addFilterLink(filter);

                        localStorage.setItem('stenik_export_saved_filters', JSON.stringify(currentFilters));
                        return false;
                    })
                } else {
                    $savedFiltersContainer.html('To use filter saving your browser must support Local Storage.');
                }
            })();
        </script>
    </div>
</form>