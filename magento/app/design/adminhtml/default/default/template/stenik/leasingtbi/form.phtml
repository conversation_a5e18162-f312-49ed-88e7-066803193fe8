<?php
/** @var Mage_Payment_Block_Form $this */

/** @var Mage_Payment_Model_Method_Abstract $_method */
$_method = $this->getMethod();
$_code = $this->getMethodCode();

/** @var Mage_Sales_Model_Quote $_quote */
$_quote = $_method->getInfoInstance()->getQuote();

$_info = $_quote->getPayment()->getMethod() == $_code ? $_method->getInfoInstance()->getAdditionalInformation() : [];

$_principal = $_quote->getGrandTotal();
$_downpayment = array_key_exists('downpayment', $_info) ? $_info['downpayment'] : 0.00;

$_curr = Mage::app()->getStore()->getCurrentCurrency();

$_variants = $_method->getAvailableVariants(null, $_downpayment);
?>
<div id="payment_form_<?php echo $_code ?>" style="display:none; padding-top: 10px;">

    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="fieldset-legend"><?php echo $this->escapeHtml($this->__('Repayment Schedule')) ?></h4>
        </div>
        <div class="fieldset " id="main">
            <div class="hor-scroll">
                <table class="form-list">
                    <tbody>
                    <tr>
                        <td class="label a-right"><label
                                    for="<?php echo $_code ?>_principal"><strong><?php echo $this->escapeHtml($this->__('Principal')) ?></strong></label>
                        </td>
                        <td class="value">
                            <strong><?php echo $this->escapeHtml($_curr->formatPrecision($_principal, 2, [], false, false)) ?></strong>
                        </td>
                    </tr>
                    <tr>
                        <td class="label a-right" rowspan="2"><label
                                    for="<?php echo $_code ?>_downpayment"><?php echo $this->escapeHtml($this->__('Downpayment')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_downpayment"
                                                 name="<?php echo "payment[{$_code}][downpayment]" ?>"
                                                 value="<?php echo $this->quoteEscape($_curr->formatPrecision($_downpayment, 2, ['display' => Zend_Currency::NO_SYMBOL], false, false)) ?>"
                                                 type="text"
                                                 style="width: 100px;">
                            <button title="Преизчисли" type="button" class="scalable" onclick="order.itemsUpdate()">
                                <span><?php echo $this->escapeHtml($this->__('Recalculate')) ?></span>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <?php if (empty($_variants)): ?>
        <ul class="messages"><li class="error-msg"><ul><li><span><?php echo $this->escapeHtml($this->__('No repayment schedules available for this order.')) ?></span></li></ul></li></ul>
    <?php else: ?>

        <div class="grid hor-scroll" id="<?php echo $_code ?>-variants" style="margin: -16px 0 15px 0;">
            <div class="hor-scroll">
                <table cellspacing="0" class="data">
                    <thead>
                    <tr class="headings">
                        <th class="no-link"></th>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <th class="no-link a-center"><?php echo $this->escapeHtml($varient['maturity']) ?><?php echo $this->escapeHtml($this->__('m.')) ?></th>
                        <?php endforeach; ?>
                    </tr>
                    </thead>
                    <tbody class="even">
                    <tr>
                        <td class="first a-right">
                            <strong><?php echo $this->escapeHtml($this->__('Installment Amount')) ?></strong></td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="no-link a-center"><?php echo $this->escapeHtml($_curr->formatTxt($varient['installment'])) ?></td>
                        <?php endforeach; ?>
                    </tr>
                    </tbody>
                    <tbody class="odd">
                    <tr>
                        <td class="first a-right"><strong><?php echo $this->escapeHtml($this->__('APR')) ?></strong>
                        </td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="no-link a-center"><?php echo $this->escapeHtml($_curr->formatPrecision($varient['apr'], 2, ['display' => Zend_Currency::NO_SYMBOL], false, false)) ?>
                                &percnt;
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    </tbody>
                    <tbody class="even">
                    <tr>
                        <td class="first a-right"><strong><?php echo $this->escapeHtml($this->__('NIR')) ?></strong>
                        </td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="no-link a-center"><?php echo $this->escapeHtml($_curr->formatPrecision($varient['nir'], 2, ['display' => Zend_Currency::NO_SYMBOL], false, false)) ?>
                                &percnt;
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    </tbody>
                    <tbody class="odd">
                    <tr>
                        <td class="first a-right">
                            <strong><?php echo $this->escapeHtml($this->__('Total Repayment Amount')) ?></strong></td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="no-link a-center"><?php echo $this->escapeHtml($_curr->formatTxt($varient['total_repayment'])) ?></td>
                        <?php endforeach; ?>
                    </tr>
                    </tbody>
                    <tfoot>
                    <tr>
                        <td>&nbsp;</td>
                        <?php foreach ($_variants as $id => $varient): ?>
                            <td class="a-center"><input name="<?php echo "payment[{$_code}][variant_id]" ?>"
                                                        type="radio" class=""
                                                        value="<?php echo $this->quoteEscape($id) ?>"
                                    <?php if (array_key_exists('variant_id', $_info) && $_info['variant_id'] == $id): ?> checked="checked"<?php endif ?>/>
                            </td>
                        <?php endforeach; ?>
                    </tr>
                    </tfoot>
                </table>
            </div>
        </div>

    <?php endif ?>

    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="fieldset-legend"><?php echo $this->escapeHtml($this->__('Application Information')) ?></h4>
        </div>
        <div class="fieldset " id="main">
            <div class="hor-scroll">
                <table class="form-list">
                    <tbody>
                    <tr>
                        <td class="label"><label
                                    for="<?php echo $_code ?>_name"><?php echo $this->escapeHtml($this->__('Name')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_name"
                                                 name="<?php echo "payment[{$_code}][names]" ?>"
                                                 value="<?php if (array_key_exists('name', $_info)): echo $this->quoteEscape($_info['name']); endif; ?>"
                                                 class="input-text required-entry" type="text"></td>
                    </tr>
                    <tr>
                        <td class="label"><label
                                    for="<?php echo $_code ?>_pin"><?php echo $this->escapeHtml($this->__('PIN')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_pin"
                                                 name="<?php echo "payment[{$_code}][pin]" ?>"
                                                 value="<?php if (array_key_exists('pin', $_info)): echo $this->quoteEscape($_info['pin']); endif; ?>"
                                                 class="input-text required-entry" type="text"></td>
                    </tr>
                    <tr>
                        <td class="label"><label
                                    for="<?php echo $_code ?>_names"><?php echo $this->escapeHtml($this->__('Email')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_email"
                                                 name="<?php echo "payment[{$_code}][email]" ?>"
                                                 value="<?php if (array_key_exists('email', $_info)): echo $this->quoteEscape($_info['email']); endif; ?>"
                                                 class="input-text required-entry" type="text"></td>
                    </tr>
                    <tr>
                        <td class="label"><label
                                    for="<?php echo $_code ?>_phone"><?php echo $this->escapeHtml($this->__('Phone')) ?></label>
                        </td>
                        <td class="value"><input id="<?php echo $_code ?>_phone"
                                                 name="<?php echo "payment[{$_code}][phone]" ?>"
                                                 value="<?php if (array_key_exists('phone', $_info)): echo $this->quoteEscape($_info['phone']); endif; ?>"
                                                 class="input-text required-entry" type="text"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
