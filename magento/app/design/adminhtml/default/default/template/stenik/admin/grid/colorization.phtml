<?php
/**
 * @package  Stenik_Admin
 * <AUTHOR> Magento Team <<EMAIL>>
 */
?>

<?php
    $grid            = $this->getGrid();
    $colorCollection = $this->getColorCollection();

    if (!$grid || !$grid->getId() || !$colorCollection) {
        return;
    }

    $colorsData = array();

    foreach ($colorCollection as $color) {
        $colorsData[] = array(
            'row_identificator' => $color->getRowIdentificator(),
            'row_id'            => $color->getRowId(),
            'col_identificator' => $color->getColIdentificator(),
            'col_id'            => $color->getColId(),
            'color'             => $color->getColor(),
        );
    }
?>

<script>
    var colorsData = <?php echo json_encode($colorsData); ?>;

    jQuery(function() {
        var $grid = jQuery('#' + <?php echo json_encode($grid->getId()) ?> + '_table');
        var colorData;

        for (var i = colorsData.length - 1; i >= 0; i--) {
            colorData = colorsData[i];

            if (!colorData.color) {
                continue;
            }
            $allRows = $grid.find('> tbody > tr');

            var $row = null;
            if (colorData.row_id) {
                if (colorData.row_identificator) {
                    var $els = $grid.find(colorData.row_identificator);
                    $els.each(function() {
                        var $el = jQuery(this);
                        var elVal = jQuery.trim($el.val()) || jQuery.trim($el.text());
                        if (elVal == colorData.row_id) {
                            $row = $allRows.filter($el.parents('tr'));
                            return false;
                        }
                    });
                } else {
                    $row = $allRows.filter('tr:nth-child(' + parseInt(colorData.row_id) + ')');
                }
            }

            var $allCols = null
            if ($row) {
                $allCols = $row.find('td');
            } else {
                $allCols = $grid.find('> tbody > tr > td');
            }

            var $col = null;
            if (colorData.col_id) {
                if (colorData.col_identificator) {
                    var $els = $grid.find(colorData.col_identificator);
                    $els.each(function() {
                        var $el = jQuery(this);
                        var elVal = jQuery.trim($el.val()) || jQuery.trim($el.text());
                        if (elVal == colorData.col_id) {


                            $col = $allCols.filter($el.parents('td').add($el));
                            var index = $col.index() + 1;
                            if (index > 0) {
                                $col = $allCols.filter(':nth-child(' + parseInt(index) + ')');
                            }
                            return false;
                        }
                    });
                } else {
                    $col = $allCols.filter(':nth-child(' + parseInt(colorData.col_id) + ')');
                }
            }

            if ($col) {
                $col.css({'background-color' : colorData.color});
            } else if ($row) {
                $row.css({'background-color' : colorData.color});
            }
        };
    });
</script>