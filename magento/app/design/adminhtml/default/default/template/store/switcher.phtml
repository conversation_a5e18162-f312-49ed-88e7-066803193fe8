<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /* @var $this Mage_Core_Block_Template */ ?>
<?php if ($websites = $this->getWebsites()): ?>
<p class="switcher"><label for="store_switcher"><?php echo $this->__('Choose Store View:') ?></label>
<select name="store_switcher" id="store_switcher" onchange="return switchStore(this);">
<?php if ($this->hasDefaultOption()): ?>
    <option value=""><?php echo $this->escapeHtml($this->getDefaultStoreName()); ?></option>
<?php endif; ?>
    <?php foreach ($websites as $website): ?>
        <?php $showWebsite = false; ?>
        <?php foreach ($website->getGroups() as $group): ?>
            <?php $showGroup = false; ?>
            <?php foreach ($this->getStores($group) as $store): ?>
                <?php if ($showWebsite == false): ?>
                    <?php $showWebsite = true; ?>
                    <optgroup label="<?php echo $this->escapeHtml($website->getName()) ?>"></optgroup>
                <?php endif; ?>
                <?php if ($showGroup == false): ?>
                    <?php $showGroup = true; ?>
                    <optgroup label="&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($group->getName()) ?>">
                <?php endif; ?>
                <option value="<?php echo $this->escapeHtml($store->getId()) ?>"<?php if($this->getStoreId() == $store->getId()): ?> selected="selected"<?php endif; ?>>&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($store->getName()) ?></option>
            <?php endforeach; ?>
            <?php if ($showGroup): ?>
                </optgroup>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php endforeach; ?>
</select>
<?php echo $this->getHintHtml() ?>
</p>
<script type="text/javascript">
    function switchStore(obj) {
        var storeParam = obj.value ? 'store/' + obj.value + '/' : '';
        if (obj.switchParams) {
            storeParam += obj.switchParams;
        }
    <?php if ($this->getUseConfirm()): ?>
        if (confirm('<?php echo Mage::helper('core')->jsQuoteEscape($this->__('Please confirm site switching. All data that hasn\'t been saved will be lost.')) ?>')) {
            setLocation('<?php echo $this->getSwitchUrl() ?>' + storeParam);
            return true;
        } else {
            obj.value = '<?php echo $this->getStoreId() ?>';
        }
        return false;
    <?php else: ?>
        setLocation('<?php echo $this->getSwitchUrl() ?>' + storeParam);
    <?php endif; ?>
    }
</script>
<?php endif; ?>
