<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /*{
    "label":"Root page layout",
    "type":"core/template",
    "children":{
        "header":{ "label":"Header", "type":"adminhtml/page_header" },
        "menu":{ "label":"Top navigation", "type":"adminhtml/page_menu" },
        "breadcrumbs":{ "label":"Breadcrumbs", "type":"adminhtml/widget_breadcrumbs" },
        "content":{ "label":"Content block", "type":"core/template" },
        "left":{ "label":"Left navigation", "type":"core/template" },
        "footer":{ "label":"Footer", "type":"adminhtml/page_footer" }
    },
    "vars":{}
}*/ ?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="<?php echo $this->getLang() ?>" lang="<?php echo $this->getLang() ?>">
<head>
    <?php echo $this->getChildHtml('head') ?>
</head>

<body id="html-body"<?php echo $this->getBodyClass() ? ' class="' . $this->getBodyClass() . '"' : ''; ?>>
<?php echo $this->getChildHtml('notification_window'); ?>
<div class="wrapper">
    <?php echo $this->getChildHtml('global_notices') ?>
        <div class="header">
            <?php echo $this->getChildHtml('header') ?>
        <div class="clear"></div>
            <?php echo $this->getChildHtml('menu') ?>
        </div>
        <?php echo $this->getChildHtml('notifications'); ?>
        <div class="middle" id="anchor-content">
            <div id="page:main-container">
            <?php if($this->getChildHtml('left')): ?>

                <div class="columns <?php echo $this->getContainerCssClass() ?>">
                    <div class="side-col" id="page:left">
                        <?php echo $this->getChildHtml('left') ?>
                    </div>
                    <div class="main-col" id="content">
                        <div class="main-col-inner">
                            <div id="messages"><?php echo $this->getMessagesBlock()->toHtml() ?></div>
                            <?php echo $this->getChildHtml('content') ?>
                        </div>
                    </div>
                </div>

            <?php else: ?>
                <div id="messages"><?php echo $this->getMessagesBlock()->toHtml() ?></div>
                <?php echo $this->getChildHtml('content') ?>
            <?php endif; ?>
            </div>
        </div>
        <div class="footer">
            <?php echo $this->getChildHtml('footer') ?>
        </div>
    </div>
    <?php echo $this->getChildHtml('js') ?>
    <?php echo $this->getChildHtml('profiler') ?>
<div id="loading-mask" style="display:none">
    <p class="loader" id="loading_mask_loader"><img src="<?php echo $this->getSkinUrl('images/ajax-loader-tr.gif') ?>" alt="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('adminhtml')->__('Loading...')) ?>"/><br/><?php echo Mage::helper('adminhtml')->__('Please wait...') ?></p>
</div>

<?php echo $this->getChildHtml('before_body_end') ?>

</body>
</html>
