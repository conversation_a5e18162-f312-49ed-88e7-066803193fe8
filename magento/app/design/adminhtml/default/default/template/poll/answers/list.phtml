<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div id="answers_container">
<?php if( !$answers ): ?>
<?php else: ?>
    <div class="entry-edit-head">
    <h4 class="icon-head head-edit-form fieldset-legend"><?php echo Mage::helper('poll')->__('Assigned Answers') ?></h4>
    </div>
    <?php foreach($answers->getItems() as $_item): ?>
        <div class="fieldset fieldset-wide" id="poll_answer_fieldset_<?php echo $_item->getId() ?>">
        <table cellspacing="0" class="form-list">
            <tr>
                <td class="label"><label for="answer_<?php echo $_item->getId() ?>"><?php echo Mage::helper('poll')->__('Answer Title:') ?> <span class="required">*</span></label></td>
                <td class="value"><input type="text" id="answer_<?php echo $_item->getId() ?>" name="answer[<?php echo $_item->getId() ?>][title]" value="<?php echo $this->escapeHtml($_item->getAnswerTitle()) ?>" class="input-text required-entry" /></td>
            </tr>
            <tr>
                <td class="label"><label for="answer_votes_<?php echo $_item->getId() ?>"><?php echo Mage::helper('poll')->__('Votes Count:') ?> <span class="required">*</span></label></td>
                <td class="value"><input type="text" id="answer_votes_<?php echo $_item->getId() ?>" name="answer[<?php echo $_item->getId() ?>][votes]" value="<?php echo $_item->getVotesCount() ?>" class="input-text required-entry validate-not-negative-number" />
                    <p class="a-left" style="margin:7px 0 0;"><?php echo $this->getDeleteButtonHtml() ?></p>
                </td>
             </tr>
        </table>
        <input type="hidden" id="id_poll_answer_fieldset_<?php echo $_item->getId() ?>" value="<?php echo $_item->getId() ?>" />
        </div>
    <?php endforeach; ?>
<?php endif; ?>
</div>
<p class="a-right" style="margin:7px 0 0;"><?php echo $this->getAddButtonHtml(); ?></p>

<script type="text/javascript">
//<![CDATA[
    var lastId = 0;
    var answer = function() {
        return {
            del : function(obj) {
                if( confirm('<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('poll')->__('Are you sure you want to delete it?')) ?>') ) {
                    fieldSet = obj.up('div.fieldset');
                    if( $('id_' + fieldSet.id) ) {
                        var itemId = $('id_' + fieldSet.id).value;
                        var newElement = document.createElement('input');
                        newElement.type = 'hidden';
                        newElement.value = itemId;
                        newElement.name = 'deleteAnswer[]';
                        $('answers_container').appendChild(newElement);
                    }
                    fieldSet.remove();
                }
            },

            add : function(obj) {
                var newElement = document.createElement('div');
                newElement.innerHTML = this.getTemplate();
                $('answers_container').appendChild(newElement);
            },

            getTemplate : function() {
                var id = this.createId();
                return  '<div class="fieldset fieldset-wide" id="poll_answer_fieldset_' + id + '">'
                       +'<table cellspacing="0" class="form-list">'
                       +'<tr>'
                       +    '<td class="label"><label for="answer_' + id + '"><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('poll')->__('Answer Title:')) ?> <span class="required">*</span></label><\/td>'
                       +    '<td class="value"><input type="text" id="answer_' + id + '" name="answer[' + id + '][title]" value="" class="input-text required-entry" /><\/td>'
                       +'<\/tr>'
                       +'<tr>'
                       +    '<td class="label"><label for="answer_votes_' + id + '"><?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('poll')->__('Votes Count:')) ?> <span class="required">*</span></label><\/td>'
                       +    '<td class="value"><input type="text" id="answer_votes_' + id + '" name="answer[' + id + '][votes]" value="0" class="input-text required-entry validate-not-negative-number" />'
                       +        '<p class="a-left" style="margin:7px 0 0;"><?php echo Mage::helper('core')->quoteEscape($this->getDeleteButtonHtml()) ?></p>'
                       +'<\/td>'
                       +'<\/tr>'
                       +'<\/table>'
                       +'<input type="hidden" id="id_poll_answer_fieldset_' + id + '" value="' + id + '" />'
                       +'<\/div>';
            },

            createId : function () {
                return --lastId;
            }
        }
    }();
//]]>
</script>
