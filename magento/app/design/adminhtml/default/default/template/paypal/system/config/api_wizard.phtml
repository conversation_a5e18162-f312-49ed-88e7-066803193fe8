<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Paypal_Block_Adminhtml_System_Config_ApiWizard
 */
?>
<div class="pp-buttons-container">
    <button onclick="javascript:window.open('<?php echo $this->getButtonUrl()?>', 'apiwizard','toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=yes, resizable=yes, ,left=100, top=100, width=380, height=470'); return false;" class="scalable" type="button" id="<?php echo $this->getHtmlId() ?>">
        <span><span><span><?php echo $this->escapeHtml($this->getButtonLabel()); ?></span></span></span>
    </button>
    <button onclick="javascript:window.open('<?php echo $this->getSandboxButtonUrl()?>', 'apiwizard','toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=yes, resizable=yes, ,left=100, top=100, width=380, height=470'); return false;" class="scalable" type="button" id="<?php echo $this->getSandboxHtmlId() ?>">
        <span><span><span><?php echo $this->escapeHtml($this->getSandboxButtonLabel()); ?></span></span></span>
    </button>
</div>
