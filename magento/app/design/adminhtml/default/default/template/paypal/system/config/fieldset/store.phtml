<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Paypal_Block_System_Config_Fieldset_Store
 */
?>
<script type="text/javascript">

Event.observe(window, 'load', function () {
    var pConfigDisabler = new PaypalConfigDisabler;
    var disabledMethods = <?php echo Mage::helper('core')->jsonEncode($this->getPaypalDisabledMethods()); ?>;
    pConfigDisabler.disableMethods(disabledMethods);
});

PaypalConfigDisabler = Class.create();
PaypalConfigDisabler.prototype = {
    initialize: function () {
        this.methods = $H({
            express: $H({fieldset: 'express'}),
            wps: $H({fieldset: 'wps'}),
            wpp: $H({fieldset: 'wpp'}),
            wpppe: $H({fieldset: 'wpp_pe'}),
            expresspe: $H({fieldset: 'express_pe'}),
            verisign: $H({fieldset: 'verisign'}),
            wpppl: $H({fieldset: 'payflow_link'}),
            hosted_pro: $H({fieldset: 'hosted_pro'})
        });
    },

    getMethods: function ()
    {
        return this.methods;
    },

    getMethod: function (method)
    {
        return this.methods.get(method);
    },

    getMethodFieldset: function (method)
    {
        var fieldsetId = 'paypal_' + this.getMethod(method).get('fieldset');
        return $(fieldsetId);
    },

    disableMethod: function (method)
    {
        this.hideFieldset(this.getMethodFieldset(method));
    },

    disableMethods: function (methods)
    {
        for(var methodId in methods) {
            this.disableMethod(methodId);
        }
    },

    hideFieldset: function (fieldset)
    {
        this.disableValueElements(fieldset);
        fieldset.hide();
        var heading = fieldset.previous('div');
        var headingLink = heading.down('a');
        heading.addClassName('disabled');
        headingLink.onclick = "return false;";
        headingLink.href = "javascript:void(0)";
    },

    disableValueElements: function (container)
    {
        $(container).select('select', 'input', 'textarea', 'button').each(function (elem) {
            elem.addClassName('disabled');
        });
    }
}
</script>
