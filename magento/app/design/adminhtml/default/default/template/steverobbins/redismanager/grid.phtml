<?php
/**
 * Redis Management Module
 * 
 * @category   Steverobbins
 * @package    <PERSON><PERSON><PERSON>ins_Redismanager
 * <AUTHOR> <<EMAIL>>
 * @copyright  Copyright (c) 2014 <PERSON> (https://github.com/steverobbins)
 * @license    http://creativecommons.org/licenses/by/3.0/deed.en_US Creative Commons Attribution 3.0 Unported License
 */
/**
 * @var Stevero<PERSON>ins_Redismanager_Block_Adminhtml_Manager $this
 */
$_services = $this->getSortedServices();
?>
<button id="flushAll" title="<?php echo $this->__('Flush All') ?>" type="button" class="scalable delete right" onclick="setLocation('<?php echo $this->getUrl('*/*/flushAll') ?> ')"><span><span><span><?php echo $this->__('Flush All') ?></span></span></span></button>
<div class="content-header">
    <h3><?php echo $this->__('Databases') ?></h3>
</div>
<form action="<?php echo $this->getUrl('*/*/mass') ?>" method="post" id="redisServices">
    <input name="form_key" type="hidden" value="<?php echo $this->getFormKey() ?>" />
    <table cellspacing="0" cellpadding="0" class="massaction">
        <tbody>
        <tr>
            <td>
                <a href="#" onclick="return redisCheckAll()">Select All</a>
                <span class="separator">|</span>
                <a href="#" onclick="return redisCheckNone()">Unselect All</a>
            </td>
            <td>
                <div class="right">
                    <div class="entry-edit">
                        <fieldset>
                            <span class="field-row">
                                <label>Actions</label>
                                <select id="redis_grid_massaction-select" class="required-entry select absolute-advice local-validation">
                                    <option value="flushdb">Flush DBs</option>
                                </select>
                            </span>
                            <span class="field-row">
                                <button title="Submit" class="scalable"><span><span><span>Submit</span></span></span></button>
                            </span>
                        </fieldset>
                    </div>
                </div>
            </td>
        </tbody>
    </table>
    <table class="services">
        <col width="15" />
        <col />
        <col />
        <col />
        <col />
        <col />
        <col />
        <col />
        <col />
        <col />
        <col />
        <col width="80" />
        <thead>
            <tr>
                <th>&nbsp;</th>
                <th><?php echo $this->__('Host') ?></th>
                <th><?php echo $this->__('Port') ?></th>
                <th><?php echo $this->__('Uptime') ?></th>
                <th><?php echo $this->__('Connections') ?></th>
                <th><?php echo $this->__('Memory / Peak') ?></th>
                <th><?php echo $this->__('Role') ?></th>
                <th><?php echo $this->__('Last Save') ?></th>
                <th colspan="3"><?php echo $this->__('Details') ?></th>
                <th>&nbsp;</th>
            </tr>
        </thead>
        <tbody>
            <?php if (count($_services)): ?>
            <?php foreach ($_services as $_host => $_service): ?>
            <tr onclick="redisCheckRow(this)">
                <td></td>
                <td><?php echo $_service['host'] ?></td>
                <td><?php echo $_service['port'] ?></td>
                <td><?php echo $_service['uptime'] ?></td>
                <td><?php echo $_service['connections'] ?></td>
                <td><?php echo $_service['memory'] ?></td>
                <td><?php echo $_service['role'] ?></td>
                <td><?php echo $_service['lastsave'] ?></td>
                <td colspan="3">&nbsp;</td>
                <td onclick="return false"><button class="delete" onclick="window.location = '<?php
                    echo $this->getUrl('*/*/flushAll', array(
                        '_query' => array(
                            'host' => $_host
                        )
                    ))
                ?>'; return false;"><?php echo $this->__("Flush All") ?></button></td>
            </tr>
            <?php foreach ($_service['services'] as $_id => $_db): ?>
            <tr onclick="redisCheckRow(this)">
                <td><input type="checkbox" name="service[]" value="<?php echo $_id ?>" /></td>
                <td colspan="7">&nbsp;</td>
                <td>
                    <strong><?php echo $this->__('Name') ?>:</strong> <?php echo $_db['name'] ?>
                </td>
                <td>
                    <strong><?php echo $this->__('Database') ?>:</strong> <?php echo $_db['db'] ?>
                </td>
                <td>
                    <strong><?php echo $this->__('Keys') ?>:</strong> <?php echo $_db['keys'] ?>
                </td>
                <td onclick="return false"><button onclick="window.location = '<?php
                    echo $this->getUrl('*/*/flushDb', array(
                        '_query' => array(
                            'id' => $_id
                        )
                    ))
                ?>'; return false;"><?php echo $this->__("Flush DB") ?></button></td>
            </tr>
            <?php endforeach; ?>
            <?php endforeach; ?>
            <?php else: ?>
            <tr>
                <td class="empty-text a-center" colspan="100"><?php echo $this->__('No Redis services were found.') ?></td>
            </tr>
            <?php endif ?>
        </tbody>
    </table>
</form>
<script>
// <![CDATA[
redisForm = $('redisServices');
// ]]>
</script>
