<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Adminhtml_Block_Notification_Window
 */
?>
<?php if ($this->canShow()): ?>
<script type="text/javascript">
//<![CDATA[
    var messagePopupClosed = false;
    function openMessagePopup() {
        var height = $('html-body').getHeight();
        $('message-popup-window-mask').setStyle({'height':height+'px'});
        toggleSelectsUnderBlock($('message-popup-window-mask'), false);
        Element.show('message-popup-window-mask');
        $('message-popup-window').addClassName('show');
    }

    function closeMessagePopup() {
        toggleSelectsUnderBlock($('message-popup-window-mask'), true);
        Element.hide('message-popup-window-mask');
        $('message-popup-window').removeClassName('show');
        messagePopupClosed = true;
    }

    Event.observe(window, 'load', openMessagePopup);
    Event.observe(window, 'keyup', function(evt) {
        if(messagePopupClosed) return;
        var code;
        if (evt.keyCode) code = evt.keyCode;
        else if (evt.which) code = evt.which;
        if (code == Event.KEY_ESC) {
            closeMessagePopup();
        }
    });
//]]>
</script>
<div id="message-popup-window-mask" style="display:none;"></div>
<div id="message-popup-window" class="message-popup">
    <div class="message-popup-head">
        <a href="#" onclick="closeMessagePopup(); return false;" title="<?php echo $this->getCloseText(); ?>"><span><?php echo $this->getCloseText(); ?></span></a>
        <h2><?php echo $this->getHeaderText(); ?></h2>
    </div>
    <div class="message-popup-content">
        <div class="message">
            <span class="message-icon message-<?php echo $this->getSeverityText(); ?>" style="background-image:url(<?php echo $this->escapeUrl($this->getSeverityIconsUrl()); ?>);"><?php echo $this->getSeverityText(); ?></span>
            <p class="message-text"><?php echo $this->getNoticeMessageText(); ?></p>
        </div>
        <p class="read-more"><a href="<?php echo $this->getNoticeMessageUrl(); ?>" onclick="this.target='_blank';"><?php echo $this->getReadDetailsText(); ?></a></p>
    </div>
</div>
<?php endif; ?>
