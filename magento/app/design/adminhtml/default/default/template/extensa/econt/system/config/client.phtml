<?php $_htmlId = 'extensa_econt'; ?>

<script type="text/javascript">
    $('carriers_<?php echo $_htmlId; ?>_client_id').observe('change', function (evenet) {
        if (evenet.target.value !== '') {
            new Ajax.Request(
                '<?php echo $this->getUrlClient(); ?>',
                {
                    method: 'post',
                    parameters: {
                        client_id: evenet.target.value,
                    },
                    onSuccess: function(transport) {
                        if (transport.responseText.isJSON()) {
                            var response = transport.responseText.evalJSON();

                            $('carriers_<?php echo $_htmlId; ?>_key_word').setValue(response['keyword']);
                            html = '<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('моля, изберете')); ?></option>';
                            html += '<option value="0"><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Не')); ?></option>';
                            html += '<option value="1"><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Да в брой (на подателя по пратката)')); ?></option>';

                            var agreementsCount = response['agreements'].length;
                            for (var i = 0; i < agreementsCount; i++) {
                                html += '<option value="cd' + response['agreements'][i] + '"><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Да по споразумение')); ?> ' + response['agreements'][i] + '</option>'; 
                            }
                            
                            $('carriers_<?php echo $_htmlId; ?>_cd').update(html);

                            var takeInstructions = '<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Кликнете Синхронизирай данни--')); ?></option>'
                            var giveInstructions = '<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Кликнете Синхронизирай данни--')); ?></option>'
                            var returnInstructions = '<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Кликнете Синхронизирай данни--')); ?></option>'

                            for (var take_id in response['instructions']['take']) {
                                takeInstructions += '<option value="' + response['instructions']['take'][take_id] + '">' + response['instructions']['take'][take_id] + '</option>';
                            }

                            for (var give_id in response['instructions']['give']) {
                                giveInstructions += '<option value="' + response['instructions']['give'][give_id] + '">' + response['instructions']['give'][give_id] + '</option>';
                            }

                            for (var return_id in response['instructions']['return']) {
                                returnInstructions += '<option value="' + response['instructions']['return'][return_id] + '">' + response['instructions']['return'][return_id] + '</option>';
                            }

                            $('carriers_extensa_econt_instructions_id_take').update(takeInstructions);
                            $('carriers_extensa_econt_instructions_id_give').update(giveInstructions);
                            $('carriers_extensa_econt_instructions_id_return').update(returnInstructions);
                        }
                    }
                }
            );
        }
    });
</script>
