<?php
/**
 * <AUTHOR> Web Development Ltd. <<EMAIL>>
 */
?>
<?php
/**
 * @var $this Extensa_Econt_Block_Adminhtml_System_Config_Form_Getcdagreementnum
 */
?>
<script type="text/javascript">
    //<![CDATA[
    function extensa_econt_get_cd_agreement_num() {
        $('carriers_extensa_econt_cd_agreement_num_id').update('<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Please wait...')); ?></option>');

        new Ajax.Request(
            '<?php echo $this->getGetCDAgreementNumUrl(); ?>',
            {
                method:     'post',
                parameters: {
                    username: $F('carriers_extensa_econt_username'),
                    password: $F('carriers_extensa_econt_password'),
                    test    : $F('carriers_extensa_econt_test')
                },
                onSuccess: function(transport) {
                    if (transport.responseText.isJSON()) {
                        response = transport.responseText.evalJSON();
                        if (response.cd_agreement_nums) {
                            html = '<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Please Select--')); ?></option>';

                            for (i = 0; i < response.cd_agreement_nums.length; i++) {
                                if (response.cd_agreement_nums[i] && response.cd_agreement_nums[i].length) {
                                    html += '<option value="' + response.cd_agreement_nums[i] + '">' + response.cd_agreement_nums[i] + '</option>';
                                }
                            }

                            $('carriers_extensa_econt_cd_agreement_num_id').update(html);
                            $$('select#carriers_extensa_econt_cd_agreement_num_id option').each(function(o) {
                                if (o.readAttribute('value') == $F('carriers_extensa_econt_cd_agreement_num')) {
                                    o.selected = true;
                                    throw $break;
                                }
                            });
                        } else if (response.error) {
                            alert(response.message);
                        }
                    }
                }
            }
        );
    }

    document.observe('dom:loaded', function() {
        $('carriers_extensa_econt_cd_agreement_num_id').observe('change', function() {
            $('carriers_extensa_econt_cd_agreement_num').setValue($F(this));
        });
    });
    //]]>
</script>
