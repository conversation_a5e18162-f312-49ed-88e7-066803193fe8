<script type="text/javascript">
    document.observe('dom:loaded', function() {
        $('<?php echo $this->getElement()->getId(); ?>_enabled').observe('change', function() {
            econt_hideShippingPaymentTable();
        });

        econt_hideShippingPaymentTable();

        function econt_hideShippingPaymentTable() {
            if ($F('<?php echo $this->getElement()->getId(); ?>_enabled') == 0 || !$('row_<?php echo $this->getElement()->getId(); ?>_enabled').visible()) {
                $('row_<?php echo $this->getElement()->getId(); ?>').hide();
            } else {
                $('row_<?php echo $this->getElement()->getId(); ?>').show();
            }
        }
    });

    Validation.add('is-unique', Translator.translate('Приоритет полетата трябва да са уникални.'), function(v, e) {
        var inputs = document.getElementsByClassName('is-unique');
        for (var i = 0; i < inputs.length; i++) {
            var item = inputs[i];
            if (e.name === item.name) {
                continue;
            }

            if (item.value === e.value) {
                return false;
            }
        }

        return true;
    });

    function extensa_econt_clear_conditions() {
        $('row_<?php echo $this->getElement()->getId(); ?>').select('tr[id^="_"]').invoke('remove');
    }
</script>