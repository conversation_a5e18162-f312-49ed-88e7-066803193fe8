<?php
/**
 * <AUTHOR> Web Development Ltd. <<EMAIL>>
 */
?>
<?php /** @var $this Extensa_Econt_Block_Checkout_Onepage_Shipping_Method_Econt */ ?>
<?php //if (Mage::helper('extensa_econt')->getStoreConfigFlag('active')): ?>
<?php $_htmlId = 'extensa_econt'; //$this->_alias; ?>
<?php $_receiver_address = $this->getReceiverAddress(); ?>
<?php $_error = $this->getError(); ?>
<?php $_timePriorities = Mage::helper('extensa_econt')->getPriorityTimeTypes(); ?>
<?php $_officePrice = Mage::helper('core')->currency($this->getOfficePrice(), true, false); ?>
<?php $_apsPrice = Mage::helper('core')->currency($this->getApsPrice(), true, false); ?>
<div id="<?php echo $_htmlId; ?>-form">
    <input type="hidden" name="shipping_method_name" value="<?php echo $_htmlId; ?>" />
    <?php if (!empty($_error['message']) && empty($_error['fixed'])): ?><p class="error-msg"><?php echo $this->htmlEscape($_error['message']); ?></p><?php endif; ?>
    <ul class="form-list">
        <li class="fields">
            <div class="field" <?php if (!$this->getCd()): ?> style="display: none;"<?php endif; ?>>
                <label for="<?php echo $_htmlId; ?>:cd_payment"><?php echo Mage::helper('extensa_econt')->__('Наложен платеж'); ?></label>
                <div>
                    <select id="<?php echo $_htmlId; ?>:cd_payment" name="<?php echo $_htmlId; ?>[cd_payment]" class="select <?php echo $_htmlId; ?>-required-cd-aps">
                        <option value="1" <?php if ($this->getCdPayment()): ?> selected="selected"<?php endif; ?>><?php echo Mage::helper('extensa_econt')->__('Yes'); ?></option>
                        <option value="0" <?php if (!$this->getCdPayment()): ?> selected="selected"<?php endif; ?>><?php echo Mage::helper('extensa_econt')->__('No'); ?></option>
                    </select>
                </div>
            </div>
            <div class="field" style="display: none;">
                <label for="<?php echo $_htmlId; ?>:shipping_to"><?php echo Mage::helper('extensa_econt')->__('Доставка'); ?></label>
                <div>
                    <select id="<?php echo $_htmlId; ?>:shipping_to" name="<?php echo $_htmlId; ?>[shipping_to]" class="select">
                        <option value="OFFICE" <?php if ($this->getShippingTo() == 'OFFICE'): ?> selected="selected"<?php endif; ?>><?php echo Mage::helper('extensa_econt')->__('до офис'); ?></option>
                        <option value="DOOR" <?php if ($this->getShippingTo() == 'DOOR'): ?> selected="selected"<?php endif; ?>><?php echo Mage::helper('extensa_econt')->__('до врата'); ?></option>
                        <option value="APS" <?php if ($this->getShippingTo() == 'APS'): ?> selected="selected"<?php endif; ?>><?php echo Mage::helper('extensa_econt')->__('до АПС'); ?></option>
                    </select>
                </div>
            </div>
        </li>
        <li>
            <fieldset>
                <ul>
                    <li class="fields">
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:country"><?php echo Mage::helper('extensa_econt')->__('Държава'); ?></label>
                            <div class="input-box">
                                <select title="<?php echo Mage::helper('extensa_econt')->__('Държава'); ?>" id="<?php echo $_htmlId; ?>:country" name="<?php echo $_htmlId; ?>[country_id]" class="select <?php echo $_htmlId; ?>-required-country">
                                    <option value=""><?php echo Mage::helper('extensa_econt')->__('--Please Select--'); ?></option>
                                    <?php foreach ($this->getCountries() as $_country): ?>
                                    <option value="<?php echo $_country->getId(); ?>" <?php if ($this->getCountry() == $_country->getId()): ?> selected="selected"<?php endif; ?>><?php echo $_country->getName(); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </li>
                </ul>
            </fieldset>
        </li>
        <?php if ($this->getShippingTo() == 'OFFICE' && $this->getToOffice()): ?>
        <li class="fields" id="<?php echo $_htmlId; ?>-to_office">
            <fieldset>
                <ul>
                    <li class="fields">
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:office_city_id"><?php echo Mage::helper('extensa_econt')->__('Населено място'); ?></label>
                            <div class="input-box">
                                <select title="<?php echo Mage::helper('extensa_econt')->__('Населено място'); ?>" id="<?php echo $_htmlId; ?>:office_city_id" name="<?php echo $_htmlId; ?>[office_city_id]" class="select <?php echo $_htmlId; ?>-required-office-city">
                                    <option value=""><?php echo Mage::helper('extensa_econt')->__('--Please Select--'); ?></option>
                                    <?php foreach ($this->getCities() as $_city): ?>
                                    <option value="<?php echo $_city->getCityId(); ?>" <?php if ($this->getOffice() && ($this->getOffice()->getCityId() == $_city->getCityId()) || !$this->getOffice() && !empty($_receiver_address['office_city_id']) && ($_receiver_address['office_city_id'] == $_city->getCityId()) || !$this->getOffice() && ($_receiver_address['city_id'] == $_city->getCityId())): ?> selected="selected"<?php endif; ?>><?php echo $_city->getName(); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="field">
                            <label>&nbsp;</label>
                            <div class="input-box">
                                <button type="button" class="button" id="<?php echo $_htmlId; ?>:office_locator" title="<?php echo Mage::helper('extensa_econt')->__('Офис Локатор'); ?>"><span><span><?php echo Mage::helper('extensa_econt')->__('Офис Локатор'); ?></span></span></button>
                            </div>
                        </div>
                    </li>
                    <li class="fields">
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:office_id"><?php echo Mage::helper('extensa_econt')->__('Офис'); ?></label>
                            <div class="input-box">
                                <select title="<?php echo Mage::helper('extensa_econt')->__('Офис'); ?>" id="<?php echo $_htmlId; ?>:office_id" name="<?php echo $_htmlId; ?>[office_id]" class="select <?php echo $_htmlId; ?>-required-office">
                                    <option value=""><?php echo Mage::helper('extensa_econt')->__('--Please Select--'); ?></option>
                                    <?php foreach ($this->getOffices() as $_office): ?>
                                    <option value="<?php echo $_office->getOfficeId(); ?>" <?php if ($this->getOffice() && ($this->getOffice()->getOfficeId() == $_office->getOfficeId())): ?> selected="selected"<?php endif; ?>><?php echo $_office->getOfficeCode() . ', ' . $_office->getName() . ', ' . $_office->getAddress(); ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:office_code"><?php echo Mage::helper('extensa_econt')->__('Код на офиса'); ?></label>
                            <div class="input-box">
                                <input type="text" title="<?php echo Mage::helper('extensa_econt')->__('Код на офиса'); ?>" id="<?php echo $_htmlId; ?>:office_code" name="<?php echo $_htmlId; ?>[office_code]" value="<?php if ($this->getOffice()): echo $this->getOffice()->getOfficeCode(); endif; ?>" class="input-text" disabled="disabled" />
                            </div>
                        </div>
                    </li>
                </ul>
            </fieldset>
            <div class="<?php echo $_htmlId; ?>_aps_info" <?php if ($this->getShippingTo() != 'APS'): ?> style="display: none;"<?php endif; ?>>
                <p><i><?php echo Mage::helper('extensa_econt')->__('Ако размерите на избрания от вас продукт/и надвишава/т максимално допустимите за доставка до Еконтомат (61см/44см/37см), пратката ще бъде пренасочена до офис или адрес по ваш избор.'); ?></i></p>
            </div>
        </li>
        <?php endif; ?>
        <?php if ($this->getShippingTo() == 'DOOR' && $this->getToDoor()): ?>
        <li class="fields" id="<?php echo $_htmlId; ?>-to_door">
            <fieldset>
                <ul>
                    <li class="fields">
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:city"><?php echo Mage::helper('extensa_econt')->__('Населено място'); ?></label>
                            <div class="input-box" style="position: relative;">
                                <input type="text" title="<?php echo Mage::helper('extensa_econt')->__('Населено място'); ?>" id="<?php echo $_htmlId; ?>:city" name="<?php echo $_htmlId; ?>[city]" value="<?php echo $this->htmlEscape($_receiver_address['city']); ?>" class="input-text <?php echo $_htmlId; ?>-required-city" /><input type="hidden" id="<?php echo $_htmlId; ?>:city_id" name="<?php echo $_htmlId; ?>[city_id]" value="<?php echo $this->htmlEscape($_receiver_address['city_id']); ?>" />
                                <span id="<?php echo $_htmlId; ?>:city_indicator" class="please-wait" style="display: none; position: absolute; right: 2px; top: 0;">
                                    <img src="<?php echo $this->getSkinUrl('images/opc-ajax-loader.gif'); ?>" alt="<?php echo Mage::helper('extensa_econt')->__('Loading...'); ?>" title="<?php echo Mage::helper('extensa_econt')->__('Loading...') ?>" class="v-middle" />
                                </span>
                                <div id="<?php echo $_htmlId; ?>:city_autocomplete" class="autocomplete" style="display: none; top: 20px !important; left: 0px !important; position: absolute !important;"></div>
                            </div>
                        </div>
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:post_code"><?php echo Mage::helper('extensa_econt')->__('Пощенски код'); ?></label>
                            <div class="input-box">
                                <input type="text" title="<?php echo Mage::helper('extensa_econt')->__('Пощенски код'); ?>" id="<?php echo $_htmlId; ?>:post_code" name="<?php echo $_htmlId; ?>[postcode]" value="<?php echo $this->htmlEscape($_receiver_address['post_code']); ?>" class="input-text disabled" readonly="readonly" />
                            </div>
                        </div>
                    </li>
                    <li class="fields">
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:quarter"><?php echo Mage::helper('extensa_econt')->__('Квартал'); ?></label>
                            <div class="input-box" style="position: relative;">
                                <input type="text" title="<?php echo Mage::helper('extensa_econt')->__('Квартал'); ?>" id="<?php echo $_htmlId; ?>:quarter" name="<?php echo $_htmlId; ?>[quarter]" value="<?php echo $this->htmlEscape($_receiver_address['quarter']); ?>" class="input-text <?php echo $_htmlId; ?>-required-quarter" />
                                <span id="<?php echo $_htmlId; ?>:quarter_indicator" class="please-wait" style="display: none; position: absolute; right: 2px; top: 0;">
                                    <img src="<?php echo $this->getSkinUrl('images/opc-ajax-loader.gif'); ?>" alt="<?php echo Mage::helper('extensa_econt')->__('Loading...'); ?>" title="<?php echo Mage::helper('extensa_econt')->__('Loading...') ?>" class="v-middle" />
                                </span>
                                <div id="<?php echo $_htmlId; ?>:quarter_autocomplete" class="autocomplete" style="display: none; top: 20px !important; left: 0px !important; position: absolute !important;"></div>
                            </div>
                        </div>
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:other"><?php echo Mage::helper('extensa_econt')->__('Друго'); ?></label>
                            <div class="input-box">
                                <input type="text" title="<?php echo Mage::helper('extensa_econt')->__('Друго'); ?>" id="<?php echo $_htmlId; ?>:other" name="<?php echo $_htmlId; ?>[other]" value="<?php echo $this->htmlEscape($_receiver_address['other']); ?>" class="input-text <?php echo $_htmlId; ?>-required-other" />
                            </div>
                        </div>
                    </li>
                    <li class="fields">
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:street"><?php echo Mage::helper('extensa_econt')->__('Улица'); ?></label>
                            <div class="input-box" style="position: relative;">
                                <input type="text" title="<?php echo Mage::helper('extensa_econt')->__('Улица'); ?>" id="<?php echo $_htmlId; ?>:street" name="<?php echo $_htmlId; ?>[street]" value="<?php echo $this->htmlEscape($_receiver_address['street']); ?>" class="input-text <?php echo $_htmlId; ?>-required-street" />
                                <span id="<?php echo $_htmlId; ?>:street_indicator" class="please-wait" style="display: none; position: absolute; right: 2px; top: 0;">
                                    <img src="<?php echo $this->getSkinUrl('images/opc-ajax-loader.gif'); ?>" alt="<?php echo Mage::helper('extensa_econt')->__('Loading...'); ?>" title="<?php echo Mage::helper('extensa_econt')->__('Loading...') ?>" class="v-middle" />
                                </span>
                                <div id="<?php echo $_htmlId; ?>:street_autocomplete" class="autocomplete" style="display: none; top: 20px !important; left: 0px !important; position: absolute !important;"></div>
                            </div>
                        </div>
                        <div class="field">
                            <label for="<?php echo $_htmlId; ?>:street_num"><?php echo Mage::helper('extensa_econt')->__('Номер'); ?></label>
                            <div class="input-box">
                                <input type="text" title="<?php echo Mage::helper('extensa_econt')->__('Номер'); ?>" id="<?php echo $_htmlId; ?>:street_num" name="<?php echo $_htmlId; ?>[street_num]" value="<?php echo $this->htmlEscape($_receiver_address['street_num']); ?>" class="input-text <?php echo $_htmlId; ?>-required-street-num" />
                            </div>
                        </div>
                    </li>
                    <li class="control" id="<?php echo $_htmlId; ?>-priority_time" <?php if (!$this->getPriorityTime()) { ?> style="display: none;"<?php } ?>>
                        <div>
                            <input type="checkbox" title="<?php echo Mage::helper('extensa_econt')->__('Точен час на доставка'); ?>" id="<?php echo $_htmlId; ?>:priority_time_cb" name="<?php echo $_htmlId; ?>[priority_time_cb]" value="1" class="checkbox" <?php if ($this->getPriorityTime() && $this->getPriorityTimeCb()): ?> checked="checked"<?php endif; ?> /><label for="<?php echo $_htmlId; ?>:priority_time_cb"><?php echo Mage::helper('extensa_econt')->__('Точен час на доставка'); ?></label>
                        </div>
                        <div class="field">
                            <select id="<?php echo $_htmlId; ?>:priority_time_type_id" name="<?php echo $_htmlId; ?>[priority_time_type_id]" class="select" <?php if (!$this->getPriorityTimeCb()): ?> disabled="disabled"<?php endif; ?>>
                                <?php foreach ($_timePriorities as $_priority_time_type => $_priority_time_type_value): ?>
                                <option value="<?php echo $_priority_time_type; ?>" <?php if ($_priority_time_type == $this->getPriorityTimeTypeId()): $_priority_time_hours = $_priority_time_type; ?> selected="selected"<?php endif; ?>><?php echo $_priority_time_type_value['name']; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="field" id="<?php echo $_htmlId; ?>:priority_time_type_in"<?php if ($this->getPriorityTimeTypeId() != 'IN'): ?> style="display: none;"<?php endif; ?>>
                            <select id="<?php echo $_htmlId; ?>:priority_time_hour_id" name="<?php echo $_htmlId; ?>[priority_time_hour_id]" class="select" <?php if (!$this->getPriorityTimeCb()): ?> disabled="disabled"<?php endif; ?>>
                                <?php foreach ($_timePriorities['IN']['hours'] as $_priority_time_hour): ?>
                                <option value="<?php echo $_priority_time_hour; ?>" <?php if ($_priority_time_hour == $this->getPriorityTimeHourId()): ?> selected="selected"<?php endif; ?>><?php echo $_priority_time_hour; ?> <?php echo Mage::helper('extensa_econt')->__('ч.'); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="field"id="<?php echo $_htmlId; ?>:priority_time_type_between"<?php if ($this->getPriorityTimeTypeId() != 'BETWEEN'): ?> style="display: none;"<?php endif; ?>>
                        <?php if (isset($_priority_time_hours['hours2'])) { echo Mage::helper('extensa_econt')->__('От:'); } ?>
                            <select id="<?php echo $_htmlId; ?>:priority_time_hour_id_from" name="<?php echo $_htmlId; ?>[priority_time_hour_id_from]" class="select small" <?php if (!$this->getPriorityTimeCb()): ?> disabled="disabled"<?php endif; ?>>
                                <?php foreach ($_timePriorities['BETWEEN']['hours'] as $_priority_time_hour): ?>
                                <option value="<?php echo $_priority_time_hour; ?>" <?php if ($_priority_time_hour == $this->getPriorityTimeHourIdFrom()): ?> selected="selected"<?php endif; ?>><?php echo $_priority_time_hour; ?> <?php echo Mage::helper('extensa_econt')->__('ч.'); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <?php echo Mage::helper('extensa_econt')->__('До:'); ?>
                            <select id="<?php echo $_htmlId; ?>:priority_time_hour_id_to" name="<?php echo $_htmlId; ?>[priority_time_hour_id_to]" class="select from-to-hours small" <?php if (!$this->getPriorityTimeCb()): ?> disabled="disabled"<?php endif; ?>>
                                <?php foreach ($_timePriorities['BETWEEN']['hours2'] as $_priority_time_hour): ?>
                                <option value="<?php echo $_priority_time_hour; ?>" <?php if ($_priority_time_hour == $this->getPriorityTimeHourIdTo()): ?> selected="selected"<?php endif; ?>><?php echo $_priority_time_hour; ?> <?php echo Mage::helper('extensa_econt')->__('ч.'); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </li>
                    <li class="control" id="<?php echo $_htmlId; ?>-express_city_courier" <?php if (!$this->getExpressCityCourier()) { ?> style="display: none;"<?php } ?>>
                        <input type="checkbox" title="<?php echo Mage::helper('extensa_econt')->__('Експресен градски куриер'); ?>" id="<?php echo $_htmlId; ?>:express_city_courier_cb" name="<?php echo $_htmlId; ?>[express_city_courier_cb]" value="1" class="checkbox" <?php if ($this->getExpressCityCourier() && $this->getExpressCityCourierCb()): ?> checked="checked"<?php endif; ?> /><label for="<?php echo $_htmlId; ?>:express_city_courier_cb"><?php echo Mage::helper('extensa_econt')->__('Експресен градски куриер'); ?></label>
                        <div>
                            <input type="radio" title="<?php echo Mage::helper('extensa_econt')->__('до 60 мин (Е1)'); ?>" id="<?php echo $_htmlId; ?>:express_city_courier_e1" name="<?php echo $_htmlId; ?>[express_city_courier_e]" value="e1" class="radio" <?php if ($this->getExpressCityCourierE() == 'e1'): ?> checked="checked"<?php endif; ?> <?php if (!$this->getExpressCityCourierCb()): ?> disabled="disabled"<?php endif; ?> /><label for="<?php echo $_htmlId; ?>:express_city_courier_e1"><?php echo Mage::helper('extensa_econt')->__('до 60 мин (Е1)'); ?></label>
                            <input type="radio" title="<?php echo Mage::helper('extensa_econt')->__('до 120 мин (Е2)'); ?>" id="<?php echo $_htmlId; ?>:express_city_courier_e2" name="<?php echo $_htmlId; ?>[express_city_courier_e]" value="e2" class="radio" <?php if ($this->getExpressCityCourierE() == 'e2'): ?> checked="checked"<?php endif; ?> <?php if (!$this->getExpressCityCourierCb()): ?> disabled="disabled"<?php endif; ?> /><label for="<?php echo $_htmlId; ?>:express_city_courier_e2"><?php echo Mage::helper('extensa_econt')->__('до 120 мин (Е2)'); ?></label>
                            <input type="radio" title="<?php echo Mage::helper('extensa_econt')->__('в рамките на деня (Е3)'); ?>" id="<?php echo $_htmlId; ?>:express_city_courier_e3" name="<?php echo $_htmlId; ?>[express_city_courier_e]" value="e3" class="radio" <?php if ($this->getExpressCityCourierE() == 'e3'): ?> checked="checked"<?php endif; ?> <?php if (!$this->getExpressCityCourierCb()): ?> disabled="disabled"<?php endif; ?> /><label for="<?php echo $_htmlId; ?>:express_city_courier_e3"><?php echo Mage::helper('extensa_econt')->__('в рамките на деня (Е3)'); ?></label>
                        </div>
                    </li>
                </ul>
            </fieldset>
        </li>
        <?php endif; ?>
    </ul>
    <div class="<?php echo $_htmlId; ?>_services" <?php if ($this->getShippingTo() == 'APS'): ?> style="display: none;"<?php endif; ?>>
        <?php if ($this->getInvoiceBeforeCd()): ?><p><i><?php echo Mage::helper('extensa_econt')->__('Добавена е услугата предай фактура преди плащане на наложения платеж.'); ?></i></p><?php endif; ?>
        <?php if ($this->getPayAfterAccept()): ?><p><i><?php echo Mage::helper('extensa_econt')->__('Търговецът позволява пратката да се прегледа от получателя и да плати наложения платеж само ако приеме стоката.'); ?></i></p><?php endif; ?>
        <?php if ($this->getPayAfterTest()): ?><p><i><?php echo Mage::helper('extensa_econt')->__('Търговецът позволява пратката да се прегледа и тества от получателя и да плати наложения платеж само ако приеме стоката.'); ?></i></p><?php endif; ?>
        <?php if ($this->getInstructionShippingReturns()): ?><p><i><?php echo Mage::helper('extensa_econt')->__('При отказ на пратката след преглед: доставката и връщането са за сметка на търговеца.'); ?></i></p><?php endif; ?>
        <?php if ($this->getInstructionReturns()): ?><p><i><?php echo Mage::helper('extensa_econt')->__('При отказ на пратката след преглед: връщането е за сметка на търговеца.'); ?></i></p><?php endif; ?>
        <?php if ($this->getPartialDelivery()): ?><p><b><?php echo Mage::helper('extensa_econt')->__('Има възможност за частична доставка.'); ?></b></p><?php endif; ?>
    </div>
</div>
<script type="text/javascript">
//<![CDATA[

    Validation.add('from-to-hours', Translator.translate('Въведете валиден часови диапазон.'), function(v) {
        function hoursToInt(value) {
            var valueEx = value.split(':');
            return parseInt(valueEx[0]) * 60 + parseInt(valueEx[1]);
        }

        return (hoursToInt(v) >= hoursToInt($F('<?php echo $_htmlId; ?>:priority_time_hour_id_from')));
    });

    <?php if ($this->getShippingTo() == 'DOOR' && $this->getToDoor()): ?>
    new Ajax.Autocompleter(
        '<?php echo $_htmlId; ?>:city',
        '<?php echo $_htmlId; ?>:city_autocomplete',
        '<?php echo Mage::helper('extensa_econt')->getAutocompleteCityUrl(); ?>',
        {
            indicator         : '<?php echo $_htmlId; ?>:city_indicator',
            callback          : <?php echo $_htmlId; ?>_autocomplete_address_city_callback,
            afterUpdateElement: <?php echo $_htmlId; ?>_autocomplete_address_city_update,
            onSuccess         : function (response) {
                if (response.responseText.search('li') == -1) {
                    $('<?php echo $_htmlId; ?>:city').setValue('');
                }
            },
            onShow            : function (element, update) {
                Effect.Appear(update, {duration:0});
            }
        }
    );
    <?php endif; ?>

    function <?php echo $_htmlId; ?>_autocomplete_address_city_callback(input, query) {
        $('<?php echo $_htmlId; ?>:city_id').setValue('');
        $('<?php echo $_htmlId; ?>:post_code').setValue('');
        $('<?php echo $_htmlId; ?>:quarter').setValue('');
        $('<?php echo $_htmlId; ?>:street').setValue('');
        $('<?php echo $_htmlId; ?>:street_num').setValue('');
        $('<?php echo $_htmlId; ?>:other').setValue('');

        return query;
    }

    function <?php echo $_htmlId; ?>_autocomplete_address_city_update(input, li) {
        $('<?php echo $_htmlId; ?>:city_id').setValue(li.readAttribute('city_id'));
        $('<?php echo $_htmlId; ?>:post_code').setValue(li.readAttribute('post_code'));

        <?php if (count($this->getSenderAddresses()) == 1): ?>
        if (li.readAttribute('post_code') == '<?php echo $this->getSenderPostcode(); ?>') {
            $('<?php echo $_htmlId; ?>-express_city_courier').show();
        } else {
            $('<?php echo $_htmlId; ?>-express_city_courier').hide();
            $('<?php echo $_htmlId; ?>:express_city_courier_cb').checked = false;
            $('<?php echo $_htmlId; ?>:express_city_courier_e1').disable();
            $('<?php echo $_htmlId; ?>:express_city_courier_e2').disable();
            $('<?php echo $_htmlId; ?>:express_city_courier_e3').disable();
        }
        <?php endif; ?>
    }

    <?php if ($this->getShippingTo() == 'DOOR' && $this->getToDoor()): ?>
    new Ajax.Autocompleter(
        '<?php echo $_htmlId; ?>:quarter',
        '<?php echo $_htmlId; ?>:quarter_autocomplete',
        '<?php echo Mage::helper('extensa_econt')->getAutocompleteQuarterUrl(); ?>',
        {
            indicator         : '<?php echo $_htmlId; ?>:quarter_indicator',
            callback          : <?php echo $_htmlId; ?>_autocomplete_address_quarter_callback,
            onSuccess         : function (response) {
                if (response.responseText.search('li') == -1) {
                    $('<?php echo $_htmlId; ?>:quarter').setValue('');
                }
            },
            onShow            : function (element, update) {
                Effect.Appear(update, {duration:0});
            }
        }
    );
    <?php endif; ?>

    function <?php echo $_htmlId; ?>_autocomplete_address_quarter_callback(input, query) {
        return query + '&city_id=' + $F('<?php echo $_htmlId; ?>:city_id');
    }

    <?php if ($this->getShippingTo() == 'DOOR' && $this->getToDoor()): ?>
    new Ajax.Autocompleter(
        '<?php echo $_htmlId; ?>:street',
        '<?php echo $_htmlId; ?>:street_autocomplete',
        '<?php echo Mage::helper('extensa_econt')->getAutocompleteStreetUrl(); ?>',
        {
            indicator         : '<?php echo $_htmlId; ?>:street_indicator',
            callback          : <?php echo $_htmlId; ?>_autocomplete_address_street_callback,
            onSuccess         : function (response) {
                if (response.responseText.search('li') == -1) {
                    $('<?php echo $_htmlId; ?>:street').setValue('');
                }
            },
            onShow            : function (element, update) {
                Effect.Appear(update, {duration:0});
            }
        }
    );
    <?php endif; ?>

    function <?php echo $_htmlId; ?>_autocomplete_address_street_callback(input, query) {
        return query + '&city_id=' + $F('<?php echo $_htmlId; ?>:city_id');
    }

    function <?php echo $_htmlId; ?>_get_office_locator() {
        url = '<?php echo Mage::helper('extensa_econt')->getOfficeLocatorUrl(); ?>' + '&shop_url=<?php echo Mage::getUrl('', array('_secure' => true)); ?>';

        if ($F('<?php echo $_htmlId; ?>:office_city_id')) {
            url += '&address=' + $('<?php echo $_htmlId; ?>:office_city_id').options[$('<?php echo $_htmlId; ?>:office_city_id').selectedIndex].text;
        }

        <?php echo $_htmlId; ?>_win = new Window({url: url, width: 1000, height: 800, destroyOnClose: true, minimizable: false, maximizable: false, recenterAuto: false, zIndex:9999});
        <?php echo $_htmlId; ?>_win.showCenter(false, 50);
    }

    <?php if ($this->getShippingTo() == 'OFFICE' && $this->getToOffice()): ?>
    $('<?php echo $_htmlId; ?>:office_locator').observe('click', <?php echo $_htmlId; ?>_get_office_locator);
    <?php endif; ?>
    function <?php echo $_htmlId; ?>_receive_message(event) {
        if (event.origin !== '<?php echo Mage::helper('extensa_econt')->getOfficeLocatorDomain(); ?>')
            return;

        map_data = event.data.split('||');

        new Ajax.Request(
            '<?php echo Mage::helper('extensa_econt')->getOfficeByCodeUrl(); ?>',
            {
                method:     'post',
                parameters: {
                    office_code: map_data[0]
                },
                onSuccess: function(transport) {
                    if (transport.responseText.isJSON()) {
                        response = transport.responseText.evalJSON();

                        if ($F('<?php echo $_htmlId; ?>:office_city_id') == response['city_id']) {
                            options = $('<?php echo $_htmlId; ?>:office_id').options;

                            for (i = 0; i < options.length; i++) {
                                if (options[i].readAttribute('value') == response['office_id']) {
                                    options[i].selected = true;
                                    break;
                                }
                            }

                            $('<?php echo $_htmlId; ?>:office_code').setValue(response['office_code']);
                        } else {
                            $('<?php echo $_htmlId; ?>:office_city_id').setValue(response['city_id']);
                            <?php echo $_htmlId; ?>_get_offices(response, 0);
                        }
                    }
                }
            }
        );

        <?php echo $_htmlId; ?>_win.destroy();
    }

    if (window.addEventListener) {
        window.addEventListener('message', <?php echo $_htmlId; ?>_receive_message, false);
    } else if (window.attachEvent) {
        window.attachEvent('onmessage', <?php echo $_htmlId; ?>_receive_message);
    }

    function <?php echo $_htmlId; ?>_get_offices(office_data) {
        var office_id = 'office_id';
        var office_city_id = 'office_city_id';
        var office_code = 'office_code';

        $('<?php echo $_htmlId; ?>:' + office_id).update('<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Please Select--')); ?></option>');
        $('<?php echo $_htmlId; ?>:' + office_code).setValue('');

        if ($F('<?php echo $_htmlId; ?>:' + office_city_id)) {
            new Ajax.Request(
                '<?php echo Mage::helper('extensa_econt')->getOfficesUrl(); ?>',
                {
                    method:     'post',
                    parameters: {
                        city_id      : $F('<?php echo $_htmlId; ?>:' + office_city_id),
                        delivery_type: 'to_office'
                    },
                    onSuccess: function(transport) {
                        if (transport.responseText.isJSON()) {
                            response = transport.responseText.evalJSON();
                            html = '<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Please Select--')); ?></option>';

                            for (i = 0; i < response.length; i++) {
                                html += '<option value="' + response[i]['office_id'] + '"';
                                if (office_data && office_data['office_id'] == response[i]['office_id']) {
                                    html += ' selected="selected"';
                                }
                                html += '>' + response[i]['office_code'] + ', ' + response[i]['name'] + ', ' + response[i]['address'] +  '</option>';
                            }

                            $('<?php echo $_htmlId; ?>:' + office_id).update(html);

                            if (office_data) {
                                $('<?php echo $_htmlId; ?>:' + office_code).setValue(office_data['office_code']);
                            }
                        }
                    }
                }
            );
        }
    }

    <?php if ($this->getShippingTo() == 'OFFICE' && $this->getToOffice()): ?>
    $('<?php echo $_htmlId; ?>:office_city_id').observe('change', function (evenet) {
        <?php echo $_htmlId; ?>_get_offices('', 0);
    });
    <?php endif; ?>

    function <?php echo $_htmlId; ?>_get_cities() {
        try { $('<?php echo $_htmlId; ?>:office_city_id').update('<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Please Select--')); ?></option>'); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:office_id').update('<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Please Select--')); ?></option>'); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:city').setValue(''); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:post_code').setValue(''); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:office_code').setValue(''); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:quarter').setValue(''); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:other').setValue(''); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:street').setValue(''); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:street_num').setValue(''); } catch (e) { }
        try { $('<?php echo $_htmlId; ?>:city_id').setValue(''); } catch (e) { }

        if ($F('<?php echo $_htmlId; ?>:country')) {
            new Ajax.Request(
                '<?php echo Mage::helper('extensa_econt')->getCitiesUrl(); ?>',
                {
                    method:     'post',
                    parameters: {
                        country_id: $F('<?php echo $_htmlId; ?>:country'),
                    },
                    onSuccess: function(transport) {
                        if (transport.responseText.isJSON()) {
                            response = transport.responseText.evalJSON();
                            html = '<option value=""><?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Please Select--')); ?></option>';

                            for (i = 0; i < response.length; i++) {
                                html += '<option value="' + response[i]['city_id'] + '">' + response[i]['name'] + '</option>';
                            }

                            $('<?php echo $_htmlId; ?>:office_city_id').update(html);
                        }
                    }
                }
            );
        }
    }

    $('<?php echo $_htmlId; ?>:country').observe('change', function (evenet) {
        <?php echo $_htmlId; ?>_get_cities();
    });

    function <?php echo $_htmlId; ?>_get_office() {
        var office_id = 'office_id';
        var office_code = 'office_code';

        $('<?php echo $_htmlId; ?>:' + office_code).setValue('');

        if ($F('<?php echo $_htmlId; ?>:' + office_id)) {
            new Ajax.Request(
                '<?php echo Mage::helper('extensa_econt')->getOfficeUrl(); ?>',
                {
                    method:     'post',
                    parameters: {
                        office_id: $F('<?php echo $_htmlId; ?>:' + office_id)
                    },
                    onSuccess: function(transport) {
                        if (transport.responseText.isJSON()) {
                            response = transport.responseText.evalJSON();
                            if (response.is_machine === '1') {
                                $$('label[for*="<?php echo $_htmlId; ?>_econt_office"] .price')[0].update('<?php echo $_apsPrice; ?>');
                                $$('.<?php echo $_htmlId; ?>_aps_info').each(Element.show);
                            } else {
                                $$('label[for*="<?php echo $_htmlId; ?>_econt_office"] .price')[0].update('<?php echo $_officePrice; ?>');
                                $$('.<?php echo $_htmlId; ?>_aps_info').each(Element.hide);
                            }

                            $('<?php echo $_htmlId; ?>:' + office_code).setValue(response.office_code);
                        }
                    }
                }
            );
        }
    }

    <?php if ($this->getShippingTo() == 'OFFICE' && $this->getToOffice()): ?>
    $('<?php echo $_htmlId; ?>:office_id').observe('change', function (evenet) {
        <?php echo $_htmlId; ?>_get_office();
    });
    <?php endif; ?>

    function <?php echo $_htmlId; ?>_check_priority_time() {
        if ($('<?php echo $_htmlId; ?>:priority_time_cb').checked) {
            $('<?php echo $_htmlId; ?>:priority_time_type_id').enable();
            $('<?php echo $_htmlId; ?>:priority_time_hour_id').enable();
        } else {
            $('<?php echo $_htmlId; ?>:priority_time_type_id').disable();
            $('<?php echo $_htmlId; ?>:priority_time_hour_id').disable();
        }
    }

    <?php if ($this->getShippingTo() == 'DOOR' && $this->getToDoor()): ?>
    $('<?php echo $_htmlId; ?>:priority_time_cb').observe('click', <?php echo $_htmlId; ?>_check_priority_time);
    <?php endif; ?>

    function <?php echo $_htmlId; ?>_check_express_city_courier() {
        if ($('<?php echo $_htmlId; ?>:express_city_courier_cb').checked) {
            $('<?php echo $_htmlId; ?>:express_city_courier_e1').enable();
            $('<?php echo $_htmlId; ?>:express_city_courier_e2').enable();
            $('<?php echo $_htmlId; ?>:express_city_courier_e3').enable();
        } else {
            $('<?php echo $_htmlId; ?>:express_city_courier_e1').disable();
            $('<?php echo $_htmlId; ?>:express_city_courier_e2').disable();
            $('<?php echo $_htmlId; ?>:express_city_courier_e3').disable();
        }
    }

    <?php if ($this->getShippingTo() == 'DOOR' && $this->getToDoor()): ?>
    $('<?php echo $_htmlId; ?>:express_city_courier_cb').observe('click', <?php echo $_htmlId; ?>_check_express_city_courier);
    <?php endif; ?>

    function <?php echo $_htmlId; ?>_set_priority_time() {
        type = $F('<?php echo $_htmlId; ?>:priority_time_type_id');

        if (type == 'IN') {
            $('<?php echo $_htmlId; ?>:priority_time_type_in').show();
            $('<?php echo $_htmlId; ?>:priority_time_type_between').hide();
        } else if (type == 'BETWEEN') {
            $('<?php echo $_htmlId; ?>:priority_time_type_in').hide();
            $('<?php echo $_htmlId; ?>:priority_time_type_between').show();
        }
    }

    <?php if ($this->getShippingTo() == 'DOOR' && $this->getToDoor()): ?>
    $('<?php echo $_htmlId; ?>:priority_time_type_id').observe('change', <?php echo $_htmlId; ?>_set_priority_time);
    <?php endif; ?>

    $$('label[for*="<?php echo $_htmlId; ?>_"]').each(function(el){
        <?php if (!empty($_error['general'])): ?>
        $$('label[for="' + el.readAttribute('for') + '"] .price')[0].update('');
        <?php elseif (!empty($_error['fixed'])): ?>
        $$('label[for="' + el.readAttribute('for') + '"] .price')[0].update('<?php echo $this->jsQuoteEscape($_error['message']); ?>');
        <?php elseif (!empty($_error['office'])): ?>
        if (el.readAttribute('for').indexOf('office') != -1) {
            $$('label[for="' + el.readAttribute('for') + '"] .price')[0].update('');
        }
        <?php elseif (!empty($_error['door'])): ?>
        if (el.readAttribute('for').indexOf('door') != -1) {
            $$('label[for="' + el.readAttribute('for') + '"] .price')[0].update('');
        }
        <?php endif; ?>
    });

    Validation.addAllThese([
        ['<?php echo $_htmlId; ?>-required-city', '<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, изберете населено място.')); ?>', function(v) {
                return !Validation.get('IsEmpty').test(v);
            }],
        ['<?php echo $_htmlId; ?>-required-quarter', '<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, изберете квартал или улица.')); ?>', function(v) {
                return !Validation.get('IsEmpty').test(v) ||
                    !Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:street'));
            }],
        ['<?php echo $_htmlId; ?>-required-street', '<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, изберете улица или квартал.')); ?>', function(v) {
                return !Validation.get('IsEmpty').test(v) ||
                    !Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:quarter'));
            }],
        ['<?php echo $_htmlId; ?>-required-street-num', '<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, попълнете номер.')); ?>', function(v) {
                return Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:street')) ||
                    !Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:street')) &&
                    !Validation.get('IsEmpty').test(v) ||
                    !Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:quarter')) &&
                    !Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:other'));
            }],
        ['<?php echo $_htmlId; ?>-required-other', '<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, попълнете друго.')); ?>', function(v) {
                return Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:quarter')) ||
                    !Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:quarter')) &&
                    !Validation.get('IsEmpty').test(v) ||
                    !Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:street')) &&
                    !Validation.get('IsEmpty').test($F('<?php echo $_htmlId; ?>:street_num'));
            }],
        ['<?php echo $_htmlId; ?>-required-office-city', '<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, изберете населено място.')); ?>', function(v) {
                return !Validation.get('IsEmpty').test(v);
            }],
        ['<?php echo $_htmlId; ?>-required-office', '<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, изберете офис.')); ?>', function(v) {
                return !Validation.get('IsEmpty').test(v);
            }],
        ['<?php echo $_htmlId; ?>-required-cd-aps', '<?php echo $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('При доставка до АПС единствената възможна услуга е наложен платеж и е нужно заплащане на сумата и разходите за доставка с карта!')); ?>', function(v) {
                var value = 1;
                $$('input[type="radio"][name="shipping_method"]').each(function(el){
                    if (el.readAttribute('value').indexOf('aps') != -1 && el.checked == true) {
                        value = v;
                        throw $break;
                    }
                });

                return value == 1;
            }]
    ]);
//]]>
</script>
<?php //endif; ?>
