<?php
/**
 * <AUTHOR> Web Development Ltd. <<EMAIL>>
 */
?>
<?php
/**
 * @var $this Extensa_Econt_Block_Adminhtml_System_Config_Form_Select_Office
 */
?>
<script type="text/javascript">
    //<![CDATA[

    function checkApsCd() {
        if ($F('carriers_extensa_econt_shipping_from') == 'APS' && $F('carriers_extensa_econt_cd') == 1 && $F('carriers_extensa_econt_cd_agreement') == 0) {
            alert('<?php echo Mage::helper('extensa_econt')->__('Внимание! При комбинация: "Доставка от АПС" и "Възможно заплащането на стоката с наложен платеж" трябва да въведе споразумение за НП!'); ?>');

            $('carriers_extensa_econt_cd_agreement').setValue(1);

            if(document.createEventObject) {
                var evt = document.createEventObject();
                document.getElementById('carriers_extensa_econt_cd_agreement').fireEvent('onchange', evt);
            } else {
                var evt = document.createEvent('HTMLEvents');
                evt.initEvent('change', false, true);
                document.getElementById('carriers_extensa_econt_cd_agreement').dispatchEvent(evt);
            }
        }
    }

    document.observe('dom:loaded', function() {
        $('carriers_extensa_econt_shipping_from').observe('change', checkApsCd);
        $('carriers_extensa_econt_cd').observe('change', checkApsCd);
        $('carriers_extensa_econt_cd_agreement').observe('change', checkApsCd);
    });
    //]]>
</script>
