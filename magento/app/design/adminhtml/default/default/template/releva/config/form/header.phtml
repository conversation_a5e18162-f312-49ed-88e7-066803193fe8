<?php
/** @var $this Releva_Releva_Block_Adminhtml_System_Config_Form_Header */
?>

<div class="clear"></div>
<div class="releva-header">
    <?php echo $this->getMessagesHtml() ?>
    <div class="clear"></div>
</div>

<div class="clear"></div>
<script type="text/javascript">
//<![CDATA[
    function updateRelevaMessage(message, className) {
      $('releva-popup-window-message-text').innerHTML = message;
      $('releva-message-item').className = className;
    }

    function showRelevaMessage(title, message, className) {
        $('releva-popup-window-message-title').innerHTML = title;
        updateRelevaMessage(message, className)
    }

    function relevaSync(type, url, buttonElement) {
      buttonElement.addClassName('disabled');
      showRelevaMessage(type + ' sync', '<div class="loading"></div> Releva ' + type + ' sync is in progress.', 'notice-msg');
      fetch(url).then(function(data) {
        data.text().then(function(text) {
          updateRelevaMessage(text, text.indexOf('error') === -1 ? 'success-msg' : 'error-msg');
        });
      }).catch(function(error) {
        updateRelevaMessage('An unexpected error has occurred: ' + error.message, 'error-msg');
      }).finally(function() {
        buttonElement.removeClassName('disabled');
      });
    }
//]]>
</script>
<div>
  <style>
    .loading:before{content:"";display:inline-block;float:right; font-size:3px;text-indent:-9999em}.loader-animation-4 .loading:before,.loading:before{width:1em;height:1em;margin:2em;border:none;-webkit-border-radius:50%;border-radius:50%;background:0 0;-webkit-animation:loader-animation-4 1.3s infinite linear;animation:loader-animation-4 1.3s infinite linear;-webkit-transform:translateZ(0);transform:translateZ(0)}@-webkit-keyframes loader-animation-4{0%,100%{-webkit-box-shadow:0 -4em 0 .2em #000,2.7em -2.7em 0 0 #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 0 #000;box-shadow:0 -4em 0 .2em #000,2.7em -2.7em 0 0 #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 0 #000}12.5%{-webkit-box-shadow:0 -4em 0 0 #000,2.7em -2.7em 0 .2em #000,4em 0 0 0 #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 0 #000,2.7em -2.7em 0 .2em #000,4em 0 0 0 #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000}25%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 0 #000,4em 0 0 .2em #000,2.7em 2.7em 0 0 #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 0 #000,4em 0 0 .2em #000,2.7em 2.7em 0 0 #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000}37.5%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 0 #000,2.7em 2.7em 0 .2em #000,0 4em 0 0 #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 0 #000,2.7em 2.7em 0 .2em #000,0 4em 0 0 #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000}50%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 0 #000,0 4em 0 .2em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 0 #000,0 4em 0 .2em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000}62.5%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 0 #000,-2.7em 2.7em 0 .2em #000,-4em 0 0 0 #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 0 #000,-2.7em 2.7em 0 .2em #000,-4em 0 0 0 #000,-2.7em -2.7em 0 -.5em #000}75%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 .2em #000,-2.7em -2.7em 0 0 #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 .2em #000,-2.7em -2.7em 0 0 #000}87.5%{-webkit-box-shadow:0 -4em 0 0 #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 0 #000,-2.7em -2.7em 0 .2em #000;box-shadow:0 -4em 0 0 #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 0 #000,-2.7em -2.7em 0 .2em #000}}@keyframes loader-animation-4{0%,100%{-webkit-box-shadow:0 -4em 0 .2em #000,2.7em -2.7em 0 0 #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 0 #000;box-shadow:0 -4em 0 .2em #000,2.7em -2.7em 0 0 #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 0 #000}12.5%{-webkit-box-shadow:0 -4em 0 0 #000,2.7em -2.7em 0 .2em #000,4em 0 0 0 #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 0 #000,2.7em -2.7em 0 .2em #000,4em 0 0 0 #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000}25%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 0 #000,4em 0 0 .2em #000,2.7em 2.7em 0 0 #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 0 #000,4em 0 0 .2em #000,2.7em 2.7em 0 0 #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000}37.5%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 0 #000,2.7em 2.7em 0 .2em #000,0 4em 0 0 #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 0 #000,2.7em 2.7em 0 .2em #000,0 4em 0 0 #000,-2.7em 2.7em 0 -.5em #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000}50%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 0 #000,0 4em 0 .2em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 0 #000,0 4em 0 .2em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 -.5em #000,-2.7em -2.7em 0 -.5em #000}62.5%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 0 #000,-2.7em 2.7em 0 .2em #000,-4em 0 0 0 #000,-2.7em -2.7em 0 -.5em #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 0 #000,-2.7em 2.7em 0 .2em #000,-4em 0 0 0 #000,-2.7em -2.7em 0 -.5em #000}75%{-webkit-box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 .2em #000,-2.7em -2.7em 0 0 #000;box-shadow:0 -4em 0 -.5em #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 .2em #000,-2.7em -2.7em 0 0 #000}87.5%{-webkit-box-shadow:0 -4em 0 0 #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 0 #000,-2.7em -2.7em 0 .2em #000;box-shadow:0 -4em 0 0 #000,2.7em -2.7em 0 -.5em #000,4em 0 0 -.5em #000,2.7em 2.7em 0 -.5em #000,0 4em 0 -.5em #000,-2.7em 2.7em 0 0 #000,-4em 0 0 0 #000,-2.7em -2.7em 0 .2em #000}}
  </style>
  <ul id="notification-container" class="messages">
    <li id="releva-message-item">
      <h2 id="releva-popup-window-message-title"></h2>
      <p id="releva-popup-window-message-text"></p>
    </li>
  </div>
</div>
<table cellpadding="0" cellspacing="0" width="100%">
    <tr>
        <?php if ($this->isActive()): ?>
          <td>
              <div class="entry-edit entry-edit-box">
                  <div class="entry-edit-head">
                      <h4><?php echo $this->__('Account Connected'); ?></h4>
                  </div>
                  <div class="fieldset">
                      <p><?php echo $this->__('Your Releva account is connected.'); ?></p>
                      <a class="button" href="<?php echo $this->getDashboardUrl() ?>" target="_blank"><?php echo $this->__('Open Releva Dashboard'); ?></a>
                  </div>
              </div>
          </td>
        <?php else: ?>
          <td>
              <div class="entry-edit entry-edit-box">
                  <div class="entry-edit-head">
                      <h4><?php echo $this->__('Connect your Account'); ?></h4>
                  </div>
                  <div class="fieldset">
                      <p><?php echo $this->__('Please follow the link below to get you secretKey from the Shop section and paste it in the Account->Secret Key field.'); ?></p>
                      <a class="button" href="<?php echo $this->getDashboardUrl() ?>" target="_blank"><?php echo $this->__('Open Releva Dashboard'); ?></a>
                  </div>
              </div>
          </td>
        <?php endif ?>
    </tr>
</table>

<div class="clear"></div>
