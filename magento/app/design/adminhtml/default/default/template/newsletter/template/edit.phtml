<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Adminhtml_Block_Newsletter_Template_Edit */
?>
<div class="content-header">
   <h3 class="icon-head head-newsletter-list"><?php echo $this->getHeaderText() ?></h3>
   <p class="content-buttons form-buttons">
                <?php echo $this->getBackButtonHtml(); ?>
                <?php echo $this->getResetButtonHtml(); ?>
                <?php if(!$this->isTextType()): ?>
                <?php echo $this->getToPlainButtonHtml(); ?>
                <?php echo $this->getToHtmlButtonHtml(); ?>
                <?php endif ?>
                <?php echo $this->getPreviewButtonHtml(); ?>
                <?php if($this->getEditMode()): ?>
                <?php echo $this->getDeleteButtonHtml(); ?>
                <?php endif ?>
                <?php if($this->getEditMode()): ?>
                <?php echo $this->getSaveAsButtonHtml(); ?>
                <?php endif ?>
                <?php echo $this->getSaveButtonHtml(); ?>
    </p>
</div>
<form action="<?php echo $this->getSaveUrl() ?>" method="post" id="newsletter_template_edit_form">
    <?php echo $this->getBlockHtml('formkey')?>
    <div class="no-display">
        <input type="hidden" id="change_flag_element" name="_change_type_flag" value="" />
        <input type="hidden" id="save_as_flag" name="_save_as_flag" value="<?php echo $this->getSaveAsFlag() ?>" />
    </div>
    <?php echo $this->getForm() ?>
</form>
<form action="<?php echo $this->getPreviewUrl() ?>" method="post" id="newsletter_template_preview_form" target="_blank">
    <?php echo $this->getBlockHtml('formkey')?>
    <div class="no-display">
        <input type="hidden" id="preview_type" name="type" value="<?php echo $this->isTextType()?1:2 ?>" />
        <input type="hidden" id="preview_text" name="text" value="" />
        <input type="hidden" id="preview_styles" name="styles" value="" />
        <input type="hidden" id="preview_id" name="id" value="" />
    </div>
</form>
<script type="text/javascript">
//<![CDATA[
    var templateForm = new varienForm('newsletter_template_edit_form');
    var templatePreviewForm = new varienForm('newsletter_template_preview_form');
    var templateControl = {

        unconvertedText: '',
        typeChange: false,
        templateName: false,
        id: 'text',

        init: function () {
            if ($('convert_button_back')) {
                $('convert_button_back').hide();
            }
        },

        stripTags: function () {
            if(!window.confirm("<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('newsletter')->__('Are you sure that you want to strip all tags?')) ?>")) {
                return false;
            }
            if(this.isEditor()) {
                this.getEditor().turnOff();
                this.getEditor().getToggleButton().hide();
            }
            this.unconvertedText = $(this.id).value;
            $('convert_button').hide();
            $('convert_button_back').show();
            $(this.id).value =  $(this.id).value.stripScripts().stripTags();
            $('field_template_styles').hide();
            this.typeChange = true;
            return false;
        },

        unStripTags: function () {
            $('convert_button').show();
            $('convert_button_back').hide();
            $(this.id).value =  this.unconvertedText;
            if(this.isEditor()) {
                this.getEditor().turnOn();
                this.getEditor().getToggleButton().show();
            }
            this.typeChange = false;
            $('field_template_styles').show();
            return false;
        },

        save: function() {
            if (this.typeChange) {
                $('change_flag_element').value = '1';
            }
            if(this.isEditor()) {
                tinyMCE.triggerSave();
            }
            templateForm.submit();
            return false;
        },

        saveAs: function() {
            if (this.typeChange) {
                $('change_flag_element').value = '1';
            }

            if($F('code').blank() || $F('code')==templateControl.templateName) {
               value = prompt('<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('newsletter')->__('Please enter new template name')) ?>', templateControl.templateName + '<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('newsletter')->__(' Copy')) ?>');
               if(!value) {
                   if(value !== null) {
                       $('code').value = '';
                       templateForm.submit();
                   }
                   return false;
               } else {
                   $('code').value = value;
               }
            }

            $('save_as_flag').value = '1';

            if(this.isEditor()) {
                tinyMCE.triggerSave();
            }
            templateForm.submit();
            return false;
        },

        preview: function() {
            if (this.typeChange) {
                $('preview_type').value = 1;
            } else {
                $('preview_type').value = 2;
            }
            if (this.isEditor() && tinyMCE.get(this.id)) {
                tinyMCE.triggerSave();
                $('preview_text').value = $(this.id).value;
                tinyMCE.triggerSave();
            } else {
                $('preview_text').value = $(this.id).value;
            }
            if ($('template_styles') != undefined) {
                $('preview_styles').value = $('template_styles').value;
            }
            if ($('id') != undefined) {
                $('preview_id').value = $('id').value;
            }
            templatePreviewForm.submit();
            return false;
        },

        deleteTemplate: function() {
            if(window.confirm("<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('newsletter')->__('Are you sure that you want to delete this template?')) ?>")) {
                   window.location.href = '<?php echo $this->getDeleteUrl() ?>';
            }
        },

        isEditor: function() {
            return (typeof tinyMceEditors != 'undefined' && tinyMceEditors.get(this.id) != undefined)
        },

        getEditor: function() {
            return tinyMceEditors.get(this.id);
        }
    };

    templateControl.init();
    templateControl.templateName = "<?php echo $this->getJsTemplateName() ?>";
//]]>
</script>
