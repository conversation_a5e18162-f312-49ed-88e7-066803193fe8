<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php if ($websites = $this->getWebsites()): ?>
<label for="store_switcher"><?php echo $this->__('Choose Store View:') ?></label>
<select name="store_switcher" id="store_switcher">
    <?php foreach ($websites as $website): ?>
        <?php $showWebsite=false; ?>
        <?php foreach ($website->getGroups() as $group): ?>
            <?php $showGroup=false; ?>
            <?php foreach ($this->getStores($group) as $store): ?>
                <?php if ($showWebsite == false): ?>
                    <?php $showWebsite = true; ?>
                    <optgroup label="<?php echo $this->escapeHtml($website->getName()); ?>"></optgroup>
                <?php endif; ?>
                <?php if ($showGroup == false): ?>
                    <?php $showGroup = true; ?>
                    <optgroup label="&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($group->getName()); ?>">
                <?php endif; ?>
                <option value="<?php echo $store->getId() ?>"<?php if($this->getStoreId() == $store->getId()): ?> selected="selected"<?php endif; ?>>&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($store->getName()); ?></option>
            <?php endforeach; ?>
            <?php if ($showGroup): ?>
                </optgroup>
            <?php endif; ?>
        <?php endforeach; ?>
    <?php endforeach; ?>
</select>
<?php echo $this->getHintHtml() ?>
<script type="text/javascript">
//<![CDATA[
Event.observe($('store_switcher'), 'change', function(event) {
    var element = Event.element(event);
    $('preview_store_id').value = element.value;
});
//]]>
</script>
<?php endif; ?>
