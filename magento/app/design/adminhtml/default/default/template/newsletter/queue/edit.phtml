<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
/* @var $this Mage_Adminhtml_Block_Newsletter_Queue_Edit */
?>
<div class="content-header">
    <table cellspacing="0">
        <tr>
            <td><h3><?php echo $this->getHeaderText() ?></h3></td>
            <td class="form-buttons">
                <?php echo $this->getBackButtonHtml() ?>
                <?php echo $this->getPreviewButtonHtml(); ?>
                <?php if(!$this->getIsPreview()): ?>
                    <?php echo $this->getResetButtonHtml() ?>
                    <?php echo $this->getSaveButtonHtml() ?>
                <?php endif ?>
                <?php if($this->getCanResume()): ?>
                    <?php echo $this->getResumeButtonHtml() ?>
                <?php endif ?>
            </td>
        </tr>
    </table>
</div>
<form action="<?php echo $this->getSaveUrl() ?>" method="post" id="queue_edit_form">
    <?php echo $this->getBlockHtml('formkey')?>
    <div class="no-display">
        <input type="hidden" name="_resume" id="_resume_flag" value="" />
    </div>
    <?php echo $this->getChildHtml('form') ?>
</form>
<form action="<?php echo $this->getPreviewUrl() ?>" method="post" id="newsletter_queue_preview_form" target="_blank">
    <?php echo $this->getBlockHtml('formkey')?>
    <div class="no-display">
        <input type="hidden" id="preview_type" name="type" value="<?php echo $this->getIsTextType()?1:2 ?>" />
        <input type="hidden" id="preview_text" name="text" value="" />
        <input type="hidden" id="preview_styles" name="styles" value="" />
        <input type="hidden" id="preview_id" name="id" value="" />
    </div>
</form>
<script type="text/javascript">
//<![CDATA[
var queueForm = new varienForm('queue_edit_form');
var newsletterPreviewForm = new varienForm('newsletter_queue_preview_form');

var queueControl = {
    id: 'text',
    save: function() {
        $('_resume_flag').value = '';
        queueForm.submit();
    },
    preview: function() {
        if (this.isEditor() && tinyMCE.get(this.id)) {
            tinyMCE.triggerSave();
            $('preview_text').value = $(this.id).value;
            tinyMCE.triggerSave();
        } else {
            $('preview_text').value = $(this.id).value;
        }
        if ($('styles') != undefined) {
            $('preview_styles').value = $('styles').value;
        }
        if ($('id') != undefined) {
            $('preview_id').value = $('id').value;
        }
        newsletterPreviewForm.submit();
        return false;
    },
    isEditor: function() {
        return (typeof tinyMceEditors != 'undefined' && tinyMceEditors.get(this.id) != undefined)
    },
    resume: function() {
        $('_resume_flag').value = '1';
        queueForm.submit();
    }
};
//]]>
</script>
