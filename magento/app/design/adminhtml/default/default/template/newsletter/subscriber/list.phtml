<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="content-header">
    <table cellspacing="0">
        <tr>
            <td style="width:50%;"><h3><?php echo Mage::helper('newsletter')->__('Newsletter Subscribers') ?></h3></td>
        </tr>
    </table>
</div>
<div>
    <?php echo $this->getChildHtml('grid') ?>
</div>
<?php if(count($this->getQueueAsOptions())>0 && $this->getShowQueueAdd()): ?>
<div class="form-buttons">
    <select id="queueList" name="queue">
    <?php foreach ($this->getQueueAsOptions() as $_queue): ?>
        <option value="<?php echo $_queue['value'] ?>"><?php echo $_queue['label'] ?> #<?php echo $_queue['value'] ?></option>
    <?php endforeach; ?>
    </select>
    <button class="scalable" onclick="subscriberController.addToQueue();"><span><span><span><?php echo Mage::helper('newsletter')->__('Add to Queue'); ?></span></span></span></button>
</div>
<?php endif ?>
<script type="text/javascript">
<!--
    var subscriberController = {
        checkCheckboxes: function(controlCheckbox) {
            var elements = $('subscriberGrid').getElementsByClassName('subscriberCheckbox');
            elements.each(function(obj){
                obj.checked = controlCheckbox.checked;
            });
        },
        rowClick: function (e) {
            if(!Event.element(e).hasClassName('subscriberCheckbox')) {
                var elements = Event.findElement(e, 'tr').getElementsByClassName('subscriberCheckbox');
                if(elements.length != 1) {
                    return;
                }
                elements[0].checked = !elements[0].checked;
            }
        },
        addToQueue: function () {
            var elements = $('subscriberGrid').getElementsByClassName('subscriberCheckbox');
            elements.push($('queueList'));
            var serializedElements = Form.serializeElements(elements, true);
            serializedElements.add = 'subscribers';
            serializedElements.form_key = FORM_KEY;
            var url = subscriberGridJsObject.url;

            new Ajax.Updater('subscriberGrid', url + '?ajax=1',
                             {parameters:  serializedElements,
                              evalScripts: true,
                              onComplete:subscriberGridJsObject.initGrid.bind(subscriberGridJsObject)});
        }
    }

    varienGlobalEvents.attachEventHandler('gridRowClick', subscriberController.rowClick.bind(subscriberController));
//-->
</script>
