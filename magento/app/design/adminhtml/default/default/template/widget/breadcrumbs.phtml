<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if(!empty($links)): ?>
<ul class="breadcrumbs">
    <?php $_size=count($links); ?>
    <?php foreach ($links as $_index=>$_link): ?>
    <li>
        <?php if(empty($_link['url'])): ?>
            <?php if($_index!=$_size-1): ?>
                <span><?php echo $this->escapeHtml($_link['label']) ?></span>
            <?php else: ?>
                <strong><?php echo $this->escapeHtml($_link['label']) ?></strong>
            <?php endif; ?>
        <?php else: ?>
            <a href="<?php echo $_link['url'] ?>" title="<?php echo $this->escapeHtml($_link['title']) ?>"><?php echo $this->escapeHtml($_link['label']) ?></a>
        <?php endif; ?>
        <?php if($_index!=$_size-1): ?>
            &raquo;
        <?php endif; ?>
    </li>
    <?php endforeach; ?>
</ul>
<?php endif; ?>
