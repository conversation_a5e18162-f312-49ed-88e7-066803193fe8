<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_element = $this->getElement() ?>
<?php if ($_element->getFieldsetContainerId()): ?>
<div id="<?php echo $_element->getFieldsetContainerId(); ?>">
<?php endif; ?>
<?php if ($_element->getLegend()): ?>
<div class="entry-edit-head">
    <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->escapeHtml($_element->getLegend()) ?></h4>
    <div class="form-buttons"><?php echo $_element->getHeaderBar() ?></div>
</div>
<?php endif; ?>
<?php if (!$_element->getNoContainer()): ?>
    <div class="fieldset <?php echo $_element->getClass() ?>" id="<?php echo $_element->getHtmlId() ?>">
<?php endif; ?>
    <div class="hor-scroll">
        <?php if ($_element->getComment()): ?>
            <p class="comment"><?php echo $this->escapeHtml($_element->getComment()) ?></p>
        <?php endif; ?>
        <?php if ($_element->hasHtmlContent()): ?>
            <?php echo $_element->getHtmlContent(); ?>
        <?php else: ?>
        <table cellspacing="0" class="<?php echo $_element->hasTableClass() ? $_element->getTableClass() : 'form-list'?>">
            <tbody>
                <?php echo $_element->getChildrenHtml(); ?>
            </tbody>
        </table>
        <?php endif; ?>
    </div>
    <?php echo $_element->getSubFieldsetHtml() ?>
<?php if (!$_element->getNoContainer()): ?>
    </div>
<?php endif; ?>
<?php if ($_element->getFieldsetContainerId()): ?>
</div>
<?php endif; ?>
