<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div id="<?php echo $this->getHtmlId() ?>">
<table cellspacing="0" cellpadding="0" class="massaction">
<tr>
    <td><?php if ($this->getUseSelectAll()):?>
        <a href="#" onclick="return <?php echo $this->getJsObjectName() ?>.selectAll()"><?php echo $this->__('Select All') ?></a>
        <span class="separator">|</span>
        <a href="#" onclick="return <?php echo $this->getJsObjectName() ?>.unselectAll()"><?php echo $this->__('Unselect All') ?></a>
        <span class="separator">|</span>
        <?php endif; ?>
        <a href="#" onclick="return <?php echo $this->getJsObjectName() ?>.selectVisible()"><?php echo $this->__('Select Visible') ?></a>
        <span class="separator">|</span>
        <a href="#" onclick="return <?php echo $this->getJsObjectName() ?>.unselectVisible()"><?php echo $this->__('Unselect Visible') ?></a>
        <span class="separator">|</span>
        <strong id="<?php echo $this->getHtmlId() ?>-count">0</strong> <?php echo $this->__('items selected') ?>
    </td>
    <td>
        <div class="right">
            <div class="entry-edit">
                <?php if ($this->getHideFormElement() !== true):?>
                <form action="" id="<?php echo $this->getHtmlId() ?>-form" method="post">
                <?php endif ?>
                    <?php echo $this->getBlockHtml('formkey')?>
                    <fieldset>
                        <span class="field-row">
                            <label><?php echo $this->__('Actions') ?></label>
                            <select id="<?php echo $this->getHtmlId() ?>-select" class="required-entry select absolute-advice local-validation">
                                <option value=""></option>
                                <?php foreach($this->getItems() as $_item): ?>
                                    <option value="<?php echo $_item->getId() ?>"<?php echo ($_item->getSelected() ? ' selected="selected"' : '')?>><?php echo $_item->getLabel() ?></option>
                                <?php endforeach; ?>
                            </select>
                        </span>
                        <span class="outer-span" id="<?php echo $this->getHtmlId() ?>-form-hiddens"></span>
                        <span class="outer-span" id="<?php echo $this->getHtmlId() ?>-form-additional"></span>
                        <span class="field-row">
                            <?php echo $this->getApplyButtonHtml() ?>
                        </span>
                    </fieldset>
                <?php if ($this->getHideFormElement() !== true):?>
                </form>
                <?php endif ?>
            </div>

            <div class="no-display">
            <?php foreach($this->getItems() as $_item): ?>
                <div id="<?php echo $this->getHtmlId() ?>-item-<?php echo $_item->getId() ?>-block">
                    <?php echo $_item->getAdditionalActionBlockHtml() ?>
                </div>
            <?php endforeach; ?>
            </div>
        </div>
    </td>
</tr>
</table>
<?php if(!$this->getParentBlock()->canDisplayContainer()): ?>
<script type="text/javascript">
    <?php echo $this->getJsObjectName() ?>.setGridIds('<?php echo $this->getGridIdsJson() ?>');
</script>
<?php endif; ?>
</div>
