<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @var Mage_Adminhtml_Block_Widget_Grid_Serializer
 */
?>
<?php $_id = 'id_' . md5(microtime()) ?>
<?php $formId = $this->getFormId()?>
<?php if (!empty($formId)) :?>
<script type="text/javascript">
    Event.observe(window, "load", function(){
        var serializeInput  = document.createElement('input');
        serializeInput.type = 'hidden';
        serializeInput.name = '<?php echo $this->getInputElementName()?>';
        serializeInput.id   = '<?php echo $_id?>';
        try {
            document.getElementById('<?php echo $formId?>').appendChild(serializeInput);
            new serializerController('<?php echo $_id?>', <?php echo $this->getDataAsJSON() ?>, <?php echo $this->getColumnInputNames(true) ?>, <?php echo $this->getGridBlock()->getJsObjectName() ?>, '<?php echo $this->getReloadParamName()?>');
        } catch(e) {
            //Error add serializer
        }
    });
</script>
<?php else :?>
<input type="hidden" name="<?php echo $this->getInputElementName()?>"  value="" id="<?php echo $_id?>" />
<script type="text/javascript">
    new serializerController('<?php echo $_id?>', <?php echo $this->getDataAsJSON() ?>, <?php echo $this->getColumnInputNames(true) ?>, <?php echo $this->getGridBlock()->getJsObjectName() ?>, '<?php echo $this->getReloadParamName()?>');
</script>
<?php endif;?>

