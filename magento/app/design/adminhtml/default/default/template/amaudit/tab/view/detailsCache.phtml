<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2016 Amasty (https://www.amasty.com)
 * @package Amasty_Audit
 */
?>
<?php if (count($this->getLogRows())): ?>
 <div class="entry-edit">
    <div class="entry-edit-head"><h4 class="icon-head head-audit-log-statistics"><?php echo Mage::helper('amaudit')->__('Modifications Breakdown') ?></h4></div>
    <div class="grid">
        <table cellspacing="0" class="data">
            <thead>
                <tr class="headings">
                    <th><?php echo Mage::helper('amaudit')->__('Type') ?></th>
                    <th><?php echo Mage::helper('amaudit')->__('Description') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php $_i = 0; ?>
                <?php foreach ($this->getLogRows() as $_value): ?>
                    <tr<?php echo ($_i++ % 2 ? ' class="even"' : '') ?>>
                         <td class="label"><?php echo $_value->getName(); ?></td>
                         <td><span><?php echo $_value->getOldValue(); ?></span></td>
                    </tr>
                    <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<div class="clear"></div> 
<?php else: ?>
 <div class="entry-edit">
    <div class="entry-edit-head"><h4 class="icon-head head-audit-log-statistics"><?php echo Mage::helper('amaudit')->__('Not found') ?></h4></div>
</div>
<?php endif; ?>
