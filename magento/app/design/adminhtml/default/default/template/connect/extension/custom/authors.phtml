<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">
var id = 0;
var authorTemplate =
    '<tr>'+
        '<td style="margin-bottom:5px;">'+
            '<input class="input-text required-entry" style="width:160px;" name="authors[name][]" id="authors_name_{{id}}" value="{{name}}"/>'+
        '</td>'+
        '<td style="margin-bottom:5px;">'+
            '<input class="input-text required-entry" style="width:140px;" name="authors[user][]" id="authors_user_{{id}}" value="{{user}}"/>'+
        '</td>'+
        '<td style="margin-bottom:5px;">'+
            '<input class="input-text required-entry validate-email" style="width:160px;" name="authors[email][]" id="authors_email_{{id}}" value="{{email}}"/>'+
        '</td>'+
        '<td style="margin-bottom:5px;">'+
            '<?php echo $this->jsQuoteEscape($this->getRemoveRowButtonHtml('tr')) ?>'+
        '</td>'+
    '</tr>';

function addAuthor(data)
{
    if (data == undefined) {
        data = {};
    }
    data.id = id++;

    var template = new Template(authorTemplate, /(^|.|\r|\n)({{(\w+)}})/);
    Element.insert($('authors_container'), {bottom: template.evaluate(data)});
}
</script>

<div class="entry-edit">
    <?php echo $this->getFormHtml() ?>
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__("Authors") ?></h4>
    </div>
    <fieldset id="authors_fieldset" class="grid">
        <legend><?php echo $this->__("Authors") ?></legend>
        <table class="data" cellspacing="0">
            <thead>
                <tr class="headings">
                    <th><?php echo $this->__("Name") ?> <span class="required">*</span></th>
                    <th><?php echo $this->__("User") ?> <span class="required">*</span></th>
                    <th><?php echo $this->__("Email") ?> <span class="required">*</span></th>
                    <th><?php echo $this->__("Remove") ?></th>
                </tr>
            </thead>
            <tbody id="authors_container">
                <script type="text/javascript">
                <?php if (count($this->getAuthors())): ?>
                    <?php foreach ($this->getAuthors() as $author): ?>
                        addAuthor(<?php echo $author ?>);
                    <?php endforeach ?>
                <?php else: ?>
                    addAuthor();
                <?php endif;?>
                </script>
            </tbody>
            <tfoot>
                <tr>
                    <td class="a-right" colspan="4"><?php echo $this->getAddAuthorButtonHtml() ?></td>
                </tr>
            </tfoot>
        </table>
    </fieldset>
</div>
