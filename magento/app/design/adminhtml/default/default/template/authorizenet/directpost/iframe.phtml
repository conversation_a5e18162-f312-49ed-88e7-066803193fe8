<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * @var $this Mage_Authorizenet_Block_Directpost_Iframe
 */
?>
<?php
$_params = $this->getParams();
/* @var $_helper Mage_Authorizenet_Helper_Admin  */
$_helper = $this->helper('authorizenet/admin');
?>
<html>
<head>
<script type="text/javascript">
//<![CDATA[
<?php if (isset($_params['redirect'])): ?>
window.location="<?php echo $this->escapeUrl($_params['redirect']) ?>";
<?php endif; ?>
<?php if (isset($_params['redirect_parent'])): ?>
window.top.location="<?php echo $this->escapeUrl($_params['redirect_parent']) ?>";
<?php endif; ?>
<?php if (isset($_params['error_msg'])): ?>
window.top.directPostModel.showError(<?php echo $this->helper('core')->jsonEncode($_params['error_msg']) ?>);
<?php if (isset($_params['x_invoice_num'])): ?>
window.top.directPostModel.successUrl="<?php echo $_helper->getSuccessOrderUrl($_params) ?>";
<?php endif; ?>
<?php endif; ?>
//]]>
</script>
</head>
<body></body>
</html>
