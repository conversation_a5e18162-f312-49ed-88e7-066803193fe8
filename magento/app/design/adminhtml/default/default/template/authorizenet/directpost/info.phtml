<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */

/**
 * @see Mage_Authorizenet_Block_Directpost_Form
 */
?>
<?php
$_form = $this;
$_code = $_form->getMethodCode();
$_method = $_form->getMethod();
$_controller = $this->helper('authorizenet')->getControllerName();
$_orderUrl = $this->helper('authorizenet')->getPlaceOrderAdminUrl();
?>
<!-- IFRAME for request to our server -->
<iframe id="order-directpost-iframe" allowtransparency="true" frameborder="0" name="iframeSubmitOrder" style="display:none;width:100%;background-color:transparent" src="<?php echo $this->getJsUrl() ?>blank.html"></iframe>
<!-- IFRAME for request to Authorize.net -->
<iframe id="directpost-iframe" allowtransparency="true" frameborder="0"  name="iframeDirectPost" style="display:none;width:100%;background-color:transparent" src="<?php echo $this->getJsUrl() ?>blank.html"></iframe>
<ul id="payment_form_<?php echo $_code ?>" style="display:none;">
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_cc_type"><?php echo Mage::helper('payment')->__('Credit Card Type') ?> <span class="required">*</span></label><br/>
            <select id="<?php echo $_code ?>_cc_type" name="payment[cc_type]" class="required-entry validate-cc-type-select">
            <?php $_ccType = $_form->getInfoData('cc_type') ?>
                <option value=""></option>
            <?php foreach ($_form->getCcAvailableTypes() as $_typeCode => $_typeName): ?>
                <option value="<?php echo $_typeCode ?>" <?php if($_typeCode==$_ccType): ?>selected="selected"<?php endif ?>><?php echo $_typeName ?></option>
            <?php endforeach ?>
            </select>
        </div>
    </li>
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_cc_number"><?php echo Mage::helper('payment')->__('Credit Card Number') ?> <span class="required">*</span></label><br/>
            <input type="text" id="<?php echo $_code ?>_cc_number" name="payment[cc_number]" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('payment')->__('Credit Card Number')) ?>" class="input-text validate-cc-number" value="<?php echo $this->getInfoData('cc_number')?>" />
        </div>
    </li>
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_expiration"><?php echo Mage::helper('payment')->__('Expiration Date') ?> <span class="required">*</span></label><br/>
            <select id="<?php echo $_code ?>_expiration" style="width:140px;" name="payment[cc_exp_month]" class="validate-cc-exp required-entry">
            <?php $_ccExpMonth = $_form->getInfoData('cc_exp_month') ?>
            <?php foreach ($_form->getCcMonths() as $k=>$v): ?>
                <option value="<?php echo $k ?>" <?php if($k==$_ccExpMonth): ?>selected="selected"<?php endif ?>><?php echo $v ?></option>
            <?php endforeach ?>
            </select>
            <?php $_ccExpYear = $_form->getInfoData('cc_exp_year') ?>
            <select id="<?php echo $_code ?>_expiration_yr" style="width:103px;" name="payment[cc_exp_year]" class="required-entry">
            <?php foreach ($_form->getCcYears() as $k=>$v): ?>
                <option value="<?php echo $k ? $k : '' ?>" <?php if($k==$_ccExpYear): ?>selected="selected"<?php endif ?>><?php echo $v ?></option>
            <?php endforeach ?>
            </select>
        </div>
    </li>
    <?php if($_form->hasVerification()): ?>
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_cc_cid"><?php echo Mage::helper('payment')->__('Card Verification Number') ?> <span class="required">*</span></label><br/>
            <input type="text" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('payment')->__('Card Verification Number')) ?>" class="required-entry input-text validate-cc-cvn" id="<?php echo $_code ?>_cc_cid" name="payment[cc_cid]" style="width:3em;" value="<?php echo $this->getInfoData('cc_cid')?>" />
        </div>
    </li>
    <?php endif; ?>
</ul>
<script type="text/javascript">
//<![CDATA[
/**
 * Disable cart server validation in admin
 */
AdminOrder.prototype.prepareParams = function(params) {
    if (!params) {
        params = {};
    }
    if (!params.customer_id) {
        params.customer_id = this.customerId;
    }
    if (!params.store_id) {
        params.store_id = this.storeId;
    }
    if (!params.currency_id) {
        params.currency_id = this.currencyId;
    }
    if (!params.form_key) {
        params.form_key = FORM_KEY;
    }

    if (this.paymentMethod != '<?php echo $_code ?>') {
        var data = this.serializeData('order-billing_method');
        if (data) {
            data.each(function(value) {
                params[value[0]] = value[1];
            });
        }
    } else {
        params['payment[method]'] = '<?php echo $_code ?>';
    }
    return params;
};
AdminOrder.prototype.getPaymentData = function(currentMethod) {
    if (typeof (currentMethod) == 'undefined') {
        if (this.paymentMethod) {
            currentMethod = this.paymentMethod;
        } else {
            return false;
        }
    }
    if (currentMethod == '<?php echo $_code ?>') {
        return false;
    }
    var data = {};
    var fields = $('payment_form_' + currentMethod).select('input', 'select');
    for ( var i = 0; i < fields.length; i++) {
        data[fields[i].name] = fields[i].getValue();
    }
    if ((typeof data['payment[cc_type]']) != 'undefined'
            && (!data['payment[cc_type]'] || !data['payment[cc_number]'])) {
        return false;
    }
    return data;
};
<?php if (!$this->isAjaxRequest()): ?>
document.observe('dom:loaded', function(){
<?php endif; ?>
directPostModel = new directPost(
        '<?php echo $_code ?>',
        'directpost-iframe',
        '<?php echo $_controller ?>',
        '<?php echo $_orderUrl ?>',
        '<?php echo $_method->getCgiUrl() ?>',
        '<?php echo $this->getUrl('*/*/save', array('_secure' => $this->getRequest()->isSecure())) ?>');
<?php if (!$this->isAjaxRequest()): ?>
});
<?php endif; ?>
//]]>
</script>
