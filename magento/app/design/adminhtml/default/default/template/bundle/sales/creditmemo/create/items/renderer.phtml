<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Bundle_Block_Adminhtml_Sales_Order_Items_Renderer
 */
?>

<?php $_item = $this->getItem() ?>
<?php $items = $this->getChilds($_item); ?>
<?php $_count = count ($items) ?>
<?php $_index = 0 ?>

<?php $_prevOptionId = '' ?>

<?php if($this->getOrderOptions() || $_item->getDescription()): ?>
    <?php $_showlastRow = true ?>
<?php else: ?>
    <?php $_showlastRow = false ?>
<?php endif; ?>

<?php foreach ($items as $_item): ?>
    <?php $this->setPriceDataObject($_item) ?>
    <?php $attributes = $this->getSelectionAttributes($_item) ?>
    <?php if ($_item->getOrderItem()->getParentItem()): ?>
        <?php if ($_prevOptionId != $attributes['option_id']): ?>
        <tr>
            <td><div class="option-label"><?php echo $this->escapeHtml($attributes['option_label']); ?></div></td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td>&nbsp;</td>
            <td class="last">&nbsp;</td>
        </tr>
        <?php $_prevOptionId = $attributes['option_id'] ?>
        <?php endif; ?>
    <?php endif; ?>
    <tr<?php echo (++$_index==$_count && !$_showlastRow)?' class="border"':'' ?>>
        <?php if (!$_item->getOrderItem()->getParentItem()): ?>
        <td><h5 class="title"><?php echo $this->escapeHtml($_item->getName()) ?></h5>
            <div>
                <strong><?php echo $this->helper('sales')->__('SKU') ?>:</strong>
                <?php echo implode('<br />', Mage::helper('catalog')->splitSku($this->escapeHtml($_item->getSku()))); ?>
            </div>
        </td>
        <?php else: ?>
        <td><div class="option-value"><?php echo $this->getValueHtml($_item)?></div></td>
        <?php endif; ?>
        <td class="a-right">
            <?php if ($this->canShowPriceInfo($_item)): ?>
                <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceExclTax()): ?>
                    <span class="price-excl-tax">
                        <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                            <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
                        <?php endif; ?>

                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
                            <?php
                            echo $this->displayPrices(
                                $_item->getBasePrice()+$_item->getBaseWeeeTaxAppliedAmount()+$_item->getBaseWeeeTaxDisposition(),
                                $_item->getPrice()+$_item->getWeeeTaxAppliedAmount()+$_item->getWeeeTaxDisposition()
                            );
                            ?>
                        <?php else: ?>
                            <?php echo $this->displayPrices($_item->getBasePrice(), $_item->getPrice()) ?>
                        <?php endif; ?>


                        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                            <br />
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                                <small>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></span>
                                <?php endforeach; ?>
                                </small>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></small></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                                <small>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></span>
                                <?php endforeach; ?>
                                </small>
                            <?php endif; ?>

                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                                <br />
                                <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br />
                                <?php
                                echo $this->displayPrices(
                                    $_item->getBasePrice()+$_item->getBaseWeeeTaxAppliedAmount()+$_item->getBaseWeeeTaxDisposition(),
                                    $_item->getPrice()+$_item->getWeeeTaxAppliedAmount()+$_item->getWeeeTaxDisposition()
                                );
                                ?>
                                </span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </span>
                    <br />
                <?php endif; ?>
                <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceInclTax()): ?>
                    <span class="price-incl-tax">
                        <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                            <span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
                        <?php endif; ?>
                        <?php $_incl = $this->helper('checkout')->getPriceInclTax($_item); ?>
                        <?php $_baseIncl = $this->helper('checkout')->getBasePriceInclTax($_item); ?>

                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
                            <?php echo $this->displayPrices($_baseIncl + Mage::helper('weee')->getBaseWeeeTaxInclTax($_item), $_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?>
                        <?php else: ?>
                            <?php echo $this->displayPrices($_baseIncl-$_item->getBaseWeeeTaxDisposition(), $_incl-$_item->getWeeeTaxDisposition()) ?>
                        <?php endif; ?>

                        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                            <br />
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                                <small>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount_incl_tax'], $tax['amount_incl_tax']); ?></span>
                                <?php endforeach; ?>
                                </small>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount_incl_tax'], $tax['amount_incl_tax']); ?></small></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                                <small>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_amount_incl_tax'], $tax['amount_incl_tax']); ?></span>
                                <?php endforeach; ?>
                                </small>
                            <?php endif; ?>

                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                                <br />
                                <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br /> <?php echo $this->displayPrices($_baseIncl + Mage::helper('weee')->getBaseWeeeTaxInclTax($_item), $_incl + Mage::helper('weee')->getWeeeTaxInclTax($_item)); ?></span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </span>
                <?php endif; ?>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
        <td>
            <?php if ($this->canShowPriceInfo($_item)): ?>
                <table cellspacing="0" class="qty-table">
                    <tr>
                        <td><?php echo Mage::helper('sales')->__('Ordered') ?></td>
                        <td><strong><?php echo $_item->getOrderItem()->getQtyOrdered()*1 ?></strong></td>
                    </tr>
                    <?php if ((float) $_item->getOrderItem()->getQtyInvoiced()): ?>
                    <tr>
                        <td><?php echo Mage::helper('sales')->__('Invoiced') ?></td>
                        <td><strong><?php echo $_item->getOrderItem()->getQtyInvoiced()*1 ?></strong></td>
                    </tr>
                    <?php endif; ?>
                    <?php if ((float) $_item->getOrderItem()->getQtyShipped() && $this->isShipmentSeparately($_item)): ?>
                    <tr>
                        <td><?php echo Mage::helper('sales')->__('Shipped') ?></td>
                        <td><strong><?php echo $_item->getOrderItem()->getQtyShipped()*1 ?></strong></td>
                    </tr>
                    <?php endif; ?>
                    <?php if ((float) $_item->getOrderItem()->getQtyRefunded()): ?>
                    <tr>
                        <td><?php echo Mage::helper('sales')->__('Refunded') ?></td>
                        <td><strong><?php echo $_item->getOrderItem()->getQtyRefunded()*1 ?></strong></td>
                    </tr>
                    <?php endif; ?>
                    <?php if ((float) $_item->getOrderItem()->getQtyCanceled()): ?>
                    <tr>
                        <td><?php echo Mage::helper('sales')->__('Canceled') ?></td>
                        <td><strong><?php echo $_item->getOrderItem()->getQtyCanceled()*1 ?></strong></td>
                    </tr>
                    <?php endif; ?>
                </table>
            <?php elseif ($this->isShipmentSeparately($_item)): ?>
                <table cellspacing="0" class="qty-table">
                    <tr>
                        <td><?php echo Mage::helper('sales')->__('Ordered') ?></td>
                        <td><strong><?php echo $_item->getOrderItem()->getQtyOrdered()*1 ?></strong></td>
                    </tr>
                    <?php if ((float) $_item->getOrderItem()->getQtyShipped()): ?>
                    <tr>
                        <td><?php echo Mage::helper('sales')->__('Shipped') ?></td>
                        <td><strong><?php echo $_item->getOrderItem()->getQtyShipped()*1 ?></strong></td>
                    </tr>
                    <?php endif; ?>
                </table>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
        <?php if ($this->canParentReturnToStock($_item)) : ?>
        <td class="a-center">
            <?php if ($this->canShowPriceInfo($_item)): ?>
                <?php if ($this->canReturnItemToStock($_item)) : ?>
                    <input type="checkbox" name="creditmemo[items][<?php echo $_item->getOrderItemId() ?>][back_to_stock]" value="1"<?php if ($_item->getBackToStock()):?> checked="checked"<?php endif;?> />
                <?php endif; ?>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
        <?php endif; ?>
        <td class="a-center">
            <?php if ($this->canShowPriceInfo($_item)): ?>
                <?php if ($this->canEditQty()) : ?>
                    <input type="text" class="input-text qty-input" name="creditmemo[items][<?php echo $_item->getOrderItemId() ?>][qty]" value="<?php echo $_item->getQty()*1 ?>" />
                <?php else: ?>
                    <?php echo $_item->getQty()*1 ?>
                <?php endif; ?>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
        <td class="a-right">
            <?php if ($this->canShowPriceInfo($_item)): ?>
                <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceExclTax()): ?>
                    <span class="price-excl-tax">
                        <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                            <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
                        <?php endif; ?>

                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
                            <?php
                            echo $this->displayPrices(
                                $_item->getBaseRowTotal()+$_item->getBaseWeeeTaxAppliedRowAmount()+$_item->getBaseWeeeTaxRowDisposition(),
                                $_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()
                            );
                            ?>
                        <?php else: ?>
                            <?php echo $this->displayPrices($_item->getBaseRowTotal(), $_item->getRowTotal()) ?>
                        <?php endif; ?>


                        <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                                <small>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></span>
                                <?php endforeach; ?>
                                </small>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></small></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                                <small>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></span>
                                <?php endforeach; ?>
                                </small>
                            <?php endif; ?>

                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                                <br />
                                <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br />
                                <?php
                                echo $this->displayPrices(
                                    $_item->getBaseRowTotal()+$_item->getBaseWeeeTaxAppliedRowAmount()+$_item->getBaseWeeeTaxRowDisposition(),
                                    $_item->getRowTotal()+$_item->getWeeeTaxAppliedRowAmount()+$_item->getWeeeTaxRowDisposition()
                                );
                                ?>
                                </span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </span>
                    <br />
                <?php endif; ?>
                <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceInclTax()): ?>
                    <span class="price-incl-tax">
                        <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                            <span class="label"><?php echo $this->__('Incl. Tax'); ?>:</span>
                        <?php endif; ?>
                        <?php $_incl = $this->helper('checkout')->getSubtotalInclTax($_item); ?>
                        <?php $_baseIncl = $this->helper('checkout')->getBaseSubtotalInclTax($_item); ?>
                        <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales')): ?>
                            <?php echo $this->displayPrices($_baseIncl + Mage::helper('weee')->getBaseRowWeeeTaxInclTax($_item), $_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?>
                        <?php else: ?>
                            <?php echo $this->displayPrices($_baseIncl-$_item->getBaseWeeeTaxRowDisposition(), $_incl-$_item->getWeeeTaxRowDisposition()) ?>
                        <?php endif; ?>


                        <?php if (Mage::helper('weee')->getApplied($_item)): ?>

                            <br />
                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): ?>
                                <small>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount_incl_tax'], $tax['row_amount_incl_tax']); ?></span>
                                <?php endforeach; ?>
                                </small>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><small><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount_incl_tax'], $tax['row_amount_incl_tax']); ?></small></span>
                                <?php endforeach; ?>
                            <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales')): ?>
                                <small>
                                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                    <span class="nobr"><?php echo $tax['title']; ?>: <?php echo $this->displayPrices($tax['base_row_amount_incl_tax'], $tax['row_amount_incl_tax']); ?></span>
                                <?php endforeach; ?>
                                </small>
                            <?php endif; ?>

                            <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): ?>
                                <br /><span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br /> <?php echo $this->displayPrices($_baseIncl + Mage::helper('weee')->getBaseRowWeeeTaxInclTax($_item), $_incl + Mage::helper('weee')->getRowWeeeTaxInclTax($_item)); ?></span>
                            <?php endif; ?>
                        <?php endif; ?>
                    </span>
                <?php endif; ?>
                </span>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
        <td class="a-right">
            <?php if ($this->canShowPriceInfo($_item)): ?>
                <?php echo $this->displayPriceAttribute('tax_amount') ?>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
        <td class="a-right">
            <?php if ($this->canShowPriceInfo($_item)): ?>
                <?php echo $this->displayPriceAttribute('discount_amount') ?>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
        <td class="a-right last">
            <?php if ($this->canShowPriceInfo($_item)): ?>
                <?php echo $this->displayPrices(
                    $_item->getBaseRowTotal() + $_item->getBaseTaxAmount() - $_item->getBaseDiscountAmount() + $_item->getBaseHiddenTaxAmount() + $_item->getBaseWeeeTaxAppliedRowAmount(),
                    $_item->getRowTotal() + $_item->getTaxAmount() - $_item->getDiscountAmount() + $_item->getHiddenTaxAmount() + $_item->getWeeeTaxAppliedRowAmount()
                ) ?>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
        </td>
    </tr>
<?php endforeach; ?>
<?php if($_showlastRow): ?>
    <tr class="border">
        <td>
            <?php if ($this->getOrderOptions($_item->getOrderItem())): ?>
                <dl class="item-options">
                <?php foreach ($this->getOrderOptions($_item->getOrderItem()) as $option): ?>
                    <dt><?php echo $this->escapeHtml($option['label']) ?></dt>
                    <dd>
                    <?php if (isset($option['custom_view']) && $option['custom_view']): ?>
                        <?php echo $option['value'];?>
                    <?php else: ?>
                        <?php echo Mage::helper('core/string')->truncate($option['value'], 55, '', $_remainder);?>
                        <?php if ($_remainder):?>
                            ... <span id="<?php echo $_id = 'id' . uniqid()?>"><?php echo $_remainder ?></span>
                            <script type="text/javascript">
                            $('<?php echo $_id ?>').hide();
                            $('<?php echo $_id ?>').up().observe('mouseover', function(){$('<?php echo $_id ?>').show();});
                            $('<?php echo $_id ?>').up().observe('mouseout',  function(){$('<?php echo $_id ?>').hide();});
                            </script>
                        <?php endif;?>
                    <?php endif;?>
                    </dd>
                <?php endforeach; ?>
                </dl>
            <?php else: ?>
                &nbsp;
            <?php endif; ?>
            <?php echo $this->escapeHtml($_item->getDescription()) ?>
        </td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td>&nbsp;</td>
        <td class="last">&nbsp;</td>
    </tr>
<?php endif; ?>
