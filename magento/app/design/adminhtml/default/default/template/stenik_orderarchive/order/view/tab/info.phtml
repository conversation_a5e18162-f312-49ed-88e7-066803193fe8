<?php /** @var $this Stenik_OrderArchive_Block_Adminhtml_Order_View_Tab_Info */ ?>
<?php $_order = $this->getOrder() ?>
<div>
    <div id="order-messages">
        <?php echo $this->getChildHtml('order_messages') ?>
    </div>
    <?php echo $this->getChildHtml('order_info') ?>
    <input type="hidden" name="order_id" value="<?php echo $_order->getId() ?>"/>


    <div class="box-left">
        <!--Payment Method-->
        <div class="entry-edit">
            <div class="entry-edit-head">
                <h4 class="icon-head head-payment-method"><?php echo Mage::helper('stenik_orderarchive')->__('Payment Information') ?></h4>
            </div>
            <fieldset>
                <?php if ($_order->getData('pay')): ?>
                    <?php echo $_order->getPaymentMethodLabel(); ?><br />
                <?php else: ?>
                    <p><?php echo $_order->getData('reference'); ?></p>
                <?php endif; ?>
            </fieldset>
        </div>
    </div>

    <div class="box-right">
        <!--Shipping Method-->
        <div class="entry-edit">
            <div class="entry-edit-head">
                <h4 class="icon-head head-shipping-method"><?php echo Mage::helper('stenik_orderarchive')->__('Shipping &amp; Handling Information') ?></h4>
            </div>
            <fieldset>
                <?php if ($_order->getData('delivery_method')): ?>
                    <srtong><?php echo $_order->getDeliveryMethodLabel(); ?></srtong>
                <?php else: ?>
                    <strong><?php echo $this->helper('sales')->__('No shipping information available'); ?></strong>
                <?php endif; ?>
            </fieldset>
        </div>
    </div>

    <div class="clear"></div>

    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-products"><?php echo Mage::helper('stenik_orderarchive')->__('Items Ordered') ?></h4>
        </div>
    </div>
    <?php echo $this->getItemsHtml() ?>
    <div class="clear"></div>

    <div class="box-right entry-edit">
        <div class="entry-edit-head"><h4><?php echo Mage::helper('stenik_orderarchive')->__('Order Totals') ?></h4></div>
        <div class="order-totals"><?php echo $this->getChildHtml('order_totals') ?></div>
    </div>
    <div class="clear"></div>
</div>