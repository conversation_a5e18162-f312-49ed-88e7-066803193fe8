<table cellspacing="0" width="100%">
    <col />
    <col width="1" />
    <?php $_totals = $this->getTotals('footer')?>
    <?php if ($_totals):?>
    <tfoot>
    <?php foreach ($this->getTotals('footer') as $_code => $_total): ?>
        <?php if ($_total->getBlockName()): ?>
            <?php echo $this->getChildHtml($_total->getBlockName(), false); ?>
        <?php else:?>
        <tr class="<?php echo $_code?>">
            <td <?php echo $this->getLabelProperties()?> class="label">
                <strong><?php echo $this->escapeHtml($_total->getLabel()); ?></strong>
            </td>
            <td <?php echo $this->getValueProperties()?> class="emph">
                <strong><?php echo $this->formatValue($_total) ?></strong>
            </td>
        </tr>
        <?php endif?>
    <?php endforeach?>
    </tfoot>
    <?php endif?>

    <?php $_totals = $this->getTotals('')?>
    <?php if ($_totals):?>
    <tbody>
    <?php foreach ($_totals as $_code => $_total): ?>
        <?php if ($_total->getBlockName()): ?>
            <?php echo $this->getChildHtml($_total->getBlockName(), false); ?>
        <?php else:?>
        <tr class="<?php echo $_code?>">
            <td <?php echo $this->getLabelProperties()?> class="label">
                <?php if ($_total->getStrong()):?>
                <strong><?php echo $this->escapeHtml($_total->getLabel()); ?></strong>
                <?php else:?>
                <?php echo $this->escapeHtml($_total->getLabel()); ?>
                <?php endif?>
            </td>
            <?php if ($_total->getStrong()):?>
            <td <?php echo $this->getValueProperties()?> class="emph">
                <strong><?php echo $this->formatValue($_total) ?></strong>
            <?php else:?>
            <td <?php echo $this->getValueProperties()?>>
                <?php echo $this->formatValue($_total) ?>
            <?php endif?>
            </td>
        </tr>
        <?php endif?>
    <?php endforeach?>
    </tbody>
    <?php endif?>
</table>
