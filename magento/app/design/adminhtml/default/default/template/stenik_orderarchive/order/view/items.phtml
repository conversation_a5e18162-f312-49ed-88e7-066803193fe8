<?php $_order = $this->getOrder() ?>
<div class="grid np">
  <div class="hor-scroll">
    <table cellspacing="0" class="data order-tables">
        <col />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <col width="1" />
        <thead>
            <tr class="headings">
                <th><?php echo $this->helper('stenik_orderarchive')->__('Product') ?></th>
                <th><span class="nobr"><?php echo $this->helper('stenik_orderarchive')->__('Original Price') ?></span></th>
                <th><?php echo $this->helper('stenik_orderarchive')->__('Price') ?></th>
                <th class="a-center"><?php echo $this->helper('stenik_orderarchive')->__('Qty') ?></th>
                <th><?php echo $this->helper('stenik_orderarchive')->__('Subtotal') ?></th>
                <th><span class="nobr"><?php echo $this->helper('stenik_orderarchive')->__('Discount Percent') ?></span></th>
                <th><span class="nobr"><?php echo $this->helper('stenik_orderarchive')->__('Discount Amount') ?></span></th>
                <th class="last"><span class="nobr"><?php echo $this->helper('stenik_orderarchive')->__('Row Total') ?></span></th>
            </tr>
        </thead>
        <?php $_items = $this->getItemsCollection(); ?>
        <?php $i=0; foreach ($_items as $_item):?>
            <?php $i++; ?>
            <tbody class="<?php echo $i%2?'even':'odd' ?>">


                <?php $this->setPriceDataObject($_item) ?>
                <tr class="border">
                    <td>
                        <div class="item-text">
                            <h5 class="title"><?php echo $this->escapeHtml($_item->getName()) ?></h5>
                            <div>
                                <strong><?php echo Mage::helper('stenik_orderarchive')->__('SKU'); ?>: <?php echo $this->escapeHtml(Mage::helper('core/string')->splitInjection($_item->getSku())) ?></strong>
                            </div>
                        </div>
                    </td>
                    <td class="a-right">
                        <?php echo $this->getOrder()->formatPrice($_item->getPrice()) ?>
                    </td>
                    <td class="a-right">
                        <?php echo $this->getOrder()->formatPrice($_item->getFinalPrice()) ?>
                    </td>
                    <td>
                        <?php echo $this->escapeHtml($_item->getMeasureQty()) ?>
                    </td>
                    <td class="a-right">
                        <?php echo $this->getOrder()->formatPrice($_item->getFinalPrice()) ?>
                    </td>
                    <td class="a-right"><?php echo $_item->getDiscountPercent(); ?>%</td>
                    <td class="a-right"><?php echo $this->getOrder()->formatPrice($_item->getDiscountAmount()); ?></td>
                    <td class="a-right last">
                        <?php echo $this->getOrder()->formatPrice($_item->getRowPrice()) ?>
                    </td>
                </tr>

            </tbody>
        <?php endforeach; ?>
    </table>
  </div>
</div>
<br />
