<?php $_order = $this->getOrder() ?>
<?php
$orderStoreDate = $this->formatDate($_order->getCreatedAtStoreDate(), 'medium', true);
?>
<div class="box-left">
    <!--Order Information-->
    <div class="entry-edit">
        <div class="entry-edit-head">
        <?php if ($this->getNoUseOrderLink()): ?>
            <h4 class="icon-head head-account"><?php echo Mage::helper('stenik_orderarchive')->__('Order # %s', $_order->getRealOrderId()) ?></h4>
        <?php else: ?>
            <a href="<?php echo $this->getViewUrl($_order->getId()) ?>"><?php echo Mage::helper('stenik_orderarchive')->__('Order # %s', $_order->getRealOrderId()) ?></a>
        <?php endif; ?>
        </div>
        <div class="fieldset">
            <table cellspacing="0" class="form-list">
            <tr>
                <td class="label"><label><?php echo Mage::helper('stenik_orderarchive')->__('Order Date'); ?></label></td>
                <td class="value"><strong><?php echo $orderStoreDate ?></strong></td>
            </tr>

            <tr>
                <td class="label"><label><?php echo Mage::helper('stenik_orderarchive')->__('Order Status') ?></label></td>
                <td class="value"><strong><span id="order_status"><?php echo $_order->getStatusLabel() ?></span></strong></td>
            </tr>
            </table>
        </div>
    </div>
</div>
<div class="box-right">
    <!--Account Information-->
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-account"><?php echo Mage::helper('stenik_orderarchive')->__('Account Information') ?></h4>
            <div class="tools"><?php echo $this->getAccountEditLink()?></div>
        </div>
        <div class="fieldset">
            <div class="hor-scroll">
                <table cellspacing="0" class="form-list">
                <tr>
                    <td class="label"><label><?php echo Mage::helper('stenik_orderarchive')->__('Customer Name') ?></label></td>
                    <td class="value">
                    <?php if ($_customerUrl=$this->getCustomerViewUrl()) : ?>
                        <a href="<?php echo $_customerUrl ?>" target="_blank"><strong><?php echo $this->escapeHtml($_order->getCustomerName()) ?></strong></a>
                    <?php else: ?>
                        <strong><?php echo $this->escapeHtml($_order->getCustomerName()) ?></strong>
                    <?php endif; ?>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label><?php echo Mage::helper('stenik_orderarchive')->__('Email') ?></label></td>
                    <td class="value"><a href="mailto:<?php echo $this->escapeHtml($_order->getCustomerEmail()) ?>"><strong><?php echo $this->escapeHtml($_order->getCustomerEmail()) ?></strong></a></td>
                </tr>
                <tr>
                    <td class="label"><label><?php echo Mage::helper('stenik_orderarchive')->__('Customer Old ID') ?></label></td>
                    <td class="value"><strong><?php echo $_order->getCustomerId(); ?></strong></td>
                </tr>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="clear"></div>

<div class="box-left">
    <!--Billing Address-->
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-billing-address"><?php echo Mage::helper('stenik_orderarchive')->__('Shipping Address') ?></h4>
        </div>
        <fieldset>
            <address>
                <?php echo $_order->getData('delivery_name'); ?> <?php echo $_order->getData('delivery_family'); ?><br />
                <?php echo $_order->getData('delivery_phone'); ?><br />
                <?php echo $_order->getData('delivery_adres'); ?><br />
                <?php echo $_order->getData('delivery_city'); ?>, <?php echo $_order->getData('delivery_postcode'); ?><br />

            </address>
        </fieldset>
    </div>
</div>

<div class="box-right">
    <!--Billing Address-->
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-billing-address"><?php echo Mage::helper('stenik_orderarchive')->__('Invoice Information') ?></h4>
        </div>
        <fieldset>
            <address>
                <?php if ($_order->hasInvoiceData()): ?>
                    <?php echo Mage::helper('stenik_orderarchive')->__('Company'); ?>: <?php echo $_order->getData('invoice_company'); ?><br />
                    <?php echo Mage::helper('stenik_orderarchive')->__('MOL'); ?>: <?php echo $_order->getData('invoice_mol'); ?><br />
                    <?php echo Mage::helper('stenik_orderarchive')->__('EIK'); ?>: <?php echo $_order->getData('invoice_id'); ?><br />
                    <?php echo Mage::helper('stenik_orderarchive')->__('DDS'); ?>: <?php echo $_order->getData('invoice_dds'); ?><br />
                    <?php echo Mage::helper('stenik_orderarchive')->__('City'); ?>: <?php echo $_order->getData('invoice_city'); ?><br />
                    <?php echo Mage::helper('stenik_orderarchive')->__('Address'); ?>: <?php echo $_order->getData('invoice_adres'); ?><br />
                <?php endif; ?>
            </address>
        </fieldset>
    </div>
</div>

<div class="clear"></div>
