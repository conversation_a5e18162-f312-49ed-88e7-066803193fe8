<?php
/**
 * @var Praktis_Econt_Block_Econt_Adminhtml_Sales_Order_View_Tab_Loading $this
 */

$_htmlId = 'extensa_econt';
$_delivery_days = (date('w') == 5 || date('w') == 6);
$_products_all = $this->getProducts();
$_products = $_products_all['products'];
$_products_no_weight = $_products_all['productsNoWeight'];
$_timePriorities = Mage::helper('extensa_econt')->getPriorityTimeTypes();
?>
<script type="text/javascript">
  function formHandler() {
    return {
      packageType: 'pack',
      packageCount: '<?= $this->getPackCount(); ?>',
      palletData: [],
      clearPalletData(event) {
        if (event.target.value !== 'pallet') {
          this.palletData = []
        }
      },
      addPalletField() {
        this.palletData.push({
          weight: '',
          width: '',
          length: '',
          height: '',
        });
      },
      removeField(index) {
        this.palletData.splice(index, 1);
      }
    }
  }
</script>
<style>
    .pallet-info {
        display: flex;
        flex-flow: row;
        margin-top: 5px;
        margin-bottom: 5px;
    }

    .pallet-info-label {
        width: 40%;
    }

    .pallet-info-input {
        display: block;
        width: 60% !important;
    }
</style>
<div id="<?= $_htmlId; ?>_form" x-data="formHandler">
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head"><?= $this->getTabTitle(); ?></h4>
            <div class="tools">
                <a href="<?= Mage::helper('extensa_econt')->getCourierUrl(); ?>"><?= Mage::helper('extensa_econt')->__('Заявка за куриер'); ?></a>
            </div>
        </div>
        <div class="fieldset">
            <input type="hidden" name="html_id" value="<?= $_htmlId; ?>"/>
            <input type="hidden" name="<?= $_htmlId; ?>[order_id]" value="<?= $this->getEcontOrderId(); ?>"/>
            <table cellspacing="0" class="form-list">
                <?php if ($_products_no_weight): ?>
                    <tr>
                        <td class="label"><label><?= Mage::helper('extensa_econt')->__('Продукти без тегло'); ?></label>
                        </td>
                        <td class="value">
                            <?php foreach ($_products_no_weight as $_product_no_weight): ?>
                                [ <a href="<?= $_product_no_weight['href']; ?>"
                                     target="_blank"><?= $_product_no_weight['text']; ?></a> ]
                            <?php endforeach; ?>
                            <p class="note">
                                <span><?= Mage::helper('extensa_econt')->__('трябва да попълните теглото им, за да можете да генерирате товарителница'); ?></span>
                            </p>
                        </td>
                    </tr>
                <?php endif; ?>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_shipping_to"><?= Mage::helper('extensa_econt')->__('Доставка'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_shipping_to" name="<?= $_htmlId; ?>[shipping_to]" class="select">
                            <option value="OFFICE" <?php if ($this->getShippingTo() == 'OFFICE'): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('до офис'); ?></option>
                            <option value="DOOR" <?php if ($this->getShippingTo() == 'DOOR'): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('до врата'); ?></option>
                            <option value="APS" <?php if ($this->getShippingTo() == 'APS'): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('до АПС'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_office_city_id"><?= Mage::helper('extensa_econt')->__('Населено място'); ?></label>
                    </td>
                    <td class="value">
                        <select title="<?= Mage::helper('extensa_econt')->__('Населено място'); ?>"
                                id="<?= $_htmlId; ?>_office_city_id" name="<?= $_htmlId; ?>[office_city_id]"
                                class="select required-entry">
                            <option value=""><?= Mage::helper('extensa_econt')->__('--Please Select--'); ?></option>
                            <?php foreach ($this->getCities() as $_city): ?>
                                <option value="<?= $_city->getCityId(); ?>" <?php if ($this->getOffice() && ($this->getOffice()->getCityId() == $_city->getCityId())): ?> selected="selected"<?php endif; ?>><?= $_city->getName(); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label for="<?= $_htmlId; ?>_office_locator"></label></td>
                    <td class="value">
                        <button type="button" class="button" id="<?= $_htmlId; ?>_office_locator"
                                title="<?= Mage::helper('extensa_econt')->__('Офис Локатор'); ?>">
                            <span><span><?= Mage::helper('extensa_econt')->__('Офис Локатор'); ?></span></span></button>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_office_id"><?= Mage::helper('extensa_econt')->__('Офис'); ?></label>
                    </td>
                    <td class="value">
                        <select title="<?= Mage::helper('extensa_econt')->__('Офис'); ?>"
                                id="<?= $_htmlId; ?>_office_id" name="<?= $_htmlId; ?>[office_id]"
                                class="select required-entry">
                            <option value=""><?= Mage::helper('extensa_econt')->__('--Please Select--'); ?></option>
                            <?php foreach ($this->getOffices() as $_office): ?>
                                <option value="<?= $_office->getOfficeId(); ?>" <?php if ($this->getOffice() && ($this->getOffice()->getOfficeId() == $_office->getOfficeId())): ?> selected="selected"<?php endif; ?>><?= $_office->getOfficeCode() . ', ' . $_office->getName() . ', ' . $_office->getAddress(); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_office_code"><?= Mage::helper('extensa_econt')->__('Код на офиса'); ?></label>
                    </td>
                    <td class="value"><input type="text"
                                             title="<?= Mage::helper('extensa_econt')->__('Код на офиса'); ?>"
                                             id="<?= $_htmlId; ?>_office_code" name="<?= $_htmlId; ?>[office_code]"
                                             value="<?php if ($this->getOffice()): echo $this->getOffice()->getOfficeCode(); endif; ?>"
                                             class="input-text disabled" disabled="disabled"/></td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_office_city_aps_id"><?= Mage::helper('extensa_econt')->__('Населено място'); ?></label>
                    </td>
                    <td class="value">
                        <select title="<?= Mage::helper('extensa_econt')->__('Населено място'); ?>"
                                id="<?= $_htmlId; ?>_office_city_aps_id" name="<?= $_htmlId; ?>[office_city_aps_id]"
                                class="select required-entry">
                            <option value=""><?= Mage::helper('extensa_econt')->__('--Please Select--'); ?></option>
                            <?php foreach ($this->getCitiesAps() as $_city_aps): ?>
                                <option value="<?= $_city_aps->getCityId(); ?>" <?php if ($this->getOffice() && ($this->getOffice()->getCityId() == $_city_aps->getCityId())): ?> selected="selected"<?php endif; ?>><?= $_city_aps->getName(); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_office_aps_id"><?= Mage::helper('extensa_econt')->__('Офис'); ?></label>
                    </td>
                    <td class="value">
                        <select title="<?= Mage::helper('extensa_econt')->__('Офис'); ?>"
                                id="<?= $_htmlId; ?>_office_aps_id" name="<?= $_htmlId; ?>[office_aps_id]"
                                class="select required-entry">
                            <option value=""><?= Mage::helper('extensa_econt')->__('--Please Select--'); ?></option>
                            <?php foreach ($this->getOfficesAps() as $_office_aps): ?>
                                <option value="<?= $_office_aps->getOfficeId(); ?>" <?php if ($this->getOffice() && ($this->getOffice()->getOfficeId() == $_office_aps->getOfficeId())): ?> selected="selected"<?php endif; ?>><?= $_office_aps->getOfficeCode() . ', ' . $_office_aps->getName() . ', ' . $_office_aps->getAddress(); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_office_aps_code"><?= Mage::helper('extensa_econt')->__('Код на офиса'); ?></label>
                    </td>
                    <td class="value"><input type="text"
                                             title="<?= Mage::helper('extensa_econt')->__('Код на офиса'); ?>"
                                             id="<?= $_htmlId; ?>_office_aps_code"
                                             name="<?= $_htmlId; ?>[office_aps_code]"
                                             value="<?php if ($this->getOffice()): echo $this->getOffice()->getOfficeCode(); endif; ?>"
                                             class="input-text disabled" disabled="disabled"/></td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_city"><?= Mage::helper('extensa_econt')->__('Населено място'); ?></label>
                    </td>
                    <td class="value">
                        <input type="text" title="<?= Mage::helper('extensa_econt')->__('Населено място'); ?>"
                               id="<?= $_htmlId; ?>_city" name="<?= $_htmlId; ?>[city]"
                               value="<?php if ($this->getReceiverCity()): ?><?= $this->escapeHtml($this->getReceiverCity()->getName()); ?><?php endif; ?>"
                               class="input-text required-entry"/><input type="hidden" id="<?= $_htmlId; ?>_city_id"
                                                                         name="<?= $_htmlId; ?>[city_id]"
                                                                         value="<?php if ($this->getReceiverCity()): ?><?= $this->escapeHtml($this->getReceiverCity()->getCityId()); ?><?php endif; ?>"/>
                        <span id="<?= $_htmlId; ?>_city_indicator" class="autocomplete-indicator"
                              style="display: none; position: absolute; margin-left: -17px;">
                            <img src="<?= $this->getSkinUrl('images/ajax-loader.gif'); ?>"
                                 alt="<?= Mage::helper('extensa_econt')->__('Loading...'); ?>"
                                 title="<?= Mage::helper('extensa_econt')->__('Loading...') ?>" class="v-middle"/>
                        </span>
                        <div id="<?= $_htmlId; ?>_city_autocomplete" class="autocomplete" style="display: none;"></div>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_post_code"><?= Mage::helper('extensa_econt')->__('Пощенски код'); ?></label>
                    </td>
                    <td class="value"><input type="text"
                                             title="<?= Mage::helper('extensa_econt')->__('Пощенски код'); ?>"
                                             id="<?= $_htmlId; ?>_post_code" name="<?= $_htmlId; ?>[postcode]"
                                             value="<?= $this->escapeHtml($this->getReceiverPostcode()); ?>"
                                             class="input-text disabled" disabled="disabled"/></td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_quarter"><?= Mage::helper('extensa_econt')->__('Квартал'); ?></label>
                    </td>
                    <td class="value">
                        <input type="text" title="<?= Mage::helper('extensa_econt')->__('Квартал'); ?>"
                               id="<?= $_htmlId; ?>_quarter" name="<?= $_htmlId; ?>[quarter]"
                               value="<?= $this->escapeHtml($this->getReceiverQuarter()); ?>"
                               class="input-text <?= $_htmlId; ?>-required-quarter"/>
                        <span id="<?= $_htmlId; ?>_quarter_indicator" class="autocomplete-indicator"
                              style="display: none; position: absolute; margin-left: -17px;">
                            <img src="<?= $this->getSkinUrl('images/ajax-loader.gif'); ?>"
                                 alt="<?= Mage::helper('extensa_econt')->__('Loading...'); ?>"
                                 title="<?= Mage::helper('extensa_econt')->__('Loading...') ?>" class="v-middle"/>
                        </span>
                        <div id="<?= $_htmlId; ?>_quarter_autocomplete" class="autocomplete"
                             style="display: none;"></div>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_street"><?= Mage::helper('extensa_econt')->__('Улица'); ?></label>
                    </td>
                    <td class="value">
                        <input type="text" title="<?= Mage::helper('extensa_econt')->__('Улица'); ?>"
                               id="<?= $_htmlId; ?>_street" name="<?= $_htmlId; ?>[street]"
                               value="<?= $this->escapeHtml($this->getReceiverStreet()); ?>"
                               class="input-text <?= $_htmlId; ?>-required-street"/>
                        <span id="<?= $_htmlId; ?>_street_indicator" class="autocomplete-indicator"
                              style="display: none; position: absolute; margin-left: -17px;">
                            <img src="<?= $this->getSkinUrl('images/ajax-loader.gif'); ?>"
                                 alt="<?= Mage::helper('extensa_econt')->__('Loading...'); ?>"
                                 title="<?= Mage::helper('extensa_econt')->__('Loading...') ?>" class="v-middle"/>
                        </span>
                        <div id="<?= $_htmlId; ?>_street_autocomplete" class="autocomplete"
                             style="display: none;"></div>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_street_num"><?= Mage::helper('extensa_econt')->__('Номер'); ?></label>
                    </td>
                    <td class="value"><input type="text" title="<?= Mage::helper('extensa_econt')->__('Номер'); ?>"
                                             id="<?= $_htmlId; ?>_street_num" name="<?= $_htmlId; ?>[street_num]"
                                             value="<?= $this->escapeHtml($this->getReceiverStreetNum()); ?>"
                                             class="input-text <?= $_htmlId; ?>-required-street-num"/></td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_other"><?= Mage::helper('extensa_econt')->__('Друго'); ?></label>
                    </td>
                    <td class="value"><input type="text" title="<?= Mage::helper('extensa_econt')->__('Друго'); ?>"
                                             id="<?= $_htmlId; ?>_other" name="<?= $_htmlId; ?>[other]"
                                             value="<?= $this->escapeHtml($this->getReceiverOther()); ?>"
                                             class="input-text <?= $_htmlId; ?>-required-other"/></td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_address_id"><?= Mage::helper('extensa_econt')->__('Адрес на подателя'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_address_id" name="<?= $_htmlId; ?>[address_id]"
                                class="required-entry select">
                            <?php foreach ($this->getSenderAddresses() as $_address_id => $_address): ?>
                                <option value="<?= $_address_id; ?>" <?php if ($this->getExpressCityCourier() == $_address_id): ?> selected="selected"<?php endif; ?>><?= $_address['name']; ?></option>
                            <?php endforeach; ?>
                        </select>
                        <?php foreach ($this->getSenderAddresses() as $_address_id => $_address): ?>
                            <?php $post = isset($_address['post_code']) ? $_address['post_code'] : $_address['city_post_code']; ?>
                            <input type="hidden" id="<?= $_htmlId; ?>_address_postcode_<?= $_address_id; ?>"
                                   value="<?= $post; ?>"/>
                        <?php endforeach; ?>
                    </td>
                </tr>
                <tr <?php if ($this->getShippingFrom() == 'APS'): ?> style="display: none;"<?php endif; ?>>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_sms_no"><?= Mage::helper('extensa_econt')->__('номер'); ?></label>
                    </td>
                    <td class="value"><input type="text" id="<?= $_htmlId; ?>_sms_no" name="<?= $_htmlId; ?>[sms_no]"
                                             value="<?= $this->getSmsNo(); ?>" class="required-entry input-text"/></td>
                </tr>
                <tr <?php if ($this->getShippingFrom() == 'APS'): ?> style="display: none;"<?php endif; ?>>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_invoice_before_cd"><?= Mage::helper('extensa_econt')->__('Предай фактура преди плащане на наложения платеж'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_invoice_before_cd" name="<?= $_htmlId; ?>[invoice_before_cd]"
                                class="select">
                            <option value="1" <?php if ($this->getInvoiceBeforeCd()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('Yes'); ?></option>
                            <option value="0" <?php if (!$this->getInvoiceBeforeCd()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('No'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_dc"><?= Mage::helper('extensa_econt')->__('Обратна разписка'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_dc" name="<?= $_htmlId; ?>[dc]" class="select">
                            <option value="2" <?php if ($this->getDcCp()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('Да, Обратна стокова разписка'); ?></option>
                            <option value="1" <?php if ($this->getDc()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('Да, Обратна разписка'); ?></option>
                            <option value="0" <?php if (!$this->getDc()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('Не'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_disposition"><?= Mage::helper('extensa_econt')->__('Да се добавя ли разпореждане от следния тип'); ?></label>
                    </td>
                    <td class="value">
                        <ul id="<?= $_htmlId; ?>_disposition" class="checkboxes">
                            <li><input type="checkbox" id="<?= $_htmlId; ?>_pay_after_accept"
                                       name="<?= $_htmlId; ?>[pay_after_accept]"
                                       value="1" <?php if ($this->getPayAfterAccept()) { ?> checked="checked"<?php } ?>
                                       onclick="$('<?= $_htmlId; ?>_pay_after_test').checked = false;$('<?= $_htmlId; ?>_pay_choose').checked = false;"/>
                                <label for="<?= $_htmlId; ?>_pay_after_accept"><?= Mage::helper('extensa_econt')->__('разпореждам пратката да се прегледа от получателя и да плати наложения платеж само ако приеме стоката ми'); ?></label>
                            </li>
                            <li style="padding-left: 20px"><input type="checkbox" id="<?= $_htmlId; ?>_pay_after_test"
                                                                  name="<?= $_htmlId; ?>[pay_after_test]"
                                                                  value="1" <?php if ($this->getPayAfterTest()) { ?> checked="checked"<?php } ?>
                                                                  onclick="$('<?= $_htmlId; ?>_pay_after_accept').checked = true;"/>
                                <label for="<?= $_htmlId; ?>_pay_after_test"><?= Mage::helper('extensa_econt')->__('разпореждам пратката да се прегледа и тества от получателя и да плати наложения платеж само ако приеме стоката ми'); ?></label>
                            </li>
                            <li style="padding-left: 20px"><input type="checkbox" id="<?= $_htmlId; ?>_pay_choose"
                                                                  name="<?= $_htmlId; ?>[pay_choose]"
                                                                  value="1" <?php if ($this->getPayChoose()) { ?> checked="checked"<?php } ?>
                                                                  onclick="$('<?= $_htmlId; ?>_pay_after_accept').checked = true;"/>
                                <label for="<?= $_htmlId; ?>_pay_choose"><?= Mage::helper('extensa_econt')->__('разпореждам клиентът да може да избере един или повече от артикулите в пратката и да върне останалите на куриера'); ?></label>
                            </li>
                        </ul>
                    </td>
                </tr>
                <tr id="<?= $_htmlId; ?>_priority_time" <?php if ($this->getShippingTo() != 'DOOR' || $this->getPriorityTime() === false) { ?> style="display: none;"<?php } ?>>
                    <td class="label">
                        <input type="checkbox" id="<?= $_htmlId; ?>_priority_time_cb"
                               name="<?= $_htmlId; ?>[priority_time_cb]" value="1"
                               class="checkbox" <?php if ($this->getPriorityTimeCb()) { ?> checked="checked"<?php } ?>
                               onclick="toggleValueElements(this, this.parentNode.parentNode, null, !this.checked);"/>
                        <label for="<?= $_htmlId; ?>_priority_time_cb"
                               style="display: inline;"><?= Mage::helper('extensa_econt')->__('Час за приоритет'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_priority_time_type_id"
                                name="<?= $_htmlId; ?>[priority_time_type_id]" class="select">
                            <?php foreach ($_timePriorities as $_priority_time_type => $_priority_time_type_value): ?>
                                <option value="<?= $_priority_time_type; ?>" <?php if ($_priority_time_type == $this->getPriorityTimeTypeId()): $_priority_time_hours = $_priority_time_type; ?> selected="selected"<?php endif; ?>><?= $_priority_time_type_value['name']; ?></option>
                            <?php endforeach; ?>
                        </select>
                        <select id="<?= $_htmlId; ?>_priority_time_hour_id"
                                name="<?= $_htmlId; ?>[priority_time_hour_id]" class="select">
                            <?php foreach ($_timePriorities['IN']['hours'] as $_priority_time_hour): ?>
                                <option value="<?= $_priority_time_hour; ?>" <?php if ($_priority_time_hour == $this->getPriorityTimeHourId()): ?> selected="selected"<?php endif; ?>><?= $_priority_time_hour; ?> <?= Mage::helper('extensa_econt')->__('ч.'); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <tr <?php if ($this->getShippingFrom() == 'APS' || $_delivery_days['delivery_days']): ?> style="display: none;"<?php endif; ?>>
                    <td class="label">
                        <label for="<?= $_htmlId; ?>_saturday"><?= Mage::helper('extensa_econt')->__('Желаете ли да бъдете обслужвани в събота?'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_saturday" name="<?= $_htmlId; ?>[saturday]" class="select">
                            <option value="1"><?= Mage::helper('extensa_econt')->__('Yes'); ?></option>
                            <option value="0"
                                    selected="selected"><?= Mage::helper('extensa_econt')->__('No'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr <?php if ($this->getShippingFrom() == 'APS'): ?> style="display: none;"<?php endif; ?>>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_inventory"><?= Mage::helper('extensa_econt')->__('Подаване на опис'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_inventory" name="<?= $_htmlId; ?>[inventory]" class="select">
                            <option value="1" <?php if ($this->getShippingFrom() != 'APS' && $this->getInventory()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('Yes'); ?></option>
                            <option value="0" <?php if ($this->getShippingFrom() == 'APS' || !$this->getInventory()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('No'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr <?php if ($this->getShippingFrom() == 'APS'): ?> style="display: none;"<?php endif; ?>>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_inventory_type"><?= Mage::helper('extensa_econt')->__('Метод за подаване на опис'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_inventory_type" name="<?= $_htmlId; ?>[inventory_type]"
                                class="select">
                            <?php foreach ($this->getInventoryTypes() as $_inventory_type): ?>
                                <option value="<?= $_inventory_type['value']; ?>"
                                        <?php if ($_inventory_type['value'] == $this->getInventoryType()): ?>selected="selected"<?php endif; ?>><?= $_inventory_type['label']; ?></option>
                            <?php endforeach; ?>
                        </select>
                    </td>
                </tr>
                <tr <?php if ($this->getShippingFrom() == 'APS'): ?> style="display: none;"<?php endif; ?>>
                    <td class="label"><label for="<?= $_htmlId; ?>_inventory_type_loading"></label></td>
                    <td class="value"><p class="note" id="<?= $_htmlId; ?>_inventory_type_loading">
                            <span><?= Mage::helper('extensa_econt')->__('Моля, разпечатайте опис на стоката и го приложете към генерираната товарителница.'); ?></span>
                        </p></td>
                </tr>
                <tr <?php if ($this->getShippingFrom() == 'APS'): ?> style="display: none;"<?php endif; ?>>
                    <td class="label"><label for="<?= $_htmlId; ?>_inventory_type_digital"></label></td>
                    <td class="value">
                        <div class="grid" id="<?= $_htmlId; ?>_inventory_type_digital">
                            <table cellpadding="0" cellspacing="0" class="border">
                                <thead>
                                <tr class="headings">
                                    <th><?= Mage::helper('extensa_econt')->__('Инвентарен номер'); ?></th>
                                    <th><?= Mage::helper('extensa_econt')->__('Описание'); ?></th>
                                    <th><?= Mage::helper('extensa_econt')->__('Тегло'); ?></th>
                                    <th><?= Mage::helper('extensa_econt')->__('Цена'); ?></th>
                                    <th></th>
                                </tr>
                                </thead>
                                <tfoot>
                                <tr>
                                    <td colspan="4"></td>
                                    <td>
                                        <button type="button" class="scalable add" id="<?= $_htmlId; ?>_add_product">
                                            <span><span><span><?= Mage::helper('extensa_econt')->__('Add'); ?></span></span></span>
                                        </button>
                                    </td>
                                </tr>
                                </tfoot>
                                <tbody id="<?= $_htmlId; ?>_products">
                                <?php $_product_row = 0; ?>
                                <?php foreach ($_products as $_product): ?>
                                    <tr id="<?= $_htmlId; ?>_product_<?= $_product_row; ?>">
                                        <td><input type="text" id="<?= $_htmlId; ?>_product_id_<?= $_product_row; ?>"
                                                   name="<?= $_htmlId; ?>[products][<?= $_product_row; ?>][product_id]"
                                                   value="<?= $_product['product_id']; ?>" class="input-text"
                                                   style="width: 70px"/></td>
                                        <td><input type="text" id="<?= $_htmlId; ?>_product_name_<?= $_product_row; ?>"
                                                   name="<?= $_htmlId; ?>[products][<?= $_product_row; ?>][name]"
                                                   value="<?= $_product['name']; ?>" class="input-text"/></td>
                                        <td><input type="text"
                                                   id="<?= $_htmlId; ?>_product_weight_<?= $_product_row; ?>"
                                                   name="<?= $_htmlId; ?>[products][<?= $_product_row; ?>][weight]"
                                                   value="<?= $_product['weight']; ?>" class="input-text"
                                                   style="width: 70px"/></td>
                                        <td><input type="text" id="<?= $_htmlId; ?>_product_price_<?= $_product_row; ?>"
                                                   name="<?= $_htmlId; ?>[products][<?= $_product_row; ?>][price]"
                                                   value="<?= $_product['price']; ?>" class="input-text"
                                                   style="width: 70px"/></td>
                                        <td>
                                            <button type="button" class="scalable delete"
                                                    onclick="$('<?= $_htmlId; ?>_product_<?= $_product_row; ?>').remove();">
                                                <span><span><span><?= Mage::helper('extensa_econt')->__('Delete'); ?></span></span></span>
                                            </button>
                                        </td>
                                    </tr>
                                    <?php $_product_row++; ?>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
                <tr <?php if ($this->getShippingFrom() == 'APS'): ?> style="display: none;"<?php endif; ?>>
                    <td class="label"><label
                                for="<?= $_htmlId; ?>_instruction"><?= Mage::helper('extensa_econt')->__('Ще използвате ли указания?'); ?></label>
                    </td>
                    <td class="value">
                        <select id="<?= $_htmlId; ?>_instruction" name="<?= $_htmlId; ?>[instruction]" class="select">
                            <option value="1" <?php if ($this->getShippingFrom() != 'APS' && $this->getInstruction()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('Yes'); ?></option>
                            <option value="0" <?php if ($this->getShippingFrom() == 'APS' || !$this->getInstruction()): ?> selected="selected"<?php endif; ?>><?= Mage::helper('extensa_econt')->__('No'); ?></option>
                        </select>
                    </td>
                </tr>
                <tr style="display: none;" <?php if ($this->getShippingFrom() == 'APS'): ?> style="display: none;"<?php endif; ?>>
                    <td class="label"><label for="<?= $_htmlId; ?>_instructions"></label></td>
                    <td class="value">
                        <div class="grid" id="<?= $_htmlId; ?>_instructions">
                            <table cellpadding="0" cellspacing="0" class="border">
                                <tbody>
                                <tr class="headings">
                                    <th><?= Mage::helper('extensa_econt')->__('Tип'); ?></th>
                                    <th><?= Mage::helper('extensa_econt')->__('Списък с указания'); ?></th>
                                </tr>
                                <?php $_instructions = $this->getInstructions(); ?>
                                <?php $_instructionSelect = $this->getInstructionSelect(); ?>
                                <?php foreach ($this->getInstructionsTypes() as $_instructions_type): ?>
                                    <tr>
                                        <td><?= $_instructions_type['label']; ?></td>
                                        <td>
                                            <select id="<?= $_htmlId; ?>_instructions_id_<?= $_instructions_type['value']; ?>"
                                                    name="<?= $_htmlId; ?>[instructions_id_<?= $_instructions_type['value']; ?>]"
                                                    class="select">
                                                <option value=""><?= Mage::helper('extensa_econt')->__('--Кликнете Синхронизирай данни--'); ?></option>
                                                <?php if (isset($_instructions[$_instructions_type['value']])): ?>
                                                    <?php foreach ($_instructions[$_instructions_type['value']] as $_instruction_id): ?>
                                                        <option value="<?= $_instruction_id; ?>" <?php if (isset($_instructionSelect[$_instructions_type['value']]) && $_instructionSelect[$_instructions_type['value']] == $_instruction_id): ?> selected="selected"<?php endif; ?>><?= $_instruction_id; ?></option>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>
                                            </select>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
                <?php // custom fields added for praktis ?>
                <tr>
                    <td class="label">
                        <label for="<?= $_htmlId; ?>_weight">
                            <?= $this->__('Weight (kg)'); ?>
                        </label>
                    </td>
                    <td class="value">
                        <input type="text"
                               title="<?= $this->__('Weight (kg)'); ?>"
                               id="<?= $_htmlId; ?>_weight"
                               name="<?= $_htmlId; ?>[custom_weight]"
                               value="<?= $this->getOrderWeight(); ?>"
                               class="input-text required-entry validate-greater-than-zero"/>
                    </td>
                </tr>
                <tr>
                    <td class="label">
                        <label for="<?= $_htmlId; ?>_shipment_type">
                            <?= $this->__('Тип пратка'); ?>
                        </label>
                    </td>
                    <td class="value">
                        <select title="<?= $this->__('Тип пратка'); ?>"
                                id=<?= $_htmlId; ?>_shipment_type"
                                name="<?= $_htmlId; ?>[shipment_type]"
                                class="select"
                                x-model="packageType"
                                x-on:change="clearPalletData($event)"
                        >
                            <option value="pack">
                                <?= $this->__('Пакет'); ?>
                            </option>
                            <option value="pallet">
                                <?= $this->__('Пале'); ?>
                            </option>
                        </select>
                    </td>
                </tr>
                <tr x-show="packageType == 'pack'">
                    <td class="label">
                        <label for="<?= $_htmlId; ?>_count">
                            <?= $this->__('Брой пакети / опаковки'); ?>
                        </label>
                    </td>
                    <td class="value">
                        <input type="text"
                               title="<?= $this->__('Брой пакети / опаковки'); ?>"
                               id="<?= $_htmlId; ?>_count"
                               name="<?= $_htmlId; ?>[pack_count]"
                               x-model="packageCount"
                               class="input-text required-entry validate-greater-than-zero"/>
                    </td>
                </tr>
                <?php // pallet fields ?>
                <template x-for="(field, index) in palletData"
                          :key="index">
                    <tr>
                        <td class="label">
                            <div>
                                <?= $this->__('Пале'); ?> <span x-text="1 + index"></span>
                            </div>
                        </td>
                        <td class="value">
                            <button type="button"
                                    class="scalable delete"
                                    @click="removeField(index)">
                                <span><span><span><?= $this->__('Премахни'); ?></span></span></span>
                            </button>
                            <div class="pallet-info">
                                <label class="pallet-info-label">
                                    <?= $this->__('Тегло (кг)'); ?>
                                </label>
                                <input x-model="field.weight"
                                       type="text"
                                       x-bind:name="`<?= $_htmlId; ?>[pallet][${index}][weight]`"
                                       class="input-text required-entry validate-greater-than-zero pallet-info-input"
                                />
                            </div>
                            <div class="pallet-info">
                                <label class="pallet-info-label">
                                    <?= $this->__('Ширина (см)'); ?>
                                </label>
                                <input x-model="field.width"
                                       x-bind:name="`<?= $_htmlId; ?>[pallet][${index}][width]`"
                                       type="text"
                                       class="input-text required-entry validate-greater-than-zero pallet-info-input"
                                />
                            </div>
                            <div class="pallet-info">
                                <label class="pallet-info-label">
                                    <?= $this->__('Дължина (см)'); ?>
                                </label>
                                <input x-model="field.length"
                                       x-bind:name="`<?= $_htmlId; ?>[pallet][${index}][length]`"
                                       type="text"
                                       class="input-text required-entry validate-greater-than-zero pallet-info-input"
                                />
                            </div>
                            <div class="pallet-info">
                                <label class="pallet-info-label">
                                    <?= $this->__('Височина (см)'); ?>
                                </label>
                                <input x-model="field.height"
                                       x-bind:name="`<?= $_htmlId; ?>[pallet][${index}][height]`"
                                       type="text"
                                       class="input-text required-entry validate-greater-than-zero pallet-info-input"
                                />
                            </div>
                        </td>
                    </tr>
                </template>
                <tr x-show="packageType == 'pallet'">
                    <td class="label">
                    </td>
                    <td class="value">
                        <button type="button" class="scalable add"
                                @click="addPalletField()">
                            <span><span><span><?= $this->__('Добави Пале'); ?></span></span></span>
                        </button>
                    </td>
                </tr>
                <?php // end palled fields ?>
                <tr>
                    <td class="label">
                        <label for="<?= $_htmlId; ?>_insurance">
                            <?= $this->__('Insurance'); ?>
                        </label>
                    </td>
                    <td class="value">
                        <select title="<?= $this->__('Insurance'); ?>"
                                id="<?= $_htmlId; ?>_insurance"
                                name="<?= $_htmlId; ?>[custom_insurance]"
                                class="select"
                        >
                            <option value="0">
                                <?= $this->__('No'); ?>
                            </option>
                            <option value="1" selected="selected">
                                <?= $this->__('Yes'); ?>
                            </option>
                        </select>
                        <p class="note">
                            <span><?= $this->__("Cannot add Insurance when shipping to APS"); ?></span>
                        </p>
                    </td>
                </tr>
                <tr>
                    <td class="label">
                        <label for="<?= $_htmlId; ?>_insurance_total">
                            <?= $this->__('Insurance Total'); ?>
                        </label>
                    </td>
                    <td class="value">
                        <input type="text"
                               title="<?= $this->__('Insurance Total'); ?>"
                               id="<?= $_htmlId; ?>_insurance_total"
                               name="<?= $_htmlId; ?>[custom_insurance_total]"
                               value="<?= $this->getInsuranceTotal() ?>"
                               class="input-text validate-greater-than-zero"
                        />
                    </td>
                </tr>
                <tr>
                    <td class="label">
                        <label for="<?= $_htmlId; ?>_custom_cod">
                            <?= $this->__('Cash On Delivery'); ?>
                        </label>
                    </td>
                    <td class="value">
                        <select title="<?= $this->__('Cash On Delivery'); ?>"
                                id="<?= $_htmlId; ?>_custom_cod"
                                name="<?= $_htmlId; ?>[custom_cod]" class="select">
                            <option value="1" <?php if ($this->isCod()): ?> selected="selected"<?php endif; ?>>
                                <?= $this->__('Yes'); ?>
                            </option>
                            <option value="0" <?php if (!$this->isCod()): ?> selected="selected"<?php endif; ?>>
                                <?= $this->__('No'); ?>
                            </option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td class="label">
                        <label for="<?= $_htmlId; ?>_custom_cod_total">
                            <?= $this->__('Cash On Delivery Total'); ?>
                        </label>
                    </td>
                    <td class="value">
                        <input type="text"
                               title="<?= $this->__('Cash On Delivery Total'); ?>"
                               id="<?= $_htmlId; ?>_custom_cod_total"
                               name="<?= $_htmlId; ?>[custom_cod_total]"
                               value="<?= $this->getCodTotal(); ?>"
                               class="input-text validate-greater-than-zero"/>
                        <p class="note">
                            <span><?= $this->__("Total without shipping"); ?></span>
                        </p>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        <button type="button" title="<?= Mage::helper('extensa_econt')->__('Генериране'); ?>"
                                id="<?= $_htmlId; ?>_submit" class="scalable save f-right">
                            <span><span><span><?= Mage::helper('extensa_econt')->__('Генериране'); ?></span></span></span>
                        </button>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>
<script type="text/javascript">
  //<![CDATA[
  new Ajax.Autocompleter(
    '<?= $_htmlId; ?>_city',
    '<?= $_htmlId; ?>_city_autocomplete',
    '<?= Mage::helper('extensa_econt')->getAutocompleteCityUrl(); ?>',
    {
      indicator: '<?= $_htmlId; ?>_city_indicator',
      callback: <?= $_htmlId; ?>_autocomplete_address_city_callback,
      afterUpdateElement: <?= $_htmlId; ?>_autocomplete_address_city_update,
      onSuccess: function (response) {
        if (response.responseText.search('li') == -1) {
          $('<?= $_htmlId; ?>_city').setValue('');
        }
      },
      onShow: function (element, update) {
        Effect.Appear(update, {duration: 0});
      }
    }
  );

  function <?= $_htmlId; ?>_autocomplete_address_city_callback(input, query) {
    $('<?= $_htmlId; ?>_city_id').setValue('');
    $('<?= $_htmlId; ?>_post_code').setValue('');
    $('<?= $_htmlId; ?>_quarter').setValue('');
    $('<?= $_htmlId; ?>_street').setValue('');
    $('<?= $_htmlId; ?>_street_num').setValue('');
    $('<?= $_htmlId; ?>_other').setValue('');

    return query;
  }

  function <?= $_htmlId; ?>_autocomplete_address_city_update(input, li) {
    $('<?= $_htmlId; ?>_city_id').setValue(li.readAttribute('city_id'));
    $('<?= $_htmlId; ?>_post_code').setValue(li.readAttribute('post_code'));

      <?= $_htmlId; ?>_display_express_city_courier();
      <?= $_htmlId; ?>_display_priority_time();
  }

  new Ajax.Autocompleter(
    '<?= $_htmlId; ?>_quarter',
    '<?= $_htmlId; ?>_quarter_autocomplete',
    '<?= Mage::helper('extensa_econt')->getAutocompleteQuarterUrl(); ?>',
    {
      indicator: '<?= $_htmlId; ?>_quarter_indicator',
      callback: <?= $_htmlId; ?>_autocomplete_address_quarter_callback,
      onSuccess: function (response) {
        if (response.responseText.search('li') == -1) {
          $('<?= $_htmlId; ?>_quarter').setValue('');
        }
      },
      onShow: function (element, update) {
        Effect.Appear(update, {duration: 0});
      }
    }
  );

  function <?= $_htmlId; ?>_autocomplete_address_quarter_callback(input, query) {
    return query + '&city_id=' + $F('<?= $_htmlId; ?>_city_id');
  }

  new Ajax.Autocompleter(
    '<?= $_htmlId; ?>_street',
    '<?= $_htmlId; ?>_street_autocomplete',
    '<?= Mage::helper('extensa_econt')->getAutocompleteStreetUrl(); ?>',
    {
      indicator: '<?= $_htmlId; ?>_street_indicator',
      callback: <?= $_htmlId; ?>_autocomplete_address_street_callback,
      onSuccess: function (response) {
        if (response.responseText.search('li') == -1) {
          $('<?= $_htmlId; ?>_street').setValue('');
        }
      },
      onShow: function (element, update) {
        Effect.Appear(update, {duration: 0});
      }
    }
  );

  function <?= $_htmlId; ?>_autocomplete_address_street_callback(input, query) {
    return query + '&city_id=' + $F('<?= $_htmlId; ?>_city_id');
  }

  function <?= $_htmlId; ?>_get_office_locator() {
    url = '<?= Mage::helper('extensa_econt')->getOfficeLocatorUrl(); ?>' + '&shop_url=<?= Mage::getUrl('', ['_secure' => true]); ?>';

    if ($F('<?= $_htmlId; ?>_office_city_id')) {
      url += '&address=' + $('<?= $_htmlId; ?>_office_city_id').options[$('<?= $_htmlId; ?>_office_city_id').selectedIndex].text;
    }

      <?= $_htmlId; ?>_win = new Window({
        url: url,
        width: 1000,
        height: 800,
        destroyOnClose: true,
        minimizable: false,
        maximizable: false,
        recenterAuto: false,
        zIndex: 9999
      });
      <?= $_htmlId; ?>_win.showCenter(false, 50);
  }

  $('<?= $_htmlId; ?>_office_locator').observe('click', <?= $_htmlId; ?>_get_office_locator);

  function <?= $_htmlId; ?>_receive_message(event) {
    if (event.origin !== '<?= Mage::helper('extensa_econt')->getOfficeLocatorDomain(); ?>')
      return;

    map_data = event.data.split('||');

    new Ajax.Request(
      '<?= Mage::helper('extensa_econt')->getOfficeByCodeUrl(); ?>',
      {
        method: 'post',
        parameters: {
          office_code: map_data[0]
        },
        onSuccess: function (transport) {
          if (transport.responseText.isJSON()) {
            response = transport.responseText.evalJSON();

            if ($F('<?= $_htmlId; ?>_office_city_id') == response['city_id']) {
              options = $('<?= $_htmlId; ?>_office_id').options;

              for (i = 0; i < options.length; i++) {
                if (options[i].readAttribute('value') == response['office_id']) {
                  options[i].selected = true;
                  break;
                }
              }

              $('<?= $_htmlId; ?>_office_code').setValue(response['office_code']);
            } else {
              $('<?= $_htmlId; ?>_office_city_id').setValue(response['city_id']);
                <?= $_htmlId; ?>_get_offices(response, 0);
            }
          }
        }
      }
    );

      <?= $_htmlId; ?>_win.destroy();
  }

  if (window.addEventListener) {
    window.addEventListener('message', <?= $_htmlId; ?>_receive_message, false);
  } else if (window.attachEvent) {
    window.attachEvent('onmessage', <?= $_htmlId; ?>_receive_message);
  }

  function <?= $_htmlId; ?>_get_offices(office_data, aps) {
    if (aps == 0) {
      var office_id = 'office_id';
      var office_city_id = 'office_city_id';
      var office_code = 'office_code';
    } else {
      var office_id = 'office_aps_id';
      var office_city_id = 'office_city_aps_id';
      var office_code = 'office_aps_code';
    }

    $('<?= $_htmlId; ?>_' + office_id).update('<option value=""><?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Please Select--')); ?></option>');
    $('<?= $_htmlId; ?>_' + office_code).setValue('');

    if ($F('<?= $_htmlId; ?>_' + office_city_id)) {
      new Ajax.Request(
        '<?= Mage::helper('extensa_econt')->getOfficesUrl(); ?>',
        {
          method: 'post',
          parameters: {
            city_id: $F('<?= $_htmlId; ?>_' + office_city_id),
            delivery_type: 'to_office',
            aps: aps
          },
          onSuccess: function (transport) {
            if (transport.responseText.isJSON()) {
              response = transport.responseText.evalJSON();
              html = '<option value=""><?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('--Please Select--')); ?></option>';

              for (i = 0; i < response.length; i++) {
                html += '<option value="' + response[i]['office_id'] + '"';
                if (office_data && office_data['office_id'] == response[i]['office_id']) {
                  html += ' selected="selected"';
                }
                html += '>' + response[i]['office_code'] + ', ' + response[i]['name'] + ', ' + response[i]['address'] + '</option>';
              }

              $('<?= $_htmlId; ?>_' + office_id).update(html);

              if (office_data) {
                $('<?= $_htmlId; ?>_' + office_code).setValue(office_data['office_code']);
              }
            }
          }
        }
      );
    }
  }

  $('<?= $_htmlId; ?>_office_city_id').observe('change', function (evenet) {
      <?= $_htmlId; ?>_get_offices('', 0);
  });

  $('<?= $_htmlId; ?>_office_city_aps_id').observe('change', function (evenet) {
      <?= $_htmlId; ?>_get_offices('', 1);
  });

  function <?= $_htmlId; ?>_get_office(aps) {
    if (aps == 0) {
      var office_id = 'office_id';
      var office_code = 'office_code';
    } else {
      var office_id = 'office_aps_id';
      var office_code = 'office_aps_code';
    }

    $('<?= $_htmlId; ?>_' + office_code).setValue('');

    if ($F('<?= $_htmlId; ?>_' + office_id)) {
      new Ajax.Request(
        '<?= Mage::helper('extensa_econt')->getOfficeUrl(); ?>',
        {
          method: 'post',
          parameters: {
            office_id: $F('<?= $_htmlId; ?>_' + office_id)
          },
          onSuccess: function (transport) {
            if (transport.responseText.isJSON()) {
              response = transport.responseText.evalJSON();
              $('<?= $_htmlId; ?>_' + office_code).setValue(response.office_code);
            }
          }
        }
      );
    }
  }

  $('<?= $_htmlId; ?>_office_id').observe('change', function (evenet) {
      <?= $_htmlId; ?>_get_office(0);
  });

  $('<?= $_htmlId; ?>_office_aps_id').observe('change', function (evenet) {
      <?= $_htmlId; ?>_get_office(1);
  });

  function <?= $_htmlId; ?>_display_priority_time() {
    if ($F('<?= $_htmlId; ?>_address_postcode_' + $F('<?= $_htmlId; ?>_address_id')) == $F('<?= $_htmlId; ?>_post_code')) {
      $('<?= $_htmlId; ?>_priority_time').show();
    } else {
      $('<?= $_htmlId; ?>_priority_time').hide();
      $('<?= $_htmlId; ?>_priority_time_cb').checked = false;
      $('<?= $_htmlId; ?>_priority_time_type_id').disable();
      $('<?= $_htmlId; ?>_priority_time_hour_id').disable();
    }
  }

  $('<?= $_htmlId; ?>_address_id').observe('change', <?= $_htmlId; ?>_display_priority_time);

  function <?= $_htmlId; ?>_dc() {
    if ($F(this) == 1) {
      $$('select#<?= $_htmlId; ?>_dc_cp option').each(function (o) {
        if (o.readAttribute('value') == 0) {
          o.selected = true;
          throw $break;
        }
      });
    }
  }

  $('<?= $_htmlId; ?>_dc').observe('change', <?= $_htmlId; ?>_dc);

  function <?= $_htmlId; ?>_set_priority_time() {
    type = $F('<?= $_htmlId; ?>_priority_time_type_id');
    hour = $F('<?= $_htmlId; ?>_priority_time_hour_id');

    html = '';
    for (i = 10; i <= 17; i++) {
      html += '<option value="' + i + '">' + i + ' <?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('ч.')); ?></option>';
    }

    if (type == 'BEFORE') {
      $('<?= $_htmlId; ?>_priority_time_hour_id').update(html + '<option value="18">18 <?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('ч.')); ?></option>');
    } else if (type == 'IN') {
      $('<?= $_htmlId; ?>_priority_time_hour_id').update('<option value="9">9 <?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('ч.')); ?></option>' + html + '<option value="18">18 <?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('ч.')); ?></option>');
    } else if (type == 'AFTER') {
      $('<?= $_htmlId; ?>_priority_time_hour_id').update('<option value="9">9 <?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('ч.')); ?></option>' + html);
    }

    $$('select#<?= $_htmlId; ?>_priority_time_hour_id option').each(function (o) {
      if (o.readAttribute('value') == hour) {
        o.selected = true;
        throw $break;
      }
    });
  }

  $('<?= $_htmlId; ?>_priority_time_type_id').observe('change', <?= $_htmlId; ?>_set_priority_time);

  var <?= $_htmlId; ?>_product_row = <?= $_product_row; ?>;

  function <?= $_htmlId; ?>_add_product() {
    html = '<tr id="<?= $_htmlId; ?>_product_' + <?= $_htmlId; ?>_product_row + '">';
    html += '  <td><input type="text" id="<?= $_htmlId; ?>_product_id_' + <?= $_htmlId; ?>_product_row + '" name="<?= $_htmlId; ?>[products][' + <?= $_htmlId; ?>_product_row + '][product_id]" value="" class="input-text" style="width: 70px" /></td>';
    html += '  <td><input type="text" id="<?= $_htmlId; ?>_product_name_' + <?= $_htmlId; ?>_product_row + '" name="<?= $_htmlId; ?>[products][' + <?= $_htmlId; ?>_product_row + '][name]" value="" class="input-text" /></td>';
    html += '  <td><input type="text" id="<?= $_htmlId; ?>_product_weight_' + <?= $_htmlId; ?>_product_row + '" name="<?= $_htmlId; ?>[products][' + <?= $_htmlId; ?>_product_row + '][weight]" value="" class="input-text" style="width: 70px" /></td>';
    html += '  <td><input type="text" id="<?= $_htmlId; ?>_product_price_' + <?= $_htmlId; ?>_product_row + '" name="<?= $_htmlId; ?>[products][' + <?= $_htmlId; ?>_product_row + '][price]" value="" class="input-text" style="width: 70px" /></td>';
    html += '  <td><button type="button" class="scalable delete" onclick="$(\'<?= $_htmlId; ?>_product_' + <?= $_htmlId; ?>_product_row + '\').remove();"><span><span><span><?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Delete')); ?></span></span></span></button></td>';
    html += '</tr>';

    $('<?= $_htmlId; ?>_products').insert(html);

      <?= $_htmlId; ?>_product_row++;
  }

  $('<?= $_htmlId; ?>_add_product').observe('click', <?= $_htmlId; ?>_add_product);

  function <?= $_htmlId; ?>_fill_instructions(type) {
    $('<?= $_htmlId; ?>_instructions_' + type).setValue($F('<?= $_htmlId; ?>_instructions_id_' + type));
  }

  toggleValueElements($('<?= $_htmlId; ?>_priority_time_cb'), $('<?= $_htmlId; ?>_priority_time_cb').parentNode.parentNode, null, !$('<?= $_htmlId; ?>_priority_time_cb').checked);
  new FormElementDependenceController({
    '<?= $_htmlId; ?>_office_city_id': {'<?= $_htmlId; ?>_shipping_to': 'OFFICE'},
    '<?= $_htmlId; ?>_office_locator': {'<?= $_htmlId; ?>_shipping_to': 'OFFICE'},
    '<?= $_htmlId; ?>_office_id': {'<?= $_htmlId; ?>_shipping_to': 'OFFICE'},
    '<?= $_htmlId; ?>_office_code': {'<?= $_htmlId; ?>_shipping_to': 'OFFICE'},
    '<?= $_htmlId; ?>_office_city_aps_id': {'<?= $_htmlId; ?>_shipping_to': 'APS'},
    '<?= $_htmlId; ?>_office_aps_id': {'<?= $_htmlId; ?>_shipping_to': 'APS'},
    '<?= $_htmlId; ?>_office_aps_code': {'<?= $_htmlId; ?>_shipping_to': 'APS'},
    '<?= $_htmlId; ?>_city': {'<?= $_htmlId; ?>_shipping_to': 'DOOR'},
    '<?= $_htmlId; ?>_post_code': {'<?= $_htmlId; ?>_shipping_to': 'DOOR'},
    '<?= $_htmlId; ?>_quarter': {'<?= $_htmlId; ?>_shipping_to': 'DOOR'},
    '<?= $_htmlId; ?>_street': {'<?= $_htmlId; ?>_shipping_to': 'DOOR'},
    '<?= $_htmlId; ?>_street_num': {'<?= $_htmlId; ?>_shipping_to': 'DOOR'},
    '<?= $_htmlId; ?>_other': {'<?= $_htmlId; ?>_shipping_to': 'DOOR'},
    '<?= $_htmlId; ?>_sms_no': {'<?= $_htmlId; ?>_sms': '1'},
    '<?= $_htmlId; ?>_partial_delivery_instruction': {'<?= $_htmlId; ?>_partial_delivery': '1'},
    '<?= $_htmlId; ?>_inventory_type': {'<?= $_htmlId; ?>_inventory': '1'},
    '<?= $_htmlId; ?>_inventory_type_loading': {
      '<?= $_htmlId; ?>_inventory': '1',
      '<?= $_htmlId; ?>_inventory_type': 'LOADING'
    },
    '<?= $_htmlId; ?>_inventory_type_digital': {
      '<?= $_htmlId; ?>_inventory': '1',
      '<?= $_htmlId; ?>_inventory_type': 'DIGITAL'
    },
    '<?= $_htmlId; ?>_get_instruction': {'<?= $_htmlId; ?>_instruction': '1'},
    '<?= $_htmlId; ?>_instruction_form': {'<?= $_htmlId; ?>_instruction': '1'},
    '<?= $_htmlId; ?>_instructions': {'<?= $_htmlId; ?>_instruction': '1'},
    '<?= $_htmlId; ?>_pack_count': {'<?= $_htmlId; ?>_shipping_to': ['OFFICE', 'DOOR']}
  });

  function <?= $_htmlId; ?>_generate_loading() {
    validator = new Validation('<?= $_htmlId; ?>_form');
    if (validator && validator.validate()) {
      submitAndReloadArea($('<?= $_htmlId; ?>_form').parentNode, '<?= $this->getSubmitUrl() ?>');
    }
    return false;
  }

  $('<?= $_htmlId; ?>_submit').observe('click', <?= $_htmlId; ?>_generate_loading);

  Validation.addAllThese([
    ['<?= $_htmlId; ?>-required-quarter', '<?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, изберете квартал или улица.')); ?>', function (v) {
      return !Validation.get('IsEmpty').test(v) ||
        !Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_street'));
    }],
    ['<?= $_htmlId; ?>-required-street', '<?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, изберете улица или квартал.')); ?>', function (v) {
      return !Validation.get('IsEmpty').test(v) ||
        !Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_quarter'));
    }],
    ['<?= $_htmlId; ?>-required-street-num', '<?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, попълнете номер.')); ?>', function (v) {
      return Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_street')) ||
        !Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_street')) &&
        !Validation.get('IsEmpty').test(v) ||
        !Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_quarter')) &&
        !Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_other'));
    }],
    ['<?= $_htmlId; ?>-required-other', '<?= $this->jsQuoteEscape(Mage::helper('extensa_econt')->__('Моля, попълнете друго.')); ?>', function (v) {
      return Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_quarter')) ||
        !Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_quarter')) &&
        !Validation.get('IsEmpty').test(v) ||
        !Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_street')) &&
        !Validation.get('IsEmpty').test($F('<?= $_htmlId; ?>_street_num'));
    }]
  ]);
  //]]>
</script>
