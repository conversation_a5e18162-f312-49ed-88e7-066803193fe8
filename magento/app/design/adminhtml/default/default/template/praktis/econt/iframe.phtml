<?php
/** @var $this Praktis_Econt_Block_Adminhtml_Econt_Shipment */
$warehouse = $this->getOrderWarehouse()
?>
<div style="width: 50%; float: left;">
  <div id="error-message-econt-wrapper">
    <div id="error-message-econt-container">

    </div>
  </div>

  <iframe id='econt-shipping-frame'
          src="<?= $this->getEcontIframeUrl(); ?>"
          frameborder="0"
          style="width: 100%; height: 610px;">
  </iframe>
</div>

<div style="width: 40%; float: left; margin-left: 10%">

  <input type="hidden" id="econt_iframe_data" name="econt_iframe_data" value=""/>
  <input id="econt_iframe_form_key" name="form_key" type="hidden"
         value="<?= Mage::getSingleton('core/session')->getFormKey() ?>"/>

  <div>
      <?= $this->__("Shipped from: ") . $warehouse->getName(); ?>
  </div>
  <hr style="margin-bottom: 10px"/>

  <address><?= $this->getOrder()->getShippingAddress()->format('html') ?></address>
  <hr style="margin-bottom: 10px"/>

  <div id="create_shipment_messages"></div>
  <button id="create_shipment" name="create_shipment" style="display: none">
      <?= $this->__('Create Shipment') ?>
  </button>

</div>
<div style="clear: both"></div>

<script type="text/javascript">
    jQuery(document).ready(function ($) {
        function appendError(error) {
            $('#error-message-econt-wrapper').show();
            $('#error-message-econt-container').append($('<li>').append(error));
        }

        function clearErrors() {
            $('#error-message-econt-wrapper').hide();
            $('#error-message-econt-container').empty()
        }

        function showCreateShipmentButton() {
            $("#create_shipment").show();
        }

        function hideCreateShipmentButton() {
            $("#create_shipment").hide();
        }

        $("#create_shipment").click(function () {
            $('#create_shipment_messages').empty();
            $.post(
                '<?= $this->getCreateShipmentUrl() ?>',
                {
                    econt_iframe_data: $('#econt_iframe_data').val(),
                    form_key: $("#econt_iframe_form_key").val()
                },
                function (result) {
                    if (result.hasOwnProperty('message')) {
                        let messageClass = 'warning-msg';
                        if (result.hasOwnProperty('status')) {
                            messageClass = result.status + '-msg';
                        }

                        $('#create_shipment_messages').html(
                            "<div id=\"messages\">" +
                              "<ul class=\"messages\">" +
                                "<li class=\""+messageClass+"\"><ul><li><span>"+result.message+"</span></li></ul></li>" +
                              "</ul>" +
                            "</div>");
                        hideCreateShipmentButton();
                    }
                },
                'json'
            );
        });

        window.addEventListener('message', function (message) {
            // check for origin
            if (message.origin !== 'https://delivery.econt.com') {
                return false;
            }

            var data = message['data'];
            clearErrors();
            if (!data || (data['shipment_error'] && data['shipment_error'] !== '') || (data['name'] === '')) {
                appendError('<?= $this->__('There was an error with calculating the shipping price'); ?>');
                return;
            }

            $("#econt_iframe_data").val(JSON.stringify(data));
            showCreateShipmentButton();
        }, false);
    });
</script>
