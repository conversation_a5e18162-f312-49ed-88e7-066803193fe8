<?php
/**
 * MageWorx
 * Admin Order Editor extension
 *
 * @category   MageWorx
 * @package    MageWorx_OrdersEdit
 * @copyright  Copyright (c) 2016 MageWorx (http://www.mageworx.com/)
 */
?>
<?php
$editAllowed = false;
/** @var MageWorx_OrdersEdit_Helper_Data $helper */
$helper = Mage::helper('mageworx_ordersedit');
?>
<div id="order_history_block">
    <form enctype="multipart/form-data" method="post" action="<?php echo $this->getSubmitUrl(); ?>" id="order_history_form">
        <?php echo $this->getBlockHtml('formkey')?>
        <?php if ($this->canAddComment()):?>
        <div id="history_form" class="order-history-form">
            <div><?php echo Mage::helper('sales')->__('Add Order Comments') ?></div>
            <span class="field-row">
            <label class="normal" for="history_status"><?php echo Mage::helper('sales')->__('Status') ?></label><br/>
            <select name="history[status]" class="select" id="history_status">
            <?php foreach ($this->getStatuses() as $_code=>$_label): ?>
                <option value="<?php echo $_code ?>"<?php if($_code==$this->getOrder()->getStatus()): ?> selected="selected"<?php endif; ?>><?php echo $_label ?></option>
            <?php endforeach; ?>
            </select>
                <?php if ($helper->isEnabled()): ?><input type="file" value="" name="send_file" id="send_file" style="float:right" /><?php endif; ?>
        </span>
            <span class="field-row">
            <label class="normal" for="history_comment"><?php echo Mage::helper('sales')->__('Comment') ?></label>
            <textarea name="history[comment]" rows="3" cols="5" style="height:6em; width:99%;" id="history_comment"></textarea>
        </span>
            <div class="f-left">
                <?php if ($this->canSendCommentEmail()): ?>
                    <input name="history[is_customer_notified]" type="checkbox" id="history_notify" value="1" /><label class="normal" for="history_notify"> <?php echo Mage::helper('sales')->__('Notify Customer by Email') ?></label><br />
                <?php endif; ?>
                <input name="history[is_visible_on_front]" type="checkbox" id="history_visible" value="1" /><label class="normal" for="history_visible"> <?php echo Mage::helper('sales')->__('Visible on Frontend') ?></label>
            </div>
            <div class="f-right">
                <?php echo $this->getChildHtml('submit_button') ?>
            </div>
            <div class="clear"></div>
        </div>
    </form>
    <div class="divider"></div>
    <?php endif;?>
    <ul class="note-list">
        <?php foreach ($this->getOrder()->getStatusHistoryCollection(true) as $_item): ?>
            <li>
                <strong><?php echo Mage::helper('core')->formatDate($_item->getCreatedAtDate(), 'medium') ?></strong>
                <?php echo Mage::helper('core')->formatTime($_item->getCreatedAtDate(), 'medium') ?><span class="separator">|</span><strong><?php echo $_item->getStatusLabel() ?></strong>
                <?php if ($editAllowed):?>
                    <a href="" onclick="deleteHistory('<?php echo  $this->getUrl('adminhtml/mageworx_ordersedit_history/deleteHistory', array('id'=>$_item->getEntityId())) ?>'); return false;" title="<?php echo $helper->__('Delete') ?>" class="ordersedit-delete"><?php echo $helper->__('Delete') ?></a>
                    <a href="" onclick="orderEdit.editComment('<?php echo $_item->getEntityId() ?>'); return false;" title="<?php echo $helper->__('Edit') ?>" class="ordersedit-edit"><?php echo $helper->__('Edit') ?></a>
                <?php endif;?>
                <br/>
                <small><?php echo $this->helper('sales')->__('Customer') ?>
                    <strong class="subdue">
                        <?php if ($this->isCustomerNotificationNotApplicable($_item)): ?>
                            <?php echo $this->helper('sales')->__('Notification Not Applicable') ?>
                        <?php elseif ($_item->getIsCustomerNotified()): ?>
                            <?php echo $this->helper('sales')->__('Notified') ?>
                            <img src="<?php echo $this->getSkinUrl('images/ico_success.gif') ?>" width="16" height="16" alt="" />
                        <?php else: ?>
                            <?php echo $this->helper('sales')->__('Not Notified') ?>
                        <?php endif; ?>
                    </strong></small>
                <?php if ($_item->getCreatorAdminUserId()): ?>
                    <small><?php echo $this->helper('sales')->__('Created by') ?>
                        <strong class="subdue">
                            <?php echo $helper->getAdminUserName($_item); ?>
                        </strong>
                    </small>
                <?php endif; ?>
                <?php if ($_item->getComment()): ?>
                    <br/>
                    <p id="order_comment_<?php echo $_item->getEntityId() ?>">
                        <?php
                        // TODO: refactor this
                        if ($helper->foeModuleCheck('MageWorx_OrdersSurcharge') && mb_stripos($_item->getComment(), 'The email with the payment link was sent to the customer. ID #') !== false) {
                            $comment = nl2br($this->cleanString($_item->getComment()));
                            $matches = array();
                            preg_match('/([ID #]+)([\d]+)/ui', $comment, $matches);
                            $surchargePossibleId = isset($matches[2]) ? $matches[2] : null;
                            $surcharge = Mage::getModel('mageworx_orderssurcharge/surcharge')->load($surchargePossibleId);
                            $surchargeId = $surcharge->getId();
                            if ($surchargeId && $surcharge->canPay()) {
                                $link = ' <div class="mw-link-delete_surcharge" onclick="deleteSurcharge(\'' . $surchargeId . '\'); return false;">' . $helper->__('Cancel') . '</div>';
                                $comment .= $link;
                            }
                            echo $comment;
                        } else {
                            echo nl2br($this->cleanString($_item->getComment()));
                        }
                        ?>
                    </p>
                    <?php if ($editAllowed) {?>
                        <form action="<?php echo $this->getSubmitEditUrl() ?>" id="edit_comment_<?php echo $_item->getEntityId() ?>" style="display:none;">
                            <input type="hidden" name="comment_id" value="<?php echo $_item->getEntityId() ?>" />
                            <textarea rows="5" cols="100" name="comment"><?php echo $this->cleanString($_item->getComment()); ?></textarea>
                            <div class="ordersedit-buttons">
                                <button class="f-left" type="button" title="<?php echo $this->__('Cancel') ?>" onclick="orderEdit.cancelCommentEdit('<?php echo $_item->getEntityId() ?>')">
                                    <span><span><?php echo $this->__('Cancel') ?></span></span>
                                </button>
                                <button class="f-right" type="button" title="<?php echo $this->__('Submit') ?>" onclick="orderEdit.saveCommentEdit(<?php echo $_item->getEntityId() ?>)">
                                    <span><span><?php echo $this->__('Submit') ?></span></span>
                                </button>
                            </div>
                        </form>
                    <?php }?>
                <?php endif; ?>
                <?php if ($fileId=$_item->getFileId()): ?>
                    <br/>
                    <a href="<?php echo $helper->getAdminUploadFilesUrl($fileId, $_item->getFileName()) ?>" class="subdue ordersedit-file"><?php echo $_item->getFileName() ?></a> (<?php echo $helper->__('Size:') ?> <?php echo $helper->prepareFileSize($_item->getFileSize()) ?>)
                <?php endif; ?>
            </li>
        <?php endforeach; ?>
    </ul>
    <script type="text/javascript">
        if($('order_status'))$('order_status').update('<?php echo $this->getOrder()->getStatusLabel() ?>');

        function submitHistoryAndReload(area, url) {
            if ($('send_file').value) {
                $('order_history_form').submit();
            } else {
                submitAndReloadArea(area, url);
            }
        }

        function deleteHistory(itemUrl) {
            if (confirm("<?php echo $helper->__('Are you sure want to delete the comment?') ?>")) {
                url = itemUrl + '?order_id=<?php echo $this->getOrder()->getId() ?>&isAjax=true';
                new Ajax.Request(url, {
                    onSuccess: function(transport) {
                        try {
                            coneol.log(transport.responseText);
                            if (transport.responseText.isJSON()) {
                                var response = transport.responseText.evalJSON();
                                if (response.error) {
                                    alert(response.message);
                                }
                                if(response.ajaxExpired && response.ajaxRedirect) {
                                    setLocation(response.ajaxRedirect);
                                }
                            } else {
                                $('order_history_block').update(transport.responseText);
                            }
                        }
                        catch (e) {
                            $('order_history_block').update(transport.responseText);
                        }
                    }
                });
            }
        }

        function showEditForm(commentId)
        {

        }

        function deleteSurcharge(id) {
            if (confirm('Are you sure to cancel this payment link? Customer won’t be able to use it for a payment in this case.')) {
                var url = '<?php echo Mage::getModel('adminhtml/url')->getUrl('adminhtml/mageworx_orderssurcharge_surcharge/remove'); ?>';
                new Ajax.Request(url, {
                    parameters: {"surcharge_id":id,"is_ajax":"true", "order_id":"<?php echo $this->getOrder()->getId();?>"},
                    onSuccess: function(transport) {
                        try {
                            if (transport.responseText.isJSON()) {
                                var response = transport.responseText.evalJSON();
                                console.log(response);
                                if (response.error) {
                                    alert(response.message);
                                }
                                if(response.ajaxExpired && response.ajaxRedirect) {
                                    setLocation(response.ajaxRedirect);
                                }
                            } else {
                                console.log('Not JSON');
                                $('order_history_block').update(transport.responseText);
                            }
                        }
                        catch (e) {
                            console.log(e);
                            $('order_history_block').update(transport.responseText);
                        }
                    }
                });
            }
        }
    </script>
</div>