<?php
$categories = Mage::helper('pfg_catalogimages')->getCategoriesArray();
$selectedCategories = $this->getSelectedCategories();
?>
<label for="category_select"><?= $this->__('Categories Selector') ?></label>
<select id='category_select' name="category_select[]" multiple='multiple' class="searchable">
    <?php foreach ($categories as $category): ?>
        <option value="<?= $category['value']; ?>" <?= in_array($category['value'], $selectedCategories) ? 'selected' : '' ?>><?= $category['label'] ?></option>
    <?php endforeach; ?>
</select>

<script>
  jQuery('.searchable').multiSelect({
    selectableHeader: "<input type='text' class='search-input' autocomplete='off' placeholder='<?= $this->__('Search name to add') ?>'>",
    selectionHeader: "<input type='text' class='search-input' autocomplete='off' placeholder='<?= $this->__('Search name to remove') ?>'>",
    afterInit: function(ms){
      var that = this,
        $selectableSearch = that.$selectableUl.prev(),
        $selectionSearch = that.$selectionUl.prev(),
        selectableSearchString = '#'+that.$container.attr('id')+' .ms-elem-selectable:not(.ms-selected)',
        selectionSearchString = '#'+that.$container.attr('id')+' .ms-elem-selection.ms-selected';

      that.qs1 = $selectableSearch.quicksearch(selectableSearchString)
        .on('keydown', function(e){
          if (e.which === 40){
            that.$selectableUl.focus();
            return false;
          }
        });

      that.qs2 = $selectionSearch.quicksearch(selectionSearchString)
        .on('keydown', function(e){
          if (e.which == 40){
            that.$selectionUl.focus();
            return false;
          }
        });
    },
    afterSelect: function(value){
      this.qs1.cache();
      this.qs2.cache();
    },
    afterDeselect: function(value){
      this.qs1.cache();
      this.qs2.cache();

    }
  });
</script>