<?php
/** @var Praktis_Theme_Block_Adminhtml_Offerings_Edit_Form_Element_Layout_Renderer $this */
?>
<?php if($this->isNewOfferingsGrid()): ?>
    <h5><?php echo $this->__('Save first before you can edit the layout'); ?></h5>
<?php else: ?>
<?php echo $this->getAppComponentsHtml() ; ?>
<style>
    .center-block {
        display:block; 
        margin:auto; 
        margin-top: 5px;
    }
    .text-center {
        text-align: center;
    }
    .order-table table {
        width: 100%;
        border: none;
    }
    .order-table table td {
        border: 1px solid black;
    }
    .order-table .td3 {
        width: 33%;
        text-align: center;
    }
    .moved {
        background: #97d3f2;
    }
    .to-move {
        background: #00aa00 !important;
    }
</style>
<div class="layout-setting grid" id="layout-config-container">
    <input type="hidden" name="layout" v-model="layoutConfigurationValue">

    <table cellspacing="0" class="data border" style="width: 50%">
        <colgroup>
            <col>
            <col>
            <col>
            <col width="1">
            <col width="1">
        </colgroup>
        <thead>
        <tr class="headings">
            <th><?php echo $this->__('Slot 1'); ?></th>
            <th><?php echo $this->__('Slot 2'); ?></th>
            <th><?php echo $this->__('Slot 3'); ?></th>
            <th></th>
        </tr>
        </thead>
        <tbody>
        <tr v-for="(layoutRow, rowIndex) in layoutConfig">
            <td>
                <offer-block
                        v-if="layoutRow.length > 0"
                        :block-id="layoutRow[0]">
                </offer-block>
                <add-block
                        v-else-if="layoutRow.length == 0"
                        :row="rowIndex"
                ></add-block>
                <span v-else
                    class="center-block text-center">
                    <?php echo $this->__('empty'); ?>
                </span>
            </td>
            <td>
                <offer-block
                        v-if="layoutRow.length > 1"
                        :block-id="layoutRow[1]">
                </offer-block>
                <add-block
                        v-else-if="layoutRow.length == 1"
                        :row="rowIndex"
                ></add-block>
                <span v-else
                      class="center-block text-center">
                    <?php echo $this->__('empty'); ?>
                </span>
            </td>
            <td>
                <offer-block
                        v-if="layoutRow.length > 2"
                        :block-id="layoutRow[2]">
                </offer-block>
                <add-block
                        v-else-if="layoutRow.length == 2"
                        :row="rowIndex"
                ></add-block>
                <span v-else
                      class="center-block text-center">
                    <?php echo $this->__('empty'); ?>
                </span>
            </td>
            <td>
                <button
                    title="Delete Tab" type="button"
                    class="scalable delete delete-product-option"
                    @click="removeRow(rowIndex)"
                >
                    <span><span><span><?php echo $this->__('Delete row'); ?></span></span></span>
                </button>
            </td>
        </tr>
        </tbody>
        <tfoot>
        <tr>
            <td colspan="4" class="a-right">
                <button v-if="canReorder"
                    type="button" class="scalable add"
                    @click="initReorder">
                    <span><span><span><?php echo $this->__('Reorder'); ?></span></span></span>
                </button>
                &nbsp;|&nbsp;
                <button v-if="canAddRow"
                    type="button" class="scalable add"
                    @click="addRow">
                    <span><span><span><?php echo $this->__('Add row'); ?></span></span></span>
                </button>
                <span v-else>
                    <?php echo $this->__('Add at least 2 blocks per row'); ?>
                </span>
            </td>
        </tr>
        </tfoot>
    </table>

    <reorder-popup
            v-if="showReorderPopup"
            :title="'Reorder blocks'"
            :layout="orderLayout"
            v-on:close-popup="closeReorder"
    >
    </reorder-popup>
</div>
<script type="text/javascript">
    var layoutConfigObject = new Vue({
        el: '#layout-config-container',
        data: {
            showReorderPopup: false,
            orderLayout: null,
            layoutConfig: <?php echo $this->getElement()->getValue(); ?>
        },
        computed: {
            layoutConfigurationValue: function () {
                return JSON.stringify(this.layoutConfig)
            },
            canAddRow: function () {
                var r=0,
                    canAddRow = true;

                for(r;r<this.layoutConfig.length;r++) {
                    if (this.layoutConfig[r].length < 2) {
                        canAddRow = false;
                        break;
                    }
                }
                return canAddRow;
            },
            canReorder: function () {
                var r=0,
                    canReorder = false;

                for(r;r<this.layoutConfig.length;r++) {
                    if (this.layoutConfig[r].length > 1) {
                        canReorder = true;
                        break;
                    }
                }
                return canReorder;
            }
        },
        methods: {
            addRow: function () {
                setLocation('<?php echo $this->getUrl('*/*/addRow', ['offering_id' => $this->getOffering()->getId()]) ?>')
            },
            initReorder: function () {
                this.orderLayout = JSON.parse(JSON.stringify(this.layoutConfig));
                this.showReorderPopup = true;
            },
            closeReorder: function () {
                this.orderLayout = null;
                this.showReorderPopup = false;
            },
            removeRow: function (index) {
                var url = '<?php echo $this->getUrl('*/*/removeRow', [
                    'offering_id' => $this->getOffering()->getId(),
                    'row' => '{row}']) ?>'.replace('{row}', index);
                deleteConfirm(
                    '<?php echo $this->__('Are you sure you want to remove the row?'); ?>',
                    url
                );
            }
        }
    });
</script>
<?php endif; ?>