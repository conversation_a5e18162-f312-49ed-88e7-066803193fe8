<script>
    jQuery(function() {
        var $select = jQuery('.select[name="general[category_filters_order][]"]');
        $select.css({width: '100%'});
        $select.select2({
            tags: true
        });

        $select.on('select2:select', function(e){
            var id = e.params.data.id;
            var option = jQuery(e.target).children('[value='+id+']');
            console.log(option);
            option.detach();
            jQuery(e.target).append(option).change();
            console.info(jQuery('form').serialize());
        });
    });
</script>

<style>
    .value .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
        clear: both;
    }
</style>