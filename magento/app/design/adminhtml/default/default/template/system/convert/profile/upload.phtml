<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<h4 class="icon-head head-edit-form fieldset-legend">Upload File</h4>
<div id="messages">
    <ul class="messages">
        <li class="notice-msg">
            <ul>
                <li><?php echo $this->__('Your server PHP settings allow you to upload files not more than %s at a time. Please modify post_max_size (currently is %s) and upload_max_filesize (currently is %s) values in php.ini if you want to upload larger files.', $this->getDataMaxSize(), $this->getPostMaxSize(), $this->getUploadMaxSize())?></li>
            </ul>
        </li>
        <li class="notice-msg">
            <ul>
                <li><?php echo $this->__('Make sure that data encoding in the file is consistent and saved in one of supported encodings (UTF-8 or ANSI).')?></li>
            </ul>
        </li>
    </ul>
</div>
<fieldset>
        <legend>Upload File</legend>
        <span class="field-row">
            <label for="file_1">File 1:</label>
            <input type="file" id="file_1" name="file_1" />
        </span>
        <span class="field-row">
            <label for="file_2">File 2:</label>
            <input type="file" id="file_2" name="file_2" />
        </span>
        <span class="field-row">
            <label for="file_2">File 3:</label>
            <input type="file" id="file_3" name="file_3" />
        </span>
</fieldset>
