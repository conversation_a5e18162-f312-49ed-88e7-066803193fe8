<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<div class="cards-list">

<?php if (!$this->getHideTitle()): ?>
    <div class="bold"><?php echo $this->escapeHtml($this->getMethod()->getTitle()) ?></div>
<?php endif;?>

<?php
    $cards = $this->getCards();
    $showCount = count($cards) > 1;
?>

    <?php foreach ($cards as $key => $card): ?>
        <?php if ($showCount): ?>
            <span><?php echo sprintf($this->__('Credit Card %s'), $key + 1); ?></span>
        <?php endif;?>

        <table class="info-table<?php if ($showCount):?> offset<?php endif;?>">
            <tbody>
            <?php foreach ($card as $_label => $_value):?>
            <tr>
                <td><?php echo $this->escapeHtml($_label)?>:</td>
                <td><?php echo nl2br(implode($this->getValueAsArray($_value, true), "\n"))?></td>
            </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    <?php endforeach; ?>

</div>
