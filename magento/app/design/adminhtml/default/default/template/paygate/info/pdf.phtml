<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
    $cards = $this->getCards();
    $showCount = count($cards) > 1;
?>

<?php echo $this->escapeHtml($this->getMethod()->getTitle()) ?>{{pdf_row_separator}}
<?php foreach ($cards as $key => $card): ?>
    <?php if ($showCount): ?>
       <?php echo sprintf($this->__('Credit Card %s'), $key + 1); ?>
       {{pdf_row_separator}}
    <?php endif;?>

    <?php foreach ($card as $_label => $_value):?>
        <?php echo $_label ?>: <?php echo implode($this->getValueAsArray($_value), ' ')?>{{pdf_row_separator}}
     <?php endforeach; ?>
<?php endforeach; ?>
