<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_formMessage=$this->getPartialAuthorizationFormMessage(); ?>
<?php $_isPartialAuthorization=$this->isPartialAuthorization(); ?>
<?php if ($_isPartialAuthorization || $_formMessage): ?>
    <div class="form-list" id="payment_form_<?php echo $this->getMethodCode() ?>_before" style="display:none;">
        <?php if ($_formMessage): ?>
        <?php echo $this->showNoticeMessage($_formMessage) ?>
        <?php endif;?>

        <?php if ($_isPartialAuthorization): ?>
                <?php echo $this->getChildHtml('cards') ?>
                <div class="release-amounts">
                    <?php echo $this->getCancelButtonHtml()?>
                    <p class="note"><?php echo $this->__('To cancel pending authorizations and release amounts that have already been processed during this payment, click Cancel.') ?></p>
                </div>
                <?php echo $this->showNoticeMessage($this->__('Please enter another credit card number to complete your purchase.')) ?>
                    <script type="text/javascript">
                    //<![CDATA[
                    function cancelPaymentAuthorizations(){
                        new Ajax.Request('<?php echo $this->getAdminCancelUrl() ?>', {
                            onSuccess : function(transport) {
                                try{
                                    response = eval('(' + transport.responseText + ')');
                                } catch (e) {
                                    response = {};
                                }

                                if (response.success) {
                                    order.loadArea(['billing_method','totals'], true, []);
                                } else {
                                    var msg = response.error_message;
                                    if (msg) {
                                        alert(msg);
                                    }
                                }
                            }
                        });
                    }
                    <?php if ($_message = $this->getPartialAuthorizationConfirmationMessage()): ?>
                    if (!confirm('<?php echo Mage::helper('core')->jsQuoteEscape($_message) ?>')) {
                        cancelPaymentAuthorizations();
                    }
                    <?php endif;?>
                    //]]>
                    </script>
        <?php endif;?>
    </div>
<?php endif;?>
<?php echo $this->getChildHtml('method_form_block') ?>
