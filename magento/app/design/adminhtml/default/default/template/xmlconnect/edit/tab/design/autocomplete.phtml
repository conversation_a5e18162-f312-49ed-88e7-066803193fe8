<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<ul>
<?php foreach ($items as $item): ?>
    <li id="<?php echo $item['id']; ?>">
        <?php if ($item['type'] == Mage_XmlConnect_Model_ImageAction::ACTION_TYPE_PRODUCT) : ?>
            <div style="float:right; color:red; font-weight:bold;">
                <img src="<?php echo Mage::getSingleton('catalog/product_media_config')->getMediaUrl($item['image']); ?>" width="45" height="45" alt="" />
            </div>
        <?php endif; ?>
        <input type="hidden" name="<?php echo $item['id']; ?>" value="<?php echo $this->escapeHtml($item['name']) ?>">
        <strong><?php echo $this->escapeHtml($this->__('%s id:', $item['label']) . ' ' . $item['item_id']); ?></strong>
        <p><?php echo $this->escapeHtml($item['name']) ?></p>
        <span class="informal"><?php echo $this->escapeHtml($item['description']) ?></span>
        <span class="informal"><a onclick="event.stopPropagation();" href="<?php echo $item['url'] ?>" target="_blank"/><?php echo $this->__('Edit') ?></a></span>
    </li>
<?php endforeach ?>
</ul>
