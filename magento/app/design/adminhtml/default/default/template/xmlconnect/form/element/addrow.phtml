<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">
//<![CDATA[

var isReturnKeyPressed = false;
var idGlobal = 0;
/**
 * Return innerHTML for TD label
 *
 * @return string
 */
function getTdLabelInnerHtml(id) {
    return '<span class="field-row"><input id="'+id+'" type="text" class="label onclick_text input-text" value="" name="conf[new_pages][labels][]"></span>';
}

/**
 * Return innerHTML for TD Value
 *
 * @return string
 */
function getTdValueInnerHtml() {
    var html = '<span class="field-row"><select class=" select" name="conf[new_pages][ids][]">'
    <?php foreach($this->getOptions() as $page): ?>
        + '<option value="<?php echo $this->jsQuoteEscape($this->htmlEscape($page['value'])); ?>"><?php echo $this->jsQuoteEscape($this->htmlEscape($page['label']));?></option>'
    <?php endforeach; ?>
        + '</select></span>';
    return html;
}

/**
 * Return innerHTML for TD Button
 *
 * @return string
 */
function getTdButtonInnerHtml(id) {
    return '<button  id="'+id+'" class=" scalable save onclick_button" value="&minus;">'
    +'<span><?php echo Mage::helper('core')->jsQuoteEscape($this->__('Delete')); ?></span></button>';
}

/**
 * Delete row from table
 * @param object DomElement
 * @return bool;
 */
function removeTableRow(node) {
    if (!isReturnKeyPressed) {
        node.parentNode.parentNode.parentNode.removeChild(node.parentNode.parentNode);
    }
    return false;
}

/**
 * Insert new row into table
 *
 * @return bool;
 */
function insertNewTableRow(node) {
    var tableNode = node.parentNode.parentNode.parentNode.parentNode;
    var tbodyNode = node.parentNode.parentNode.parentNode;
    /**
     * "-1"  skiping <script> child inside tbody element
     */
    var rowCount = tbodyNode.children.length - 1;
    var newRow = tableNode.insertRow(rowCount);

    var cellLabel = newRow.insertCell(0);
    cellLabel.className = 'label';
    cellLabel.innerHTML = getTdLabelInnerHtml('add_row_text_label'+idGlobal);

    var cellValue = newRow.insertCell(1);
    cellValue.className = 'value';
    cellValue.innerHTML = getTdValueInnerHtml();

    var cellButton = newRow.insertCell(2);
    cellButton.innerHTML = getTdButtonInnerHtml('add_row_button_delete_'+idGlobal);
    cellButton.className = 'label';
    Element.setStyle(cellButton, { width: '5em'});

    observeTextField($('add_row_text_label'+idGlobal));
    observeButtonField($('add_row_button_delete_'+idGlobal));
    idGlobal++;
    return false;
}

document.observe("dom:loaded", function() {
    $$('.onclick_text').each(function(element)
    {
        observeTextField(element);
    });

    $$('.onclick_button').each(function(element)
    {
        observeButtonField(element);
    });
});

/**
 * Adds observer on to text field
 * @param element
 */
function observeTextField(element) {
    if (element) {
        Event.observe(element, 'keypress', function(event) {
            var key = event.which || event.keyCode;
            if (key == Event.KEY_RETURN) {
                isReturnKeyPressed = true;
            }
            return false;
        });
        Event.observe(element, 'blur', function(event) {
            isReturnKeyPressed = false;
        });
    }
}

/**
 * Adds observer for button delete field
 * @param element
 */
function observeButtonField(element) {
    if (element) {
        Event.observe(element, 'click', function(event) {
            removeTableRow(element);
            Event.stop(event);
            return false;
        });
    }
}

// ]]>
</script>
