<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
    $deviceId = $this->getApplicationId();

    /** @var $iphoneHelper Mage_XmlConnect_Helper_Iphone */
    $iphoneHelper = Mage::helper('xmlconnect/iphone');
    $imagesTypes = $iphoneHelper->getImagesTypesList();
?>

<dt class="open" id="dt-images"><a href="#"><?php echo $this->__('Images'); ?></a></dt>

<dd class="open" id="dd-images">
    <div class="entry-edit">
    <?php if ($this->getApplicationId()) :?>
        <div id="field_logo" class="app-images">
            <div class="image-form-list">
                <?php foreach($imagesTypes as $imageTypeCode => $imageConfiguredData): ?>
                    <div id="<?php echo $imageTypeCode ?>_label" class="item-label">
                        <?php echo $imageConfiguredData['label'] ?>
                    </div>
                    <div class="recommended-size"><?php echo $imageConfiguredData['sizeTip'] ?></div>
                    <ul id="<?php echo $imageTypeCode ?>"></ul>
                <?php endforeach; ?>
            </div>
        </div>
        <script type="text/javascript">
            //<![CDATA[
            <?php foreach($imagesTypes as $imageTypeCode => $imageConfiguredData): ?>
                prepareImageItems('<?php echo $imageTypeCode; ?>', '<?php echo $deviceId; ?>');
                <?php $imageData = $this->getImagesData($imageTypeCode, $imageConfiguredData['count']); ?>
                <?php foreach ($imageData as $item): ?>
                    imageItems.add(<?php echo $this->getConfigJson($item); ?>);
                <?php endforeach;?>
                <?php if ($imageConfiguredData['sortable']): ?>
                    imageItems.addDragDrop('<?php echo $imageTypeCode; ?>');
                <?php endif; ?>
            <?php endforeach; ?>

            Validation.addAllThese([
                ['validate-banner-file-0', 'Please specify File.', function(v, element) {
                    if (element.value == '' || element.value == '[]') {
                        return false;
                    }
                    return true;
                }]
            ]);
            //]]>
        </script>
    <?php else :?>
        <div class="save-application"><?php echo $this->__('Please save application first.'); ?></div>
    <?php endif; ?>
    </div>
</dd>
