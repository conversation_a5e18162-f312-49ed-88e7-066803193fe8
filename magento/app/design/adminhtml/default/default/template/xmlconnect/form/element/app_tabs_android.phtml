<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">
// <![CDATA[

// tabNumber:
//  * 0, 1, 2  main tabs
//  * 3, ...  active tabs

// disabledTabNumber — another numbers

enabledTabs = <?php echo Mage::helper('core')->jsonEncode($this->getTabs()->getEnabledTabs()); ?>;
disabledTabs = <?php echo Mage::helper('core')->jsonEncode($this->getTabs()->getDisabledTabs()); ?>;
firstTabItems = [];
secondTabItems = [];
for(i=0; i<enabledTabs.length; i++) {
    if(enabledTabs[i].menu == <?php echo Mage_XmlConnect_Helper_Android::TAGS_ID_FOR_TITLE_BAR ?>) {
        firstTabItems.push(enabledTabs[i]);
    } else {
        secondTabItems.push(enabledTabs[i]);
    }
}

disabledTabsMaxCount = 5;

function XmlconnectTabUpdate() {

    for(i=0; i<3; i++) {
        tags = document.getElementsByClassName('mm1_tab'+i);

        for(j=0; j<tags.length; j++) {
            isActive = i<firstTabItems.length
            if(isActive) {
                // Enabled tabs
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl(); ?>'+firstTabItems[i].image+'" alt="" />'
                    tags[j].style.visibility = 'visible'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = '<span>'+firstTabItems[i].label+'</span>'
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'visible'
                }
            } else {
                // Empty places
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl('tab_empty.png'); ?>" alt="" />'
                    tags[j].style.visibility = 'hidden'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = ''
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'hidden';
                }
            }
        }
    }

   for(i=0; i<3; i++) {
        tags = document.getElementsByClassName('mm2_tab'+i);

        for(j=0; j<tags.length; j++) {
            isActive = i<secondTabItems.length
            isMore = false
            if(isActive) {
                if(secondTabItems[i].menu != 2) {
                    continue;
                }
                // Enabled tabs
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl(); ?>'+secondTabItems[i].image+'" alt="" />'
                    tags[j].style.visibility = 'visible'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = '<span>'+secondTabItems[i].label+'</span>'
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'visible'
                }
            } else {
                // Empty places
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl('tab_empty.png'); ?>" alt="" />'
                    tags[j].style.visibility = 'hidden'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = ''
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'hidden';
                }
            }
        }
    }
    for(i=0; i<disabledTabsMaxCount; i++) {
        tags=document.getElementsByClassName('mmd_tab'+i)
        for(j=0; j<tags.length; j++) {
            if(i<disabledTabs.length && disabledTabs[i]) {
                // Disabled tabs
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl(); ?>'+disabledTabs[i].image+'" alt="" />'
                    tags[j].style.visibility = 'visible'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = disabledTabs[i].label
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'visible'
                }
            } else {
                // Empty places
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl('tab_empty.png'); ?>" alt="" />'
                    tags[j].style.visibility = 'hidden'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = ''
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'hidden'
                }
            }
        }
    }
    XmlconnectUpdateEnabled();
    document.getElementById('<?php echo htmlspecialchars($this->getName()); ?>').value = Object.toJSON({"enabledTabs": enabledTabs, "disabledTabs": disabledTabs})
}

document.observe("dom:loaded", function() {
    XmlconnectTabUpdate();
});

function XmlconnectUpdateEnabled() {
    enabledTabs = [];
    for(i=0; i<firstTabItems.length; i++) {
        enabledTabs.push(firstTabItems[i]);
    }
    for(i=0; i<secondTabItems.length; i++) {
        enabledTabs.push(secondTabItems[i]);
    }
}
function XmlconnectTabMove(tab1, tab2) {
    if(secondTabItems.length<2) {
        return;
    }

    directionRight = tab1 < tab2;

    if(directionRight) {
        if(secondTabItems.length==2) {
            tmp = secondTabItems[0]
            secondTabItems[0] = secondTabItems[1]
            secondTabItems[1] = tmp
        } else {
            tmp = secondTabItems[tab1]
            secondTabItems[tab1] = secondTabItems[tab2]
            secondTabItems[tab2] = tmp
        }
    } else {
        if(secondTabItems.length==2) {
            tmp = secondTabItems[1]
            secondTabItems[1] = secondTabItems[0]
            secondTabItems[0] = tmp
        } else {
            tmp = secondTabItems[tab2]
            secondTabItems[tab2] = secondTabItems[tab1]
            secondTabItems[tab1] = tmp
        }
    }
    XmlconnectTabUpdate()
}

function XmlconnectTabEnable(disabledTabNumber) {
    if(disabledTabs[disabledTabNumber].menu == <?php echo Mage_XmlConnect_Helper_Android::TAGS_ID_FOR_TITLE_BAR ?>) {
            if(firstTabItems.length == 2 && disabledTabs[disabledTabNumber].action == 'Search') {
                temp = firstTabItems[1];
                firstTabItems[1] = disabledTabs.splice(disabledTabNumber, 1).shift();
                firstTabItems[2] = temp;
            } else {
                firstTabItems.push(disabledTabs.splice(disabledTabNumber, 1).shift());
            }

    } else {
        secondTabItems.push(disabledTabs.splice(disabledTabNumber, 1).shift());
    }
    XmlconnectUpdateEnabled();
    XmlconnectTabUpdate();
    return false;
}

function XmlconnectTabDisable(tabNumber, panelNamber) {
    tabActionText = '';
    if(panelNamber == 1) {
        itemsList = firstTabItems;
    } else {
        itemsList = secondTabItems;
    }
    if (itemsList[tabNumber]) {
        tabActionText = itemsList[tabNumber].action;
    }
    if(disabledTabs.length > disabledTabsMaxCount) {
        return false;
    }

    /**
     * Correct tabNumber value after possible change enabledTab array by using fetchMore() function.
     */
    for (i = 0; i < itemsList.length; i++) {
        if (itemsList[i].action == tabActionText) {
            tabNumber = i;
            break;
        }
    }
    disabledTabs.push(itemsList.splice(tabNumber, 1).shift());

    XmlconnectUpdateEnabled();
    XmlconnectTabUpdate();
    return false;
}
// ]]>
</script>

<tr><td>

<input type="hidden"
    name="<?php echo htmlspecialchars($this->getName()); ?>"
    id="<?php echo htmlspecialchars($this->getName()); ?>"
    value="<?php echo htmlspecialchars($this->getValue()); ?>" />

<div class="mm-tabs-title"><?php echo $this->__('Title bar') ?></div>
<table border="0">
    <tr>
        <td width="20%" align="center" valign="bottom" class="mm1_tab0 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm1_tab1 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm1_tab2 mm_img"> </td>
    </tr>
    <tr>
        <td align="center" class="mm1_tab0 mm_name"> </td>
        <td align="center" class="mm1_tab1 mm_name"> </td>
        <td align="center" class="mm1_tab2 mm_name"> </td>
    </tr>
    <tr>
        <td><!-- Home tab == fixed place --></td>
        <td><!-- Search tab == fixed place --></td>
        <td><!-- Account tab == fixed place --></td>
    </tr>
    <tr>
        <td><!-- Home tab == always active --></td>
        <td align="center" class="mm1_tab1 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(1,1)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm1_tab2 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(2,1)"><?php echo $this->__('Make Inactive'); ?></a></td>
    </tr>
</table>
<div class="mm-tabs-title"><?php echo $this->__('Options menu items') ?></div>
<table border="0">
    <tr>
        <td width="20%" align="center" valign="bottom" class="mm2_tab0 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm2_tab1 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm2_tab2 mm_img"> </td>
    </tr>
    <tr>
        <td align="center" class="mm2_tab0 mm_name"> </td>
        <td align="center" class="mm2_tab1 mm_name"> </td>
        <td align="center" class="mm2_tab2 mm_name"> </td>
    </tr>
    <tr>
        <td align="center" valign="top" class="mm2_tab0 mm_ctrl">
            <input type="button" class="m-arrows r-arrow full-arrow" value="&rarr;" onclick="XmlconnectTabMove(0,1)" style="margin-left:50px;" />
        </td>
        <td align="center" valign="top" class="mm2_tab1 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(1,0)" style="margin-left:20px;" />
            <input type="button" class="m-arrows r-arrow" value="&rarr;" onclick="XmlconnectTabMove(1,2)" />
        </td>
        <td align="center" valign="top" class="mm2_tab2 mm_ctrl">
            <input type="button" class="m-arrows l-arrow full-arrow-left" value="&larr;" onclick="XmlconnectTabMove(2,1)" style="margin-left:50px;" />
        </td>
    </tr>
    <tr>
        <td align="center" class="mm2_tab0 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(0,2)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm2_tab1 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(1,2)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm2_tab2 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(2,2)"><?php echo $this->__('Make Inactive'); ?></a></td>
    </tr>
</table>
<div class="mm-tabs-title"><?php $this->__('Inactive Tabs'); ?></div>
<table border="0">
    <tr>
        <td width="20%" align="center" valign="bottom" class="mmd_tab0 mm_img" style="opacity: .5"> </td>
        <td width="20%" align="center" valign="bottom" class="mmd_tab1 mm_img" style="opacity: .5"> </td>
        <td width="20%" align="center" valign="bottom" class="mmd_tab2 mm_img" style="opacity: .5"> </td>
        <td width="20%" align="center" valign="bottom" class="mmd_tab3 mm_img" style="opacity: .5"> </td>
        <td width="20%" align="center" valign="bottom" class="mmd_tab4 mm_img" style="opacity: .5"> </td>
    </tr>
    <tr>
        <td align="center" class="mmd_tab0 mm_name"> </td>
        <td align="center" class="mmd_tab1 mm_name"> </td>
        <td align="center" class="mmd_tab2 mm_name"> </td>
        <td align="center" class="mmd_tab3 mm_name"> </td>
        <td align="center" class="mmd_tab4 mm_name"> </td>
    </tr>
    <tr>
        <td align="center" class="mmd_tab0 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(0)"><?php echo $this->__('Activate'); ?></a></td>
        <td align="center" class="mmd_tab1 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(1)"><?php echo $this->__('Activate'); ?></a></td>
        <td align="center" class="mmd_tab2 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(2)"><?php echo $this->__('Activate'); ?></a></td>
        <td align="center" class="mmd_tab3 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(3)"><?php echo $this->__('Activate'); ?></a></td>
        <td align="center" class="mmd_tab4 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(4)"><?php echo $this->__('Activate'); ?></a></td>
    </tr>
</table>

</td></tr>
