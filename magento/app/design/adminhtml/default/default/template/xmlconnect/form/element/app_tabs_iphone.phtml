<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<script type="text/javascript">
// <![CDATA[

// tabNumber:
//  * 0, 1, 2  main tabs
//  * 3, ...  active tabs

// disabledTabNumber — another numbers

enabledTabs = <?php echo Mage::helper('core')->jsonEncode($this->getTabs()->getEnabledTabs()); ?>

disabledTabs = <?php echo Mage::helper('core')->jsonEncode($this->getTabs()->getDisabledTabs()); ?>

disabledTabsMaxCount = 5;
visibleTabsCount = 5;
moreActionText = 'More';

function XmlconnectTabUpdate() {
    for(i=0; i<10; i++) {
        tags=document.getElementsByClassName('mm_tab'+i)
        for(j=0; j<tags.length; j++) {
            isActive = i<enabledTabs.length
            isMore = false
            if(enabledTabs[i] && (enabledTabs[i].action==moreActionText)) {
                isMore = true
            }
            if(isActive) {
                // Enabled tabs
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl(); ?>'+enabledTabs[i].image+'" alt="" />'
                    tags[j].style.visibility = 'visible'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = '<span>'+enabledTabs[i].label+'</span>'
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = isMore? 'hidden': 'visible'
                }
            } else {
                // Empty places
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl('tab_empty.png'); ?>" alt="" />'
                    tags[j].style.visibility = 'hidden'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = ''
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'hidden';
                }
            }
        }
    }
    for(i=0; i<disabledTabsMaxCount; i++) {
        tags=document.getElementsByClassName('mmd_tab'+i)
        for(j=0; j<tags.length; j++) {
            if(i<disabledTabs.length && disabledTabs[i] && (disabledTabs[i].action != moreActionText)) {
                // Disabled tabs
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl(); ?>'+disabledTabs[i].image+'" alt="" />'
                    tags[j].style.visibility = 'visible'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = disabledTabs[i].label
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'visible'
                }
            } else {
                // Empty places
                if(tags[j].className.indexOf('mm_img')>0) {
                    tags[j].innerHTML = '<img src="<?php echo Mage::helper('xmlconnect/image')->getSkinImagesUrl('tab_empty.png'); ?>" alt="" />'
                    tags[j].style.visibility = 'hidden'
                }
                if(tags[j].className.indexOf('mm_name')>0) {
                    tags[j].innerHTML = ''
                }
                if(tags[j].className.indexOf('mm_ctrl')>0) {
                    tags[j].style.visibility = 'hidden'
                }
            }
        }
    }
    document.getElementById('<?php echo htmlspecialchars($this->getName()); ?>').value = Object.toJSON({"enabledTabs": enabledTabs, "disabledTabs": disabledTabs})
}

document.observe("dom:loaded", function() {
    XmlconnectTabUpdate();
});

function XmlconnectTabMove(tab1, tab2) {
    directionLeft = tab1 > tab2;
    if (enabledTabs[tab1] && (enabledTabs[tab1].action == moreActionText)) {
        return;
    }
    if (enabledTabs[tab2] && (enabledTabs[tab2].action == moreActionText)) {
        tab2 = directionLeft ? tab2 - 1 : tab2 + 1;
    }
    if(tab1>=enabledTabs.length) {
        return;
    }
    if(tab2>=enabledTabs.length) {
        return;
    }

    tmp = enabledTabs[tab1]
    enabledTabs[tab1] = enabledTabs[tab2]
    enabledTabs[tab2] = tmp
    XmlconnectTabUpdate()
}

function XmlconnectTabEnable(disabledTabNumber) {
    enabledTabs.push(disabledTabs.splice(disabledTabNumber, 1).shift());
    insertMore(fetchMore());
    XmlconnectTabUpdate();
    return false;
}

function XmlconnectTabDisable(tabNumber) {
    tabActionText = '';
    if (enabledTabs[tabNumber]) {
        tabActionText = enabledTabs[tabNumber].action;
    }
    tabMore = fetchMore();
    if(disabledTabs.length >= disabledTabsMaxCount) {
        disabledTabs.push(tabMore);
        return false;
    }
    /**
     * Correct tabNumber value after possible change enabledTab array by using fetchMore() function.
     */
    for (i = 0; i < enabledTabs.length; i++) {
        if (enabledTabs[i].action == tabActionText) {
            tabNumber = i;
            break;
        }
    }
    disabledTabs.push(enabledTabs.splice(tabNumber, 1).shift());
    insertMore(tabMore);
    XmlconnectTabUpdate();
    return false;
}


/**
 * Insert "tab more" on last visible position
 * if enabled more than visible tabs
 *
 * @param Object tabMore
 * @return bool
 */
function insertMore(tabMore) {
    if (enabledTabs.length > visibleTabsCount) {
        enabledTabsMoreArray = enabledTabs.splice(visibleTabsCount-1, enabledTabs.length - (visibleTabsCount - 1));

        enabledTabs.push(tabMore);
        while (enabledTabsMoreArray.length) {
            enabledTabs.push(enabledTabsMoreArray.shift());
        }
    } else {
        disabledTabs.push(tabMore);
    }
    return false;
}

/**
 * Fetch "tab more" from enabledTab or disabledTab arrays
 *
 * @return Object|null
 */
function fetchMore() {
    for (i=0; i < enabledTabs.length; i++) {
        if (enabledTabs[i] && (enabledTabs[i].action == moreActionText)) {
            return enabledTabs.splice(i, 1).shift();
        }
    }
    for (i=0; i < disabledTabs.length; i++) {
        if (disabledTabs[i] && (disabledTabs[i].action == moreActionText)) {
            return disabledTabs.splice(i, 1).shift();
        }
    }
    return null;
}
// ]]>
</script>

<tr><td>

<input type="hidden"
    name="<?php echo htmlspecialchars($this->getName()); ?>"
    id="<?php echo htmlspecialchars($this->getName()); ?>"
    value="<?php echo htmlspecialchars($this->getValue()); ?>" />

<div class="mm-tabs-title"><?php echo $this->__('Main Tabs') ?></div>
<table border="0">
    <tr>
        <td width="20%" align="center" valign="bottom" class="mm_tab0 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm_tab1 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm_tab2 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm_tab3 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm_tab4 mm_img"> </td>
    </tr>
    <tr>
        <td align="center" class="mm_tab0 mm_name"><span><?php echo $this->__('Home'); ?></span></td>
        <td align="center" class="mm_tab1 mm_name"> </td>
        <td align="center" class="mm_tab2 mm_name"> </td>
        <td align="center" class="mm_tab3 mm_name"> </td>
        <td align="center" class="mm_tab4 mm_name"> </td>
    </tr>
    <tr>
        <td><!-- Home tab == fixed place --></td>
        <td align="center" valign="top" class="mm_tab1 mm_ctrl">
            <input type="button"  class="m-arrows full-arrow" value="&rarr;" onclick="XmlconnectTabMove(1,2)" />
        </td>
        <td align="center" valign="top" class="mm_tab2 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(2,1)" />
            <input type="button"  class="m-arrows r-arrow" value="&rarr;" onclick="XmlconnectTabMove(2,3)" />
        </td>
        <td align="center" valign="top" class="mm_tab3 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(3,2)" />
            <input type="button"  class="m-arrows r-arrow" value="&rarr;" onclick="XmlconnectTabMove(3,4)" />
        </td>
        <td align="center" valign="top" class="mm_tab4 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(4,3)" />
            <input type="button"  class="m-arrows r-arrow" value="&rarr;" onclick="XmlconnectTabMove(4,5)" />
        </td>
    </tr>
    <tr>
        <td><!-- Home tab == always active --></td>
        <td align="center" class="mm_tab1 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(1)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm_tab2 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(2)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm_tab3 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(3)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm_tab4 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(4)"><?php echo $this->__('Make Inactive'); ?></a></td>
    </tr>
</table>
<div class="mm-tabs-title"><?php echo $this->__('More Tabs') ?></div>
<table border="0">
    <tr>
        <td width="20%" align="center" valign="bottom" class="mm_tab5 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm_tab6 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm_tab7 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm_tab8 mm_img"> </td>
        <td width="20%" align="center" valign="bottom" class="mm_tab9 mm_img"> </td>
    </tr>
    <tr>
        <td align="center" class="mm_tab5 mm_name"> </td>
        <td align="center" class="mm_tab6 mm_name"> </td>
        <td align="center" class="mm_tab7 mm_name"> </td>
        <td align="center" class="mm_tab8 mm_name"> </td>
        <td align="center" class="mm_tab9 mm_name"> </td>
    </tr>
    <tr>
        <td align="center" valign="top" class="mm_tab5 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(5,4)" />
            <input type="button" class="m-arrows r-arrow" value="&rarr;" onclick="XmlconnectTabMove(5,6)" />
        </td>
        <td align="center" valign="top" class="mm_tab6 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(6,5)" />
            <input type="button" class="m-arrows r-arrow" value="&rarr;" onclick="XmlconnectTabMove(6,7)" />
        </td>
        <td align="center" valign="top" class="mm_tab7 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(7,6)" />
            <input type="button" class="m-arrows r-arrow" value="&rarr;" onclick="XmlconnectTabMove(7,8)" />
        </td>
        <td align="center" valign="top" class="mm_tab8 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(8,7)" />
            <input type="button" class="m-arrows r-arrow" value="&rarr;" onclick="XmlconnectTabMove(8,9)" />
        </td>
        <td align="center" valign="top" class="mm_tab9 mm_ctrl">
            <input type="button" class="m-arrows l-arrow" value="&larr;" onclick="XmlconnectTabMove(9,8)" />
            <input type="button" class="m-arrows r-arrow" value="&rarr;" onclick="" />
        </td>
    </tr>
    <tr>
        <td align="center" class="mm_tab5 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(5)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm_tab6 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(6)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm_tab7 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(7)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm_tab8 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(8)"><?php echo $this->__('Make Inactive'); ?></a></td>
        <td align="center" class="mm_tab9 mm_ctrl"><a href="#" onclick="return XmlconnectTabDisable(9)"><?php echo $this->__('Make Inactive'); ?></a></td>
    </tr>
</table>
<div class="mm-tabs-title"><?php $this->__('Inactive Tabs'); ?></div>
<table border="0">
    <tr>
        <td width="20%" align="center" valign="bottom" class="mmd_tab0 mm_img" style="opacity: .5"> </td>
        <td width="20%" align="center" valign="bottom" class="mmd_tab1 mm_img" style="opacity: .5"> </td>
        <td width="20%" align="center" valign="bottom" class="mmd_tab2 mm_img" style="opacity: .5"> </td>
        <td width="20%" align="center" valign="bottom" class="mmd_tab3 mm_img" style="opacity: .5"> </td>
        <td width="20%" align="center" valign="bottom" class="mmd_tab4 mm_img" style="opacity: .5"> </td>
    </tr>
    <tr>
        <td align="center" class="mmd_tab0 mm_name"> </td>
        <td align="center" class="mmd_tab1 mm_name"> </td>
        <td align="center" class="mmd_tab2 mm_name"> </td>
        <td align="center" class="mmd_tab3 mm_name"> </td>
        <td align="center" class="mmd_tab4 mm_name"> </td>
    </tr>
    <tr>
        <td align="center" class="mmd_tab0 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(0)"><?php echo $this->__('Activate'); ?></a></td>
        <td align="center" class="mmd_tab1 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(1)"><?php echo $this->__('Activate'); ?></a></td>
        <td align="center" class="mmd_tab2 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(2)"><?php echo $this->__('Activate'); ?></a></td>
        <td align="center" class="mmd_tab3 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(3)"><?php echo $this->__('Activate'); ?></a></td>
        <td align="center" class="mmd_tab4 mm_ctrl"><a href="#" onclick="return XmlconnectTabEnable(4)"><?php echo $this->__('Activate'); ?></a></td>
    </tr>
</table>

</td></tr>
