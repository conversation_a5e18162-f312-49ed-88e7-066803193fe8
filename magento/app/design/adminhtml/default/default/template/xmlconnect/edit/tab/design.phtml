<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @var $this Mage_XmlConnect_Block_Adminhtml_Mobile_Edit_Tab_Design
 */
?>
<script type="text/javascript">
// <![CDATA[
var imageTemplate = '<input type="hidden" name="{{file_field}}[image][{{id}}][image_id]" value="{{image_id}}" />'+
        '<div class="banner-image">'+
            '<div class="row a-right">' +
                '<div class="flex">' +
                '<?php echo $this->getBrowseButtonHtml() ?>'+
                '</div>' +
                '<div id="{{file_field}}_{{id}}_file" class="uploader a-left">'+
                    '<div id="{{file_field}}_{{id}}_file-old" class="file-row-info"><div id="{{file_field}}_preview_{{id}}" style="background:url({{thumbnail}}) no-repeat center;" class="image-placeholder"></div></div>'+
                    '<div id="{{file_field}}_{{id}}_file-new" class="file-row-info new-file"></div>'+
                    '<div class="clear"></div>'+
                '</div>'+
            '</div>'+
            '<input type="hidden" class="validate-banner-file-{{id}}" id="{{file_field}}_{{id}}_file_save" name="{{file_field}}[image][{{id}}][file]" value="{{file_save}}" />'+
        '</div>';

function prepareImageItems(imageType, deviceId) {
    imageItems.application_id = deviceId;
    imageItems.itemCount = 0;
    imageItems.ulImages = $(imageType);
    imageItems.ulImagesId = imageType;
}

var imageItems = {
    ulImages : null,
    ulImagesId : null,
    application_id : null,
    fileUploadDir : '<?php echo Mage::getModel('xmlconnect/images')->getImageUrl('/') ?>',
    templateSyntax : /(^|.|\r|\n)({{(\w+)}})/,
    templateText : imageTemplate,
    itemCount : 0,
    excludedActionImages : ['<?php echo Mage_XmlConnect_Model_Device_Abstract::IMAGE_TYPE_ICON;?>',
        '<?php echo Mage_XmlConnect_Model_Device_Iphone::IMAGE_TYPE_PORTRAIT_BACKGROUND;?>',
        '<?php echo Mage_XmlConnect_Model_Device_Ipad::IMAGE_TYPE_PORTRAIT_BACKGROUND;?>',
        '<?php echo Mage_XmlConnect_Model_Device_Ipad::IMAGE_TYPE_LANDSCAPE_BACKGROUND;?>'],
    template: null,
    imageActionTruncateLenght: 35,
    add : function(config) {
        try {
            if(Object.isString(config)) {
                config = config.evalJSON();
            }
            config.file_field = config.uploaderConfig.fileParameterName;
            config.file_save = config.miscConfig.file_save;
            config.thumbnail = config.miscConfig.thumbnail;
            config.image_id = config.miscConfig.image_id;
            config.image_action_data = config.miscConfig.image_action_data;
            config.image_count = config.miscConfig.image_count;

            var isUploadedImage = true, uploaderClass = '';
            this.template = new Template(this.templateText, this.templateSyntax);

            config.ulImagesId = this.ulImagesId;

            if (!config.id) {
                config.id = this.itemCount;
                this.itemCount++;
            }
            if (!config.image_id) {
                config.image_id = 'uploader';
            }
            if (config.image_id == 'uploader') {
                isUploadedImage = false;
                uploaderClass = ' image-item-upload';
            } else {
                uploaderClass = ' image-item';
            }

            Element.insert(this.ulImages, {'bottom' : '<li id="pictureId_' + config.image_id  + '" class="' + uploaderClass  + '"></li>'});
            Element.insert(this.ulImages.down('li', config.id), {'bottom' : this.template.evaluate(config)});
            var container = $(config.file_field + '_' + config.id + '_file').up('li');

            if (config.image_id != 'uploader') {
                container.down('.flex').remove();
                imageItems.addEditButton(container, config);
                imageItems.addDeleteButton(container, config);
            } else {
                config.file_save = [];

                new Downloadable.FileUploader(
                    config.file_field,
                    config.file_field + '_' + config.id,
                    container,
                    config.file_field + '[image][' + config.id + ']',
                    config.file_save,
                    config.file_field + '_' + config.id + '_file',
                    config
                );
            }
        } catch (e) {
            alert(e.message);
        }
    },
    addEditButton : function(container, config) {
        var actionAllowed = true;
        for (var i in imageItems.excludedActionImages) {
            if (imageItems.excludedActionImages[i] == config.file_field) {
                actionAllowed = false;
            }
        }

        if (config.image_id !== undefined && actionAllowed) {
            var edit_button_id = 'edit_link_' + config.image_id;
            var edit_button_state_id = 'edit_link_state' + config.image_id;
            var editButton = '';
            if (config.image_action_data !== undefined && config.image_action_data.entity_name !== undefined) {
                editButton += '<strong><?php echo $this->jsQuoteEscape($this->__('Action')); ?>:</strong> <a id="' + edit_button_id + '" href="#" class="edit-btn">' + (config.image_action_data.action_type + ', ' + config.image_action_data.entity_name).truncate(this.imageActionTruncateLenght) + '</a>';
            } else {
                editButton += '<strong><?php echo $this->jsQuoteEscape($this->__('Action')); ?>:</strong> <a id="' + edit_button_id + '" href="#" class="edit-btn"><?php echo $this->jsQuoteEscape($this->__('not set')); ?></a>';
            }
            Element.insert(container, { 'bottom' : editButton});
            imageItems.bindEditButton(edit_button_id, config);
        }
    },
    updateEditButtonState : function(config) {
        var edit_button_state_id = 'edit_link_' + config.image_id;
        if (config.image_action_data !== undefined) {
            $(edit_button_state_id).update((config.image_action_data.action_type + ', ' + config.image_action_data.entity_name).truncate(this.imageActionTruncateLenght))
        } else {
            $(edit_button_state_id).update('<?php echo $this->jsQuoteEscape($this->__('not set')); ?>')
        }
    },
    bindEditButton : function(edit_button_id, config) {
        try {
            $(edit_button_id).config = config;
            Event.observe(edit_button_id, 'click', this.editBanner.bind(this));
        } catch (e) {
            alert(e.message);
        }
    },
    addDeleteButton : function(container, config) {
        var delete_button_id = config.file_field + '_' + config.id + '_delete_button';

        if (config.image_id === undefined) {
            image_id = 0;
        } else {
            image_id = config.image_id;
        }

        var deleteButton = '<a href="#" onclick="imageItems.remove(' + image_id + '); return false;" id="' + delete_button_id + '" class="item-remove"><?php echo $this->jsQuoteEscape($this->__('Remove')); ?></a>';
        Element.insert(container, {'bottom' : deleteButton});
    },
    remove : function(image_id) {
        new Ajax.Request('<?php echo $this->getUrl('*/*/deleteimage')?>' + 'image_id/' + image_id + '/'
            + 'application_id/' +  imageItems.application_id + '/', {
            onSuccess : function(transport) {
                try {
                    if (transport.responseText.isJSON()) {
                        var response = transport.responseText.evalJSON()
                        if (response.error) {
                            alert(response.error);
                        } else {
                            imageItems.reloadImages(response.image_list);
                        }
                        if(response.ajaxExpired && response.ajaxRedirect) {
                            setLocation(response.ajaxRedirect);
                        }
                    } else {
                        alert(transport.responseText);
                    }
                } catch (e) {
                    alert(e.message);
                }
            }
        });
    },
    getImageListByType : function(image_type) {
        new Ajax.Request('<?php echo $this->getUrl('*/*/imagelist')?>' + 'image_type/' + image_type + '/'
            + 'application_id/' +  imageItems.application_id + '/', {
            onSuccess : function(transport) {
                try {
                    if (transport.responseText.isJSON()) {
                        var response = transport.responseText.evalJSON()
                        if (response.error) {
                            alert(response.error);
                        }
                        if(response.ajaxExpired && response.ajaxRedirect) {
                            setLocation(response.ajaxRedirect);
                        }
                        imageItems.reloadImages(response.image_list);
                    } else {
                        alert(transport.responseText);
                    }
                } catch (e) {
                    alert(e.message);
                }
            }
        });
    },
    reloadImages : function(image_list) {
        try {
            image_list = image_list.map(function (item) {
                return Object.isString(item) ? item.evalJSON(): item;
            });
            var imageType = image_list[0].uploaderConfig.fileParameterName;
            Downloadable.unsetUploaderByType(imageType);
            var currentContainerId = imageType;
            var currentContainer = $(currentContainerId);
            var countOfNodes = 0;
            while (currentContainer.childNodes[0]) {
                ++countOfNodes;
                currentContainer.removeChild(currentContainer.childNodes[0]);
            }
            prepareImageItems(imageType, this.application_id);
            for (i=0; i < image_list.length; i++) {
                imageItems.add(image_list[i]);
            }
            imageItems.addDragDrop(imageType);
            updatePreview();
        } catch (e) {
            alert(e.message);
        }
    },
    addDragDrop : function (type) {
        var changeEffect;
        Sortable.create(type, { tag: 'li', only: 'image-item', overlap:'horizontal', constraint:false,
            onChange: function(item) {
                var list = Sortable.options(item).element;
            },

            onUpdate: function(list) {
                var newPositions = Sortable.sequence(list, arguments[1]).map( function(item) {
                    return item;
                });
                var i = 1,
                    data = {};

                newPositions.each( function(position) {
                    if (position != "uploader") {
                        data['data[' + position + ']'] = i;
                        i++;
                    }
                });

                data['type'] = type;
                data['application_id'] = imageItems.application_id;

                new Ajax.Request("<?php echo $this->getUrl('*/*/saveImageOrder')?>", {
                    method: "post",
                    parameters: data,
                    onSuccess: function (transport) {
                        if (transport.responseText.isJSON()) {
                            var response = transport.responseText.evalJSON()
                        }
                        imageItems.reloadImages(response.image_list);
                    }
                });
            }
        });
        if ($(type).select('.image-item-upload')[0]) {
            $(type).select('.image-item-upload')[0].stopObserving();
        }
    },
    editBanner : function (event) {
        try {
            var element = event.element();
            actionHelper.closeDialog();
            actionHelper.setImageElement(element);
            actionHelper.startDialog(element);
            event.preventDefault();
        } catch (e) {
            alert(e.message);
        }
    }
}

jscolor.dir = '<?php echo $this->getJsUrl(); ?>jscolor/';

var uploaderTemplate = '<div class="no-display" id="[[idName]]-template">' +
                            '<div id="{{id}}-container" class="file-row file-row-narrow">' +
                                '<span class="file-info">' +
                                    '<span class="file-info-name">{{name}}</span>' + ' ' +
                                    '<span class="file-info-size">{{size}}</span>' +
                                '</span>' +
                                '<span class="progress-text"></span>' +
                                '<div class="clear"></div>' +
                            '</div>' +
                        '</div>';

var fileListTemplate = '<div style="background:url({{file}}) no-repeat center;" class="image-placeholder"></div>';

var Downloadable = {
    uploaderObj : $H({}),
    setUploaderObj : function(type, key, obj) {
        if (!this.uploaderObj.get(type)) {
            this.uploaderObj.set(type, $H({}));
        }
        this.uploaderObj.get(type).set(key, obj);
    },
    getUploaderObj : function(type, key){
        try {
            return this.uploaderObj.get(type).get(key);
        } catch (e) {
            try {
                console.log(e);
            } catch (e2) {
                alert(e.name + '\n' + e.message);
            }
        }
    },
    unsetUploaderObj : function(type, key) {
        try {
            this.uploaderObj.get(type).unset(key);
        } catch (e) {
            try {
                console.log(e);
            } catch (e2) {
                alert(e.name + '\n' + e.message);
            }
        }
    },
    unsetUploaderByType : function(type) {
        try {
            if (this.uploaderObj.get(type)) {
                this.uploaderObj.unset(type);
                this.uploaderObj.set(type, $H({}));
            }
        } catch (e) {
            try {
                console.log(e);
            } catch (e2) {
                alert(e.name + '\n' + e.message);
            }
        }
    },
    massUploadByType : function(type){
        try {
            this.uploaderObj.get(type).each(function(item){
                item.value.upload();
            });
        } catch (e) {
            try {
                console.log(e);
            } catch (e2) {
                alert(e.name + '\n' + e.message);
            }
        }
    }
};

Downloadable.FileUploader = Class.create();
Downloadable.FileUploader.prototype = {
    type : null,
    key : null, //key, identifier of uploader obj
    elmContainer : null, //insert Flex object and templates to elmContainer
    fileValueName : null, //name of field of JSON data of saved file
    fileValue : null,
    idName : null, //id name of elements for unique uploader
    uploaderText: uploaderTemplate,
    uploaderSyntax : /(^|.|\r|\n)(\[\[(\w+)\]\])/,
    uploaderObj : $H({}),
    config : null,
    initialize: function (type, key, elmContainer, fileValueName, fileValue ,idName, config) {
        this.type = type;
        this.key = key;
        this.elmContainer = elmContainer;
        this.fileValueName = fileValueName;
        this.fileValue = fileValue;
        this.idName = idName;
        this.config = config;
        uploaderTemplate = new Template(this.uploaderText, this.uploaderSyntax);

        Element.insert(
            elmContainer,
            {
                'top' : uploaderTemplate.evaluate({
                    'idName' : this.idName,
                    'fileValueName' : this.fileValueName,
                    'uploaderObj' : 'Downloadable.getUploaderObj(\'' + this.type + '\', \'' + this.key + '\')'
                })
            }
        );
        $(config.file_field + '_preview_' + config.id).update('<?php echo $this->__('Choose file'); ?>');
        if ($(this.idName + '_save')) {
            $(this.idName + '_save').value = this.fileValue.toJSON ? this.fileValue.toJSON() : Object.toJSON(this.fileValue);
        }

        this.config = Object.toJSON(this.config).replace(
            new RegExp(config.elementIds.idToReplace, 'g'),
            config.file_field + '_'+ config.id + '_file').evalJSON();

        Downloadable.setUploaderObj(
            this.type,
            this.key,
            new Uploader(this.config)
        );
        new Downloadable.FileList(this.idName, Downloadable.getUploaderObj(type, key), this.config);
        if (varienGlobalEvents) {
            varienGlobalEvents.attachEventHandler('tabChangeBefore', Downloadable.getUploaderObj(type, key).onContainerHideBefore);
        }
    }
}

Downloadable.FileList = Class.create();
Downloadable.FileList.prototype = {
    file: [],
    containerId: '',
    container: null,
    uploader: null,
    fileListTemplate: fileListTemplate,
    templatePattern : /(^|.|\r|\n)({{(\w+)}})/,
    listTemplate : null,
    initialize: function (containerId, uploader, config) {
        this.containerId  = containerId,
        this.container = $(this.containerId);
        this.uploader = uploader;
        this.uploader.uploader.on('filesSubmitted', this.handleFileSelect.bind(this));
        document.on('uploader:fileSuccess', function(event) {
            var memo = event.memo;
            if(this._checkCurrentContainer(memo.containerId)) {
                this.handleUploadComplete([{response: memo.response}]);
            }
        }.bind(this));
        this.file = this.getElement('save').value.evalJSON();
        this.listTemplate = new Template(this.fileListTemplate, this.templatePattern);
        this.updateFiles();
        this.uploader.config = config;
        this.onContainerHideBefore = this.uploader.onContainerHideBefore.bind(
            this.uploader,
            function () {
                return 'change';
            });
    },
    _checkCurrentContainer: function (child) {
        return $(this.containerId).down('#' + child);
    },
    handleFileSelect: function(event) {
        try {
            if(this.uploader.uploader.files.length) {
                $(this.containerId + '-old').hide();
                this.uploader.elements.browse.invoke('setStyle', {'visibility': 'hidden'});
            }
            this.updateFiles();
            Downloadable.massUploadByType(this.uploader.config.file_field);
        } catch (e) {
            alert(e.message);
        }
    },
    getElement: function (name) {
        return $(this.containerId + '_' + name);
    },
    handleUploadComplete: function (files) {
        try {
            files.each(function(item) {
                if (!item.response.isJSON()) {
                    try {
                        console.log(item.response);
                    } catch (e2) {
                        alert(item.response);
                    }
                    return;
                }
                var response = item.response.evalJSON();
                if (response.error) {
                    imageItems.getImageListByType(this.uploader.config.file_field);
                    return;
                }
                var newFile = {};
                newFile.file = response.thumbnail;
                newFile.name = response.name;
                newFile.size = response.size;
                newFile.status = 'new';
                this.file[0] = newFile;
                imageItems.reloadImages(response.image_list);
            }.bind(this));
            this.updateFiles();
        } catch (e) {
            alert(e.message);
        }
    },
    updateFiles: function() {
        try {
            this.getElement('save').value = this.file.toJSON ? this.file.toJSON() : Object.toJSON(this.file);
            this.file.each(function(row){
                row.size = this.uploader.formatSize(row.size);
                $(this.containerId + '-old').innerHTML = this.listTemplate.evaluate(row);
                $(this.containerId + '-new').hide();
                $(this.containerId + '-old').show();
            }.bind(this));
        } catch (e) {
            alert(e.message);
        }
    }
}
//]]>
</script>

<?php echo $this->getChildHtml('image_action_block');?>

<table border="0" cellspacing="0" cellpadding="0">
    <tr>
        <td style="width:540px">
            <dl id="dl-image-accordion" class="accordion">
                <?php echo $this->getChildHtml('design_images'); ?>
            </dl>
            <?php echo $this->getChildHtml('design_accordion'); ?>
        </td>
        <td style="width:20px">&nbsp;</td>
        <td style="width:350px">
            <?php if ($this->canShowPreview()) : ?>
                <?php echo $this->getChildHtml('app_preview') ?>
            <?php endif; ?>
        </td>
    </tr>
</table>


