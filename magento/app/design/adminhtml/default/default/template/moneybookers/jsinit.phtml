<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2020 Phoenix Medien GmbH & Co. KG (http://www.phoenix-medien.de)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
?>

<script type="text/javascript">
//<![CDATA[
    function initMoneybookers() {
        if (!document.getElementById("moneybookers_settings_activationstatus")) {
            // Don't perform for website and store view
            return;
        }
        document.getElementById("moneybookers_settings_activationstatus").style.display = "none";
        $('moneybookers_settings_activationstatus').up().insert('<button type="button" id="moneybookers_multifuncbutton" onclick="moneybookers.button()" style="display: none; margin-top: 1em;"><span id="moneybookers_multifuncbutton_label" style="display:block; margin-top:1em;"></span></button>');
        $('moneybookers_settings_activationstatus').up().insert('<span id="moneybookers_inf_div" style="display:block; margin-top:1em;"></span>');
        $('moneybookers_settings_activationstatus').up().insert('<input id="moneybookers_settings_customer_id_hidden" type="hidden"/>');
        moneybookers = new Moneybookers('<?php echo $this->getSkinUrl('images/moneybookers/banner.jpg'); ?>', '<?php echo $this->getUrl('*/moneybookers/activateemail'); ?>', '<?php echo $this->getUrl('*/moneybookers/checksecret'); ?>', '<?php echo $this->getUrl('*/moneybookers/checkemail');?>');
    }
//]]>
</script>

