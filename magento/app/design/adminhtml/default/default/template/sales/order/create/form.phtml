<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">
    var order = new AdminOrder(<?php echo $this->getOrderDataJson() ?>);
    order.setLoadBaseUrl('<?php echo $this->getLoadBlockUrl() ?>');
    var payment = {};
    payment.switchMethod = order.switchPaymentMethod.bind(order);
</script>
<form id="edit_form" action="<?php echo $this->getSaveUrl() ?>" method="post" enctype="multipart/form-data">
    <?php echo $this->getBlockHtml('formkey')?>
    <div id="order-message">
        <?php echo $this->getChildHtml('message') ?>
    </div>
    <div id="order-customer-selector" style="display:<?php echo $this->getCustomerSelectorDisplay() ?>">
        <?php echo $this->getChildHtml('customer') ?>
    </div>
    <div id="order-store-selector" style="display:<?php echo $this->getStoreSelectorDisplay() ?>">
        <?php echo $this->getChildHtml('store') ?>
    </div>
    <div id="order-data" style="display:<?php echo $this->getDataSelectorDisplay() ?>">
        <?php echo $this->getChildHtml('data') ?>
    </div>
</form>
