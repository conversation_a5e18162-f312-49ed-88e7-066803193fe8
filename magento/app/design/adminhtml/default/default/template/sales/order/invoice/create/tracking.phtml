<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /** @var $this Mage_Adminhtml_Block_Sales_Order_Invoice_Create_Tracking */ ?>
<script type="text/javascript">
//<![CDATA[
var trackingControl;
trackingControl = {
    index : 0,
    add : function () {
        this.index++;
        var data = {index:this.index};
        Element.insert($('track_row_container'), {bottom: this.template.evaluate(data)});
        $('trackingC' + this.index).disabled = false;
        $('trackingT' + this.index).disabled = false;
        $('trackingN' + this.index).disabled = false;
        this.bindCurrierOnchange();
    },
    deleteRow : function(event) {
        var row = Event.findElement(event, 'tr');
        if (row) {
            row.parentNode.removeChild(row)
        }
    },
    bindCurrierOnchange : function() {
        var elems = $('tracking_numbers_table').select('.select');
        elems.each(function (elem) {
            if (!elem.onchangeBound) {
                elem.onchangeBound = true;
                elem.valueInput = $(elem.parentNode.parentNode).select('.number-title')[0];
                elem.observe('change', this.currierOnchange);
            }
        }.bind(this));
    },
    currierOnchange : function(event) {
        var elem = Event.element(event);
        var option = elem.options[elem.selectedIndex];
        if (option.value && option.value != 'custom') {
            elem.valueInput.value = option.text;
        }
        else {
            elem.valueInput.value = '';
        }
    }
}
//]]>
</script>
<div class="grid">
<table cellspacing="0" class="data" id="tracking_numbers_table">
    <col width="100" />
    <col />
    <col />
    <col width="80" />
    <thead>
        <tr class="headings">
            <th><?php echo Mage::helper('sales')->__('Carrier') ?></th>
            <th><?php echo Mage::helper('sales')->__('Title') ?></th>
            <th><?php echo Mage::helper('sales')->__('Number') ?></th>
            <th class="last"><?php echo Mage::helper('sales')->__('Action') ?></th>
        </tr>
    </thead>
    <tfoot>
        <tr>
            <td colspan="4" class="a-center last" style="padding:8px;"><?php echo $this->getChildHtml('add_button') ?></td>
        </tr>
    </tfoot>
    <tbody id="track_row_container">
        <tr id="track_row_template" class="template no-display">
            <td>
                <select name="tracking[__index__][carrier_code]" id="trackingC__index__" class="select carrier" style="width:110px;" disabled="disabled">
                    <?php foreach ($this->getCarriers() as $_code=>$_name): ?>
                    <option value="<?php echo $_code ?>"><?php echo $this->escapeHtml($_name) ?></option>
                    <?php endforeach; ?>
                </select>
            </td>
            <td><input class="input-text number-title" type="text" name="tracking[__index__][title]" id="trackingT__index__" value="" disabled="disabled" /></td>
            <td><input class="input-text" type="text" name="tracking[__index__][number]" id="trackingN__index__" value="" disabled="disabled" /></td>
            <td class="last"><a href="#" onclick="trackingControl.deleteRow(event);return false"><?php echo $this->__('Delete') ?></a></td>
        </tr>
    </tbody>
</table>
</div>
<script type="text/javascript">
//<![CDATA[
trackingControl.template = new Template('<tr>' + $('track_row_template').innerHTML.replace(/__index__/g, '#{index}') + '<\/tr>');
//]]>
</script>
