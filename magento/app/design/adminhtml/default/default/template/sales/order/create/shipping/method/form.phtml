<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /** @var $this Mage_Adminhtml_Block_Sales_Order_Create_Shipping_Method_Form */ ?>
<?php $_shippingRateGroups = $this->getShippingRates(); ?>
<?php if ($_shippingRateGroups): ?>
    <div id="order-shipping-method-choose" style="display:none">
    <dl class="shipment-methods">
    <?php foreach ($_shippingRateGroups as $code => $_rates): ?>
        <dt><strong><?php echo $this->escapeHtml($this->getCarrierName($code)) ?></strong></dt>
        <dd>
            <ul>
            <?php foreach ($_rates as $_rate): ?>
                <?php $_radioProperty = 'name="order[shipping_method]" type="radio" onclick="order.setShippingMethod(this.value)"' ?>
                <?php $_code=$_rate->getCode() ?>
                <li>
                   <?php if ($_rate->getErrorMessage()): ?>
                        <ul class="messages">
                            <li class="error-msg"><?php echo $this->escapeHtml($_rate->getErrorMessage()) ?></li>
                        </ul>
                   <?php else: ?>
                        <?php $_checked = $this->isMethodActive($_code) ? 'checked="checked"' : '' ?>
                        <input <?php echo $_radioProperty ?> value="<?php echo $_code ?>" id="s_method_<?php echo $_code ?>" <?php echo $_checked ?>/>
                        <label class="normal" for="s_method_<?php echo $_code ?>">
                            <?php echo $this->escapeHtml($_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription()) ?> -
                            <strong>
                                <?php $_excl = $this->getShippingPrice($_rate->getPrice(), $this->helper('tax')->displayShippingPriceIncludingTax()); ?>
                                <?php $_incl = $this->getShippingPrice($_rate->getPrice(), true); ?>

                                <?php echo $_excl; ?>
                                <?php if ($this->helper('tax')->displayShippingBothPrices() && $_incl != $_excl): ?>
                                    (<?php echo $this->__('Incl. Tax'); ?> <?php echo $_incl; ?>)
                                <?php endif; ?>
                            </strong>
                        </label>
                   <?php endif ?>
                </li>
            <?php endforeach; ?>
            </ul>
        </dd>
    <?php endforeach; ?>
    </dl>
    </div>
    <?php if ($_rate = $this->getActiveMethodRate()): ?>
        <div id="order-shipping-method-info">
            <strong><?php echo $this->escapeHtml($this->getCarrierName($_rate->getCarrier())) ?></strong><br/>
            <?php echo $this->escapeHtml($_rate->getMethodTitle() ? $_rate->getMethodTitle() : $_rate->getMethodDescription()) ?> -
            <strong>
                <?php $_excl = $this->getShippingPrice($_rate->getPrice(), $this->helper('tax')->displayShippingPriceIncludingTax()); ?>
                <?php $_incl = $this->getShippingPrice($_rate->getPrice(), true); ?>

                <?php echo $_excl; ?>
                <?php if ($this->helper('tax')->displayShippingBothPrices() && $_incl != $_excl): ?>
                    (<?php echo $this->__('Incl. Tax'); ?> <?php echo $_incl; ?>)
                <?php endif; ?>
            </strong>
            <!--input type="text" class="input-text" style="width:70px"/-->
            <br/>
            <a href="#" onclick="$('order-shipping-method-info').hide();$('order-shipping-method-choose').show();return false">
                <?php echo Mage::helper('sales')->__('Click to change shipping method') ?>
            </a>
        </div>
    <?php else: ?>
        <script type="text/javascript">$('order-shipping-method-choose').show();</script>
    <?php endif; ?>
<?php elseif($this->getIsRateRequest()): ?>
    <strong><?php echo Mage::helper('sales')->__('Sorry, no quotes are available for this order at this time.') ?></strong>
<?php else: ?>
    <div id="order-shipping-method-summary">
        <a href="#" onclick="order.loadShippingRates();return false">
            <?php echo Mage::helper('sales')->__('Get shipping methods and rates') ?>
        </a>
        <input type="hidden" name="order[has_shipping]" value="" class="required-entry" />
    </div>
<?php endif; ?>
<div style="display:none;" id="shipping-method-overlay" class="overlay"><span><?php echo $this->__('Shipping method selection is not applicable') ?></span></div>
<script type="text/javascript">
order.overlay('shipping-method-overlay', <?php if ($this->getQuote()->isVirtual()): ?>false<?php else: ?>true<?php endif; ?>);
order.overlay('address-shipping-overlay', <?php if ($this->getQuote()->isVirtual()): ?>false<?php else: ?>true<?php endif; ?>);
</script>
