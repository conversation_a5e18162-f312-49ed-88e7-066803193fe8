<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php  $_order = $this->getShipment()->getOrder() ?>
<?php echo $this->getChildHtml('order_info') ?>

<div class="box-left">
    <!--Billing Address-->
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-payment-method"><?php echo $this->helper('sales')->__('Payment Information') ?></h4>
        </div>
        <fieldset>
            <div><?php echo $this->getChildHtml('order_payment') ?></div>
            <div><?php echo Mage::helper('sales')->__('Order was placed using %s', $_order->getOrderCurrencyCode()) ?></div>
        </fieldset>
    </div>
</div>
<div class="box-right">
    <!--Shipping Address-->
    <div class="entry-edit">
        <div class="entry-edit-head">
            <h4 class="icon-head head-shipping-method"><?php echo $this->helper('sales')->__('Shipping and Tracking Information') ?></h4>
        </div>
        <fieldset>
            <div>
                <?php if($this->getShipment()->getTracksCollection()->count()): ?>
                <a href="#" id="linkId" onclick="popWin('<?php echo $this->helper('shipping')->getTrackingPopupUrlBySalesModel($this->getShipment()) ?>','trackshipment','width=800,height=600,resizable=yes,scrollbars=yes')" title="<?php echo Mage::helper('core')->quoteEscape($this->__('Track this shipment')) ?>"><?php echo $this->__('Track this shipment') ?></a>
                <br/>
                <?php endif; ?>
                <strong><?php echo $this->escapeHtml($_order->getShippingDescription()) ?></strong>
                <?php echo $this->helper('sales')->__('Total Shipping Charges'); ?>:

                <?php if ($this->helper('tax')->displayShippingPriceIncludingTax()): ?>
                    <?php $_excl = $this->displayShippingPriceInclTax($_order); ?>
                <?php else: ?>
                    <?php $_excl = $this->displayPriceAttribute('shipping_amount', false, ' '); ?>
                <?php endif; ?>
                <?php $_incl = $this->displayShippingPriceInclTax($_order); ?>

                <?php echo $_excl; ?>
                <?php if ($this->helper('tax')->displayShippingBothPrices() && $_incl != $_excl): ?>
                    (<?php echo $this->__('Incl. Tax'); ?> <?php echo $_incl; ?>)
                <?php endif; ?>
            </div>
            <?php if ($this->canCreateShippingLabel()): ?>
            <div style="text-align: center; margin: 10px 0;">
                <?php echo $this->getCreateLabelButton()?>
                <?php if ($this->getShipment()->getShippingLabel()): ?>
                    <?php echo $this->getPrintLabelButton() ?>
                <?php endif ?>
                <?php if ($this->getShipment()->getPackages()): ?>
                    <?php echo $this->getShowPackagesButton() ?>
                <?php endif ?>
            </div>
            <?php endif ?>
            <div><?php echo $this->getChildHtml('shipment_tracking') ?></div>
        </fieldset>
    <?php echo $this->getChildHtml('shipment_packaging') ?>
    <script type="text/javascript">
    //<![CDATA[
        document.observe("dom:loaded", function() {
            setTimeout(function(){
                packaging.setConfirmPackagingCallback(function(){
                    packaging.sendCreateLabelRequest();
                });
                packaging.setLabelCreatedCallback(function(response){
                    setLocation("<?php echo $this->getUrl(
                        '*/sales_order_shipment/view',
                        array('shipment_id' => $this->getShipment()->getId())
                    ); ?>");
                });
            }, 500);
        });
    //]]>
    </script>
    </div>
</div>
<div class="clear"></div>

    <div class="entry-edit">
        <div class="entry-edit-head">
        <h4 class="icon-head head-products"><?php echo $this->helper('sales')->__('Items Shipped') ?></h4>
        </div>
</div>

<?php echo $this->getChildHtml('shipment_items') ?>
<?php echo $this->getChildHtml('shipment_packed') ?>

<div class="box-left entry-edit">
    <div class="entry-edit-head"><h4><?php echo $this->__('Shipment History') ?></h4></div>
    <fieldset><?php echo $this->getChildHtml('order_comments') ?></fieldset>
</div>
<div class="clear"></div>
