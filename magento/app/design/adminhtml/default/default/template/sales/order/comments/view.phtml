<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if ($_entity = $this->getEntity()): ?>
<div id="comments_block">
    <span class="field-row">
        <label class="normal" for="history_comment"><?php echo Mage::helper('sales')->__('Comment Text') ?></label>
        <textarea name="comment[comment]" rows="3" cols="5" style="height:6em; width:99%;" id="history_comment"></textarea>
    </span>
    <div class="f-left">
        <?php if ($this->canSendCommentEmail()): ?>
            <input name="comment[is_customer_notified]" type="checkbox" id="history_notify" value="1" />
            <label class="normal" for="history_notify"><?php echo Mage::helper('sales')->__('Notify Customer by Email') ?></label><br />
        <?php endif; ?>
        <input name="comment[is_visible_on_front]" type="checkbox" id="history_visible" value="1" /><label class="normal" for="history_visible"> <?php echo Mage::helper('sales')->__('Visible on Frontend') ?></label>
    </div>
    <div class="f-right">
        <?php echo $this->getChildHtml('submit_button') ?>
    </div>
    <div class="clear"></div>
    <ul class="note-list">
    <?php foreach ($_entity->getCommentsCollection(true) as $_comment): ?>
        <li>
            <strong><?php echo $this->helper('core')->formatDate($_comment->getCreatedAtDate(), 'medium') ?></strong>
            <?php echo $this->helper('core')->formatTime($_comment->getCreatedAtDate(), 'medium') ?><span class="separator">|</span><small><?php echo $this->helper('sales')->__('Customer') ?>
            <strong class="subdue">
            <?php if ($_comment->getIsCustomerNotified()): ?>
            <?php echo $this->helper('sales')->__('Notified') ?>
            <img src="<?php echo $this->getSkinUrl('images/ico_success.gif') ?>" width="16" height="16" alt="" />
            <?php else: ?>
            <?php echo $this->helper('sales')->__('Not Notified') ?>
            <?php endif; ?>
            </strong></small>
            <br/>
            <?php echo $this->escapeHtml($_comment->getComment(), array('b', 'br', 'strong', 'i', 'u', 'a')) ?>
        </li>
    <?php endforeach; ?>
    </ul>
<script type="text/javascript">
function submitComment() {
    submitAndReloadArea($('comments_block').parentNode, '<?php echo $this->getSubmitUrl() ?>')
}

if ($('submit_comment_button')) {
    $('submit_comment_button').observe('click', submitComment);
}
</script>
</div>
<?php endif; ?>
