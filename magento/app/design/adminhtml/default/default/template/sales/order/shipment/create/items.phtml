<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="grid np">
  <div class="hor-scroll">
    <table cellspacing="0" class="data order-tables">
        <col />
        <col width="1" />
        <col width="1" />
        <?php if (!$this->canShipPartiallyItem()): ?>
        <col width="20" />
        <?php endif; ?>
        <thead>
            <tr class="headings">
                <th><?php echo $this->helper('sales')->__('Product') ?></th>
                <th class="a-center"><?php echo $this->helper('sales')->__('Qty') ?></th>
                <th<?php if ($this->isShipmentRegular()): ?> class="last"<?php endif; ?>><span class="nobr"><?php echo $this->helper('sales')->__('Qty to Ship') ?></span></th>

                <?php if (!$this->canShipPartiallyItem()): ?>
                <th class="a-center last"><span class="nobr"><?php echo $this->helper('sales')->__('Ship') ?></span></th>
                <?php endif; ?>

            </tr>
        </thead>
        <?php $_items = $this->getShipment()->getAllItems() ?>
        <?php $_i=0;foreach ($_items as $_item): if ($_item->getOrderItem()->getIsVirtual() || $_item->getOrderItem()->getParentItem()): continue; endif; $_i++ ?>
        <tbody class="<?php echo $_i%2?'odd':'even' ?>">
            <?php echo $this->getItemHtml($_item) ?>
            <?php echo $this->getItemExtraInfoHtml($_item->getOrderItem()) ?>
        </tbody>
        <?php endforeach; ?>
    </table>
  </div>
</div>
<br />
<div class="box-left entry-edit">
    <div class="entry-edit-head"><h4><?php echo $this->__('Shipment Comments') ?></h4></div>
    <fieldset>
        <div id="order-history_form">
            <span class="field-row">
                <label class="normal" for="shipment_comment_text"><?php echo Mage::helper('sales')->__('Shipment Comments') ?></label>
                <textarea id="shipment_comment_text" name="shipment[comment_text]" rows="3" cols="5" style="height:6em; width:99%;"><?php echo $this->getShipment()->getCommentText(); ?></textarea>
            </span>
            <div class="clear"></div>
        </div>
    </fieldset>
</div>

<div class="box-right entry-edit">
    <div class="order-totals">
        <div class="order-totals-bottom">
          <?php if ($this->canCreateShippingLabel()): ?>
          <p>
              <label class="normal" for="create_shipping_label"><?php echo Mage::helper('sales')->__('Create Shipping Label') ?></label>
              <input id="create_shipping_label" name="shipment[create_shipping_label]" value="1" type="checkbox"  onclick="toggleCreateLabelCheckbox();" />
          </p>
          <?php endif ?>
          <p>
              <label class="normal" for="notify_customer"><?php echo Mage::helper('sales')->__('Append Comments') ?></label>
              <input id="notify_customer" name="shipment[comment_customer_notify]" value="1" type="checkbox" />
          </p>
          <?php if ($this->canSendShipmentEmail()): ?>
          <p>
              <label class="normal" for="send_email"><?php echo Mage::helper('sales')->__('Email Copy of Shipment') ?></label>
              <input id="send_email" name="shipment[send_email]" value="1" type="checkbox" />
          </p>
          <?php endif; ?>
          <div class="a-right">
          <?php echo $this->getChildHtml('submit_button') ?>
          </div>
        </div>
    </div>
</div>
<div class="clear"></div>
<script type="text/javascript">
//<![CDATA[
var sendEmailCheckbox = $('send_email');
if (sendEmailCheckbox) {
    var notifyCustomerCheckbox = $('notify_customer');
    var shipmentCommentText = $('shipment_comment_text');
    Event.observe(sendEmailCheckbox, 'change', bindSendEmail);
    bindSendEmail();
}
function bindSendEmail() {
    if (sendEmailCheckbox.checked == true) {
        notifyCustomerCheckbox.disabled = false;
        //shipmentCommentText.disabled = false;
    }
    else {
        notifyCustomerCheckbox.disabled = true;
        //shipmentCommentText.disabled = true;
    }
}
function toggleCreateLabelCheckbox() {
    var checkbox = $('create_shipping_label');
    var submitButton = checkbox.up('.order-totals').select('.submit-button span')[0];
    if (checkbox.checked) {
        submitButton.innerText += '...';
    } else {
        submitButton.innerText = submitButton.innerText.replace(/\.\.\.$/, '');
    }
}
function submitShipment(btn) {
    var checkbox = $(btn).up('.order-totals').select('#create_shipping_label')[0];

    if (!validQtyItems()) {
        return;
    }

    if (checkbox && checkbox.checked) {
        packaging.showWindow();
    } else if(editForm.submit()) {
        disableElements('submit-button');
    }
}

function validQtyItems() {
    var valid = true;
    var errorMessage = '<?php echo Mage::helper('core')->jsQuoteEscape($this->helper('sales')->__('Invalid value(s) for Qty to Ship')) ?>';
    $$('.qty-item').each(function(item) {
        var val = parseFloat(item.value);
        if (isNaN(val) || val < 0) {
            valid = false;
            alert(errorMessage);
            throw $break;
        }
    });
    return valid;
}
//]]>
</script>
