<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /* @var $this Mage_Sales_Block_Payment_Form_Billing_Agreement */?>
<?php $_code=$this->getMethodCode() ?>
<ul class="form-list" id="payment_form_<?php echo $_code ?>" style="display:none;">
    <li>
        <div class="input-box">
            <label for="<?php echo $_code ?>_ba_agreement_id"><?php echo $this->__('Billing Agreement') ?> <span class="required">*</span></label><br/>
            <select id="<?php echo $_code ?>_ba_agreement_id" name="payment[<?php echo $this->getTransportName(); ?>]" class="required-entry">
                <option value=""><?php echo $this->__('-- Please Select Billing Agreement--') ?></option>
            <?php foreach ($this->getBillingAgreements() as $id => $referenceId): ?>
                <option value="<?php echo $id ?>"<?php echo ($id == $this->getInfoData($this->getTransportName())) ? ' selected="selected"' : ''?>><?php echo $this->escapeHtml($referenceId) ?></option>
            <?php endforeach ?>
            </select>
        </div>
    </li>
</ul>
