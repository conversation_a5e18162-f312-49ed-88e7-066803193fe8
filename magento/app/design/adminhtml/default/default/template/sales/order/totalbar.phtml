<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php if( sizeof($this->getTotals()) > 0 ): ?>
<div class="box">
    <div class="entry-edit">
        <table border="0" cellspacing="0" width="100%">
            <tr>
            <?php foreach( $this->getTotals() as $_total ): ?>
                <td class="a-center">
                    <div <?php if ($_total['grand']): ?>class="grand-total"<?php endif; ?>>
                        <?php echo $_total['label'] ?><br />
                        <?php echo $_total['value'] ?>
                    </div>
                </td>
            <?php endforeach; ?>
            </tr>
        </table>
    </div>
</div>
<?php endif; ?>
