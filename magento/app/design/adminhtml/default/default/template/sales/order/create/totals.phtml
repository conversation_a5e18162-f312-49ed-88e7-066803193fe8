<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<table cellspacing="0" cellpadding="8" width="100%">
    <tbody>
        <?php echo $this->renderTotals(); ?>
        <?php echo $this->renderTotals('footer'); ?>
    </tbody>
</table>
<div class="divider"></div>
<div class="order-totals-bottom">
    <p>
        <label for="notify_customer" class="normal"><?php echo Mage::helper('sales')->__('Append Comments') ?></label>
        <input type="checkbox" id="notify_customer" name="order[comment][customer_note_notify]" value="1" <?php if($this->getNoteNotify()): ?>checked="true"<?php endif; ?>/>
    </p>
    <?php if ($this->canSendNewOrderConfirmationEmail()): ?>
    <p>
        <label for="send_confirmation" class="normal"><?php echo Mage::helper('sales')->__('Email Order Confirmation') ?></label>
        <input type="checkbox" id="send_confirmation" name="order[send_confirmation]" value="1" checked="checked"/>
    </p>
    <?php endif; ?>
    <p><?php echo $this->getButtonHtml(Mage::helper('sales')->__('Submit Order'),'order.submit()','save'); ?></p>
</div>

<script type="text/javascript">
//<![CDATA[
var sendEmailCheckbox = $('send_confirmation');
if (sendEmailCheckbox) {
    Event.observe(sendEmailCheckbox, 'change', notifyCustomerUpdate);
    notifyCustomerUpdate();
}

function notifyCustomerUpdate() {
    var sendEmailCheckbox = $('send_confirmation');
    var notifyCustomerCheckbox = $('notify_customer');
    if (!sendEmailCheckbox || !notifyCustomerCheckbox)
        return;
    notifyCustomerCheckbox.disabled = !sendEmailCheckbox.checked;
}
//]]>
</script>
