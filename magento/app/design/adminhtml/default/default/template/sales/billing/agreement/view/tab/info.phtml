<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="entry-edit">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('General Information'); ?></h4>
    </div>
    <div class="log-details">
        <table cellspacing="0">
            <col width="25%" />
            <col />
            <tbody>
                <tr>
                    <th><?php echo $this->__('Reference ID'); ?></th>
                    <td><?php echo $this->escapeHtml($this->getReferenceId()) ?></td>
                </tr>
                <tr>
                    <th><?php echo $this->__('Customer'); ?></th>
                    <td>
                        <a href="<?php echo $this->getCustomerUrl(); ?>">
                            <?php echo $this->escapeHtml($this->getCustomerEmail()) ?>
                        </a>
                    </td>
                </tr>
                <tr>
                    <th><?php echo $this->__('Status'); ?></th>
                    <td><?php echo $this->getStatus() ?></td>
                </tr>
                <tr>
                    <th><?php echo $this->__('Created At'); ?></th>
                    <td><?php echo $this->getCreatedAt(); ?></td>
                </tr>
                <tr>
                    <th><?php echo $this->__('Updated At'); ?></th>
                    <td><?php echo $this->getUpdatedAt(); ?></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
