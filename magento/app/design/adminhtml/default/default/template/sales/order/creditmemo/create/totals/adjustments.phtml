<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_source  = $this->getSource() ?>
<?php if ($_source): ?>
    <tr>
        <td class="label"><?php echo $this->getShippingLabel() ?></td>
        <td><input type="text" name="creditmemo[shipping_amount]" value="<?php echo $this->getShippingAmount()?>" class="input-text not-negative-amount" style="width:60px;text-align:right" id="shipping_amount" /></td>
    </tr>
    <tr>
        <td colspan="2"><div id="shipping_amount_adv"></div></td>
    </tr>
    <tr>
        <td class="label"><?php echo $this->helper('sales')->__('Adjustment Refund') ?></td>
        <td><input type="text" name="creditmemo[adjustment_positive]" value="<?php echo $_source->getBaseAdjustmentFeePositive()*1 ?>" class="input-text not-negative-amount" style="width:60px;text-align:right" id="adjustment_positive" /></td>
    </tr>
    <tr>
        <td colspan="2"><div id="adjustment_positive_adv"></div></td>
    </tr>
    <tr>
        <td class="label"><?php echo $this->helper('sales')->__('Adjustment Fee') ?></td>
        <td><input type="text" name="creditmemo[adjustment_negative]" value="<?php echo $_source->getBaseAdjustmentFeeNegative()*1 ?>" class="input-text not-negative-amount" style="width:60px;text-align:right" id="adjustment_negative"/></td>
    </tr>
    <tr>
        <td colspan="2"><div id="adjustment_negative_adv"></div></td>
    </tr>
    <script type="text/javascript">
    //<![CDATA[
    Validation.addAllThese([
        ['not-negative-amount', '<?php echo Mage::helper('core')->jsQuoteEscape($this->helper('sales')->__('Please enter positive number in this field.')) ?>', function(v) {
            if(v.length)
                return /^\s*\d+([,.]\d+)*\s*%?\s*$/.test(v);
            else
                return true;
        }]
    ]);

    if ($('shipping_amount')) {
        $('shipping_amount').advaiceContainer = $('shipping_amount_adv');
        unblockSubmit('shipping_amount');
    }
    if ($('adjustment_positive')) {
        $('adjustment_positive').advaiceContainer = $('adjustment_positive_adv');
        unblockSubmit('adjustment_positive');
    }
    if ($('adjustment_negative')) {
        $('adjustment_negative').advaiceContainer = $('adjustment_negative_adv');
        unblockSubmit('adjustment_negative');
    }

    function unblockSubmit(id) {
        $(id).observe('focus', function(event) {
            if ($$('button[class="scalable update-button disabled"]').size() > 0) {
                enableElements('submit-button');
            }
        });
    }
    //]]>
    </script>
<?php endif; ?>
