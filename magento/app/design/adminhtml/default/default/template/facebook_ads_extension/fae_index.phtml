<script>
// Debug Mode
setTimeout(
  function() {
    if (!window.facebookAdsExtensionConfig || !window.facebookAdsExtensionConfig.feedPrepared || !window.facebookAdsExtensionConfig.feedPrepared.samples) {
      var div = document.createElement("DIV");
      div.id = "fae-debug-mode-text";
      var a = document.createElement("A");
      a.href=window.facebookAdsExtensionAjax.debug;
      var t2 = document.createTextNode("The extension failed to load due to a critical error. Please enter Debug Mode to understand the cause.");
      var body = document.getElementById('fae-flow');
      var loader = document.getElementById('fae-loader');
      loader.hidden = true;
      div.appendChild(t2);
      a.appendChild(document.createTextNode("Enter Debug Mode"))
      var div2 = document.createElement("DIV").appendChild(a);
      div2.id = "fae-debug-mode-link";
      body.appendChild(div);
      body.appendChild(div2);
    }
  },
  10000
);

window.facebookAdsExtensionAjax = {
  setDiaSettingId: '<?php echo $this->getDiaSettingIdAjaxRoute() ?>'
 ,setPixelId: '<?php echo $this->getPixelAjaxRoute() ?>'
 ,setStoreId: '<?php echo $this->getStoreAjaxRoute() ?>'
 ,generateFeedNow: '<?php echo $this->getFeedGenerateNowAjaxRoute() ?>'
 ,setMsgerChatSetup: '<?php echo $this->getMsgerChatSetupAjaxRoute() ?>'
 ,debug: '<?php echo $this->getDebugRoute() ?>'
 ,upgrade: '<?php echo $this->getUpgradeAjaxRoute() ?>'
};
</script>

<div id="wrapper">
  <div id="facebook-header">
    <table><tbody>
      <tr><td><i class='logo'></i></td>
      </tbody></table>
  </div>
  <div id="fae-flow">
    <div class="fae-loader" id="fae-loader"></div>
  </div>
</div>

<script>

window.facebookAdsExtensionConfig = {
  hasGzipSupport: <?php echo extension_loaded('zlib') ? 'true' : 'false' ?>
 ,enabledPlugins: ['MESSENGER_CHAT','INSTAGRAM_SHOP']
 ,popupOrigin: 'https://www.facebook.com/ads/dia'
 ,feedWasDisabled: '<?php echo $this->enableFeedNOW() ?>'
 ,feedWritePermissionError: '<?php echo $this->checkFeedWriteError() ?>'
 ,platform: 'Magento'
 ,pixel: {
    pixelId: '<?php echo $this->fetchPixelId() ?>'
   ,advanced_matching_supported: true
  }
 ,pixel_install_time: '<?php echo $this->getPixelInstallTime() ?>'
 ,store: {
    baseUrl: '<?php echo $this->getStoreBaseUrl() ?>'
   ,baseCurrency: '<?php echo $this->fetchStoreBaseCurrency() ?>'
   ,timezoneId: <?php echo $this->fetchStoreTimezone() ?>
   ,storeName: '<?php echo $this->fetchStoreName() ?>'
   ,version: '<?php echo FacebookAdsExtension::getMagentoVersion() ?>'
   ,plugin_version: '<?php echo FacebookAdsExtension::version() ?>'
   ,debug_key: '<?php echo FacebookAdsExtension::getDebugKey() ?>'
   ,canSetupShop: true
  }
 ,feed: {
    enabled: <?php echo $this->fetchFeedSetupEnabled() === 1 ? 'true' : 'false' ?>
   ,format: '<?php echo $this->fetchFeedSetupFormat() ?>'
   ,totalVisibleProducts: <?php echo FacebookAdsExtension::getTotalVisibleProducts(
        $this->getSelectedStore()
      )
    ?>
 }
 ,defaultStoreId: '<?php echo $this->getSelectedStore() ?>'
 ,stores: '<?php echo json_encode($this->getStores()) ?>'
<?php if ($this->getDiaSettingId() != null) { ?>
 ,diaSettingId: <?php echo $this->getDiaSettingId() ?>
<?php } else { ?>
  /* no dia setting id found */
<?php } ?>
 ,feedPrepared: {
    feedUrl: '<?php echo $this->getFeedUrl() ?>'
   ,feedPingUrl: '<?php echo FacebookAdsExtension::getFeedPingUrl() ?>'
   ,samples:
<?php
  $samples = $this->fetchFeedSamples();
  if ($samples && count($samples) > 0) {
    echo json_encode($samples);
  } else {
    echo '[ /* no samples are generated */ ]';
  }
?>
  }
};
// I used this to test : window.facebookAdsExtensionConfig = null; // will remove before landing.
</script>
