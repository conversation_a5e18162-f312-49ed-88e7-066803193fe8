<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php
/**
 * JS block for including Countries with Optional Zip
 *
 * @see Mage_Adminhtml_Block_Template
 */
?>
<script type="text/javascript">
//<![CDATA[
optionalZipCountries = <?php echo $this->helper('directory')->getCountriesWithOptionalZip(true) ?>;

function onAddressCountryChanged (countryElement) {
    var zipElementId = countryElement.id.replace(/country_id/, 'postcode');

    // Ajax-request and normal content load compatibility
    if ($(zipElementId) != undefined) {
        setPostcodeOptional($(zipElementId), countryElement.value);
    } else {
        Event.observe(window, "load", function () {
            setPostcodeOptional($(zipElementId), countryElement.value);
        });
    }
}

function setPostcodeOptional(zipElement, country) {
    if (optionalZipCountries.indexOf(country) != -1) {
        while (zipElement.hasClassName('required-entry')) {
            zipElement.removeClassName('required-entry');
        }
        zipElement.up(1).down('label > span.required').hide();
    } else {
        zipElement.addClassName('required-entry');
        zipElement.up(1).down('label > span.required').show();
    }
}

varienGlobalEvents.attachEventHandler("address_country_changed", onAddressCountryChanged);
//]]>
</script>
