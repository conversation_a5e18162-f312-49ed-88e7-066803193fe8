<div class="entry-edit">
    <form>
        <div class="entry-edit-head">
          <h4 class="fieldset-legend">Check why product can't be found by search phrase</h4>
        </div>
        <div class="fieldset">
            <div class="hor-scroll">
                <table cellspacing="0" class="form-list">
                    <tr>
                        <td class="label">
                            <label>Search Phrase <span class="required">*</span></label>
                        </td>
                        <td class="value">
                            <input type="text" class="input-text required-entry" name="q" value="<?php echo $this->getQ() ?>">
                        </td>
                    </tr>
                    <tr>
                        <td class="label">
                            <label>Product ID <span class="required">*</span></label>
                        </td>
                        <td class="value">
                            <input type="text" class="input-text required-entry" name="id" value="<?php echo $this->getId() ?>">
                        </td>
                    </tr>
                    <tr>
                        <td></td>
                        <td class="value">
                            <button type="submit" class="scalable save"><span><span><span>Submit</span></span></span></button>
                        </td>
                </table>
            </div>
        </div>
    </form>
</div>
<?php $_results = $this->getValidationResult() ?>
<div class="grid">
    <table class="data" cellspacing="0">
        <thead>
            <tr class="headings">
                <th>Test</th>
                <th>Status</th>
                <th></th>
            </tr>
        </thead>
    <?php foreach ($_results as $_result): ?>
        <tr>
            <td width="500px"><?php echo $_result[1] ?></td>
            <td width="100px">
                <?php if ($_result[0] == true): ?>
                <div class="grid-severity-notice"><span>Success</span></div>
                <?php else: ?>
                <div class="grid-severity-critical"><span>Fail</span></div>
                <?php endif ?>
            </td>
            <td><?php echo $_result[2] ?></td>
        </tr>
    <?php endforeach ?>
    </table>
</div>