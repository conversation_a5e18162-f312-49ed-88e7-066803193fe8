<?php
/**
 * aheadWorks Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://ecommerce.aheadworks.com/AW-LICENSE.txt
 *
 * =================================================================
 *                 MAGENTO EDITION USAGE NOTICE
 * =================================================================
 * This software is designed to work with Magento community edition and
 * its use on an edition other than specified is prohibited. aheadWorks does not
 * provide extension support in case of incorrect edition use.
 * =================================================================
 *
 * @category   AW
 * @package    AW_Onsale
 * @version    2.5.4
 * @copyright  Copyright (c) 2010-2012 aheadWorks Co. (http://www.aheadworks.com)
 * @license    http://ecommerce.aheadworks.com/AW-LICENSE.txt
 */
?><div class="content-header">
    <h3><?php echo $this->getHeaderText(); ?></h3>
    <p class="form-buttons"><?php echo $this->getBackButton();?></p>
</div>

<div class="entry-edit main-plugin-block">
    <?php echo $this->getPluginsHtml();?>
</div>
<script type="text/javascript">
    Event.observe(window, 'load', function(){
        var state = localStorage.getItem('state');
        if (null !== state) {
            state = state.split(',');
            for (var i=0; i<state.length; i++) {
                if ($(state[i])) {
                    Fieldset.toggleCollapse(state[i]);
                }
            }
        }
    });
    var saveState = function(fieldset)
    {
        var state = [];
        var needPush = true;
        if (null !== localStorage.getItem('state')) {
            state = localStorage.getItem('state').split(',');
        }
        for (var i=0; i<state.length; i++) {
            if (state[i] == fieldset) {
                state.splice(i, 1);
                needPush = false;
            }
        }
        if (needPush === true) {
            state.push(fieldset);
        }
        localStorage.setItem('state', state.join(','));
    }
</script>