<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="entry-edit" id="export_filter_container" style="display:none;">
    <div class="entry-edit-head">
        <h4 class="icon-head head-edit-form fieldset-legend"><?php echo $this->__('Entity Attributes'); ?></h4>
    </div>
    <form id="export_filter_form" action="<?php echo $this->getUrl('*/*/export') ?>" method="post">
        <input name="form_key" type="hidden" value="<?php echo $this->getFormKey() ?>" />
        <div id="export_filter_grid_container" class="fieldset"><!-- --></div>
    </form>
    <div class="a-right">
        <button class="scalable" type="button" onclick="getFile();"><span><span><span><?php echo $this->__('Continue') ?></span></span></span></button>
    </div>
</div>
<script type="text/javascript">
//<![CDATA[
   $('entity').selectedIndex = 0; // forced resetting entity selector after page refresh
//]]>
</script>
