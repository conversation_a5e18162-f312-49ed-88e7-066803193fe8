<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @var $this Mage_Currencysymbol_Block_Adminhtml_System_Currencysymbol
 */
?>
<div class="content-header">
    <table cellspacing="0">
        <tr>
            <td style="width:50%;"><h3 class="icon-head head-system-currency"><?php echo $this->getHeader() ?></h3></td>
            <td class="form-buttons">
                <?php
                    echo $this->getSaveButtonHtml();
                ?>
            </td>
        </tr>
    </table>
</div>

<?php $this->getCurrencySymbolsData();?>

<form id="currency_symbols_form" action="<?php echo $this->getFormActionUrl() ?>" method="post">
    <input name="form_key" type="hidden" value="<?php echo $this->getFormKey() ?>" />

    <div class="entry-edit">
        <div class="fieldset fieldset-wide">
            <div class="grid">
                <div class="hor-scroll">
                    <table cellspacing="0" class="data table-cat-img-sizes">
                        <colgroup>
                            <col width="15%"/>
                            <col />
                        </colgroup>
                        <thead>
                            <tr class="headings">
                                <th><span class="nobr"><span class="not-sort"><?php echo $this->__('Currency'); ?></span></span></th>
                                <th><span class="nobr"><span class="not-sort"><?php echo $this->__('Symbol'); ?><span class="required"><em>*</em></span></span></span></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach($this->getCurrencySymbolsData() as $code => $data): ?>
                            <tr>
                                <td class="label">
                                <label for="custom_currency_symbol<?php echo $this->escapeHtml($code); ?>"><?php echo $this->escapeHtml($code); ?> (<?php echo $this->escapeHtml($data['displayName']); ?>)</label>
                                </td>
                                <td class="value">
                                    <input id="custom_currency_symbol<?php echo $this->escapeHtml($code); ?>" class=" required-entry input-text" type="text" value="<?php echo Mage::helper('core')->quoteEscape($this->escapeHtml($data['displaySymbol'])); ?>"<?php echo $data['inherited'] ? ' disabled="disabled"' : '';?> name="custom_currency_symbol[<?php echo $this->escapeHtml($code); ?>]">
                                    &nbsp; <input id="custom_currency_symbol_inherit<?php echo $this->escapeHtml($code); ?>" class="checkbox config-inherit" type="checkbox" onclick="toggleUseDefault(<?php echo '\'' . $this->escapeHtml($code) . '\',\'' . Mage::helper('core')->quoteEscape($data['parentSymbol'], true) . '\''; ?>)"<?php echo $data['inherited'] ? ' checked="checked"' : ''; ?> value="1" name="inherit_custom_currency_symbol[<?php echo $this->escapeHtml($code); ?>]">
                                    <label class="inherit" title="" for="custom_currency_symbol_inherit<?php echo $this->escapeHtml($code); ?>"><?php echo $this->getInheritText(); ?></label>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</form>
<script type="text/javascript">
    var currencySymbolsForm = new varienForm('currency_symbols_form');

    function toggleUseDefault(code, value)
    {
        checkbox = $('custom_currency_symbol_inherit'+code);
        input = $('custom_currency_symbol'+code);
        if (checkbox.checked) {
            input.value = value;
            input.disabled = true;
        } else {
            input.disabled = false;
        }
    }
</script>

