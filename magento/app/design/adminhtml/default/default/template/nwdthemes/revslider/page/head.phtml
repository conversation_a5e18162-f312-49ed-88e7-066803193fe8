<meta http-equiv="Content-Type" content="<?php echo $this->getContentType() ?>"/>
<title><?php echo htmlspecialchars(html_entity_decode($this->getTitle())) ?></title>
<link rel="icon" href="<?php echo $this->getSkinUrl('favicon.ico') ?>" type="image/x-icon"/>
<link rel="shortcut icon" href="<?php echo $this->getSkinUrl('favicon.ico') ?>" type="image/x-icon"/>

<script type="text/javascript">
    var BLANK_URL = '<?php echo $this->getJsUrl() ?>blank.html';
    var BLANK_IMG = '<?php echo $this->getJsUrl() ?>spacer.gif';
    var BASE_URL = '<?php echo $this->getUrl('*') ?>';
    var SKIN_URL = '<?php echo $this->getSkinUrl() ?>';
    var FORM_KEY = '<?php echo $this->getFormKey() ?>';
</script>

<?php echo $this->getChildHtml('thickbox.js') ?>
<?php echo $this->getCssJsHtml() ?>

<?php if($this->getCanLoadExtJs()): ?>
<script type="text/javascript">
    Ext.BLANK_IMAGE_URL = BLANK_IMG;
    Ext.UpdateManager.defaults.loadScripts = false;
    Ext.UpdateManager.defaults.disableCaching = true;
</script>
<?php endif; ?>

<?php if($this->getCanLoadTinyMce()): // TinyMCE is broken when loaded through index.php ?>
<script type="text/javascript" src="<?php echo $this->getJsUrl() ?>tiny_mce/tiny_mce.js"></script>
<?php endif; ?>

<script type="text/javascript">
    Fieldset.addToPrefix(<?php echo Mage::helper('adminhtml')->getCurrentUserId() ?>);
</script>

<?php echo $this->helper('core/js')->getTranslatorScript() ?>
<?php echo $this->getChildHtml('calendar'); ?>
<?php echo $this->getChildHtml('optional_zip_countries'); ?>

<?php foreach ($inlineStyles as $_handle) foreach ($_handle as $_style) echo $_style . "\n"; ?>
<script type="text/javascript">
    <?php foreach ($localizeScripts as $_script) : ?>
        var <?php echo $_script['var']; ?> = <?php echo json_encode($_script['lang']); ?>;
    <?php endforeach; ?>
</script>