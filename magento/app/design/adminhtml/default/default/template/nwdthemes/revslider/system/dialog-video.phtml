<!-- //Youtube dialog: -->
<div id="dialog_video" class="dialog-video" title="<?php echo $this->__('Add Video Layer'); ?>" style="display:none">

	<form id="video_dialog_form" name="video_dialog_form" onkeypress="return event.keyCode != 13;">
		<div id="video_content" style="display:none"></div>

		<div id="video-dialog-wrap">
            <div id="video_dialog_tabs" class="box-closed tp-accordion disabled" style="background:#fff">
				<ul class="rs-layer-settings-tabs">
                    <li class="selected" data-content="#rs-video-source" id="reset_video_dialog_tab"><i style="height:45px" class="rs-mini-layer-icon eg-icon-export rs-toolbar-icon"></i><?php echo $this->__('Source'); ?></li>
                    <li class="rs-hide-on-audio" data-content="#rs-video-size"><i style="height:45px; font-size:16px" class="rs-mini-layer-icon eg-icon-resize-full-alt rs-toolbar-icon"></i><?php echo $this->__('Sizing'); ?></li>
                    <li class="" data-content="#rs-video-settings"><i style="height:45px; font-size:16px" class="rs-mini-layer-icon eg-icon-cog rs-toolbar-icon"></i><?php echo $this->__('Settings'); ?></li>
                    <li class="rs-hide-on-audio" data-content="#rs-video-thumbnails"><i style="height:45px; font-size:16px" class="rs-mini-layer-icon eg-icon-eye rs-toolbar-icon"></i><?php echo $this->__('Poster/Mobile Visibility'); ?></li>
                    <li class="" data-content="#rs-video-arguments"><i style="height:45px; font-size:16px" class="rs-mini-layer-icon eg-icon-th rs-toolbar-icon"></i><?php echo $this->__('Arguments'); ?></li>
				</ul>
				<div style="clear:both"></div>
			</div>
			
		</div>

		<div id="rs-video-source">
			<!-- Type chooser -->
			<div id="video_type_chooser" class="video-type-chooser" style="margin-bottom:25px">
				<label><?php echo $this->__('Choose video type'); ?></label>
				<input type="radio" checked id="video_radio_youtube" name="video_select">
				<span for="video_radio_youtube"><?php echo $this->__('YouTube'); ?></span>
				<input type="radio" id="video_radio_vimeo" name="video_select" style="margin-left:20px">
				<span for="video_radio_vimeo"><?php echo $this->__('Vimeo'); ?></span>
				<input type="radio" id="video_radio_html5" name="video_select" style="margin-left:20px">
				<span for="video_radio_html5"><?php echo $this->__('HTML5'); ?></span>
				
				<span class="rs-show-when-youtube-stream" style="display: none;">
					<input type="radio" id="video_radio_streamyoutube" name="video_select" style="margin-left:20px">
					<span for="video_radio_streamyoutube"><?php echo $this->__('From Stream'); ?></span>
				</span>
				<span class="rs-show-when-vimeo-stream" style="display: none;">
					<input type="radio" id="video_radio_streamvimeo" name="video_select" style="margin-left:20px">
					<span for="video_radio_streamvimeo"><?php echo $this->__('From Stream'); ?></span>
				</span>
				<span class="rs-show-when-instagram-stream" style="display: none;">
					<input type="radio" id="video_radio_streaminstagram" name="video_select" style="margin-left:20px">
					<span for="video_radio_streaminstagram"><?php echo $this->__('From Stream'); ?></span>
				</span>
                
                <input type="radio" checked id="video_radio_audio" name="video_select" style="display: none;">
			</div>


			<!-- Vimeo block -->		
			<div id="video_block_vimeo" class="video-select-block" style="display:none;" >
				<label><?php echo $this->__('Vimeo ID or URL'); ?></label>
				<input type="text" id="vimeo_id" value="">
				<input type="button" style="vertical-align:middle" id="button_vimeo_search" class="button-regular video_search_button" value="search">
				<span class="video_example"><?php echo $this->__('example: 30300114'); ?></span>		
				<img id="vimeo_loader" src="<?php echo Nwdthemes_Revslider_Helper_Framework::$RS_PLUGIN_URL; ?>/admin/assets/images/loader.gif" style="display:none">
			</div>

			<!-- Youtube block -->
			<div id="video_block_youtube" class="video-select-block">
				<label><?php echo $this->__('YouTube ID or URL'); ?></label>
				<input type="text" id="youtube_id" value="">
				<input type="button" style="vertical-align:middle" id="button_youtube_search" class="button-regular video_search_button" value="search">
				<span class="video_example"><?php echo $this->__('example'); ?>: <?php echo RevSliderGlobals::YOUTUBE_EXAMPLE_ID; ?></span>
				<img id="youtube_loader" src="<?php echo Nwdthemes_Revslider_Helper_Framework::$RS_PLUGIN_URL; ?>/admin/assets/images/loader.gif" style="display:none">
			</div>
			
			<!-- Html 5 block -->		
			<div id="video_block_html5" class="video-select-block" style="display:none;">
				<label><?php echo $this->__('Poster Image Url'); ?></label>
				<input style="width:330px" type="text" id="html5_url_poster" name="html5_url_poster" value="">
				<span class="imgsrcchanger-div" style="margin-left:20px;">
                    <a href="javascript:void(0)" class="button-image-select-html5-video button-primary revblue" ><?php echo $this->__('Choose from Library'); ?></a>
				</span>
                <span class="video_example">&nbsp;</span>
				

				<label><?php echo $this->__('Video MP4 Url'); ?></label>
				<input style="width:330px" type="text" id="html5_url_mp4" name="html5_url_mp4" value="">
				<span class="vidsrcchanger-div" style="margin-left:20px;">
                    <a href="javascript:void(0)" data-inptarget="html5_url_mp4" class="button_change_video button-primary revblue" ><?php echo $this->__('Choose from Library'); ?></a>
				</span>
                <span class="video_example"><?php echo $this->__("example"); ?>: http://clips.vorwaerts-gmbh.de/big_buck_bunny.mp4</span>

				<label><?php echo $this->__('Video WEBM Url'); ?></label>
				<input style="width:330px" type="text" id="html5_url_webm" name="html5_url_webm" value="">
				<span class="vidsrcchanger-div" style="margin-left:20px;">
                    <a href="javascript:void(0)" data-inptarget="html5_url_webm" class="button_change_video button-primary revblue" ><?php echo $this->__('Choose from Library'); ?></a>
				</span>
                <span class="video_example"><?php echo $this->__('example'); ?>: http://clips.vorwaerts-gmbh.de/big_buck_bunny.webm</span>

				<label><?php echo $this->__('Video OGV Url'); ?></label>
				<input style="width:330px" type="text" id="html5_url_ogv" name="html5_url_ogv" value="">
				<span class="vidsrcchanger-div" style="margin-left:20px;">
                    <a href="javascript:void(0)" data-inptarget="html5_url_ogv" class="button_change_video button-primary revblue" ><?php echo $this->__('Choose from Library'); ?></a>
				</span>
                <span class="video_example"><?php echo $this->__('example'); ?>: http://clips.vorwaerts-gmbh.de/big_buck_bunny.ogv</span>

			</div>
            
            <div id="video_block_audio" class="video-select-block" style="display:none;" >
                <label><?php echo $this->__('Audio Url'); ?></label>
                <input style="width:330px" type="text" id="html5_url_audio" name="html5_url_audio" value="">
                <span class="vidsrcchanger-div" style="margin-left:20px;">
                    <a href="javascript:void(0)" data-inptarget="html5_url_audio" class="button_change_video button-primary revblue" ><?php echo $this->__('Choose from Library'); ?></a>
                </span>
            </div>
		</div>


		<div id="rs-video-size"  style="display:none">
			<!-- Video Sizing -->
			<div id="video_size_wrapper" class="youtube-inputs-wrapper">
				<label for="input_video_fullwidth"><?php echo $this->__('Full Screen:'); ?></label>	
				<input type="checkbox" class="tp-moderncheckbox rs-staticcustomstylechange tipsy_enabled_top" id="input_video_fullwidth">
				<div class="clearfix mb10"></div>
			</div>
			
			<label for="input_video_cover" class="video-label"><?php echo $this->__('Force Cover:'); ?></label>
			<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox mb10" id="input_video_cover">

            <div id="fullscreenvideofun1" class="video-settings-line mb10">
				<label for="input_video_dotted_overlay" class="video-label" id="input_video_dotted_overlay_lbl">
					<?php echo $this->__('Dotted Overlay:'); ?>
				</label>
				<select id="input_video_dotted_overlay" style="width:100px">
					<option value="none"><?php echo $this->__('none'); ?></option>
					<option value="twoxtwo"><?php echo $this->__('2 x 2 Black'); ?></option>
					<option value="twoxtwowhite"><?php echo $this->__('2 x 2 White'); ?></option>
					<option value="threexthree"><?php echo $this->__('3 x 3 Black'); ?></option>
					<option value="threexthreewhite"><?php echo $this->__('3 x 3 White'); ?></option>
				</select>
				<div class="clearfix mb10"></div>
				<label for="input_video_ratio" class="video-label" id="input_video_ratio_lbl">
					<?php echo $this->__('Aspect Ratio:'); ?>
				</label>				
				<select id="input_video_ratio" style="width:100px">
					<option value="16:9"><?php echo $this->__('16:9'); ?></option>
					<option value="4:3"><?php echo $this->__('4:3'); ?></option>
				</select>
			</div>
            <div id="video_full_screen_settings" class="video-settings-line">
                <div class="mb10">
                    <label for="input_video_leave_fs_on_pause"><?php echo $this->__('Leave Full Screen on Pause/End:'); ?></label>
                    <input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_leave_fs_on_pause">
                </div>
            </div>
		</div>

		<div id="rs-video-settings" style="display:none">
			<div class="mb10">
                <label for="input_video_loop"><?php echo $this->__("Loop:"); ?></label>
				<?php /* <input type="checkbox" class="checkbox_video_dialog  mtop_13" id="input_video_loop" > */ ?>
				<select id="input_video_loop" style="width: 200px;">
					<option value="none"><?php echo $this->__('Disable'); ?></option>
                    <option class="rs-hide-on-audio" value="loop"><?php echo $this->__('Loop, Slide is paused'); ?></option>
                    <option class="rs-hide-on-audio" value="loopandnoslidestop"><?php echo $this->__('Loop, Slide does not stop'); ?></option>
                    <option class="rs-show-on-audio" value="loopandnoslidestop"><?php echo $this->__('Loop Segment'); ?></option>
				</select>
			</div>

			<div class="mb10">
				<label for="input_video_autoplay"><?php echo $this->__('Autoplay:'); ?></label>
				<select id="select_video_autoplay">
					<option value="false"><?php echo $this->__('Off'); ?></option>
					<option value="true"><?php echo $this->__('On'); ?></option>
					<option value="1sttime"><?php echo $this->__('On 1st Time'); ?></option>
					<option value="no1sttime"><?php echo $this->__('Not on 1st Time'); ?></option>
				</select>
			</div>

			<div class="mb10">
				<label for="input_video_stopallvideo"><?php echo $this->__('Stop Other Media:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_stopallvideo" >
			</div>

            <div class="mb10 hide-for-vimeo rs-hide-on-audio">
				<label for="input_video_allowfullscreen"><?php echo $this->__('Allow FullScreen:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_allowfullscreen" >
			</div>

			<div class="mb10">
				<label for="input_video_nextslide"><?php echo $this->__('Next Slide On End:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_nextslide" >
			</div>

			<div class="mb10">
				<label for="input_video_force_rewind"><?php echo $this->__('Rewind at Slide Start:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_force_rewind" >
			</div>

            <div class="mb10">
				<label for="input_video_control"><?php echo $this->__('Hide Controls:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_control" >
                <span style="vertical-align:middle; margin-left:15px; display:none" class="description hidecontroldepend"><?php echo $this->__('Layer Action may needed to start/stop Video'); ?></span>
			</div>

            <script>
                (function(jQuery) {
                jQuery('#input_video_control').on('change',function() {
                    if (jQuery(this).attr('checked')==="checked")
                        jQuery('.hidecontroldepend').show();
                    else
                        jQuery('.hidecontroldepend').hide();
                })
                })($nwd_jQuery);
            </script>

            <div class="mb10 rs-hide-on-audio">
				<label for="input_video_mute"><?php echo $this->__('Mute:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_mute" >
			</div>

			<div class="mb10 video-volume">
				<label for="input_video_volume"><?php echo $this->__('Volume (0 - 100):'); ?></label>
				<input type="text" class="input_video_dialog" style="width: 50px;" id="input_video_volume" >
			</div>

			<div class="mb10">
                <span class="rs-hide-on-audio"><label for="input_video_start_at"><?php echo $this->__('Start at:'); ?></label></span>
                <span class="rs-show-on-audio"><label for="input_video_start_at"><?php echo $this->__('Segment Start:'); ?></label></span>
				<input type="text" id="input_video_start_at" style="width: 50px;"> <?php echo $this->__('i.e.: 0:17'); ?>
			</div>
			
			<div class="mb10">
                <span class="rs-hide-on-audio"><label for="input_video_end_at"><?php echo $this->__('End at:'); ?></label></span>
                <span class="rs-show-on-audio"><label for="input_video_end_at"><?php echo $this->__('Segment End:'); ?></label></span>
				<input type="text" id="input_video_end_at" style="width: 50px;"> <?php echo $this->__('i.e.: 2:41'); ?>
			</div>
			
            <div class="mb10 rs-hide-on-audio">
				<label for="input_video_show_cover_pause"><?php echo $this->__('Show Cover at Pause:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_show_cover_pause" >
			</div>

            <div class="mb10 rs-show-on-audio">
                <label for="input_video_show_visibility"><?php echo $this->__('Invisible on Frontend:'); ?></label>
                <input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_show_visibility" >
            </div>
            
            <div id="rev-youtube-options" class="video-settings-line mb10 rs-hide-on-audio">
				<div class="mb10">
					<label for="input_video_speed"><?php echo $this->__('Video Speed:'); ?></label>
					<select id="input_video_speed" style="width:75px">
						<option value="0.25">0.25</option>
						<option value="0.50">0.50</option>
						<option value="1">1</option>
						<option value="1.5">1.5</option>
						<option value="2">2</option>
					</select>
				</div>
            </div>

            <div id="rev-playsinline-options" class="video-settings-line mb10 rs-hide-on-audio">
				<div class="mb10">
					<label for="input_video_play_inline"><?php echo $this->__('Play Video Inline:'); ?></label>
					<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_play_inline" >
				</div>
			</div>

            <div class="mb10 rs-show-on-audio" style="display: none">
                <div class="mb10">
                    <label for="input_audio_preload" class="video-label">
                        <?php echo $this->__("Audio Preload:")?>
                    </label>
                    <select id="input_audio_preload" style="width:200px">
                        <option value="none"><?php echo $this->__('Disable'); ?></option>
                        <option value="metadata"><?php echo $this->__('Metadata'); ?></option>
                        <option value="progress"><?php echo $this->__('Progress'); ?></option>
                        <option value="canplay"><?php echo $this->__('Can Play'); ?></option>
                        <option value="canplaythrough"><?php echo $this->__('Can Play Through'); ?></option>
                    </select>
                </div>
                <div class="mb10">
                    <label for="input_audio_preload" class="video-label">
                        <?php echo $this->__("Ignore Preload after "); ?>
                    </label>
                    <select id="input_video_preload_wait">
						<option value="0">0</option>
						<option value="1">1</option>
						<option value="2">2</option>
						<option value="3">3</option>
						<option value="4">4</option>
						<option value="5">5</option>
						<option value="6">6</option>
						<option value="7">7</option>
						<option value="8">8</option>
						<option value="9">9</option>
						<option value="10">10</option>
                    </select><?php echo $this->__(" seconds"); ?>
                </div>
            </div>
            
			<div id="rev-html5-options" style="display: none; mb10">
            
                <div class="mb10">
                    <label for="input_video_preload" class="video-label">
                        <?php echo $this->__("Video Preload:")?>
                    </label>
                    <select id="input_video_preload" style="width:200px">
                        <option value="auto"><?php echo $this->__('Auto'); ?></option>
                        <option value="none"><?php echo $this->__('Disable'); ?></option>
                        <option value="metadata"><?php echo $this->__('Metadata'); ?></option>
                    </select>
                </div>
                
                <div class="mb10">
                    <label for="input_video_large_controls"><?php echo $this->__('Large Controls:'); ?></label>
                    <input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_video_large_controls" >
                </div>
			</div>
		</div>

		<div id="rs-video-thumbnails" style="display:none">
			<div id="preview-image-video-wrap" class="mb10">
                <label><?php echo $this->__('Poster Image'); ?></label>
				<input type="text" class="checkbox_video_dialog " id="input_video_preview">
				<input type="button" id="" class="button-image-select-video button-primary revblue" value="<?php echo $this->__('Image Library'); ?>">
				<input type="button" id="" class="button-image-select-video-default button-primary revblue" value="<?php echo $this->__('Video Thumbnail'); ?>">
				<input type="button" id="" class="button-image-remove-video button-primary revblue" value="<?php echo $this->__('Remove'); ?>">
				<div class="clear"></div>			
			</div>

			<div class="mb10">
                <label for="input_disable_on_mobile"><?php echo $this->__('Disable Video and Show<br>only Poster on Mobile:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_disable_on_mobile" >
			</div>

			<div class="mb10">
                <label for="input_use_poster_on_mobile"><?php echo $this->__('No Poster on Mobile:'); ?></label>
				<input type="checkbox" class="checkbox_video_dialog tp-moderncheckbox" id="input_use_poster_on_mobile" >
				<div style="width:100%;height:10px"></div>
			</div>
            <!--div class="mb10">
                <select id="select_mobile_setting">
                    <option value=""><?php echo $this->__('Show Video and Show Poster'); ?></option>posteronlyonmobile="off" && disablevideoonmobile="off"
                    <option value=""><?php echo $this->__('Show Video and Hide Poster'); ?></option>
                    <option value="use_poster_on_mobile"><?php echo $this->__('Hide Video and Show Poster'); ?></option>
                    <option value="disable_on_mobile"><?php echo $this->__('Hide Video and Hide Poster'); ?></option>
                </select>
            </div-->

		</div>

		<div id="rs-video-arguments" style="display:none">
			<div>
				<label><?php echo $this->__('Arguments:'); ?></label>
				<input type="text" id="input_video_arguments" style="width:350px;" value="" data-youtube="<?php echo RevSliderGlobals::DEFAULT_YOUTUBE_ARGUMENTS; ?>" data-vimeo="<?php echo RevSliderGlobals::DEFAULT_VIMEO_ARGUMENTS; ?>" >
			</div>
		</div>
		
		<div class="add-button-wrapper" style="margin-left:25px;">
			<a href="javascript:void(0)" class="button-primary revblue" id="button-video-add" data-textadd="<?php echo $this->__('Add This Video'); ?>" data-textupdate="<?php echo $this->__('Update Video'); ?>" ><?php echo $this->__('Add This Video'); ?></a>
            <a href="javascript:void(0)" class="button-primary revblue" style="display: none;" id="button-audio-add" data-textadd="<?php echo $this->__('Add This Audio'); ?>" data-textupdate="<?php echo $this->__('Update Audio'); ?>" ><?php echo $this->__('Add This Audio'); ?></a>
		</div>
	</form>
</div>