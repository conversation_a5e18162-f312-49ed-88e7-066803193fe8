<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php if ($_item = $this->getItem()): ?>
    <h5 class="title"><?php echo $_item->getName() ?></h5>
    <div><strong><?php echo $this->helper('sales')->__('SKU') ?>:</strong> <?php echo implode('<br />', Mage::helper('catalog')->splitSku($this->escapeHtml($this->getSku()))); ?></div>
    <?php if ($this->getOrderOptions()): ?>
        <dl class="item-options">
        <?php foreach ($this->getOrderOptions() as $_option): ?>
            <dt><?php echo $this->escapeHtml($_option['label']) ?></dt>
            <dd>
            <?php if (isset($_option['custom_view']) && $_option['custom_view']): ?>
                <?php echo $_option['value'];?>
            <?php else: ?>
                <?php echo Mage::helper('core/string')->truncate($_option['value'], 55, '', $_remainder);?>
                <?php if ($_remainder):?>
                    ... <span id="<?php echo $_id = 'id' . uniqid()?>"><?php echo $_remainder ?></span>
                    <script type="text/javascript">
                    $('<?php echo $_id ?>').hide();
                    $('<?php echo $_id ?>').up().observe('mouseover', function(){$('<?php echo $_id ?>').show();});
                    $('<?php echo $_id ?>').up().observe('mouseout',  function(){$('<?php echo $_id ?>').hide();});
                    </script>
                <?php endif;?>
            <?php endif;?>
            </dd>
        <?php endforeach; ?>
        </dl>
    <?php endif; ?>
    <?php if ($this->getLinks()): ?>
        <dl class="item-options">
            <dt><?php echo $this->escapeHtml($this->getLinksTitle()); ?></dt>
            <?php foreach ($this->getLinks()->getPurchasedItems() as $_link): ?>
                <dd><?php echo $this->escapeHtml($_link->getLinkTitle()); ?></dd>
            <?php endforeach; ?>
        </dl>
    <?php endif; ?>
    <?php echo $this->escapeHtml($_item->getDescription()) ?>
<?php endif; ?>
