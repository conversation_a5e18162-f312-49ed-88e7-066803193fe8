<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /* @var $this Mage_Downloadable_Block_Adminhtml_Catalog_Product_Composite_Fieldset_Downloadable */ ?>
<?php $_linksPurchasedSeparately = $this->getLinksPurchasedSeparately(); ?>
<?php $_skipSaleableCheck = Mage::helper('catalog/product')->getSkipSaleableCheck(); ?>
<?php if (($this->getProduct()->isSaleable() || $_skipSaleableCheck) && $this->hasLinks()):?>

<div id="catalog_product_composite_configure_fields_downloadable" class="<?php echo $this->getIsLastFieldset() ? 'last-fieldset' : '' ?>">
    <h4><?php echo Mage::helper('catalog')->__('Downloadable Information') ?></h4>
    <div class="product-options">
        <dl>
        <?php $_links = $this->getLinks(); ?>
        <?php $_isRequired = $this->getLinkSelectionRequired(); ?>
            <dt><label<?php if ($_isRequired) echo ' class="required"' ?>><?php if ($_isRequired) echo '<em>*</em>' ?><?php echo $this->escapeHtml($this->getLinksTitle()); ?></label></dt>
            <dd class="last">
                <ul id="downloadable-links-list" class="options-list">
                <?php foreach ($_links as $_link): ?>
                    <li>
                    <?php if ($_linksPurchasedSeparately): ?>
                        <input type="checkbox" class="checkbox<?php if ($_isRequired):?> validate-one-required-by-name<?php endif; ?> product-downloadable-link" name="links[]" id="links_<?php echo $_link->getId() ?>" value="<?php echo $_link->getId(); ?>" <?php echo $this->getLinkCheckedValue($_link); ?> price="<?php echo $this->getCurrencyPrice($_link->getPrice()); ?>"/>
                        <?php endif; ?>
                        <span class="label">
                        <label for="links_<?php echo $_link->getId() ?>">
                        <?php echo $this->escapeHtml($_link->getTitle()) ?>
                        </label>
                        <?php if ($_link->getSampleFile() || $_link->getSampleUrl()): ?>
                            &nbsp;(<a href="<?php echo $this->getLinkSamlpeUrl($_link) ?>" <?php echo $this->getIsOpenInNewWindow()?'onclick="this.target=\'_blank\'"':''; ?>><?php echo Mage::helper('downloadable')->__('sample') ?></a>)
                        <?php endif; ?>
                        <?php if ($_linksPurchasedSeparately): ?>
                        <?php echo $this->getFormattedLinkPrice($_link); ?>
                        <?php endif; ?>
                    </span>
                    <?php if ($_isRequired): ?>
                        <script type="text/javascript">
                            //<![CDATA[
                            $('links_<?php echo $_link->getId() ?>').advaiceContainer = 'links-advice-container';
                            $('links_<?php echo $_link->getId() ?>').callbackFunction = 'validateDownloadableCallback';
                            //]]>
                        </script>
                    <?php endif; ?>
                    </li>
                <?php endforeach; ?>
                </ul>

            <?php if ($_isRequired): ?>
                <span id="links-advice-container"></span>
            <?php endif;?>
            </dd>
        </dl>
    </div>
</div>

    <script type="text/javascript">
    //<![CDATA[
        validateDownloadableCallback = function (elmId, result) {
            var container = $('downloadable-links-list');
            if (result == 'failed') {
                container.removeClassName('validation-passed');
                container.addClassName('validation-failed');
            } else {
                container.removeClassName('validation-failed');
                container.addClassName('validation-passed');
            }
        }
    //]]>
    </script>
<?php endif;?>
