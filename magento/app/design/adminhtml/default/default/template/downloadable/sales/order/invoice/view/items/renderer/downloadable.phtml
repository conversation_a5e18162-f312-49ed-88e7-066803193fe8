<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php $_item = $this->getItem() ?>
<?php $this->setPriceDataObject($_item) ?>
<tr class="border">
    <td><?php echo $this->getColumnHtml($_item, 'downloadable') ?></td>
    <td class="a-right">

        <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceExclTax()): ?>
            <span class="price-excl-tax">
            <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
            <?php endif; ?>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales', $_item->getStoreId())): ?>
                    <?php
                    echo $this->displayPrices(
                        $_item->getBasePrice() + $_item->getBaseWeeeTaxAppliedAmount() + $_item->getBaseWeeeTaxDisposition(),
                        $_item->getPrice() + $_item->getWeeeTaxAppliedAmount() + $_item->getWeeeTaxDisposition()
                    );
                    ?>
                <?php else: ?>
                    <?php echo $this->displayPrices($_item->getBasePrice(), $_item->getPrice()) ?>
                <?php endif; ?>

                <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                    <br/>
                <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales', $_item->getStoreId())): ?>
                        <small>
                            <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                <span class="nobr"><?php echo $tax['title']; ?>
                                    : <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></span>
                            <?php endforeach; ?>
                        </small>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><small><?php echo $tax['title']; ?>
                                    : <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></small></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales', $_item->getStoreId())): ?>
                        <small>
                            <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                <span class="nobr"><?php echo $tax['title']; ?>
                                    : <?php echo $this->displayPrices($tax['base_amount'], $tax['amount']); ?></span>
                            <?php endforeach; ?>
                        </small>
                    <?php endif; ?>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <br/>
                        <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br/>
                            <?php
                            echo $this->displayPrices(
                                $_item->getBasePrice() + $_item->getBaseWeeeTaxAppliedAmount() + $_item->getBaseWeeeTaxDisposition(),
                                $_item->getPrice() + $_item->getWeeeTaxAppliedAmount() + $_item->getWeeeTaxDisposition()
                            );
                            ?>
                    </span>
                    <?php endif; ?>
                <?php endif; ?>
            </span>
            <br/>
        <?php endif; ?>

        <?php if ($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
            <span class="price-incl-tax">
            <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
                <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax'); ?>:</span>
            <?php endif; ?>
                <?php $weeeTaxInclTax = Mage::helper('weee')->getWeeeTaxInclTax($_item); ?>
                <?php if ($weeeTaxInclTax && Mage::helper('weee')->typeOfDisplay($_item, 0, 'sales')): // including ?>
                    <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getPriceInclTax($_item) + $weeeTaxInclTax); ?>
                <?php elseif ($weeeTaxInclTax && Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): // incl. + weee ?>
                    <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getPriceInclTax($_item) + $weeeTaxInclTax); ?>
                    <br/>
                    <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>
                                : <?php echo Mage::helper('core')->currency($tax['amount_incl_tax'], true, true); ?></span>
                            <br/>
                        <?php endforeach; ?>
                    </small>
                <?php elseif ($weeeTaxInclTax && Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): // excl. + weee + final ?>
                    <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getPriceInclTax($_item)); ?>
                    <br/>
                <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                        <span class="nobr"><small><?php echo $tax['title']; ?>
                                : <?php echo Mage::helper('core')->currency($tax['amount_incl_tax'], true, true); ?></small></span>
                        <br/>
                    <?php endforeach; ?>
                <span class="nobr">
                    <?php echo Mage::helper('weee')->__('Total'); ?>:
                        <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getPriceInclTax($_item) + $weeeTaxInclTax); ?>
                </span>
                <?php else: // excl. ?>
                    <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getPriceInclTax($_item)); ?>
                <?php endif; ?>
            </span>
        <?php endif; ?>
    </td>
    <td class="a-center"><?php echo $_item->getQty() * 1 ?></td>
    <td class="a-right">
        <?php if ($this->helper('tax')->displaySalesBothPrices() || $this->helper('tax')->displaySalesPriceExclTax()): ?>
            <span class="price-excl-tax">
                <?php if ($this->helper('tax')->displaySalesBothPrices()): ?>
                    <span class="label"><?php echo $this->__('Excl. Tax'); ?>:</span>
                <?php endif; ?>

                <?php if (Mage::helper('weee')->typeOfDisplay($_item, array(0, 1, 4), 'sales', $_item->getStoreId())): ?>
                    <?php
                    echo $this->displayPrices(
                        $_item->getBaseRowTotal() + $_item->getBaseWeeeTaxAppliedRowAmount() + $_item->getBaseWeeeTaxRowDisposition(),
                        $_item->getRowTotal() + $_item->getWeeeTaxAppliedRowAmount() + $_item->getWeeeTaxRowDisposition()
                    );
                    ?>
                <?php else: ?>
                    <?php echo $this->displayPrices($_item->getBaseRowTotal(), $_item->getRowTotal()) ?>
                <?php endif; ?>

                <?php if (Mage::helper('weee')->getApplied($_item)): ?>
                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales', $_item->getStoreId())): ?>
                        <small>
                            <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                <span class="nobr"><?php echo $tax['title']; ?>
                                    : <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></span>
                            <?php endforeach; ?>
                        </small>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><small><?php echo $tax['title']; ?>
                                    : <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></small></span>
                        <?php endforeach; ?>
                    <?php elseif (Mage::helper('weee')->typeOfDisplay($_item, 4, 'sales', $_item->getStoreId())): ?>
                        <small>
                            <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                                <span class="nobr"><?php echo $tax['title']; ?>
                                    : <?php echo $this->displayPrices($tax['base_row_amount'], $tax['row_amount']); ?></span>
                            <?php endforeach; ?>
                        </small>
                    <?php endif; ?>

                    <?php if (Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales', $_item->getStoreId())): ?>
                        <br/>
                        <span class="nobr"><?php echo Mage::helper('weee')->__('Total'); ?>:<br/>
                            <?php
                            echo $this->displayPrices(
                                $_item->getBaseRowTotal() + $_item->getBaseWeeeTaxAppliedRowAmount() + $_item->getBaseWeeeTaxRowDisposition(),
                                $_item->getRowTotal() + $_item->getWeeeTaxAppliedRowAmount() + $_item->getWeeeTaxRowDisposition()
                            );
                            ?>
                        </span>
                    <?php endif; ?>
                <?php endif; ?>
            </span>
            <br/>
        <?php endif; ?>

        <?php if ($this->helper('tax')->displayCartPriceInclTax() || $this->helper('tax')->displayCartBothPrices()): ?>
            <span class="price-incl-tax">
                <?php if ($this->helper('tax')->displayCartBothPrices()): ?>
                    <span class="label"><?php echo $this->helper('tax')->__('Incl. Tax'); ?>:</span>
                <?php endif; ?>
                <?php $rowWeeeTaxInclTax = Mage::helper('weee')->getRowWeeeTaxInclTax($_item); ?>
                <?php if ($rowWeeeTaxInclTax && Mage::helper('weee')->typeOfDisplay($_item, 0, 'sales')): // including ?>
                    <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getSubtotalInclTax($_item) + $rowWeeeTaxInclTax); ?>
                <?php elseif ($rowWeeeTaxInclTax && Mage::helper('weee')->typeOfDisplay($_item, 1, 'sales')): // incl. + weee ?>
                    <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getSubtotalInclTax($_item) + $rowWeeeTaxInclTax); ?>
                    <br/>
                    <small>
                        <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                            <span class="nobr"><?php echo $tax['title']; ?>
                                : <?php echo Mage::helper('core')->currency($tax['row_amount_incl_tax'], true, true); ?></span>
                            <br/>
                        <?php endforeach; ?>
                    </small>
                <?php elseif ($rowWeeeTaxInclTax && Mage::helper('weee')->typeOfDisplay($_item, 2, 'sales')): // excl. + weee + final ?>
                    <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getSubtotalInclTax($_item)); ?>
                    <br/>
                    <?php foreach (Mage::helper('weee')->getApplied($_item) as $tax): ?>
                        <span class="nobr"><small><?php echo $tax['title']; ?>
                                : <?php echo Mage::helper('core')->currency($tax['row_amount_incl_tax'], true, true); ?></small></span>
                        <br/>
                    <?php endforeach; ?>
                  <span class="nobr">
                    <?php echo Mage::helper('weee')->__('Total'); ?>:
                        <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getSubtotalInclTax($_item) + $rowWeeeTaxInclTax); ?>
                  </span>
                <?php else: // excl. ?>
                    <?php echo $this->helper('checkout')->formatPrice($this->helper('checkout')->getSubtotalInclTax($_item)); ?>
                <?php endif; ?>
            </span>
        <?php endif; ?>
    </td>
    <td class="a-right"><?php echo $this->displayPriceAttribute('tax_amount') ?></td>
    <td class="a-right"><?php echo $this->displayPriceAttribute('discount_amount') ?></td>
    <td class="a-right last">
        <?php echo $this->displayPrices(
            $_item->getBaseRowTotal() + $_item->getBaseTaxAmount() - $_item->getBaseDiscountAmount() + $_item->getBaseHiddenTaxAmount() + $_item->getBaseWeeeTaxAppliedRowAmount(),
            $_item->getRowTotal() + $_item->getTaxAmount() - $_item->getDiscountAmount() + $_item->getHiddenTaxAmount() + $_item->getWeeeTaxAppliedRowAmount()
        ) ?>
    </td>
</tr>
