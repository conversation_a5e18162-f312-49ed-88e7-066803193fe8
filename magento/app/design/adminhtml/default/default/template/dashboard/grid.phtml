<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php

$numColumns = sizeof($this->getColumns());
?>
<?php if($this->getCollection()): ?>
<div class="grid np">
<table cellspacing="0" style="border:0;" id="<?php echo $this->getId() ?>_table">
    <?php foreach ($this->getColumns() as $_column): ?>
    <col <?php echo $_column->getHtmlProperty() ?> />
    <?php endforeach; ?>
    <?php if ($this->getHeadersVisibility() || $this->getFilterVisibility()): ?>
        <thead>
            <?php if ($this->getHeadersVisibility()): ?>
                <tr class="headings">
                <?php foreach ($this->getColumns() as $_column): ?>
                    <th <?php echo $_column->getHeaderHtmlProperty() ?>><span class="nobr"><?php echo $_column->getHeaderHtml() ?></span></th>
                <?php endforeach; ?>
                </tr>
            <?php endif; ?>
        </thead>
    <?php endif; ?>
    <tbody>
    <?php if (($this->getCollection()->getSize()>0) && (!$this->getIsCollapsed())): ?>
    <?php foreach ($this->getCollection() as $_index=>$_item): ?>
        <tr title="<?php echo $this->getRowId($_item) ?>">
        <?php $i=0;foreach ($this->getColumns() as $_column): ?>
            <td class="<?php echo $_column->getCssProperty() ?> <?php echo ++$i==$numColumns?'last':'' ?>"><?php echo (($_html = $_column->getRowField($_item)) != '' ? $_html : '&nbsp;') ?></td>
        <?php endforeach; ?>
        </tr>
    <?php endforeach; ?>
    <?php elseif ($this->getEmptyText()): ?>
        <tr>
            <td class="empty-text <?php echo $this->getEmptyTextClass() ?>" colspan="100"><?php echo $this->getEmptyText() ?></td>
        </tr>
    <?php endif; ?>
    </tbody>
</table>
</div>
<?php if($this->canDisplayContainer()): ?>
<script type="text/javascript">
//<![CDATA[
    <?php echo $this->getJsObjectName() ?> = new varienGrid('<?php echo $this->getId() ?>', '<?php echo $this->getGridUrl() ?>', '<?php echo $this->getVarNamePage() ?>', '<?php echo $this->getVarNameSort() ?>', '<?php echo $this->getVarNameDir() ?>', '<?php echo $this->getVarNameFilter() ?>');
    <?php echo $this->getJsObjectName() ?>.useAjax = '<?php echo $this->getUseAjax() ?>';
    <?php if($this->getRowClickCallback()): ?>
        <?php echo $this->getJsObjectName() ?>.rowClickCallback = <?php echo $this->getRowClickCallback() ?>;
    <?php endif; ?>
    <?php if($this->getCheckboxCheckCallback()): ?>
        <?php echo $this->getJsObjectName() ?>.checkboxCheckCallback = <?php echo $this->getCheckboxCheckCallback() ?>;
    <?php endif; ?>
    <?php if($this->getRowInitCallback()): ?>
        <?php echo $this->getJsObjectName() ?>.initRowCallback = <?php echo $this->getRowInitCallback() ?>;
        <?php echo $this->getJsObjectName() ?>.rows.each(function(row){<?php echo $this->getRowInitCallback() ?>(<?php echo $this->getJsObjectName() ?>, row)});
    <?php endif; ?>
    <?php if($this->getMassactionBlock()->isAvailable()): ?>
    <?php echo $this->getMassactionBlock()->getJavaScript() ?>
    <?php endif ?>
//]]>
</script>
<?php endif; ?>
<?php endif ?>
