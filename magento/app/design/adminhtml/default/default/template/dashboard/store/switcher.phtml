<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<p class="switcher"><label for="store_switcher"><?php echo $this->__('View Statistics For:') ?></label>
<?php echo $this->getHintHtml() ?>
<select name="store_switcher" id="store_switcher" class="left-col-block" onchange="return switchStore(this);">
    <option value=""><?php echo $this->__('All Websites') ?></option>
    <?php foreach ($this->getWebsiteCollection() as $_website): ?>
        <?php $showWebsite=false; ?>
        <?php foreach ($this->getGroupCollection($_website) as $_group): ?>
            <?php $showGroup=false; ?>
            <?php foreach ($this->getStoreCollection($_group) as $_store): ?>
                <?php if ($showWebsite == false): ?>
                    <?php $showWebsite = true; ?>
                    <option website="true" value="<?php echo $_website->getId() ?>"<?php if($this->getRequest()->getParam('website') == $_website->getId()): ?> selected="selected"<?php endif; ?>><?php echo $this->escapeHtml($_website->getName()); ?></option>
                <?php endif; ?>
                <?php if ($showGroup == false): ?>
                    <?php $showGroup = true; ?>
                    <!--optgroup label="&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($_group->getName()); ?>"-->
                    <option group="true" value="<?php echo $_group->getId() ?>"<?php if($this->getRequest()->getParam('group') == $_group->getId()): ?> selected="selected"<?php endif; ?>>&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($_group->getName()); ?></option>
                <?php endif; ?>
                <option value="<?php echo $_store->getId() ?>"<?php if($this->getStoreId() == $_store->getId()): ?> selected="selected"<?php endif; ?>>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $this->escapeHtml($_store->getName()); ?></option>
            <?php endforeach; ?>
            <?php if ($showGroup): ?>
                <!--</optgroup>-->
            <?php endif; ?>
        <?php endforeach; ?>
    <?php endforeach; ?>
</select>
</p>
<script type="text/javascript">
//<![CDATA[
    function switchStore(obj){
        if (obj.options[obj.selectedIndex].getAttribute('website') == 'true') {
            var selectionType = 'website';
        } else if (obj.options[obj.selectedIndex].getAttribute('group') == 'true') {
            var selectionType = 'group';
        } else {
            var selectionType = 'store';
        }
        var storeParam = obj.value ? selectionType + '/' + obj.value + '/' : '';
        if(obj.switchParams){
            storeParam+= obj.switchParams;
        }
        if ($('diagram_tab_orders_content').style.display != 'none') {
            var select = $('order_orders_period');
        } else if ($('diagram_tab_amounts_content').style.display != 'none') {
            var select = $('order_amounts_period');
        }
        var periodParam = select.value ? 'period/'+select.value + '/' : '';
        setLocation('<?php echo $this->getSwitchUrl() ?>'+storeParam+periodParam);
    }
//]]>
</script>
