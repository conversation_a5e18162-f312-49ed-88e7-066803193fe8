<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php /* <table class="dynamic-grid" cellspacing="0" id="tax-rate-titles-table"> */ ?>
    <tr class="dynamic-grid">
    <?php foreach ($this->getStores() as $_store): ?>
        <th><?php echo $this->escapeHtml($_store->getName()); ?></th>
    <?php endforeach; ?>
    </tr>
    <tr class="dynamic-grid">
        <?php $_labels = $this->getTitles() ?>
        <?php foreach ($this->getStores() as $_store): ?>
        <td>
            <input class="input-text<?php if($_store->getId()==0): ?> required-entry<?php endif; ?>" type="text" name="title[<?php echo $_store->getId() ?>]" value="<?php echo $_labels[$_store->getId()] ?>" />
        </td>
        <?php endforeach; ?>
    </tr>
    <tr class="dynamic-grid">
        <td colspan="<?php echo count($this->getStores()); ?>" class="tax-rate-titles-note-td">
            <p><strong><?php echo $this->__('Note:'); ?></strong> <?php echo $this->__('Leave empty to use tax identifier'); ?></p>
        </td>
    </tr>
<?php /* </table> */ ?>
