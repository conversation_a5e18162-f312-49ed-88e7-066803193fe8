<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2021 Amasty (https://www.amasty.com)
 * @package Amasty_Base
 */
?>
<?php
/**
 * @var Amasty_Base_Block_Extensions $this
 */
$modules = $this->getModuleList();
?>
<div class="ambase-updates-block">
    <div class="ambase-updates-container">
        <div class="ambase-btns-block">
            <a href="https://amasty.com/extupdates/account/downloads/<?= $this->escapeUrl($this->getSeoparams()) ?>"
               class="ambase-btn -orange"
               target="_blank"
               title="<?= $this->escapeHtml($this->__('Get Updates at Your Amasty.com Account')) ?>">
                <?= $this->escapeHtml($this->__('Get Updates at Your')) ?>
                <span class="_strong"><?= $this->escapeHtml($this->__('Amasty.com')) ?></span>
                <?= $this->escapeHtml($this->__('Account')) ?>
            </a>
            <a href="https://amasty.com/knowledge-base/upgrade-magento-extension.html<?= $this->escapeUrl($this->getSeoparams()) ?>"
               class="ambase-btn -blue"
               target="_blank"
               title="<?= $this->escapeHtml($this->__('How to Update?')) ?>">
                <?= $this->escapeHtml($this->__('How to Update?')) ?>
            </a>
        </div>
        <div class="ambase-modules-block">
            <?php foreach ($modules['hasUpdate'] as $module): ?>
                <div class="ambase-module-container -update">
                    <div class="ambase-title-container">
                        <?php if (array_key_exists('url', $module)): ?>
                            <a href="<?= $this->escapeUrl($module['url']) . $this->escapeUrl($this->getSeoparams()) ?>"
                               class="ambase-title -link"
                               target="_blank"
                               title="<?= $this->escapeHtml($this->__($module['description'])) ?>">
                                <?= $this->escapeHtml($this->__($module['description'])) ?>
                            </a>
                        <?php else: ?>
                            <h3 class="ambase-title"><?= $this->escapeHtml($this->__($module['description'])) ?></h3>
                        <?php endif ?>
                    </div>
                    <div class="ambase-version-block">
                        <div class="ambase-version-container">
                            <p class="ambase-version"><?= $this->escapeHtml($this->__($module['version'])) ?></p>
                            <div class="ambase-svg -arrow"></div>
                            <p class="ambase-version"><?= $this->escapeHtml($this->__($module['lastVersion'])) ?></p>
                        </div>
                        <?php if (array_key_exists('url', $module)): ?>
                            <a href="<?= $this->escapeUrl($module['url']) . $this->escapeUrl($this->getSeoparams()) . '#changelog' ?>"
                               class="ambase-link"
                               target="_blank"
                               title="<?= $this->escapeHtml($this->__('See what\'s new')) ?>">
                                <?= $this->escapeHtml($this->__('See what\'s new')) ?>
                            </a>
                        <?php endif ?>
                        <?php if (array_key_exists('disabled', $module)): ?>
                            <p class="ambase-warning"><?= $this->escapeHtml($this->__("Output disabled")) ?></p>
                        <?php endif ?>
                        <?php if ($this->isMigrationModule($module)): ?>
                            <br/>
                            <a class="line-upper" href="<?= $this->escapeUrl($this->generateMigrationUrl($module['description']))?>">
                                <?= $this->escapeHtml($this->__('Data Migration Service')) ?>
                            </a>
                            <br/>
                            <a class="line-lower" href="<?= $this->escapeUrl($this->generateMigrationUrl($module['description']))?>">
                                <?= $this->escapeHtml($this->__('is available')) ?>
                            </a>
                        <?php endif ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>
<div class="ambase-updates-block -last">
    <h2 class="ambase-title -small"><?= $this->escapeHtml($this->__('Up-to-Date Amasty Extensions')) ?></h2>
    <div class="ambase-modules-block">
        <?php foreach ($modules['lastVersion'] as $module): ?>
            <?php if (array_key_exists('url', $module)): ?>
                <a href="<?= $this->escapeUrl($module['url']) . $this->escapeUrl($this->getSeoparams()) ?>"
                   class="ambase-module-container -link"
                   target="_blank"
                   title="<?= $this->escapeHtml($this->__($module['description'])) ?>">
                <span class="ambase-title">
                    <?= $this->escapeHtml($this->__($module['description'])) ?>
                </span>
                    <span class="ambase-version-block">
                    <span class="ambase-version-container">
                        <span class="ambase-svg"></span>
                        <span class="ambase-version"><?= $this->escapeHtml($this->__($module['version'])) ?></span>
                    </span>
                        <?php if ($this->isMigrationModule($module)): ?>
                            <span class="line-upper" onclick="window.open(
                                    '<?= $this->escapeUrl($this->generateMigrationUrl($module['description']))?>',
                                    '_blank'
                                    );">
                           <?= $this->escapeHtml($this->__('Data Migration Service')) ?>
                        </span>
                            <br/>
                            <span class="line-lower" onclick="window.open(
                                    '<?= $this->escapeUrl($this->generateMigrationUrl($module['description']))?>',
                                    '_blank'
                                    );">
                            <?= $this->escapeHtml($this->__('is available')) ?>
                        </span>
                        <?php endif ?>
                        <?php if (array_key_exists('date', $module)): ?>
                            <span class="ambase-date"><?= $this->escapeHtml($this->__($module['date'])) ?></span>
                        <?php endif; ?>
                        <?php if (array_key_exists('disabled', $module)): ?>
                            <span class="ambase-warning"><?= $this->escapeHtml($this->__("Output disabled")) ?></span>
                        <?php endif ?>
                </span>
                </a>
            <?php else: ?>
                <div class="ambase-module-container">
                    <div class="ambase-title-container">
                        <h3 class="ambase-title">
                            <?= $this->escapeHtml($this->__($module['description'])) ?>
                        </h3>
                    </div>
                    <div class="ambase-version-block">
                        <div class="ambase-version-container">
                            <span class="ambase-svg"></span>
                            <p class="ambase-version">
                                <?= $this->escapeHtml($this->__($module['version'])) ?>
                            </p>
                        </div>
                        <?php if (array_key_exists('date', $module)): ?>
                            <p class="ambase-date">
                                <?= $this->escapeHtml($this->__($module['date'])) ?>
                            </p>
                        <?php endif; ?>
                        <?php if (array_key_exists('disabled', $module)): ?>
                            <p class="ambase-warning"><?= $this->escapeHtml($this->__("Output disabled")) ?></p>
                        <?php endif ?>
                    </div>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
</div>
