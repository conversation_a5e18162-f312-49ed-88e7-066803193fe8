<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2018 Amasty (https://www.amasty.com)
 * @package Amasty_Base
 */
?>
<?php
if ($this->isNewVersionAvailable())
{
    
    $closeUrl = $this->getCloseUrl();
    $unsubscribeUrl = $this->getUnsubscribeUrl();
?>
    <div id="amasty-promo-notification">
        <ul class="messages">
            <li class="notice-msg">
                <ul>
                    <li>
                        
                        <?php echo $this->__("New version %s is available for the %s by Amasty", $this->getLatestVersion(), $this->getModuleTitle())?>.&nbsp;<?php echo $this->__("See")?>&nbsp;<a href="<?php echo $this->getModuleLink();?>" onclick="this.target='_blank';"><?php echo $this->__("change-log")?></a>

                        <span class="f-right">
                            <a href="#" onclick="Effect.toggle('amasty-promo-notification', 'blind', { duration: 0.1 });new Ajax.Request('<?php echo $closeUrl;?>', {
                                onCreate: function(request) {
                                    Ajax.Responders.unregister(varienLoaderHandler.handler);
                                },
                                onSuccess: function(transport) { 
                                    Ajax.Responders.register(varienLoaderHandler.handler);
                                }
                            }); return false;"><?php echo $this->__("Close")?></a>&nbsp;|&nbsp;<a style="color: #A3A3A3;" href="<?php echo $unsubscribeUrl;?>"><?php echo $this->__("Unsubscribe")?></a>
                         </span>
                        
                    </li>
                </ul>
            </li>
        </ul>
    </div>
<?php
}
?>