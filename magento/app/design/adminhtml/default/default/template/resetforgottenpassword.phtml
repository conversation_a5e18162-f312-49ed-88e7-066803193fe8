<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html lang="en">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <meta name="robots" content="noindex, nofollow" />
        <title><?php echo Mage::helper('adminhtml')->__('Reset a Password'); ?></title>
        <link type="text/css" rel="stylesheet" href="<?php echo $this->getSkinUrl('reset.css'); ?>" media="all" />
        <link type="text/css" rel="stylesheet" href="<?php echo $this->getSkinUrl('boxes.css'); ?>" media="all" />
        <link rel="icon" href="<?php echo $this->getSkinUrl('favicon.ico'); ?>" type="image/x-icon" />
        <link rel="shortcut icon" href="<?php echo $this->getSkinUrl('favicon.ico'); ?>" type="image/x-icon" />

        <script type="text/javascript" src="<?php echo $this->getJsUrl() ?>index.php/x.js?f=prototype/prototype.js,prototype/validation.js,mage/adminhtml/events.js,mage/adminhtml/form.js,scriptaculous/effects.js"></script>

    <!--[if IE]> <link rel="stylesheet" href="<?php echo $this->getSkinUrl('iestyles.css'); ?>" type="text/css" media="all" /> <![endif]-->
    <!--[if lt IE 7]> <link rel="stylesheet" href="<?php echo $this->getSkinUrl('below_ie7.css'); ?>" type="text/css" media="all" /> <![endif]-->
    <!--[if IE 7]> <link rel="stylesheet" href="<?php echo $this->getSkinUrl('ie7.css'); ?>" type="text/css" media="all" /> <![endif]-->
    </head>
    <body id="page-login">
        <div class="login-container">
            <div class="login-box">
                <form method="post" action="<?php echo $this->getUrl('*/*/resetpasswordpost', array('_query' => array('id' => $userId, 'token' => $resetPasswordLinkToken))); ?>" id="reset-password-form">
                    <fieldset class="login-form">
                        <input name="form_key" type="hidden" value="<?php echo $this->getFormKey(); ?>" />
                        <h2><?php echo Mage::helper('adminhtml')->__('Reset a Password'); ?></h2>
                        <div id="messages">
                            <?php echo $this->getMessagesBlock()->toHtml(); ?>
                        </div>
                        <div class="input-box f-left half">
                            <label for="password"><em class="required">*</em> <?php echo $this->__('New Password'); ?></label>
                            <br />
                            <!-- This is a dummy hidden field to trick firefox from auto filling the password -->
                            <input type="password" class="input-text no-display" name="dummy" id="dummy" />
                            <input type="password"
                                   class="input-text required-entry validate-admin-password min-admin-pass-length-<?php echo $minAdminPasswordLength; ?>"
                                   name="password"
                                   id="password"
                                   autocomplete="new-password"/>
                            <p class="note">
                                <span>
                                    <?php echo Mage::helper('adminhtml')->__('Password must be at least of %d characters.', $minAdminPasswordLength); ?>
                                </span>
                            </p>
                        </div>
                        <div class="input-box f-right">
                            <label for="confirmation"><em class="required">*</em> <?php echo $this->__('Confirm New Password'); ?></label>
                            <br />
                            <!-- This is a dummy hidden field to trick firefox from auto filling the password -->
                            <input type="password" class="input-text no-display" name="dummy" id="dummy" />
                            <input type="password" class="input-text required-entry validate-cpassword" name="confirmation" id="confirmation" autocomplete="new-password"/>
                        </div>
                        <div class="clear"></div>
                        <div class="form-buttons">
                            <a class="left" href="<?php echo $this->getUrl('adminhtml', array('_nosecret' => true)) ?>">&laquo; <?php echo Mage::helper('adminhtml')->__('Back to Login'); ?></a>
                            <button type="submit" title="<?php echo Mage::helper('core')->quoteEscape(Mage::helper('adminhtml')->__('Reset Password')); ?>" class="forgot-password"><span><span><span><?php echo Mage::helper('adminhtml')->__('Reset Password'); ?></span></span></span></button>
                        </div>
                    </fieldset>
                    <p class="legal"><?php echo Mage::helper('adminhtml')->__('Magento is a trademark of Magento Inc. Copyright &copy; %s Magento Inc.', date('Y')); ?></p>
                </form>
                <div class="bottom"></div>
                <script type="text/javascript">
                    var resetPasswordForm = new varienForm('reset-password-form');
                </script>
            </div>
        </div>
    </body>
</html>
