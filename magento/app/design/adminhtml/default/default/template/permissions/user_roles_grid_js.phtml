<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<script type="text/javascript">
<!--
<?php $myBlock = $this->getLayout()->getBlock('user.roles.grid'); ?>
<?php if( is_object($myBlock) && $myBlock->getJsObjectName()): ?>
    var radioBoxes = $H({});
    var warning = false;
    var userRoles = $H(<?php echo $myBlock->_getSelectedRoles(true) ?>);
    if (userRoles.size() > 0) warning = true;
    $('user_user_roles').value = userRoles.toQueryString();

    function registerUserRole(grid, element, checked){
        if(checked){
            userRoles.each(function(o){userRoles.remove(o[0]);});
            userRoles[element.value] = 0;
        } else {
            userRoles.remove(element.value);
        }
        $('user_user_roles').value = userRoles.toQueryString();
        grid.reloadParams = {'user_roles[]':userRoles.keys()};
    }

    function roleRowClick(grid, event){
        var trElement = Event.findElement(event, 'tr');
        var isInput   = Event.element(event).tagName == 'INPUT';
        if(trElement){
            var checkbox = Element.getElementsBySelector(trElement, 'input');
            if(checkbox[0] && !checkbox[0].checked){
                var checked = isInput ? checkbox[0].checked : !checkbox[0].checked;
                if (checked && warning && radioBoxes.size() > 0) {
                    if ( !confirm('<?php echo Mage::helper('core')->jsQuoteEscape($this->__('Warning!\r\nThis action will remove this user from already assigned role\r\nAre you sure?')) ?>') ) {
                        checkbox[0].checked = false;
                        for(i in radioBoxes) {
                            if( radioBoxes[i].status == 1) {
                                radioBoxes[i].object.checked = true;
                            }
                        }
                        return false;
                    }
                    warning = false;
                }
                <?php echo $myBlock->getJsObjectName() ?>.setCheckboxChecked(checkbox[0], checked);
            }
        }
    }

    function rolesRowInit(grid, row){
        var checkbox = $(row).getElementsByClassName('radio')[0];
        if (checkbox) {
            radioBoxes[checkbox.value] = {'status' : ((checkbox.checked) ? 1 : 0), 'object' : checkbox};
        }
    }

<?php echo $myBlock->getJsObjectName() ?>.rowClickCallback = roleRowClick;
<?php echo $myBlock->getJsObjectName() ?>.initRowCallback = rolesRowInit;
<?php echo $myBlock->getJsObjectName() ?>.checkboxCheckCallback = registerUserRole;
<?php echo $myBlock->getJsObjectName() ?>.rows.each(function(row){rolesRowInit(<?php echo $myBlock->getJsObjectName() ?>, row)});
<?php endif; ?>
//-->
</script>
