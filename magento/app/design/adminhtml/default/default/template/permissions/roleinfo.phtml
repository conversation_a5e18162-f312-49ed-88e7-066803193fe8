<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="content-header">
    <table cellspacing="0">
        <tr>
            <td style="width:50%;"><h3 class="icon-head head-permissions-role"><?php echo ($this->getRoleId() > 0 ) ? ($this->__('Edit Role') . " '{$this->escapeHtml($this->getRoleInfo()->getRoleName())}'") : $this->__('Add New Role') ?></h3></td>
            <td class="form-buttons">
                <?php echo $this->getBackButtonHtml() ?>
                <?php echo $this->getResetButtonHtml() ?>
                <?php echo $this->getDeleteButtonHtml() ?>
                <?php echo $this->getSaveButtonHtml() ?>
            </td>
        </tr>
    </table>
</div>
<form action="<?php echo $this->getUrl('*/*/saverole') ?>" method="post" id="role_edit_form">
    <?php echo $this->getBlockHtml('formkey')?>
</form>
<script type="text/javascript">
    var roleForm = new varienForm('role_edit_form');
</script>
