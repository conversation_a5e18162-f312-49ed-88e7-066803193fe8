<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php echo $this->getGridParentHtml() ?>
<?php if($this->canDisplayContainer()): ?>
<script type="text/javascript">
//<!--
wishlistControl = {
    reload: function (urlParams) {
        if (!urlParams) {
            urlParams = '';
        }
        var url = <?php echo $this->getJsObjectName() ?>.url + '?ajax=true' + urlParams;
        new Ajax.Updater(
            <?php echo $this->getJsObjectName() ?>.containerId,
            url,
            {
                parameters: {form_key: FORM_KEY},
                onComplete: <?php echo $this->getJsObjectName() ?>.initGrid.bind(<?php echo $this->getJsObjectName() ?>),
                evalScripts:true
            }
        );
    },

    configureItem: function (itemId) {
        productConfigure.setOnLoadIFrameCallback('wishlist', this.cbOnLoadIframe.bind(this));
        productConfigure.showItemConfiguration('wishlist', itemId);
        return false;
    },

    cbOnLoadIframe: function (response) {
        if (!response.ok) {
            return;
        }
        this.reload();
    },

    removeItem: function (itemId) {
        if(!confirm('<?php echo Mage::helper('core')->jsQuoteEscape(Mage::helper('customer')->__('Are you sure that you want to remove this item?')) ?>')) {
            return false;
        }
        this.reload('&delete=' + itemId);
        return false;
    }
}

productConfigure.addListType(
    'wishlist',
    {
        urlFetch: '<?php echo $this->getUrl('*/customer_wishlist_product_composite_wishlist/configure') ?>',
        urlConfirm: '<?php echo $this->getUrl('*/customer_wishlist_product_composite_wishlist/update') ?>'
    }
);
//-->
</script>
<?php endif ?>
