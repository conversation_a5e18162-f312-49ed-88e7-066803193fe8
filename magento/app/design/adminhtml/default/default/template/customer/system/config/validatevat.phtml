<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<?php
/**
 * @see Mage_Adminhtml_Block_Customer_System_Config_Validatevat
 */
?>
<script type="text/javascript">
//<![CDATA[
    function validateVat() {
        var elem = $('<?php echo $this->getHtmlId() ?>');

        params = {
            country: $('general_store_information_merchant_country').value,
            vat: $('general_store_information_merchant_vat_number').value
        };

        new Ajax.Request('<?php echo $this->getAjaxUrl() ?>', {
            parameters: params,
            onSuccess: function(response) {
                result = '<?php echo Mage::helper('core')->jsQuoteEscape($this->__('VAT Number is Invalid')) ?>';
                try {
                    response = response.responseText;
                    if (response == 1) {
                        result = '<?php echo Mage::helper('core')->jsQuoteEscape($this->__('VAT Number is Valid')) ?>';
                        elem.removeClassName('fail').addClassName('success')
                    } else {
                        elem.removeClassName('success').addClassName('fail')
                    }
                } catch (e) {
                    elem.removeClassName('success').addClassName('fail')
                }
                $('validation_result').update(result);
            }
        });
    }
//]]>
</script>
<button onclick="javascript:validateVat(); return false;" class="scalable" type="button" id="<?php echo $this->getHtmlId() ?>">
    <span id="validation_result"><?php echo $this->escapeHtml($this->getButtonLabel()) ?></span>
</button>
