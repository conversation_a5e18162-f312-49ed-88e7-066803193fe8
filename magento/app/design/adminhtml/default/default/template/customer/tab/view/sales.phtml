<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>
<div class="entry-edit">
    <div class="entry-edit-head"><h4 class="icon-head head-customer-sales-statistics"><?php echo Mage::helper('customer')->__('Sales Statistics') ?></h4></div>
    <div class="grid">
        <table cellspacing="0" class="data">
            <thead>
                <tr class="headings">
                    <th><?php echo Mage::helper('customer')->__('Website') ?></th>
                    <th><?php echo Mage::helper('customer')->__('Store') ?></th>
                    <th><?php echo Mage::helper('customer')->__('Store View') ?></th>
                    <th><?php echo Mage::helper('customer')->__('Lifetime Sales') ?></th>
                    <th><?php echo Mage::helper('customer')->__('Average Sale') ?></th>
                </tr>
            </thead>
            <tfoot>
                <tr>
                    <td colspan="3" class="label"><strong><big><?php echo Mage::helper('customer')->__('All Store Views') ?></big></strong></td>
                    <td class="emph"><strong><big><?php echo $this->formatCurrency($this->getTotals()->getBaseLifetime()) ?></big></strong></td>
                    <td class="emph"><strong><big><?php echo $this->formatCurrency($this->getTotals()->getAvgsale()) ?></big></strong></td>
                </tr>
            </tfoot>
            <?php if ($this->getRows()): ?>
            <tbody>
                <?php $_i = 0; ?>
                <?php foreach ($this->getRows() as $_websiteId => $_groups): ?>
                    <?php $_websiteRow = false; ?>
                    <?php foreach ($_groups as $_groupId => $_stores): ?>
                        <?php $_groupRow = false; ?>
                        <?php foreach ($_stores as $_row): ?>
                        <?php if ($_row->getStoreId() == 0): ?>
                        <td colspan="3" class="label"><?php echo $this->escapeHtml($_row->getStoreName()); ?></td>
                        <?php else: ?>
                <tr<?php echo ($_i++ % 2 ? ' class="even"' : '') ?>>
                        <?php if (!$_websiteRow): ?>
                    <td rowspan="<?php echo $this->getWebsiteCount($_websiteId) ?>"><?php echo $this->escapeHtml($_row->getWebsiteName()); ?></td>
                            <?php $_websiteRow = true; ?>
                        <?php endif; ?>
                        <?php if (!$_groupRow): ?>
                    <td rowspan="<?php echo count($_stores) ?>"><?php echo $this->escapeHtml($_row->getGroupName()); ?></td>
                            <?php $_groupRow = true; ?>
                        <?php endif; ?>
                    <td class="label"><?php echo $this->escapeHtml($_row->getStoreName()); ?></td>
                        <?php endif; ?>
                    <td><?php echo $this->formatCurrency($_row->getLifetime(), $_row->getWebsiteId()) ?></td>
                    <td><?php echo $this->formatCurrency($_row->getAvgsale(), $_row->getWebsiteId()) ?></td>
                </tr>
                        <?php endforeach; ?>
                    <?php endforeach; ?>
                <?php endforeach; ?>
            </tbody>
            <?php else: ?>
            <tbody>
                <tr class="no-display"><td colspan="5"></td></tr>
            </tbody>
            <?php endif; ?>
        </table>
    </div>
</div>
<div class="clear"></div>
<br/>
