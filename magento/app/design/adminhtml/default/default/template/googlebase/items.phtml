<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */
?>

<?php echo $this->getCaptchaHtml() ?>

<?php echo $this->getStoreSwitcherHtml() ?>

<div class="entry-edit">
    <div class="entry-edit-head">
        <div style="float: right;"></div>
        <h4 class="fieldset-legend"><?php echo $this->__('Google Base Items') ?></h4>
    </div>
    <?php echo $this->getChildHtml('item') ?>
</div>
<div class="entry-edit">
    <div class="entry-edit-head">
        <div style="float: right;"><?php echo $this->getAddButtonHtml() ?></div>
        <h4 class="fieldset-legend"><?php echo $this->__('Available Products') ?></h4>
    </div>
    <?php echo $this->getChildHtml('product') ?>
</div>


<script type="text/javascript">
var productsGridElement = $('<?php echo $this->getChild('product')->getId() ?>');
productsGridElement.hide();
$('products_grid_button').observe('click', function (event) {
    var element = event.element();
    productsGridElement.visible() ? productsGridElement.hide() : productsGridElement.show();
});
</script>
