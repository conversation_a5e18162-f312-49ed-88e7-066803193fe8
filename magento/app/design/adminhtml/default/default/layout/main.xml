<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE_AFL.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.magento.com for more information.
 *
 * @category    design
 * @package     default_default
 * @copyright   Copyright (c) 2006-2020 Magento, Inc. (http://www.magento.com)
 * @license     http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 */


Supported layout update handles (action):


Supported layout update handles (special):
- adminhtml
- default
- admin_noroute
- admin_denied
- preview
- systemPreview
-->

<layout>

<!--
Form key for inserting into various forms
-->
    <formkey>
        <block type="core/template" name="formkey" as="formkey" template="formkey.phtml"/>
    </formkey>

<!--
Default layout, loads most of the pages
-->

    <default>
        <block type="adminhtml/page" name="root" output="toHtml" template="page.phtml">
           <block type="adminhtml/page_head" name="head" as="head" template="page/head.phtml">
                <action method="setTitle" translate="title"><title>Magento Admin</title></action>
                <action method="addJs"><script>prototype/prototype.js</script></action>
                <action method="addItem"><type>js</type><name>extjs/fix-defer-before.js</name><params/><if/><condition>can_load_ext_js</condition></action>
                <action method="addJs"><script>prototype/window.js</script></action>
                <action method="addJs"><script>scriptaculous/builder.js</script></action>
                <action method="addJs"><script>scriptaculous/effects.js</script></action>
                <action method="addJs"><script>scriptaculous/dragdrop.js</script></action>
                <action method="addJs"><script>scriptaculous/controls.js</script></action>
                <action method="addJs"><script>scriptaculous/slider.js</script></action>
                <action method="addJs"><script>lib/ccard.js</script></action>
                <action method="addJs"><script>prototype/validation.js</script></action>
                <action method="addJs"><script>varien/js.js</script></action>
                <action method="addJs"><script>mage/translate.js</script></action>

                <action method="addJs"><script>mage/adminhtml/hash.js</script></action>
                <action method="addJs"><script>mage/adminhtml/events.js</script></action>
                <action method="addJs"><script>mage/adminhtml/loader.js</script></action>
                <action method="addJs"><script>mage/adminhtml/grid.js</script></action>
                <action method="addJs"><script>mage/adminhtml/tabs.js</script></action>
                <action method="addJs"><script>mage/adminhtml/form.js</script></action>
                <action method="addJs"><script>mage/adminhtml/accordion.js</script></action>
                <action method="addJs"><script>mage/adminhtml/tools.js</script></action>
                <action method="addJs"><script>mage/adminhtml/uploader.js</script></action>
                <action method="addJs"><script>mage/adminhtml/product.js</script></action>

                <action method="addCss"><name>reset.css</name></action>
                <action method="addCss"><name>boxes.css</name></action>
                <action method="addCss"><name>custom.css</name></action>

                <action method="addItem"><type>skin_css</type><name>iestyles.css</name><params/><if>lt IE 8</if></action>
                <action method="addItem"><type>skin_css</type><name>below_ie7.css</name><params/><if>lt IE 7</if></action>
                <action method="addItem"><type>skin_css</type><name>ie7.css</name><params/><if>IE 7</if></action>

                <action method="addCss"><name>print.css</name><params>media="print"</params></action>

                <action method="addItem"><type>js</type><name>lib/ds-sleight.js</name><params>defer</params><if>lt IE 7</if></action>
                <action method="addItem"><type>js</type><name>varien/iehover-fix.js</name><params/><if>lt IE 7</if></action>

                <action method="addItem"><type>skin_css</type><name>menu.css</name><params>media="screen, projection"</params></action>

                <action method="addItem"><type>js_css</type><name>calendar/calendar-win2k-1.css</name><params/><!--<if/><condition>can_load_calendar_js</condition>--></action>
                <action method="addItem"><type>js</type><name>calendar/calendar.js</name><!--<params/><if/><condition>can_load_calendar_js</condition>--></action>
                <action method="addItem"><type>js</type><name>calendar/calendar-setup.js</name><!--<params/><if/><condition>can_load_calendar_js</condition>--></action>

                <action method="addItem"><type>js</type><name>extjs/ext-tree.js</name><params/><if/><condition>can_load_ext_js</condition></action>
                <action method="addItem"><type>js</type><name>extjs/fix-defer.js</name><params/><if/><condition>can_load_ext_js</condition></action>
                <action method="addItem"><type>js</type><name>extjs/ext-tree-checkbox.js</name><params/><if/><condition>can_load_ext_js</condition></action>
                <action method="addItem"><type>js_css</type><name>extjs/resources/css/ext-all.css</name><params/><if/><condition>can_load_ext_js</condition></action>
                <action method="addItem"><type>js_css</type><name>extjs/resources/css/ytheme-magento.css</name><params/><if/><condition>can_load_ext_js</condition></action>

                <action method="addItem"><type>js</type><name>mage/adminhtml/rules.js</name><params/><if/><condition>can_load_rules_js</condition></action>

                <action method="addItem"><type>js</type><name>mage/adminhtml/wysiwyg/tiny_mce/setup.js</name><params/><if/><condition>can_load_tiny_mce</condition></action>

                <block type="core/html_calendar" name="head.calendar" as="calendar" template="page/js/calendar.phtml"/>
            </block>

            <block type="adminhtml/page_notices" name="global_notices" as="global_notices" template="page/notices.phtml" />
            <block type="adminhtml/page_header" name="header" as="header"></block>
            <block type="adminhtml/page_menu" name="menu" as="menu"></block>
            <block type="core/text_list" name="notifications" as="notifications">
                <block type="adminhtml/notification_baseurl" name="notification_baseurl" as="notification_baseurl" template="notification/baseurl.phtml"></block>
                <block type="adminhtml/cache_notifications" name="cache_notifications" template="system/cache/notifications.phtml"></block>
                <block type="adminhtml/notification_survey" name="notification_survey" template="notification/survey.phtml"/>
                <block type="adminhtml/notification_security" name="notification_security" as="notification_security" template="notification/security.phtml"></block>
                <block type="adminhtml/checkout_formkey" name="checkout_formkey" as="checkout_formkey" template="notification/formkey.phtml"/>
                <block type="adminhtml/notification_symlink" name="notification_symlink" template="notification/symlink.phtml"/>
            </block>
            <block type="adminhtml/widget_breadcrumbs" name="breadcrumbs" as="breadcrumbs"></block>

            <!--<update handle="formkey"/> this won't work, see the try/catch and a jammed exception in Mage_Core_Model_Layout::createBlock() -->
            <block type="core/template" name="formkey" as="formkey" template="formkey.phtml"/>

            <!-- deprecated since 1.7.0.0 see Mage_Page_Block_Js_Translate -->
            <block type="page/js_translate" name="js_translate" as="js_translate" template="page/js/translate.phtml"/>
            <block type="core/text_list" name="left" as="left"/>
            <block type="core/text_list" name="content" as="content"/>
            <block type="core/messages" name="messages" as="messages"/>
            <block type="core/text_list" name="js" as="js"/>
            <block type="adminhtml/page_footer" name="footer" as="footer">
                <action method="setBugreportUrl"><url>http://www.magentocommerce.com/bug-tracking</url></action>
                <action method="setConnectWithMagentoUrl"><url>http://magento.com/resources/technical</url></action>
            </block>
            <block type="core/profiler" name="profiler" as="profiler"/>
            <block type="core/text_list" name="before_body_end" as="before_body_end"/>

        </block>
    </default>

<!--
Layout for pop up windows
-->
    <popup>
        <update handle="default"/>
        <remove name="header"/>
        <remove name="menu"/>
        <reference name="root">
            <action method="setTemplate"><template>popup.phtml</template></action>
        </reference>
    </popup>

<!--
Layout for overlay pop up windows
-->
    <overlay_popup>
        <update handle="popup"/>
        <reference name="root">
            <action method="setTemplate"><template>overlay_popup.phtml</template></action>
        </reference>
    </overlay_popup>

<!--
Layout for editor element
-->
    <editor>
        <reference name="head">
            <action method="setCanLoadExtJs"><flag>1</flag></action>
            <action method="addJs"><script>mage/adminhtml/variables.js</script></action>
            <action method="addJs"><script>mage/adminhtml/wysiwyg/widget.js</script></action>
            <action method="addJs"><name>lib/uploader/flow.min.js</name></action>
            <action method="addJs"><name>lib/uploader/fusty-flow.js</name></action>
            <action method="addJs"><name>lib/uploader/fusty-flow-factory.js</name></action>
            <action method="addJs"><name>mage/adminhtml/uploader/instance.js</name></action>
            <action method="addJs"><script>mage/adminhtml/browser.js</script></action>
            <action method="addJs"><script>prototype/window.js</script></action>
            <action method="addItem"><type>js_css</type><name>prototype/windows/themes/default.css</name></action>
            <action method="addCss"><name>lib/prototype/windows/themes/magento.css</name></action>
        </reference>
    </editor>

<!--
No page found
-->

    <adminhtml_noroute>
        <reference name="content">
            <block type="core/text" name="content.noRoute">
                <action method="setText" translate="text" module="adminhtml"><text><![CDATA[<h1 class="page-heading">404 Error</h1><p>Page not found.</p>]]></text></action>
            </block>
        </reference>
    </adminhtml_noroute>

<!--
Access denied
-->

    <adminhtml_denied>
        <reference name="content">
            <block type="adminhtml/denied" name="content.denied" template="access_denied.phtml"/>
        </reference>
    </adminhtml_denied>

<!--
Base preview layout (?)
-->

    <preview>
        <block type="core/template" name="root" output="toHtml" template="newsletter/template/preview.phtml">
            <block type="adminhtml/newsletter_template_preview" name="content" as="content"></block>
        </block>
    </preview>

    <newsletter_template_preview>
        <block type="core/template" name="root" output="toHtml" template="newsletter/template/preview.phtml">
            <block type="adminhtml/newsletter_template_preview" name="content" as="content"></block>
        </block>
    </newsletter_template_preview>

    <newsletter_template_preview_switcher>
        <block type="core/template" name="root" output="toHtml" template="newsletter/template/preview/iframeswitcher.phtml">
            <block type="adminhtml/store_switcher" name="store_switcher" as="store_switcher" />
            <block type="adminhtml/newsletter_template_preview_form" name="preview_form" />
        </block>
    </newsletter_template_preview_switcher>

    <newsletter_queue_preview>
        <block type="core/template" name="root" output="toHtml" template="newsletter/queue/preview.phtml">
            <block type="adminhtml/newsletter_queue_preview" name="content" as="content"></block>
        </block>
    </newsletter_queue_preview>

<!--
Base customer alerts preview layout (?)
-->

    <alert_preview>
        <block type="core/template" name="root" output="toHtml" template="alert/template/preview.phtml">
            <block type="adminhtml/alert_template_preview" name="content" as="content"></block>
        </block>
    </alert_preview>
<!--
Base preview layout
-->

    <systemPreview>
        <block type="core/template" name="root" output="toHtml" template="system/email/template/preview.phtml">
            <block type="adminhtml/system_email_template_preview" name="content" as="content"></block>
        </block>
    </systemPreview>

    <adminhtml_dashboard_customersmost>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/dashboard_tab_customers_most" name="adminhtml.dashboard.tab.customers.most"/>
        </block>
    </adminhtml_dashboard_customersmost>

    <adminhtml_dashboard_customersnewest>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/dashboard_tab_customers_newest" name="adminhtml.dashboard.tab.customers.newest"/>
        </block>
    </adminhtml_dashboard_customersnewest>

    <adminhtml_dashboard_productsviewed>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/dashboard_tab_products_viewed" name="adminhtml.dashboard.tab.products.viewed"/>
        </block>
    </adminhtml_dashboard_productsviewed>

    <adminhtml_index_login>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/template" name="content" template="login.phtml">
                <block type="core/text_list" name="form.additional.info" />
            </block>
        </block>
    </adminhtml_index_login>

    <adminhtml_index_forgotpassword>
        <block type="core/text_list" name="root" output="toHtml">
            <block type="adminhtml/template" name="content" template="forgotpassword.phtml">
                <block type="core/text_list" name="form.additional.info" />
            </block>
        </block>
    </adminhtml_index_forgotpassword>
<!--
Empty hanle for ajax response etc.
-->
    <empty>
    </empty>
</layout>
