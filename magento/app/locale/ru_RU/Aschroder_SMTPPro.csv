"SMTPPro - Email Log","SMTPPro - почтовые логи"
"SMTP Pro Email Settings","Настройка почты в SMTP Pro"
"Email Log","Почтовые логи"
"Aschroder Extensions","Расширения Aschroder"
"SMTP Pro","SMTP Pro"
"General Settings","Общие настройки"
"<div style='background-color: #efefef;margin-bottom: 10px;height: 40px;'> <img style='float:left;width: 150px;' src='http://www.aschroder.com/smtppro-logo.png' /> <span style='float:left;font-size: 20px; margin:10px;'>SMTP Pro Email Extension</span> </div> Configure your SMTP connection below. If you have any questions or would like any help please visit <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>.","<div style='background-color: #efefef;margin-bottom: 10px;height: 40px;'> <img style='float:left;width: 150px;' src='http://www.aschroder.com/smtppro-logo.png' /> <span style='float:left;font-size: 20px; margin:10px;'>Настройка почты в SMTP Pro</span> </div> Настройте ваше SMTP-соединение ниже. Если у вас есть вопросы или вам нужна помощь - пожалуйста, зайдите на сайт <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>."
"Email Connection","Подключение по электронной почте"
"Google Apps Email Address","Адрес электронной почты Google Apps"
"Google Apps Password","Пароль Google Apps"
"Input your Google Apps or Gmail username and password here. For configuration recommendations please see the guide at <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>","Введите имя пользователя и пароль вашей учётной записи Google Apps или Gmail. За рекомендациями по настройке, пожалуйста, смотрите руководство на сайте <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>"
"SendGrid Username","Имя пользователя SendGrid"
"SendGrid Password","Пароль SendGrid"
"Input your SendGrid username and password here. For more information visit <a href='http://sendgrid.com' target='_blank'>SendGrid</a>","Введите имя пользователя и пароль вашей учётной записи SendGrid. За дополнительной информацией обращайтесь на сайт <a href='http://sendgrid.com' target='_blank'>SendGrid</a>"
"Amazon SES Access Key","Ключ доступа в Amazon SES"
"Amazon SES Secret Key","Секретный ключ в Amazon SES"
"Amazon SES support in SMTP Pro is limited and best suited to development and testing purposes. For a full integration with region selection, error/bounce logging and send statistics please see the premium extension: <a href='http://magesend.com' target='_blank'>MageSend</a>","Поддержка Amazon SES в расширении SMTP Pro ограничена и лучше всего подходит для целей разработки и тестирования. Для полной интеграции с выбором региона, ведением логов ошибок/отказов и отправкой статистики, пожалуйста, см. премиум расширение: <a href='http://magesend.com' target='_blank'>MageSend</a>"
"Authentication","Аутентификация"
"Username","Имя пользователя"
"Password","Пароль"
"Host","Хост"
"Port","Порт"
"SSL Security","Безопасность SSL"
"Custom SMTP servers can be configured in this section. For more information about these configuration options and troubleshooting advice please see <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>","Пользовательские SMTP-серверы могут быть сконфигурированны в этой секции. Дополнительную информацию по конфигурационным опциям и рекомендациям по устранению неполадок смотрите сайте <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>"
"Logging and Debugging","Логи и отладка"
"Please only use these settings if you are a software developer or server admin.","Пожалуйста, используйте эти настройки только в случае, если вы разработчик ПО или системный администратор."
"Log Emails","Лог-сообщения почты"
"This will log all outbound emails. View from System->Tools->SMTPPro - Email Log.","Это будет регистрировать все исходящие письма. См. в «Система» > «Инструменты» > «SMTPPro - почтовые логи»."
"Clean Email Logs","Очистить почтовые логи"
"If this is set to yes, old entries will be deleted from email log. Cron is required and log cleaning must be enabled in system/log/enabled for this to work.","Если это значение равно «Да», то старые записи будут удалены из лога электронной почты. Требуется Cron и очистка лога должна быть включена в system/log/enabled для того, чтобы это сработало."
"Email Log Days Kept","Длительность хранения записи в логе"
"Enable Debug Logging","Включить ведение отладочного лога"
"If yes, a log file will be written with debug information to file var/log/aschroder_smtppro.log.","Если да, файл с логом будет записан с отладочной информацией в файл var/log/aschroder_smtppro.log."
"Save settings before running this test.","Сохранить настройки до запуска этого теста."
"Compatible Email Services","Совместимые службы электронной почты"
"Running SMTP Pro Self Test","Выполнение самотестирования SMTP Pro"
"SMTP Pro Self Test Results","Результаты самотестирования SMTP Pro"
"Extension disabled, cannot run test.","Расширение отключено, тест не запускается."
"Checking config re-writes have not clashed.","Проверка отсутствия конфликтов при перезаписывании конфигурации."
"Detected overwrite conflict: %s","Обнаружен конфликт перезаписи: %s"
"Raw connection test for SMTP options.","Тестировать Raw-соединение для SMTP-опций."
"Complete","Завершить"
"Failed to connect to SMTP server. Reason: ","Не удалось подключиться к SMTP-серверу. Причина: "
"This extension requires an outbound SMTP connection on port: ","Этому расширению требуется исходящее SMTP-соединение на порт: "
"Connection to Host SMTP server successful","Соединение с SMTP-сервером успешно установлено"
"Skipping raw connection test for non-SMTP options.","Пропустить проверку Raw-соединения для не SMTP-опций."
"Test Email From SMTP Pro Magento Extension","Тестировать электронную почту из расширения SMTP Pro"
"Actual email sending test...","Тестовая отправка почты..."
" from: "," из: "
"Test email was sent successfully","Тестовое сообщение успешно отправлено"
"Failed to find transport for test.","Не удалось найти транспорт для теста."
"Unable to send test email.","Не удалось отправить тестовое сообщение."
"Exception message was: %s","Сообщение об ошибке было: %s"
"Please check the user guide for frequent error messages and their solutions.","Пожалуйста, проверьте руководство пользователя на наличие частых сообщений об ошибках и их решений."
"Test email was not sent successfully: %s","Тестовое письмо отправлено не было: %s"
"See exception log for more details.","Подробнее см. лог исключений."
"Checking that a template exists for the default locale and that email communications are enabled...","Проверка наличия шаблона для локализации по умолчанию и того, что сообщения электронной почты включены..."
"Default templates exist.","Шаблоны по умолчанию существуют."
"Email communications are enabled.","Сообщения электронной почты включены."
"Default templates exist and email communications are enabled.","Шаблоны по умолчанию существуют, и сообщения электронной почты включены."
"Could not find default template, or template not valid, or email communications disabled in Advanced > System settings.","Не удалось найти шаблон по умолчанию, шаблон недействителен или связь по электронной почте отключена в меню «Расширенные» > «Настройки системы»."
"Please check that you have templates in place for your emails. These are in app/locale, or custom defined in System > Transaction Emails. Also check Advanced > System settings to ensure email communications are enabled.","Пожалуйста, убедитесь, что у вас есть шаблоны для ваших писем. Они находятся в app/locale, или определены в «Система» > «Транзакционные письма». Также проверьте «Дополнительно» > «Системные настройки», чтобы обеспечить связь по электронной почте."
"Could not find default template, or template not valid, or email communications disabled in Advanced > System settings","Не удалось найти шаблон по умолчанию, шаблон недействителен или связь по электронной почте отключена в «Расширенные» > «Настройки системы»"
"Could not test default template validity.","Не удалось проверить правильность шаблона по умолчанию."
"Please check that you have templates in place for your emails. These are in app/locale, or custom defined in System > Transaction Emails.","Убедитесь, что у вас есть шаблоны для ваших писем. Они находятся в app/locale, или определены в «Система» > «Транзакционные письма»."
"Could not test default template validity: %s","Не удалось проверить правильность шаблона по умолчанию: %s"
"Checking that tables are created...","Проверка создания таблиц..."
"Could not find required database tables.","Не удалось найти требуемые таблицы БД."
"Please try to manually re-run the table creation script. For assistance please contact us.","Попробуйте вручную перезапустить сценарий создания таблицы. Для получения помощи, пожалуйста, свяжитесь с нами."
"Required database tables exist.","Необходимые таблицы БД существуют."
"Testing complete, if you are still experiencing difficulties please visit <a target='_blank' href='http://magesmtppro.com'>the support page</a> or contact me via <a target='_blank' href='mailto:<EMAIL>'><EMAIL></a> for support.","Тестирование завершено, если вы все еще испытываете трудности, то посетите <a target='_blank' href='http://magesmtppro.com'>страницу поддержки</a> или свяжитесь со мной <a target='_blank' href='mailto:<EMAIL>'><EMAIL></a> для поддержки."
"Testing failed,  please review the reported problems and if you need further help visit  <a target='_blank' href='http://magesmtppro.com'>the support page</a> or contact me via <a target='_blank' href='mailto:<EMAIL>'><EMAIL></a> for support.","Тестирование не удалось,  просмотрите описанные проблемы и если вам нужна дополнительная помощь посетите <a target='_blank' href='http://magesmtppro.com'>страницу поддержки</a> или свяжитесь со мной <a target='_blank' href='mailto:<EMAIL>'><EMAIL></a> для поддержки."
"Disabled","Отключено"
"MailUp Username","Имя пользователя MailUp"
"MailUp Password","Пароль MailUp"
"Input your MailUp username and password here. For more information visit <a href='http://mailup.com' target='_blank'>MailUp</a>","Введите ваше имя пользователя и пароль в MailUp. Для дополнительной информации смотрите на сайте <a href='http://mailup.com' target='_blank'>MailUp</a>"
"Google Apps or Gmail","Google Apps или Gmail"
