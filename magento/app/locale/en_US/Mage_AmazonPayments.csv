" Select Shipping Method"," Select Shipping Method"
"<br/>Trace confirmation request:<br/>%s","<br/>Trace confirmation request:<br/>%s"
"Access Key","Access Key"
"Access Key ID","Access Key ID"
"Amazon Simple Pay","Amazon Simple Pay"
"Amazon Simple Pay notification error","Amazon Simple Pay notification error"
"Amazon Simple Pay service capture confirmation error: confirmation request amount is not equal to the amount of order.","Amazon Simple Pay service capture confirmation error: confirmation request amount is not equal to the amount of order."
"Amazon Simple Pay service capture confirmation error: the amount in the existing invoice does not match the amount in the confirmation request.","Amazon Simple Pay service capture confirmation error: the amount in the existing invoice does not match the amount in the confirmation request."
"Amazon Simple Pay service capture confirmation error: the existing transaction ID does not match the transaction ID in the confirmation request.","Amazon Simple Pay service capture confirmation error: the existing transaction ID does not match the transaction ID in the confirmation request."
"Amazon Simple Pay service capture confirmation. The invoice was captured automatically.","Amazon Simple Pay service capture confirmation. The invoice was captured automatically."
"Amazon Simple Pay service confirmation error: order states sequence violation.","Amazon Simple Pay service confirmation error: order states sequence violation."
"Amazon Simple Pay service confirmation error: the order currency does not match the currency of the IPN request.","Amazon Simple Pay service confirmation error: the order currency does not match the currency of the IPN request."
"Amazon Simple Pay service confirmation error: the order specified in the IPN request cannot be found.","Amazon Simple Pay service confirmation error: the order specified in the IPN request cannot be found."
"Amazon Simple Pay service confirmation error: the payment method in the order is not Amazon Simple Pay.","Amazon Simple Pay service confirmation error: the payment method in the order is not Amazon Simple Pay."
"Amazon Simple Pay service confirmed capture for invoice %s.","Amazon Simple Pay service confirmed capture for invoice %s."
"Amazon Simple Pay service confirmed refund for credit memo %s.","Amazon Simple Pay service confirmed refund for credit memo %s."
"Amazon Simple Pay service has confirmed amount authorization.","Amazon Simple Pay service has confirmed amount authorization."
"Amazon Simple Pay service has confirmed cancelation.","Amazon Simple Pay service has confirmed cancelation."
"Amazon Simple Pay service has confirmed capture for invoice %s. The invoice was automatically captured.","Amazon Simple Pay service has confirmed capture for invoice %s. The invoice was automatically captured."
"Amazon Simple Pay service has confirmed capture initiation.","Amazon Simple Pay service has confirmed capture initiation."
"Amazon Simple Pay service has confirmed capture.","Amazon Simple Pay service has confirmed capture."
"Amazon Simple Pay service has confirmed payment capture. Invoice %s was automatically created after confirmation.","Amazon Simple Pay service has confirmed payment capture. Invoice %s was automatically created after confirmation."
"Amazon Simple Pay service has confirmed payment capture. Invoice was created automatically.","Amazon Simple Pay service has confirmed payment capture. Invoice was created automatically."
"Amazon Simple Pay service has confirmed payment refund. Credit memo %s was automatically created after confirmation.","Amazon Simple Pay service has confirmed payment refund. Credit memo %s was automatically created after confirmation."
"Amazon Simple Pay service has confirmed payment refund. Credit memo was created automatically.","Amazon Simple Pay service has confirmed payment refund. Credit memo was created automatically."
"Amazon Simple Pay service has confirmed payment refund. The credit memo was created automatically.","Amazon Simple Pay service has confirmed payment refund. The credit memo was created automatically."
"Amazon Simple Pay service has confirmed refund.","Amazon Simple Pay service has confirmed refund."
"Amazon Simple Pay service has confirmed refund. The credit memo was processed automatically.","Amazon Simple Pay service has confirmed refund. The credit memo was processed automatically."
"Amazon Simple Pay service has confirmed refunded credit memo %s. The credit memo was processed automatically.","Amazon Simple Pay service has confirmed refunded credit memo %s. The credit memo was processed automatically."
"Amazon Simple Pay service is not available. Payment was not processed.","Amazon Simple Pay service is not available. Payment was not processed."
"Amazon Simple Pay service payment confirmation has failed.","Amazon Simple Pay service payment confirmation has failed."
"Amazon Simple Pay service refund confirmation error: the amount in the existing creditmemo does not match the amount in the confirmation request.","Amazon Simple Pay service refund confirmation error: the amount in the existing creditmemo does not match the amount in the confirmation request."
"Amazon Simple Pay service refund confirmation error: the confirmation request amount does not match the order amount.","Amazon Simple Pay service refund confirmation error: the confirmation request amount does not match the order amount."
"Amazon Simple Pay service refund confirmation error: the transaction ID in the existing creditmemo does not match the transaction ID in the confirmation request.","Amazon Simple Pay service refund confirmation error: the transaction ID in the existing creditmemo does not match the transaction ID in the confirmation request."
"Authorize Only","Authorize Only"
"Authorize and Capture","Authorize and Capture"
"Cancel Description","Cancel Description"
"Cancel Order Notification received from Checkout by Amazon service.","Cancel Order Notification received from Checkout by Amazon service."
"Checkout by Amazon","Checkout by Amazon"
"Continue Shopping","Continue Shopping"
"Customer was redirected to Amazon Simple Pay site","Customer was redirected to Amazon Simple Pay site"
"Debug","Debug"
"Debug Log","Debug Log"
"Email Sender","Email Sender"
"Enable","Enable"
"Error Log","Error Log"
"FPS Sandbox Service URI","FPS Sandbox Service URI"
"FPS Service URI","FPS Service URI"
"Immediate Return","Immediate Return"
"Invalid action code.","Invalid action code."
"Invalid request.","Invalid request."
"Invoice was created with Checkout by Amazon.","Invoice was created with Checkout by Amazon."
"Merchant ID","Merchant ID"
"Merchant Login","Merchant Login"
"Merchant Name","Merchant Name"
"Merchant Password","Merchant Password"
"Merchant Token","Merchant Token"
"Method Choice Checkout Message","Method Choice Checkout Message"
"New Order Notification received from Checkout by Amazon service.","New Order Notification received from Checkout by Amazon service."
"New Order Status","New Order Status"
"Notification Error Transactional Email","Notification Error Transactional Email"
"Order Ready To Ship Notification received form Checkout by Amazon service.","Order Ready To Ship Notification received form Checkout by Amazon service."
"Order was canceled with Checkout by Amazon.","Order was canceled with Checkout by Amazon."
"Payment Action","Payment Action"
"Payment Description","Payment Description"
"Payment Sandbox Service URI","Payment Sandbox Service URI"
"Payment Service URI","Payment Service URI"
"Payment authorization canceled with Amazon Simple Pay service.","Payment authorization canceled with Amazon Simple Pay service."
"Pending Amazon Simple Pay","Pending Amazon Simple Pay"
"Redirect Message","Redirect Message"
"Refund Description","Refund Description"
"Refund was created with Checkout by Amazon.","Refund was created with Checkout by Amazon."
"Report Email","Report Email"
"Report Error to Email","Report Error to Email"
"Response body is not a valid FPS response.","Response body is not a valid FPS response."
"SUCCESS","SUCCESS"
"Sandbox","Sandbox"
"Secret Key","Secret Key"
"Secret Key ID","Secret Key ID"
"Shipment was created with Checkout by Amazon.","Shipment was created with Checkout by Amazon."
"Thank you for your purchase!","Thank you for your purchase!"
"The customer has canceled payment and successfully returned from Amazon Simple Pay site","The customer has canceled payment and successfully returned from Amazon Simple Pay site"
"The customer has successfully returned from Amazon Simple Pay site","The customer has successfully returned from Amazon Simple Pay site"
"The invoice was created (online capture). Waiting for capture confirmation from Amazon Simple Pay service.","The invoice was created (online capture). Waiting for capture confirmation from Amazon Simple Pay service."
"The invoice was not refunded. Amazon Simple Pay service error: [%s] %s","The invoice was not refunded. Amazon Simple Pay service error: [%s] %s"
"The item specified in callback request XML was not found in quote.","The item specified in callback request XML was not found in quote."
"The order was not canceled. Amazon Simple Pay service error: [%s] %s","The order was not canceled. Amazon Simple Pay service error: [%s] %s"
"The order was not captured online. Authorization confirmation is required.","The order was not captured online. Authorization confirmation is required."
"The order was not captured. Amazon Simple Pay service error: [%s] %s","The order was not captured. Amazon Simple Pay service error: [%s] %s"
"The payment was captured online with Amazon Simple Pay service. The invoice was created. Waiting for capture confirmation from the payment service.","The payment was captured online with Amazon Simple Pay service. The invoice was created. Waiting for capture confirmation from the payment service."
"The payment was refunded online with Amazon Simple Pay service. The credit memo was created. Waiting for refund confirmation from Amazon Simple Pay service.","The payment was refunded online with Amazon Simple Pay service. The credit memo was created. Waiting for refund confirmation from Amazon Simple Pay service."
"The payment was refunded online. Waiting for refund confirmation from Amazon Simple Pay service.","The payment was refunded online. Waiting for refund confirmation from Amazon Simple Pay service."
"The request is not a valid IPN request.","The request is not a valid IPN request."
"The request was signed incorrectly or the signature is missing.","The request was signed incorrectly or the signature is missing."
"Title","Title"
"Use Callback API","Use Callback API"
"You will be redirected to Checkout by Amazon in a few seconds.","You will be redirected to Checkout by Amazon in a few seconds."
"You will receive an order confirmation email with details of your order and a link to track its progress.","You will receive an order confirmation email with details of your order and a link to track its progress."
"You will receive an order confirmation email with details of your order and a link to track its progress.<br/>","You will receive an order confirmation email with details of your order and a link to track its progress.<br/>"
"Your billing address will be ignored and you will be redirected to Checkout by Amazon website.","Your billing address will be ignored and you will be redirected to Checkout by Amazon website."
"Your order has been received.","Your order has been received."
