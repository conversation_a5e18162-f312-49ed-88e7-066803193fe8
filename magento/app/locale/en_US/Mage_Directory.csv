"-- Please select --","-- Please select --"
"API Access Key","API Access Key"
"API Key","API Key"
"Allowed Currencies","Allowed Currencies"
"An invalid base currency has been entered.","An invalid base currency has been entered."
"Base Currency","Base Currency"
"Base currency is used for all online payment transactions. Scope is defined by the catalog price scope (""Catalog"" > ""Price"" > ""Catalog Price Scope"").","Base currency is used for all online payment transactions. Scope is defined by the catalog price scope (""Catalog"" > ""Price"" > ""Catalog Price Scope"")."
"Can\'t convert rate from ""%s-%s"".","Can\'t convert rate from ""%s-%s""."
"Cannot retrieve rate from %s.","Cannot retrieve rate from %s."
"Connection Timeout in Seconds","Connection Timeout in Seconds"
"Continue &raquo;","Continue &raquo;"
"Country","Country"
"Country API","Country API"
"Country and Format Type combination should be unique","Country and Format Type combination should be unique"
"Currency","Currency"
"Currency Options","Currency Options"
"Currency Setup","Currency Setup"
"Currency Update Warnings","Currency Update Warnings"
"Currency rates can\'t be retrieved.","Currency rates can\'t be retrieved."
"CurrencyConverterAPI","CurrencyConverterAPI"
"Default Display Currency","Default Display Currency"
"Directory","Directory"
"Display not required State","Display not required State"
"Enabled","Enabled"
"Error Email Recipient","Error Email Recipient"
"Error Email Sender","Error Email Sender"
"Error Email Template","Error Email Template"
"FATAL ERROR:","FATAL ERROR:"
"Fixer.IO","Fixer.IO"
"Frequency","Frequency"
"Installed Currencies","Installed Currencies"
"Invalid Import Service specified.","Invalid Import Service specified."
"Invalid country code: %s","Invalid country code: %s"
"Invalid rates received","Invalid rates received"
"Invalid target currency.","Invalid target currency."
"List of countries","List of countries"
"List of regions in specified country","List of regions in specified country"
"No API Key was specified or an invalid API Key was specified.","No API Key was specified or an invalid API Key was specified."
"One or more invalid symbols have been specified.","One or more invalid symbols have been specified."
"Postal Code is Optional for the following countries","Postal Code is Optional for the following countries"
"Region","Region"
"Region API","Region API"
"Scheduled Import Settings","Scheduled Import Settings"
"Select Your Currency","Select Your Currency"
"Service","Service"
"Start Time","Start Time"
"State is required for","State is required for"
"State/Province","State/Province"
"States Options","States Options"
"Strikeiron Email Update Warnings","Strikeiron Email Update Warnings"
"The ""%s"" is not allowed as base currency for your subscription plan.","The ""%s"" is not allowed as base currency for your subscription plan."
"The account this API request is coming from is inactive.","The account this API request is coming from is inactive."
"The current request did not return any results.","The current request did not return any results."
"The maximum allowed API amount of monthly API requests has been reached.","The maximum allowed API amount of monthly API requests has been reached."
"Unable to initialize the import model.","Unable to initialize the import model."
"Undefined rate from ""%s-%s"".","Undefined rate from ""%s-%s""."
"WARNING:","WARNING:"
"We can\'t retrieve a rate from %s for %s.","We can\'t retrieve a rate from %s for %s."
"Webservicex","Webservicex"
"Your Currency","Your Currency"
"Your Currency:","Your Currency:"
"Your current currency is: %s","Your current currency is: %s"
