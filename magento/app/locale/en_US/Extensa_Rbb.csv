"Check KBC Status","Check KBC Status"
"Payment with Credit/Debit Cards (KBC)","Payment with Credit/Debit Cards (KBC)"
"OpenSSL must be loaded.","OpenSSL must be loaded."
"Enabled","Enabled"
"Title","Title"
"Common Settings","Common Settings"
"Test Mode","Test Mode"
"Transaction type","Transaction type"
"Authorization","Authorization"
"Delayed Authorization","Delayed Authorization"
"Terminal ID (TID)","Terminal ID (TID)"
"Merchant ID (MID)","Merchant ID (MID)"
"Short Order Description","Short Order Description"
"Currency","Currency"
"Payment from Applicable Countries","Payment from Applicable Countries"
"Payment from Specific Countries","Payment from Specific Countries"
"Minimum Order Total","Minimum Order Total"
"Maximum Order Total","Maximum Order Total"
"Sort Order","Sort Order"
"in latin","in latin"
"Success URL","Success URL"
"Failure URL","Failure URL"
"Notify URL","Notify URL"
"State/Province","State/Province"
"Locality (city)","Locality (city)"
"Name (e.g. site domain)","Name (e.g. site domain)"
"Email","Email"
"Organization (company)","Organization (company)"
"ATTENTION! If you again generate Private key and Self-signed certificate you have to send it to the bank, it has to send you back the new Certificates and you have to paste them in the details. The Payment will be not working until then!","ATTENTION! If you again generate Private key and Self-signed certificate you have to send it to the bank, it has to send you back the new Certificates and you have to paste them in the details. The Payment will be not working until then!"
"Download","Download"
"Generate","Generate"
"Download Pem","Download Pem"
"8 symbol code provided by your bank","8 symbol code provided by your bank"
"send it to your bank","send it to your bank"
"Test Private key (.pem) and Self-signed certificate (.crt)","Test Private key (.pem) and Self-signed certificate (.crt)"
"Real Private key (.pem) and Self-signed certificate (.crt)","Real Private key (.pem) and Self-signed certificate (.crt)"
"To generate the Self-signed certificate, first you have to fill in the data!","To generate the Self-signed certificate, first you have to fill in the data!"
"Specify test or real mode!","Specify test or real mode!"
"There was an error while generating Self-signed certificate!<br />%s","There was an error while generating Self-signed certificate!<br />%s"
"You have generated Private key and Self-signed certificate!","You have generated Private key and Self-signed certificate!"
"To download the Self-signed certificate, first you have to generate it!","To download the Self-signed certificate, first you have to generate it!"
"Certificate Generation Settings","Certificate Generation Settings"
"Save the settings before generate Private key and Self-signed certificate!","Save the settings before generate Private key and Self-signed certificate!"
"Test certificate","Test certificate"
"Real certificate","Real certificate"
"You will be redirected to the KBC website when you place an order.","You will be redirected to the KBC website when you place an order."
"Approved or completed successfully","Approved or completed successfully"
"text_trans_code_000","Approved"
"text_trans_code_101","Invalid card parameters"
"text_trans_code_105","Not approved by emitent"
"text_trans_code_108","Lost/stolen card"
"text_trans_code_111","Non existent card"
"text_trans_code_116","Insufficient funds"
"text_trans_code_130","Limit is exceeded"
"text_trans_code_290","Issuer is not accessible"
"text_trans_code_291","Technical/Communication problem"
"text_trans_code_401","Invalid format"
"text_trans_code_402","Invalid Acquirer/Merchant data"
"text_trans_code_403","Component communication failure"
"text_trans_code_404","Authentication error"
"text_trans_code_405","Signature is invalid"
"text_trans_code_406","Quota of transactions exceeded"
"text_trans_code_407","Merchant is not active"
"text_trans_code_408","Transaction was not found"
"text_trans_code_409","Too many transactions were found"
"text_trans_code_410","The order was paid (possible replay)"
"text_trans_code_411","The order request time is out-of-date"
"text_trans_code_412","Replay order condition"
"text_trans_code_413","Unknown card type"
"text_trans_code_414","CVC required"
"text_trans_code_420","The total amount of successful transactions per day is limited"
"text_trans_code_421","Tran amount limit (non 3-D Secure full authenticated)"
"text_trans_code_430","Transaction is prohibited by Gateway"
"text_trans_code_431","Attempted 3D-Secure is not accepted"
"text_trans_code_432","Card is in stop list"
"text_trans_code_433","The number of transactions has exceeded the limit"
"text_trans_code_434","The merchant does not accept cards from the country"
"text_trans_code_435","CLient IP address is on stop list"
"text_trans_code_436","The sum of amount transactions has exceeded the limit"
"text_trans_code_437","The limit of card number inputs has been exceeded"
"text_trans_code_438","Unacceptable currency code"
"text_trans_code_439","The time limit from request to authorization has been exceeded"
"text_trans_code_440","The authorization time limit has been exceeded"
"text_trans_code_441","MPI interaction problem"
"text_trans_code_442","ACS communication problem"
"text_trans_code_450","Recurrent payments are prohibited"
"text_trans_code_451","MPI service not enabled"
"text_trans_code_452","Card-to-Card Payment service not enabled"
"text_trans_code_460","Token service not enabled"
"text_trans_code_501","Canceled by user"
"text_trans_code_502","The web session is expired"
"text_trans_code_503","Transaction was canceled by merchant"
"text_trans_code_504","Transaction was canceled by gateway with reversal"
"text_trans_code_505","Invalid sequense of operations"
"text_trans_code_506","Preauthorized transaction is expired"
"text_trans_code_507","Preauthorized transaction already processed with payment"
"text_trans_code_508","Invalid amount to pay a preauthorized transaction"
"text_trans_code_509","Not able to trace back to original transaction"
"text_trans_code_510","Refund is expired"
"text_trans_code_511","Transaction was canceled by settlement action"
"text_trans_code_512","Repeated reversal or refund"
"text_trans_code_601","Not completed"
"text_trans_code_602","Waiting confirmation of instalment"
"text_trans_code_902","Cannot process transaction"
"text_trans_code_909","Cannot process transaction"
"text_trans_code_999","transaction in progress.."
"Certificate not match!","Certificate not match!"
"Terminal ID not match!","Terminal ID not match!"
"Paid Amount not match!","Paid Amount not match!"
"Can\'t get KBC Status","Can\'t get KBC Status"
"Can\'t find KBC transaction id","Can\'t find KBC transaction id"
"Successfully refunded %s %s!","Successfully refunded %s %s!"
"Failed to refund: (%s) %s!","Failed to refund: (%s) %s!"
"<br>Note: prtial refunds are allowed after 1 day of the transaction","<br>Note: prtial refunds are allowed after 1 day of the transaction"
"No response","No response"
"Wait for notification","Wait for notification"
"KBC %s: Use latin characters!","KBC %s: Use latin characters!"