"Login Attempts","Login Attempts"
"Action Log","Action Log"
"Username","Username"
"Date","Date"
"Full Name","Full Name"
"IP Address","IP Address"
"Status","Status"
"Failed","Failed"
"Success","Success"
"Locked out","Locked out"
"Action Type","Action Type"
"Object","Object"
"Store View","Store View"
"Item","Item"
"Actions","Actions"
"view","view"
"Preview Changes","Preview Changes"
"View Details","View Details"
"Login Attempts","Login Attempts"
"General","General"
"Enable Automatic Login Lockout","Enable Automatic Login Lockout"
"User will not be able to login after unsuccessful login attempts","User will not be able to login after unsuccessful login attempts"
"Number Of Failed Login Attempts","Number Of Failed Login Attempts"
"Maximum allowed number of tries","Maximum allowed number of tries"
"Lockout Time (in seconds)","Lockout Time (in seconds)"
"Admin Users To Log Actions For","Admin Users To Log Actions For"
"Automatically Purge Action Logs After (Days)","Automatically Purge Action Logs After (Days)"
"Remove All Lockouts","Remove All Lockouts"
"Clicking this button will unblock all currently blocked users","Clicking this button will unblock all currently blocked users"
"Amasty Customer Attribute","Amasty Customer Attribute"
"Order","Order"
"Product","Product"
"Product Attribute","Product Attribute"
"Product Attribute Set","Product Attribute Set"
"Product Attribute Set","Product Attribute Set"
"Tax Rules","Tax Rules"
"Tags","Tags"
"Attributes map","Attributes map"
"Customer Groups","Customer Groups"
"Catalog Price Rules","Catalog Price Rules"
"Shopping Cart Price Rules","Shopping Cart Price Rules"
"Newsletter Templates","Newsletter Templates"
"CMS Manage Pages","CMS Manage Pages"
"CMS Static Blocks","CMS Static Blocks"
"CMS Widget Instances","CMS Widget Instances"
"CMS Poll","CMS Poll"
"Customer","Customer"
"System Configuration","System Configuration"
"User","User"
"Role","Role"
"System Design","System Design"
"System Web Services Users","System Web Services Users"
"System Web Services Roles","System Web Services Roles"
"System Transactional Emails","System Transactional Emails"
"System Custom Variable","System Custom Variable"
"Order","Order"
"Customer","Customer"
"Date:","Date:"
"Username:","Username:"
"Full Name:","Full Name:"
"Email:","Email:"
"Action Type:","Action Type:"
"Object:","Object:"
"Item","Item"
"Action Log Details","Action Log Details"
"Modifications Breakdown","Modifications Breakdown"
"Name","Name"
"Old Value","Old Value"
"New Value","New Value"
"Not found","Not found"
"Type","Type"
"Description","Description"