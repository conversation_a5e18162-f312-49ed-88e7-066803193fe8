"%s %s cycle.","%s %s cycle."
"* Required Fields","* Required Fields"
"--Please Select--","--Please Select--"
"3D Secure Card Validation","3D Secure Card Validation"
"3D Secure Credit Card Validation","3D Secure Credit Card Validation"
"<label>Make Check payable to:</label> %s","<label>Make Check payable to:</label> %s"
"A value is required for live mode. Refer to your CardinalCommerce agreement.","A value is required for live mode. Refer to your CardinalCommerce agreement."
"Allow Initial Fee Failure","Allow Initial Fee Failure"
"An internal reference ID is required to save the payment profile.","An internal reference ID is required to save the payment profile."
"Authorize action is not available.","Authorize action is not available."
"Auto Bill on Next Cycle","Auto Bill on Next Cycle"
"Automatically Invoice All Items","Automatically Invoice All Items"
"Automatically bill the outstanding balance amount in the next billing cycle (if there were failed payments).","Automatically bill the outstanding balance amount in the next billing cycle (if there were failed payments)."
"Bank Transfer Payment","Bank Transfer Payment"
"Billing Agreement status is not set.","Billing Agreement status is not set."
"Billing Amount","Billing Amount"
"Billing Frequency","Billing Frequency"
"Billing Period","Billing Period"
"Billing Period Unit","Billing Period Unit"
"Billing period unit is not defined or wrong.","Billing period unit is not defined or wrong."
"Cannot retrieve the payment info model object.","Cannot retrieve the payment info model object."
"Cannot retrieve the payment information object instance.","Cannot retrieve the payment information object instance."
"Cannot retrieve the payment method code.","Cannot retrieve the payment method code."
"Cannot retrieve the payment method model object.","Cannot retrieve the payment method model object."
"Capture action is not available.","Capture action is not available."
"Card Verification Number","Card Verification Number"
"Cash On Delivery Payment","Cash On Delivery Payment"
"Centinel API URL","Centinel API URL"
"Check / Money Order","Check / Money Order"
"Credit Card Number","Credit Card Number"
"Credit Card Number: xxxx-%s","Credit Card Number: xxxx-%s"
"Credit Card Type","Credit Card Type"
"Credit Card Type: %s","Credit Card Type: %s"
"Credit Card Types","Credit Card Types"
"Credit card number mismatch with credit card type.","Credit card number mismatch with credit card type."
"Credit card type is not allowed for this payment method.","Credit card type is not allowed for this payment method."
"Currency","Currency"
"Currency code is undefined.","Currency code is undefined."
"Customer ID is not set.","Customer ID is not set."
"Day","Day"
"Debug Mode","Debug Mode"
"Enabled","Enabled"
"Expiration Date","Expiration Date"
"Expiration Date: %s/%s","Expiration Date: %s/%s"
"Full name of the person receiving the product or service paid for by the recurring payment.","Full name of the person receiving the product or service paid for by the recurring payment."
"Incorrect credit card expiration date.","Incorrect credit card expiration date."
"Initial Fee","Initial Fee"
"Initial non-recurring payment amount due immediately upon profile creation.","Initial non-recurring payment amount due immediately upon profile creation."
"Instructions","Instructions"
"Internal Reference ID","Internal Reference ID"
"Invalid Credit Card Number","Invalid Credit Card Number"
"Issue Number","Issue Number"
"Make Check Payable to","Make Check Payable to"
"Make Check payable to:","Make Check payable to:"
"Make Check payable to: %s","Make Check payable to: %s"
"Maximum Billing Cycles","Maximum Billing Cycles"
"Maximum Order Total","Maximum Order Total"
"Maximum Payment Failures","Maximum Payment Failures"
"Maximum Trial Billing Cycles","Maximum Trial Billing Cycles"
"Merchant ID","Merchant ID"
"Minimum Order Total","Minimum Order Total"
"Month","Month"
"N/A","N/A"
"Name on Card","Name on Card"
"Name on the Card","Name on the Card"
"Name on the Card: %s","Name on the Card: %s"
"New Order Status","New Order Status"
"Number of billing periods that make up one billing cycle.","Number of billing periods that make up one billing cycle."
"Order action is not available.","Order action is not available."
"Overrides API URL that may be specified by a payment method.","Overrides API URL that may be specified by a payment method."
"Password","Password"
"Payment ID: %s","Payment ID: %s"
"Payment Method","Payment Method"
"Payment Methods","Payment Methods"
"Payment Methods Section","Payment Methods Section"
"Payment Reference ID","Payment Reference ID"
"Payment Services","Payment Services"
"Payment from Applicable Countries","Payment from Applicable Countries"
"Payment from Specific Countries","Payment from Specific Countries"
"Payment method code is not set.","Payment method code is not set."
"Payment method code is undefined.","Payment method code is undefined."
"Payment profile is invalid:\n%s","Payment profile is invalid:\n%s"
"Period frequency is wrong.","Period frequency is wrong."
"Please enter a valid credit card verification number.","Please enter a valid credit card verification number."
"Processor ID","Processor ID"
"Purchase Order","Purchase Order"
"Purchase Order Number","Purchase Order Number"
"Purchase Order Number:","Purchase Order Number:"
"Purchase Order Number: %s","Purchase Order Number: %s"
"Recurring Profile Start Date","Recurring Profile Start Date"
"Recurring profile start date has invalid format.","Recurring profile start date has invalid format."
"Reference ID is not set.","Reference ID is not set."
"Refund action is not available.","Refund action is not available."
"Repeats %s time(s).","Repeats %s time(s)."
"Repeats until suspended or canceled.","Repeats until suspended or canceled."
"Request Card Security Code","Request Card Security Code"
"Saved CC","Saved CC"
"Schedule Description","Schedule Description"
"Schedule description must be not empty.","Schedule description must be not empty."
"Selected payment type is not allowed for billing country.","Selected payment type is not allowed for billing country."
"Send Check to","Send Check to"
"Send Check to:","Send Check to:"
"Severe 3D Secure Card Validation","Severe 3D Secure Card Validation"
"Severe validation removes chargeback liability on merchant.","Severe validation removes chargeback liability on merchant."
"Shipping Amount","Shipping Amount"
"Short description of the recurring payment. By default equals to the product name.","Short description of the recurring payment. By default equals to the product name."
"Sort Order","Sort Order"
"Start Date","Start Date"
"Start date has invalid format.","Start date has invalid format."
"Start date is undefined.","Start date is undefined."
"Subscriber Name","Subscriber Name"
"Switch/Solo/Maestro Issue Number","Switch/Solo/Maestro Issue Number"
"Switch/Solo/Maestro Only","Switch/Solo/Maestro Only"
"Switch/Solo/Maestro Start Date","Switch/Solo/Maestro Start Date"
"Tax Amount","Tax Amount"
"Test Mode","Test Mode"
"The date when billing for the profile begins.","The date when billing for the profile begins."
"The number of billing cycles for payment period.","The number of billing cycles for payment period."
"The number of scheduled payments that can fail before the profile is automatically suspended.","The number of scheduled payments that can fail before the profile is automatically suspended."
"The payment review action is unavailable.","The payment review action is unavailable."
"The requested Payment Method is not available.","The requested Payment Method is not available."
"Title","Title"
"Trial Billing Amount","Trial Billing Amount"
"Trial Billing Frequency","Trial Billing Frequency"
"Trial Billing Period Unit","Trial Billing Period Unit"
"Trial Period","Trial Period"
"Trial billing amount is wrong.","Trial billing amount is wrong."
"Trial billing period unit is wrong.","Trial billing period unit is wrong."
"Trial period frequency is wrong.","Trial period frequency is wrong."
"Trial period max cycles is wrong.","Trial period max cycles is wrong."
"Two Weeks","Two Weeks"
"Unable to save Billing Agreement:","Unable to save Billing Agreement:"
"Unit for billing during the subscription period.","Unit for billing during the subscription period."
"Unsupported currency code: %s.","Unsupported currency code: %s."
"Void action is not available.","Void action is not available."
"Week","Week"
"What is this?","What is this?"
"Whether to suspend the payment profile if the initial fee fails or add it to the outstanding balance.","Whether to suspend the payment profile if the initial fee fails or add it to the outstanding balance."
"Wrong %s specified.","Wrong %s specified."
"Wrong or empty billing amount specified.","Wrong or empty billing amount specified."
"Year","Year"
"Zero Subtotal Checkout","Zero Subtotal Checkout"
