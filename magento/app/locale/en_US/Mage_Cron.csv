"10 minutes","10 minutes"
"15 minutes","15 minutes"
"20 minutes","20 minutes"
"30 minutes","30 minutes"
"5 minutes","5 minutes"
"Cron (Scheduled Tasks) - all the times are in minutes","Cron (Scheduled Tasks) - all the times are in minutes"
"Daily","Daily"
"Failure History Lifetime","Failure History Lifetime"
"For correct URLs generated during cron runs please make sure that Web > Secure and Unsecure Base URLs are explicitly set.","For correct URLs generated during cron runs please make sure that Web > Secure and Unsecure Base URLs are explicitly set."
"Generate Schedules Every","Generate Schedules Every"
"History Cleanup Every","History Cleanup Every"
"Hourly","Hourly"
"Invalid callback: %s::%s does not exist","Invalid callback: %s::%s does not exist"
"Invalid model/method definition, expecting ""model/class::method"".","Invalid model/method definition, expecting ""model/class::method""."
"Minute Intervals","Minute Intervals"
"Missed if Not Run Within","Missed if Not Run Within"
"Monthly","Monthly"
"No callbacks found","No callbacks found"
"Schedule Ahead for","Schedule Ahead for"
"Success History Lifetime","Success History Lifetime"
"Too late for the schedule.","Too late for the schedule."
"Unable to delete the cron task.","Unable to delete the cron task."
"Unable to save the cron expression.","Unable to save the cron expression."
"Weekly","Weekly"
