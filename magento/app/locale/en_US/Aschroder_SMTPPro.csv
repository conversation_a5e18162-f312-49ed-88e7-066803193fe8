"SMTPPro - Email Log","SMTPPro - Email Log"
"SMTP Pro Email Settings","SMTP Pro Email Settings"
"Email Log","Email Log"
"Aschroder Extensions","Aschroder Extensions"
"SMTP Pro","SMTP Pro"
"General Settings","General Settings"
"<div style='background-color: #efefef;margin-bottom: 10px;height: 40px;'> <img style='float:left;width: 150px;' src='http://www.aschroder.com/smtppro-logo.png' /> <span style='float:left;font-size: 20px; margin:10px;'>SMTP Pro Email Extension</span> </div> Configure your SMTP connection below. If you have any questions or would like any help please visit <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>.","<div style='background-color: #efefef;margin-bottom: 10px;height: 40px;'> <img style='float:left;width: 150px;' src='http://www.aschroder.com/smtppro-logo.png' /> <span style='float:left;font-size: 20px; margin:10px;'>SMTP Pro Email Extension</span> </div> Configure your SMTP connection below. If you have any questions or would like any help please visit <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>."
"Email Connection","Email Connection"
"Google Apps Email Address","Google Apps Email Address"
"Google Apps Password","Google Apps Password"
"Input your Google Apps or Gmail username and password here. For configuration recommendations please see the guide at <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>","Input your Google Apps or Gmail username and password here. For configuration recommendations please see the guide at <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>"
"SendGrid Username","SendGrid Username"
"SendGrid Password","SendGrid Password"
"Input your SendGrid username and password here. For more information visit <a href='http://sendgrid.com' target='_blank'>SendGrid</a>","Input your SendGrid username and password here. For more information visit <a href='http://sendgrid.com' target='_blank'>SendGrid</a>"
"Amazon SES Access Key","Amazon SES Access Key"
"Amazon SES Secret Key","Amazon SES Secret Key"
"Amazon SES support in SMTP Pro is limited and best suited to development and testing purposes. For a full integration with region selection, error/bounce logging and send statistics please see the premium extension: <a href='http://magesend.com' target='_blank'>MageSend</a>","Amazon SES support in SMTP Pro is limited and best suited to development and testing purposes. For a full integration with region selection, error/bounce logging and send statistics please see the premium extension: <a href='http://magesend.com' target='_blank'>MageSend</a>"
"Authentication","Authentication"
"Username","Username"
"Password","Password"
"Host","Host"
"Port","Port"
"SSL Security","SSL Security"
"Custom SMTP servers can be configured in this section. For more information about these configuration options and troubleshooting advice please see <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>","Custom SMTP servers can be configured in this section. For more information about these configuration options and troubleshooting advice please see <a href='http://magesmtppro.com' target='_blank'>magesmtppro.com</a>"
"Logging and Debugging","Logging and Debugging"
"Please only use these settings if you are a software developer or server admin.","Please only use these settings if you are a software developer or server admin."
"Log Emails","Log Emails"
"This will log all outbound emails. View from System->Tools->SMTPPro - Email Log.","This will log all outbound emails. View from System->Tools->SMTPPro - Email Log."
"Clean Email Logs","Clean Email Logs"
"If this is set to yes, old entries will be deleted from email log. Cron is required and log cleaning must be enabled in system/log/enabled for this to work.","If this is set to yes, old entries will be deleted from email log. Cron is required and log cleaning must be enabled in system/log/enabled for this to work."
"Email Log Days Kept","Email Log Days Kept"
"Enable Debug Logging","Enable Debug Logging"
"If yes, a log file will be written with debug information to file var/log/aschroder_smtppro.log.","If yes, a log file will be written with debug information to file var/log/aschroder_smtppro.log."
"Save settings before running this test.","Save settings before running this test."
"Compatible Email Services","Compatible Email Services"
"Running SMTP Pro Self Test","Running SMTP Pro Self Test"
"SMTP Pro Self Test Results","SMTP Pro Self Test Results"
"Extension disabled, cannot run test.","Extension disabled, cannot run test."
"Checking config re-writes have not clashed.","Checking config re-writes have not clashed."
"Detected overwrite conflict: %s","Detected overwrite conflict: %s"
"Raw connection test for SMTP options.","Raw connection test for SMTP options."
"Complete","Complete"
"Failed to connect to SMTP server. Reason: ","Failed to connect to SMTP server. Reason: "
"This extension requires an outbound SMTP connection on port: ","This extension requires an outbound SMTP connection on port: "
"Connection to Host SMTP server successful","Connection to Host SMTP server successful"
"Skipping raw connection test for non-SMTP options.","Skipping raw connection test for non-SMTP options."
"Test Email From SMTP Pro Magento Extension","Test Email From SMTP Pro Magento Extension"
"Actual email sending test...","Actual email sending test..."
" from: "," from: "
"Test email was sent successfully","Test email was sent successfully"
"Failed to find transport for test.","Failed to find transport for test."
"Unable to send test email.","Unable to send test email."
"Exception message was: %s","Exception message was: %s"
"Please check the user guide for frequent error messages and their solutions.","Please check the user guide for frequent error messages and their solutions."
"Test email was not sent successfully: %s","Test email was not sent successfully: %s"
"See exception log for more details.","See exception log for more details."
"Checking that a template exists for the default locale and that email communications are enabled...","Checking that a template exists for the default locale and that email communications are enabled..."
"Default templates exist.","Default templates exist."
"Email communications are enabled.","Email communications are enabled."
"Default templates exist and email communications are enabled.","Default templates exist and email communications are enabled."
"Could not find default template, or template not valid, or email communications disabled in Advanced > System settings.","Could not find default template, or template not valid, or email communications disabled in Advanced > System settings."
"Please check that you have templates in place for your emails. These are in app/locale, or custom defined in System > Transaction Emails. Also check Advanced > System settings to ensure email communications are enabled.","Please check that you have templates in place for your emails. These are in app/locale, or custom defined in System > Transaction Emails. Also check Advanced > System settings to ensure email communications are enabled."
"Could not find default template, or template not valid, or email communications disabled in Advanced > System settings","Could not find default template, or template not valid, or email communications disabled in Advanced > System settings"
"Could not test default template validity.","Could not test default template validity."
"Please check that you have templates in place for your emails. These are in app/locale, or custom defined in System > Transaction Emails.","Please check that you have templates in place for your emails. These are in app/locale, or custom defined in System > Transaction Emails."
"Could not test default template validity: %s","Could not test default template validity: %s"
"Checking that tables are created...","Checking that tables are created..."
"Could not find required database tables.","Could not find required database tables."
"Please try to manually re-run the table creation script. For assistance please contact us.","Please try to manually re-run the table creation script. For assistance please contact us."
"Required database tables exist.",""
"Testing complete, if you are still experiencing difficulties please visit <a target='_blank' href='http://magesmtppro.com'>the support page</a> or contact me via <a target='_blank' href='mailto:<EMAIL>'><EMAIL></a> for support.","Testing complete, if you are still experiencing difficulties please visit <a target='_blank' href='http://magesmtppro.com'>the support page</a> or contact me via <a target='_blank' href='mailto:<EMAIL>'><EMAIL></a> for support."
"Testing failed,  please review the reported problems and if you need further help visit  <a target='_blank' href='http://magesmtppro.com'>the support page</a> or contact me via <a target='_blank' href='mailto:<EMAIL>'><EMAIL></a> for support.","Testing failed,  please review the reported problems and if you need further help visit  <a target='_blank' href='http://magesmtppro.com'>the support page</a> or contact me via <a target='_blank' href='mailto:<EMAIL>'><EMAIL></a> for support."
"Disabled","Disabled"
"MailUp Username","MailUp Username"
"MailUp Password","MailUp Password"
"Input your MailUp username and password here. For more information visit <a href='http://mailup.com' target='_blank'>MailUp</a>","Input your MailUp username and password here. For more information visit <a href='http://mailup.com' target='_blank'>MailUp</a>"
"Google Apps or Gmail","Google Apps or Gmail"
