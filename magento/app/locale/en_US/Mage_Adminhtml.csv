" The customer does not exist in the system anymore."," The customer does not exist in the system anymore."
" [deleted]"," [deleted]"
" and "," and "
"%s (Default Template from Locale)","%s (Default Template from Locale)"
"%s cache type(s) disabled.","%s cache type(s) disabled."
"%s cache type(s) enabled.","%s cache type(s) enabled."
"%s cache type(s) refreshed.","%s cache type(s) refreshed."
"(For Type ""Local Server"" need to use relative path to Magento install var/export or var/import, e.g. var/export, var/import, var/export/some/dir, var/import/some/dir)","(For Type ""Local Server"" need to use relative path to Magento install var/export or var/import, e.g. var/export, var/import, var/export/some/dir, var/import/some/dir)"
"(For US 2-letter state names)","(For US 2-letter state names)"
"(If left empty will be auto-generated)","(If left empty will be auto-generated)"
"(Leave empty for first spreadsheet)","(Leave empty for first spreadsheet)"
"(Products will be added/updated to this store if 'store' column is blank or missing in the import file.)","(Products will be added/updated to this store if 'store' column is blank or missing in the import file.)"
"(Shift-)Click or drag to change value","(Shift-)Click or drag to change value"
"(Starting with)","(Starting with)"
"(When 'No', only mapped fields will be imported. When mapping, use 'column1', 'column2', etc.)","(When 'No', only mapped fields will be imported. When mapping, use 'column1', 'column2', etc.)"
"(You have to increase php memory_limit before changing this value)","(You have to increase php memory_limit before changing this value)"
"(\\t for tab)","(\\t for tab)"
"* - If indexing is in progress, it will be killed and new indexing process will start.","* - If indexing is in progress, it will be killed and new indexing process will start."
"* Required Fields","* Required Fields"
"- Click on any of the time parts to increase it","- Click on any of the time parts to increase it"
"- Hold mouse button on any of the above buttons for faster selection.","- Hold mouse button on any of the above buttons for faster selection."
"- Use the %s buttons to select month","- Use the %s buttons to select month"
"- Use the %s, %s buttons to select month","- Use the %s, %s buttons to select month"
"- Use the %s, %s buttons to select year","- Use the %s, %s buttons to select year"
"- or Shift-click to decrease it","- or Shift-click to decrease it"
"- or click and drag for faster selection.","- or click and drag for faster selection."
"-- Not Selected --","-- Not Selected --"
"-- Please Select --","-- Please Select --"
"-- Please Select Billing Agreement--","-- Please Select Billing Agreement--"
"-- Please Select a Category --","-- Please Select a Category --"
"-- Please select --","-- Please select --"
"--Please Select--","--Please Select--"
"1 Hour","1 Hour"
"12 Hours","12 Hours"
"12h AM/PM","12h AM/PM"
"2 Hours","2 Hours"
"24 Hours","24 Hours"
"24h","24h"
"2YTD","2YTD"
"6 Hours","6 Hours"
"<h1 class=""page-heading"">404 Error</h1><p>Page not found.</p>","<h1 class=""page-heading"">404 Error</h1><p>Page not found.</p>"
"<strong>%s</strong> requests access to your account","<strong>%s</strong> requests access to your account"
"<strong>Attention</strong>: Captcha is case sensitive.","<strong>Attention</strong>: Captcha is case sensitive."
"A user with the same user name or email already exists.","A user with the same user name or email already exists."
"API Key","API Key"
"API Key Confirmation","API Key Confirmation"
"ASCII","ASCII"
"Abandoned Carts","Abandoned Carts"
"About the calendar","About the calendar"
"Access Denied","Access Denied"
"Access denied","Access denied"
"Access denied.","Access denied."
"Account Created in:","Account Created in:"
"Account Created on (%s):","Account Created on (%s):"
"Account Created on:","Account Created on:"
"Account Information","Account Information"
"Account Status","Account Status"
"Account status","Account status"
"Action","Action"
"Actions","Actions"
"Actions XML","Actions XML"
"Active","Active"
"Add","Add"
"Add Exception","Add Exception"
"Add Field Mapping","Add Field Mapping"
"Add Field with URL:","Add Field with URL:"
"Add New","Add New"
"Add New Block","Add New Block"
"Add New Image","Add New Image"
"Add New Profile","Add New Profile"
"Add New Role","Add New Role"
"Add New Template","Add New Template"
"Add New URL Rewrite","Add New URL Rewrite"
"Add New User","Add New User"
"Add New Variable","Add New Variable"
"Add Products","Add Products"
"Add URL Rewrite","Add URL Rewrite"
"Add URL Rewrite for a Category","Add URL Rewrite for a Category"
"Add URL Rewrite for a Product","Add URL Rewrite for a Product"
"Add after","Add after"
"Add new variable","Add new variable"
"Add to category","Add to category"
"Additional Cache Management","Additional Cache Management"
"Address Type:","Address Type:"
"Admin","Admin"
"Advanced Admin Section","Advanced Admin Section"
"Advanced Profiles","Advanced Profiles"
"Advanced Section","Advanced Section"
"After authorization application will have access to you account.","After authorization application will have access to you account."
"All","All"
"All Allowed Countries","All Allowed Countries"
"All Cache","All Cache"
"All Files","All Files"
"All Reviews","All Reviews"
"All Store Views","All Store Views"
"All Storefronts","All Storefronts"
"All Tags","All Tags"
"All Websites","All Websites"
"All countries","All countries"
"All fields","All fields"
"All possible rates were fetched, please click on ""Save"" to apply","All possible rates were fetched, please click on ""Save"" to apply"
"All rates were fetched, please click on ""Save"" to apply","All rates were fetched, please click on ""Save"" to apply"
"All valid rates have been saved.","All valid rates have been saved."
"Allowed","Allowed"
"Already in category","Already in category"
"Always (during development)","Always (during development)"
"Amounts","Amounts"
"An error has occured while syncronizing media storages.","An error has occured while syncronizing media storages."
"An error occurred while adding condition.","An error occurred while adding condition."
"An error occurred while clearing the JavaScript/CSS cache.","An error occurred while clearing the JavaScript/CSS cache."
"An error occurred while clearing the configurable swatches image cache.","An error occurred while clearing the configurable swatches image cache."
"An error occurred while clearing the image cache.","An error occurred while clearing the image cache."
"An error occurred while deleting URL Rewrite.","An error occurred while deleting URL Rewrite."
"An error occurred while deleting email template data. Please review log and try again.","An error occurred while deleting email template data. Please review log and try again."
"An error occurred while deleting record(s).","An error occurred while deleting record(s)."
"An error occurred while deleting this role.","An error occurred while deleting this role."
"An error occurred while deleting this set.","An error occurred while deleting this set."
"An error occurred while deleting this template.","An error occurred while deleting this template."
"An error occurred while finishing process. Please refresh the cache","An error occurred while finishing process. Please refresh the cache"
"An error occurred while logging in.","An error occurred while logging in."
"An error occurred while rebuilding the CatalogInventory Stock Status.","An error occurred while rebuilding the CatalogInventory Stock Status."
"An error occurred while rebuilding the catalog index.","An error occurred while rebuilding the catalog index."
"An error occurred while rebuilding the flat catalog category.","An error occurred while rebuilding the flat catalog category."
"An error occurred while rebuilding the flat product catalog.","An error occurred while rebuilding the flat product catalog."
"An error occurred while rebuilding the search index.","An error occurred while rebuilding the search index."
"An error occurred while refreshing roles.","An error occurred while refreshing roles."
"An error occurred while refreshing the Catalog Rewrites.","An error occurred while refreshing the Catalog Rewrites."
"An error occurred while refreshing the Layered Navigation indices.","An error occurred while refreshing the Layered Navigation indices."
"An error occurred while refreshing the catalog rewrites.","An error occurred while refreshing the catalog rewrites."
"An error occurred while refreshing the layered navigation indices.","An error occurred while refreshing the layered navigation indices."
"An error occurred while saving URL Rewrite.","An error occurred while saving URL Rewrite."
"An error occurred while saving account.","An error occurred while saving account."
"An error occurred while saving review.","An error occurred while saving review."
"An error occurred while saving the customer.","An error occurred while saving the customer."
"An error occurred while saving this configuration:","An error occurred while saving this configuration:"
"An error occurred while saving this role.","An error occurred while saving this role."
"An error occurred while saving this template.","An error occurred while saving this template."
"An error occurred while updating the selected review(s).","An error occurred while updating the selected review(s)."
"Any","Any"
"Any Attribute Set","Any Attribute Set"
"Any Group","Any Group"
"Any Status","Any Status"
"Any Store","Any Store"
"Any Type","Any Type"
"Any Visibility","Any Visibility"
"Any data created since the backup was made will be lost including admin users, customers and orders.","Any data created since the backup was made will be lost including admin users, customers and orders."
"Archive file name:","Archive file name:"
"Are you sure that you want to delete this template?","Are you sure that you want to delete this template?"
"Are you sure that you want to strip tags?","Are you sure that you want to strip tags?"
"Are you sure you want to do this?","Are you sure you want to do this?"
"Are you sure you want to proceed?","Are you sure you want to proceed?"
"Area","Area"
"As low as:","As low as:"
"Assigned","Assigned"
"Associated Tags","Associated Tags"
"Attribute Set Name:","Attribute Set Name:"
"Attributes","Attributes"
"Authorization confirmed","Authorization confirmed"
"Authorize","Authorize"
"Authorize application","Authorize application"
"Authorized OAuth Tokens","Authorized OAuth Tokens"
"Automatic (equalize price ranges)","Automatic (equalize price ranges)"
"Automatic (equalize product counts)","Automatic (equalize product counts)"
"Average Order Amount","Average Order Amount"
"Average Orders","Average Orders"
"BINARY","BINARY"
"Back","Back"
"Back to Login","Back to Login"
"Backup","Backup"
"Backup Name","Backup Name"
"Backup options","Backup options"
"Backups","Backups"
"Base currency","Base currency"
"Bcc","Bcc"
"Bestsellers","Bestsellers"
"Billing Address","Billing Address"
"Billing Address: ","Billing Address: "
"Billing Agreement","Billing Agreement"
"Billing Agreements","Billing Agreements"
"Block Details","Block Details"
"Block Information","Block Information"
"Block Name","Block Name"
"Block Name is disallowed.","Block Name is disallowed."
"Block Name is incorrect.","Block Name is incorrect."
"Block Name is required field.","Block Name is required field."
"Block has been deleted.","Block has been deleted."
"Blocks","Blocks"
"Both (without and with tax)","Both (without and with tax)"
"Both IPN and PDT","Both IPN and PDT"
"Browse Files...","Browse Files..."
"Bundle with dynamic pricing cannot include custom defined options. Options will not be saved.","Bundle with dynamic pricing cannot include custom defined options. Options will not be saved."
"By Email","By Email"
"By IP","By IP"
"By IP and Email","By IP and Email"
"CMS","CMS"
"CRITICAL","CRITICAL"
"CSV","CSV"
"CSV / Tab separated","CSV / Tab separated"
"Cache Control","Cache Control"
"Cache Control (beta)","Cache Control (beta)"
"Cache Management","Cache Management"
"Cache Type","Cache Type"
"Cancel","Cancel"
"Cancel detach","Cancel detach"
"Cannot add new comment.","Cannot add new comment."
"Cannot add tracking number.","Cannot add tracking number."
"Cannot create an invoice without products.","Cannot create an invoice without products."
"Cannot create credit memo for the order.","Cannot create credit memo for the order."
"Cannot delete the design change.","Cannot delete the design change."
"Cannot delete tracking number.","Cannot delete tracking number."
"Cannot do shipment for the order separately from invoice.","Cannot do shipment for the order separately from invoice."
"Cannot do shipment for the order.","Cannot do shipment for the order."
"Cannot initialize shipment for adding tracking number.","Cannot initialize shipment for adding tracking number."
"Cannot initialize shipment for delete tracking number.","Cannot initialize shipment for delete tracking number."
"Cannot load track with retrieving identifier.","Cannot load track with retrieving identifier."
"Cannot retrieve tracking number detail.","Cannot retrieve tracking number detail."
"Cannot save shipment.","Cannot save shipment."
"Cannot save the credit memo.","Cannot save the credit memo."
"Cannot send shipment information.","Cannot send shipment information."
"Cannot update item quantity.","Cannot update item quantity."
"Cannot update the item\'s quantity.","Cannot update the item\'s quantity."
"Card Verification Number","Card Verification Number"
"Catalog","Catalog"
"Catalog Price Rules","Catalog Price Rules"
"Catalog Rewrites","Catalog Rewrites"
"Categories","Categories"
"Category","Category"
"Category:","Category:"
"Chart is disabled. If you want to enable chart, click <a href=""%s"">here</a>.","Chart is disabled. If you want to enable chart, click <a href=""%s"">here</a>."
"Checkbox","Checkbox"
"Child Transactions","Child Transactions"
"Choose Store View:","Choose Store View:"
"Choose an attribute","Choose an attribute"
"Chosen category does not associated with any website, so url rewrite is not possible.","Chosen category does not associated with any website, so url rewrite is not possible."
"Chosen product does not associated with any website, so url rewrite is not possible.","Chosen product does not associated with any website, so url rewrite is not possible."
"Clear","Clear"
"Close","Close"
"Code","Code"
"Comment text field cannot be empty.","Comment text field cannot be empty."
"Complete","Complete"
"Config form fieldset clone model required to be able to clone fields","Config form fieldset clone model required to be able to clone fields"
"Configuration","Configuration"
"Confirm New Password","Confirm New Password"
"Confirm token authorization Pop Up for admin","Confirm token authorization Pop Up for admin"
"Confirm token authorization for admin","Confirm token authorization for admin"
"Confirmation Of Authorization","Confirmation Of Authorization"
"Confirmed email:","Confirmed email:"
"Connect with the Magento Community","Connect with the Magento Community"
"Continue","Continue"
"Convert to Plain Text","Convert to Plain Text"
"Cookie (unsafe)","Cookie (unsafe)"
"Country","Country"
"Country:","Country:"
"Coupons","Coupons"
"Create","Create"
"Create DB Backup","Create DB Backup"
"Create New Attribute","Create New Attribute"
"Create URL Rewrite:","Create URL Rewrite:"
"Created At","Created At"
"Creation date","Creation date"
"Credit Card %s","Credit Card %s"
"Credit Card Number","Credit Card Number"
"Credit Card Number: xxxx-%s","Credit Card Number: xxxx-%s"
"Credit Card Type","Credit Card Type"
"Credit Card Type: %s","Credit Card Type: %s"
"Credit Memo History","Credit Memo History"
"Credit Memo Totals","Credit Memo Totals"
"Credit Memos","Credit Memos"
"Credit memo #%s comment added","Credit memo #%s comment added"
"Credit memo #%s created","Credit memo #%s created"
"Credit memo\'s total must be positive.","Credit memo\'s total must be positive."
"Currency","Currency"
"Currency ""%s"" is used as %s in %s.","Currency ""%s"" is used as %s in %s."
"Currency Information","Currency Information"
"Currency Setup Section","Currency Setup Section"
"Currency doesn\'t exist.","Currency doesn\'t exist."
"Current Admin Password","Current Admin Password"
"Current Configuration Scope:","Current Configuration Scope:"
"Current Month","Current Month"
"Custom","Custom"
"Custom Variable ""%s""","Custom Variable ""%s"""
"Custom Variables","Custom Variables"
"Customer","Customer"
"Customer Group:","Customer Group:"
"Customer Groups","Customer Groups"
"Customer Name","Customer Name"
"Customer Reviews","Customer Reviews"
"Customer Shopping Carts","Customer Shopping Carts"
"Customer Since:","Customer Since:"
"Customer Tax Classes","Customer Tax Classes"
"Customer with the same email already exists.","Customer with the same email already exists."
"Customers","Customers"
"Customers by Number of Orders","Customers by Number of Orders"
"Customers by Orders Total","Customers by Orders Total"
"DHTML Date/Time Selector","DHTML Date/Time Selector"
"Dashboard","Dashboard"
"Data Format","Data Format"
"Data transfer:","Data transfer:"
"Database","Database"
"Dataflow - Advanced Profiles","Dataflow - Advanced Profiles"
"Dataflow - Profiles","Dataflow - Profiles"
"Date","Date"
"Date & Time","Date & Time"
"Date Added","Date Added"
"Date Updated","Date Updated"
"Date selection:","Date selection:"
"Date selector","Date selector"
"Day","Day"
"Decimal separator:","Decimal separator:"
"Default (Admin) Values","Default (Admin) Values"
"Default Billing Address","Default Billing Address"
"Default Config","Default Config"
"Default Template from Locale","Default Template from Locale"
"Default Values","Default Values"
"Default display currency ""%s"" is not available in allowed currencies.","Default display currency ""%s"" is not available in allowed currencies."
"Default scope","Default scope"
"Delete","Delete"
"Delete %s","Delete %s"
"Delete %s '%s'","Delete %s '%s'"
"Delete Block","Delete Block"
"Delete File","Delete File"
"Delete Image","Delete Image"
"Delete Profile","Delete Profile"
"Delete Role","Delete Role"
"Delete Store","Delete Store"
"Delete Store View","Delete Store View"
"Delete Template","Delete Template"
"Delete User","Delete User"
"Delete Variable","Delete Variable"
"Delete Website","Delete Website"
"Description","Description"
"Design","Design"
"Design Section","Design Section"
"Detach from category","Detach from category"
"Details","Details"
"Developer Section","Developer Section"
"Direction:","Direction:"
"Disable","Disable"
"Disabled","Disabled"
"Disallowed block name for frontend.","Disallowed block name for frontend."
"Display %s first","Display %s first"
"Display default currency","Display default currency"
"Distributed under GNU LGPL. See %s for details.","Distributed under GNU LGPL. See %s for details."
"Do you really want to KILL parallel process and start new indexing process?","Do you really want to KILL parallel process and start new indexing process?"
"Do you really want to proceed?","Do you really want to proceed?"
"Download","Download"
"Download SKUs","Download SKUs"
"Downloads","Downloads"
"Drag to move","Drag to move"
"Drop-down","Drop-down"
"Edit","Edit"
"Edit Block","Edit Block"
"Edit Block '%s'","Edit Block '%s'"
"Edit Design Change","Edit Design Change"
"Edit Email Template","Edit Email Template"
"Edit Order","Edit Order"
"Edit Poll","Edit Poll"
"Edit Queue","Edit Queue"
"Edit Review","Edit Review"
"Edit Role","Edit Role"
"Edit Store View","Edit Store View"
"Edit System Template","Edit System Template"
"Edit Template","Edit Template"
"Edit URL Rewrite","Edit URL Rewrite"
"Edit User","Edit User"
"Edit User '%s'","Edit User '%s'"
"Edit Variable","Edit Variable"
"Edit Variable '%s'","Edit Variable '%s'"
"Edit Website","Edit Website"
"Email","Email"
"Email Address:","Email Address:"
"Email to a Friend","Email to a Friend"
"Email:","Email:"
"Enable","Enable"
"Enabled","Enabled"
"Enclose Values In:","Enclose Values In:"
"Enter a comma separated list of SKUs that will always be displayed at the top of your category","Enter a comma separated list of SKUs that will always be displayed at the top of your category"
"Entity Attributes","Entity Attributes"
"Entity Type","Entity Type"
"Entity type:","Entity type:"
"Error","Error"
"Excel XML","Excel XML"
"Excl. Tax","Excl. Tax"
"Excl. Tax:","Excl. Tax:"
"Exclude media folder from backup","Exclude media folder from backup"
"Expiration Date","Expiration Date"
"Expiration Date: %s/%s","Expiration Date: %s/%s"
"Export","Export"
"Export CSV","Export CSV"
"Export Filters","Export Filters"
"Export to:","Export to:"
"Export:","Export:"
"FTP Host","FTP Host"
"FTP Host[:Port]","FTP Host[:Port]"
"FTP Login","FTP Login"
"FTP Password","FTP Password"
"FTP credentials","FTP credentials"
"Failed","Failed"
"Failed to add a product to cart by id ""%s"".","Failed to add a product to cart by id ""%s""."
"Failed to cancel the billing agreement.","Failed to cancel the billing agreement."
"Failed to clear the JavaScript/CSS cache.","Failed to clear the JavaScript/CSS cache."
"Failed to delete the billing agreement.","Failed to delete the billing agreement."
"Failed to update the profile.","Failed to update the profile."
"Field","Field"
"Field Mapping","Field Mapping"
"File","File"
"File Information","File Information"
"File System","File System"
"File mode","File mode"
"File name:","File name:"
"File size should be more than 0 bytes","File size should be more than 0 bytes"
"Finished profile execution.","Finished profile execution."
"First Invoice Created Date","First Invoice Created Date"
"First Name","First Name"
"First Name is required field.","First Name is required field."
"First Name:","First Name:"
"Fixed","Fixed"
"Flush Catalog Images Cache","Flush Catalog Images Cache"
"Flush JavaScript/CSS Cache","Flush JavaScript/CSS Cache"
"Flush Swatch Images Cache","Flush Swatch Images Cache"
"For category","For category"
"For latest version visit: %s","For latest version visit: %s"
"For product","For product"
"Forgot Admin Password","Forgot Admin Password"
"Forgot your password?","Forgot your password?"
"Forgot your user name or password?","Forgot your user name or password?"
"From","From"
"GLOBAL","GLOBAL"
"Gb","Gb"
"General Information","General Information"
"General Section","General Section"
"Get Image Base64","Get Image Base64"
"Get help for this page","Get help for this page"
"Give the verifier code to application administrator","Give the verifier code to application administrator"
"Global Attribute","Global Attribute"
"Global Record Search","Global Record Search"
"Global Search","Global Search"
"Go Today","Go Today"
"Go to Top","Go to Top"
"Go to messages inbox","Go to messages inbox"
"Go to notifications","Go to notifications"
"Google Base","Google Base"
"Google Content","Google Content"
"Google Sitemaps","Google Sitemaps"
"Grand Total","Grand Total"
"Grid (default) / List","Grid (default) / List"
"Grid Only","Grid Only"
"Group:","Group:"
"Guest","Guest"
"HTTP (unsecure)","HTTP (unsecure)"
"HTTPS (SSL)","HTTPS (SSL)"
"Help Us Keep Magento Healthy - Report All Bugs","Help Us Keep Magento Healthy - Report All Bugs"
"Helper attributes should not be used in custom layout updates.","Helper attributes should not be used in custom layout updates."
"Helper for options rendering doesn't implement required interface.","Helper for options rendering doesn't implement required interface."
"Hero Products","Hero Products"
"Home","Home"
"ID","ID"
"ID Path","ID Path"
"IP Address","IP Address"
"IPN (Instant Payment Notification) Only","IPN (Instant Payment Notification) Only"
"Id","Id"
"If there is an account associated with %s you will receive an email with a link to reset your password.","If there is an account associated with %s you will receive an email with a link to reset your password."
"If this message persists, please contact the store owner.","If this message persists, please contact the store owner."
"If you do not specify an option value for a specific store view then the default (Admin) value will be used.","If you do not specify an option value for a specific store view then the default (Admin) value will be used."
"Images (.gif, .jpg, .png)","Images (.gif, .jpg, .png)"
"Images Cache","Images Cache"
"Import","Import"
"Import Service","Import Service"
"Import and Export","Import and Export"
"Import and Export Tax Rates","Import and Export Tax Rates"
"Import/Export","Import/Export"
"Import/Export Advanced","Import/Export Advanced"
"Import/Export Profile","Import/Export Profile"
"Important: ","Important: "
"Imported <strong>%s</strong> records","Imported <strong>%s</strong> records"
"In","In"
"In Database:","In Database:"
"In File:","In File:"
"Inactive","Inactive"
"Incl. Tax","Incl. Tax"
"Incl. Tax:","Incl. Tax:"
"Incoming Message","Incoming Message"
"Insert Variable...","Insert Variable..."
"Interactive","Interactive"
"Interface Locale: %s","Interface Locale: %s"
"Invalid Form Key. Please refresh the page.","Invalid Form Key. Please refresh the page."
"Invalid Import Service Specified","Invalid Import Service Specified"
"Invalid POST data (please check post_max_size and upload_max_filesize settings in your php.ini file).","Invalid POST data (please check post_max_size and upload_max_filesize settings in your php.ini file)."
"Invalid Secret Key. Please refresh the page.","Invalid Secret Key. Please refresh the page."
"Invalid User Name or Password.","Invalid User Name or Password."
"Invalid data","Invalid data"
"Invalid directory: %s","Invalid directory: %s"
"Invalid email address ""%s"".","Invalid email address ""%s""."
"Invalid email address.","Invalid email address."
"Invalid file extension used for log file. Allowed file extensions: log, txt, html, csv","Invalid file extension used for log file. Allowed file extensions: log, txt, html, csv"
"Invalid file: %s","Invalid file: %s"
"Invalid input data for %s => %s rate","Invalid input data for %s => %s rate"
"Invalid parent block for this block","Invalid parent block for this block"
"Invalid parent block for this block.","Invalid parent block for this block."
"Invalid password reset token.","Invalid password reset token."
"Invalid response","Invalid response"
"Invalid sender name ""%s"". Please use only visible characters and spaces.","Invalid sender name ""%s"". Please use only visible characters and spaces."
"Invalid template data.","Invalid template data."
"Invalid template path used in layout update.","Invalid template path used in layout update."
"Invalid timezone","Invalid timezone"
"Invalidated","Invalidated"
"Inventory Stock Status","Inventory Stock Status"
"Invoice #%s comment added","Invoice #%s comment added"
"Invoice #%s created","Invoice #%s created"
"Invoice History","Invoice History"
"Invoice Totals","Invoice Totals"
"Invoice canceling error.","Invoice canceling error."
"Invoice capturing error.","Invoice capturing error."
"Invoice voiding error.","Invoice voiding error."
"Invoices","Invoices"
"Is Allowed","Is Allowed"
"Is Allowed is required field.","Is Allowed is required field."
"Is Closed","Is Closed"
"Is Default","Is Default"
"Issue Number","Issue Number"
"Items","Items"
"JavaScript seems to be disabled in your browser.","JavaScript seems to be disabled in your browser."
"JavaScript/CSS","JavaScript/CSS"
"JavaScript/CSS Cache","JavaScript/CSS Cache"
"Kb","Kb"
"Key %s does not contain scalar value","Key %s does not contain scalar value"
"Key %s does not exist in array","Key %s does not exist in array"
"Last 24 Hours","Last 24 Hours"
"Last 5 Orders","Last 5 Orders"
"Last 5 Search Terms","Last 5 Search Terms"
"Last 7 Days","Last 7 Days"
"Last Credit Memo Created Date","Last Credit Memo Created Date"
"Last Invoice Created Date","Last Invoice Created Date"
"Last Logged In (%s):","Last Logged In (%s):"
"Last Logged In:","Last Logged In:"
"Last Name","Last Name"
"Last Name is required field.","Last Name is required field."
"Last Name:","Last Name:"
"Last updated: %s. To refresh last day\'s <a href=""%s"">statistics</a>, click <a href=""%s"">here</a>.","Last updated: %s. To refresh last day\'s <a href=""%s"">statistics</a>, click <a href=""%s"">here</a>."
"Latest Message:","Latest Message:"
"Layered Navigation Indices","Layered Navigation Indices"
"Layered Navigation Indices were refreshed.","Layered Navigation Indices were refreshed."
"Leave empty to use tax identifier","Leave empty to use tax identifier"
"Lifetime Sales","Lifetime Sales"
"Lifetime statistics have been updated.","Lifetime statistics have been updated."
"Links","Links"
"Links with associated products will retain only after saving current product.","Links with associated products will retain only after saving current product."
"List (default) / Grid","List (default) / Grid"
"List Only","List Only"
"Load Template","Load Template"
"Load default template","Load default template"
"Loading...","Loading..."
"Local Server","Local Server"
"Local/Remote Server","Local/Remote Server"
"Locale","Locale"
"Log In","Log In"
"Log Out","Log Out"
"Log in to Admin Panel","Log in to Admin Panel"
"Log in to use <strong>%s</strong>","Log in to use <strong>%s</strong>"
"Log into Magento Admin Page","Log into Magento Admin Page"
"Logged in as %s","Logged in as %s"
"Login","Login"
"Low Stock","Low Stock"
"MAJOR","MAJOR"
"MINOR","MINOR"
"MS Excel XML","MS Excel XML"
"Magento Admin","Magento Admin"
"Magento Commerce - Administrative Panel","Magento Commerce - Administrative Panel"
"Magento Connect","Magento Connect"
"Magento Connect Manager","Magento Connect Manager"
"Magento Logo","Magento Logo"
"Magento is a trademark of Magento Inc. Copyright &copy; %s Magento Inc.","Magento is a trademark of Magento Inc. Copyright &copy; %s Magento Inc."
"Magento root directory","Magento root directory"
"Magento ver. %s","Magento ver. %s"
"Magento&trade; is a trademark of Magento Inc.<br/>Copyright &copy; %s Magento Inc.","Magento&trade; is a trademark of Magento Inc.<br/>Copyright &copy; %s Magento Inc."
"Make sure that data encoding in the file is consistent and saved in one of supported encodings (UTF-8 or ANSI).","Make sure that data encoding in the file is consistent and saved in one of supported encodings (UTF-8 or ANSI)."
"Manage Attribute Sets","Manage Attribute Sets"
"Manage Attributes","Manage Attributes"
"Manage Categories","Manage Categories"
"Manage Content","Manage Content"
"Manage Currency Rates","Manage Currency Rates"
"Manage Customers","Manage Customers"
"Manage Options (values of your attribute)","Manage Options (values of your attribute)"
"Manage Ratings","Manage Ratings"
"Manage Stores","Manage Stores"
"Manage Tax Rules","Manage Tax Rules"
"Manage Tax Zones and Rates","Manage Tax Zones and Rates"
"Manage Titles (Size, Color, etc.)","Manage Titles (Size, Color, etc.)"
"Manual","Manual"
"Mass Product Assignment","Mass Product Assignment"
"Matched Expression","Matched Expression"
"Maximum sender name length is 255. Please correct your settings.","Maximum sender name length is 255. Please correct your settings."
"Mb","Mb"
"Media (.avi, .flv, .swf)","Media (.avi, .flv, .swf)"
"Media storages synchronization has completed!","Media storages synchronization has completed!"
"Messages Inbox","Messages Inbox"
"Month","Month"
"Most Viewed","Most Viewed"
"Most Viewed Products","Most Viewed Products"
"Move to bottom","Move to bottom"
"Move to top","Move to top"
"Multiple Select","Multiple Select"
"My Account","My Account"
"My Applications","My Applications"
"N/A","N/A"
"NOTICE","NOTICE"
"Name","Name"
"Name on Card","Name on Card"
"Name on the Card: %s","Name on the Card: %s"
"Name:","Name:"
"Never (production)","Never (production)"
"New ","New "
"New API Key","New API Key"
"New Accounts","New Accounts"
"New Attribute","New Attribute"
"New Block","New Block"
"New Category","New Category"
"New Class","New Class"
"New Condition","New Condition"
"New Custom Variable","New Custom Variable"
"New Customer","New Customer"
"New Customers","New Customers"
"New Design Change","New Design Change"
"New Email Template","New Email Template"
"New Group","New Group"
"New Invoice","New Invoice"
"New Item Type","New Item Type"
"New Memo","New Memo"
"New Memo for #%s","New Memo for #%s"
"New Page","New Page"
"New Password","New Password"
"New Poll","New Poll"
"New Profile","New Profile"
"New Rate","New Rate"
"New Rating","New Rating"
"New Review","New Review"
"New Role","New Role"
"New Rule","New Rule"
"New Search","New Search"
"New Set","New Set"
"New Shipment","New Shipment"
"New Sitemap","New Sitemap"
"New Store View","New Store View"
"New System Template","New System Template"
"New Tag","New Tag"
"New Template","New Template"
"New User","New User"
"New Variable","New Variable"
"New Website","New Website"
"New attribute set mapping","New attribute set mapping"
"New block","New block"
"New password field cannot be empty.","New password field cannot be empty."
"Newsletter","Newsletter"
"Newsletter Problems","Newsletter Problems"
"Newsletter Queue","Newsletter Queue"
"Newsletter Subscribers","Newsletter Subscribers"
"Newsletter Templates","Newsletter Templates"
"Next month (hold for menu)","Next month (hold for menu)"
"Next year (hold for menu)","Next year (hold for menu)"
"No","No"
"No (price without tax)","No (price without tax)"
"No Data","No Data"
"No Data Found","No Data Found"
"No Products","No Products"
"No Templates Found","No Templates Found"
"No change","No change"
"No customer id defined.","No customer id defined."
"No information available.","No information available."
"No profile loaded...","No profile loaded..."
"No records found for this period.","No records found for this period."
"No records found.","No records found."
"No report code specified.","No report code specified."
"No search keywords.","No search keywords."
"No search modules were registered","No search modules were registered"
"No wishlist item id defined.","No wishlist item id defined."
"None","None"
"Not allowed","Not allowed"
"Note:","Note:"
"Notes","Notes"
"Notifications","Notifications"
"Number of Orders","Number of Orders"
"Number of Uses","Number of Uses"
"Number of Views","Number of Views"
"Number of records:","Number of records:"
"OAuth Consumers","OAuth Consumers"
"OAuth authorization for admin","OAuth authorization for admin"
"OAuth authorization simple for admin","OAuth authorization simple for admin"
"OK","OK"
"Old rate:","Old rate:"
"Once you log into your PayPal Advanced account, navigate to the Service Settings - Hosted Checkout Pages - Set Up menu and set the options described below","Once you log into your PayPal Advanced account, navigate to the Service Settings - Hosted Checkout Pages - Set Up menu and set the options described below"
"Once you log into your PayPal Payflow Link account, navigate to the Service Settings - Hosted Checkout Pages - Set Up menu and set the options described below","Once you log into your PayPal Payflow Link account, navigate to the Service Settings - Hosted Checkout Pages - Set Up menu and set the options described below"
"One or more media files failed to be synchronized during the media storages syncronization process. Refer to the log file for details.","One or more media files failed to be synchronized during the media storages syncronization process. Refer to the log file for details."
"One or more of the Cache Types are invalidated:","One or more of the Cache Types are invalidated:"
"Online Customers","Online Customers"
"Only Once (version upgrade)","Only Once (version upgrade)"
"Only attributes with scope ""Global"", input type ""Dropdown"" and Use To Create Configurable Product ""Yes"" are available.","Only attributes with scope ""Global"", input type ""Dropdown"" and Use To Create Configurable Product ""Yes"" are available."
"Only mapped fields","Only mapped fields"
"Optional","Optional"
"Options","Options"
"Options is required","Options is required"
"Options: ","Options: "
"Order","Order"
"Order #%s","Order #%s"
"Order #%s (%s)","Order #%s (%s)"
"Order Created Date","Order Created Date"
"Order ID","Order ID"
"Order Totals","Order Totals"
"Order Updated Date","Order Updated Date"
"Order Updated Date report is real-time, does not need statistics refreshing.","Order Updated Date report is real-time, does not need statistics refreshing."
"Orders","Orders"
"Original Magento attribute names in first row:","Original Magento attribute names in first row:"
"Out of stock","Out of stock"
"PDT (Payment Data Transfer) Only","PDT (Payment Data Transfer) Only"
"Package Extensions","Package Extensions"
"Pages","Pages"
"Parent Product Thumbnail","Parent Product Thumbnail"
"Parent Transaction ID","Parent Transaction ID"
"Passive mode","Passive mode"
"Password","Password"
"Password Confirmation","Password Confirmation"
"Password confirmation must be same as password.","Password confirmation must be same as password."
"Password must be at least of %d characters.","Password must be at least of %d characters."
"Password must include both numeric and alphabetic characters.","Password must include both numeric and alphabetic characters."
"Password:","Password:"
"Path:","Path:"
"Payment method instance is not available.","Payment method instance is not available."
"Payment method is not available.","Payment method is not available."
"Payment method must be specified.","Payment method must be specified."
"Pending Reviews","Pending Reviews"
"Pending Tags","Pending Tags"
"Per Item","Per Item"
"Per Order","Per Order"
"Percent","Percent"
"Performed At","Performed At"
"Period","Period"
"Permanent (301)","Permanent (301)"
"Permissions","Permissions"
"Personal Information","Personal Information"
"Phone:","Phone:"
"Please Select","Please Select"
"Please confirm site switching. All data that hasn\'t been saved will be lost.","Please confirm site switching. All data that hasn\'t been saved will be lost."
"Please enter 6 or more characters.","Please enter 6 or more characters."
"Please enter a number greater than 0 in this field.","Please enter a number greater than 0 in this field."
"Please enter a valid $ amount. For example $100.00.","Please enter a valid $ amount. For example $100.00."
"Please enter a valid URL. For example http://www.example.com or www.example.com","Please enter a valid URL. For example http://www.example.com or www.example.com"
"Please enter a valid URL. http:// is required","Please enter a valid URL. http:// is required"
"Please enter a valid credit card number.","Please enter a valid credit card number."
"Please enter a valid date.","Please enter a valid date."
"Please enter a valid email address. <NAME_EMAIL>.","Please enter a valid email address. <NAME_EMAIL>."
"Please enter a valid email.","Please enter a valid email."
"Please enter a valid number in this field.","Please enter a valid number in this field."
"Please enter a valid phone number. For example (************* or ************.","Please enter a valid phone number. For example (************* or ************."
"Please enter a valid social security number. For example ***********.","Please enter a valid social security number. For example ***********."
"Please enter a valid value from list","Please enter a valid value from list"
"Please enter a valid value, ex: 10,20,30","Please enter a valid value, ex: 10,20,30"
"Please enter a valid zip code.","Please enter a valid zip code."
"Please enter a valid zip code. For example 90602 or 90602-1234.","Please enter a valid zip code. For example 90602 or 90602-1234."
"Please enter another credit card number to complete your purchase.","Please enter another credit card number to complete your purchase."
"Please enter password","Please enter password"
"Please enter password to confirm rollback.","Please enter password to confirm rollback."
"Please enter valid password.","Please enter valid password."
"Please make sure that all global admin search modules are installed and activated.","Please make sure that all global admin search modules are installed and activated."
"Please make sure that your changes were saved before running the profile.","Please make sure that your changes were saved before running the profile."
"Please make sure your passwords match.","Please make sure your passwords match."
"Please note, this category is set to automatically sort. Any drag-and-drop positions will be reset.","Please note, this category is set to automatically sort. Any drag-and-drop positions will be reset."
"Please select State/Province.","Please select State/Province."
"Please select a customer.","Please select a customer."
"Please select a store.","Please select a store."
"Please select an option.","Please select an option."
"Please select catalog searches.","Please select catalog searches."
"Please select customer(s).","Please select customer(s)."
"Please select message(s).","Please select message(s)."
"Please select one of the above options.","Please select one of the above options."
"Please select one of the options.","Please select one of the options."
"Please select review(s).","Please select review(s)."
"Please select tag(s).","Please select tag(s)."
"Please specify backup creation options","Please specify backup creation options"
"Please specify the admin custom URL.","Please specify the admin custom URL."
"Please try to logout and sign in again.","Please try to logout and sign in again."
"Please type the letters from the image:","Please type the letters from the image:"
"Please use in this field only ""a-z,0-9,_"".","Please use in this field only ""a-z,0-9,_""."
"Please use letters only (a-z) in this field.","Please use letters only (a-z) in this field."
"Please use numbers only in this field. Please avoid spaces or other characters such as dots or commas.","Please use numbers only in this field. Please avoid spaces or other characters such as dots or commas."
"Please use only letters (a-z or A-Z), numbers (0-9) or spaces in this field.","Please use only letters (a-z or A-Z), numbers (0-9) or spaces in this field."
"Please use only letters (a-z) or numbers (0-9) only in this field. No spaces or other characters are allowed.","Please use only letters (a-z) or numbers (0-9) only in this field. No spaces or other characters are allowed."
"Please use only letters (a-z) or numbers (0-9) or spaces and # only in this field.","Please use only letters (a-z) or numbers (0-9) or spaces and # only in this field."
"Please use this date format: dd/mm/yyyy. For example 17/03/2006 for the 17th of March, 2006.","Please use this date format: dd/mm/yyyy. For example 17/03/2006 for the 17th of March, 2006."
"Please wait","Please wait"
"Please wait while the indexes are being refreshed.","Please wait while the indexes are being refreshed."
"Please wait, loading...","Please wait, loading..."
"Please wait...","Please wait..."
"Please, add some answers to this poll first.","Please, add some answers to this poll first."
"Please, select ""Visible in Stores"" for this poll first.","Please, select ""Visible in Stores"" for this poll first."
"Poll Manager","Poll Manager"
"Polls","Polls"
"Popular","Popular"
"Position","Position"
"Position of Watermark for %s","Position of Watermark for %s"
"Pregenerated configurable swatches image files.","Pregenerated configurable swatches image files."
"Pregenerated product images files.","Pregenerated product images files."
"Prev. month (hold for menu)","Prev. month (hold for menu)"
"Prev. year (hold for menu)","Prev. year (hold for menu)"
"Preview","Preview"
"Preview Template","Preview Template"
"Price","Price"
"Price alert subscription was saved.","Price alert subscription was saved."
"Price:","Price:"
"Processed <strong>%s%% %s/%d</strong> records","Processed <strong>%s%% %s/%d</strong> records"
"Processing","Processing"
"Product","Product"
"Product Name","Product Name"
"Product Reviews","Product Reviews"
"Product Tax Classes","Product Tax Classes"
"Product Thumbnail Itself","Product Thumbnail Itself"
"Product is not loaded.","Product is not loaded."
"Product not available in this website","Product not available in this website"
"Product:","Product:"
"Products","Products"
"Products Bestsellers Report","Products Bestsellers Report"
"Products Most Viewed Report","Products Most Viewed Report"
"Products Ordered","Products Ordered"
"Products in Carts","Products in Carts"
"Profile Action","Profile Action"
"Profile Actions XML","Profile Actions XML"
"Profile Direction","Profile Direction"
"Profile History","Profile History"
"Profile Information","Profile Information"
"Profile Name","Profile Name"
"Profile Payments","Profile Payments"
"Profile Schedule","Profile Schedule"
"Profile Wizard","Profile Wizard"
"Profiles","Profiles"
"Promo","Promo"
"Promotions","Promotions"
"Purchased Item","Purchased Item"
"Put store on the maintenance mode while backup creation","Put store on the maintenance mode while backup creation"
"Put store on the maintenance mode while rollback processing","Put store on the maintenance mode while rollback processing"
"Quantity","Quantity"
"Queue Refresh","Queue Refresh"
"Queued... Cancel","Queued... Cancel"
"REST Roles","REST Roles"
"Radio Buttons","Radio Buttons"
"Rates","Rates"
"Read details","Read details"
"Rebuild","Rebuild"
"Rebuild Catalog Index","Rebuild Catalog Index"
"Rebuild Flat Catalog Category","Rebuild Flat Catalog Category"
"Rebuild Flat Catalog Product","Rebuild Flat Catalog Product"
"Recent Orders","Recent Orders"
"Recent statistics have been updated.","Recent statistics have been updated."
"Recurring Profile View","Recurring Profile View"
"Recursive Dir","Recursive Dir"
"Redirect","Redirect"
"Reference","Reference"
"Reference ID","Reference ID"
"Refresh","Refresh"
"Refresh Now*","Refresh Now*"
"Refresh Roles","Refresh Roles"
"Refresh Statistics","Refresh Statistics"
"Region/State","Region/State"
"Regular Price:","Regular Price:"
"Reject","Reject"
"Reject token authorization Pop Up for admin","Reject token authorization Pop Up for admin"
"Reject token authorization for admin","Reject token authorization for admin"
"Rejection Of Authorization","Rejection Of Authorization"
"Release","Release"
"Release Stability","Release Stability"
"Release Version","Release Version"
"Reload captcha","Reload captcha"
"Remote FTP","Remote FTP"
"Remove","Remove"
"Reports","Reports"
"Request Path","Request Path"
"Required","Required"
"Reset","Reset"
"Reset Filter","Reset Filter"
"Reset Password","Reset Password"
"Reset a Password","Reset a Password"
"Resize","Resize"
"Resource Access","Resource Access"
"Resources","Resources"
"Results","Results"
"Retrieve Password","Retrieve Password"
"Return Html Version","Return Html Version"
"Revenue","Revenue"
"Reviews","Reviews"
"Reviews and Ratings","Reviews and Ratings"
"Rewrite Rules","Rewrite Rules"
"Role ID","Role ID"
"Role Info","Role Info"
"Role Information","Role Information"
"Role Name","Role Name"
"Role Resources","Role Resources"
"Role Users","Role Users"
"Roles","Roles"
"Roles Resources","Roles Resources"
"Root Category","Root Category"
"Rotate CCW","Rotate CCW"
"Rotate CW","Rotate CW"
"Run","Run"
"Run Profile","Run Profile"
"Run Profile Inside This Window","Run Profile Inside This Window"
"Run Profile in Popup","Run Profile in Popup"
"Running... Kill","Running... Kill"
"SKU","SKU"
"SKU:","SKU:"
"SSL Error: Invalid or self-signed certificate","SSL Error: Invalid or self-signed certificate"
"Sales","Sales"
"Sales Report","Sales Report"
"Samples","Samples"
"Save","Save"
"Save & Generate","Save & Generate"
"Save Account","Save Account"
"Save Block","Save Block"
"Save Cache Settings","Save Cache Settings"
"Save Config","Save Config"
"Save Currency Rates","Save Currency Rates"
"Save Profile","Save Profile"
"Save Role","Save Role"
"Save Template","Save Template"
"Save User","Save User"
"Save Variable","Save Variable"
"Save and Continue Edit","Save and Continue Edit"
"Search","Search"
"Search Index","Search Index"
"Search Term","Search Term"
"Search Terms","Search Terms"
"Search entire store here...","Search entire store here..."
"Searching...","Searching..."
"Select","Select"
"Select All","Select All"
"Select Category","Select Category"
"Select Date","Select Date"
"Select Range","Select Range"
"Select date","Select date"
"Selected allowed currency ""%s"" is not available in installed currencies.","Selected allowed currency ""%s"" is not available in installed currencies."
"Selected base currency is not available in installed currencies.","Selected base currency is not available in installed currencies."
"Selected default display currency is not available in allowed currencies.","Selected default display currency is not available in allowed currencies."
"Selected default display currency is not available in installed currencies.","Selected default display currency is not available in installed currencies."
"Self-assigned roles cannot be deleted.","Self-assigned roles cannot be deleted."
"Sender","Sender"
"Separate Email","Separate Email"
"Serialized data is incorrect","Serialized data is incorrect"
"Shipment #%s comment added","Shipment #%s comment added"
"Shipment #%s created","Shipment #%s created"
"Shipment Comments","Shipment Comments"
"Shipment History","Shipment History"
"Shipments","Shipments"
"Shipping","Shipping"
"Shipping Address","Shipping Address"
"Shipping Address: ","Shipping Address: "
"Shipping Origin","Shipping Origin"
"Shipping Price","Shipping Price"
"Shipping address selection is not applicable","Shipping address selection is not applicable"
"Shipping method must be specified.","Shipping method must be specified."
"Shipping method selection is not applicable","Shipping method selection is not applicable"
"Shopping Cart","Shopping Cart"
"Shopping Cart Price Rules","Shopping Cart Price Rules"
"Shopping Cart from %s","Shopping Cart from %s"
"Show By","Show By"
"Show Report For:","Show Report For:"
"Show Reviews","Show Reviews"
"Sitemap Information","Sitemap Information"
"Size for %s","Size for %s"
"Skip Category Selection","Skip Category Selection"
"Some items in this order have different invoice and shipment types. You can create shipment only after the invoice is created.","Some items in this order have different invoice and shipment types. You can create shipment only after the invoice is created."
"Some of the ordered items do not exist in the catalog anymore and will be removed if you try to edit the order.","Some of the ordered items do not exist in the catalog anymore and will be removed if you try to edit the order."
"Sorry, this feature is coming soon...","Sorry, this feature is coming soon..."
"Sort options","Sort options"
"Special Price:","Special Price:"
"Specific Countries","Specific Countries"
"Specified","Specified"
"Specified profile does not exist.","Specified profile does not exist."
"Spreadsheet Name:","Spreadsheet Name:"
"Start Date","Start Date"
"Starting at:","Starting at:"
"Starting profile execution, please wait...","Starting profile execution, please wait..."
"State/Province:","State/Province:"
"Static Blocks","Static Blocks"
"Status","Status"
"Status:","Status:"
"Stock Quantity:","Stock Quantity:"
"Stock notification was saved.","Stock notification was saved."
"Store","Store"
"Store Email Addresses Section","Store Email Addresses Section"
"Store View","Store View"
"Store:","Store:"
"Storefronts : ","Storefronts : "
"Stores","Stores"
"Subject","Subject"
"Submit","Submit"
"Subpackage cannot be conflicting.","Subpackage cannot be conflicting."
"Subtotal","Subtotal"
"Switch/Solo card issue number: %s","Switch/Solo card issue number: %s"
"Switch/Solo card start Date: %s/%s","Switch/Solo card start Date: %s/%s"
"Switch/Solo/Maestro Only","Switch/Solo/Maestro Only"
"Switch/Solo/Maestro(UK Domestic) Only","Switch/Solo/Maestro(UK Domestic) Only"
"Switch/Solo/Maestro(UK Domestic) card issue number: %s","Switch/Solo/Maestro(UK Domestic) card issue number: %s"
"Switch/Solo/Maestro(UK Domestic) card start Date: %s/%s","Switch/Solo/Maestro(UK Domestic) card start Date: %s/%s"
"Symbol","Symbol"
"Symlinks are enabled. This may expose security risks. We strongly recommend to disable them.","Symlinks are enabled. This may expose security risks. We strongly recommend to disable them."
"Synchronization is required.","Synchronization is required."
"Synchronization of media storages has been successfully completed.","Synchronization of media storages has been successfully completed."
"Synchronize","Synchronize"
"Synchronizing %s to %s","Synchronizing %s to %s"
"Synchronizing...","Synchronizing..."
"System","System"
"System Section","System Section"
"System busy","System busy"
"Tags","Tags"
"Target Path","Target Path"
"Tax","Tax"
"Tb","Tb"
"Template","Template"
"Template Content","Template Content"
"Template Information","Template Information"
"Template Name","Template Name"
"Template Styles","Template Styles"
"Template Subject","Template Subject"
"Template Type","Template Type"
"Temporary (302)","Temporary (302)"
"Terms and Conditions","Terms and Conditions"
"Text","Text"
"The Catalog Rewrites were refreshed.","The Catalog Rewrites were refreshed."
"The CatalogInventory Stock Status has been rebuilt.","The CatalogInventory Stock Status has been rebuilt."
"The Comment Text field cannot be empty.","The Comment Text field cannot be empty."
"The Flat Catalog Product was rebuilt","The Flat Catalog Product was rebuilt"
"The JavaScript/CSS cache has been cleaned.","The JavaScript/CSS cache has been cleaned."
"The JavaScript/CSS cache has been cleared.","The JavaScript/CSS cache has been cleared."
"The Layered Navigation indexing has been queued.","The Layered Navigation indexing has been queued."
"The Layered Navigation indexing queue has been canceled.","The Layered Navigation indexing queue has been canceled."
"The Layered Navigation indices were refreshed.","The Layered Navigation indices were refreshed."
"The Layered Navigation process has been queued to be killed.","The Layered Navigation process has been queued to be killed."
"The Magento cache storage has been flushed.","The Magento cache storage has been flushed."
"The Special Price is active only when lower than the Actual Price.","The Special Price is active only when lower than the Actual Price."
"The URL Rewrite has been deleted.","The URL Rewrite has been deleted."
"The URL Rewrite has been saved.","The URL Rewrite has been saved."
"The account has been saved.","The account has been saved."
"The archive can be uncompressed with <a href=""%s"">%s</a> on Windows systems","The archive can be uncompressed with <a href=""%s"">%s</a> on Windows systems"
"The attribute set has been removed.","The attribute set has been removed."
"The backup's creation process will take time.","The backup's creation process will take time."
"The billing agreement has been canceled.","The billing agreement has been canceled."
"The billing agreement has been deleted.","The billing agreement has been deleted."
"The block has been saved.","The block has been saved."
"The cache storage has been flushed.","The cache storage has been flushed."
"The carrier needs to be specified.","The carrier needs to be specified."
"The catalog index has been rebuilt.","The catalog index has been rebuilt."
"The catalog rewrites have been refreshed.","The catalog rewrites have been refreshed."
"The configurable swatches image cache was cleaned.","The configurable swatches image cache was cleaned."
"The configuration has been saved.","The configuration has been saved."
"The credit memo has been canceled.","The credit memo has been canceled."
"The credit memo has been created.","The credit memo has been created."
"The credit memo has been voided.","The credit memo has been voided."
"The custom variable has been deleted.","The custom variable has been deleted."
"The custom variable has been saved.","The custom variable has been saved."
"The customer has been deleted.","The customer has been deleted."
"The customer has been saved.","The customer has been saved."
"The design change has been deleted.","The design change has been deleted."
"The design change has been saved.","The design change has been saved."
"The email address is empty.","The email address is empty."
"The email template has been deleted.","The email template has been deleted."
"The email template has been saved.","The email template has been saved."
"The flat catalog category has been rebuilt.","The flat catalog category has been rebuilt."
"The group node name must be specified with field node name.","The group node name must be specified with field node name."
"The image cache was cleaned.","The image cache was cleaned."
"The image cache was cleared.","The image cache was cleared."
"The invoice and shipment have been created.","The invoice and shipment have been created."
"The invoice and the shipment  have been created. The shipping label cannot be created at the moment.","The invoice and the shipment  have been created. The shipping label cannot be created at the moment."
"The invoice has been canceled.","The invoice has been canceled."
"The invoice has been captured.","The invoice has been captured."
"The invoice has been created.","The invoice has been created."
"The invoice has been voided.","The invoice has been voided."
"The invoice no longer exists.","The invoice no longer exists."
"The item %s (SKU %s) does not exist in the catalog anymore.","The item %s (SKU %s) does not exist in the catalog anymore."
"The order does not allow creating an invoice.","The order does not allow creating an invoice."
"The order no longer exists.","The order no longer exists."
"The poll has been deleted.","The poll has been deleted."
"The poll has been saved.","The poll has been saved."
"The profile has been deleted.","The profile has been deleted."
"The profile has been saved.","The profile has been saved."
"The profile has been updated.","The profile has been updated."
"The profile has no changes.","The profile has no changes."
"The profile you are trying to save no longer exists","The profile you are trying to save no longer exists"
"The rating has been deleted.","The rating has been deleted."
"The rating has been saved.","The rating has been saved."
"The role has been deleted.","The role has been deleted."
"The role has been saved.","The role has been saved."
"The role has been successfully saved.","The role has been successfully saved."
"The roles have been refreshed.","The roles have been refreshed."
"The search index has been rebuilt.","The search index has been rebuilt."
"The shipment has been created.","The shipment has been created."
"The shipment has been sent.","The shipment has been sent."
"The shipping label has been created.","The shipping label has been created."
"The tag has been deleted.","The tag has been deleted."
"The tag has been saved.","The tag has been saved."
"The transaction details have been updated.","The transaction details have been updated."
"The user has been deleted.","The user has been deleted."
"The user has been saved.","The user has been saved."
"The variable has been saved.","The variable has been saved."
"Themes JavaScript and CSS files combined to one file.","Themes JavaScript and CSS files combined to one file."
"There is an error in one of the option rows.","There is an error in one of the option rows."
"This Account is","This Account is"
"This Email template no longer exists.","This Email template no longer exists."
"This Role no longer exists","This Role no longer exists"
"This Role no longer exists.","This Role no longer exists."
"This account is","This account is"
"This account is inactive.","This account is inactive."
"This action cannot be undone.","This action cannot be undone."
"This attribute set does not have attributes which we can use for configurable product","This attribute set does not have attributes which we can use for configurable product"
"This attribute shares the same value in all the stores","This attribute shares the same value in all the stores"
"This block no longer exists.","This block no longer exists."
"This category is set to only include products matched by rules","This category is set to only include products matched by rules"
"This is a demo store. Any orders placed through this store will not be honored or fulfilled.","This is a demo store. Any orders placed through this store will not be honored or fulfilled."
"This is a required field.","This is a required field."
"This product is currently disabled.","This product is currently disabled."
"This report depends on timezone configuration. Once timezone is changed, the lifetime statistics need to be refreshed.","This report depends on timezone configuration. Once timezone is changed, the lifetime statistics need to be refreshed."
"This section is not allowed.","This section is not allowed."
"This user no longer exists.","This user no longer exists."
"This variable no longer exists.","This variable no longer exists."
"Time","Time"
"Time selection:","Time selection:"
"Time:","Time:"
"Timeout limit for response from synchronize process was reached.","Timeout limit for response from synchronize process was reached."
"To","To"
"To cancel pending authorizations and release amounts that have already been processed during this payment, click Cancel.","To cancel pending authorizations and release amounts that have already been processed during this payment, click Cancel."
"To use PayPal Payflow Link, you must configure your PayPal Payflow Link account on the PayPal website.","To use PayPal Payflow Link, you must configure your PayPal Payflow Link account on the PayPal website."
"To use PayPal Payments Advanced, you must configure  your PayPal Payments Advanced account on the PayPal website.","To use PayPal Payments Advanced, you must configure  your PayPal Payments Advanced account on the PayPal website."
"Toggle Editor","Toggle Editor"
"Tools","Tools"
"Top 5 Search Terms","Top 5 Search Terms"
"Total","Total"
"Total Invoiced","Total Invoiced"
"Total Order Amount","Total Order Amount"
"Total Refunded","Total Refunded"
"Total of %d record(s) have been deleted.","Total of %d record(s) have been deleted."
"Total of %d record(s) have been updated.","Total of %d record(s) have been updated."
"Total of %d record(s) were canceled.","Total of %d record(s) were canceled."
"Total of %d record(s) were deleted","Total of %d record(s) were deleted"
"Total of %d record(s) were deleted.","Total of %d record(s) were deleted."
"Total of %d record(s) were updated","Total of %d record(s) were updated"
"Total of %d record(s) were updated.","Total of %d record(s) were updated."
"Track Order","Track Order"
"Track this shipment","Track this shipment"
"Tracking number %s for %s assigned","Tracking number %s for %s assigned"
"Tracking number cannot be empty.","Tracking number cannot be empty."
"Transaction Data","Transaction Data"
"Transaction Details","Transaction Details"
"Transaction ID","Transaction ID"
"Transaction Type","Transaction Type"
"Transactional Emails","Transactional Emails"
"Transactions","Transactions"
"Type","Type"
"Type:","Type:"
"URL Rewrite","URL Rewrite"
"URL Rewrite Information","URL Rewrite Information"
"URL Rewrite Management","URL Rewrite Management"
"Unable to cancel the credit memo.","Unable to cancel the credit memo."
"Unable to find a Email Template to delete.","Unable to find a Email Template to delete."
"Unable to find a block to delete.","Unable to find a block to delete."
"Unable to find a poll to delete.","Unable to find a poll to delete."
"Unable to find a tag to delete.","Unable to find a tag to delete."
"Unable to find a user to delete.","Unable to find a user to delete."
"Unable to find a variable to delete.","Unable to find a variable to delete."
"Unable to initialize import model","Unable to initialize import model"
"Unable to refresh lifetime statistics.","Unable to refresh lifetime statistics."
"Unable to refresh recent statistics.","Unable to refresh recent statistics."
"Unable to save Cron expression","Unable to save Cron expression"
"Unable to save the cron expression.","Unable to save the cron expression."
"Unable to save the invoice.","Unable to save the invoice."
"Unable to send the invoice email.","Unable to send the invoice email."
"Unable to send the shipment email.","Unable to send the shipment email."
"Unable to update transaction details.","Unable to update transaction details."
"Unable to void the credit memo.","Unable to void the credit memo."
"Unknown","Unknown"
"Unlimited","Unlimited"
"Update","Update"
"Updated At","Updated At"
"Upload File","Upload File"
"Upload Files","Upload Files"
"Upload HTTP Error","Upload HTTP Error"
"Upload I/O Error","Upload I/O Error"
"Upload Security Error","Upload Security Error"
"Upload import file","Upload import file"
"Uploaded file is larger than %.2f kilobytes allowed by server","Uploaded file is larger than %.2f kilobytes allowed by server"
"Use All Available Attributes","Use All Available Attributes"
"Use Config Settings","Use Config Settings"
"Use Default","Use Default"
"Use Default Value","Use Default Value"
"Use Default Variable Values","Use Default Variable Values"
"Use FTP Connection","Use FTP Connection"
"Use Website","Use Website"
"Used Currently For","Used Currently For"
"Used as Default For","Used as Default For"
"User Email","User Email"
"User ID","User ID"
"User Info","User Info"
"User Information","User Information"
"User Name","User Name"
"User Name is required field.","User Name is required field."
"User Name:","User Name:"
"User Password","User Password"
"User Role","User Role"
"User Roles","User Roles"
"User Roles Information","User Roles Information"
"User Type Resources","User Type Resources"
"User name","User name"
"Users","Users"
"Utilities","Utilities"
"VAT Number is Invalid","VAT Number is Invalid"
"VAT Number is Valid","VAT Number is Valid"
"Validation Results","Validation Results"
"Value","Value"
"Value Delimiter:","Value Delimiter:"
"Variable","Variable"
"Variable Code","Variable Code"
"Variable Details","Variable Details"
"Variable HTML Value","Variable HTML Value"
"Variable ID","Variable ID"
"Variable Name","Variable Name"
"Variable Name is incorrect.","Variable Name is incorrect."
"Variable Name is required field.","Variable Name is required field."
"Variable Plain Value","Variable Plain Value"
"Variable has been deleted.","Variable has been deleted."
"Variables","Variables"
"Verifier code: %s","Verifier code: %s"
"View Actions XML","View Actions XML"
"View Full Size","View Full Size"
"View Memo","View Memo"
"View Memo for #%s","View Memo for #%s"
"View Shipment","View Shipment"
"View Statistics For:","View Statistics For:"
"Visibility:","Visibility:"
"Warning","Warning"
"Warning! Empty value can cause problems with CSV format.","Warning! Empty value can cause problems with CSV format."
"Warning!\r\nThis action will remove this user from already assigned role\r\nAre you sure?","Warning!\r\nThis action will remove this user from already assigned role\r\nAre you sure?"
"Warning!\r\nThis action will remove those users from already assigned roles\r\nAre you sure?","Warning!\r\nThis action will remove those users from already assigned roles\r\nAre you sure?"
"Warning: Please do not close the window during importing/exporting data","Warning: Please do not close the window during importing/exporting data"
"Watermark File for %s","Watermark File for %s"
"We appreciate our merchants\' feedback, please <a href=""#"" onclick=""surveyAction(\'yes\'); return false;"">take our survey</a> to provide insight on the features you would like included in Magento. <a href=""#"" onclick=""surveyAction(\'no\'); return false;"">Remove this notification</a>","We appreciate our merchants\' feedback, please <a href=""#"" onclick=""surveyAction(\'yes\'); return false;"">take our survey</a> to provide insight on the features you would like included in Magento. <a href=""#"" onclick=""surveyAction(\'no\'); return false;"">Remove this notification</a>"
"We\'re in our typing table, coding away more features for Magento. Thank you for your patience.","We\'re in our typing table, coding away more features for Magento. Thank you for your patience."
"Web Section","Web Section"
"Web Services","Web Services"
"Web services","Web services"
"Website","Website"
"What is this?","What is this?"
"Wishlist Report","Wishlist Report"
"Wishlist item is not loaded.","Wishlist item is not loaded."
"Wrong account specified.","Wrong account specified."
"Wrong billing agreement ID specified.","Wrong billing agreement ID specified."
"Wrong column format.","Wrong column format."
"Wrong field specified.","Wrong field specified."
"Wrong newsletter template.","Wrong newsletter template."
"Wrong quote item.","Wrong quote item."
"Wrong tab configuration.","Wrong tab configuration."
"Wrong tag was specified.","Wrong tag was specified."
"Wrong transaction ID specified.","Wrong transaction ID specified."
"XML","XML"
"XML data is invalid.","XML data is invalid."
"XML object is not instance of ""Varien_Simplexml_Element"".","XML object is not instance of ""Varien_Simplexml_Element""."
"YTD","YTD"
"Year","Year"
"Yes","Yes"
"Yes (301 Moved Permanently)","Yes (301 Moved Permanently)"
"Yes (302 Found)","Yes (302 Found)"
"Yes (only price with tax)","Yes (only price with tax)"
"You cannot delete your own account.","You cannot delete your own account."
"You did not sign in correctly or your account is temporarily disabled.","You did not sign in correctly or your account is temporarily disabled."
"You have %s unread message(s).","You have %s unread message(s)."
"You have %s unread message(s). <a href=""%s"">Go to messages inbox</a>.","You have %s unread message(s). <a href=""%s"">Go to messages inbox</a>."
"You have %s, %s and %s unread messages. <a href=""%s"">Go to messages inbox</a>.","You have %s, %s and %s unread messages. <a href=""%s"">Go to messages inbox</a>."
"You have logged out.","You have logged out."
"You have not enough permissions to use this functionality.","You have not enough permissions to use this functionality."
"You must have JavaScript enabled in your browser to utilize the functionality of this website.","You must have JavaScript enabled in your browser to utilize the functionality of this website."
"You need to specify order items.","You need to specify order items."
"You will need to wait when the action ends.","You will need to wait when the action ends."
"Your answers contain duplicates.","Your answers contain duplicates."
"Your password has been updated.","Your password has been updated."
"Your password reset link has expired.","Your password reset link has expired."
"Your server PHP settings allow you to upload files not more than %s at a time. Please modify post_max_size (currently is %s) and upload_max_filesize (currently is %s) values in php.ini if you want to upload larger files.","Your server PHP settings allow you to upload files not more than %s at a time. Please modify post_max_size (currently is %s) and upload_max_filesize (currently is %s) values in php.ini if you want to upload larger files."
"Your web server is configured incorrectly. As a result, configuration files with sensitive information are accessible from the outside. Please contact your hosting provider.","Your web server is configured incorrectly. As a result, configuration files with sensitive information are accessible from the outside. Please contact your hosting provider."
"Zip/Postal Code","Zip/Postal Code"
"Zip/Postal Code:","Zip/Postal Code:"
"[ deleted ]","[ deleted ]"
"[GLOBAL]","[GLOBAL]"
"[STORE VIEW]","[STORE VIEW]"
"[WEBSITE]","[WEBSITE]"
"b","b"
"close","close"
"critical","critical"
"example: ""sitemap/"" or ""/"" for base path (path must be writeable)","example: ""sitemap/"" or ""/"" for base path (path must be writeable)"
"example: sitemap.xml","example: sitemap.xml"
"failed","failed"
"from","from"
"major","major"
"minor","minor"
"notice","notice"
"store(%s) scope","store(%s) scope"
"successful","successful"
"to","to"
"website(%s) scope","website(%s) scope"
"{{base_url}} is not recommended to use in a production environment to declare the Base Unsecure URL / Base Secure URL. It is highly recommended to change this value in your Magento <a href=""%s"">configuration</a>.","{{base_url}} is not recommended to use in a production environment to declare the Base Unsecure URL / Base Secure URL. It is highly recommended to change this value in your Magento <a href=""%s"">configuration</a>."
