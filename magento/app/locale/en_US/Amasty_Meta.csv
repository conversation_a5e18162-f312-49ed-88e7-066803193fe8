"%s has been successfully deleted","%s has been successfully deleted"
"%s has been successfully saved","%s has been successfully saved"
"Add Template","Add Template"
"Are you sure?","Are you sure?"
"Best {name} in {website} {short_description}","Best {name} in {website} {short_description}"
"Buy cheap {parent_category} and {category} [by {manufacturer|brand}]from {website}","Buy cheap {parent_category} and {category} [by {manufacturer|brand}]from {website}"
"Buy cheap{category} from {website}","Buy cheap{category} from {website}"
"Buy {name} {color}, from {manufacturer|brand} for {price} only in {website}.","Buy {name} {color}, from {manufacturer|brand} for {price} only in {website}."
"Canonical Url","Canonical Url"
"Categories in nested ones","Categories in nested ones"
"Category is","Category is"
"Category","Category"
"Content","Content"
"Current Categories","Current Categories"
"Current Products","Current Products"
"Default","Default"
"Delete","Delete"
"Description","Description"
"Edit Template","Edit Template"
"Edit","Edit"
"Full Description","Full Description"
"Full: Buy {name} [by {manufacturer|brand}] [of {color} color] [for only {price}] [in {categories}] at [{store},] {website}.","Full: Buy {name} [by {manufacturer|brand}] [of {color} color] [for only {price}] [in {categories}] at [{store},] {website}."
"General","General"
"H1 Tag","H1 Tag"
"ID","ID"
"Image Alt","Image Alt"
"Image Title","Image Title"
"In Page Text","In Page Text"
"Keywords","Keywords"
"Meta Description","Meta Description"
"New Template","New Template"
"Page Content","Page Content"
"Please make sure that category image is wrapped into tag with class 'category-image' and image has %s attribute","Please make sure that category image is wrapped into tag with class 'category-image' and image has %s attribute"
"Please select records","Please select records"
"Priority","Priority"
"Products In Nested Categories","Products In Nested Categories"
"Products in Sub Categories","Products in Sub Categories"
"Record does not exist","Record does not exist"
"Robots","Robots"
"Root","Root"
"Save and Continue Edit","Save and Continue Edit"
"Short Description","Short Description"
"Short: Buy {name} [by {manufacturer|brand}] [of {color} color] [for only {price}] [in {categories}] at [{store},] {website}.","Short: Buy {name} [by {manufacturer|brand}] [of {color} color] [for only {price}] [in {categories}] at [{store},] {website}."
"Show In","Show In"
"Store","Store"
"Sub Categories","Sub Categories"
"Template Configuration","Template Configuration"
"Template already exists in chosen store","Template already exists in chosen store"
"Text after Product List","Text after Product List"
"This value will override any H1 tag even it is not empty","This value will override any H1 tag even it is not empty"
"Title","Title"
"URL","URL"
"Unable to find a record to save","Unable to find a record to save"
"You can use '*' symbol for specify url pattern","You can use '*' symbol for specify url pattern"
"Replace Product Short Descriptions on Category Page","Replace Product Short Descriptions on Category Page"
"Please set to `No` if you have no Short Descriptions for products on the Category page.","Please set to `No` if you have no Short Descriptions for products on the Category page."