"******* & later","******* & later"
"Action","Action"
"Add Author","Add Author"
"Add Contents Path","Add Contents Path"
"Add PHP Extension dependency","Add PHP Extension dependency"
"Add Package dependency","Add Package dependency"
"Add files","Add files"
"Authors","Authors"
"Channel","Channel"
"Contents","Contents"
"Create Extension Package","Create Extension Package"
"Dependencies","Dependencies"
"Description","Description"
"Edit Extension","Edit Extension"
"Email","Email"
"Extension","Extension"
"Extensions","Extensions"
"Failed to create the package.","Failed to create the package."
"Failed to load the package data.","Failed to load the package data."
"Failed to save the package.","Failed to save the package."
"Files","Files"
"Folder","Folder"
"Ignore","Ignore"
"Include","Include"
"License","License"
"License URI","License URI"
"Load Local Package","Load Local Package"
"Load local Package","Load local Package"
"Magento Connect","Magento Connect"
"Magento Connect Manager","Magento Connect Manager"
"Max","Max"
"Maximum","Maximum"
"Min","Min"
"Minimum","Minimum"
"Name","Name"
"New Extension","New Extension"
"PHP Version","PHP Version"
"Package","Package"
"Package Extensions","Package Extensions"
"Package File Name","Package File Name"
"Package Info","Package Info"
"Packages","Packages"
"Path","Path"
"Pre-*******","Pre-*******"
"Release Info","Release Info"
"Remove","Remove"
"Save As...","Save As..."
"Save Data and Create Package","Save Data and Create Package"
"Save package with custom package file name","Save package with custom package file name"
"Summary","Summary"
"Supported releases","Supported releases"
"System","System"
"Target","Target"
"The package %s data has been loaded.","The package %s data has been loaded."
"The package data has been saved.","The package data has been saved."
"There was a problem saving package data","There was a problem saving package data"
"Type","Type"
"User","User"
