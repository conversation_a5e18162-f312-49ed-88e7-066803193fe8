"""%s"" contains not only digit characters.","""%s"" contains not only digit characters."
"""%s"" does not fit the entered date format.","""%s"" does not fit the entered date format."
"""%s"" exceeds the allowed file size.","""%s"" exceeds the allowed file size."
"""%s"" exceeds the allowed length.","""%s"" exceeds the allowed length."
"""%s"" has not only alphabetic and digit characters.","""%s"" has not only alphabetic and digit characters."
"""%s"" has not only alphabetic characters.","""%s"" has not only alphabetic characters."
"""%s"" height exceeds allowed value of %s px.","""%s"" height exceeds allowed value of %s px."
"""%s"" invalid type entered.","""%s"" invalid type entered."
"""%s"" is a required value.","""%s"" is a required value."
"""%s"" is an empty string.","""%s"" is an empty string."
"""%s"" is not a valid URL.","""%s"" is not a valid URL."
"""%s"" is not a valid date.","""%s"" is not a valid date."
"""%s"" is not a valid email address.","""%s"" is not a valid email address."
"""%s"" is not a valid file","""%s"" is not a valid file"
"""%s"" is not a valid file extension.","""%s"" is not a valid file extension."
"""%s"" is not a valid file.","""%s"" is not a valid file."
"""%s"" is not a valid hostname.","""%s"" is not a valid hostname."
"""%s"" is not a valid image format","""%s"" is not a valid image format"
"""%s"" length must be equal or greater than %s characters.","""%s"" length must be equal or greater than %s characters."
"""%s"" length must be equal or less than %s characters.","""%s"" length must be equal or less than %s characters."
"""%s"" width exceeds allowed value of %s px.","""%s"" width exceeds allowed value of %s px."
"'%value%' appears to be a DNS hostname but cannot extract TLD part","'%value%' appears to be a DNS hostname but cannot extract TLD part"
"'%value%' appears to be a DNS hostname but cannot match TLD against known list","'%value%' appears to be a DNS hostname but cannot match TLD against known list"
"'%value%' appears to be a DNS hostname but cannot match against hostname schema for TLD '%tld%'","'%value%' appears to be a DNS hostname but cannot match against hostname schema for TLD '%tld%'"
"'%value%' appears to be a DNS hostname but contains a dash in an invalid position","'%value%' appears to be a DNS hostname but contains a dash in an invalid position"
"'%value%' appears to be a DNS hostname but the given punycode notation cannot be decoded","'%value%' appears to be a DNS hostname but the given punycode notation cannot be decoded"
"'%value%' appears to be a local network name but local network names are not allowed","'%value%' appears to be a local network name but local network names are not allowed"
"'%value%' appears to be an IP address, but IP addresses are not allowed","'%value%' appears to be an IP address, but IP addresses are not allowed"
"'%value%' does not appear to be a valid URI hostname","'%value%' does not appear to be a valid URI hostname"
"'%value%' does not appear to be a valid local network name","'%value%' does not appear to be a valid local network name"
"'%value%' does not match the expected structure for a DNS hostname","'%value%' does not match the expected structure for a DNS hostname"
". Do not use ""event"" for an attribute code, it is a reserved keyword.",". Do not use ""event"" for an attribute code, it is a reserved keyword."
". Do not use event"" for an attribute code"," it is a reserved keyword."""
"A joint field with this alias (%s) is already declared","A joint field with this alias (%s) is already declared"
"Add Option","Add Option"
"An error occurred while loading a record, aborting. Error: %s","An error occurred while loading a record, aborting. Error: %s"
"An error occurred while loading the collection, aborting. Error: %s","An error occurred while loading the collection, aborting. Error: %s"
"An error occurred while saving a record, aborting. Error: ","An error occurred while saving a record, aborting. Error: "
"Attempt to add an invalid object","Attempt to add an invalid object"
"Attribute '%s' used in configurable products","Attribute '%s' used in configurable products"
"Attribute Code","Attribute Code"
"Attribute Label","Attribute Label"
"Attribute Properties","Attribute Properties"
"Attribute object is undefined","Attribute object is undefined"
"Attribute set with the ""%s"" name already exists.","Attribute set with the ""%s"" name already exists."
"Attribute with the same code","Attribute with the same code"
"Can\'t create table: %s","Can\'t create table: %s"
"Catalog Input Type for Store Owner","Catalog Input Type for Store Owner"
"Current module EAV entity is undefined","Current module EAV entity is undefined"
"Current module pathname is undefined","Current module pathname is undefined"
"Data integrity: No header row found for attribute","Data integrity: No header row found for attribute"
"Date","Date"
"Decimal Number","Decimal Number"
"Default Product Listing Sort by does not exist in Available Product Listing Sort By.","Default Product Listing Sort by does not exist in Available Product Listing Sort By."
"Default Value","Default Value"
"Default option value is not defined","Default option value is not defined"
"Delete","Delete"
"Dropdown","Dropdown"
"Email","Email"
"Entity collection expected.","Entity collection expected."
"Entity collections expected.","Entity collections expected."
"Entity instance is not defined","Entity instance is not defined"
"Entity is not initialized","Entity is not initialized"
"Entity object is undefined","Entity object is undefined"
"Failed to load node %s from config","Failed to load node %s from config"
"For internal use. Must be unique with no spaces. Maximum length of attribute code must be less then %s symbols","For internal use. Must be unique with no spaces. Maximum length of attribute code must be less then %s symbols"
"Form Element with the same attribute","Form Element with the same attribute"
"Form Fieldset with the same code","Form Fieldset with the same code"
"Form Type with the same code","Form Type with the same code"
"Form code is not defined","Form code is not defined"
"Frontend label is not defined","Frontend label is not defined"
"Input Validation for Store Owner","Input Validation for Store Owner"
"Integer Number","Integer Number"
"Invalid EAV attribute.","Invalid EAV attribute."
"Invalid alias, already exists in joint attributes","Invalid alias, already exists in joint attributes"
"Invalid attribute name: %s","Invalid attribute name: %s"
"Invalid attribute requested: %s","Invalid attribute requested: %s"
"Invalid attribute type","Invalid attribute type"
"Invalid character encountered in increment ID: %s","Invalid character encountered in increment ID: %s"
"Invalid date","Invalid date"
"Invalid default date","Invalid default date"
"Invalid default decimal value","Invalid default decimal value"
"Invalid entity specified","Invalid entity specified"
"Invalid entity supplied.","Invalid entity supplied."
"Invalid entity supplied: %s","Invalid entity supplied: %s"
"Invalid entity type","Invalid entity type"
"Invalid entity_id, skipping the record.","Invalid entity_id, skipping the record."
"Invalid entity_type specified: %s","Invalid entity_type specified: %s"
"Invalid foreign key","Invalid foreign key"
"Invalid form type.","Invalid form type."
"Invalid joint fields","Invalid joint fields"
"Invalid store specified","Invalid store specified"
"Invalid type given. String expected","Invalid type given. String expected"
"Joined field with this alias is already declared","Joined field with this alias is already declared"
"Joint field or attribute expression with this alias is already declared","Joint field or attribute expression with this alias is already declared"
"Letters","Letters"
"Letters (a-z, A-Z) or Numbers (0-9)","Letters (a-z, A-Z) or Numbers (0-9)"
"Loaded %d records","Loaded %d records"
"Maximum length of attribute code must be less then %s symbols","Maximum length of attribute code must be less then %s symbols"
"Multiple Select","Multiple Select"
"No","No"
"No options found in config node %s","No options found in config node %s"
"None","None"
"Not shared with other products","Not shared with other products"
"Problem loading the collection, aborting. Error: %s","Problem loading the collection, aborting. Error: %s"
"Problem saving the collection, aborting. Error: %s","Problem saving the collection, aborting. Error: %s"
"Required","Required"
"Saved %d record(s).","Saved %d record(s)."
"Source model ""%s"" not found for attribute ""%s""","Source model ""%s"" not found for attribute ""%s"""
"System","System"
"Text Area","Text Area"
"Text Field","Text Field"
"The value of attribute ""%s"" contains invalid data.","The value of attribute ""%s"" contains invalid data."
"The value of attribute ""%s"" must be unique","The value of attribute ""%s"" must be unique"
"The value of attribute ""%s"" must be unique.","The value of attribute ""%s"" must be unique."
"This attribute is used in configurable products","This attribute is used in configurable products"
"URL","URL"
"Unique Value","Unique Value"
"Unique Value (not shared with other products)","Unique Value (not shared with other products)"
"Unknown parameter","Unknown parameter"
"Values Required","Values Required"
"Wrong attribute group ID","Wrong attribute group ID"
"Wrong attribute set ID","Wrong attribute set ID"
"Wrong entity ID","Wrong entity ID"
"Wrong type definition for %s","Wrong type definition for %s"
"Yes","Yes"
"Yes/No","Yes/No"
