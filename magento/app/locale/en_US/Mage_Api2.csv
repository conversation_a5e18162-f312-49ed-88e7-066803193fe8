"%s","%s"
"%s role is a special one and can\'t be changed.","%s role is a special one and can\'t be changed."
"%s role is a special one and can\'t be deleted.","%s role is a special one and can\'t be deleted."
"%s role is protected.","%s role is protected."
"ACL Attribute Rules","ACL Attribute Rules"
"ACL Attributes Information","ACL Attributes Information"
"Add","Add"
"Add Admin Role","Add Admin Role"
"Add New Role","Add New Role"
"Address Type","Address Type"
"Admin","Admin"
"Allow","Allow"
"An error occurred while deleting the role.","An error occurred while deleting the role."
"An error occurred while saving attribute rules.","An error occurred while saving attribute rules."
"An error occurred while saving role.","An error occurred while saving role."
"Api Rules Information","Api Rules Information"
"Assigned","Assigned"
"Associate to Website","Associate to Website"
"Attribute Rules Information","Attribute Rules Information"
"Attribute Set","Attribute Set"
"Attributes","Attributes"
"Automatically Return Credit Memo Item to Stock","Automatically Return Credit Memo Item to Stock"
"Back","Back"
"Backorders","Backorders"
"Base Currency","Base Currency"
"Base Customer Balance","Base Customer Balance"
"Base Discount","Base Discount"
"Base Discount Amount","Base Discount Amount"
"Base Item Subtotal","Base Item Subtotal"
"Base Item Subtotal Including Tax","Base Item Subtotal Including Tax"
"Base Original Price","Base Original Price"
"Base Price","Base Price"
"Base Price Including Tax","Base Price Including Tax"
"Base Shipping","Base Shipping"
"Base Shipping Discount","Base Shipping Discount"
"Base Shipping Tax","Base Shipping Tax"
"Base Subtotal","Base Subtotal"
"Base Subtotal Including Tax","Base Subtotal Including Tax"
"Base Tax Amount","Base Tax Amount"
"Base Total Due","Base Total Due"
"Base Total Paid","Base Total Paid"
"Base Total Refunded","Base Total Refunded"
"Buy Now URL","Buy Now URL"
"Can Be Divided into Multiple Boxes for Shipping","Can Be Divided into Multiple Boxes for Shipping"
"Canceled Qty","Canceled Qty"
"Catalog","Catalog"
"Catalog Product","Catalog Product"
"CatalogInventory","CatalogInventory"
"Category ID","Category ID"
"City","City"
"Company","Company"
"Country","Country"
"Coupon Code","Coupon Code"
"Create","Create"
"Create Permanent Redirect for old URL","Create Permanent Redirect for old URL"
"Created From","Created From"
"Customer","Customer"
"Customer Address","Customer Address"
"Customer Address ID","Customer Address ID"
"Customer Balance","Customer Balance"
"Customer First Name","Customer First Name"
"Customer ID","Customer ID"
"Customer Last Name","Customer Last Name"
"Customer Middle Name","Customer Middle Name"
"Customer Prefix","Customer Prefix"
"Customer Suffix","Customer Suffix"
"Default Image","Default Image"
"Delete","Delete"
"Deny","Deny"
"Disable automatic group change based on VAT ID","Disable automatic group change based on VAT ID"
"Discount","Discount"
"Discount Amount","Discount Amount"
"Discount Description","Discount Description"
"Edit","Edit"
"Edit %s ACL attribute rules","Edit %s ACL attribute rules"
"Edit Role","Edit Role"
"Edit attribute rules for %s Role","Edit attribute rules for %s Role"
"Email","Email"
"Enable Qty Increments","Enable Qty Increments"
"Exclude","Exclude"
"File Content","File Content"
"File MIME Type","File MIME Type"
"File Name","File Name"
"Final Price With Tax","Final Price With Tax"
"Final Price Without Tax","Final Price Without Tax"
"First Name","First Name"
"Gift Message","Gift Message"
"Grand Total","Grand Total"
"Grand Total to Be Charged","Grand Total to Be Charged"
"Group","Group"
"Guest","Guest"
"Has Custom Options","Has Custom Options"
"ID","ID"
"Inventory Data","Inventory Data"
"Invoiced Qty","Invoiced Qty"
"Is Confirmed","Is Confirmed"
"Is Default Billing Address","Is Default Billing Address"
"Is Default Shipping Address","Is Default Shipping Address"
"Item ID","Item ID"
"Item Subtotal","Item Subtotal"
"Item Subtotal Including Tax","Item Subtotal Including Tax"
"Label","Label"
"Last Logged In","Last Logged In"
"Last Name","Last Name"
"Low Stock Date","Low Stock Date"
"Manage Stock","Manage Stock"
"Maximum Qty Allowed in Shopping Cart","Maximum Qty Allowed in Shopping Cart"
"Minimum Qty Allowed in Shopping Cart","Minimum Qty Allowed in Shopping Cart"
"New Role","New Role"
"Notify for Quantity Below","Notify for Quantity Below"
"OAuth","OAuth"
"Order","Order"
"Order Addresses","Order Addresses"
"Order Comments","Order Comments"
"Order Currency","Order Currency"
"Order Date","Order Date"
"Order ID","Order ID"
"Order ID (internal)","Order ID (internal)"
"Order Item ID","Order Item ID"
"Order Items","Order Items"
"Order Status","Order Status"
"Ordered Qty","Ordered Qty"
"Orders","Orders"
"Original Price","Original Price"
"Parent Order Item ID","Parent Order Item ID"
"Payment Method","Payment Method"
"Phone Number","Phone Number"
"Placed from IP","Placed from IP"
"Please enter a valid number in ""max_sale_qty"" field","Please enter a valid number in ""max_sale_qty"" field"
"Please enter a valid number in ""min_qty"" field","Please enter a valid number in ""min_qty"" field"
"Please enter a valid number in ""min_sale_qty"" field","Please enter a valid number in ""min_sale_qty"" field"
"Please enter a valid number in ""notify_stock_qty"" field","Please enter a valid number in ""notify_stock_qty"" field"
"Please use numbers only in ""category_id"" field.","Please use numbers only in ""category_id"" field."
"Please use numbers only in ""qty_increments"" field. Please avoid spaces or other characters such as dots or commas.","Please use numbers only in ""qty_increments"" field. Please avoid spaces or other characters such as dots or commas."
"Position","Position"
"Price","Price"
"Price Including Tax","Price Including Tax"
"Product","Product"
"Product Category","Product Category"
"Product ID","Product ID"
"Product Image","Product Image"
"Product Type","Product Type"
"Product URL","Product URL"
"Product Website","Product Website"
"Product and Custom Options Name","Product and Custom Options Name"
"Qty","Qty"
"Qty Increments","Qty Increments"
"Qty Uses Decimals","Qty Uses Decimals"
"Qty for Item's Status to Become Out of Stock","Qty for Item's Status to Become Out of Stock"
"REST - Attributes","REST - Attributes"
"REST - Roles","REST - Roles"
"REST ACL Attributes","REST ACL Attributes"
"REST Attributes","REST Attributes"
"REST Role","REST Role"
"REST Roles","REST Roles"
"REST Roles Information","REST Roles Information"
"Read","Read"
"Refunded Qty","Refunded Qty"
"Regular Price With Tax","Regular Price With Tax"
"Regular Price Without Tax","Regular Price Without Tax"
"Rest Roles","Rest Roles"
"Retrieve","Retrieve"
"Role ""%s"" no longer exists","Role ""%s"" no longer exists"
"Role ""%s"" not found.","Role ""%s"" not found."
"Role API Resources","Role API Resources"
"Role Info","Role Info"
"Role Information","Role Information"
"Role Name","Role Name"
"Role Users","Role Users"
"Role has been deleted.","Role has been deleted."
"Roles","Roles"
"SKU","SKU"
"Salability Status","Salability Status"
"Sales","Sales"
"Save","Save"
"Shipped Qty","Shipped Qty"
"Shipping Amount","Shipping Amount"
"Shipping Discount","Shipping Discount"
"Shipping Including Tax","Shipping Including Tax"
"Shipping Method","Shipping Method"
"Shipping Tax","Shipping Tax"
"State","State"
"Stock Availability","Stock Availability"
"Stock ID","Stock ID"
"Stock Item","Stock Item"
"Stock Status","Stock Status"
"Store Currency to Order Currency Rate","Store Currency to Order Currency Rate"
"Store Name","Store Name"
"Street","Street"
"Subtotal","Subtotal"
"Subtotal Including Tax","Subtotal Including Tax"
"System","System"
"Tax Amount","Tax Amount"
"Tax Name","Tax Name"
"Tax Percent","Tax Percent"
"Tax Rate","Tax Rate"
"The ""enable_qty_increments"" field must be set to 0 or 1.","The ""enable_qty_increments"" field must be set to 0 or 1."
"The ""is_decimal_divided"" field must be set to 0 or 1.","The ""is_decimal_divided"" field must be set to 0 or 1."
"The ""is_in_stock"" field must be set to 0 or 1.","The ""is_in_stock"" field must be set to 0 or 1."
"The ""is_qty_decimal"" field must be set to 0 or 1.","The ""is_qty_decimal"" field must be set to 0 or 1."
"The ""is_qty_decimal"" field must be set to 0, 1, or 2.","The ""is_qty_decimal"" field must be set to 0, 1, or 2."
"The ""manage_stock"" field must be set to 0 or 1.","The ""manage_stock"" field must be set to 0 or 1."
"The ""stock_status_changed_auto"" field must be set to 0 or 1.","The ""stock_status_changed_auto"" field must be set to 0 or 1."
"The ""use_config_backorders"" field must be set to 0 or 1.","The ""use_config_backorders"" field must be set to 0 or 1."
"The ""use_config_enable_qty_inc"" field must be set to 0 or 1.","The ""use_config_enable_qty_inc"" field must be set to 0 or 1."
"The ""use_config_manage_stock"" field must be set to 0 or 1.","The ""use_config_manage_stock"" field must be set to 0 or 1."
"The ""use_config_max_sale_qty"" field must be set to 0 or 1.","The ""use_config_max_sale_qty"" field must be set to 0 or 1."
"The ""use_config_min_qty"" field must be set to 0 or 1.","The ""use_config_min_qty"" field must be set to 0 or 1."
"The ""use_config_min_sale_qty"" field must be set to 0 or 1.","The ""use_config_min_sale_qty"" field must be set to 0 or 1."
"The ""use_config_notify_stock_qty"" field must be set to 0 or 1.","The ""use_config_notify_stock_qty"" field must be set to 0 or 1."
"The ""use_config_qty_increments"" field must be set to 0 or 1.","The ""use_config_qty_increments"" field must be set to 0 or 1."
"The attribute rules were saved.","The attribute rules were saved."
"The role has been saved.","The role has been saved."
"The role is a special one and not for assigning it to admin users.","The role is a special one and not for assigning it to admin users."
"Total Due","Total Due"
"Total Paid","Total Paid"
"Total Refunded","Total Refunded"
"Total Reviews Count","Total Reviews Count"
"Type","Type"
"URL","URL"
"Update","Update"
"Use Config Settings for Allow Gift Message","Use Config Settings for Allow Gift Message"
"Use Config Settings for Allow Gift Wrapping","Use Config Settings for Allow Gift Wrapping"
"Use Config Settings for Backorders","Use Config Settings for Backorders"
"Use Config Settings for Enable Qty Increments","Use Config Settings for Enable Qty Increments"
"Use Config Settings for Manage Stock","Use Config Settings for Manage Stock"
"Use Config Settings for Maximum Qty Allowed in Shopping Cart","Use Config Settings for Maximum Qty Allowed in Shopping Cart"
"Use Config Settings for Minimum Qty Allowed in Shopping Cart","Use Config Settings for Minimum Qty Allowed in Shopping Cart"
"Use Config Settings for Notify for Quantity Below","Use Config Settings for Notify for Quantity Below"
"Use Config Settings for Qty Increments","Use Config Settings for Qty Increments"
"Use Config Settings for Qty for Item's Status to Become Out of Stock","Use Config Settings for Qty for Item's Status to Become Out of Stock"
"User Type","User Type"
"User type ""%s"" no longer exists","User type ""%s"" no longer exists"
"User type ""%s"" not found.","User type ""%s"" not found."
"Web Services","Web Services"
"Web services","Web services"
"Write","Write"
"ZIP/Postal Code","ZIP/Postal Code"
