<!--@subject  {{var store.getFrontendName()}}: Invoice # {{var invoice.increment_id}} for Order # {{var order.increment_id}} @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"htmlescape var=$order.getCustomerName()":"Customer Name",
"var store.getFrontendName()":"Store Name",
"store url=\"customer/account/\"":"Customer Account Url",
"var invoice.increment_id":"Invoice Id",
"var order.increment_id":"Order Id",
"var order.billing_address.format('html')":"Billing Address",
"var payment_html":"Payment Details",
"var order.shipping_address.format('html')":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"layout area=\"frontend\" handle=\"sales_email_order_invoice_items\" invoice=$invoice order=$order":"Invoice Items Grid",
"var comment":"Invoice Comment"}
@-->
<!--@styles
@-->


{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td>
            <table cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td class="email-heading">
                        <h1>Thank you for your order from {{var store.getFrontendName()}}.</h1>
                        <p>You can check the status of your order by <a href="{{store url="customer/account/"}}">logging into your account</a>.</p>
                    </td>
                    <td class="store-info">
                        <h4>Order Questions?</h4>
                        <p>
                            {{depend store_phone}}
                            <b>Call Us:</b>
                            <a href="tel:{{var phone}}">{{var store_phone}}</a><br>
                            {{/depend}}
                            {{depend store_hours}}
                            <span class="no-link">{{var store_hours}}</span><br>
                            {{/depend}}
                            {{depend store_email}}
                            <b>Email:</b> <a href="mailto:{{var store_email}}">{{var store_email}}</a>
                            {{/depend}}
                        </p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td class="order-details">
            <h3>Your Invoice <span class="no-link">#{{var invoice.increment_id}}</span></h3>
            <p>Order <span class="no-link">#{{var order.increment_id}}</span></p>
        </td>
    </tr>
    <tr class="order-information">
        <td>
            {{if comment}}
            <table cellspacing="0" cellpadding="0" class="message-container">
                <tr>
                    <td>{{var comment}}</td>
                </tr>
            </table>
            {{/if}}
            {{layout area="frontend" handle="sales_email_order_invoice_items" invoice=$invoice order=$order}}
            <table cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td class="address-details">
                        <h6>Bill to:</h6>
                        <p><span class="no-link">{{var order.billing_address.format('html')}}</span></p>
                    </td>
                    {{depend order.getIsNotVirtual()}}
                    <td class="address-details">
                        <h6>Ship to:</h6>
                        <p><span class="no-link">{{var order.shipping_address.format('html')}}</span></p>
                    </td>
                    {{/depend}}
                </tr>
                <tr>
                    {{depend order.getIsNotVirtual()}}
                    <td class="method-info">
                        <h6>Shipping method:</h6>
                        <p>{{var order.shipping_description}}</p>
                    </td>
                    {{/depend}}
                    <td class="method-info">
                        <h6>Payment method:</h6>
                        {{var payment_html}}
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}
