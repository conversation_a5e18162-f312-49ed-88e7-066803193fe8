<!--@subject {{var store.getFrontendName()}}: Credit Memo # {{var creditmemo.increment_id}} update @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",

"htmlescape var=$billing.getName()":"Guest Customer Name",
"var order.increment_id":"Order Id",
"var order.getStatusLabel()":"Order Status",
"var comment":"Credit Memo Comment",
"var store.getFrontendName()":"Store Name"}
@-->
<!--@styles
@-->

{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td>
            <table cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td class="action-content">
                        <h1>{{htmlescape var=$billing.getName()}},</h1>
                        <p>Your order <span class="no-link">#{{var order.increment_id}}</span> has been updated to: <strong>{{var order.getStatusLabel()}}</strong></p>
                        {{if comment}}
                        <table cellspacing="0" cellpadding="0" class="message-container">
                            <tr>
                                <td>{{var comment}}</td>
                            </tr>
                        </table>
                        {{/if}}
                        <p>You can check the status of your order by <a href="{{store url="customer/account/"}}">logging into your account</a>.</p>
                        <p>
                            If you have any questions, please feel free to contact us at
                            <a href="mailto:{{var store_email}}">{{var store_email}}</a>
                            {{depend store_phone}} or by phone at <a href="tel:{{var phone}}">{{var store_phone}}</a>{{/depend}}.
                        </p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}
