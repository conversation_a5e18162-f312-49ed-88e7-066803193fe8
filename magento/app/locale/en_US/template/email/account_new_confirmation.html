<!--@subject Account confirmation for {{var customer.name}} @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"store url=\"customer/account/\"":"Customer Account Url",
"htmlescape var=$customer.name":"Customer Name",
"var customer.email":"Customer Email",
"store url=\"customer/account/confirm/\" _query_id=$customer.id _query_key=$customer.confirmation _query_back_url=$back_url":"Confirmation Url",
"htmlescape var=$customer.password":"Customer password"}
@-->

<!--@styles
@-->

{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td>
            <table cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td class="action-content">
                        <h1>{{htmlescape var=$customer.name}},</h1>
                        <p>Your email {{var customer.email}} must be confirmed before using it to log in to our store.</p>
                        <p class="highlighted-text">
                            Use the following values when prompted to log in:<br/>
                            <strong>Email:</strong> {{var customer.email}}<br/>
                            <strong>Password:</strong> {{htmlescape var=$customer.password}}
                        </p>
                        <p>Click here to confirm your email and instantly log in (the link is valid only once):</p>
                        <table cellspacing="0" cellpadding="0" class="action-button" >
                            <tr>
                                <td>
                                    <a href="{{store url="customer/account/confirm/" _query_id=$customer.id _query_key=$customer.confirmation _query_back_url=$back_url}}"><span>Confirm Account</span></a>
                                </td>
                            </tr>
                        </table>
                        <p>
                            If you have any questions, please feel free to contact us at
                            <a href="mailto:{{var store_email}}">{{var store_email}}</a>
                            {{depend store_phone}} or by phone at <a href="tel:{{var phone}}">{{var store_phone}}</a>{{/depend}}.
                        </p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}
