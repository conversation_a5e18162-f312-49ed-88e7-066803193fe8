<!--@subject Your password or email changed at {{var store.getFrontendName()}} @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"store url=\"customer/account/\"":"Customer Account Url",
"var customer.email":"Customer Email",
"var customer.is_change_email":"Customer Flag Email Change",
"var customer.is_change_password":"Customer Flag Password Change",
"htmlescape var=$customer.name":"Customer Name"}
@-->
<!--@styles
@-->

{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td class="action-content">
            <h1>Hello {{htmlescape var=$customer.name}},</h1>
            {{if customer.is_change_password}}
            <p>We have received a request to change password associated with your account at {{var store.getFrontendName()}}.</p>
            {{/if}}
            {{if customer.is_change_email}}
            <br>We have received a request to change email associated with your account at {{var store.getFrontendName()}}.<br />
            <strong>Your new email is:</strong> {{var customer.email}}</p>
            {{/if}}
            <p>If you have not authorized this action, please contact us immediately {{depend store_phone}} at <a href="tel:{{var store_phone}}">{{var store_phone}}</a>{{/depend}}.</p>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}
