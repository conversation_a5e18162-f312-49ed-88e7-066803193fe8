<!--@subject Password Reset Confirmation for {{var customer.name}} @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"htmlescape var=$customer.name":"Customer Name",
"store url=\"customer/account/resetpassword/\" _query_id=$customer.id _query_token=$customer.rp_token":"Reset Password URL"}
@-->

<!--@styles
@-->

{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td class="action-content">
            <h1>{{htmlescape var=$customer.name}},</h1>
            <p>There was recently a request to change the password for your account.</p>
            <p>If you requested this password change, please reset your password here:</p>
            <table cellspacing="0" cellpadding="0" class="action-button" >
                <tr>
                    <td>
                        <a href="{{store url="customer/account/resetpassword/" _query_id=$customer.rp_customer_id _query_token=$customer.rp_token}}"><span>Reset Password</span></a>
                    </td>
                </tr>
            </table>
            <p>If you did not make this request, you can ignore this message and your password will remain the same.</p>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}
