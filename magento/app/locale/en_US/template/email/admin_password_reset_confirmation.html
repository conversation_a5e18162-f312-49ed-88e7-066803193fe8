<!--@subject Password Reset Confirmation for {{var user.name}} @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"htmlescape var=$user.name":"Admin Name",
"store url=\"adminhtml/index/resetpassword/\" _query_id=$user.id _query_token=$user.rp_token":"Reset Password URL",
"store url=\"adminhtml/system_account/\"":"Admin Account Url"}
@-->

<!--@styles
@-->

{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td class="action-content">
            <h1>{{htmlescape var=$user.name}},</h1>
            <p>There was recently a request to change the password for your account.</p>
            <p>You can change your password at any time by logging into <a href="{{store url="adminhtml/system_account/"}}">your account</a>.</p>
            <p>If you requested this password change, click here to reset your password:</p>
            <table cellspacing="0" cellpadding="0" class="action-button">
                <tr>
                    <td>
                        <a href="{{store url="adminhtml/index/resetpassword/" _query_id=$user.id _query_token=$user.rp_token}}"><span>Reset Password</span></a>
                    </td>
                </tr>
            </table>
            <p>If you did not make this request, you can ignore this message and your password will remain the same.</p>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}
