<!--@subject {{var store.getFrontendName()}}: Order # {{var order.increment_id}} update @-->
<!--@vars
{"store url=\"\"":"Store Url",
"skin url=\"images/logo_email.gif\" _area='frontend'":"Email Logo Image",
"htmlescape var=$order.getCustomerName()":"Customer Name",
"var order.increment_id":"Order Id",
"var order.getStatusLabel()":"Order Status",
"store url=\"customer/account/\"":"Customer Account Url",
"var comment":"Order Comment",
"var store.getFrontendName()":"Store Name"}
@-->
<!--@styles
body,td { color:#2f2f2f; font:11px/1.35em Verdana, Arial, Helvetica, sans-serif; }
@-->

<body style="background:#F6F6F6; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px; margin:0; padding:0;">
<div style="background:#F6F6F6; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px; margin:0; padding:0;">
<table cellspacing="0" cellpadding="0" border="0" height="100%" width="100%">
<tr>
    <td align="center" valign="top" style="padding:20px 0 20px 0">
        <!-- [ header starts here] -->
        <table bgcolor="#FFFFFF" cellspacing="0" cellpadding="10" border="0" width="650" style="border:1px solid #E0E0E0;">
            <tr>
                <td valign="top"><a href="{{store url=""}}"><img src="{{skin url="images/logo_email.gif" _area='frontend'}}" alt="{{var store.getFrontendName()}}"  style="margin-bottom:10px;" border="0"/></a></td>
            </tr>
            <!-- [ middle starts here] -->
            <tr>
                <td valign="top">
                    <h1 style="font-size:22px; font-weight:normal; line-height:22px; margin:0 0 11px 0;">Dear {{htmlescape var=$order.getCustomerName()}},</h1>
                    <p style="font-size:12px; line-height:16px; margin:0 0 10px 0;">
                        Your order # {{var order.increment_id}} has been updated.
                    </p>
                    <p style="font-size:12px; line-height:16px; margin:0 0 10px 0;">You can check your order by <a href="{{store url="customer/account/"}}" style="color:#1E7EC8;">logging into your account</a>.</p>
                    <p style="font-size:12px; line-height:16px; margin:0 0 10px 0;">{{var comment}}</p>
                    <p style="font-size:12px; line-height:16px; margin:0;">
                        If you have any questions, please feel free to contact us at
                        <a href="mailto:{{config path='trans_email/ident_support/email'}}" style="color:#1E7EC8;">{{config path='trans_email/ident_support/email'}}</a>
                        or by phone at {{config path='general/store_information/phone'}}.
                    </p>
                </td>
            </tr>
            <tr>
                <td bgcolor="#EAEAEA" align="center" style="background:#EAEAEA; text-align:center;"><center><p style="font-size:12px; margin:0;">Thank you again, <strong>{{var store.getFrontendName()}}</strong></p></center></td>
            </tr>
        </table>
    </td>
</tr>
</table>
</div>
</body>