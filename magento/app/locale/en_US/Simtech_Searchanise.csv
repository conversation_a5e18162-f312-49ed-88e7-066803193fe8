"Notice", "Notice"
"Warning", "Warning"
"Error", "Error"
"The product catalog is queued for syncing with Searchanise", "The product catalog is queued for syncing with Searchanise"
"Searchanise: New search engine for [language] created. Catalog import started", "Searchanise: New search engine for [language] created. Catalog import started"
"Searchanise: Catalog import for [language] started", "Searchanise: Catalog import for [language] started"
"If you have modified any SEO add-on settings, please <a href="[link]">Resync</a> product catalog with Searchanise to update product URLs.", "If you have modified any SEO add-on settings, please <a href="[link]">Resync</a> product catalog with Searchanise to update product URLs."
"An error occurred during Searchanise registration. Please try later", "An error occurred during Searchanise registration. Please try later"
"The product catalog syncing is in progress", "The product catalog syncing is in progress"
"The product catalog was synced successfully with Searchanise", "The product catalog was synced successfully with Searchanise"
"The product catalog is not synced with Searchanise yet. Please press "Sync" button to start", "The product catalog is not synced with <PERSON>anise yet. Please press "Sync" button to start"
"Please note that data on <PERSON>anise is updated with 2-3 minutes delay after you made changed in your store" , "Please note that data on Searchanise is updated with 2-3 minutes delay after you made changed in your store" 
"Click the button below to connect to Searchanise", "Click the button below to connect to Searchanise"
"Congratulations, you've just connected to Searchanise", "Congratulations, you've just connected to Searchanise"
"You've connected to Searchanise", "You've connected to Searchanise"
"text_se_database_restore_notice", "You've just restored your data from the database dump file. If this dump contains products data, please <a href="[link]">Resync</a> them with Searchanise."
"Please disable the Use Flat Catalog Product (Configuration -> Catalog -> Frontend) setting if you have multiple store views. Otherwise, Searchanise may work incorrectly.", "Please disable the Use Flat Catalog Product (Configuration -> Catalog -> Frontend) setting if you have multiple store views. Otherwise, Searchanise may work incorrectly."
"Relevance", "Relevance"
"Products", "Products"
"Popular suggestions", "Popular suggestions"
"Sort by Relevance", "Sort by Relevance"
"API key", "API key"
"Private key", "Private key"
"Search results", "Search results"
