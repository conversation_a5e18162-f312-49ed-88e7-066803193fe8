"Home","Home"
"Go to Home Page","Go to Home Page"
"Return to %s","Return to %s"
"News","News"
"Cannot retrieve the news","Cannot retrieve the news"
"Category Manager","Category Manager"
"Add Category","Add Category"
"Comment Manager","Comment Manager"
"News Manager","News Manager"
"Add New Article","Add New Article"
"Save Category","Save Category"
"Delete Category","Delete Category"
"Edit Category '%s'","Edit Category '%s'"
"ID","ID"
"Title","Title"
"URL Key","URL Key"
"Sort Order","Sort Order"
"Action","Action"
"Edit","Edit"
"Delete","Delete"
"Are you sure?","Are you sure?"
"Category Information","Category Information"
"Keywords","Keywords"
"Meta Keywords","Meta Keywords"
"Description","Description"
"Meta Description","Meta Description"
"Save Comment","Save Comment"
"Delete Comment","Delete Comment"
"Edit Comment By '%s'","Edit Comment By '%s'"
"Add Comment","Add Comment"
"Comment","Comment"
"Newsitem Title","Newsitem Title"
"User","User"
"E-mail","E-mail"
"Created","Created"
"Status","Status"
"Action","Action"
"News Article","News Article"
"Go to News Article","Go to News Article"
"Approve","Approve"
"Not Approve","Not Approve"
"Comment Information","Comment Information"
"User","User"
"Email Address","Email Address"
"Unapproved","Unapproved"
"Approved","Approved"
"Save News Article","Save News Article"
"Delete News Article","Delete News Article"
"Edit News Article '%s'","Edit News Article '%s'"
"Add News Article","Add News Article"
"Author","Author"
"Updated","Updated"
"Enabled","Enabled"
"Disabled","Disabled"
"Comments","Comments"
"View comments","View comments"
"Change status","Change status"
"Main Information","Main Information"
"Additional Options","Additional Options"
"News Time Settings","News Time Settings"
"News Time","News Time"
"Next to the Article will be stated current time","Next to the Article will be stated current time"
"Publish From:","Publish From:"
"Hours","Hours"
"Minutes","Minutes"
"Publish Until:","Publish Until:"
"Meta Data","Meta Data"
"Advanced Post Options","Advanced Post Options"
"Author name","Author name"
"News information","News information"
"Store View","Store View"
"Category","Category"
"File","File"
"Delete File","Delete File"
"Link Name","Link Name"
"Add Tags","Add Tags"
"Use Full Description Image","Use Full Description Image"
"Image for Short Description","Image for Short Description"
"Resize Image Height","Resize Image Height"
"Resize Image Width","Resize Image Width"
"Show Image","Show Image"
"Yes","Yes"
"No","No"
"Short Description","Short Description"
"Image for Full Description","Image for Full Description"
"Full Description","Full Description"
"Comments are not enabled.","Comments are not enabled."
"Your comment has been successfully sent. It will be displayed after approval by our admin","Your comment has been successfully sent. It will be displayed after approval by our admin"
"Thank you for adding a comment.","Thank you for adding a comment."
"Page access error","Page access error"
"Category was successfully saved","Category was successfully saved"
"Category was successfully deleted","Category was successfully deleted"
"Comment was successfully saved","Comment was successfully saved"
"Comment was successfully deleted","Comment was successfully deleted"
"News article does not exist","News article does not exist"
"News Item with such url_key already exists","News Item with such url_key already exists"
"News Category with such url_key already exists","News Category with such url_key already exists"
"News article has been successfully saved","News article has been successfully saved"
"No items to save","No items to save"
"Platform version is not correct for News module!","Platform version is not correct for News module!"
"Submit Comment","Submit Comment"
"Name","Name"
"Email","Email"
"You must be logged in to post a comment.","You must be logged in to post a comment."
"click here","click here"
"to log in","to log in"
"News Settings","News Settings"
"News Title","News Title"
"Route to News","Route to News"
"Show Breadcrumbs","Show Breadcrumbs"
"Articles per Page","Articles per Page"
"Articles Limit", "Articles Limit"
"The number of articles displayed per page","The number of articles displayed per page"
"Show Latest News Block","Show Latest News Block"
"Articles in the Latest News Block","Articles in the Latest News Block"
"Set the number","Set the number"
"Show Date of the News","Show Date of the News"
"Show Time of the News","Show Time of the News"
"Show Author of the News","Show Author of the News"
"Show Category of the News","Show Category of the News"
"default: view more","default: view more"
"Name of the Link","Name of the Link"
"example: view more","example: view more"
"Tags for the News","Tags for the News"
"Enable Google Button for News","Enable Google Button for News"
"Enable Twitter Button for News","Enable Twitter Button for News"
"Enable Facebook Button for News","Enable Facebook Button for News"
"Enable LinkedIn Button for News","Enable LinkedIn Button for News"
"Default Meta Title","Default Meta Title"
"Default Meta Keywords","Default Meta Keywords"
"Default Meta Description","Default Meta Description"
"Short Description Image Max Width","Short Description Image Max Width"
"Short Description Image Max Height","Short Description Image Max Height"
"Full Description Image Max Width","Full Description Image Max Width"
"Full Description Image Max Height","Full Description Image Max Height"
"Resize always to Max Values","Resize always to Max Values"
"Send comments to email","Send comments to email"
"Templare email","Templare email"
"Should comments be pre-moderated","Should comments be pre-moderated"
"Should user be logged in to leave a comment","Should user be logged in to leave a comment"
"Comments per Page","Comments per Page"
"The number of comments displayed per page","The number of comments displayed per page"
"RSS Feed","RSS Feed"
"Articles to show","Articles to show"
"Number of articles to show in the RSS feed.","Number of articles to show in the RSS feed."
"Enable Link ""View More""","Enable Link ""View More"""
"e.g. ""news"" will make the news accessible from domain.com/news. Should not contain slashes. Leave blank for default - news","e.g. ""news"" will make the news accessible from domain.com/news. Should not contain slashes. Leave blank for default - news"
"Leave blank to disable","Leave blank to disable"
"Use comma for multiple words","Use comma for multiple words"
"e.g. domain.com/news/url_key","e.g. domain.com/news/url_key"
"e.g. Download attached document - default","e.g. Download attached document - default"