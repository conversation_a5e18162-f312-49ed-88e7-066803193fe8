<!--@subject Забравена парола на {{var customer.name}} @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"htmlescape var=$customer.name":"Customer Name",
"store url=\"customer/account/resetpassword/\" _query_id=$customer.id _query_token=$customer.rp_token":"Reset Password URL"}
@-->

<!--@styles
@-->

{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td class="action-content">
            <h1>{{htmlescape var=$customer.name}},</h1>
            <h2>Заявка за смяна на парола в онлайн магазин {{var store.getFrontendName()}}</h2>
            <p>Получавате този e-mail, тъй като през онлайн магазин  {{var store.getFrontendName()}} дойде заявка за генериране на нова парола за Вашия профил.</p>
            <p><b>Ако наистина сте заявили смяна на паролата, последвайте бутона "Възстановяване на парола", за да си създадете нова парола за вход в онлайн магазина:</b></p>
            <table cellspacing="0" cellpadding="0" class="action-button" >
                <tr>
                    <td>
                        <a href="{{store url="customer/account/resetpassword/" _query_id=$customer.id _query_token=$customer.rp_token}}"><span>Възстановяване парола</span></a>
                    </td>
                </tr>
            </table>
            <p>Ако линкът не работи, моля копирайте адреса и го поставете във Вашия браузър и натиснете Enter.
            <p>Ако не сте отправили това искане, може да игнорирате това съобщение и паролата Ви ще си остане същата</p>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}