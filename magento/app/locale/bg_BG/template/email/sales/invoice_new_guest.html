<!--@subject  {{var store.getFrontendName()}}: Информационен бон за поръчка @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"htmlescape var=$order.getCustomerName()":"Customer Name",
"var store.getFrontendName()":"Store Name",
"store url=\"customer/account/\"":"Customer Account Url",
"var invoice.increment_id":"Invoice Id",
"var order.increment_id":"Order Id",
"var order.billing_address.format('html')":"Billing Address",
"var payment_html":"Payment Details",
"var order.shipping_address.format('html')":"Shipping Address",
"var order.shipping_description":"Shipping Description",
"layout area=\"frontend\" handle=\"sales_email_order_invoice_items\" invoice=$invoice order=$order":"Invoice Items Grid",
"var comment":"Invoice Comment"}
@-->
<!--@styles
@-->


<body style="background:#F6F6F6; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px; margin:0; padding:0;">
<div style="background:#F6F6F6; font-family:Verdana, Arial, Helvetica, sans-serif; font-size:12px; margin:0; padding:0;">
<table cellspacing="0" cellpadding="0" border="0" height="100%" width="100%">
<tr>
    <td align="center" valign="top" style="padding:20px 0 20px 0">
        <!-- [ header starts here] -->
        <table bgcolor="#FFFFFF" cellspacing="0" cellpadding="10" border="0" width="650" style="border:1px solid #E0E0E0;">
            <tr>
                <td valign="top"><a href="{{store url=""}}"><img src="{{var logo_url}}" alt="{{var logo_alt}}" style="margin-bottom:10px;" border="0"/></a></td>
            </tr>
        <!-- [ middle starts here] -->
            <tr>
                <td valign="top">
                    <h1 style="font-size:22px; font-weight:normal; line-height:22px; margin:0 0 11px 0;">Здравейте, {{htmlescape var=$billing.getName()}}</h1>
                    <p style="font-size:12px; line-height:16px; margin:0;">
                        Благодарим за Вашата поръчка от {{var store.getFrontendName()}}.
                        Ако имате въпроси относно своята поръчка ни пишете на e-mail <a href="mailto:{{var store_email}}" style="color:#1E7EC8;">{{var store_email}}</a>
                            {{depend store_phone}} или ни се обадете на {{var store_phone}} в работни дни от {{var store_hours}}{{/depend}}.
                        </p>
                </td>
            </tr>
            <tr>
                <td>
                    <h2 style="font-size:18px; font-weight:normal; margin:0;">Информационен бон #{{var invoice.increment_id}} за поръчка #{{var order.increment_id}}</h2>
                </td>
            </tr>
            <tr>
                <td>
                    <table cellspacing="0" cellpadding="0" border="0" width="650">
                        <thead>
                        <tr>
                            <th align="left" width="325" bgcolor="#EAEAEA" style="font-size:13px; padding:5px 9px 6px 9px; line-height:1em;">Лични данни:</th>
                            <th width="10"></th>
                            <th align="left" width="325" bgcolor="#EAEAEA" style="font-size:13px; padding:5px 9px 6px 9px; line-height:1em;">Метод на плащане:</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td valign="top" style="font-size:12px; padding:7px 9px 9px 9px; border-left:1px solid #EAEAEA; border-bottom:1px solid #EAEAEA; border-right:1px solid #EAEAEA;">
                                {{var order.billing_address.format('html')}}
                            </td>
                            <td>&nbsp;</td>
                            <td valign="top" style="font-size:12px; padding:7px 9px 9px 9px; border-left:1px solid #EAEAEA; border-bottom:1px solid #EAEAEA; border-right:1px solid #EAEAEA;">
                                {{var payment_html}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <br/>
                    {{depend order.getIsNotVirtual()}}
                    <table cellspacing="0" cellpadding="0" border="0" width="650">
                        <thead>
                        <tr>
                            <th align="left" width="650" bgcolor="#EAEAEA" style="font-size:13px; padding:5px 9px 6px 9px; line-height:1em;">Адрес за доставка:</th>
                            <th width="10"></th>
                            <th align="left" width="650" bgcolor="#EAEAEA" style="font-size:13px; padding:5px 9px 6px 9px; line-height:1em;">Метод на доставка:</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td valign="top" style="font-size:12px; padding:7px 9px 9px 9px; border-left:1px solid #EAEAEA; border-bottom:1px solid #EAEAEA; border-right:1px solid #EAEAEA;">
                                {{var order.shipping_address.format('html')}}
                                &nbsp;
                            </td>
                            <td>&nbsp;</td>
                            <td valign="top" style="font-size:12px; padding:7px 9px 9px 9px; border-left:1px solid #EAEAEA; border-bottom:1px solid #EAEAEA; border-right:1px solid #EAEAEA;">
                                {{var order.shipping_description}}
                                &nbsp;
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <br/>
                    {{/depend}}
                    {{layout handle="sales_email_order_invoice_items" invoice=$invoice order=$order}}
                    <p style="font-size:12px; margin:0 0 10px 0">{{var comment}}</p>
                </td>
            </tr>
            <tr>
                <td bgcolor="#EAEAEA" align="center" style="background:#EAEAEA; text-align:center;"><center><p style="font-size:12px; margin:0;">Благодарим Ви, <strong>{{var store.getFrontendName()}}</strong></strong></p></center></td>
            </tr>
        </table>
    </td>
</tr>
</table>
</div>
</body>