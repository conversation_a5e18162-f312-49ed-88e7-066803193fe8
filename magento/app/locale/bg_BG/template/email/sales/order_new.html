<!--@subject {{var store.getFrontendName()}}: Нова поръчка @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"htmlescape var=$order.getCustomerName()":"Customer Name",
"var store.getFrontendName()":"Store Name",
"store url=\"customer/account/\"":"Customer Account Url",
"var order.increment_id":"Order Id",
"var order.getCreatedAtFormated('long')":"Order Created At (datetime)",
"var order.getBillingAddress().format('html')":"Billing Address",
"var payment_html":"Payment Details",
"var order.getShippingAddress().format('html')":"Shipping Address",
"var order.getShippingDescription()":"Shipping Description",
"layout handle=\"sales_email_order_items\" order=$order":"Order Items Grid",
"var order.getEmailCustomerNote()":"Email Order Note"}
@-->
<!--@styles
@-->

{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr style="position: relative">
        <td>
            <table cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td style="position: relative">
                        <a style="position: absolute;top: 50px;left: 10px;" href="{{store url=""}}">
                        <img
                                {{if logo_width}}
                                width="{{var logo_width}}"
                                {{else}}
                                width="165"
                                {{/if}}

                        {{if logo_height}}
                        height="{{var logo_height}}"
                        {{else}}
                        height="48"
                        {{/if}}

                        src="{{var logo_url}}"
                        alt="{{var logo_alt}}"
                        border="0"/>
                        </a>
                        <img src="{{skin url="images/email/headerEmailPraktis.png" _area="frontend" _theme="praktis" _package="praktis"}}">
                        <span style="position: absolute;right: 8%;top: 45%;">
                            <h4><strong>Въпроси относно поръчката</strong></h4>
                            <p style="text-align: left">
                                {{depend store_phone}}
                                <b>Телефон:</b> <a href="тел:{{var phone}}">{{var store_phone}}</a><br>
                                {{/depend}}
                                {{depend store_email}}
                                <b>Email:</b> <a href="mailto:{{var store_email}}">{{var store_email}}</a>
                                {{/depend}}
                            </p>
                        </span>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
    <tr>
        <td class="order-details">
            <h2>Благодарим ви за поръчката от praktis.</h2>
            <h3>Вашата поръчка е с номер <span class="no-link">#{{var order.increment_id}}</span></h3>
            <p style="margin: 0">След като подготвим и изпратим Вашата пратка, ще получите втори email  с потвърждение, че
                поръчката пътува към Вас.</p>
            <p style="margin: 0">Подробности за Вашата поръчка са по-долу.</p>
            <p style="margin: 0">Благодарим Ви отново.</p>
        </td>
    </tr>
    <tr >
        <td class="section-header">
            Твоите продукти
        </td>
    </tr>
    <tr class="order-information">
        <td>
            {{if order.getEmailCustomerNote()}}
            <table cellspacing="0" cellpadding="0" class="message-container">
                <tr>
                    <td>{{var order.getEmailCustomerNote()}}</td>
                </tr>
            </table>
            {{/if}}
            {{layout handle="sales_email_order_items" order=$order}}
        </td>
    </tr>
    <tr >
        <td class="section-header">
            Информация за доставка
        </td>
    </tr>
    <tr>
        <table cellpadding="0" cellspacing="0" border="0">
            <tr>
                <td class="address-details">
                    <h6>За сметка:</h6>
                    <p><span class="no-link">{{var order.getBillingAddress().format('html')}}</span></p>
                </td>
                {{depend order.getIsNotVirtual()}}
                <td class="address-details">
                    <h6>Пратка за</h6>
                    <p><span class="no-link">{{var order.getShippingAddress().format('html')}}</span></p>
                </td>
                {{/depend}}
            </tr>
            <tr>
                {{depend order.getIsNotVirtual()}}
                <td class="method-info">
                    <h6>Начин на доставка:</h6>
                    <p>{{var order.shipping_description}}</p>
                </td>
                {{/depend}}
                <td class="method-info">
                    <h6>Начин на плащане:</h6>
                    {{var payment_html}}
                </td>
            </tr>
        </table>
    </tr>
</table>

{{template config_path="design/email/footer"}}