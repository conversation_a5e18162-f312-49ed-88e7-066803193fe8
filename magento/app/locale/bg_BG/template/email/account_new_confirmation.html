<!--@subject Потвърждаване на профил на {{var customer.name}} @-->
<!--@vars
{"store url=\"\"":"Store Url",
"var logo_url":"Email Logo Image Url",
"var logo_alt":"Email Logo Image Alt",
"store url=\"customer/account/\"":"Customer Account Url",
"htmlescape var=$customer.name":"Customer Name",
"var customer.email":"Customer Email",
"store url=\"customer/account/confirm/\" _query_id=$customer.id _query_key=$customer.confirmation _query_back_url=$back_url":"Confirmation Url",
"htmlescape var=$customer.password":"Customer password"}
@-->

<!--@styles
@-->

{{template config_path="design/email/header"}}
{{inlinecss file="email-inline.css"}}

<table cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td>
            <table cellpadding="0" cellspacing="0" border="0">
                <tr>
                    <td class="action-content">
                        <h1>{{htmlescape var=$customer.name}},</h1>
                        <p>Вашия email {{var customer.email}} трябва да бъде потвърден преди да влезете в профила си.</p>
                        <p class="highlighted-text">
                            Използвайте следните стойности, когато влизате в профила си:<br/>
                            <strong>Email:</strong> {{var customer.email}}<br/>
                            <strong>Парола:</strong> {{htmlescape var=$customer.password}}
                        </p>
                        <p>Натиснете тук, за да потвърдите електронната си поща и да влезете веднага в профила си (the link is valid only once):</p>
                        <table cellspacing="0" cellpadding="0" class="action-button" >
                            <tr>
                                <td>
                                    <a href="{{store url="customer/account/confirm/" _query_id=$customer.id _query_key=$customer.confirmation _query_back_url=$back_url}}"><span>Потвърждаване на профила</span></a>
                                </td>
                            </tr>
                        </table>
                        <p>
                            Ако имате въпроси, не се колебайте да се свържете с нас на
                            <a href="mailto:{{var store_email}}">{{var store_email}}</a>
                            {{depend store_phone}} или на телефон <a href="tel:{{var phone}}">{{var store_phone}}</a>{{/depend}}.
                        </p>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
</table>

{{template config_path="design/email/footer"}}
