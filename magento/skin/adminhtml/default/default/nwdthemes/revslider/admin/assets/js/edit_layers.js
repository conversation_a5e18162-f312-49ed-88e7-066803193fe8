var UniteLayersRev,timeline_timer="",add_meta_into="";!function(jQuery){var La;!function(e){"use strict";var t=e.GreenSockGlobals||e,r=function(e){var r,a=e.split("."),i=t;for(r=0;a.length>r;r++)i[a[r]]=i=i[a[r]]||{};return i}("com.greensock.utils"),a=function(e){var t=e.nodeType,r="";if(1===t||9===t||11===t){if("string"==typeof e.textContent)return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)r+=a(e)}else if(3===t||4===t)return e.nodeValue;return r},i=document,o=i.defaultView?i.defaultView.getComputedStyle:function(){},s=/([A-Z])/g,n=function(e,t,r,a){var i;return(r=r||o(e,null))?i=(e=r.getPropertyValue(t.replace(s,"-$1").toLowerCase()))||r.length?e:r[t]:e.currentStyle&&(i=(r=e.currentStyle)[t]),a?i:parseInt(i,10)||0},l=function(e){return!!(e.length&&e[0]&&(e[0].nodeType&&e[0].style&&!e.nodeType||e[0].length&&e[0][0]))},d=")eefec303079ad17405c",y=/(?:<br>|<br\/>|<br \/>)/gi,_="<div style='position:relative;display:inline-block;"+(i.all&&!i.addEventListener?"*display:inline;*zoom:1;'":"'"),u=function(e){var t=-1!==(e=e||"").indexOf("++"),r=1;return t&&(e=e.split("++").join("")),function(){return _+(e?" class='"+e+(t?r++:"")+"'>":">")}},c=r.SplitText=t.SplitText=function(e,t){if("string"==typeof e&&(e=c.selector(e)),!e)throw"cannot split a null element.";this.elements=l(e)?function(e){var t,r,a,i=[],o=e.length;for(t=0;o>t;t++)if(r=e[t],l(r))for(a=r.length,a=0;r.length>a;a++)i.push(r[a]);else i.push(r);return i}(e):[e],this.chars=[],this.words=[],this.lines=[],this._originals=[],this.vars=t||{},this.split(t)},m=function(e,t,r,s,l){y.test(e.innerHTML)&&(e.innerHTML=e.innerHTML.replace(y,d));var _,c,m,p,v,g,h,f,j,b,Q,k,w,x=a(e),L=t.type||t.split||"chars,words,lines",C=-1!==L.indexOf("lines")?[]:null,S=-1!==L.indexOf("words"),I=-1!==L.indexOf("chars"),z="absolute"===t.position||!0===t.absolute,A=z?"&#173; ":" ",E=-999,B=o(e),T=n(e,"paddingLeft",B),V=n(e,"borderBottomWidth",B)+n(e,"borderTopWidth",B),R=n(e,"borderLeftWidth",B)+n(e,"borderRightWidth",B),O=n(e,"paddingTop",B)+n(e,"paddingBottom",B),P=n(e,"paddingLeft",B)+n(e,"paddingRight",B),F=n(e,"textAlign",B,!0),D=e.clientHeight,q=e.clientWidth,H=x.length,N="</div>",M=u(t.wordsClass),U=u(t.charsClass),W=-1!==(t.linesClass||"").indexOf("++"),G=t.linesClass;for(W&&(G=G.split("++").join("")),m=M(),p=0;H>p;p++)")"===(g=x.charAt(p))&&x.substr(p,20)===d?(m+=N+"<BR/>",p!==H-1&&(m+=" "+M()),p+=19):" "===g&&" "!==x.charAt(p-1)&&p!==H-1?(m+=N,p!==H-1&&(m+=A+M())):m+=I&&" "!==g?U()+g+"</div>":g;for(e.innerHTML=m+N,H=(v=e.getElementsByTagName("*")).length,h=[],p=0;H>p;p++)h[p]=v[p];if(C||z)for(p=0;H>p;p++)((c=(f=h[p]).parentNode===e)||z||I&&!S)&&(j=f.offsetTop,C&&c&&j!==E&&"BR"!==f.nodeName&&(_=[],C.push(_),E=j),z&&(f._x=f.offsetLeft,f._y=j,f._w=f.offsetWidth,f._h=f.offsetHeight),C&&(S!==c&&I||(_.push(f),f._x-=T),c&&p&&(h[p-1]._wordEnd=!0)));for(p=0;H>p;p++)c=(f=h[p]).parentNode===e,"BR"!==f.nodeName?(z&&(Q=f.style,S||c||(f._x+=f.parentNode._x,f._y+=f.parentNode._y),Q.left=f._x+"px",Q.top=f._y+"px",Q.position="absolute",Q.display="block",Q.width=f._w+1+"px",Q.height=f._h+"px"),S?c?s.push(f):I&&r.push(f):c?(e.removeChild(f),h.splice(p--,1),H--):!c&&I&&(j=!C&&!z&&f.nextSibling,e.appendChild(f),j||e.appendChild(i.createTextNode(" ")),r.push(f))):C||z?(e.removeChild(f),h.splice(p--,1),H--):S||e.appendChild(f);if(C){for(z&&(b=i.createElement("div"),e.appendChild(b),k=b.offsetWidth+"px",j=b.offsetParent===e?0:e.offsetLeft,e.removeChild(b)),Q=e.style.cssText,e.style.cssText="display:none;";e.firstChild;)e.removeChild(e.firstChild);for(w=!z||!S&&!I,p=0;C.length>p;p++){for(_=C[p],(b=i.createElement("div")).style.cssText="display:block;text-align:"+F+";position:"+(z?"absolute;":"relative;"),G&&(b.className=G+(W?p+1:"")),l.push(b),H=_.length,v=0;H>v;v++)"BR"!==_[v].nodeName&&(f=_[v],b.appendChild(f),w&&(f._wordEnd||S)&&b.appendChild(i.createTextNode(" ")),z&&(0===v&&(b.style.top=f._y+"px",b.style.left=T+j+"px"),f.style.top="0px",j&&(f.style.left=f._x-j+"px")));S||I||(b.innerHTML=a(b).split(String.fromCharCode(160)).join(" ")),z&&(b.style.width=k,b.style.height=f._h+"px"),e.appendChild(b)}e.style.cssText=Q}z&&(D>e.clientHeight&&(e.style.height=D-O+"px",D>e.clientHeight&&(e.style.height=D+V+"px")),q>e.clientWidth&&(e.style.width=q-P+"px",q>e.clientWidth&&(e.style.width=q+R+"px")))},p=c.prototype;p.split=function(e){this.isSplit&&this.revert(),this.vars=e||this.vars,this._originals.length=this.chars.length=this.words.length=this.lines.length=0;for(var t=0;this.elements.length>t;t++)this._originals[t]=this.elements[t].innerHTML,m(this.elements[t],this.vars,this.chars,this.words,this.lines);return this.isSplit=!0,this},p.revert=function(){if(!this._originals)throw"revert() call wasn't scoped properly.";for(var e=this._originals.length;--e>-1;)this.elements[e].innerHTML=this._originals[e];return this.chars=[],this.words=[],this.lines=[],this.isSplit=!1,this},c.selector=e.$||e.jQuery||function(t){return e.$?(c.selector=e.$,e.$(t)):i?i.getElementById("#"===t.charAt(0)?t.substr(1):t):t}}(window||{}),La=jQuery,La.widget("ui.rotatable",La.ui.mouse,{options:{handle:!1,angle:!1,start:null,rotate:null,stop:null},handle:function(e){return void 0===e?this.options.handle:void(this.options.handle=e)},angle:function(e){return void 0===e?this.options.angle:(this.options.angle=e,void this.performRotation(this.options.angle))},_create:function(){var e;this.options.handle?e=this.options.handle:(e=La(document.createElement("div"))).addClass("ui-rotatable-handle"),this.listeners={rotateElement:La.proxy(this.rotateElement,this),startRotate:La.proxy(this.startRotate,this),stopRotate:La.proxy(this.stopRotate,this)},e.draggable({helper:"clone",start:this.dragStart,handle:e}),e.bind("mousedown",this.listeners.startRotate),e.appendTo(this.element),0!=this.options.angle?(this.elementCurrentAngle=this.options.angle,this.performRotation(this.elementCurrentAngle)):this.elementCurrentAngle=0},_destroy:function(){this.element.removeClass("ui-rotatable"),this.element.find(".ui-rotatable-handle").remove()},performRotation:function(e){var t=180*e/Math.PI;punchgs.TweenLite.set(this.element,{rotationZ:t+"deg"})},getElementOffset:function(){this.performRotation(0);var e=this.element.offset();return this.performRotation(this.elementCurrentAngle),e},getElementCenter:function(){var e=this.getElementOffset(),t=e.left+this.element.width()/2,r=e.top+this.element.height()/2;return Array(t,r)},dragStart:function(){return!this.element&&void 0},startRotate:function(e){var t=this.getElementCenter(),r=e.pageX-t[0],a=e.pageY-t[1];return this.mouseStartAngle=Math.atan2(a,r),this.elementStartAngle=this.elementCurrentAngle,this.hasRotated=!1,this._propagate("start",e),La(document).bind("mousemove",this.listeners.rotateElement),La(document).bind("mouseup",this.listeners.stopRotate),!1},rotateElement:function(e){if(!this.element)return!1;var t=this.getElementCenter(),r=e.pageX-t[0],a=e.pageY-t[1],i=Math.atan2(a,r)-this.mouseStartAngle+this.elementStartAngle;this.performRotation(i);var o=this.elementCurrentAngle;return this.elementCurrentAngle=i,this._propagate("rotate",e),o!=i&&(this._trigger("rotate",e,this.ui()),this.hasRotated=!0),!1},stopRotate:function(e){return this.element?(La(document).unbind("mousemove",this.listeners.rotateElement),La(document).unbind("mouseup",this.listeners.stopRotate),this.elementStopAngle=this.elementCurrentAngle,this.hasRotated&&this._propagate("stop",e),setTimeout(function(){this.element=!1},10),!1):void 0},_propagate:function(e,t){La.ui.plugin.call(this,e,[t,this.ui()]),"rotate"!==e&&this._trigger(e,t,this.ui())},plugins:{},ui:function(){return{element:this.element,angle:{start:this.elementStartAngle,current:this.elementCurrentAngle,stop:this.elementStopAngle}}}}),jQuery(document).ready(function(){UniteLayersRev.setGlobalAction(wp.template("rs-action-layer-wrap")),UniteLayersRev.setGlobalSlideImport(wp.template("rs-import-layer-wrap"))}),jQuery(document).ready(function(){jQuery.widget("custom.catcomplete",jQuery.ui.autocomplete,{_create:function(){this._super(),this.widget().menu("option","items","> :not(.ui-autocomplete-category)")},_renderMenu:function(e,t){var r=this,a="";t&&jQuery.each(t,function(t,i){var o;i.version!=a&&(e.closest("#tp-thelistoffonts").length>0?e.append("<li class='ui-autocomplete-category' style='font-size: 24px;'>"+i.version+"</li>"):e.append("<li class='ui-autocomplete-category' style='font-size: 24px;'>Version: "+i.version+"</li>"),a=i.version),o=r._renderItemData(e,i),i.version&&o.attr("aria-label",i.version+" : "+i.label)})}})}),jQuery(document).ready(function(){"undefined"!=typeof tp_color_picker_presets&&tp_color_picker_presets||(tp_color_picker_presets={colors:[],gradients:[]}),tp_color_picker_presets.colors||(tp_color_picker_presets.colors=[]),tp_color_picker_presets.gradients||(tp_color_picker_presets.gradients=[]),jQuery.tpColorPicker({custom:tp_color_picker_presets,onAjax:function(e,t,r,a,i){var o=!1,s=tp_color_picker_presets[r];switch(e){case"save":s[s.length]=t,o=!0;break;case"delete":for(var n=s.length,l=0;l<n;l++)for(var d in s[l])d===t&&s.splice(l,1);o=!0}o&&UniteAdminRev.ajaxRequest("save_color_preset",{presets:tp_color_picker_presets},function(e){var t=e.error||!1;jQuery(document).trigger(a,[t])})}})});var timeline_timer="",add_meta_into="";UniteLayersRev=new function(){var initTop=100,initLeft=100,initSpeed=300,initTopVideo=20,initLeftVideo=20,g_startTime=500,g_stepTime=0,g_slideTime,initText="Caption Text",initGroupName="Group",initRowName="Row",initColumnName="Column",layout="desktop",transSettings=[],t=this,u=tpLayerTimelinesRev,cm=tpLayerContextMenu,initArrFontTypes=[],containerID="#divLayers",container,update_layer_changes=!0,id_counter=0,initLayers=null,initDemoLayers=null,initStaticLayers=[],initDemoSettings=null,layerresized=!1,layerGeneralParamsStatus=!1,initLayerAnims=[],initLayerAnimsDefault=[],currentAnimationType="customin",curDemoSlideID=0,slideIDs={},alluniqueids=[],selectedLayerWidth=0,selectedlayerHeight=0,totalWidth=0,totalHeight=0,unique_layer_id=0,global_action_template=null,global_layer_import_template=null,updateRevTimer=0,updateRevChange=0,save_needed=!1,import_slides={},lastchangedinput="";t.core=5,t.sub=3,t.subsub=0,t.newlayercoord={x:-1,y:-1},u.layout=layout,t.groupMove={x:0,y:0},t.selectedLayerSerial=-1,t.selectedLayers=new Array,t.justDropped=!1,t.arrLayers={},t.arrLayersClone={},t.arrLayersDemo={},t.arrLayersChanges={undo:[],redo:[]},t.ignorAllUndoRedoLogs=!1,t.attributegroups=[{id:"0",icon:"move",groupname:"Layer Position",keys:["left.desktop","top.desktop","align_hor.desktop","align_vert.desktop","left.notebook","top.notebook","align_hor.notebook","align_vert.notebook","left.tablet","top.tablet","align_hor.tablet","align_vert.tablet","left.mobile","top.mobile","align_hor.mobile","align_vert.mobile"]},{id:"1",icon:"play",groupname:"Start Animation",keys:["text_c_start","bg_c_start","use_text_c_start","use_bg_c_start","x_rotate_start","x_rotate_start_reverse","x_start","x_start_reverse","y_rotate_start","y_rotate_start_reverse","y_start","y_start_reverse","z_rotate_start","z_rotate_start_reverse","z_start"]},{id:"2",icon:"play",groupname:"End Animation",keys:["text_c_end","bg_c_end","use_text_c_end","use_bg_c_end","x_end","x_end_reverse","x_origin_end","x_rotate_end","x_rotate_end_reverse","y_end","y_end_reverse","y_rotate_end","y_rotate_end_reverse","z_end","z_rotate_end","z_rotate_end_reverse"]},{id:"31",icon:"play",groupname:"Frames",keys:["splitdelay","animation","split","split_extratime","relative_time","easing","speed","time"]},{id:"3",icon:"play",groupname:"Loop Animation",keys:["loop_angle","loop_animation","loop_easing","loop_enddeg","loop_radius","loop_speed","loop_startdeg","loop_xend","loop_xorigin","loop_xstart","loop_yend","loop_yorigin","loop_ystart","loop_zoomend","loop_zoomstart"]},{id:"4",icon:"play",groupname:"Mask Animation",keys:["mask_ease_end","mask_ease_start","mask_end","mask_speed_end","mask_speed_start","mask_start","mask_x_end","mask_x_end_reverse","mask_x_start","mask_x_start_reverse","mask_y_end","mask_y_end_reverse","mask_y_start","mask_y_start_reverse"]},{id:"5",icon:"font",groupname:"Formatting",keys:["displaymode.desktop","displaymode.notebook","displaymode.tablet","displaymode.mobile","autolinebreak","whitespace.desktop","whitespace.notebook","whitespace.tablet","whitespace.mobile","html_tag","layer-selectable"]},{id:"6",icon:"cog",groupname:"Behavior",keys:["basealign","lazy-load","resizeme","resize-full","responsive_offset"]},{id:"7",icon:"eye",groupname:"Visibilty",keys:["hiddenunder","visible-desktop","visible-notebook","visible-tablet","visible-mobile","show-on-hover"]},{id:"8",icon:"resize-full",groupname:"Sizing",keys:["scaleProportional","video_data.fullwidth","video_data.cover","originalWidth","originalHeight","cover_mode","height","width","image-size","video_height.desktop","video_height.notebook","video_height.tablet","video_height.mobile","video_width.desktop","video_width.notebook","video_width.tablet","video_width.mobile","max_height.desktop","max_height.notebook","max_height.tablet","max_height.mobile","min_height.desktop","min_height.notebook","min_height.tablet","min_height.mobile","max_width.desktop","max_width.notebook","max_width.tablet","max_width.mobile","scaleX.desktop","scaleX.notebook","scaleX.tablet","scaleX.mobile","scaleY.desktop","scaleY.notebook","scaleY.tablet","scaleY.mobile"]},{id:"9",icon:"font",groupname:"Naming and Alias",keys:["text","alias"]},{id:"30",icon:"resize-vertical",groupname:"Order",keys:["order","p_uid"]},{id:"10",icon:"cancel",groupname:"Deleted",keys:["deleted"]},{id:"11",icon:"plus",groupname:"Created",keys:["layer_unavailable"]},{id:"12",icon:"link",groupname:"Source",keys:["svg.src","image_url"]},{id:"13",icon:"star",groupname:"SVG Style",keys:["svg.svgstroke-color","svg.svgstroke-dasharray","svg.svgstroke-dashoffset","svg.svgstroke-width","svg.svgstroke-hover-color","svg.svgstroke-hover-dasharray","svg.svgstroke-hover-dashoffset","svg.svgstroke-hover-width"]},{id:"14",icon:"palette",groupname:"Main Styling",keys:["static_styles.color.desktop","static_styles.color.notebook","static_styles.color.tablet","static_styles.color.mobile","static_styles.font-size.desktop","static_styles.font-size.notebook","static_styles.font-size.tablet","static_styles.font-size.mobile","static_styles.font-weight.desktop","static_styles.font-weight.notebook","static_styles.font-weight.tablet","static_styles.font-weight.mobile","static_styles.line-height.desktop","static_styles.line-height.notebook","static_styles.line-height.tablet","static_styles.line-height.mobile"]},{id:"15",icon:"thumbs-up",groupname:"Layer Action",keys:["layer_action","layer_action.action","layer_action.action_delay","layer_action.actioncallback","layer_action.image_link","layer_action.jump_to_slide","layer_action.jump_to_slide","layer_action.link_open_in","layer_action.link_type","layer_action.scrollunder_offset","layer_action.toggle_class","layer_action.toggle_layer_type","layer_action.tooltip_event"]},{id:"16",icon:"droplet",groupname:"Styling Text",keys:["deformation.font-family","deformation.font-style","deformation.opacity","deformation.text-align","deformation.vertical-align","deformation.display","deformation.text-decoration"]},{id:"17",icon:"droplet",groupname:"Styling Padding",keys:["deformation.padding","deformation.padding.0","deformation.padding.1","deformation.padding.2","deformation.padding.3"]},{id:"17",icon:"droplet",groupname:"Styling Margin",keys:["margin.desktop","margin.notebook","margin.tablet","margin.mobile"]},{id:"18",icon:"droplet",groupname:"Styling Corners",keys:["deformation.corner_left","deformation.corner_right"]},{id:"19",icon:"droplet",groupname:"Styling Background",keys:["deformation.background-color"]},{id:"20",icon:"droplet",groupname:"Styling Border",keys:["deformation.border-color","deformation.border-radius.0","deformation.border-radius.1","deformation.border-radius.2","deformation.border-radius.3","deformation.border-style","deformation.border-width"]},{id:"21",icon:"droplet",groupname:"Styling Transforms",keys:["deformation.2d_origin_x","deformation.2d_origin_y","deformation.parallax","deformation.pers","deformation.scalex","deformation.scaley","deformation.skewx","deformation.skewy","deformation.text-transform","deformation.x","deformation.xrotate","deformation.y","deformation.yrotate","deformation.z"]},{id:"22",icon:"droplet",groupname:"Styling Hover",keys:["hover","deformation-hover.css_cursor","deformation-hover.easing"]},{id:"23",icon:"droplet",groupname:"Styling Hover Text",keys:["deformation-hover.color","deformation-hover.font-family","deformation-hover.font-style","deformation-hover.opacity","deformation-hover.text-align","deformation-hover.vertical-align","deformation-hover.text-decoration"]},{id:"24",icon:"droplet",groupname:"Styling Hover Padding",keys:["deformation-hover.padding","deformation-hover.padding.0","deformation-hover.padding.1","deformation-hover.padding.2","deformation-hover.padding.3"]},{id:"24",icon:"droplet",groupname:"Styling Hover Margin",keys:["deformation-hover.margin","deformation-hover.margin.0","deformation-hover.margin.1","deformation-hover.margin.2","deformation-hover.margin.3"]},{id:"25",icon:"droplet",groupname:"Styling Hover Corners",keys:["deformation-hover.corner_left","deformation-hover.corner_right"]},{id:"26",icon:"droplet",groupname:"Styling Hover Background",keys:["deformation-hover.background-color"]},{id:"27",icon:"droplet",groupname:"Styling Hover Border",keys:["deformation-hover.border-color","deformation-hover.border-radius.0","deformation-hover.border-radius.1","deformation-hover.border-radius.2","deformation-hover.border-radius.3","deformation-hover.border-style","deformation-hover.border-width"]},{id:"28",icon:"droplet",groupname:"Styling Hover Transforms",keys:["deformation-hover.2d_origin_x","deformation-hover.2d_origin_y","deformation-hover.parallax","deformation-hover.pers","deformation-hover.scalex","deformation-hover.scaley","deformation-hover.skewx","deformation-hover.skewy","deformation-hover.text-transform","deformation-hover.x","deformation-hover.xrotate","deformation-hover.y","deformation-hover.yrotate","deformation-hover.z"]},{id:"29",icon:"video",groupname:"Video/Audo Settings",keys:["video_data.urlAudio","video_data.previewimage","video_data.link","video_data.thumb_medium.url","video_type","video_data.id","video_data.video_type","video_data.title","video_data.author","video_data.description","video_data.args","video_data.autoplay","video_data.nextslide","video_data.forcerewind","video_data.controls","video_data.mute","video_data.stopallvideo","video_data.allowfullscreen","video_data.videoloop","video_data.show_cover_pause","video_data.start_at","video_data.end_at","video_data.volume","video_data.desc_small","video_data.thumb_small.url","video_data.thumb_small.width","video_data.thumb_small.height","video_data.thumb_medium.width","video_data.thumb_medium.height","video_data.video_height"]},{id:"30",icon:"droplet",groupname:"Styling Hover Pointer Events",keys:["deformation-hover.pointer_events"]}],t.addon_callbacks=[],t.set_save_needed=function(e){save_needed=e},t.ulff_core=0,t.setGlobalAction=function(e){global_action_template=e},t.setGlobalSlideImport=function(e){global_layer_import_template=e},t.getLayout=function(){return layout},t.setInitSlideIds=function(e){slideIDs=jQuery.parseJSON(e)},t.setInitLayersJson=function(e){initLayers=jQuery.parseJSON(e)},t.setInitStaticLayersJson=function(e){initStaticLayers=jQuery.parseJSON(e)},t.setInitDemoLayersJson=function(e){initDemoLayers=jQuery.parseJSON(e)},t.setInitDemoSettingsJson=function(e){initDemoSettings=jQuery.parseJSON(e)},t.setInitLayerAnim=function(e){initLayerAnims=jQuery.parseJSON(e)},t.setInitLayerAnimsDefault=function(e){initLayerAnimsDefault=jQuery.parseJSON(e)},t.setInitTransSetting=function(e){transSettings=jQuery.parseJSON(e)},t.updateInitLayerAnim=function(e){initLayerAnims=[],initLayerAnims=e},t.setInitCaptionClasses=function(e){initArrCaptionClasses=jQuery.parseJSON(e)},t.setCaptionClasses=function(e){initArrCaptionClasses=e},t.setInitFontTypes=function(e){initArrFontTypes=jQuery.parseJSON(e)},t.getMaintime=function(){return g_slideTime},t.setMaintime=function(e){g_slideTime=e},t.getObjectLength=function(e){var t=0;for(var r in e)e.hasOwnProperty(r)&&t++;return t},t.sortFontTypesByUsage=function(){for(var e in sgfamilies)if(sgfamilies.hasOwnProperty(e))for(var t in initArrFontTypes)if(initArrFontTypes.hasOwnProperty(t)&&void 0!==initArrFontTypes[t]&&void 0!==initArrFontTypes[t].label&&void 0!==sgfamilies[e]&&initArrFontTypes[t].label.replace(/\ /g,"+")==sgfamilies[e].replace(/\ /g,"+")){void 0===initArrFontTypes[t].top&&(initArrFontTypes[t].top=!0);break}var r={},a=0;for(var i in initArrFontTypes)initArrFontTypes.hasOwnProperty(i)&&(r[a]=initArrFontTypes[i],a++);initArrFontTypes=r},t.setVal=function(e,t,r,a,i,o){if(-1!==jQuery.inArray(t,transSettings))if(a)"object"!=typeof e[t]&&(e[t]={}),null==o||0==o?e[t][layout]=r:(e[t].desktop=r,e[t].notebook=r,e[t].tablet=r,e[t].mobile=r);else if(null!=i)for(var s in i)i.hasOwnProperty(s)&&(void 0!==e[t]&&void 0!==e[t][i[s]]?e[t][i[s]]=r:(void 0===e[t]&&(e[t]={}),e[t][i[s]]=r));else void 0!==e[t]&&void 0!==e[t][layout]?e[t][layout]=r:(void 0===e[t]&&(e[t]={}),e[t][layout]=r);else e[t]=r;return e},t.getVal=function(e,t,r){if(r=void 0===r?layout:r,void 0!==e){if(-1===jQuery.inArray(t,transSettings))return void 0!==e[t]&&"object"!=typeof e[t]?e[t]:void 0;if(void 0!==e[t]&&void 0!==e[t][r])return e[t][r];if(void 0!==e[t]&&"object"!=typeof e[t])return e[t];if(void 0!==e[t]){var a="novalue";switch(r){case"desktop":void 0!==e[t].notebook?(a=e[t].notebook,"notebook"):void 0!==e[t].tablet?(a=e[t].tablet,"tablet"):void 0!==e[t].mobile&&(a=e[t].mobile,"mobile");break;case"notebook":void 0!==e[t].desktop?(a=e[t].desktop,"desktop"):void 0!==e[t].tablet?(a=e[t].tablet,"tablet"):void 0!==e[t].mobile&&(a=e[t].mobile,"mobile");break;case"tablet":void 0!==e[t].notebook?(a=e[t].notebook,"notebook"):void 0!==e[t].desktop?(a=e[t].desktop,"desktop"):void 0!==e[t].mobile&&(a=e[t].mobile,"mobile");break;case"mobile":void 0!==e[t].tablet?(a=e[t].tablet,"tablet"):void 0!==e[t].notebook?(a=e[t].notebook,"notebook"):void 0!==e[t].desktop&&(a=e[t].desktop,"desktop")}if("novalue"!=a)return a}}},t.insertTemplate=function(e){if(""===add_meta_into){if(-1==t.selectedLayerSerial)return!1;e.lastIndexOf("%")<0&&e.lastIndexOf("]")<0?jQuery("#layer_text").val(jQuery("#layer_text").val()+"{{"+e+"}}"):jQuery("#layer_text").val(jQuery("#layer_text").val()+e),t.updateLayerFromFields()}else"object"==jQuery.type(add_meta_into)?add_meta_into.val(add_meta_into.val()+"{{"+e+"}}").trigger("change"):jQuery('input[name="'+add_meta_into+'"]').val(jQuery('input[name="'+add_meta_into+'"]').val()+"{{"+e+"}}");jQuery("#dialog_template_insert").dialog("close")},t.refreshGridSize=function(){var e=jQuery("#hor-css-linear .helplines-offsetcontainer"),r=jQuery("#ver-css-linear .helplines-offsetcontainer"),a=jQuery("#rs-grid-sizes option:selected").val(),i=jQuery("#rs-grid-snapto option:selected").val(),o=jQuery("#divLayers");if("custom"!=a){o.css({position:"relative"}),o.find("#helpergrid").remove(),o.append('<div id="helpergrid" style="position:absolute;top:0px;left:0px;position:absolute;z-index:0;width:100%;height:100%"></div>');var s=o.find("#helpergrid");if(a>4){for(var n=1;n<o.height()/a;n++){var l=n*a;s.append('<div class="helplines" style="background-color:#4affff;width:100%;height:1px;position:absolute;left:0px;top:'+l+'px"></div>')}for(n=1;n<o.width()/a;n++){l=n*a;s.append('<div class="helplines" style="background-color:#4affff;height:100%;width:1px;position:absolute;top:0px;left:'+l+'px"></div>')}}punchgs.TweenLite.to(e,.3,{autoAlpha:0}),punchgs.TweenLite.to(r,.3,{autoAlpha:0})}else{punchgs.TweenLite.to(e,.3,{autoAlpha:1}),punchgs.TweenLite.to(r,.3,{autoAlpha:1}),o.find("#helpergrid").remove();try{e.find(".helplines").draggable("destroy")}catch(e){}try{r.find(".helplines").draggable("destroy")}catch(e){}e.find(".helplines").draggable({handle:".helpline-drag",axis:"x"}),r.find(".helplines").draggable({handle:".helpline-drag",axis:"y"})}for(var d in t.arrLayers)if(t.arrLayers.hasOwnProperty(d)){t.getHtmlLayerFromSerial(d).draggable({drag:t.onLayerDrag,snap:i,snapMode:"outer"})}},t.add_missing_unique_ids=function(){var e=t.getSimpleLayers(),r={};for(var a in e)e.hasOwnProperty(a)&&(void 0===e[a].unique_id?r[a]=!0:e[a].unique_id>unique_layer_id&&(unique_layer_id=e[a].unique_id));for(var a in r)if(r.hasOwnProperty(a)){unique_layer_id++;var i={};i.unique_id=unique_layer_id,update_layer_changes=!1,t.updateLayer(a,i),update_layer_changes=!0}},t.init=function(slideTime){null!=jQuery().draggable&&null!=jQuery().autocomplete||jQuery("#jqueryui_error_message").show(),jQuery("body").addClass("rs-layer-editor-view"),g_slideTime=Number(slideTime),u.init(g_slideTime),container=jQuery(containerID);var demoRows=new Array;if(initDemoLayers){var len=initDemoLayers.length;if(len)for(var i=0;i<len;i++)for(var key in initDemoLayers[i])initDemoLayers[i].hasOwnProperty(key)&&(curDemoSlideID=i,"row"===initDemoLayers[i][key].type||"column"===initDemoLayers[i][key].type?demoRows.push(initDemoLayers[i][key].unique_id):-1==jQuery.inArray(initDemoLayers[i][key].p_uid,demoRows)&&addLayer(initDemoLayers[i][key],!0,!0,!0));else for(var i in initDemoLayers)if(initDemoLayers.hasOwnProperty(i))for(var key in initDemoLayers[i])initDemoLayers[i].hasOwnProperty(key)&&(curDemoSlideID=i,"row"===initDemoLayers[i][key].type||"column"===initDemoLayers[i][key].type?demoRows.push(initDemoLayers[i][key].unique_id):-1==jQuery.inArray(initDemoLayers[i][key].p_uid,demoRows)&&addLayer(initDemoLayers[i][key],!0,!0,!0))}u.organiseGroupsAndLayer(!1,!0);var len=initLayers.length;if(initLayers){if(len){for(var i=0;i<len;i++)initLayers[i].addedToStage=!1;for(var i=0;i<len&&"row"!==initLayers[i].type;i++)"row"!==initLayers[i].type&&"column"!==initLayers[i].type&&("group"===initLayers[i].type&&-1!==initLayers[i].p_uid||(initLayers[i].addedToStage=!0,addLayer(initLayers[i],!0,!1,!0)));t.add_missing_unique_ids();for(var i=0;i<len&&"row"!=initLayers[i].type;i++)!0!==initLayers[i].addedToStage&&"row"!==initLayers[i].type&&"column"!==initLayers[i].type&&"group"!==initLayers[i].type&&(initLayers[i].addedToStage=!0,addLayer(initLayers[i],!0,!1,!0));t.add_missing_unique_ids();for(var i=0;i<len;i++)!0===initLayers[i].addedToStage||"group"!==initLayers[i].type&&"row"!==initLayers[i].type||(initLayers[i].addedToStage=!0,addLayer(initLayers[i],!0,!1,!0));t.add_missing_unique_ids();for(var i=0;i<len;i++)"column"===initLayers[i].type&&(initLayers[i].addedToStage=!0,addLayer(initLayers[i],!0,!1,!0));t.add_missing_unique_ids();for(var i=0;i<len;i++)!0!==initLayers[i].addedToStage&&addLayer(initLayers[i],!0,!1,!0)}else{for(var i in initLayers)initLayers.hasOwnProperty(i)&&(initLayers[i].addedToStage=!1);for(var i in initLayers)if(initLayers.hasOwnProperty(i)){if("row"===initLayers[i].type)break;"row"!==initLayers[i].type&&"column"!==initLayers[i].type&&("group"===initLayers[i].type&&-1!==initLayers[i].p_uid||(initLayers[i].addedToStage=!0,addLayer(initLayers[i],!0,!1,!0)))}for(var i in t.add_missing_unique_ids(),initLayers)if(initLayers.hasOwnProperty(i)){if("row"==initLayers[i].type)break;!0!==initLayers[i].addedToStage&&"row"!==initLayers[i].type&&"column"!==initLayers[i].type&&"group"!==initLayers[i].type&&(initLayers[i].addedToStage=!0,addLayer(initLayers[i],!0,!1,!0))}for(var i in t.add_missing_unique_ids(),initLayers)initLayers.hasOwnProperty(i)&&(!0===initLayers[i].addedToStage||"group"!==initLayers[i].type&&"row"!==initLayers[i].type||(initLayers[i].addedToStage=!0,addLayer(initLayers[i],!0,!1,!0)));for(var i in t.add_missing_unique_ids(),initLayers)initLayers.hasOwnProperty(i)&&"column"===initLayers[i].type&&(initLayers[i].addedToStage=!0,addLayer(initLayers[i],!0,!1,!0));for(var i in t.add_missing_unique_ids(),initLayers)initLayers.hasOwnProperty(i)&&!0!==initLayers[i].addedToStage&&addLayer(initLayers[i],!0,!1,!0)}t.add_missing_unique_ids()}u.timeLineTableDimensionUpdate(),u.updateAllLayerTimeline(),disableFormFields(),initMainEvents(),initButtons(),initHtmlFields(),initAlignTable(),initLoopFunctions(),scaleAndResetLayerInit(),positionChanged_Core(),initBackgroundFunctions(),jQuery("#tp-thelistofclasses ul, #tp-thelistoffonts ul").bind("scroll",function(){var e=jQuery(this),t=e.scrollTop(),r=e.data("olds"),a=(e.parent().height(),null==r||r<t?"up":"down");e.find(".ui-autocomplete-category").each(function(e){var r=jQuery(this),i=r.next().position().top,o=0==e?0:15;"up"==a&&i<50||"down"==a&&i<0-o?(r.css({position:"absolute",top:t-o,zIndex:e}),r.next().css({marginTop:50+o})):(r.css({position:"relative",top:0,zIndex:e}),r.next().css({marginTop:0}))}),e.data("olds",t)}),jQuery(".bgsrcchanger").each(function(){jQuery(this)[0].checked&&initSliderMainOptions(jQuery(this))}),jQuery(".bgsrcchanger").click(function(){initSliderMainOptions(jQuery(this))}),jQuery("#alt_option").change(function(){"custom"==jQuery("#alt_option option:selected").val()?jQuery("#alt_attr").show():jQuery("#alt_attr").hide()}),jQuery("#title_option").change(function(){"custom"==jQuery("#title_option option:selected").val()?jQuery("#title_attr").show():jQuery("#title_attr").hide()}),initDisallowCaptionsOnClick(),jQuery(".open_right_panel").click(function(){var e=jQuery(".layer_props_wrapper");e.hasClass("openeed")?e.removeClass("openeed"):e.addClass("openeed")}),jQuery("#rs-grid-sizes, #rs-grid-snapto").change(function(){t.refreshGridSize()}),jQuery("#layer_alt_option").change(function(){"custom"==jQuery("#layer_alt_option option:selected").val()?jQuery("#layer_alt").show():jQuery("#layer_alt").hide()}),jQuery('select[name="rev_show_the_slides"]').change(function(){var e=jQuery("#divbgholder").find(".slotholder");if(jQuery(".demo_layer").addClass("invisible_demolayer"),e.addClass("trans_bg"),e.css("background-image","none"),e.css("background-color","transparent"),e.css("background-position","top center"),e.css("background-size","cover"),e.css("background-repeat","no-repeat"),jQuery(".tp-bgimg").css({backgroundImage:"none",backgroundColor:"transparent"}),"none"!==jQuery(this).val()){var t=jQuery(this).val();if(jQuery(".demo_layer_"+t).show(),jQuery(".demo_layer_"+t).removeClass("invisible_demolayer"),void 0!==initDemoSettings[t]){var r="percentage"==initDemoSettings[t].bg_fit?initDemoSettings[t].bg_fit_x+"% "+initDemoSettings[t].bg_fit_y+"% ":initDemoSettings[t].bg_fit,a="percentage"==initDemoSettings[t].bg_position?initDemoSettings[t].bg_position_x+"% "+initDemoSettings[t].bg_position_y+"% ":initDemoSettings[t].bg_position;switch("contain"==initDemoSettings[t].bg_fit?jQuery("#divLayers-wrapper").css("maxWidth",jQuery("#divbgholder").css("minWidth")):jQuery("#divLayers-wrapper").css("maxWidth","none"),initDemoSettings[t].background_type){case"image":case"meta":case"streamyoutube":case"streamvimeo":case"streaminstagram":case"youtube":case"vimeo":case"html5":var i=initDemoSettings[t].image;jQuery("#the_image_source_url").html(i),e.find(".defaultimg, .slotslidebg").css("background-image","url('"+i+"')"),e.find(".defaultimg, .slotslidebg").css("background-color","transparent"),e.find(".defaultimg, .slotslidebg").css("background-size",r),e.find(".defaultimg, .slotslidebg").css("background-position",a),e.find(".defaultimg, .slotslidebg").css("background-repeat",initDemoSettings[t].bg_repeat),e.find(".defaultimg, .slotslidebg").removeClass("trans_bg");break;case"trans":e.find(".defaultimg, .slotslidebg").css("background-image","none"),e.find(".defaultimg, .slotslidebg").css("background-color","transparent"),e.find(".defaultimg, .slotslidebg").addClass("trans_bg");break;case"solid":e.find(".defaultimg, .slotslidebg").css("background-image","none"),e.find(".defaultimg, .slotslidebg").removeClass("trans_bg");var o=window.RevColor.get(initDemoSettings[t].slide_bg_color);e.find(".defaultimg, .slotslidebg").css("background-color",o);break;case"external":i=initDemoSettings[t].slide_bg_external;jQuery("#the_image_source_url").html(i),e.find(".defaultimg, .slotslidebg").css("background-image","url('"+i+"')"),e.find(".defaultimg, .slotslidebg").css("background-color","transparent"),e.find(".defaultimg, .slotslidebg").css("background-size",r),e.find(".defaultimg, .slotslidebg").css("background-position",a),e.find(".defaultimg, .slotslidebg").css("background-repeat",initDemoSettings[t].bg_repeat),e.find(".defaultimg, .slotslidebg").removeClass("trans_bg")}}}jQuery("#divbgholder").css({backgroundImage:"none",backgroundColor:"transparent"})}),jQuery("#thumb_for_admin").on("change",function(){t.changeSlotBGs()}),jQuery("#slide_thumb_button_preview").find("div").length>0&&jQuery(".show_on_thumbnail_exist").show(),jQuery(".rs-slide-device_selector").click(function(){if(0!=rev_adv_resp_sizes){var e=jQuery(this).data("val");layout=e,u.layout=e;var r=0;jQuery(".row-zone-container").each(function(){r+=jQuery(this).height()}),r=r<rev_sizes[e][1]?rev_sizes[e][1]:r,jQuery(".tp-bgimg.defaultimg, #divLayers").css({width:rev_sizes[e][0],height:r}),"auto"!=__slidertype?(jQuery("#divbgholder").css({minWidth:rev_sizes[e][0],maxWidth:"100%",marginLeft:"auto",marginRight:"auto"}),jQuery("#divbgholder").css({minHeight:r,height:r}),jQuery(".slotholder .tp-bgimg.defaultimg").css({minWidth:rev_sizes[e][0],maxWidth:"100%"}),jQuery(".tp-bgimg.defaultimg").css({width:"100%"})):(jQuery("#divbgholder").css({minWidth:rev_sizes[e][0],maxWidth:rev_sizes[e][0],marginLeft:"auto",marginRight:"auto"}),jQuery("#divbgholder").css({minHeight:r,height:r}),jQuery(".slotholder .tp-bgimg.defaultimg").css({minWidth:rev_sizes[e][0],maxWidth:rev_sizes[e][0]})),jQuery("#divLayers-wrapper").css("height",r+1),jQuery(".rs-slide-device_selector").removeClass("selected"),jQuery(this).addClass("selected"),u.resetSlideAnimations(),jQuery(window).trigger("resize"),redrawAllLayerHtml(),t.setMiddleRowZone(100)}}),jQuery("#divbgholder .oldslotholder, #divbgholder .slotholder, #divbgholder .defaultimg, #top-toolbar-wrapper, #layer-settings-toolbar-bottom").on("click",function(e){1===(e.which||e.button)&&cm.toggleMenuOff(),e.target==this&&(u.checkMultipleSelectedItems(!0),unselectLayers())});var cfith=function(e,t){return e["deformation-hover"][t]=e.deformation[t],e},cfhti=function(e,t){return e.deformation[t]=e["deformation-hover"][t],e};jQuery(".copy-from-idle").click(function(){if(confirm(rev_lang.copy_styles_to_idle_from_hover)){var e=t.getCurrentLayer();e["deformation-hover"].color=t.getVal(e.static_styles,"color"),e["deformation-hover"]["2d_rotation"]=e["2d_rotation"],e=cfith(e,"2d_origin_x"),e=cfith(e,"2d_origin_y"),e=cfith(e,"background-color"),e=cfith(e,"border-color"),e=cfith(e,"border-radius"),e=cfith(e,"border-style"),e=cfith(e,"border-width"),e=cfith(e,"opacity"),e=cfith(e,"scalex"),e=cfith(e,"scaley"),e=cfith(e,"skewx"),e=cfith(e,"skewy"),e=cfith(e,"text-decoration"),e=cfith(e,"x"),e=cfith(e,"xrotate"),e=cfith(e,"y"),e=cfith(e,"yrotate"),e=cfith(e,"z"),t.updateLayerFormFields(t.selectedLayerSerial),t.updateLayerFromFields()}}),jQuery(".copy-from-hover").click(function(){if(confirm(rev_lang.copy_styles_to_hover_from_idle)){var e=t.getCurrentLayer();e["2d_rotation"]=e["deformation-hover"]["2d_rotation"],e.static_styles=t.setVal(e.static_styles,"color",e["deformation-hover"].color,!1),e=cfhti(e,"2d_origin_x"),e=cfhti(e,"2d_origin_y"),e=cfhti(e,"background-color"),e=cfhti(e,"border-color"),e=cfhti(e,"border-radius"),e=cfhti(e,"border-style"),e=cfhti(e,"border-width"),e=cfhti(e,"opacity"),e=cfhti(e,"scalex"),e=cfhti(e,"scaley"),e=cfhti(e,"skewx"),e=cfhti(e,"skewy"),e=cfhti(e,"text-decoration"),e=cfhti(e,"x"),e=cfhti(e,"xrotate"),e=cfhti(e,"y"),e=cfhti(e,"yrotate"),e=cfhti(e,"z"),t.updateLayerFormFields(t.selectedLayerSerial),t.updateLayerFromFields()}}),jQuery("#button_css_reset").click(function(){var e=t.getCurrentLayer();null!==e&&(t.reset_to_default_static_styles(e),updateSubStyleParameters(e,!0),t.updateLayerFromFields())});var getGridDimension=function(){var e=jQuery("#divLayers"),t=e.offset(),r=jQuery("#divLayers-wrapper").offset();return{top:t.top-r.top,left:t.left-r.left,bottom:t.top-r.top+e.height(),right:t.left-r.left+e.width()}};function edit_content_current_layer(){var e=t.getCurrentLayer();if(null!==e)switch(e.type){case"text":case"button":t.showHideContentEditor(!0),jQuery("#quick-layers-wrapper").slideUp(300),jQuery(".current-active-main-toolbar").removeClass("opened"),jQuery("#button_show_all_layer i").removeClass("eg-icon-up").addClass("eg-icon-menu"),jQuery("#layer_text").focus();break;case"video":var r=e.video_data;UniteAdminRev.openVideoDialog(function(e){var r=getVideoObjLayer(e);t.updateCurrentLayer(r),updateHtmlLayersFromObject(t.selectedLayerSerial),t.updateLayerFormFields(t.selectedLayerSerial),redrawLayerHtml(t.selectedLayerSerial),scaleNormalVideo()},r);break;case"image":"objectlibrary"===e.image_library?(jQuery("#dialog_addobj").data("changeimg",!0),jQuery("#dialog_addobj").data("changeimgserial",e.serial),t.callObjectLibraryDialog("object")):UniteAdminRev.openAddImageDialog(rev_lang.select_layer_image,function(e){var r={};r.image_url=e,t.updateCurrentLayer(r),redrawLayerHtml(t.selectedLayerSerial),t.add_layer_change(),scaleNormal()});break;case"shape":break;case"svg":jQuery("#dialog_addobj").data("changesvg",!0),jQuery("#dialog_addobj").data("changesvgserial",e.serial),t.callObjectLibraryDialog("object");break;case"audio":r=e.video_data;UniteAdminRev.openVideoDialog(function(r){var a=getVideoObjLayer(r);t.updateCurrentLayer(a),updateHtmlLayersFromObject(t.selectedLayerSerial),t.updateLayerFormFields(t.selectedLayerSerial),redrawLayerHtml(t.selectedLayerSerial),u.drawAudioMap(e)},r,"audio")}}jQuery("body").on("dblclick",".layer_selected",edit_content_current_layer),setTimeout(function(){jQuery("#form_slide_params").find("input, select").on("change",function(){t.set_save_needed(!0)})},500),jQuery("body").on("click",".button_change_image_source, .button_edit_layer, .button_change_video_settings, .button_reset_size, .button_edit_shape, .button_change_audio_settings, .button_change_svg_settings",function(){jQuery(":focus").blur();var e=jQuery(this),r=e.closest(".layer-toolbar-li").data("serial");t.setLayerSelected(r),e.hasClass("button_reset_size")?resetCurrentElementSize():edit_content_current_layer()}),jQuery("body").on("click",".layer-title-with-icon",function(){var e=jQuery(this).find("input").is(":focus");e||jQuery(":focus").blur();var r=jQuery(this).closest(".layer-toolbar-li").data("serial");t.setLayerSelected(r,!1,e)}),jQuery("#button_edit_layer, #button_change_video_settings,#button_change_image_source").click(edit_content_current_layer),jQuery("body").on("click",".button_delete_layer, #button_delete_layer",function(){jQuery(":focus").blur();var e=jQuery(this);if(e.hasClass("button-now-disabled"))return!1;if(e.hasClass("button_delete_layer")){var r=e.closest(".layer-toolbar-li").data("serial");t.setLayerSelected(r)}var a=t.getCurrentLayer();if("column"===a.type)return!1;if(("group"===a.type||"row"===a.type)&&t.getLayersInGroup(a.unique_id).layers.length>0)jQuery("#delete-full-group-dialog").dialog({width:600,open:function(){},buttons:[{text:"Remove All Layers",click:function(){var e=t.getLayersInGroup(a.unique_id);jQuery.each(e.layers,function(e,r){t.deleteLayer(r.serial)}),jQuery.each(e.columns,function(e,r){t.deleteLayer(r.serial)}),t.deleteLayer(a.serial),u.organiseGroupsAndLayer(!0),jQuery(this).dialog("close")}},{text:"Move Layers to Root",click:function(){var e=t.getLayersInGroup(a.unique_id);jQuery.each(e.layers,function(e,t){t.p_uid=-1}),jQuery.each(e.columns,function(e,r){t.deleteLayer(r.serial)}),t.deleteLayer(a.serial),u.organiseGroupsAndLayer(!0),jQuery(this).dialog("close")}},{text:"Cancel Action",click:function(){jQuery(this).dialog("close")}}]});else if(confirm(rev_lang.delete_layer)){if(e.hasClass("button_delete_layer")){r=e.closest(".layer-toolbar-li").data("serial");t.setLayerSelected(r)}deleteCurrentLayer(),unselectLayers()}}),t.makeRowSortableDroppable(),jQuery("body").on("click",".button_duplicate_layer, #button_duplicate_layer",function(){jQuery(":focus").blur();var e=jQuery(this);if(e.hasClass("button-now-disabled"))return!1;if(e.hasClass("button_duplicate_layer")){var r=e.closest(".layer-toolbar-li").data("serial");t.setLayerSelected(r)}return duplicateCurrentLayer(),!1}),t.showHideContentEditor(!1),t.changeSlotBGs(),jQuery("#hide_layer_content_editor").click(function(){t.showHideContentEditor(!1)}),jQuery("#layer_animation, #layer_endanimation").change(function(){var e="layer_animation"==jQuery(this).attr("id")?"in":"out",r=jQuery(this).val(),a=!1;for(var i in initLayerAnims)if(initLayerAnims.hasOwnProperty(i)&&"custom"+e+"-"+initLayerAnims[i].id==r){switch(e){case"in":setNewAnimFromObj("start",initLayerAnims[i].params);break;case"out":setNewAnimFromObj("end",initLayerAnims[i].params)}a=!0;break}if(0==a)for(var i in initLayerAnimsDefault)if(initLayerAnimsDefault.hasOwnProperty(i)&&i==r){switch(e){case"in":setNewAnimFromObj("start",initLayerAnimsDefault[i].params);break;case"out":setNewAnimFromObj("end",initLayerAnimsDefault[i].params)}break}"out"==e&&"auto"==r&&jQuery("#masking-start")[0].checked&&1==jQuery("#masking-start")[0].checked&&(jQuery("#masking-end").attr("checked",!0),RevSliderSettings.onoffStatus(jQuery("#masking-end")),jQuery(".mask-end-settings").show()),t.checkAvailableAnimationFields(),t.updateLayerFromFields()}),jQuery("#layer_caption").change(function(){UniteCssEditorRev.checkIfHandleExists(jQuery(this).val())?jQuery("#extra_style_settings").removeClass("normal_rename normal_save save_rename save_save").addClass("normal_rename"):jQuery("#extra_style_settings").removeClass("normal_rename normal_save save_rename save_save").addClass("normal_save")}),jQuery(".save-current-animin, .save-current-animout").click(function(){var e=jQuery(this).hasClass("save-current-animin")?"start":"end",r="start"==e?jQuery('select[name="layer_animation"] option:selected').val():jQuery('select[name="layer_endanimation"] option:selected').val();if("custom"!==r)for(var a in initLayerAnimsDefault)if(initLayerAnimsDefault.hasOwnProperty(a)&&a===r)return alert(rev_lang.cant_modify_default_anims),!1;var i={};if(i.params=createNewAnimObj(e),"custom"==r){jQuery("#rs-save-under-animation").val("");var o=t.getCurrentLayer();"start"==e&&void 0!==o["orig-anim"]&&jQuery("#rs-save-under-animation").val(o["orig-anim"]),"end"==e&&void 0!==o["orig-endanim"]&&jQuery("#rs-save-under-animation").val(o["orig-endanim"]),jQuery("#dialog_save_animation").dialog({modal:!0,width:300,height:200,resizable:!1,create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")},buttons:{Save:function(){var t=jQuery("#rs-save-under-animation").val(),r=UniteAdminRev.sanitize_input(t),a=!0;for(var o in initLayerAnimsDefault)if(initLayerAnimsDefault.hasOwnProperty(o)&&initLayerAnimsDefault[o].handle==r)return alert(rev_lang.name_is_default_animations_cant_be_changed),!1;for(var o in initLayerAnims)if(initLayerAnims.hasOwnProperty(o)&&initLayerAnims[o].handle==r){a="start"==e?"customin":"customout",a+="-"+initLayerAnims[o].id;break}i.handle=r,i.params.type="start"==e?"customin":"customout",!0===a?(updateAnimInDb(r,i,!1),jQuery(this).dialog("close")):confirm(rev_lang.override_animation)&&(updateAnimInDb(a,i,!0),jQuery(this).dialog("close"))},Cancel:function(){jQuery(this).dialog("close")}}})}else i.params.type="start"==e?"customin":"customout",confirm(rev_lang.overwrite_animation)&&updateAnimInDb(r,i,!0)}),jQuery(".save-as-current-animin, .save-as-current-animout").click(function(){var e=jQuery(this).hasClass("save-as-current-animin")?"start":"end";jQuery(this).hasClass("save-as-current-animin")?jQuery("#layer_animation option:selected").val():jQuery("#layer_endanimation option:selected").val();currentAnimationType=jQuery(this).hasClass("save-as-current-animin")?"customin":"customout",jQuery("#dialog_save_as_animation").dialog({modal:!0,width:300,height:200,resizable:!1,create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")},buttons:{Save:function(){var t=jQuery("#rs-save-as-animation").val(),r=UniteAdminRev.sanitize_input(t),a=!1,i={};for(var o in initLayerAnimsDefault)if(initLayerAnimsDefault.hasOwnProperty(o)&&o==r){a=!0;break}for(var o in initLayerAnims)if(initLayerAnims.hasOwnProperty(o)&&initLayerAnims[o].handle==r){a=!0,r="start"==e?"customin":"customout",r+="-"+initLayerAnims[o].id;break}var s=!1;if(a){if(!confirm(rev_lang.override_animation))return!1;s=!0}i.params=createNewAnimObj(e),i.handle=r,i.params.type=currentAnimationType,updateAnimInDb(r,i,s),jQuery(this).dialog("close")},Cancel:function(){jQuery(this).dialog("close")}}})}),jQuery(".rename-current-animin, .rename-current-animout").click(function(){var e=jQuery(this).hasClass("rename-current-animin")?jQuery("#layer_animation option:selected").val():jQuery("#layer_endanimation option:selected").val(),t=jQuery(this).hasClass("rename-current-animin")?jQuery("#layer_animation option:selected").text():jQuery("#layer_endanimation option:selected").text();if("custom"!==e)for(var r in initLayerAnimsDefault)if(initLayerAnimsDefault.hasOwnProperty(r)&&r===e)return alert(rev_lang.cant_modify_default_anims),!1;if("custom"==e)return!1;var a=e.replace("customin-","").replace("customout-","");jQuery("#rs-rename-animation").val(t),jQuery("#dialog_rename_animation").dialog({modal:!0,width:300,height:200,resizable:!1,create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")},buttons:{Rename:function(){var e=jQuery("#rs-rename-animation").val(),t=UniteAdminRev.sanitize_input(e),r=!1,i=!1;for(var o in initLayerAnimsDefault)if(initLayerAnimsDefault.hasOwnProperty(o)&&o==t){i=!0;break}var s="";for(var o in initLayerAnims)if(initLayerAnims.hasOwnProperty(o)&&(o==t&&(i=!0),initLayerAnims[o].id==a)){r=!0,s=initLayerAnims[o].id;break}return i?(alert(rev_lang.anim_with_handle_exists),!1):r?(renameAnimInDb(s,t),jQuery(this).dialog("close"),!1):void 0},Cancel:function(){jQuery(this).dialog("close")}}})}),jQuery("#delete-current-animin, #delete-current-animout").click(function(){var e="delete-current-animin"==jQuery(this).attr("id")?jQuery("#layer_animation").val():jQuery("#layer_endanimation").val(),t="delete-current-animin"==jQuery(this).attr("id")?jQuery("#layer_animation option:selected").text():jQuery("#layer_endanimation option:selected").text();!(e.indexOf("custom")>-1)||"custom"==e?alert(rev_lang.default_animations_cant_delete):confirm(rev_lang.really_delete_anim+' "'+t+'"?')&&deleteAnimInDb(e)}),jQuery("#add_customanimation_in").click(function(){currentAnimationType="customin";var e=jQuery(this),t=jQuery("#extra_start_animation_settings"),r=jQuery("#extra_end_animation_settings");"block"==t.css("display")?(t.hide(200),e.removeClass("selected")):(t.show(200),e.addClass("selected")),r.hide(200)}),jQuery("#add_customanimation_out").click(function(){currentAnimationType="customout";var e=jQuery(this),t=jQuery("#extra_start_animation_settings"),r=jQuery("#extra_end_animation_settings");"block"==r.css("display")?(r.hide(200),e.removeClass("selected")):(r.show(200),e.addClass("selected")),t.hide(200)}),jQuery("#reset-current-animin, #reset-current-animout").click(function(){var e=jQuery(this).hasClass("reset-current-animin")?"start":"end",r=t.getCurrentLayer();"start"==e&&void 0!==r["orig-anim-handle"]&&jQuery('#layer_animation option[value="'+r["orig-anim-handle"]+'"]').attr("selected",!0).change(),"end"==e&&void 0!==r["orig-endanim-handle"]&&jQuery('#layer_endanimation option[value="'+r["orig-endanim-handle"]+'"]').attr("selected",!0).change()}),jQuery("#button_edit_css, #style-morestyle, .close_extra_settings").click(function(){var e=jQuery("#extra_style_settings"),t=jQuery("#button_edit_css"),r=jQuery("#style-morestyle");"block"==e.css("display")?(e.hide(200),t.removeClass("selected"),r.removeClass("showmore")):(e.show(200),t.addClass("selected"),r.addClass("showmore"))}),jQuery(".rename-current-css").click(function(){jQuery("#rs-rename-css").val(jQuery("#layer_caption").val()),jQuery("#dialog_rename_css").dialog({modal:!0,resizable:!1,width:400,closeOnEscape:!0,create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")},buttons:{Rename:function(){jQuery("#rs-rename-css").val(UniteAdminRev.sanitize_input(jQuery("#rs-rename-css").val()));var e=jQuery("#rs-rename-css").val();if(""!=e){var t=jQuery("#layer_caption").val(),r=UniteCssEditorRev.checkIfHandleExists(e);if(!1===UniteCssEditorRev.checkIfHandleExists(t))return alert(rev_lang.css_orig_name_does_not_exists),!1;if(!1!==r||t==e)return alert(rev_lang.css_name_already_exists),!1;UniteCssEditorRev.renameStylesInDb(t,e)}}}})}),jQuery(".save-as-current-css").click(function(){jQuery("#rs-save-as-css").val(jQuery("#layer_caption").val()),jQuery("#dialog_save_as_css").dialog({modal:!0,resizable:!1,width:400,closeOnEscape:!0,create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")},buttons:{Save:function(){jQuery("#rs-save-as-css").val(UniteAdminRev.sanitize_input(jQuery("#rs-save-as-css").val()));var e=jQuery("#rs-save-as-css").val();if(""!=e){if(!1!==UniteCssEditorRev.checkIfHandleExists(e))return alert(rev_lang.css_name_already_exists),!1;UniteCssEditorRev.saveStylesInDb(e,!0,jQuery("#dialog_save_as_css"))}}}})}),jQuery(".delete-current-css").click(function(){if(!1===confirm(rev_lang.delete_this_caption))return!1;var e=jQuery("#layer_caption").val(),t=UniteCssEditorRev.checkIfHandleExists(e);if(!1===t)return alert(rev_lang.css_name_does_not_exists),!1;UniteCssEditorRev.deleteStylesInDb(e,t)}),jQuery(".save-current-css").click(function(){if(confirm(rev_lang.this_will_change_the_class)){var e=jQuery("#layer_caption").val();!1!==UniteCssEditorRev.checkIfHandleExists(e)?UniteCssEditorRev.saveStylesInDb(e,!1):UniteCssEditorRev.saveStylesInDb(e,!0)}}),jQuery(".reset-current-css").click(function(){jQuery('input[name="rs-css-set-on[]"]').each(function(){jQuery(this).attr("checked",!0)}),jQuery('input[name="rs-css-include[]"]').each(function(){jQuery(this).attr("checked",!0)});var e=t.getCurrentLayer();void 0!==e.style&&jQuery("#dialog-change-style-from-css").dialog({buttons:{OK:function(){var r=!1,a={device:[],include:[]};if(jQuery('input[name="rs-css-set-on[]"]').each(function(){jQuery(this)[0].checked&&(r=!0,a.device.push(jQuery(this).val()))}),jQuery('input[name="rs-css-include[]"]').each(function(){jQuery(this)[0].checked&&a.include.push(jQuery(this).val())}),!r)return alert(rev_lang.select_at_least_one_device_type),!0;e.style=jQuery("#layer_caption").val(),t.reset_to_default_static_styles(e,a.include,a.device),updateSubStyleParameters(e,!0),jQuery("#layer_caption").change(),t.updateLayerFromFields(),jQuery("#dialog-change-style-from-css").dialog("close")},Close:function(){jQuery("#dialog-change-style-from-css").dialog("close")}},minWidth:275,minHeight:365,modal:!0,dialogClass:"tpdialogs",create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")},close:function(e,t){},open:function(){jQuery(".rs-style-device_input").each(function(){var e=jQuery(this);"checked"===e.attr("checked")?e.siblings(".rs-style-device_selector_prev").addClass("selected"):e.siblings(".rs-style-device_selector_prev").removeClass("selected")})}})}),jQuery("body").on("click change",".rs-style-device_input",function(){var e=jQuery(this);"checked"===e.attr("checked")?e.siblings(".rs-style-device_selector_prev").addClass("selected"):e.siblings(".rs-style-device_selector_prev").removeClass("selected")}),jQuery(".rs-layer-animation-settings-tabs li, .rs-layer-settings-tabs li").click(function(){var e=jQuery(this),t=e.closest("ul").find(".selected");jQuery(t.data("content")).hide(0),t.removeClass("selected"),e.addClass("selected"),jQuery(e.data("content")).show(0),e.data("content")}),jQuery("body").on("mousemove","#thelayer-editor-wrapper",function(e){var t=e.pageX-jQuery(this).offset().left,r=e.pageY-jQuery(this).offset().top,a=jQuery("#divLayers"),i=parseInt(a.offset().left,0)-parseInt(jQuery("#thelayer-editor-wrapper").offset().left,0);jQuery("#verlinie").css({left:t+"px"}),jQuery("#horlinie").css({top:r+"px"}),jQuery("#verlinetext").html(Math.round(t-i)),jQuery("#horlinetext").html(Math.round(r-40)),jQuery("#hor-css-linear .helplines-offsetcontainer").data("x",e.pageX-jQuery("#hor-css-linear .helplines-offsetcontainer").offset().left),jQuery("#hor-css-linear .helplines-offsetcontainer").data("y",r)}),jQuery("#hor-css-linear, #ver-css-linear, #verlinie, #horlinie").click(function(){var e=jQuery("#hor-css-linear .helplines-offsetcontainer"),t=jQuery("#ver-css-linear .helplines-offsetcontainer"),r=e.data("x"),a=e.data("y")+10,i=jQuery("#thelayer-editor-wrapper").outerHeight(!0),o=jQuery("#thelayer-editor-wrapper").outerWidth(!0);jQuery("#helpergrid").remove(),jQuery("#rs-grid-sizes").val("custom"),punchgs.TweenLite.to(e,.3,{autoAlpha:1}),punchgs.TweenLite.to(t,.3,{autoAlpha:1}),a<40&&r>0&&e.append('<div class="helplines" data-left="'+r+'" data-top="'+a+'" style="position:absolute;width:1px;height:'+(i-41)+"px;background:#4AFFFF;left:"+r+'px;top:-15px"><i class="helpline-drag eg-icon-move"></i><i class="helpline-remove eg-icon-cancel"></i></div>'),r<40&&a>0&&t.append('<div class="helplines" data-left="'+r+'" data-top="'+a+'"  style="position:absolute;width:'+(o-35)+"px;height:1px;background:#4AFFFF;top:"+a+'px;left:-15px"><i class="helpline-drag eg-icon-move"></i><i class="helpline-remove eg-icon-cancel"></i></div>');try{e.find(".helplines").draggable("destroy")}catch(e){}try{t.find(".helplines").draggable("destroy")}catch(e){}e.find(".helplines").draggable({handle:".helpline-drag",axis:"x"}),t.find(".helplines").draggable({handle:".helpline-drag",axis:"y"})}),jQuery("body").on("click",".helpline-remove",function(){jQuery(this).parent().remove()}),jQuery(".extra_sub_settings_wrapper").addClass("normal_rename"),jQuery("#extra_start_animation_settings input, #extra_end_animation_settings input").change(function(){if("new_start_animation_name"===jQuery(this).attr("id")||"new_end_animation_name"===jQuery(this).attr("id"))return!1;var e=jQuery("customin"==currentAnimationType?"#layer_animation":"#layer_endanimation");"customin"==currentAnimationType?jQuery(this).closest(".extra_start_animation_settings").removeClass("normal_rename normal_save save_rename save_save").addClass("normal_save"):jQuery(this).closest(".extra_end_animation_settings").removeClass("normal_rename normal_save save_rename save_save").addClass("normal_save");var r=e.find("option:selected").text(),a=e.find("option:selected").val();"custom"!==e.find("option:selected").val()&&(e.find('option[value="custom"]').attr("selected",!0).change(),"customin"==currentAnimationType?t.updateCurrentLayer({animation:"custom","orig-anim":r,"orig-anim-handle":a}):t.updateCurrentLayer({endanimation:"custom","orig-endanim":r,"orig-endanim-handle":a}))}),jQuery("#extra_style_settings input, #extra_style_settings select").change(function(){if("overwrite_style_name"===jQuery(this).attr("id")||"new_style_name"===jQuery(this).attr("id"))return!1;jQuery("#extra_style_settings").removeClass("normal_rename normal_save save_rename save_save").addClass("normal_save")}),jQuery('input[name="background_type"], #slide_bg_fit, input[name="bg_fit_x"], input[name="bg_fit_y"], #slide_bg_position,input[name="bg_position_x"],input[name="bg_position_y"],#slide_bg_repeat ').change(function(){t.changeSlotBGs()}),jQuery("body").on("blur",".timer-layer-text",function(){t.updateLayerFromFields()}),jQuery("#the_current-editing-layer-title").on("blur",function(){jQuery("#layer_sort_"+t.selectedLayerSerial+">.layer_sort_inner_wrapper .timer-layer-text").val(jQuery(this).val()),t.updateLayerFromFields()}),jQuery("body").on("blur",".layer-title-in-list",function(){var e=jQuery(this).closest("li").data("serial");if(void 0===e||""===e)return!1;jQuery("#layer_sort_"+e+" >.layer_sort_inner_wrapper .timer-layer-text").val(jQuery(this).val());jQuery(this).val(),t.updateLayerFromFields()}),jQuery("body").on("click",".quick-layer-lock",function(){var e=jQuery(this),r=(e.find("i"),e.closest(".layer-toolbar-li").data("serial")),a=t.getHtmlLayerFromSerial(r);if(void 0===r||""===r)return!1;u.isLayerLocked(a)?(u.unlockLayer(r),jQuery(".layer-toolbar-li").each(function(){var e=jQuery(this);if(e.data("serial")==r){var t=e.find(".quick-layer-lock"),a=t.find("i");t.addClass("in-on").removeClass("in-off"),a.removeClass("eg-icon-lock").addClass("eg-icon-lock-open")}})):(u.lockLayer(r),jQuery(".layer-toolbar-li").each(function(){var e=jQuery(this);if(e.data("serial")==r){var t=e.find(".quick-layer-lock"),a=t.find("i");t.addClass("in-off").removeClass("in-on"),a.removeClass("eg-icon-lock-open").addClass("eg-icon-lock")}}))}),jQuery("body").on("click",".quick-layer-view",function(){var e=jQuery(this).closest(".layer-toolbar-li").data("serial");if(void 0===e||""===e)return!1;var r=t.getLayer(e);u.isLayerVisible(r.references.htmlLayer)?(r.visible=!1,u.hideLayer(r),jQuery(".layer-toolbar-li").each(function(){var t=jQuery(this);if(t.data("serial")==e){var r=t.find(".quick-layer-view"),a=r.find("i");r.addClass("in-off").removeClass("in-on"),a.removeClass("eg-icon-eye").addClass("eg-icon-eye-off")}})):(r.visible=!0,u.showLayer(r),jQuery(".layer-toolbar-li").each(function(){var t=jQuery(this);if(t.data("serial")==e){var r=t.find(".quick-layer-view"),a=r.find("i");r.addClass("in-on").removeClass("in-off"),a.removeClass("eg-icon-eye-off").addClass("eg-icon-eye")}}))}),jQuery(".rs-row-break-selector").click(function(){var e=jQuery(this);jQuery(".rs-row-break-selector.selected").removeClass("selected"),e.addClass("selected"),jQuery("#column_break_at option:selected").prop("selected",!1),jQuery('#column_break_at option[value="'+e.data("val")+'"]').attr("selected","selected");var r={};r.column_break_at=e.data("val"),t.updateLayer(t.selectedLayerSerial,r,!1,!0)}),jQuery("#rs-check-row-layout").click(function(){var row_layer=t.getCurrentLayer();if("row"!==row_layer.type)return!1;var rl=jQuery('input[name="rs-row-layout"]').val(),rl_san=rl.replace(/[^\d\+\/ ]/gi,"");if(jQuery('input[name="rs-row-layout"]').val(rl_san),rl!==rl_san)return alert("Wrong format, please enter for example 1/2 + 1/4 + 1/4"),!1;var result=eval(rl_san);if(result=Math.ceil(1e14*result)/1e14,1!==result)return alert("The columns have to be added up to 1, so for example 1/2 + 1/8 is lower than 1"),!1;t.ignorAllUndoRedoLogs=!0;var columns_f=rl_san.split("+"),columns=new Array,undeleted_columns=new Array,deleted_columns=new Array;if(jQuery.each(t.arrLayers,function(e,t){"column"===t.type&&t.p_uid==row_layer.unique_id&&(columns.push(t),!0!==t.deleted&&undeleted_columns.push(t),!0===t.deleted&&deleted_columns.push(t))}),columns_f.length>undeleted_columns.length){for(var availablecolumns=0,i=columns.length;i<columns_f.length;i++){var c_objLayer={type:"column",text:initColumnName+(id_counter+1),p_uid:row_layer.unique_id,column_size:columns_f[i],createdOnInit:!1};addLayer(c_objLayer),availablecolumns++}for(var i=0;i<deleted_columns.length;i++){if(availablecolumns<columns_f.length-1){var objData={deleted:!1};t.updateLayer(deleted_columns[i].serial,objData,!1,!0),deleted_columns[i].deleted=!1,deleted_columns[i].references.htmlLayer.removeClass("layer-deleted"),deleted_columns[i].references.quicklayer.removeClass("layer-deleted"),deleted_columns[i].references.sorttable.layer.removeClass("layer-deleted"),deleted_columns[i].references.sorttable.timeline.removeClass("layer-deleted")}availablecolumns++}}else if(columns_f.length<undeleted_columns.length)for(var i=undeleted_columns.length;i>columns_f.length;i--){var highest=0,second=0;jQuery.each(undeleted_columns,function(e,t){null!=t&&t.unique_id>highest&&!1===t.deleted&&(second=highest,highest=t.unique_id)}),jQuery.each(t.arrLayers,function(e,r){r.p_uid===highest&&(r.p_uid=second),r.unique_id===highest&&t.deleteLayer(r.serial)})}var i=0;jQuery.each(t.arrLayers,function(e,t){t.p_uid==row_layer.unique_id&&!1===t.deleted&&i<columns_f.length&&(t.column_size=columns_f[i],i++)}),t.ignorAllUndoRedoLogs=!1,u.organiseGroupsAndLayer(u),u.allLayerToIdle(),t.makeRowSortableDroppable(),t.setLayerSelected(row_layer.serial),row_layer.references.htmlLayer.find(".row_editor").addClass("open_re"),jQuery("#rs-layout-row-composer").show(),u.timeLineTableDimensionUpdate(),t.hideRowLayoutComposer()}),jQuery("#button_change_background_image, #button_change_background_image_objlib").click(function(){var e=t.getCurrentLayer();null!==e&&("button_change_background_image_objlib"==jQuery(this).attr("id")?(e.image_library="objectlibrary",jQuery("#dialog_addobj").data("changeimg",!0),jQuery("#dialog_addobj").data("changeimgserial",e.serial),t.callObjectLibraryDialog("layer")):(delete e.image_library,UniteAdminRev.openAddImageDialog(rev_lang.select_layer_image,function(e){switchLayerBackground({bgimage_url:e})})))}),jQuery("#button_clear_background_image").click(function(){switchLayerBackground({bgimage_url:""})}),t.sortFontTypesByUsage(),t.cloneArrLayers(),cm.init()},t.extendSlideHeightBasedOnRows=function(){var e=0;"undefined"!=typeof rev_sizes&&void 0!==rev_sizes&&(jQuery(".row-zone-container").each(function(){var t=jQuery(this);t.hasClass("emptyzone")||(e+=t.height())}),e=e<rev_sizes[layout][1]?rev_sizes[layout][1]:e,jQuery(".tp-bgimg.defaultimg, #divLayers").each(function(){var t=jQuery(this);jQuery(this).hasClass("slide-transition-example")||t.css({height:e})}),__slidertype,jQuery("#divbgholder").css({minHeight:e,height:e}),jQuery("#divLayers-wrapper").css("height",e+1))},t.getLayersInGroup=function(e){var r={layers:[],columns:[]};return jQuery.each(t.arrLayers,function(t,a){a.p_uid===e&&"column"===a.type&&r.columns.push(a)}),jQuery.each(t.arrLayers,function(t,a){a.p_uid===e&&"column"!==a.type&&r.layers.push(a);for(var i=0;i<r.columns.length;i++)r.columns[i].unique_id===a.p_uid&&r.layers.push(a)}),r},t.getHighestGroupOrder=function(e){var r=0;for(key in t.arrLayers)if(t.arrLayers.hasOwnProperty(key)){var a=t.arrLayers[key];"row"===a.type&&t.getVal(a,"align_vert")===e&&r<=a.groupOrder&&(r=a.groupOrder+1)}return r},t.makeRowSortableDroppable=function(){jQuery("#divLayers, .tp_layer_group_inner_wrapper .slide_layer_type_column, .slide_layer_type_group .tp_layer_group_inner_wrapper").droppable({refreshPositions:!0,tolerance:"pointer",accept:".slide_layer_type_text, .slide_layer_type_image, .slide_layer_type_video, .slide_layer_type_shape, .slide_layer_type_audio, .slide_layer_type_svg, .slide_layer_type_button",over:function(e,t){var r=jQuery(e.target);jQuery(".slide_layer_type_column, .slide_layer_type_row, .slide_layer_type_group").removeClass("layer_selected").removeClass("layerchild_selected"),"divLayers"!==r[0].id&&(punchgs.TweenLite.set(r,{zIndex:0}),r.addClass("layer_selected").addClass("layerchild_selected")),e.target.id,r.closest(".slide_layer_type_row, .slide_layer_type_group").addClass("layer_selected"),r.closest(".row-zone-container").addClass("layerrow_selected")},out:function(e,t){var r=jQuery(e.target);punchgs.TweenLite.set(r,{zIndex:1}),"divLayers"!==r[0].id&&r.removeClass("layer_selected").removeClass("layerchild_selected"),r.closest(".layer_selected").removeClass("layer_selected").removeClass("layerchild_selected"),r.find(".layer_selected").removeClass("layer_selected").removeClass("layerchild_selected"),r.closest(".row-zone-container").removeClass("layerrow_selected"),jQuery(t.draggable).addClass("layer_selected")},drop:function(e,r){jQuery(r.draggable).data("dropin",jQuery(e.target)),t.setMiddleRowZone(100),t.checkRowZoneContents(),jQuery(".row-zone-container").removeClass("nowyouseeme"),jQuery(".row-zone-container").removeClass("layerrow_selected")}}),jQuery("#row-zone-top, #row-zone-middle, #row-zone-bottom").sortable({refreshPositions:!0,handle:".row_moveme",start:function(e,t){t.placeholder.html(t.item.html()),jQuery(".row-zone-container").addClass("nowyouseeme"),jQuery(".row-zone-container").addClass("layerrow_selected")},stop:function(e,r){jQuery(".row-zone-container").removeClass("nowyouseeme"),jQuery(".row-zone-container").removeClass("layerrow_selected"),t.setRowZoneOrders(),u.organiseGroupsAndLayer()},update:function(e,r){var a=t.getLayer(t.selectedLayerSerial);switch(r.item[0].parentNode.id){case"row-zone-top":a=t.setVal(a,"align_vert","top",!0);break;case"row-zone-middle":a=t.setVal(a,"align_vert","middle",!0);break;case"row-zone-bottom":a=t.setVal(a,"align_vert","bottom",!0)}t.setRowZoneOrders(),u.organiseGroupsAndLayer(),t.updateLayerFormFields(a.serial),t.setMiddleRowZone(100),t.checkRowZoneContents()},sort:function(e,t){}}),jQuery(".slide_layer_type_column .tp_layer_group_inner_wrapper").sortable({refreshPositions:!0,placeholder:"layer_placeholder_in_column",start:function(e,t){jQuery(".slide_layer_type_column, .slide_layer_type_row").removeClass("layer_selected").removeClass("layerchild_selected"),jQuery(".row-zone-container").removeClass("layerrow_selected"),t.placeholder.html(t.item.html());var r=t.placeholder.find(".innerslide_layer>img");r.length>0&&r.css({width:t.item.width(),height:t.item.height()})},items:"> .slide_layer_type_text, > .slide_layer_type_image, > .slide_layer_type_video, > .slide_layer_type_shape, > .slide_layer_type_audio, > .slide_layer_type_svg, > .slide_layer_type_button",cancel:".slide_layer_type_row, .slide_layer_type_column, .slide_layer_type_group",update:function(e,r){t.setInnerGroupOrders(jQuery(r.item[0].parentNode)),t.setMiddleRowZone(100)},sort:function(e,t){var r=jQuery(e.target);r.closest(".slide_layer_type_column").addClass("layer_selected").addClass("layerchild_selected"),r.closest(".slide_layer_type_row").addClass("layer_selected")}})},t.setRowZoneOrders=function(){t.setInnerGroupOrders(jQuery("#row-zone-top")),t.setInnerGroupOrders(jQuery("#row-zone-middle")),t.setInnerGroupOrders(jQuery("#row-zone-bottom"))},t.setInnerGroupOrders=function(e){for(var r=e.sortable("toArray",{attribute:"data-uniqueid"}),a=0;a<r.length;a++){var i={groupOrder:a};t.updateLayer(t.getLayerByUniqueId(r[a]).serial,i,!1,!0)}},t.droppedContainerCheck=function(e){var r,a=e.references.htmlLayer,i=a.data("dropin");if("row"===e.type||"column"===e.type||"group"===e.type);else if(null!=i&&i[0].classList.contains("tp_layer_group_inner_wrapper"))if(i[0].contains(a[0]))r=e.p_uid;else{e=t.setVal(e,"align_hor","left"),e=t.setVal(e,"align_vert","top");var o=a.position(),s={left:0,top:0},n={left:0,top:0};a.data("originalPosition");if(-1!=e.p_uid)if("column"===(c=t.getLayerByUniqueId(e.p_uid)).type||"group"===c.type){s=c.references.htmlLayer.position();var l=c.references.htmlLayer.closest(".row-zone-container");(n=void 0===l||0==l.length?{left:0,top:0}:l.position()).top+=void 0===l||0==l.length?0:parseInt(l.css("marginTop"),0)}i[0].appendChild(a[0]),r=a.closest(".slide_layer_type_group").data("uniqueid");var d=t.getLayerByUniqueId(r);a.width(),a.height();e.p_uid=r,jQuery.each(d.left,function(t,r){void 0!==e.top&&void 0!==d.top&&void 0!==d.top[t]&&("right"===d.align_hor[t]?void 0!==rev_sizes?newl=Math.round(o.left+s.left-(rev_sizes[t][0]-(parseInt(d.left[t],0)+parseInt(d.max_width[t],0)))):newl=Math.round(o.left+s.left-(jQuery("#divLayers").width()-parseInt(d.left[t],0))):newl=Math.round(o.left+s.left-parseInt(d.left[t],0)),"bottom"===d.align_vert[t]?void 0!==rev_sizes?newt=Math.round(o.top+s.top+n.top-(rev_sizes[t][1]-(parseInt(d.top[t],0)+parseInt(d.max_height[t],0)))):newt=Math.round(o.top+s.top+n.top-(jQuery("#divLayers").height()-parseInt(d.top[t],0))):newt=Math.round(o.top+s.top+n.top-parseInt(d.top[t],0)),e.left[t]=c?newl:newl-1,e.top[t]=c?newt:newt-1)}),t.updateHtmlLayerPosition(!1,e,t.getVal(e,"top"),t.getVal(e,"left"),t.getVal(e,"align_hor"),t.getVal(e,"align_vert")),u.organiseGroupsAndLayer(!1),d.references.htmlLayer.addClass("inwork"),jQuery("#focusongroup").addClass("inwork")}else if(null!=i&&i[0].classList.contains("slide_layer_type_column")){i[0].getElementsByClassName("tp_layer_group_inner_wrapper")[0].contains(a[0])||i[0].getElementsByClassName("tp_layer_group_inner_wrapper")[0].appendChild(a[0]),r=a.closest(".slide_layer_type_column").data("uniqueid"),"column"!==(c=t.getLayerByUniqueId(e.p_uid)).type&&"group"!==c.type&&(e=t.setVal(e,"whitespace","normal")),e.p_uid=r,u.organiseGroupsAndLayer(!1);t.getVal(e,"align_hor"),t.getVal(e,"align_vert");if("left","top",e=t.setVal(e,"align_hor","left"),"text"===(e=t.setVal(e,"align_vert","top")).type&&(e=t.setVal(e,"max_height","auto",!0)),"shape"===e.type){var y=jQuery("#layer_cover_mode option:selected").val();if("fullheight"===y||"cover"===y||"cover-proportional"===y){jQuery("#layer_cover_mode option:selected").prop("selected",!1);var _={};"fullheight"===y?(jQuery('#layer_cover_mode option[value="custom"]').attr("selected","selected"),_=t.setVal(_,"cover_mode","custom",!0)):(jQuery('#layer_cover_mode option[value="fullwidth"]').attr("selected","selected"),_=t.setVal(_,"cover_mode","fullwidth",!0)),t.updateLayer(e.serial,_),t.set_cover_mode()}}setTimeout(function(){t.extendSlideHeightBasedOnRows()},100),unselectAllFocusedGroup()}else if(null!=i&&"divLayers"===i[0].id&&"divLayers"!==a[0].parentNode.id){o=a.position();var c,m=null!=(d=t.getLayerByUniqueId(e.p_uid))&&null!=d.references&&null!=d.references.htmlLayer?d.references.htmlLayer.position():{top:0,left:0},p=(n={left:0,top:0},{left:0,top:0});if(e=t.setVal(e,"align_hor","left"),-1!=(e=t.setVal(e,"align_vert","top")).p_uid)if("column"===(c=t.getLayerByUniqueId(e.p_uid)).type){s=c.references.htmlLayer.position();l=c.references.htmlLayer.closest(".row-zone-container");var v=c.references.htmlLayer.closest(".slide_layer_type_row");p=void 0===v||0==p.length?{left:0,top:0}:v.position(),(n=void 0===l||0==l.length?{left:0,top:0}:l.position()).top+=void 0===l||0==l.length?0:parseInt(l.css("marginTop"),0),p.top+=void 0===v||0==v.length?0:parseInt(v.css("marginTop"),0)}e.p_uid=-1,document.getElementById("divLayers").appendChild(a[0]),jQuery.each(e.left,function(t,r){e.left[t]=Math.round(o.left+1+m.left+n.left+p.left),e.top[t]=Math.round(o.top+1+m.top+n.top+p.top)}),t.updateHtmlLayerPosition(!1,e,t.getVal(e,"top"),t.getVal(e,"left"),"left","top"),u.organiseGroupsAndLayer(!1),unselectAllFocusedGroup()}t.setMiddleRowZone(),t.resetLayerSelected(e)},t.checkRowZoneContents=function(){jQuery(".row-zone-container").each(function(){0===this.children.length?jQuery(this).addClass("emptyzone"):jQuery(this).removeClass("emptyzone")})},t.setMiddleRowZone=function(e){e=void 0===e?0:e,setTimeout(function(){var e=document.getElementById("row-zone-middle");e.style.marginTop=0-jQuery(e).height()/2+"px"},e)},t.showHideToggleContent=function(e){!0===e.toggle?jQuery("#layer_text_wrapper").addClass("withtoggle"):jQuery("#layer_text_wrapper").removeClass("withtoggle")},t.showHideContentEditor=function(e){e?(jQuery("#button_edit_layer").hide(),jQuery("#button_delete_layer").hide(),jQuery("#button_duplicate_layer").hide(),jQuery("#tp-addiconbutton").show(),jQuery("#hide_layer_content_editor").show(),jQuery("#linkInsertTemplate").show(),jQuery("#layer_text_wrapper").show(),jQuery("#button_reset_size").hide(),jQuery("#button_change_video_settings").hide(),jQuery("#layer_text_wrapper").addClass("currently_editing_txt"),jQuery("#layer-short-tool-placeholder-a").hide(),jQuery("#layer-short-tool-placeholder-b").hide()):(jQuery("#layer-short-tool-placeholder-a").show(),jQuery("#layer-short-tool-placeholder-b").show(),jQuery("#button_edit_layer").hide(),jQuery("#button_change_image_source").hide(),jQuery("#button_delete_layer").show(),jQuery("#button_duplicate_layer").show(),jQuery("#tp-addiconbutton").hide(),jQuery("#hide_layer_content_editor").hide(),jQuery("#linkInsertTemplate").hide(),jQuery("#button_reset_size").hide(),jQuery("#button_change_video_settings").hide(),jQuery("#layer_text_wrapper").hide(),jQuery("#layer_text_wrapper").removeClass("currently_editing_txt"));var r=-1===t.selectedLayerSerial?"":t.getLayer(t.selectedLayerSerial);t.toolbarInPos(r),t.showHideToggleContent(r)},t.changeSlotBGs=function(){var e=jQuery("#divbgholder").find(".slotholder"),r=jQuery("#image_url").val(),a="percentage"==jQuery("#slide_bg_position").val()?jQuery("input[name='bg_position_x']").val()+"% "+jQuery("input[name='bg_position_y']").val()+"% ":jQuery("#slide_bg_position").val(),i="percentage"==jQuery("#slide_bg_fit").val()?jQuery("input[name='bg_fit_x']").val()+"% "+jQuery("input[name='bg_fit_y']").val()+"% ":jQuery("#slide_bg_fit").val(),o=window.RevColor.get(jQuery("#slide_bg_color").val());gallery_type=jQuery('input[name="rs-gallery-type"]').val(),jQuery("#the_image_source_url").html(r),jQuery("#video-settings").hide(),jQuery("#bg-setting-wrap").show(),jQuery("#vid-rev-youtube-options").hide(),jQuery("#vid-rev-vimeo-options").hide(),jQuery("#streamvideo_cover").hide(),jQuery("#streamvideo_cover_both").hide(),jQuery("#button_change_image").show(),jQuery("#button_change_image_objlib").show(),jQuery(".video_volume_wrapper").hide(),jQuery("#slide_selector .list_slide_links li.selected .slide-media-container ").removeClass("mini-transparent").css({backgroundSize:"cover"});var s=jQuery('input[name="background_type"]:checked').data("bgtype");switch(s){case"image":switch(jQuery("#button_change_image").hide(),jQuery("#button_change_image_objlib").hide(),gallery_type){case"gallery":jQuery("#button_change_image").show(),jQuery("#button_change_image_objlib").show();break;case"posts":r=rs_plugin_url+"public/assets/assets/sources/post.png";break;case"woocommerce":r=rs_plugin_url+"public/assets/assets/sources/wc.png";break;case"facebook":r=rs_plugin_url+"public/assets/assets/sources/fb.png";break;case"twitter":r=rs_plugin_url+"public/assets/assets/sources/tw.png";break;case"instagram":r=rs_plugin_url+"public/assets/assets/sources/ig.png";break;case"flickr":r=rs_plugin_url+"public/assets/assets/sources/fr.png";break;case"youtube":r=rs_plugin_url+"public/assets/assets/sources/yt.png";break;case"vimeo":r=rs_plugin_url+"public/assets/assets/sources/vm.png"}jQuery(".mainbg-sub-kenburns-selector").show(),jQuery(".mainbg-sub-parallax-selector").show(),jQuery(".mainbg-sub-settings-selector").show(),jQuery("#button_change_image").appendTo(jQuery("#tp-bgimagewpsrc")),jQuery("#button_change_image_objlib").appendTo(jQuery("#tp-bgimagewpsrc")),e.find(".defaultimg, .slotslidebg").css({backgroundImage:"url("+r+")",backgroundPosition:a,backgroundSize:i,backgroundColor:"transparent"}),jQuery("#ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:a,backgroundSize:i,backgroundColor:"transparent"}).data("src",r),t.updateKenBurnExampleValues();break;case"trans":jQuery("#slide_selector .list_slide_links li.selected .slide-media-container ").css("background-image","").css("background-repeat","").addClass("mini-transparent").css({backgroundSize:"inherit"}),e.find(".defaultimg, .slotslidebg").css({backgroundImage:"none",backgroundPosition:a,backgroundSize:i,backgroundColor:"transparent"}),jQuery(".mainbg-sub-kenburns-selector").hide(),jQuery(".mainbg-sub-parallax-selector").hide(),jQuery(".mainbg-sub-settings-selector").hide();break;case"solid":e.find(".defaultimg, .slotslidebg").css({backgroundImage:"none",backgroundPosition:a,backgroundSize:i,background:o}),jQuery(".mainbg-sub-kenburns-selector").hide(),jQuery(".mainbg-sub-parallax-selector").hide(),jQuery(".mainbg-sub-settings-selector").hide(),jQuery("#slide_selector .list_slide_links li.selected .slide-media-container ").css({backgroundImage:"none",backgroundPosition:a,backgroundSize:i,background:o});break;case"external":r=jQuery("#slide_bg_external").val(),jQuery("#the_image_source_url").html(r),e.find(".defaultimg, .slotslidebg").css({backgroundImage:"url("+r+")",backgroundPosition:a,backgroundSize:i,backgroundColor:"transparent"}),jQuery("#ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:a,backgroundSize:i,backgroundColor:"transparent"}).data("src",r),t.updateKenBurnExampleValues(),jQuery(".mainbg-sub-kenburns-selector").show(),jQuery(".mainbg-sub-parallax-selector").show(),jQuery(".mainbg-sub-settings-selector").show();break;case"streamtwitter":case"streamtwitterboth":jQuery("#streamvideo_cover").show(),jQuery("#streamvideo_cover_both").show(),r=rs_plugin_url+"public/assets/assets/sources/tw.png",e.find(".defaultimg, .slotslidebg, #ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:"center center",backgroundSize:"cover",backgroundColor:"transparent"}),jQuery("#ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:"center center",backgroundSize:"cover",backgroundColor:"transparent"}).data("src",r),t.updateKenBurnExampleValues(),jQuery(".mainbg-sub-kenburns-selector").hide(),jQuery(".mainbg-sub-parallax-selector").show(),jQuery(".mainbg-sub-settings-selector").show(),jQuery("#button_change_image").appendTo(jQuery("#vimeo-image-picker")),jQuery("#button_change_image_objlib").appendTo(jQuery("#vimeo-image-picker")),jQuery("#video-settings").show(),jQuery("#bg-setting-wrap").hide(),jQuery("#vid-rev-vimeo-options").show(),jQuery("#vid-rev-youtube-options").show(),jQuery(".video_volume_wrapper").show();break;case"streamyoutube":case"streamyoutubeboth":jQuery("#streamvideo_cover").show(),jQuery("#streamvideo_cover_both").show(),r=rs_plugin_url+"public/assets/assets/sources/yt.png";case"youtube":e.find(".defaultimg, .slotslidebg, #ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:"center center",backgroundSize:"cover",backgroundColor:"transparent"}),jQuery("#ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:"center center",backgroundSize:"cover",backgroundColor:"transparent"}).data("src",r),t.updateKenBurnExampleValues(),jQuery(".mainbg-sub-kenburns-selector").hide(),jQuery(".mainbg-sub-parallax-selector").show(),jQuery(".mainbg-sub-settings-selector").show(),jQuery("#button_change_image").appendTo(jQuery("#youtube-image-picker")),jQuery("#button_change_image_objlib").appendTo(jQuery("#youtube-image-picker")),jQuery("#video-settings").show(),jQuery("#bg-setting-wrap").hide(),jQuery("#vid-rev-youtube-options").show(),jQuery(".video_volume_wrapper").show();break;case"streamvimeo":case"streamvimeoboth":jQuery("#streamvideo_cover").show(),jQuery("#streamvideo_cover_both").show(),r=rs_plugin_url+"public/assets/assets/sources/vm.png";case"vimeo":e.find(".defaultimg, .slotslidebg, #ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:"center center",backgroundSize:"cover",backgroundColor:"transparent"}),jQuery("#ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:"center center",backgroundSize:"cover",backgroundColor:"transparent"}).data("src",r),t.updateKenBurnExampleValues(),jQuery(".mainbg-sub-kenburns-selector").hide(),jQuery(".mainbg-sub-parallax-selector").show(),jQuery(".mainbg-sub-settings-selector").show(),jQuery("#button_change_image").appendTo(jQuery("#vimeo-image-picker")),jQuery("#button_change_image_objlib").appendTo(jQuery("#vimeo-image-picker")),jQuery("#video-settings").show(),jQuery("#bg-setting-wrap").hide(),jQuery("#vid-rev-vimeo-options").show(),jQuery(".video_volume_wrapper").show();break;case"streaminstagram":case"streaminstagramboth":jQuery("#streamvideo_cover").show(),jQuery("#streamvideo_cover_both").show(),r=rs_plugin_url+"public/assets/assets/sources/ig.png";case"html5":e.find(".defaultimg, .slotslidebg").css({backgroundImage:"url("+r+")",backgroundPosition:"center center",backgroundSize:"cover",backgroundColor:"transparent"}),jQuery("#ken_burn_slot_example").css({backgroundImage:"url("+r+")",backgroundPosition:"center center",backgroundSize:"cover",backgroundColor:"transparent"}).data("src",r),t.updateKenBurnExampleValues(),jQuery(".mainbg-sub-kenburns-selector").hide(),jQuery(".mainbg-sub-parallax-selector").show(),jQuery(".mainbg-sub-settings-selector").show(),jQuery("#button_change_image").appendTo(jQuery("#html5video-image-picker")),jQuery("#button_change_image_objlib").appendTo(jQuery("#html5video-image-picker")),jQuery("#video-settings").show(),jQuery("#bg-setting-wrap").hide()}if("solid"!=s&&"trans"!=s&&jQuery("#slide_selector .list_slide_links li.selected .slide-media-container ").css("background-image","url("+r+")"),jQuery("#divbgholder").css({background:"none",backgroundImage:"none",backgroundColor:"transparent"}),"checked"==jQuery("#thumb_for_admin").attr("checked")){var n=jQuery("#slide_thumb_button_preview div").css("background-image");jQuery("#slide_selector .list_slide_links li.selected .slide-media-container").css({"background-image":n,backgroundSize:"cover",backgroundPosition:"center center"})}u.resetSlideAnimations(!1)};var initDisallowCaptionsOnClick=function(){jQuery(".slide_layer.tp-caption a").on("click",function(){return!1})};function initSliderMainOptions(e){var t=e;jQuery(".bgsrcchanger-div").each(function(){("tp-bgimagesettings"!=jQuery(this).attr("id")||"tp-bgimagesettings"==jQuery(this).attr("id")&&"on"!=t.data("imgsettings"))&&("tp-bgimagesettings"==jQuery(this).attr("id")?jQuery(this).slideUp(200):jQuery(this).css({display:"none"}))}),jQuery("#"+t.data("callid")).css({display:"inline-block"}),"on"==t.data("imgsettings")&&jQuery("#tp-bgimagesettings").slideDown(200),"image"==jQuery('input[name="background_type"]:checked').val()?(jQuery(".rs-img-source-size").show(),jQuery("#alt_option").show(),jQuery("#title_option").show(),"custom"==jQuery("#alt_option option:selected").val()?jQuery("#alt_attr").show():jQuery("#alt_attr").hide(),"custom"==jQuery("#title_option option:selected").val()?jQuery("#title_attr").show():jQuery("#title_attr").hide()):(jQuery("#alt_option").hide(),jQuery("#title_option").hide(),jQuery("#alt_attr").show(),jQuery("#title_attr").hide(),jQuery(".rs-img-source-size").hide()),"external"==jQuery('input[name="background_type"]:checked').val()?jQuery(".ext_setting").show():jQuery(".ext_setting").hide()}var initAlignTable=function(){jQuery(".rs-new-align-button").click(function(){var e=jQuery(this);if(jQuery(e).parent().hasClass("table_disabled"))return!1;var r=jQuery("#layer_left_text"),a=jQuery("#layer_top_text"),i=e.data("hor"),o=e.data("ver");if(void 0===o){switch(jQuery("#rs-align-wrapper").find(".selected").removeClass("selected"),i){case"left":r.html(r.data("textnormal")).css("width","auto"),jQuery("#layer_left").val("0");break;case"right":case"center":r.html(r.data("textoffset")).css("width","42px"),jQuery("#layer_left").val("0")}jQuery("#layer_align_hor").val(i)}else{switch(jQuery("#rs-align-wrapper-ver").find(".selected").removeClass("selected"),o){case"top":a.html(a.data("textnormal")).css("width","auto"),jQuery("#layer_top").val("0");break;case"bottom":case"middle":a.html(a.data("textoffset")).css("width","42px"),jQuery("#layer_top").val("0")}jQuery("#layer_align_vert").val(o)}var s=t.getLayer(t.selectedLayerSerial);if("row"===s.type){switch(o){case"top":document.getElementById("row-zone-top").appendChild(s.references.htmlLayer[0]);break;case"middle":document.getElementById("row-zone-middle").appendChild(s.references.htmlLayer[0]);break;case"bottom":document.getElementById("row-zone-bottom").appendChild(s.references.htmlLayer[0])}s=t.setVal(s,"align_vert",o,!0,null,!0),t.checkRowZoneContents()}e.addClass("selected"),t.updateLayerFromFields(),t.toolbarInPos(),t.setMiddleRowZone(100)})},initMainEvents=function(){container.click(function(e){layerresized?layerresized=!1:((e.target==this||jQuery(e.target).hasClass("slide_layers_border"))&&u.checkMultipleSelectedItems(!0),unselectLayers(),jQuery("#quick-layers-wrapper").slideUp(300),jQuery(".current-active-main-toolbar").removeClass("opened"),jQuery("#button_show_all_layer i").removeClass("eg-icon-up").addClass("eg-icon-menu"))})},showHideLinkActions=function(e,t){var r=e.closest("li"),a=e.val();switch(r.find(".action-link-wrapper").hide(),r.find(".action-jump-to-slide").hide(),r.find(".action-scrollofset").hide(),r.find(".action-speed-wrapper").hide(),r.find(".action-easing-wrapper").hide(),r.find(".action-target-layer").hide(),r.find(".action-callback").hide(),r.find(".action-toggle_layer").hide(),r.find(".action-toggleclass").hide(),r.find(".action-delay-wrapper").show(),a){case"none":r.find(".action-delay-wrapper").hide();break;case"link":r.find(".action-link-wrapper").show();break;case"jumpto":r.find(".action-jump-to-slide").show();break;case"scroll_under":r.find(".action-scrollofset").show(),r.find(".action-speed-wrapper").show(),r.find(".action-easing-wrapper").show();break;case"callback":r.find(".action-callback").show();break;case"start_in":case"start_out":case"start_video":case"stop_video":case"mute_video":case"unmute_video":case"toggle_video":case"toggle_mute_video":r.find(".action-target-layer").show();break;case"toggle_layer":r.find(".action-target-layer").show(),r.find(".action-toggle_layer").show();break;case"simulate_click":r.find(".action-target-layer").show();break;case"toggle_class":r.find(".action-target-layer").show(),r.find(".action-toggleclass").show()}switch(a){case"start_in":case"start_out":case"toggle_layer":r.find(".action-triggerstates").show();break;default:r.find(".action-triggerstates").hide()}switch(a){case"toggle_video":case"mute_video":case"unmute_video":case"toggle_global_mute_video":case"toggle_mute_video":case"start_video":case"stop_video":r.find(".action-target-layer").find('select[name="layer_target[]"] option').each(function(){"video"!==jQuery(this).data("mytype")&&"video-special"!==jQuery(this).data("mytype")&&"audio"!==jQuery(this).data("mytype")?"all"===jQuery(this).data("mytype")?jQuery(this).show():jQuery(this).hide():jQuery(this).show()});break;default:r.find(".action-target-layer").find('select[name="layer_target[]"] option').each(function(){"video-special"!==jQuery(this).data("mytype")?jQuery(this).show():"all"===jQuery(this).data("mytype")?jQuery(this).show():jQuery(this).hide()})}t&&u.updateAllLayerTimeline()},showHideToolTip=function(){var e=jQuery("#layer_tooltip_event").val(),t=jQuery(".tooltip-parrent-part"),r=jQuery(".tooltip-child-part");switch(e){case"none":t.hide(),r.hide();break;case"parrent":t.show(),r.hide();break;case"child":t.hide(),r.show()}},specOrVal=function(e,t,r,a){var i=parseFloat(e),o=void 0===a&&void 0!==i&&jQuery.isNumeric(i)&&!jQuery.isNumeric(e)?e.replace(i,""):void 0===a?void 0===r?"":r:a;return result=jQuery.inArray(e,t)>=0?e:void 0!==e&&jQuery.isNumeric(parseInt(e))&&0!==e.length?parseInt(e)+o:"",result};t.check_the_font_bold=function(e){var t=["100","200","300","400","500","600","700","800","900"];for(var r in initArrFontTypes)if(initArrFontTypes.hasOwnProperty(r)&&initArrFontTypes[r].label==e){void 0!==initArrFontTypes[r].variants&&(t=initArrFontTypes[r].variants);break}var a=jQuery('select[name="font_weight_static"]'),i=a.find("option:selected").val();for(var r in a.html(""),t)if(t.hasOwnProperty(r)){if(t[r].indexOf("italic")>-1)continue;a.append('<option value="'+t[r]+'">'+t[r]+"</option>")}a.find('option[value="'+i+'"]').attr("selected","selected")},t.do_google_font_loading=function(e){var r=[];for(var a in initArrFontTypes)if(initArrFontTypes.hasOwnProperty(a)&&initArrFontTypes[a].label==e){if("googlefont"==initArrFontTypes[a].type){var i="";for(var o in initArrFontTypes[a].variants)initArrFontTypes[a].variants.hasOwnProperty(o)&&(o>0&&(i+=","),i+=initArrFontTypes[a].variants[o]);if(void 0!==initArrFontTypes[a].subsets)for(var o in i+="&subset=",initArrFontTypes[a].subsets)initArrFontTypes[a].subsets.hasOwnProperty(o)&&(o>0&&(i+=","),i+=initArrFontTypes[a].subsets[o]);fontclass=e.replace(/\ /g,"-"),e=e.replace(/\ /g,"+"),0==jQuery(".rev-css-"+fontclass).length&&(r.push(e+":"+i),jQuery("body").append('<div style="display:none" class="rev-css-'+fontclass+'">RevSliderFont</div>'),-1==sgfamilies.indexOf(e)&&sgfamilies.push(e),null!==r&&r.length>0&&tpWebFont.load({google:{families:r},loading:function(){showWaitAMinute({fadeIn:300,text:e+" is Loading..."})},active:function(){setTimeout(function(){showWaitAMinute({fadeOut:300})},300),u.allLayerToIdle({type:"text"})},inactive:function(){setTimeout(function(){showWaitAMinute({fadeOut:300})},300),u.allLayerToIdle({type:"text"})}}))}break}t.sortFontTypesByUsage()};var initHtmlFields=function(){var e;jQuery("body").on("change",'select[name="layer_action[]"], select[name="no_layer_action[]"]',function(){showHideLinkActions(jQuery(this))}),jQuery("body").on("click","#kenburn-playpause-wrapper",function(){var e=jQuery("#kenburn-playpause-wrapper"),t=jQuery("#ken_burn_example").data("kbtl");e.hasClass("playing")?(e.html('<i class="eg-icon-play"></i><span>PLAY</span>'),e.removeClass("playing"),t.pause()):(e.html('<i class="eg-icon-pause"></i><span>PAUSE</span>'),e.addClass("playing"),t.play())}),jQuery("body").on("click","#kenburn-backtoidle",function(){jQuery("#ken_burn_example").data("kbtl").progress(0)}),jQuery(".kb_input_values").change(t.updateKenBurnExampleValues),jQuery("#layer_tooltip_event").change(showHideToolTip),jQuery("#layer_caption").catcomplete({source:initArrCaptionClasses,minLength:0,appendTo:"#tp-thelistofclasses",open:function(e,t){jQuery("#tp-thelistofclasses ul").height()>450&&jQuery("#tp-thelistofclasses ul").perfectScrollbar("destroy").perfectScrollbar({wheelPropagation:!1,suppressScrollX:!0})},close:function(e,r){var a=t.getCurrentLayer();if(!1===a||null==a)return!1;void 0!==a.style&&a.style!==jQuery("#layer_caption").val()&&(a.style=jQuery("#layer_caption").val(),t.reset_to_default_static_styles(a),updateSubStyleParameters(a,!0)),jQuery("#layer_caption").change(),jQuery("#css_font-family").change(),t.updateLayerFromFields()}}).data("customCatcomplete")._renderItem=function(e,t){var r=jQuery("<li></li>").data("item.autocomplete",t).append("<a>"+t.label+"</a>").appendTo(e);return r.attr("original-title",t.value),r},jQuery("#layer_captions_down").click(function(e){e.stopPropagation(),jQuery("#css_font-family").catcomplete("close"),jQuery("#css_editor_expert").hide(),jQuery("#css_editor_wrap").hide(),1==jQuery("#layer_caption").data("is_open")?jQuery("#layer_caption").catcomplete("close"):jQuery(this).hasClass("ui-state-active")&&(jQuery("#layer_caption").catcomplete("search","").data("customCatcomplete")._renderItem=function(e,t){var r=jQuery("<li></li>").data("item.autocomplete",t).append("<a>"+t.label+"</a>").appendTo(e);return r.attr("original-title",t.value),r})}),jQuery("#layer_caption").bind("catcompleteopen",function(){jQuery(this).data("is_open",!0),jQuery(".ui-autocomplete li").tipsy({delayIn:70,html:!0,gravity:"w",title:function(){return setTimeout(function(){jQuery(".tp-present-caption-small").parent().addClass("tp-present-wrapper-small"),jQuery(".tp-present-caption-small").parent().parent().addClass("tp-present-wrapper-parent-small")},10),'<div class="tp-present-caption-small"><div class="example-dark-blinker"></div><div class="tp-caption '+this.getAttribute("original-title")+'">example</div></div>'}})}),jQuery("#layer_caption").bind("catcompleteclose",function(){jQuery(this).data("is_open",!1)}),t.setLayersAutoComplete(),jQuery('input[name="adbutton-fontfamily"]').autocomplete({source:initArrFontTypes,minLength:0}),jQuery("#css_fonts_down").click(function(e){e.stopPropagation(),jQuery("#layer_caption").catcomplete("close"),1==jQuery("#css_font-family").data("is_open")?jQuery("#css_font-family").catcomplete("close"):jQuery(this).hasClass("ui-state-active")&&jQuery("#css_font-family").catcomplete("search","").data("ui-autocomplete")}),jQuery("body").click(function(){jQuery("#layer_caption").catcomplete("close"),jQuery("#css_font-family").catcomplete("close")}),jQuery("body").on("change",".form_layers select, #layer_proportional_scale, #layer_auto_line_break, #layer_displaymode",function(){t.updateLayerFromFields()}),jQuery("#layer_proportional_scale, #layer_auto_line_break, #layer_displaymode").change(function(){jQuery(this)[0].checked?jQuery(this).parent().removeClass("notselected"):jQuery(this).parent().addClass("notselected")}),jQuery("#layer_text").keyup(function(){clearTimeout(e);var r=jQuery(this).val();e=setTimeout(function(){updateLayerTextField("",jQuery(".sortlist li.ui-state-hover"),r),t.toolbarInPos(),t.updateLayerFromFields(),u.updateCurrentLayerTimeline()},150)}),jQuery("#layer_text_toggle").keyup(function(){clearTimeout(e);var r=jQuery(this).val();e=setTimeout(function(){updateLayerTextField("",jQuery(".sortlist li.ui-state-hover"),r),t.toolbarInPos(),t.updateLayerFromFields()},150)}),jQuery(".rev-visibility-on-sizes input").click(function(){t.updateLayerFromFields()}),jQuery("body").on("blur",".form_layers input, .form_layers textarea",function(){t.updateLayerFromFields()}),jQuery("body").on("change",".form_layers input, .form_layers textarea",function(){t.updateLayerFromFields()}),jQuery("body").on("keypress",".form_layers input, .form_layers textarea",function(e){13==e.keyCode&&t.updateLayerFromFields()}),jQuery("#delay").keypress(function(e){Number(jQuery("#delay").val())>0&&(g_slideTime=jQuery("#delay").val())}),jQuery("#delay").blur(function(){Number(jQuery("#delay").val())>0&&(g_slideTime=jQuery("#delay").val());var e=g_slideTime/10;jQuery("#mastertimer-maxtime").css({left:e+"px"}),jQuery("#mastertimer-maxcurtime").html(u.convToTime(e)),jQuery(".slide-idle-section").css({left:e}),jQuery(".mastertimer-slide .tl-fullanim").css({width:e+"px"}),u.mainMaxTimeLeft=e,u.masterTimerPositionChange(!0),u.compareLayerEndsVSSlideEnd()}),jQuery(".form_layers input").on("click",function(){jQuery(this).select()}),jQuery(".form_layers input").on("change blur focus",function(e){var t=jQuery(this),r=parseFloat(t.val()),a=parseFloat(t.data("min")),i=parseFloat(t.data("max")),o=t.val().slice(-1)||"",s=t.val().slice(-2)||"",n=t.data("suffix"),l=t.data("suffixalt");lastchangedinput=t.attr("id"),null!=n&&jQuery.isNumeric(r)&&r>-9999999&&r<9999999&&(null!=a&&r<a&&(r=a),null!=a&&r>i&&(r=i),isNaN(r)&&(r=0),r=Math.round(100*r)/100,null==l?t.val(r+n):o==l?t.val(r+o):s==l?t.val(r+s):t.val(r+n)),"focus"===e.type&&this.select()}),jQuery(".form_layers select").on("change blur focus",function(){lastchangedinput=jQuery(this).attr("id")}),jQuery("#layer_speed, #layer_endspeed, #layer_splitdelay, #layer_endsplitdelay, #layer_split, #layer_endsplit, #layer_split_direction, #layer_endsplit_direction").on("change blur",function(){t.updateLayerFromFields(),u.updateCurrentLayerTimeline()}),jQuery("body").on("focus","#layer_text, #layer_text_toggle",function(){jQuery("#layer_text").removeClass("lasteditedlayertext"),jQuery("#layer_text_toggle").removeClass("lasteditedlayertext"),jQuery(this).addClass("lasteditedlayertext")})};t.nextNewLayerToPosition=function(e){t.newlayercoord.x=e.x,t.newlayercoord.y=e.y},t.setLayersAutoComplete=function(){jQuery("#css_font-family").catcomplete({source:initArrFontTypes,appendTo:"#tp-thelistoffonts",open:function(e,t){jQuery("#tp-thelistoffonts ul").height()>450&&(jQuery("#tp-thelistoffonts ul").perfectScrollbar("destroy").perfectScrollbar({wheelPropagation:!1,suppressScrollX:!0}),jQuery("#tp-thelistoffonts ul").scrollTop(0))},minLength:0,close:t.updateLayerFromFields,select:function(e,r){t.check_the_font_bold(r.item.label),t.do_google_font_loading(r.item.label)}}).data("customCatcomplete")._renderItem=function(e,t){var r=jQuery("<li></li>").data("item.autocomplete",t).append("<a>"+t.label+"</a>");return t.top?(r.prependTo(e),r.insertAfter("#insert-google-font-after-me")):r.appendTo(e),"Dont Show Me"==t.label&&(r.css({display:"block",height:"0px",width:"0px",visibility:"hidden"}),r.attr("id","insert-google-font-after-me")),r.attr("original-title",t.value),r},jQuery("#css_font-family").change(function(){var e=jQuery(this).val();t.check_the_font_bold(e),t.do_google_font_loading(e)}),jQuery("#css_font-family").bind("catcompleteopen",function(){jQuery(this).data("is_open",!0)}),jQuery("#css_font-family").bind("catcompleteclose",function(){jQuery(this).data("is_open",!1)})};var switchLayerBackground=function(e){t.updateCurrentLayer(e),t.add_layer_change();var r=t.getCurrentLayer();u.rebuildLayerIdleProgress(r.references.htmlLayer)},initButtons=function(){function e(e){var r=jQuery('input[name="background_type"]:checked').val();jQuery.inArray(r,-1!=["trans","solid","external"])&&jQuery("#radio_back_image").attr("checked","checked").change().click(),jQuery("#divbgholder").css("background-image","url("+e+")"),jQuery("#slide_selector .list_slide_links li.selected .slide-media-container ").css("background-image","url("+e+")"),jQuery("#image_url").val(e),jQuery("#image_id").val(""),t.changeSlotBGs(),jQuery(".bgsrcchanger:checked").click(),jQuery('input[name="kenburn_effect"]').is(":checked")&&jQuery('input[name="kb_start_fit"]').change()}jQuery("#button_add_layer").click(function(){addLayerText(1==jQuery(this).data("isstatic")?"static":null)}),jQuery("#button_add_layer_image").click(function(){var e=1==jQuery(this).data("isstatic")?rev_lang.select_static_layer_image:rev_lang.select_layer_image,t=1==jQuery(this).data("isstatic")?"static":null;UniteAdminRev.openAddImageDialog(e,function(e,r,a,i){addLayerImage({imgurl:e,imgid:r,imgwidth:a,imgheight:i},t)})}),jQuery("#button_add_layer_video").click(function(){var e=jQuery('input[name="rs-gallery-type"]').val();t.prepare_video_layer_add(e)}),jQuery("#button_add_layer_audio").click(function(){t.prepare_video_layer_add("audio")}),t.prepare_video_layer_add=function(e){switch(jQuery("#video_dialog_form").trigger("reset"),jQuery("#reset_video_dialog_tab").click(),e){case"youtube":jQuery(".rs-show-when-youtube-stream").show(),jQuery(".rs-hide-when-youtube-stream").show();break;case"vimeo":jQuery(".rs-show-when-vimeo-stream").show(),jQuery(".rs-hide-when-vimeo-stream").show();break;case"instagram":jQuery(".rs-show-when-instagram-stream").show(),jQuery(".rs-hide-when-instagram-stream").show()}var t=1==jQuery(this).data("isstatic")?"static":null;UniteAdminRev.openVideoDialog(function(e){addLayerVideo(e,t)},!1,e)},jQuery("#button_add_layer_button").click(function(){setExampleButtons(),jQuery("#dialog_addbutton").dialog({buttons:{Close:function(){jQuery("#dialog_addbutton").dialog("close")}},minWidth:830,minHeight:500,modal:!0,dialogClass:"tpdialogs",create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")}})}),jQuery("#button_add_layer_svg, #button_add_object_layer").click(function(){setExampleButtons(),t.callObjectLibraryDialog("object")}),jQuery("#button_change_image_objlib").click(function(){setExampleButtons(),t.callObjectLibraryDialog("background")}),jQuery("#button_change_image_yt").click(function(){var e,r="https://img.youtube.com/vi/"+jQuery("#slide_bg_youtube").val()+"/maxresdefault.jpg";e=r,jQuery("#divbgholder").css("background-image","url("+e+")"),jQuery("#slide_selector .list_slide_links li.selected .slide-media-container ").css("background-image","url("+e+")"),jQuery("#image_url").val(e),jQuery("#image_id").val(""),t.changeSlotBGs(),jQuery(".bgsrcchanger:checked").click(),jQuery('input[name="kenburn_effect"]').is(":checked")&&jQuery('input[name="kb_start_fit"]').change()}),jQuery("body").on("click",".layer-link-type-element",function(){var e=jQuery(this),r=e.closest(".list-of-layer-links"),a=r.find(".layer-link-type-element-cs"),i=t.getLayerByUniqueId(r.data("uniqueid")),o={};o.groupLink=e.data("linktype"),t.updateLayer(i.serial,o);for(var s=0;s<6;s++)a.removeClass("layer-link-type-"+s),i.references.htmlLayer.removeClass("ldles_"+s);i.references.htmlLayer.addClass("ldles_"+o.groupLink),a.addClass("layer-link-type-"+o.groupLink)}),jQuery("body").on("click",".objadd-single-item, .obj-item-size-selector",function(){var r=jQuery(this);if(-1===jQuery.inArray(r.data("type"),[1,2,"1","2","img"])){if(r.hasClass("obj-item-size-selector")){var a="1";switch(r.data("s")){case"xs":a=.1;break;case"s":a=.25;break;case"m":a=.5;break;case"l":a=.75;break;case"o":a=1}r=r.closest(".objadd-single-item")}var i,o,s,n,l,d,y=jQuery("#dialog_addobj").data("last_open_state"),_=jQuery("#obj-layer-bg-switcher").hasClass("addthisasbg");switch(_=("2"===r.data("type")||2===r.data("type"))&&_,_="layer"!==y&&_,r.data("type")){case"img":case"1":case"2":case 1:case 2:if(!rs_plugin_validated)return show_premium_dialog("register-to-acess-object-library"),jQuery("#dialog_addobj").dialog("close"),!1;var c=jQuery("#button_add_layer_image").data("isstatic"),m=(1==c?rev_lang.select_static_layer_image:rev_lang.select_layer_image,c?"static":null);if(-1!==(b=r.data("origsrc")).indexOf("/"))if(!0!==jQuery("#dialog_addobj").data("changeimg")){var p={imgurl:b,imglib:"objectlibrary",imgproportion:!0,imgwidth:parseInt(r.data("mediawidth"),0)*a,imgheight:parseInt(r.data("mediaheight"),0)*a};if("object"!==y||_){if("background"===y||_)e(b);else if("layer"===y){(v={}).bgimage_url=b,v.image_librarysize={},v.image_librarysize.width=parseInt(r.data("mediawidth"),0)*a,v.image_librarysize.height=parseInt(r.data("mediaheight"),0)*a,switchLayerBackground(v)}}else addLayerImage(p,m)}else{if("object"!==y||_){if("background"===y||_)e(b);else if("layer"===y){var v;(v={}).bgimage_url=b,v.image_librarysize={},v.image_librarysize.width=parseInt(r.data("mediawidth"),0)*a,v.image_librarysize.height=parseInt(r.data("mediaheight"),0)*a,switchLayerBackground(v)}}else(v={}).image_url=b,v.image_librarysize={},v.image_librarysize.width=parseInt(r.data("mediawidth"),0)*a,v.image_librarysize.height=parseInt(r.data("mediaheight"),0)*a,v.scaleProportional=!0,t.updateCurrentLayer(v),resetCurrentElementSize(),redrawLayerHtml(t.selectedLayerSerial),t.add_layer_change()}else i=b,o=m,s=a,n=jQuery("#dialog_addobj").data("changeimg"),l=y,d=_,UniteAdminRev.ajaxRequest("load_library_object",{handle:i,type:"orig"},function(r){if(r.success){var a={imgurl:r.url,imglib:"objectlibrary",imgproportion:!0,imgwidth:parseInt(r.width,0)*s,imgheight:parseInt(r.height,0)*s};if("object"!==l||d){if("background"===l)e(r.url);else if("layer"===l){var i;(i={}).bgimage_url=r.url,i.image_librarysize={},i.image_librarysize.width=parseInt(r.width,0)*s,i.image_librarysize.height=parseInt(r.height,0)*s,switchLayerBackground(i)}}else n?((i={}).image_url=r.url,i.image_librarysize={},i.image_librarysize.width=parseInt(r.width,0)*s,i.image_librarysize.height=parseInt(r.height,0)*s,i.scaleProportional=!0,t.updateCurrentLayer(i),resetCurrentElementSize(),redrawLayerHtml(t.selectedLayerSerial),t.add_layer_change()):addLayerImage(a,o)}});jQuery("#dialog_addobj").data("changeimg",!1);break;case"svg":var g=r.find("svg");if(!0!==jQuery("#dialog_addobj").data("changesvg")){var h={},f=g.attr("width"),j=g.attr("height");h.static_styles={},h.static_styles=t.setVal(h.static_styles,"color","#000000",!0),h.deformation={},h["deformation-hover"]={},h.text=" ",h.alias="SVG",h.type="svg",h.style="",h.svg={},h.svg.src=jQuery(this).data("src"),h=t.setVal(h,"max_width",f,!0),(h=t.setVal(h,"max_height",j,!0)).createdOnInit=!1,addLayer(h)}else{var b=jQuery(this).data("src");f=g.attr("width"),j=g.attr("height");serial=jQuery("#dialog_addobj").data("changesvgserial"),jQuery.get(b,function(e){jQuery("#slide_layer_"+serial+" .innerslide_layer.tp-caption").first().html(""),jQuery("#slide_layer_"+serial+" .innerslide_layer.tp-caption").first()[0].innerHTML=(new XMLSerializer).serializeToString(e.documentElement),u.rebuildLayerIdle(jQuery("#slide_layer_"+serial));var r=t.getLayer(serial),a=parseInt(t.getVal(r,"max_width"),0);parseInt(t.getVal(r,"max_height"),0);j*=a/f,f=a,r.svg.src=b,r=t.setVal(r,"max_width",f,!0),r=t.setVal(r,"max_height",j,!0)}),t.add_layer_change(),jQuery("#dialog_addobj").data("changesvg",!1)}break;case"icon":addLayerText(1==jQuery(this).data("isstatic")?"static":null,'<i class="'+r.data("src")+'"></i>',50)}jQuery("#dialog_addobj").dialog("close")}}),jQuery("#button_add_layer_shape").click(function(){setExampleShape(),jQuery("#dialog_addshape").dialog({buttons:{Add:function(){var e={static_styles:{},deformation:{},"deformation-hover":{},text:" ",alias:"Shape",type:"shape",style:"",internal_class:"tp-shape tp-shapewrapper",autolinebreak:!1};e.deformation["background-color"]=jQuery('input[name="adshape-color-1"]').val(),e.deformation["border-color"]=jQuery('input[name="adshape-border-color"]').val(),e.deformation["border-width"]=jQuery('input[name="adshape-border-width"]').val(),e.deformation["border-style"]="solid",e.deformation["border-radius"]=[jQuery(".example-shape").css("borderTopLeftRadius"),jQuery(".example-shape").css("borderTopRightRadius"),jQuery(".example-shape").css("borderBottomRightRadius"),jQuery(".example-shape").css("borderBottomLeftRadius")],jQuery('input[name="shape_fullwidth"]')[0].checked?(e.max_width="100%",e.cover_mode="fullwidth"):e.max_width=jQuery('input[name="shape_width"]').val(),jQuery('input[name="shape_fullheight"]')[0].checked?(e.max_height="100%",e.cover_mode="fullheight"):e.max_height=jQuery('input[name="shape_height"]').val(),jQuery('input[name="shape_fullheight"]')[0].checked&&jQuery('input[name="shape_fullwidth"]')[0].checked&&(e.cover_mode="cover"),e.createdOnInit=!1,addLayer(e),jQuery("#dialog_addshape").dialog("close")},Close:function(){jQuery("#dialog_addshape").dialog("close")}},minWidth:830,minHeight:500,modal:!0,create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")},dialogClass:"tpdialogs"})}),jQuery("#button_add_layer_import").click(function(){t.reset_import_layer(),jQuery("#dialog_addimport").dialog({minWidth:830,minHeight:500,modal:!0,dialogClass:"tpdialogs",create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")}})}),jQuery("#button_add_layer_group, #add_new_group").click(function(){addLayerGroup(1==jQuery(this).data("isstatic")?"static":null)}),jQuery("#add_new_row").click(function(){addLayerRow(1==jQuery(this).data("isstatic")?"static":null)}),jQuery("#rs-import-layer-slider").change(function(){var e=jQuery(this).val();return"-"==e?(t.reset_import_layer(),!1):void 0===import_slides[e]?(UniteAdminRev.ajaxRequest("get_import_slides_data",e,function(r){return void 0!==r.slides?(import_slides[e]=r.slides,t.do_clear_layer_import(),t.do_clear_slide_import(),t.do_add_slide_import(),t.do_show_sliders_import(),!0):(alert(rev_lang.empty_data_retrieved_for_slider),t.reset_import_layer(),!1)}),!1):(t.do_clear_layer_import(),t.do_clear_slide_import(),t.do_add_slide_import(),void t.do_show_sliders_import())}),jQuery("#rs-import-layer-type").change(function(){t.do_clear_layer_import(),t.do_show_sliders_import()}),jQuery("#rs-import-layer-slide").change(function(){t.do_clear_layer_import(),t.do_show_sliders_import()}),jQuery("body").on("click","#rs-import-layer-holder li .import-layer-now",function(){if(confirm(rev_lang.import_selected_layer)){var e=jQuery(this),r=e.closest("li"),a=r.data("sliderid"),i=r.data("slideid"),o=r.data("id");r.data("actiondep");if(void 0===import_slides[a])return!1;if(void 0===import_slides[a][i])return!1;var s=t.get_layers_to_add_through_actions(import_slides[a][i].layers,o);if(null!==s&&s.length>0)if(s.length>1&&confirm(rev_lang.import_all_layer_from_actions)){var n=[];for(var l in n=t.get_action_dependencies(r,i,n),s)if(s.hasOwnProperty(l))for(var d in s[l].createdOnInit=!1,addLayer(s[l],!0),r.addClass("layerimported"),e.find("i").addClass("eg-icon-ok").removeClass("eg-icon-plus"),n)if(n.hasOwnProperty(d)){var y=jQuery("#to-import-layer-id-"+i+"-"+n[d]);y.hasClass("layerimported")||(y.addClass("layerimported"),y.find(".import-layer-now i").addClass("eg-icon-ok").removeClass("eg-icon-plus"))}}else for(var l in s)if(s.hasOwnProperty(l)){s[l]=t.remove_import_layer_actions(s[l]),s[l].createdOnInit=!1,s[l].p_uid=-1,addLayer(s[l],!0),r.addClass("layerimported"),e.find("i").addClass("eg-icon-ok").removeClass("eg-icon-plus");break}}}),t.object_library_loaded=!1,t.callObjectLibraryDialog=function(e){t.object_library_loaded?t.call_object_dialog(e):UniteAdminRev.ajaxRequest("load_object_library",{},function(r){if(r.success){for(var a in r.data.html)r.data.html.hasOwnProperty(a)&&("tag"==r.data.html[a].type?jQuery(".object-tag-list").append('<span class="obj_library_cats" id="obj_library_cats_'+r.data.html[a].handle+'" data-tag="'+r.data.html[a].handle+'">'+r.data.html[a].name+"</span>"):"inner"==r.data.html[a].type&&jQuery("#object_library_results-inner").append('<div style="display:none" class="rs-obj-library"></div>'));for(var a in r.data.list)if(r.data.list.hasOwnProperty(a)){var i={handle:a,list:[]};for(var o in r.data.list[a])r.data.list[a].hasOwnProperty(o)&&i.list.push(r.data.list[a][o]);obj_libraries.push(i)}push_objects_to_library(),t.object_library_loaded=!0,t.call_object_dialog(e)}})},t.call_object_dialog=function(e){jQuery("#dialog_addobj").dialog({width:jQuery(window).width(),height:jQuery(window).height(),modal:!0,dialogClass:"tpdialogs fullscreen-dialog-window objectlibrary_dialog",create:function(e,t){var r=jQuery(e.target).parent().find(".ui-dialog-titlebar");r.addClass("tp-slider-new-dialog-title"),r.prepend('<span class="revlogo-mini" style="margin-right:15px;"></span>')},hide:{effect:"",delay:250},open:function(r){var a=jQuery(r.target).parent();if(setTimeout(function(){a.addClass("show")},200),jQuery("#dialog_addobj").data("last_open_state")!==e){jQuery("#dialog_addobj").data("last_open_state",e);var i=a.find(".ui-dialog-titlebar .ui-dialog-title");"background"===e?(jQuery(".obj_library_cats_filter").hide(),jQuery("#object_library_results").addClass("backgrounds"),jQuery("#obj_lib_main_cat_filt_bgimage").show().click(),i.html("Add Background Image")):"object"===e?(jQuery("#object_library_results").removeClass("backgrounds"),jQuery(".obj_library_cats_filter").show(),jQuery("#obj_lib_main_cat_filt_all").show().click(),i.html("Add Object Layer")):"layer"===e&&(jQuery(".obj_library_cats_filter").hide(),jQuery("#object_library_results").addClass("backgrounds"),jQuery("#obj_lib_main_cat_filt_bgimage").show(),jQuery("#obj_lib_main_cat_filt_image").show(),jQuery("#obj_lib_main_cat_filt_allimages").click(),i.html("Add Image as Layer Background"))}jQuery("#object_library_results-inner").perfectScrollbar("update"),0==jQuery(".obj_library_cats.selected").length&&jQuery(".obj_library_cats").first().click(),jQuery("#dialog_addobj").closest(".ui-dialog").addClass("visible-fullscreen-dialog"),t.setObjectLibraryHeight()},beforeClose:function(e){jQuery(e.target).parent().removeClass("show")},close:function(e){jQuery("#dialog_addobj").closest(".ui-dialog").removeClass("visible-fullscreen-dialog")}})},t.setObjectLibraryHeight=function(){var e=jQuery("#dialog_addobj");if(e.is(":visible")){var t=jQuery("#object_library_results"),r=jQuery(window).height(),a=jQuery("#addobj-dialog-header").height(),i=e.parent().find(".tp-slider-new-dialog-title").height(),o=r-(a+i+100);e.height(r-i),t.height(o)}},t.get_action_dependencies=function(e,r,a){var i=e.data("actiondep");if(""!==i)for(var o in i=i.toString().split(","))if(i.hasOwnProperty(o)&&"backgroundvideo"!==i[o]&&"firstvideo"!==i[o]&&-1==jQuery.inArray(i[o],a)){a.push(i[o]);var s=t.get_action_dependencies(jQuery("#to-import-layer-id-"+r+"-"+i[o]),r,a);for(var n in s)s.hasOwnProperty(n)&&-1==jQuery.inArray(s[n],a)&&a.push(s[n])}return a},t.remove_import_layer_actions=function(e){var t=["tooltip_event","action","image_link","link_follow","link_open_in","jump_to_slide","scrollunder_offset","actioncallback","layer_target","action_delay","link_type","toggle_layer_type","toggle_class"];if(void 0!==e.layer_action&&void 0!==e.layer_action.action&&e.layer_action.action.length>0)for(var r in e.layer_action.action)if(e.layer_action.action.hasOwnProperty(r))switch(e.layer_action.action[r]){case"start_in":case"start_out":case"toggle_layer":case"start_video":case"stop_video":case"toggle_video":case"mute_video":case"unmute_video":case"toggle_global_mute_video":case"toggle_mute_video":case"simulate_click":case"toggle_class":for(var a in t)t.hasOwnProperty(a)&&void 0!==e.layer_action[t[a]]&&void 0!==e.layer_action[t[a]][r]&&delete e.layer_action[t[a]][r]}return e},t.get_layers_to_add_through_actions=function(e,r){var a=[];return a=t.add_layer_to_queue_by_unique_id(e,r,a),a=t.check_actions_change_unique_ids(a)},t.add_layer_to_queue_by_unique_id=function(e,r,a){for(var i in e)if(e.hasOwnProperty(i)&&e[i].unique_id==r&&(a.push(UniteAdminRev.duplicateObject(e[i])),void 0!==e[i].layer_action&&void 0!==e[i].layer_action.action&&e[i].layer_action.action.length>0)){var o=e[i].layer_action;for(var s in o.action)if(o.action.hasOwnProperty(s))switch(o.action[s]){case"start_in":case"start_out":case"toggle_layer":case"start_video":case"stop_video":case"toggle_video":case"mute_video":case"unmute_video":case"toggle_mute_video":case"toggle_global_mute_video":case"simulate_click":case"toggle_class":if(o.layer_target[s]){var n=!0;for(var l in a)if(a.hasOwnProperty(l)&&a[l].unique_id==o.layer_target[s]){n=!1;break}if(n)for(var d in e)if(e.hasOwnProperty(d)&&e[d].unique_id==o.layer_target[s]){t.add_layer_to_queue_by_unique_id(e,e[d].unique_id,a);break}}}}return a},t.check_actions_change_unique_ids=function(e){var t={};for(var r in e)e.hasOwnProperty(r)&&(unique_layer_id++,t[e[r].unique_id]=unique_layer_id,e[r].unique_id=unique_layer_id);for(var r in e)if(e.hasOwnProperty(r)&&void 0!==e[r].layer_action&&void 0!==e[r].layer_action.action&&e[r].layer_action.action.length>0){for(var a in e[r].layer_action.layer_target)if(e[r].layer_action.layer_target.hasOwnProperty(a))for(var i in t)if(t.hasOwnProperty(i)&&i==e[r].layer_action.layer_target[a]){e[r].layer_action.layer_target[a]=t[i];break}for(var a in e[r].layer_action.jump_to_slide)e[r].layer_action.jump_to_slide.hasOwnProperty(a)&&(e[r].layer_action.jump_to_slide[a]="-1")}return e},t.reset_import_layer=function(){jQuery('#rs-import-layer-slider option[value="-"]').attr("selected",!0),jQuery('#rs-import-layer-slide option[value="-"]').attr("selected",!0),jQuery('#rs-import-layer-type option[value="-"]').attr("selected",!0)},t.do_add_slide_import=function(){var e=jQuery("#rs-import-layer-slider option:selected").val();if("-"==e||void 0===import_slides[e])return!1;for(var t in import_slides[e])import_slides[e].hasOwnProperty(t)&&jQuery("#rs-import-layer-slide").append(jQuery("<option></option>").val(t).text("Slide: "+import_slides[e][t].params.title))},t.do_clear_slide_import=function(){jQuery("#rs-import-layer-slide option").each(function(){"all"!==jQuery(this).val()&&"-"!==jQuery(this).val()&&jQuery(this).remove()})},t.do_clear_layer_import=function(){jQuery("#rs-import-layer-holder").html("")},t.do_show_sliders_import=function(){var e=jQuery("#rs-import-layer-slider option:selected").val(),r=jQuery("#rs-import-layer-slide option:selected").val(),a=jQuery("#rs-import-layer-type option:selected").val();if("-"==e)return!1;if(void 0===import_slides[e])return!1;for(var i in import_slides[e])if(import_slides[e].hasOwnProperty(i)&&("all"==r||"-"==r||r==i))for(var o in import_slides[e][i].layers.length>0&&jQuery("#rs-import-layer-holder").append('<li class="layer-import-slide-seperator">Slide Title - '+import_slides[e][i].params.title+"</li>"),import_slides[e][i].layers)if(import_slides[e][i].layers.hasOwnProperty(o)&&("all"==a||"-"==a||a==import_slides[e][i].layers[o].type)){var s=[];s=t.get_action_string_dependencies(import_slides[e][i].layers[o],r,s,import_slides[e][i].layers);var n="off";if(void 0!==import_slides[e][i].layers[o].layer_action&&void 0!==import_slides[e][i].layers[o].layer_action.action&&import_slides[e][i].layers[o].layer_action.action.length>0){for(var l in import_slides[e][i].layers[o].layer_action.action)if(import_slides[e][i].layers[o].layer_action.action.hasOwnProperty(l))switch(import_slides[e][i].layers[o].layer_action.action[l]){case"start_in":case"start_out":case"toggle_layer":case"start_video":case"stop_video":case"toggle_video":case"mute_video":case"unmute_video":case"toggle_mute_video":case"toggle_global_mute_video":case"simulate_click":case"toggle_class":s.push(import_slides[e][i].layers[o].layer_action.layer_target[l])}n="on"}var d=global_layer_import_template({withaction:n,action_layers:s,type:import_slides[e][i].layers[o].type,alias:import_slides[e][i].layers[o].alias,width:import_slides[e][i].layers[o].width,height:import_slides[e][i].layers[o].height,unique_id:import_slides[e][i].layers[o].unique_id,slider_id:e,slide_id:i});jQuery("#rs-import-layer-holder").append(d)}},t.get_action_string_dependencies=function(e,r,a,i){if(void 0!==e.layer_action&&void 0!==e.layer_action.action&&e.layer_action.action.length>0)for(var o in e.layer_action.action)if(e.layer_action.action.hasOwnProperty(o))switch(e.layer_action.action[o]){case"start_in":case"start_out":case"toggle_layer":case"start_video":case"stop_video":case"toggle_video":case"mute_video":case"unmute_video":case"toggle_mute_video":case"toggle_global_mute_video":case"simulate_click":case"toggle_class":if(void 0!==e.layer_action.layer_target&&void 0!==e.layer_action.layer_target[o]&&"backgroundvideo"!==e.layer_action.layer_target[o]&&"firstvideo"!==e.layer_action.layer_target[o]&&-1==jQuery.inArray(e.layer_action.layer_target[o],a))for(var s in a.push(e.layer_action.layer_target[o]),i)if(i.hasOwnProperty(s)&&i[s].unique_id==e.layer_action.layer_target[o]){var n=t.get_action_string_dependencies(i[s],r,a,i);for(var l in n)n.hasOwnProperty(l)&&-1==jQuery.inArray(n[l],a)&&a.push(n[l]);break}}return a},jQuery("body").on("click",".addbutton-examples-wrapper a.rev-btn",function(){var e={static_styles:{},inline:{idle:{},hover:{}},deformation:{},"deformation-hover":{}};e.text=jQuery('input[name="adbutton-text"]').val(),e.type="button",e.subtype="roundbutton",e.specialsettings={},e.alias="Button",e.style="",e.internal_class=jQuery(this).data("needclass"),e["resize-full"]=!1,e.resizeme=!1,e.static_styles.color=jQuery('input[name="adbutton-color-2"]').val(),e.static_styles["font-size"]=jQuery(this).css("font-size"),e.static_styles["line-height"]=jQuery(this).css("font-size"),e.static_styles["font-weight"]=jQuery(this).css("font-weight"),e.max_width="auto",e.max_height="auto",e.autolinebreak=!1,e.deformation.padding=[jQuery(this).css("paddingTop"),jQuery(this).css("paddingRight"),jQuery(this).css("paddingBottom"),jQuery(this).css("paddingLeft")],e.deformation["font-family"]=jQuery('input[name="adbutton-fontfamily"]').val(),e.deformation["background-color"]=jQuery('input[name="adbutton-color-1"]').val(),e.deformation["border-radius"]=[jQuery(this).css("borderTopLeftRadius"),jQuery(this).css("borderTopRightRadius"),jQuery(this).css("borderBottomRightRadius"),jQuery(this).css("borderBottomLeftRadius")],e.deformation["border-color"]=jQuery('input[name="adbutton-border-color"]').val(),e.deformation["border-opacity"]=jQuery('input[name="adbutton-border-opacity"]').val(),e.deformation["border-style"]="solid",jQuery(this).hasClass("rev-withicon")?(e.deformation["icon-class"]=jQuery(".addbutton-icon i").attr("class"),e.text+='<i class="'+e.deformation["icon-class"]+'"></i>'):e.deformation["icon-class"]="",e.hover=!0,e["deformation-hover"]["background-color"]=jQuery('input[name="adbutton-color-1-h"]').val(),e["deformation-hover"].color=jQuery('input[name="adbutton-color-2-h"]').val(),e["deformation-hover"]["border-radius"]=[jQuery(this).css("borderTopLeftRadius"),jQuery(this).css("borderTopRightRadius"),jQuery(this).css("borderBottomRightRadius"),jQuery(this).css("borderBottomLeftRadius")],e["deformation-hover"]["border-color"]=jQuery('input[name="adbutton-border-color-h"]').val(),e["deformation-hover"]["border-opacity"]=jQuery('input[name="adbutton-border-opacity-h"]').val(),e["deformation-hover"]["border-style"]="solid",jQuery(this).hasClass("rev-hiddenicon")?(e["deformation-hover"]["icon-class"]=jQuery(".addbutton-icon i").attr("class"),e.text+=' <i class="'+e["deformation-hover"]["icon-class"]+'"></i>'):e["deformation-hover"]["icon-class"]="",jQuery(this).hasClass("rev-btn")&&(e.deformation["text-decoration"]="none",e["deformation-hover"].css_cursor="pointer",e["deformation-hover"].pointer_events="auto",e.inline.idle.outline="none",e.inline.idle["box-shadow"]="none",e.inline.idle["box-sizing"]="border-box",e.inline.idle["-moz-box-sizing"]="border-box",e.inline.idle["-webkit-box-sizing"]="border-box"),jQuery(this).hasClass("rev-uppercase")&&(e.inline.idle["text-transform"]="uppercase",e.inline.idle["letter-spacing"]="1px"),e.createdOnInit=!1,addLayer(e),jQuery("#dialog_addbutton").dialog("close")});var r=function(e){var t={static_styles:{},inline:{idle:{},hover:{}},deformation:{},"deformation-hover":{}};return t.text=e.html(),t.type="button",t.specialsettings={},t.style="",t.internal_class=e.data("needclass"),t["resize-full"]=!1,t.resizeme=!1,t.max_width=e.css("width"),t.max_height=e.css("height"),t.deformation.padding=[e.css("paddingTop"),e.css("paddingRight"),e.css("paddingBottom"),e.css("paddingLeft")],t.deformation["background-color"]=e.css("backgroundColor"),t.deformation["border-color"]=e.css("borderTopColor"),t.deformation["border-radius"]=[e.css("borderTopLeftRadius"),e.css("borderTopRightRadius"),e.css("borderBottomRightRadius"),e.css("borderBottomLeftRadius")],t.deformation["border-style"]=e.css("borderTopStyle"),t["deformation-hover"].css_cursor=e.css("cursor"),t["deformation-hover"].css_cursor=e.css("cursor"),t["deformation-hover"].pointer_events=e.css("pointer-events")||"auto",t.inline.idle["box-sizing"]="border-box",t.inline.idle["-moz-box-sizing"]="border-box",t.inline.idle["-webkit-box-sizing"]="border-box",t};jQuery("body").on("click",".addbutton-examples-wrapper div.rev-burger",function(){var e=r(jQuery(this));e.alias="Burger Button",e.subtype="burgerbutton",e.layer_action={},e.layer_action.tooltip_event=[],e.layer_action.tooltip_event.push("click"),e.layer_action.action=[],e.layer_action.action.push("toggle_class"),e.layer_action.layer_target=[],e.layer_action.layer_target.push("self"),e.layer_action.action_delay=[],e.layer_action.action_delay.push(0),e.layer_action.toggle_class=[],e.layer_action.toggle_class.push("open"),e.createdOnInit=!1,addLayer(e),jQuery("#dialog_addbutton").dialog("close")}),jQuery("body").on("click",".addbutton-examples-wrapper span.rev-control-btn",function(){var e=r(jQuery(this));e.alias="Control Button",e.subtype="controlbutton",void 0===e.static_styles&&(e.static_styles={}),e.static_styles["font-size"]=jQuery(this).css("font-size"),e.static_styles["line-height"]=jQuery(this).css("line-height"),e.static_styles["font-weight"]=jQuery(this).css("font-weight"),e.static_styles.color=jQuery(this).css("color"),e.deformation["font-family"]=jQuery(this).css("font-family"),e["text-align"]=jQuery(this).css("text-align"),e.deformation["vertical-align"]=jQuery(this).css("vertical-align"),e.createdOnInit=!1,addLayer(e),jQuery("#dialog_addbutton").dialog("close")}),jQuery("body").on("click",".addbutton-examples-wrapper span.rev-scroll-btn",function(){var e=r(jQuery(this));e.subtype="scrollbutton",e.alias="Scroll Button",e.createdOnInit=!1,addLayer(e),jQuery("#dialog_addbutton").dialog("close")}),jQuery("#linkInsertTemplate").click(function(){if(jQuery(this).hasClass("disabled"))return!1;add_meta_into="";var e={Cancel:function(){jQuery("#dialog_template_insert").dialog("close")}};jQuery("#dialog_template_insert").dialog({buttons:e,minWidth:700,dialogClass:"tpdialogs",modal:!0,create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")}})}),jQuery(".rs-param-meta-open").click(function(){add_meta_into="params_"+jQuery(this).data("curid");var e={Cancel:function(){jQuery("#dialog_template_insert").dialog("close")}};jQuery("#dialog_template_insert").dialog({buttons:e,minWidth:700,dialogClass:"tpdialogs",modal:!0,create:function(e){jQuery(e.target).parent().find(".ui-dialog-titlebar").addClass("tp-slider-new-dialog-title")}})}),jQuery("#rs-undo-handler").click(function(){t.undo_redo_layer("undo")}),jQuery("#rs-redo-handler").click(function(){t.undo_redo_layer("redo")}),jQuery("body").on("click",".rs-undo-step",function(){var e=jQuery(this).data("gotokey");t.undo_redo_layer("undo",e)}),jQuery("body").on("click",".rs-redo-step",function(){var e=jQuery(this).data("gotokey");t.undo_redo_layer("redo",e)})},createNewAnimObj=function(e){var t=new Object;return"start"==e?(t.movex=jQuery('input[name="layer_anim_xstart"]').val(),t.movey=jQuery('input[name="layer_anim_ystart"]').val(),t.mask=jQuery("#masking-start")[0].checked,t.use_text_c=jQuery("#use_text_color_start")[0].checked,t.use_bg_c=jQuery("#use_bg_color_start")[0].checked,t.movez=jQuery('input[name="layer_anim_zstart"]').val(),t.rotationx=jQuery('input[name="layer_anim_xrotate"]').val(),t.rotationy=jQuery('input[name="layer_anim_yrotate"]').val(),t.rotationz=jQuery('input[name="layer_anim_zrotate"]').val(),t.scalex=jQuery('input[name="layer_scale_xstart"]').val(),t.scaley=jQuery('input[name="layer_scale_ystart"]').val(),t.skewx=jQuery('input[name="layer_skew_xstart"]').val(),t.skewy=jQuery('input[name="layer_skew_ystart"]').val(),t.captionopacity=jQuery('input[name="layer_opacity_start"]').val(),t.blurfilter=jQuery('input[name="blurfilter_start"]').val(),t.grayscalefilter=jQuery('input[name="grayscalefilter_start"]').val(),t.brightnessfilter=jQuery('input[name="brightnessfilter_start"]').val(),t.mask_x=jQuery('input[name="mask_anim_xstart"]').val(),t.mask_y=jQuery('input[name="mask_anim_ystart"]').val(),t.text_c=jQuery('input[name="text_color_start"]').val(),t.bg_c=jQuery('input[name="bg_color_start"]').val(),t.mask_ease=jQuery('input[name="mask_easing"]').val(),t.mask_speed=jQuery('input[name="mask_speed"]').val(),t.easing=jQuery('select[name="layer_easing"] option:selected').val(),t.speed=jQuery('input[name="layer_speed"]').val(),t.split=jQuery('select[name="layer_split"] option:selected').val(),t.split_direction=jQuery('select[name="layer_split_direction"] option:selected').val(),t.splitdelay=jQuery('input[name="layer_splitdelay"]').val(),t.sfx_effect=jQuery("#sfx_in_effect").val(),t.movex_reverse=jQuery("#layer_anim_xstart_reverse")[0].checked,t.movey_reverse=jQuery("#layer_anim_ystart_reverse")[0].checked,t.rotationx_reverse=jQuery("#layer_anim_xrotate_reverse")[0].checked,t.rotationy_reverse=jQuery("#layer_anim_yrotate_reverse")[0].checked,t.rotationz_reverse=jQuery("#layer_anim_zrotate_reverse")[0].checked,t.scalex_reverse=jQuery("#layer_scale_xstart_reverse")[0].checked,t.scaley_reverse=jQuery("#layer_scale_ystart_reverse")[0].checked,t.skewx_reverse=jQuery("#layer_skew_xstart_reverse")[0].checked,t.skewy_reverse=jQuery("#layer_skew_ystart_reverse")[0].checked,t.mask_x_reverse=jQuery("#mask_anim_xstart_reverse")[0].checked,t.mask_y_reverse=jQuery("#mask_anim_ystart_reverse")[0].checked):(t.use_text_c=jQuery("#use_text_color_end")[0].checked,t.use_bg_c=jQuery("#use_bg_color_end")[0].checked,t.text_c=jQuery('input[name="text_color_end"]').val(),t.bg_c=jQuery('input[name="bg_color_end"]').val(),t.movex=jQuery("#layer_anim_xend").val(),t.movey=jQuery("#layer_anim_yend").val(),t.movez=jQuery("#layer_anim_zend").val(),t.rotationx=jQuery("#layer_anim_xrotate_end").val(),t.rotationy=jQuery("#layer_anim_yrotate_end").val(),t.rotationz=jQuery("#layer_anim_zrotate_end").val(),t.scalex=jQuery("#layer_scale_xend").val(),t.scaley=jQuery("#layer_scale_yend").val(),t.skewx=jQuery("#layer_skew_xend").val(),t.skewy=jQuery("#layer_skew_yend").val(),t.captionopacity=jQuery("#layer_opacity_end").val(),t.mask=jQuery("#masking-end")[0].checked,t.mask_x=jQuery("#mask_anim_xend").val(),t.mask_y=jQuery("#mask_anim_yend").val(),t.easing=jQuery("#layer_endeasing option:selected").val(),t.speed=jQuery("#layer_endspeed").val(),t.split=jQuery("#layer_endsplit option:selected").val(),t.split_direction=jQuery("#layer_endsplit_direction option:selected").val(),t.splitdelay=jQuery("#layer_endsplitdelay").val(),t.sfx_effect=jQuery("#sfx_out_effect").val(),t.movex_reverse=jQuery("#layer_anim_xend_reverse")[0].checked,t.movey_reverse=jQuery("#layer_anim_yend_reverse")[0].checked,t.rotationx_reverse=jQuery("#layer_anim_xrotate_end_reverse")[0].checked,t.rotationy_reverse=jQuery("#layer_anim_yrotate_end_reverse")[0].checked,t.rotationz_reverse=jQuery("#layer_anim_zrotate_end_reverse")[0].checked,t.scalex_reverse=jQuery("#layer_scale_xend_reverse")[0].checked,t.scaley_reverse=jQuery("#layer_scale_yend_reverse")[0].checked,t.skewx_reverse=jQuery("#layer_skew_xend_reverse")[0].checked,t.skewy_reverse=jQuery("#layer_skew_yend_reverse")[0].checked,t.mask_x_reverse=jQuery("#mask_anim_xend_reverse")[0].checked,t.mask_y_reverse=jQuery("#mask_anim_xend_reverse")[0].checked),t},setNewAnimFromObj=function(e,r){if(null==r)return!0;"start"==e?(void 0!==r.movex?jQuery('input[name="layer_anim_xstart"]').val(r.movex):jQuery('input[name="layer_anim_xstart"]').val(0),void 0!==r.movey?jQuery('input[name="layer_anim_ystart"]').val(r.movey):jQuery('input[name="layer_anim_ystart"]').val(0),void 0!==r.movez?jQuery('input[name="layer_anim_zstart"]').val(r.movez):jQuery('input[name="layer_anim_zstart"]').val(0),void 0!==r.rotationx?jQuery('input[name="layer_anim_xrotate"]').val(r.rotationx):jQuery('input[name="layer_anim_xrotate"]').val(0),void 0!==r.rotationy?jQuery('input[name="layer_anim_yrotate"]').val(r.rotationy):jQuery('input[name="layer_anim_yrotate"]').val(0),void 0!==r.rotationz?jQuery('input[name="layer_anim_zrotate"]').val(r.rotationz):jQuery('input[name="layer_anim_zrotate"]').val(0),void 0!==r.scalex?jQuery('input[name="layer_scale_xstart"]').val(r.scalex):jQuery('input[name="layer_scale_xstart"]').val(0),void 0!==r.scaley?jQuery('input[name="layer_scale_ystart"]').val(r.scaley):jQuery('input[name="layer_scale_ystart"]').val(0),void 0!==r.skewx?jQuery('input[name="layer_skew_xstart"]').val(r.skewx):jQuery('input[name="layer_skew_xstart"]').val(0),void 0!==r.skewy?jQuery('input[name="layer_skew_ystart"]').val(r.skewy):jQuery('input[name="layer_skew_ystart"]').val(0),void 0!==r.captionopacity?jQuery('input[name="layer_opacity_start"]').val(r.captionopacity):jQuery('input[name="layer_opacity_start"]').val(0),void 0!==r.blurfilter?jQuery('input[name="blurfilter_start"]').val(r.blurfilter):jQuery('input[name="blurfilter_start"]').val(0),void 0!==r.grayscalefilter?jQuery('input[name="grayscalefilter_start"]').val(r.grayscalefilter):jQuery('input[name="grayscalefilter_start"]').val(0),void 0!==r.brightnessfilter?jQuery('input[name="brightnessfilter_start"]').val(r.brightnessfilter):jQuery('input[name="brightnessfilter_start"]').val(100),void 0===r.mask||"true"!=r.mask&&1!=r.mask?jQuery("#masking-start").attr("checked",!1):jQuery("#masking-start").attr("checked",!0),void 0===r.use_text_c||"true"!=r.use_text_c&&1!=r.use_text_c?jQuery("#use_text_color_start").attr("checked",!1):jQuery("#use_text_color_start").attr("checked",!0),void 0===r.use_bg_c||"true"!=r.use_bg_c&&1!=r.use_bg_c?jQuery("#use_bg_color_start").attr("checked",!1):jQuery("#use_bg_color_start").attr("checked",!0),void 0!==r.text_c?jQuery('input[name="text_color_start"]').val(r.text_c):jQuery('input[name="text_color_start"]').val("transparent"),void 0!==r.bg_c?jQuery('input[name="bg_color_start"]').val(r.bg_c):jQuery('input[name="bg_color_start"]').val("transparent"),void 0!==r.mask_x?jQuery('input[name="mask_anim_xstart"]').val(r.mask_x):jQuery('input[name="mask_anim_xstart"]').val(0),void 0!==r.mask_y?jQuery('input[name="mask_anim_ystart"]').val(r.mask_y):jQuery('input[name="mask_anim_ystart"]').val(0),void 0!==r.mask_ease?jQuery('input[name="mask_easing"]').val(r.mask_ease):jQuery('input[name="mask_easing"]').val(0),void 0!==r.mask_speed?jQuery('input[name="mask_speed"]').val(r.mask_speed):jQuery('input[name="mask_speed"]').val(0),void 0!==r.easing&&jQuery('select[name="layer_easing"] option[value="'+r.easing+'"]').attr("selected","selected"),void 0!==r.speed&&jQuery('input[name="layer_speed"]').val(r.speed),void 0!==r.sfx_color_in&&jQuery('input[name="sfx_color_in"]').val(r.sfx_color_in),void 0!==r.split&&jQuery('select[name="layer_split"] option[value="'+r.split+'"]').attr("selected","selected"),void 0!==r.split_direction&&jQuery('select[name="layer_split_direction"] option[value="'+r.split_direction+'"]').attr("selected","selected"),void 0!==r.splitdelay&&jQuery('input[name="layer_splitdelay"]').val(r.splitdelay),void 0===r.movex_reverse||"true"!=r.movex_reverse&&1!=r.movex_reverse?jQuery("#layer_anim_xstart_reverse").attr("checked",!1):jQuery("#layer_anim_xstart_reverse").attr("checked",!0),void 0===r.movey_reverse||"true"!=r.movey_reverse&&1!=r.movey_reverse?jQuery("#layer_anim_ystart_reverse").attr("checked",!1):jQuery("#layer_anim_ystart_reverse").attr("checked",!0),void 0===r.rotationx_reverse||"true"!=r.rotationx_reverse&&1!=r.rotationx_reverse?jQuery('input[name="layer_anim_xrotate_start_reverse"]').attr("checked",!1):jQuery('input[name="layer_anim_xrotate_start_reverse"]').attr("checked",!0),void 0===r.rotationy_reverse||"true"!=r.rotationy_reverse&&1!=r.rotationy_reverse?jQuery('input[name="layer_anim_yrotate_start_reverse"]').attr("checked",!1):jQuery('input[name="layer_anim_yrotate_start_reverse"]').attr("checked",!0),void 0===r.rotationz_reverse||"true"!=r.rotationz_reverse&&1!=r.rotationz_reverse?jQuery('input[name="layer_anim_zrotate_start_reverse"]').attr("checked",!1):jQuery('input[name="layer_anim_zrotate_start_reverse"]').attr("checked",!0),void 0===r.scalex_reverse||"true"!=r.scalex_reverse&&1!=r.scalex_reverse?jQuery("#layer_scale_xstart_reverse").attr("checked",!1):jQuery("#layer_scale_xstart_reverse").attr("checked",!0),void 0===r.scaley_reverse||"true"!=r.scaley_reverse&&1!=r.scaley_reverse?jQuery("#layer_scale_ystart_reverse").attr("checked",!1):jQuery("#layer_scale_ystart_reverse").attr("checked",!0),void 0===r.skewx_reverse||"true"!=r.skewx_reverse&&1!=r.skewx_reverse?jQuery("#layer_skew_xstart_reverse").attr("checked",!1):jQuery("#layer_skew_xstart_reverse").attr("checked",!0),void 0===r.skewy_reverse||"true"!=r.skewy_reverse&&1!=r.skewy_reverse?jQuery("#layer_skew_ystart_reverse").attr("checked",!1):jQuery("#layer_skew_ystart_reverse").attr("checked",!0),void 0===r.mask_x_reverse||"true"!=r.mask_x_reverse&&1!=r.mask_x_reverse?jQuery("#mask_anim_xstart_reverse").attr("checked",!1):jQuery("#mask_anim_xstart_reverse").attr("checked",!0),void 0===r.mask_y_reverse||"true"!=r.mask_y_reverse&&1!=r.mask_y_reverse?jQuery("#mask_anim_ystart_reverse").attr("checked",!1):jQuery("#mask_anim_ystart_reverse").attr("checked",!0)):(void 0===r.use_text_c||"true"!=r.use_text_c&&1!=r.use_text_c?jQuery("#use_text_color_end").attr("checked",!1):jQuery("#use_text_color_end").attr("checked",!0),void 0===r.use_bg_c||"true"!=r.use_bg_c&&1!=r.use_bg_c?jQuery("#use_bg_color_end").attr("checked",!1):jQuery("#use_bg_color_end").attr("checked",!0),void 0!==r.text_c?jQuery('input[name="text_color_end"]').val(r.text_c):jQuery('input[name="text_color_end"]').val("transparent"),void 0!==r.bg_c?jQuery('input[name="bg_color_end"]').val(r.bg_c):jQuery('input[name="bg_color_end"]').val("transparent"),void 0!==r.movex?jQuery("#layer_anim_xend").val(r.movex):jQuery("#layer_anim_xend").val(0),void 0!==r.movey?jQuery("#layer_anim_yend").val(r.movey):jQuery("#layer_anim_yend").val(0),void 0!==r.movez?jQuery("#layer_anim_zend").val(r.movez):jQuery("#layer_anim_zend").val(0),void 0!==r.rotationx?jQuery("#layer_anim_xrotate_end").val(r.rotationx):jQuery("#layer_anim_xrotate_end").val(0),void 0!==r.rotationy?jQuery("#layer_anim_yrotate_end").val(r.rotationy):jQuery("#layer_anim_yrotate_end").val(0),void 0!==r.rotationz?jQuery("#layer_anim_zrotate_end").val(r.rotationz):jQuery("#layer_anim_zrotate_end").val(0),void 0!==r.scalex?jQuery("#layer_scale_xend").val(r.scalex):jQuery("#layer_scale_xend").val(0),void 0!==r.scaley?jQuery("#layer_scale_yend").val(r.scaley):jQuery("#layer_scale_yend").val(0),void 0!==r.skewx?jQuery("#layer_skew_xend").val(r.skewx):jQuery("#layer_skew_xend").val(0),void 0!==r.skewy?jQuery("#layer_skew_yend").val(r.skewy):jQuery("#layer_skew_yend").val(0),void 0!==r.captionopacity?jQuery("#layer_opacity_end").val(r.captionopacity):jQuery("#layer_opacity_end").val(0),void 0===r.mask||"true"!=r.mask&&1!=r.mask?jQuery("#masking-end").attr("checked",!1):jQuery("#masking-end").attr("checked",!0),void 0!==r.mask_x?jQuery("#mask_anim_xend").val(r.mask_x):jQuery("#mask_anim_xend").val(0),void 0!==r.mask_y?jQuery("#mask_anim_yend").val(r.mask_y):jQuery("#mask_anim_yend").val(0),void 0!==r.easing&&jQuery('#layer_endeasing option[value="'+r.easing+'"]').attr("selected","selected"),void 0!==r.speed&&jQuery("#layer_endspeed").val(r.speed),void 0!==r.sfxcolor&&jQuery('input[name="sfx_color_end"]').val(r.sfxcolor),void 0!==r.split&&jQuery('#layer_endsplit option[value="'+r.split+'"]').attr("selected","selected"),void 0!==r.split_direction&&jQuery('#layer_endsplit_direction option[value="'+r.split_direction+'"]').attr("selected","selected"),void 0!==r.splitdelay&&jQuery("#layer_endsplitdelay").val(r.splitdelay),void 0===r.movex_reverse||"true"!=r.movex_reverse&&1!=r.movex_reverse?jQuery("#layer_anim_xend_reverse").attr("checked",!1):jQuery("#layer_anim_xend_reverse").attr("checked",!0),void 0===r.movey_reverse||"true"!=r.movey_reverse&&1!=r.movey_reverse?jQuery("#layer_anim_yend_reverse").attr("checked",!1):jQuery("#layer_anim_yend_reverse").attr("checked",!0),void 0===r.rotationx_reverse||"true"!=r.rotationx_reverse&&1!=r.rotationx_reverse?jQuery("#layer_anim_xrotate_end_reverse").attr("checked",!1):jQuery("#layer_anim_xrotate_end_reverse").attr("checked",!0),void 0===r.rotationy_reverse||"true"!=r.rotationy_reverse&&1!=r.rotationy_reverse?jQuery("#layer_anim_yrotate_end_reverse").attr("checked",!1):jQuery("#layer_anim_yrotate_end_reverse").attr("checked",!0),void 0===r.rotationz_reverse||"true"!=r.rotationz_reverse&&1!=r.rotationz_reverse?jQuery("#layer_anim_zrotate_end_reverse").attr("checked",!1):jQuery("#layer_anim_zrotate_end_reverse").attr("checked",!0),void 0===r.scalex_reverse||"true"!=r.scalex_reverse&&1!=r.scalex_reverse?jQuery("#layer_scale_xend_reverse").attr("checked",!1):jQuery("#layer_scale_xend_reverse").attr("checked",!0),void 0===r.scaley_reverse||"true"!=r.scaley_reverse&&1!=r.scaley_reverse?jQuery("#layer_scale_yend_reverse").attr("checked",!1):jQuery("#layer_scale_yend_reverse").attr("checked",!0),void 0===r.skewx_reverse||"true"!=r.skewx_reverse&&1!=r.skewx_reverse?jQuery("#layer_skew_xend_reverse").attr("checked",!1):jQuery("#layer_skew_xend_reverse").attr("checked",!0),void 0===r.skewy_reverse||"true"!=r.skewy_reverse&&1!=r.skewy_reverse?jQuery("#layer_skew_yend_reverse").attr("checked",!1):jQuery("#layer_skew_yend_reverse").attr("checked",!0),void 0===r.mask_x_reverse||"true"!=r.mask_x_reverse&&1!=r.mask_x_reverse?jQuery("#mask_anim_xend_reverse").attr("checked",!1):jQuery("#mask_anim_xend_reverse").attr("checked",!0),void 0===r.mask_y_reverse||"true"!=r.mask_y_reverse&&1!=r.mask_y_reverse?jQuery("#mask_anim_yend_reverse").attr("checked",!1):jQuery("#mask_anim_yend_reverse").attr("checked",!0)),void 0===r.mask||"true"!=r.mask&&1!=r.mask?jQuery(".mask-start-settings").hide():jQuery(".mask-start-settings").show(),RevSliderSettings.onoffStatus(jQuery("#masking-start")),RevSliderSettings.onoffStatus(jQuery("#masking-end")),RevSliderSettings.onoffStatus(jQuery("#use_text_color_start")),RevSliderSettings.onoffStatus(jQuery("#use_bg_color_start")),RevSliderSettings.onoffStatus(jQuery("#use_text_color_end")),RevSliderSettings.onoffStatus(jQuery("#use_bg_color_end")),t.updateReverseList()},checkMaskingAvailabity=function(){1!=jQuery("#layer__scalex").val()||1!=jQuery("#layer__scaley").val()||0!=parseInt(jQuery("#layer__skewx").val(),0)||0!=parseInt(jQuery("#layer__skewy").val(),0)||0!=parseInt(jQuery("#layer__xrotate").val(),0)||0!=parseInt(jQuery("#layer__yrotate").val(),0)||0!=parseInt(jQuery("#layer_2d_rotation").val(),0)?(jQuery(".mask-not-available").show(),jQuery(".mask-is-available").hide(),jQuery("#masking-start").prop("checked",!1),jQuery("#masking-end").prop("checked",!1),jQuery(".mask-start-settings").hide(),jQuery(".mask-end-settings").hide(),jQuery(".tp-showmask").removeClass("tp-showmask"),RevSliderSettings.onoffStatus(jQuery("#masking-start")),RevSliderSettings.onoffStatus(jQuery("#masking-end")),u.rebuildLayerIdle(getjQueryLayer()),t.updateLayerFromFields()):(jQuery(".mask-not-available").hide(),jQuery(".mask-is-available").show())},checkIfAnimExists=function(e){if("object"==typeof initLayerAnims&&!jQuery.isEmptyObject(initLayerAnims))for(var t in initLayerAnims)if(initLayerAnims.hasOwnProperty(t)&&initLayerAnims[t].handle==e)return initLayerAnims[t].id;return!1},checkIfAnimIsEditable=function(e){if("object"==typeof initLayerAnims&&!jQuery.isEmptyObject(initLayerAnims))for(var t in initLayerAnims)if(initLayerAnims.hasOwnProperty(t)&&initLayerAnims[t].handle==e)return initLayerAnims[t].id;return!1},deleteAnimInDb=function(e){if(UniteAdminRev.setErrorMessageID("dialog_error_message"),""!=(e=jQuery.trim(e))){jQuery("customin"==currentAnimationType?"#layer_animation option":"#layer_endanimation option");UniteAdminRev.ajaxRequest("delete_custom_anim",e,function(r){jQuery("#layer_animation option:selected")!=e&&jQuery("#layer_animation option:selected")!=e.replace("customout","customin")||jQuery('#layer_animation option[value="tp-fade"]').attr("selected",!0),jQuery("#layer_endanimation option:selected")!=e&&jQuery("#layer_endanimation option:selected")!=e.replace("customin","customout")||jQuery('#layer_endanimation option[value="tp-fade"]').attr("selected",!0),t.updateInitLayerAnim(r.customfull),updateLayerAnimsInput(r.customin,"customin"),updateLayerAnimsInput(r.customout,"customout")})}},renameAnimInDb=function(e,r){var a={};a.id=e,a.handle=r,UniteAdminRev.ajaxRequest("update_custom_anim_name",a,function(e){t.updateInitLayerAnim(e.customfull),updateLayerAnimsInput(e.customin,"customin"),updateLayerAnimsInput(e.customout,"customout"),selectLayerAnim(r)})},updateAnimInDb=function(e,r,a){UniteAdminRev.setErrorMessageID("dialog_error_message"),r.handle=e,!1===a?UniteAdminRev.ajaxRequest("insert_custom_anim",r,function(r){t.updateInitLayerAnim(r.customfull),updateLayerAnimsInput(r.customin,"customin"),updateLayerAnimsInput(r.customout,"customout"),selectLayerAnim(e)}):UniteAdminRev.ajaxRequest("update_custom_anim",r,function(a){t.updateInitLayerAnim(a.customfull),updateLayerAnimsInput(a.customin,"customin"),updateLayerAnimsInput(a.customout,"customout"),selectLayerAnim(e);var i=e.replace("customin-","").replace("customout-","");for(var o in t.arrLayers)t.arrLayers.hasOwnProperty(o)&&(t.arrLayers[o].frames.frame_999.animation=="customout-"+i?(t.arrLayers[o].x_end=r.params.movex,t.arrLayers[o].y_end=r.params.movey,t.arrLayers[o].z_end=r.params.movez,t.arrLayers[o].x_rotate_end=r.params.rotationx,t.arrLayers[o].y_rotate_end=r.params.rotationy,t.arrLayers[o].z_rotate_end=r.params.rotationz,t.arrLayers[o].scale_x_end=r.params.scalex,t.arrLayers[o].scale_y_end=r.params.scaley,t.arrLayers[o].opacity_end=r.params.captionopacity,t.arrLayers[o].skew_x_end=r.params.skewx,t.arrLayers[o].skew_y_end=r.params.skewy,t.arrLayers[o].mask_end=r.params.mask,t.arrLayers[o].mask_x_end=r.params.mask_x,t.arrLayers[o].mask_y_end=r.params.mask_y,t.arrLayers[o].mask_ease_end=r.params.mask_ease,t.arrLayers[o].mask_speed_end=r.params.mask_speed,t.arrLayers[o].use_text_c_end=r.params.use_text_c,t.arrLayers[o].use_bg_c_end=r.params.use_bg_c,t.arrLayers[o].text_c_end=r.params.text_c,t.arrLayers[o].bg_c_end=r.params.bg_c,t.arrLayers[o].x_end_reverse=r.params.movex_reverse,t.arrLayers[o].y_end_reverse=r.params.movey_reverse,t.arrLayers[o].x_rotate_end_reverse=r.params.rotationx_reverse,t.arrLayers[o].y_rotate_end_reverse=r.params.rotationy_reverse,t.arrLayers[o].z_rotate_end_reverse=r.params.rotationz_reverse,t.arrLayers[o].scale_x_end_reverse=r.params.scalex_reverse,t.arrLayers[o].scale_y_end_reverse=r.params.scaley_reverse,t.arrLayers[o].skew_x_end_reverse=r.params.skewx_reverse,t.arrLayers[o].skew_y_end_reverse=r.params.skewy_reverse,t.arrLayers[o].mask_x_end_reverse=r.params.mask_x_reverse,t.arrLayers[o].mask_y_end_reverse=r.params.mask_y_reverse,t.arrLayers[o].frames.frame_999.easing=r.params.easing,t.arrLayers[o].frames.frame_999.split=r.params.split,t.arrLayers[o].frames.frame_999.splitdelay=r.params.splitdelay,t.arrLayers[o].frames.frame_999.speed=r.params.speed):t.arrLayers[o].frames.frame_0.animation=="customin-"+i&&(t.arrLayers[o].x_start=r.params.movex,t.arrLayers[o].y_start=r.params.movey,t.arrLayers[o].z_start=r.params.movez,t.arrLayers[o].x_rotate_start=r.params.rotationx,t.arrLayers[o].y_rotate_start=r.params.rotationy,t.arrLayers[o].z_rotate_start=r.params.rotationz,t.arrLayers[o].scale_x_start=r.params.scalex,t.arrLayers[o].scale_y_start=r.params.scaley,t.arrLayers[o].opacity_start=r.params.captionopacity,t.arrLayers[o].blurfilter_start=r.params.blurfilter,t.arrLayers[o].grayscalefilter_start=r.params.grayscalefilter,t.arrLayers[o].brightnessfilter_start=r.params.brightnessfilter,t.arrLayers[o].skew_x_start=r.params.skewx,t.arrLayers[o].skew_y_start=r.params.skewy,t.arrLayers[o].mask_start=r.params.mask,t.arrLayers[o].mask_x_start=r.params.mask_x,t.arrLayers[o].mask_y_start=r.params.mask_y,t.arrLayers[o].mask_ease_start=r.params.mask_ease,t.arrLayers[o].mask_speed_start=r.params.mask_speed,t.arrLayers[o].use_text_c_start=r.params.use_text_c,t.arrLayers[o].use_bg_c_start=r.params.use_bg_c,t.arrLayers[o].text_c_start=r.params.text_c,t.arrLayers[o].bg_c_start=r.params.bg_c,t.arrLayers[o].x_start_reverse=r.params.movex_reverse,t.arrLayers[o].y_start_reverse=r.params.movey_reverse,t.arrLayers[o].x_rotate_start_reverse=r.params.rotationx_reverse,t.arrLayers[o].y_rotate_start_reverse=r.params.rotationy_reverse,t.arrLayers[o].z_rotate_start_reverse=r.params.rotationz_reverse,t.arrLayers[o].scale_x_start_reverse=r.params.scalex_reverse,t.arrLayers[o].scale_y_start_reverse=r.params.scaley_reverse,t.arrLayers[o].skew_x_start_reverse=r.params.skewx_reverse,t.arrLayers[o].skew_y_start_reverse=r.params.skewy_reverse,t.arrLayers[o].mask_x_start_reverse=r.params.mask_x_reverse,t.arrLayers[o].mask_y_start_reverse=r.params.mask_y_reverse,t.arrLayers[o].frames.frame_0.easing=r.params.easing,t.arrLayers[o].frames.frame_0.split=r.params.split,t.arrLayers[o].frames.frame_0.splitdelay=r.params.splitdelay,t.arrLayers[o].frames.frame_0.speed=r.params.speed))})},selectLayerAnim=function(e){jQuery("customin"==currentAnimationType?"#layer_animation option":"#layer_endanimation option").each(function(){jQuery(this).text()==e||jQuery(this).val()==e?jQuery(this).prop("selected",!0):jQuery(this).prop("selected",!1)}),jQuery("customin"==currentAnimationType?"#layer_animation option:selected":"#layer_endanimation option:selected").change()},updateLayerAnimsInput=function(e,t){if("customin"==t)var r=jQuery("#layer_animation"),a=jQuery("#layer_animation option"),i=jQuery("#layer_animation option:selected").val();else r=jQuery("#layer_endanimation"),a=jQuery("#layer_endanimation option"),i=jQuery("#layer_endanimation option:selected").val();if(a.each(function(){jQuery(this).val().indexOf(t)>-1&&jQuery(this).remove()}),"object"==typeof e&&!jQuery.isEmptyObject(e))for(key in e)e.hasOwnProperty(key)&&r.append(new Option(e[key],key));r.val(i),r.change()},getFirstStyle=function(){var e=jQuery("#layer_caption").catcomplete("option","source");return null==e||0==e.length?"":e[0].label},disableFormFields=function(){jQuery(".form_layers")[0].reset(),jQuery(".form_layers input, .form_layers select, .form_layers textarea").attr("disabled","disabled").addClass("setting-disabled"),jQuery("#button_delete_layer").addClass("button-now-disabled"),jQuery("#button_duplicate_layer").addClass("button-now-disabled"),jQuery(".form_layers label, .form_layers .setting_text, .form_layers .setting_unit").addClass("text-disabled"),jQuery("#layer_captions_down").removeClass("ui-state-active").addClass("ui-state-default"),jQuery("#css_fonts_down").removeClass("ui-state-active").addClass("ui-state-default"),jQuery("#linkInsertTemplate").addClass("disabled"),jQuery("#rs-align-wrapper").addClass("table_disabled"),jQuery("#rs-align-wrapper-ver").addClass("table_disabled"),jQuery("#preview_looper").hasClass("deactivated")||jQuery("#preview_looper").click(),layerGeneralParamsStatus=!1},enableFormFields=function(){jQuery(".form_layers input, .form_layers select, .form_layers textarea").not(".rs_disabled_field").prop("disabled",!1).removeClass("setting-disabled"),jQuery("#button_delete_layer").removeClass("button-now-disabled"),jQuery("#button_duplicate_layer").removeClass("button-now-disabled"),jQuery(".form_layers label, .form_layers .setting_text, .form_layers .setting_unit").removeClass("text-disabled"),jQuery("#layer_captions_down").removeClass("ui-state-default").addClass("ui-state-active"),jQuery("#css_fonts_down").removeClass("ui-state-default").addClass("ui-state-active"),jQuery("#linkInsertTemplate").removeClass("disabled"),jQuery("#rs-align-wrapper").removeClass("table_disabled"),jQuery("#rs-align-wrapper-ver").removeClass("table_disabled"),jQuery("#preview_looper").hasClass("deactivated")&&jQuery("#preview_looper").click(),layerGeneralParamsStatus=!0};t.getLayers=function(){return-1!=t.selectedLayerSerial&&t.updateLayerFromFields(),updateLayersImageSizes(),t.arrLayers},t.getSimpleLayers=function(){return t.arrLayers};var updateLayersImageSizes=function(){for(serial in t.arrLayers)if(t.arrLayers.hasOwnProperty(serial)){var e=t.arrLayers[serial];if("image"==e.type){var r=e.references.htmlLayer,a={};a=t.setVal(a,"width",r.width()),a=t.setVal(a,"height",r.height()),t.updateLayer(serial,a)}}};t.clickInsideElement=function(e,t){var r=e.srcElement||e.target;if(r.classList.contains(t))return r;for(;r=r.parentNode;)if(r.classList&&r.classList.contains(t))return r;return!1};var unselectAllFocusedGroup=function(){jQuery(".slide_layer_type_group.inwork").removeClass("inwork"),jQuery("#focusongroup").removeClass("inwork")},refreshEvents=function(e){var r=t.getHtmlLayerFromSerial(e),a=jQuery("#rs-grid-sizes option:selected").val(),i=r[0].classList.contains("slide_layer_type_row")?"#row-zone-top, #row-zone-middle, #row-zone-bottom":r[0].classList.contains("slide_layer_type_column")||r[0].classList.contains("slide_layer_type_group")?"":".slide_layer_type_column .tp_layer_group_inner_wrapper",o=!!r[0].classList.contains("slide_layer_type_row")&&".row_moveme";r.draggable({handle:o,start:onLayerDragStart,refreshPositions:!0,drag:t.onLayerDrag,connectToSortable:i,cancel:" textbox, #layer_text, .layer_on_lock",grid:[a,a],stop:onLayerDragEnd}),jQuery("#focusongroup").click(unselectAllFocusedGroup),jQuery("body").on("dblclick",".inlaydecor",function(e){jQuery(this).closest(".slide_layer_type_group").addClass("inwork"),jQuery("#focusongroup").addClass("inwork")}),r.click(function(a){var i=t.clickInsideElement(a,"groupinfo");if(!1!==i)return jQuery(i).closest(".slide_layer_type_group").addClass("inwork"),jQuery("#focusongroup").addClass("inwork"),!1;if(a.metaKey||-1!==navigator.platform.toUpperCase().indexOf("WIN")&&a.ctrlKey){var o=t.getLayer(e);"checked"==(s=jQuery("input#lots_id_"+o.unique_id)).attr("checked")?s.prop("checked",!1):s.attr("checked","checked"),u.checkMultipleSelectedItems()}else{if(cm.toggleMenuOff(),"rs-row-layout"===jQuery(":focus").attr("id"))return!1;jQuery(":focus").blur(),t.setLayerSelected(e);o=t.getLayer(e);var s=jQuery("input#lots_id_"+o.unique_id);if(-1==jQuery.inArray(o.unique_id,t.selectedLayers)&&u.checkMultipleSelectedItems(!0),s.attr("checked","checked"),t.setLayerSelected(e),r.hasClass("slide_layer_type_row")){o=t.getCurrentLayer();if(void 0!==a.srcElement&&("eg-icon-menu"===a.srcElement.className||"row_editor"===a.srcElement.className)||null!=a.target&&("eg-icon-menu"===a.target.className||"row_editor"===a.target.className)){var n=r.find(".row_editor"),l=jQuery("#rs-layout-row-composer");jQuery(".row_editor.open_re").removeClass("open_re"),n.addClass("open_re"),l.show(),jQuery(".rs-row-break-selector").removeClass("selected"),jQuery(".rs-row-break-selector.rs-slide-ds-"+jQuery("#column_break_at option:selected").val()).addClass("selected")}else if(null!=a.srcElement&&("eg-icon-cancel"===a.srcElement.className||"row_editor"===a.srcElement.className)||null!=a.target&&("eg-icon-cancel"===a.target.className||"row_editor"===a.target.className)){n=r.find(".row_editor"),l=jQuery("#rs-layout-row-composer");jQuery(".row_editor.open_re").removeClass("open_re"),l.hide()}else if(null!=a.srcElement&&("eg-icon-droplet"===a.srcElement.className||"row_config"===a.srcElement.className)||null!=a.target&&("eg-icon-droplet"===a.target.className||"row_config"===a.target.className)){var d=jQuery("#style-morestyle");d.hasClass("showmore")||d.click()}}a.stopPropagation(),u.checkAnimationTab()?(u.stopAllLayerAnimation(),u.animateCurrentSelectedLayer(0)):u.checkLoopTab()&&(u.stopAllLayerAnimation(),u.callCaptionLoops())}})};function checkKeyGroups(e){var r=t.arrLayersChanges.undo&&t.arrLayersChanges.undo.length>0?t.arrLayersChanges.undo[t.arrLayersChanges.undo.length-1].undogroup:"",a=t.arrLayersChanges.undo&&t.arrLayersChanges.undo.length>0?t.arrLayersChanges.undo[t.arrLayersChanges.undo.length-1].serial:-77,i=e.split(".")[0],o={};e=e.substring(e.indexOf(".")+1),o.addtoarray=!1,o.serial=i;var s=e.indexOf("layer_action")>=0?"layer_action":e;return jQuery.each(t.attributegroups,function(e,t){-1!=jQuery.inArray(s,t.keys)&&(o.undogroup=t.groupname,o.icon=t.icon,o.id=t.id)}),o.serial==a||30==o.id&&"Order"==r?o.undogroup!=r?o.addtoarray=!0:o.addtoarray="samegroup":o.addtoarray=!0,null==o.undogroup&&(o.addtoarray=!1),o}function objRek(e,r,a,i){return a=void 0===a?"":a,jQuery.each(e,function(e,o){if(i)return!0;if("references"!==e)if(o instanceof Object){var s=a;a=0==a.length?e:a+"."+e,i=objRek(o,r[e],a,i),a=s}else{s=a;if(a=null==a||0==a.length?e:a+"."+e,void 0!==r&&o!==r[e]&&parseInt(o,0)!=parseInt(r[e],0)&&null!=o&&null!=r[e]&&"renderedData"!=e)if(t.arrLayersChanges.undo&&0==t.arrLayersChanges.undo.length||t.arrLayersChanges.undo[t.arrLayersChanges.undo.length-1].chain!=a){var n=checkKeyGroups(a);!0===n.addtoarray?(t.arrLayersChanges.redo.splice(0,t.arrLayersChanges.redo.length),t.arrLayersChanges.undo.push({amount:0,backup:UniteAdminRev.duplicateObject(t.arrLayersClone),restore:UniteAdminRev.duplicateObject(t.arrLayers),icon:n.icon,id:n.id,undogroup:n.undogroup,serial:n.serial,key:e,chain:a,oldval:o,newval:r[e]}),t.set_save_needed(!0),i=!0):"samegroup"==n.addtoarray&&(t.arrLayersChanges.redo.splice(0,t.arrLayersChanges.redo.length),t.arrLayersChanges.undo[t.arrLayersChanges.undo.length-1].amount++,t.set_save_needed(!0),i=!0)}else t.arrLayersChanges.undo[t.arrLayersChanges.undo.length-1].chain===a&&(t.arrLayersChanges.redo.splice(0,t.arrLayersChanges.redo.length),t.arrLayersChanges.undo[t.arrLayersChanges.undo.length-1].amount++,t.set_save_needed(!0),i=!0);a=s}}),i}function _len(e){var t=0;return jQuery.each(e,function(e){t++}),t}function forceShowHideLayer(e,t){void 0!==e&&!1!==e&&("hide"==t?(e.references.htmlLayer.addClass("layer-deleted"),null!=e.references.sorttable&&(e.references.sorttable.layer.addClass("layer-deleted"),e.references.sorttable.timeline.addClass("layer-deleted"),e.references.quicklayer.addClass("layer-deleted"))):(e.references.htmlLayer.removeClass("layer-deleted"),null!=e.references.sorttable&&(e.references.sorttable.layer.removeClass("layer-deleted"),e.references.sorttable.timeline.removeClass("layer-deleted"),e.references.quicklayer.removeClass("layer-deleted"))),u.timeLineTableDimensionUpdate())}function showHideDeletedLayers(e){jQuery.each(t.arrLayers,function(e,t){t.layer_unavailable||!0===t.deleted?forceShowHideLayer(t,"hide"):forceShowHideLayer(t,"show")})}function checkInvisibleRedoItems(){jQuery.each(t.arrLayersChanges.redo,function(e,t){jQuery.each(t.backup,function(e,t){t.layer_unavailable&&forceShowHideLayer(t,"hide")})})}function checkChangedSources(e){function r(t,r){12!=t.id&&9!=t.id&&29!=t.id||jQuery.each(t[e],function(e,r){(12==t.id&&("svg"==r.type||"image"==r.type)||29==t.id&&("audio"==r.type||"video"==r.type)||9==t.id&&"text"==r.type)&&redrawLayerHtml(r.serial),29==t.id&&"audio"==r.type&&u.drawAudioMap(r)})}jQuery.each(t.arrLayersChanges.redo,function(e,t){r(t)}),jQuery.each(t.arrLayersChanges.undo,function(e,t){r(t)})}t.getSerialFromID=function(e){return null!=e&&e.replace("slide_layer_","").replace("demo_layer_","")},t.getSerialFromSortID=function(e){var t=e.replace("layer_sort_time_","");return t=(t=t.replace("layer_sort_","")).replace("layer_quicksort_","")},t.lockAllLayers=function(e){for(e in t.arrLayers)t.arrLayers.hasOwnProperty(e)&&u.lockLayer(e)},t.unlockAllLayers=function(e){for(e in t.arrLayers)t.arrLayers.hasOwnProperty(e)&&u.unlockLayer(e)},t.showAllLayers=function(e){for(var r in t.arrLayers)t.arrLayers.hasOwnProperty(r)&&e!==t.arrLayers[r].serial&&u.showLayer(t.arrLayers[r],!0)},t.hideAllLayers=function(e){for(var r in t.arrLayers)t.arrLayers.hasOwnProperty(r)&&e!==t.arrLayers[r].serial&&u.hideLayer(t.arrLayers[r],!0)},t.getHtmlLayerFromSerial=function(e,t){if(t||(t=!1),t)r=jQuery("#demo_layer_"+e);else var r=jQuery("#slide_layer_"+e);return null!==r&&0==r.length&&UniteAdminRev.showErrorMessage("Html Layer with serial: "+e+" not found!"),r},t.getLayerByUniqueId=function(e){for(var r in t.arrLayers)if(t.arrLayers.hasOwnProperty(r)&&t.arrLayers[r].unique_id==e)return t.getLayer(r);for(var r in t.arrLayersDemo)if(t.arrLayersDemo.hasOwnProperty(r)&&t.arrLayersDemo[r].unique_id==e)return t.getLayer(r);return!1},t.getLayerIdByUniqueId=function(e){for(var r in t.arrLayers)if(t.arrLayers.hasOwnProperty(r)&&t.arrLayers[r].unique_id==e)return r;for(var r in t.arrLayersDemo)if(t.arrLayersDemo.hasOwnProperty(r)&&t.arrLayersDemo[r].unique_id==e)return r;return!1},t.getUniqueIdByLayer=function(e){for(var r in t.arrLayers)if(t.arrLayers.hasOwnProperty(r)&&r==e)return t.arrLayers[r].unique_id;return!1},t.getLayer=function(e,r){if(r)var a=t.arrLayersDemo[e];else a=t.arrLayers[e];if(!a)a=t.arrLayersDemo[e];return!!a&&(a.frames.frame_0.speed=Number(a.frames.frame_0.speed),a.frames.frame_999.speed=Number(a.frames.frame_999.speed),a)},t.getCurrentLayer=function(){return-1!=t.selectedLayerSerial&&t.getLayer(t.selectedLayerSerial)},t.makeLayerHtml=function(e,r,a){a||(a=!1);var i="text";r.type&&(i=r.type);var o="z-index:"+(Number(r.order)+100)+";position:absolute;",s="",n="",l="";"auto"!==t.getVal(r,"max_width")&&(o+=" width: "+t.getVal(r,"max_width")+";"),"auto"!==t.getVal(r,"max_height")&&(o+=" height: "+t.getVal(r,"max_height")+";"),o+=" white-space: "+t.getVal(r,"whitespace")+";";var d="";void 0!==r.special_type&&"static"==r.special_type&&(d=" static_layer");var y="";void 0===r.type||"button"!=r.type&&"shape"!=r.type||(y=" "+r.internal_class);var _=r.groupLink;if(_="ldles_"+(_=void 0===_?0:_),"image"==i&&(o+="line-height:0;"),"column"==i&&(n='<div class="slide_layer_col_sizer">',l="</div>",s='<div class="column_background"></div>'),a){rev_adv_resp_sizes,null!=r.static_styles&&(o+=" font-size: "+t.getVal(r.static_styles,"font-size")+";",o+=" line-height: "+t.getVal(r.static_styles,"line-height")+";",o+=" font-weight: "+t.getVal(r.static_styles,"font-weight")+";",o+=" color: "+t.getVal(r.static_styles,"color")+";",o+=" letter-spacing: "+t.getVal(r.static_styles,"letter-spacing")+";");c='<div id="demo_layer_'+e+'" data-uniqueid="'+r.unique_id+'" data-serial="'+e+'"  data-type="'+i+'" style="'+o+'" class="invisible_demolayer demo_layer_type_'+i+" demo_layer demo_layer_"+curDemoSlideID+' slide_layer" >'+s+n+'<div style="" class="innerslide_layer tp-caption '+r.style+d+y+'" >'}else var c='<div id="slide_layer_'+e+'" data-uniqueid="'+r.unique_id+'" data-serial="'+e+'" data-type="'+i+'" style="'+o+'"               class="slide_layer_type_'+i+" slide_layer "+_+'">'+s+n+'<div style="" class="innerslide_layer tp-caption '+r.style+d+y+'" >';switch(i){case"image":c+='<img src="'+r.image_url+'" alt="'+r.alt+'" style=""/>';break;default:case"text":case"button":c+=r.text;break;case"group":c+='<div class="inlaydecor"></div><div class="groupinfo"><i class="fa-icon-object-group"></i><span class="group_info_text">Edit Group Layers</span></div><div class="tp_layer_group_inner_wrapper"></div>';break;case"column":c+='<div class="tp_layer_group_inner_wrapper"></div>';break;case"row":c+='<div class="tp_layer_group_inner_wrapper"></div>',a||(c+='<div class="row_toolbar"><div class="row_editor"><i class="eg-icon-menu"></i><i class="eg-icon-cancel"></i></div><div class="row_moveme"><i class="eg-icon-arrow-combo"></i></div><div class="row_config"><i class="eg-icon-droplet"></i></div></div>');break;case"video":var m="width:100%;height:100%";if(void 0!==r.video_data)var p=""!=jQuery.trim(r.video_data.previewimage)?r.video_data.previewimage:r.video_image_url;else p=r.video_image_url;var v=r.video_type;switch(r.video_type){case"youtube":case"vimeo":m+=";background-image:url("+p+");";break;case"html5":void 0!==p&&""!=p&&(m+=";background-image:url("+p+");");break;case"streamyoutube":v="youtube";break;case"streamvimeo":v="vimeo";break;case"streaminstagram":v="html5"}c+="<div class='slide_layer_video' style='"+m+"'><div class='video-layer-inner video-icon-"+v+"'>",c+="<div class='layer-video-title'>"+r.video_title+"</div>",c+="</div></div>";break;case"audio":c+="<div class='slide_layer_audio' style=''><div class='video-layer-inner video-icon-'>",c+='<div class="slide_layer_audio_c_wrapper">',c+='<audio controls src="'+r.video_data.urlAudio+'"></audio>',c+='<div class="tp-video-controls"><div class="tp-video-button-wrap"><button type="button" class="tp-video-button tp-vid-play-pause">Play</button></div><div class="tp-video-seek-bar-wrap"><input  type="range" class="tp-seek-bar" value="0"></div><div class="tp-video-button-wrap"><button  type="button" class="tp-video-button tp-vid-mute">Mute</button></div><div class="tp-video-vol-bar-wrap"><input  type="range" class="tp-volume-bar" min="0" max="1" step="0.1" value="1"></div></div>',c+='<div class="slide_layer_audio_c_cover"></div>',c+="</div>",c+="<div class='layer-audio-title'><i class='rs-icon-layeraudio_n'></i>"+r.audio_title+"</div>",c+="</div></div>";break;case"svg":null!=r.svg&&null!=r.svg.src&&jQuery.get(r.svg.src,function(t){var r=a?"#demo_layer_":"#slide_layer_";jQuery(r+e+" .innerslide_layer.tp-caption").first().html(""),jQuery(r+e+" .innerslide_layer.tp-caption").first()[0].innerHTML=(new XMLSerializer).serializeToString(t.documentElement),u.rebuildLayerIdle(jQuery(r+e))})}return c+="</div>"+l,c+="<div class='icon_cross'></div>",c+="</div>"},t.reset_to_default_static_styles=function(e,r,a){if(void 0!==e.style){var i=UniteCssEditorRev.getStyleSettingsByHandle(e.style);!1!==i&&(void 0!==i.params["font-size"]&&(void 0!==r&&void 0!==a?-1!==jQuery.inArray("font-size",r)&&(e.static_styles=t.setVal(e.static_styles,"font-size",i.params["font-size"],!1,a),-1!==jQuery.inArray(layout,a)&&jQuery("#layer_font_size_s").val(i.params["font-size"])):jQuery("#layer_font_size_s").val(i.params["font-size"])),void 0!==i.params["line-height"]&&(void 0!==r&&void 0!==a?-1!==jQuery.inArray("line-height",r)&&(e.static_styles=t.setVal(e.static_styles,"line-height",i.params["line-height"],!1,a),-1!==jQuery.inArray(layout,a)&&jQuery("#layer_line_height_s").val(i.params["line-height"])):jQuery("#layer_line_height_s").val(i.params["line-height"])),void 0!==i.params["letter-spacing"]&&(void 0!==r&&void 0!==a?-1!==jQuery.inArray("letter-spacing",r)&&(e.static_styles=t.setVal(e.static_styles,"letter-spacing",i.params["letter-spacing"],!1,a),-1!==jQuery.inArray(layout,a)&&jQuery("#letter_spacing_s").val(i.params["letter-spacing"])):jQuery("#letter_spacing_s").val(i.params["letter-spacing"])),void 0!==i.params["font-weight"]&&(void 0!==r&&void 0!==a?-1!==jQuery.inArray("font-weight",r)&&(e.static_styles=t.setVal(e.static_styles,"font-weight",i.params["font-weight"],!1,a),-1!==jQuery.inArray(layout,a)&&jQuery('#layer_font_weight_s option[value="'+i.params["font-weight"]+'"]').attr("selected",!0)):jQuery('#layer_font_weight_s option[value="'+i.params["font-weight"]+'"]').attr("selected",!0)),void 0!==i.params.color&&(void 0!==r&&void 0!==a?(-1!==jQuery.inArray("color",r)&&(e.static_styles=t.setVal(e.static_styles,"color",i.params.color,!1,a)),-1!==jQuery.inArray(layout,a)&&(jQuery("#layer_color_s").val(i.params.color).tpColorPicker("refresh"),jQuery(".wp-color-result").each(function(){jQuery(this).css("backgroundColor",jQuery(this).parent().find(".my-color-field").val())}))):(jQuery("#layer_color_s").val(i.params.color).tpColorPicker("refresh"),jQuery(".wp-color-result").each(function(){jQuery(this).css("backgroundColor",jQuery(this).parent().find(".my-color-field").val())}))))}},t.updateLayer=function(e,r,a,i){var o=t.getLayer(e);if(!o)return!1;var s=!1;if(void 0!==r.style&&r.style!==o.style&&"none"==jQuery("#dialog-change-style-from-css").css("display")&&(s=!0),void 0!==a)for(var n in a)a.hasOwnProperty(n)&&delete o[a[n]];for(var n in r)if(r.hasOwnProperty(n))if("object"==typeof r[n]){for(var l in r[n])if(r[n].hasOwnProperty(l))if("object"==typeof o[n])if("object"==typeof o[n][l])for(var d in r[n][l])r[n][l].hasOwnProperty(d)&&(o[n][l][d]=r[n][l][d]);else o[n][l]=r[n][l];else o[n]={},o[n][l]=r[n][l]}else o[n]=r[n];if(s&&(t.reset_to_default_static_styles(o),updateSubStyleParameters(o)),!t.arrLayers[e])return UniteAdminRev.showErrorMessage("setLayer error, Layer with ID: "+e+" not found"),!1;i||update_layer_changes&&t.add_layer_change(e,r),t.arrLayers[e]=jQuery.extend({},o),t.updateReverseList()},t.cloneArrLayers=function(){t.arrLayersClone=UniteAdminRev.duplicateObject(t.arrLayers)},t.add_layer_change=function(){if(!t.ignorAllUndoRedoLogs){if(t.arrLayersChanges.undo.length-1>27)t.arrLayersChanges.undo.splice(0,1);objRek(t.arrLayersClone,t.arrLayers,""),0==t.arrLayersChanges.redo.length&&t.arrLayersChanges.undo.length>0&&(t.arrLayersChanges.undo[t.arrLayersChanges.undo.length-1].restore=UniteAdminRev.duplicateObject(t.arrLayers)),t.cloneArrLayers(),t.update_undo_redo_list()}},t.oneStepUndo=function(){t.arrLayersChanges.undo.length-1>=0&&t.undo_redo_layer("undo",t.arrLayersChanges.undo.length-1)},t.oneStepRedo=function(){t.arrLayersChanges.redo.length-1>=0&&t.undo_redo_layer("redo",0)},t.update_undo_redo_list=function(){0==jQuery("#rs-undo-list").length&&(jQuery("#layer-settings-toolbar-bottom").append('<div id="rs-undo-list"><div id="undo-redo-wrapper"></div></div>'),jQuery("body").on("click",".undostep",function(){t.undo_redo_layer("undo",jQuery(this).data("undoindex"))}),jQuery("body").on("click",".redostep",function(){t.undo_redo_layer("redo",jQuery(this).data("redoindex"))}),jQuery("body").on("click","#undo-last-action",function(){t.arrLayersChanges.undo.length-1>=0&&t.undo_redo_layer("undo",t.arrLayersChanges.undo.length-1)}),jQuery("body").on("click","#showhide_undolist",function(){var e=jQuery("#rs-undo-list");e.hasClass("inactive")?(e.slideUp(200).removeClass("inactive"),jQuery("#showhide_undolist i").removeClass("eg-icon-down-open").addClass("eg-icon-menu"),setTimeout(function(){var e=jQuery("#undo-redo-wrapper");e.scrollTop(e.prop("scrollHeight")),e.perfectScrollbar("update")},250)):(e.slideDown(200).addClass("inactive"),jQuery("#showhide_undolist i").addClass("eg-icon-down-open").removeClass("eg-icon-menu"))}),jQuery("#undo-redo-wrapper").perfectScrollbar({wheelPropagation:!0,suppressScrollX:!0}));var e=jQuery("#undo-redo-wrapper");e.find(".undoredostep").remove(),jQuery.each(t.arrLayersChanges.undo,function(r,a){var i='<div data-undoindex="'+r+'" class="undoredostep undostep">',o=t.arrLayers[a.serial].alias;switch(t.arrLayers[a.serial].type){case"text":i+='<span class="layer-title-with-icon"><i class="rs-icon-layerfont_n"></i></span>';break;case"shape":i+='<span class="layer-title-with-icon"><i class="rs-icon-layershape_n"></i></span>';break;case"button":i+='<span class="layer-title-with-icon"><i class="rs-icon-layerbutton_n"></i></span>';break;case"image":i+='<span class="layer-title-with-icon"><i class="rs-icon-layerimage_n"></i></span>';break;case"video":i+='<span class="layer-title-with-icon"><i class="rs-icon-layervideo_n"></i></span>';break;case"audio":i+='<span class="layer-title-with-icon"><i class="rs-icon-layeraudio_n"></i></span>';break;case"svg":i+='<span class="layer-title-with-icon"><i class="rs-icon-layersvg_n"></i></span>';break;case"row":case"group":i+='<span class="layer-title-with-icon"><i class="fa-icon-object-group"></i></span>';break;case"column":i+='<span class="layer-title-with-icon"><i class="rs-icon-layercolumn_n"></i></span>'}var s="";a.amount>0&&(s=" ("+a.amount+")"),r==t.arrLayersChanges.undo.length-1&&jQuery("#quick-undo .single-undo-action").html('<span class="undo-name">'+o+'</span><span class="undo-action"><i class="eg-icon-'+a.icon+'"></i>'+a.undogroup+s+"</span>"),i+='<span class="undo-name">'+o+'</span><span class="undo-action"><i class="eg-icon-'+a.icon+'"></i>'+a.undogroup+s+'</span><i class="eg-icon-cw"></i>',i+="</div>",e.append(i)}),t.arrLayersChanges.undo.length-1<0&&jQuery("#quick-undo .single-undo-action").html('<span class="undo-name">'+jQuery(".single-undo-action").data("origtext")+"</span>"),jQuery.each(t.arrLayersChanges.redo,function(r,a){var i='<div data-redoindex="'+r+'" class="undoredostep redostep">',o=void 0===t.arrLayers[a.serial]?a.backup[a.serial].alias:t.arrLayers[a.serial].alias;switch(type=void 0===t.arrLayers[a.serial]?a.backup[a.serial].type:t.arrLayers[a.serial].type,type){case"text":i+='<span class="layer-title-with-icon"><i class="rs-icon-layerfont_n"></i></span>';break;case"shape":i+='<span class="layer-title-with-icon"><i class="rs-icon-layershape_n"></i></span>';break;case"button":i+='<span class="layer-title-with-icon"><i class="rs-icon-layerbutton_n"></i></span>';break;case"image":i+='<span class="layer-title-with-icon"><i class="rs-icon-layerimage_n"></i></span>';break;case"video":i+='<span class="layer-title-with-icon"><i class="rs-icon-layervideo_n"></i></span>';break;case"audio":i+='<span class="layer-title-with-icon"><i class="rs-icon-layeraudio_n"></i></span>';break;case"svg":i+='<span class="layer-title-with-icon"><i class="rs-icon-layersvg_n"></i></span>'}var s="";a.amount>0&&(s=" ("+a.amount+")"),i+='<span class="undo-name">'+o+'</span><span class="undo-action"><i class="eg-icon-'+a.icon+'"></i>'+a.undogroup+s+'</span><i class="eg-icon-cw"></i>',i+="</div>",e.append(i)}),e.data("steps")!=t.arrLayersChanges.redo.length+t.arrLayersChanges.undo.length&&(e.scrollTop(e.prop("scrollHeight")),e.data("steps",t.arrLayersChanges.redo.length+t.arrLayersChanges.undo.length)),e.perfectScrollbar("update")},t.undo_redo_layer=function(e,r){if(t.set_save_needed(!0),jQuery(".layer-on-timeline-selector").each(function(){this.checked=!1}),t.selectedLayers=[],"undo"==e){t.arrLayers=UniteAdminRev.duplicateObject(t.arrLayersChanges.undo[r].backup),showHideDeletedLayers();for(var a=t.arrLayersChanges.undo.length-r,i=0;i<a;i++)t.arrLayersChanges.redo.unshift(t.arrLayersChanges.undo.pop());checkInvisibleRedoItems(),checkChangedSources("backup")}else if("redo"==e){t.arrLayers=UniteAdminRev.duplicateObject(t.arrLayersChanges.redo[r].restore),showHideDeletedLayers();for(i=0;i<=r;i++)t.arrLayersChanges.undo.push(t.arrLayersChanges.redo.shift());checkChangedSources("restore")}t.recreateLayerReferences(),u.redrawSortbox(),unselectLayers(),t.cloneArrLayers(),updateHtmlLayersFromObject(),u.allLayerToIdle(),u.stopAllLayerAnimation(),u.organiseGroupsAndLayer(),setTimeout(function(){t.update_undo_redo_list(),u.updateAllLayerTimeline()},10)},t.recreateLayerReferences=function(){for(var e in t.arrLayers)if(t.arrLayers.hasOwnProperty(e)){(r=t.arrLayers[e]).references=void 0===r.references?{}:r.references,r.references.htmlLayer=jQuery(document.getElementById("slide_layer_"+r.serial)),r.references.sorttable.layer=jQuery(document.getElementById("layer_sort_"+r.serial)),r.references.sorttable.timeline=jQuery(document.getElementById("layer_sort_time_"+r.serial)),r.references.quicklayer=jQuery(document.getElementById("layer_quicksort_"+r.serial))}for(var e in t.arrLayersDemo)if(t.arrLayersDemo.hasOwnProperty(e)){var r;(r=t.arrLayersDemo[e]).references=void 0===r.references?{}:r.references,r.references.htmlLayer=jQuery(document.getElementById("demo_layer_"+r.serial)),r.references.sorttable.layer=jQuery(document.getElementById("demo_sort_"+r.serial)),r.references.sorttable.timeline=jQuery(document.getElementById("demo_sort_time_"+r.serial)),r.references.quicklayer=jQuery(document.getElementById("demo_quicksort_"+r.serial))}},t.updateReverseList=function(){clearTimeout(updateRevTimer),updateRevTimer=setTimeout(function(){RevSliderSettings.onoffStatus(jQuery("#layer_anim_xstart_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_ystart_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_xrotate_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_yrotate_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_zrotate_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_scale_xstart_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_scale_ystart_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_skew_xstart_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_skew_ystart_reverse")),RevSliderSettings.onoffStatus(jQuery("#mask_anim_xstart_reverse")),RevSliderSettings.onoffStatus(jQuery("#mask_anim_ystart_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_xend_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_yend_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_xrotate_end_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_yrotate_end_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_anim_zrotate_end_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_scale_xend_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_scale_yend_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_skew_xend_reverse")),RevSliderSettings.onoffStatus(jQuery("#layer_skew_yend_reverse")),RevSliderSettings.onoffStatus(jQuery("#mask_anim_xend_reverse")),RevSliderSettings.onoffStatus(jQuery("#mask_anim_yend_reverse"))},100)},t.updateCurrentLayer=function(e,r){if(!t.arrLayers[t.selectedLayerSerial])return UniteAdminRev.showErrorMessage("error! the layer with serial: "+t.selectedLayerSerial+" doesn't exists"),!1;t.updateLayer(t.selectedLayerSerial,e,r)};var addLayerImage=function(e,r){objLayer={style:"",text:"Image "+(id_counter+1),type:"image",image_url:e.imgurl},void 0!==e.imglib&&(objLayer.image_library=e.imglib,objLayer.image_librarysize={},objLayer.image_librarysize.width=e.imgwidth,objLayer.image_librarysize.height=e.imgheight),objLayer.scaleProportional=!0,objLayer=t.setVal(objLayer,"scaleX",e.imgwidth,!0),objLayer=t.setVal(objLayer,"scaleY",e.imgheight,!0),objLayer=t.setVal(objLayer,"originalWidth",e.imgwidth,!0),objLayer=t.setVal(objLayer,"originalHeight",e.imgheight,!0),void 0!==r&&(objLayer.special_type=r),objLayer.createdOnInit=!1,addLayer(objLayer)},getVideoObjLayer=function(e,t){var r={type:"video",style:"",video_type:e.video_type,video_data:e};switch(r.video_data.autoplayonlyfirsttime=!1,void 0!==t&&(r.video_width=e.video_width,r.video_height=e.video_height),"youtube"!=r.video_type&&"vimeo"!=r.video_type||(r.video_id=e.id,r.video_title=e.title,r.video_image_url=e.thumb_medium.url,r.video_args=e.args),r.video_type){case"youtube":r.text="Youtube: "+e.title;break;case"vimeo":r.text="Vimeo: "+e.title;break;case"streamyoutube":r.text="YouTube Stream",r.video_title=r.text,r.video_image_url="",""!=e.urlPoster&&(r.video_image_url=e.urlPoster);break;case"streamvimeo":r.text="Vimeo Stream",r.video_title=r.text,r.video_image_url="",""!=e.urlPoster&&(r.video_image_url=e.urlPoster);break;case"streaminstagram":r.text="Instagram Stream",r.video_title=r.text,r.video_image_url="",""!=e.urlPoster&&(r.video_image_url=e.urlPoster);break;case"html5":r.text="Html5 Video",r.video_title=r.text,r.video_image_url="",""!=e.urlPoster&&(r.video_image_url=e.urlPoster);break;case"audio":r.text="Audio Layer",r.audio_title=r.text,r.audio_image_url="",""!=e.urlPoster&&(r.audio_image_url=e.urlPoster),r.type="audio"}return r},addLayerVideo=function(e,r){var a=getVideoObjLayer(e,!0);if(void 0!==r&&(a.special_type=r),a.createdOnInit=!1,addLayer(a),void 0!==r&&"audio"==r){var i=t.getCurrentLayer();!1!==i&&u.drawAudioMap(i)}},addLayerText=function(e,r){var a={text:r=void 0===r?initText+(id_counter+1):r,type:"text"};void 0!==e&&(a.special_type=e),a.createdOnInit=!1,addLayer(a),setTimeout(function(){t.showHideContentEditor(!0),jQuery("#layer_text").data("new_content",!0),jQuery("#layer_text").focus()},50)},addLayerGroup=function(e){var r={type:"group",text:initGroupName+(id_counter+1),createdOnInit:!1,grouptype:"logical"};void 0!==e&&(r.special_type=e);addLayer(r);t.makeRowSortableDroppable()},addLayerRow=function(e){var r={type:"row",text:initRowName+(id_counter+1),createdOnInit:!1};void 0!==e&&(r.special_type=e);for(var a=addLayer(r),i=t.getLayer(a).unique_id,o=0;o<2;o++)addLayer({type:"column",text:initColumnName+(id_counter+1),ref_group:i,p_uid:i,column_size:"1/2",createdOnInit:!1});t.makeRowSortableDroppable(),t.setLayerSelected(a)};jQuery(document).on("addLayer",function(e,t){void 0!==t&&addLayer(t.objLayer,t.isInit,t.isDemo,t.skipScroller)});var addLayer=function(e,r,a,i){void 0===e.version&&(void 0!==e.endtime&&void 0!==e.endspeed&&jQuery.isNumeric(e.endtime)&&(e.endtime=e.endtime-e.endspeed),e.realEndTime&&"undefined"!=e.realEndTime&&null!=e.realEndTime&&(e.endtime=e.realEndTime,delete e.realEndTime,delete e.endTimeFinal,delete e.endSpeedFinal)),e.version=t.core+""+t.sub+t.subsub,void 0===e.frames&&(e.frames={}),r=r||!1,a=a||!1;var o=!1;null==e.subtype&&(e.subtype=""),null==e.specialsettings&&(e.specialsettings={}),null==e.order&&(e.order=id_counter),e.order=Number(e.order),0!=r||a||(unique_layer_id++,e.unique_id=unique_layer_id),void 0===e.unique_id&&(unique_layer_id++,e.unique_id=unique_layer_id),jQuery.inArray(e.unique_id,alluniqueids)>=0&&(e.unique_id=e.unique_id+Math.round(100*Math.random()+100),unique_layer_id=e.unique_id),unique_layer_id<e.unique_id&&(unique_layer_id=e.unique_id+1),a||alluniqueids.push(e.unique_id);var s="video"===e.type?initLeftVideo:initLeft,n="video"===e.type?initTopVideo:initTop;if(s=-1!==t.newlayercoord.x?t.newlayercoord.x:s,n=-1!==t.newlayercoord.y?t.newlayercoord.y:n,e=null==t.getVal(e,"left")?t.setVal(e,"left",s,!0):"object"!=typeof e.left?t.setVal(e,"left",e.left,!0):e,"video"==(e=null==t.getVal(e,"top")?t.setVal(e,"top",n,!0):"object"!=typeof e.top?t.setVal(e,"top",e.top,!0):e).type&&(e=checkUpdateFullwidthVideo(e)),t.newlayercoord.x=-1,t.newlayercoord.y=-1,e.isDemo=a,void 0!==e.layer_action&&void 0!==e.layer_action.layer_target)for(var l in e.layer_action.layer_target)e.layer_action.layer_target.hasOwnProperty(l)&&"self"==e.layer_action.layer_target[l]&&(e.layer_action.layer_target[l]=e.unique_id);if(e.internal_class=e.internal_class||"",e.hover=void 0!==e.hover&&e.hover,e.alias=void 0!==e.alias?e.alias:u.getSortboxText(e.text).toLowerCase(),e.layer_unavailable=!1,e.deleted=!1,e.createdOnInit=null!=e.createdOnInit?e.createdOnInit:1==r,e.layer_bg_position=void 0!==e.layer_bg_position?e.layer_bg_position:"center center",e.layer_bg_size=void 0!==e.layer_bg_size?e.layer_bg_size:"cover",e.layer_bg_repeat=void 0!==e.layer_bg_repeat?e.layer_bg_repeat:"no-repeat",e.loop_animation=void 0!==e.loop_animation?e.loop_animation:"none",e.loop_easing=void 0!==e.loop_easing?e.loop_easing:"linearEaseNone",e.loop_speed=null!=e.loop_speed?e.loop_speed:2,e.loop_startdeg=null!=e.loop_startdeg?e.loop_startdeg:-20,e.loop_enddeg=null!=e.loop_enddeg?e.loop_enddeg:20,e.loop_xorigin=null!=e.loop_xorigin?e.loop_xorigin:50,e.loop_yorigin=null!=e.loop_yorigin?e.loop_yorigin:50,e.loop_xstart=null!=e.loop_xstart?e.loop_xstart:0,e.loop_xend=null!=e.loop_xend?e.loop_xend:0,e.loop_ystart=null!=e.loop_ystart?e.loop_ystart:0,e.loop_yend=null!=e.loop_yend?e.loop_yend:0,e.loop_zoomstart=null!=e.loop_zoomstart?e.loop_zoomstart:1,e.loop_zoomend=null!=e.loop_zoomend?e.loop_zoomend:1,e.loop_angle=null!=e.loop_angle?e.loop_angle:0,e.loop_radius=null!=e.loop_radius?e.loop_radius:10,e.layer_blend_mode=null!=e.layer_blend_mode?e.layer_blend_mode:"normal",e.html_tag=void 0!==e.html_tag?e.html_tag:"div",e.parallax_layer_ddd_zlevel=void 0!==e.parallax_layer_ddd_zlevel?e.parallax_layer_ddd_zlevel:"front",e.mask_start=void 0!==e.mask_start&&e.mask_start,e.mask_end=void 0!==e.mask_end&&e.mask_end,"row"===e.type&&(e.column_break_at=void 0!==e.column_break_at?e.column_break_at:"mobile"),e.x_start_reverse=void 0!==e.x_start_reverse&&e.x_start_reverse,e.y_start_reverse=void 0!==e.y_start_reverse&&e.y_start_reverse,e.x_end_reverse=void 0!==e.x_end_reverse&&e.x_end_reverse,e.y_end_reverse=void 0!==e.y_end_reverse&&e.y_end_reverse,e.x_rotate_start_reverse=void 0!==e.x_rotate_start_reverse&&e.x_rotate_start_reverse,e.y_rotate_start_reverse=void 0!==e.y_rotate_start_reverse&&e.y_rotate_start_reverse,e.z_rotate_start_reverse=void 0!==e.z_rotate_start_reverse&&e.z_rotate_start_reverse,e.x_rotate_end_reverse=void 0!==e.x_rotate_end_reverse&&e.x_rotate_end_reverse,e.y_rotate_end_reverse=void 0!==e.y_rotate_end_reverse&&e.y_rotate_end_reverse,e.z_rotate_end_reverse=void 0!==e.z_rotate_end_reverse&&e.z_rotate_end_reverse,e.scale_x_start_reverse=void 0!==e.scale_x_start_reverse&&e.scale_x_start_reverse,e.scale_y_start_reverse=void 0!==e.scale_y_start_reverse&&e.scale_y_start_reverse,e.scale_x_end_reverse=void 0!==e.scale_x_end_reverse&&e.scale_x_end_reverse,e.scale_y_end_reverse=void 0!==e.scale_y_end_reverse&&e.scale_y_end_reverse,e.skew_x_start_reverse=void 0!==e.skew_x_start_reverse&&e.skew_x_start_reverse,e.skew_y_start_reverse=void 0!==e.skew_y_start_reverse&&e.skew_y_start_reverse,e.skew_x_end_reverse=void 0!==e.skew_x_end_reverse&&e.skew_x_end_reverse,e.skew_y_end_reverse=void 0!==e.skew_y_end_reverse&&e.skew_y_end_reverse,e.mask_x_start_reverse=void 0!==e.mask_x_start_reverse&&e.mask_x_start_reverse,e.mask_y_start_reverse=void 0!==e.mask_y_start_reverse&&e.mask_y_start_reverse,e.mask_x_end_reverse=void 0!==e.mask_x_end_reverse&&e.mask_x_end_reverse,e.mask_y_end_reverse=void 0!==e.mask_y_end_reverse&&e.mask_y_end_reverse,e.mask_x_start=void 0!==e.mask_x_start?e.mask_x_start:0,e.mask_y_start=void 0!==e.mask_y_start?e.mask_y_start:0,e.mask_speed_start=void 0!==e.mask_speed_start?e.mask_speed_start:"inherit",e.mask_ease_start=void 0!==e.mask_ease_start?e.mask_ease_start:"inherit",e.mask_x_end=void 0!==e.mask_x_end?e.mask_x_end:0,e.mask_y_end=void 0!==e.mask_y_end?e.mask_y_end:0,e.mask_speed_end=void 0!==e.mask_speed_end?e.mask_speed_end:"inherit",e.mask_ease_end=void 0!==e.mask_ease_end?e.mask_ease_end:"inherit",e.alt_option=void 0!==e.alt_option?e.alt_option:"media_library",e.alt=void 0!==e.alt?e.alt:"",e.layer_action=void 0!==e.layer_action?e.layer_action:{},e.layer_action.tooltip_event=void 0!==e.layer_action.tooltip_event?e.layer_action.tooltip_event:[],e.layer_action.action=void 0!==e.layer_action.action?e.layer_action.action:[],e.layer_action.image_link=void 0!==e.layer_action.image_link?e.layer_action.image_link:[],e.layer_action.link_open_in=void 0!==e.layer_action.link_open_in?e.layer_action.link_open_in:[],e.layer_action.link_follow=void 0!==e.layer_action.link_follow?e.layer_action.link_follow:[],e.layer_action.jump_to_slide=void 0!==e.layer_action.jump_to_slide?e.layer_action.jump_to_slide:[],e.layer_action.scrollunder_offset=void 0!==e.layer_action.scrollunder_offset?e.layer_action.scrollunder_offset:[],e.layer_action.actioncallback=void 0!==e.layer_action.actioncallback?e.layer_action.actioncallback:[],e.layer_action.layer_target=void 0!==e.layer_action.layer_target?e.layer_action.layer_target:[],e.layer_action.link_type=void 0!==e.layer_action.link_type?e.layer_action.link_type:[],e.layer_action.action_delay=void 0!==e.layer_action.action_delay?e.layer_action.action_delay:[],e.layer_action.toggle_layer_type=void 0!==e.layer_action.toggle_layer_type?e.layer_action.toggle_layer_type:[],e.layer_action.toggle_class=void 0!==e.layer_action.toggle_class?e.layer_action.toggle_class:[],e=null==t.getVal(e,"min_height")?t.setVal(e,"min_height","40",!0):"object"!=typeof e.min_height?t.setVal(e,"min_height",e.min_height,!0):e,e=null==t.getVal(e,"max_height")?t.setVal(e,"max_height","auto",!0):"object"!=typeof e.max_height?t.setVal(e,"max_height",e.max_height,!0):e,null==t.getVal(e,"max_width")?e=t.setVal(e,"max_width","auto",!0):"object"!=typeof e.max_width&&(e=t.setVal(e,"max_width",e.max_width,!0)),"group"===e.type&&"auto"==t.getVal(e,"max_height")&&(e=t.setVal(e,"max_width","200",!0),e=t.setVal(e,"max_height","200",!0)),"video"==e.type&&void 0===e.video_width&&void 0!==e.video_data.width&&(e.video_width=e.video_data.width),null==t.getVal(e,"video_width")?e=t.setVal(e,"video_width",480,!0):"object"!=typeof e.video_width&&(e=t.setVal(e,"video_width",e.video_width,!0)),"video"==e.type&&void 0===e.video_height&&void 0!==e.video_data.height&&(e.video_height=e.video_data.height),null==t.getVal(e,"video_height")?e=t.setVal(e,"video_height",360,!0):"object"!=typeof e.video_height&&(e=t.setVal(e,"video_height",e.video_height,!0)),e["2d_rotation"]=null==e["2d_rotation"]&&r?0:e["2d_rotation"],e["2d_origin_x"]=null!=e["2d_origin_x"]?e["2d_origin_x"]:50,e["2d_origin_y"]=null!=e["2d_origin_y"]?e["2d_origin_y"]:50,null==t.getVal(e,"whitespace")?e=t.setVal(e,"whitespace",jQuery("#layer_whitespace option:selected").val(),!0):"object"!=typeof e.whitespace&&(e=t.setVal(e,"whitespace",e.whitespace,!0)),null==t.getVal(e,"display")?e=t.setVal(e,"display",jQuery("#layer_display option:selected").val(),!0):"object"!=typeof e.display&&(e=t.setVal(e,"display",e.display,!0)),null==e.static_start&&(e.static_start=jQuery("#layer_static_start option:selected").val()),null==e.static_end&&(e.static_end="last"),null==t.getVal(e,"align_hor")?e=t.setVal(e,"align_hor","left",!0):"object"!=typeof e.align_hor&&(e=t.setVal(e,"align_hor",e.align_hor,!0)),null==t.getVal(e,"align_vert")?e=t.setVal(e,"align_vert","top",!0):"object"!=typeof e.align_vert&&(e=t.setVal(e,"align_vert",e.align_vert,!0)),e.hiddenunder=void 0!==e.hiddenunder&&e.hiddenunder,e.resizeme=void 0===e.resizeme||e.resizeme,e["seo-optimized"]=void 0!==e["seo-optimized"]&&e["seo-optimized"],e.link=void 0!==e.link?e.link:"",e.link_open_in=void 0!==e.link_open_in?e.link_open_in:"same",e.link_follow=void 0!==e.link_follow?e.link_follow:"follow",e.link_slide=void 0!==e.link_slide?e.link_slide:"nothing",e.scrollunder_offset=void 0!==e.scrollunder_offset?e.scrollunder_offset:"",e.style=void 0!==e.style?e.style:"",e["visible-desktop"]=void 0===e["visible-desktop"]||e["visible-desktop"],e["visible-notebook"]=void 0===e["visible-notebook"]||e["visible-notebook"],e["visible-tablet"]=void 0===e["visible-tablet"]||e["visible-tablet"],e["visible-mobile"]=void 0===e["visible-mobile"]||e["visible-mobile"],e["resize-full"]=void 0===e["resize-full"]||e["resize-full"],e.hiddenunder=void 0!==e.hiddenunder&&e.hiddenunder,e["show-on-hover"]=void 0!==e["show-on-hover"]&&e["show-on-hover"],e.basealign=void 0!==e.basealign?e.basealign:"grid",e.responsive_offset=void 0===e.responsive_offset||e.responsive_offset,e.style=jQuery.trim(e.style),0!=r||"text"!=e.type||e.style&&""!=e.style||(o=!0),e["lazy-load"]=void 0!==e["lazy-load"]?e["lazy-load"]:"auto",e["image-size"]=void 0!==e["image-size"]?e["image-size"]:"auto",e["css-position"]=void 0!==e["css-position"]?e["css-position"]:"relative",void 0!==e.time&&(e.frames.frame_0={time:Number(e.time),delay:0,split:e.split,split_direction:"forward",split_extratime:0,splitdelay:e.splitdelay,speed:e.speed,animation:e.animation,easing:e.easing},delete e.time,delete e.splitdelay,delete e.speed,delete e.split,delete e.easing,delete e.animation),void 0!==e.endtime&&(e.frames.frame_999={time:e.endtime,delay:0,split:e.endsplit,split_extratime:0,split_direction:"forward",splitdelay:e.endsplitdelay,speed:null!=e.endspeed?e.endspeed:initSpeed,animation:e.endanimation,easing:e.endeasing},delete e.endtime,delete e.endsplitdelay,delete e.endspeed,delete e.endeasing,delete e.endsplit,delete e.endanimation),void 0===e.frames.frame_0&&(e.frames.frame_0={time:0,delay:0,split:"none",splitdelay:10,split_extratime:0,speed:300,animation:"tp-fade",easing:"Power3.easeInOut"}),void 0===e.frames.frame_999&&(e.frames.frame_999={time:g_slideTime,delay:0,split:"none",splitdelay:10,split_extratime:0,speed:300,animation:"tp-fade",easing:"Power3.easeInOut"}),e.frames.frame_0.text_c=void 0!==e.frames.frame_0.text_c?e.frames.frame_0.text_c:"transparent",e.frames.frame_0.bg_c=void 0!==e.frames.frame_0.bg_c?e.frames.frame_0.bg_c:"transparent",e.frames.frame_0.use_text_c=void 0!==e.frames.frame_0.use_text_c&&e.frames.frame_0.use_text_c,e.frames.frame_0.use_bg_c=void 0!==e.frames.frame_0.use_bg_c&&e.frames.frame_0.use_bg_c,e.frames.frame_0.time=void 0===e.frames.frame_0.time?10:e.frames.frame_0.time,e.frames.frame_0.delay=void 0===e.frames.frame_0.delay?0:e.frames.frame_0.delay,e.frames.frame_0.split=void 0===e.frames.frame_0.split?"none":e.frames.frame_0.split,e.frames.frame_0.split_direction=void 0===e.frames.frame_0.split_direction?"forward":e.frames.frame_0.split_direction,e.frames.frame_0.splitdelay=void 0===e.frames.frame_0.splitdelay?10:e.frames.frame_0.splitdelay,e.frames.frame_0.split_extratime=void 0===e.frames.frame_0.split_extratime?0:e.frames.frame_0.split_extratime,e.frames.frame_0.speed=void 0===e.frames.frame_0.speed?300:e.frames.frame_0.speed,e.frames.frame_0.sfxcolor=void 0===e.frames.frame_0.sfxcolor?"#ffffff":e.frames.frame_0.sfxcolor,e.frames.frame_0.animation=void 0===e.frames.frame_0.animation?"tp-fade":e.frames.frame_0.animation,e.frames.frame_0.easing=void 0===e.frames.frame_0.easing?"Power3.easeInOut":e.frames.frame_0.easing,e.frames.frame_999.time=void 0===e.frames.frame_999.time?10:e.frames.frame_999.time,e.frames.frame_999.delay=void 0===e.frames.frame_999.delay?0:e.frames.frame_999.delay,e.frames.frame_999.split=void 0===e.frames.frame_999.split?"none":e.frames.frame_999.split,e.frames.frame_999.split_direction=void 0===e.frames.frame_999.split_direction?"forward":e.frames.frame_999.split_direction,e.frames.frame_999.splitdelay=void 0===e.frames.frame_999.splitdelay?10:e.frames.frame_999.splitdelay,e.frames.frame_999.split_extratime=void 0===e.frames.frame_999.split_extratime?0:e.frames.frame_999.split_extratime,e.frames.frame_999.speed=void 0===e.frames.frame_999.speed?300:e.frames.frame_999.speed,e.frames.frame_999.animation=void 0===e.frames.frame_999.animation?"tp-fade":e.frames.frame_999.animation,e.frames.frame_999.easing=void 0===e.frames.frame_999.easing?"Power3.easeInOut":e.frames.frame_999.easing,e.frames.frame_999.sfxcolor=void 0===e.frames.frame_999.sfxcolor?"#ffffff":e.frames.frame_999.sfxcolor,e.frames.frame_999.use_text_c=void 0!==e.frames.frame_0.use_text_c&&e.frames.frame_0.use_text_c,e.frames.frame_999.use_bg_c=void 0!==e.frames.frame_0.use_bg_c&&e.frames.frame_0.use_bg_c,e.frames.frame_999.text_c=void 0!==e.frames.frame_999.text_c?e.frames.frame_999.text_c:"transparent",e.frames.frame_999.bg_c=void 0!==e.frames.frame_999.bg_c?e.frames.frame_999.bg_c:"transparent",null==t.getVal(e,"width")?e=t.setVal(e,"width",-1,!0):"object"!=typeof e.width&&(e=t.setVal(e,"width",e.width,!0)),null==t.getVal(e,"height")?e=t.setVal(e,"height",-1,!0):"object"!=typeof e.height&&(e=t.setVal(e,"height",e.height,!0)),null==t.getVal(e,"cover_mode")&&(e=t.setVal(e,"cover_mode","custom",!0)),null==e.static_styles?(e.static_styles={},t.reset_to_default_static_styles(e),null==t.getVal(e.static_styles,"font-size")&&(e.static_styles=t.setVal(e.static_styles,"font-size",20,!0)),null==t.getVal(e.static_styles,"line-height")&&(e.static_styles=t.setVal(e.static_styles,"line-height",22,!0)),null==t.getVal(e.static_styles,"font-weight")&&(e.static_styles=t.setVal(e.static_styles,"font-weight",400,!0)),null==t.getVal(e.static_styles,"color")&&(e.static_styles=t.setVal(e.static_styles,"color","#ffffff",!0)),null==t.getVal(e.static_styles,"letter-spacing")&&(e.static_styles=t.setVal(e.static_styles,"letter-spacing",0,!0))):("object"!=typeof e.static_styles["font-size"]&&(e.static_styles=t.setVal(e.static_styles,"font-size",e.static_styles["font-size"],!0)),"object"!=typeof e.static_styles["line-height"]&&(e.static_styles=t.setVal(e.static_styles,"line-height",e.static_styles["line-height"],!0)),"object"!=typeof e.static_styles["font-weight"]&&(e.static_styles=t.setVal(e.static_styles,"font-weight",e.static_styles["font-weight"],!0)),"object"!=typeof e.static_styles.color&&(e.static_styles=t.setVal(e.static_styles,"color",e.static_styles.color,!0)),"object"!=typeof e.static_styles["letter-spacing"]&&(e.static_styles=t.setVal(e.static_styles,"letter-spacing",e.static_styles["letter-spacing"],!0))),void 0!==e.deformation){if(void 0!==e.deformation["color-transparency"]){for(var d in e.static_styles.color)void 0!==e.static_styles.color[d]&&e.static_styles.color[d].length>0&&(e.static_styles.color[d]=window.RevColor.convert(e.static_styles.color[d],100*e.deformation["color-transparency"]));delete e.deformation["color-transparency"]}void 0!==e.deformation["background-transparency"]&&void 0!==e.deformation["background-color"]&&e.deformation["background-color"].length>0&&(e.deformation["background-color"]=window.RevColor.convert(e.deformation["background-color"],100*e.deformation["background-transparency"]),delete e.deformation["background-transparency"]),void 0!==e.deformation["border-transparency"]&&void 0!==e.deformation["border-color"]&&e.deformation["border-color"].length>0&&(e.deformation["border-color"]=window.RevColor.convert(e.deformation["border-color"],100*e.deformation["border-transparency"]),delete e.deformation["border-transparency"])}if(void 0!==e["deformation-hover"]&&(void 0!==e["deformation-hover"]["color-transparency"]&&void 0!==e["deformation-hover"].color&&e["deformation-hover"].color.length>0&&(e["deformation-hover"].color=window.RevColor.convert(e["deformation-hover"].color,100*e["deformation-hover"]["color-transparency"]),delete e["deformation-hover"]["color-transparency"]),void 0!==e["deformation-hover"]["background-transparency"]&&void 0!==e["deformation-hover"]["background-color"]&&e["deformation-hover"]["background-color"].length>0&&(e["deformation-hover"]["background-color"]=window.RevColor.convert(e["deformation-hover"]["background-color"],100*e["deformation-hover"]["background-transparency"]),delete e["deformation-hover"]["background-transparency"]),void 0!==e["deformation-hover"]["border-transparency"]&&void 0!==e["deformation-hover"]["border-color"]&&e["deformation-hover"]["border-color"].length>0&&(e["deformation-hover"]["border-color"]=window.RevColor.convert(e["deformation-hover"]["border-color"],100*e["deformation-hover"]["border-transparency"]),delete e["deformation-hover"]["border-transparency"])),void 0!==e.svg&&(void 0!==e.svg["svgstroke-transparency"]&&void 0!==e.svg["svgstroke-color"]&&e.svg["svgstroke-color"].length>0&&(e.svg["svgstroke-color"]=window.RevColor.convert(e.svg["svgstroke-color"],100*e.svg["svgstroke-transparency"]),delete e.svg["svgstroke-transparency"]),void 0!==e.svg["svgstroke-hover-transparency"]&&void 0!==e.svg["svgstroke-hover-color"]&&e.svg["svgstroke-hover-color"].length>0&&(e.svg["svgstroke-hover-color"]=window.RevColor.convert(e.svg["svgstroke-hover-color"],100*e.svg["svgstroke-hover-transparency"]),delete e.svg["svgstroke-hover-transparency"])),null==e.margin){e.margin={};var y=[];jQuery('input[name="css_margin[]"]').each(function(){y.push(0)}),e=t.setVal(e,"margin",y,!0)}else"object"!=typeof e.margin&&(e=t.setVal(e,"margin",e.margin,!0));if(null==e.padding)if(void 0!==e.deformation&&void 0!==e.deformation.padding)e=t.setVal(e,"padding",e.deformation.padding,!0);else{e.padding={};var _=[];jQuery('input[name="css_padding[]"]').each(function(){_.push(0)}),e=t.setVal(e,"padding",_,!0)}else if("object"!==jQuery.type(e.padding)){var c=e.padding;delete e.padding,e=t.setVal(e,"padding",c,!0)}if(null==e["text-align"]?e=void 0!==e.deformation&&void 0!==e.deformation["text-align"]?t.setVal(e,"text-align",e.deformation["text-align"],!0):t.setVal(e,"text-align","inherit",!0):"object"!==jQuery.type(e["text-align"])&&(e=t.setVal(e,"text-align",e["text-align"],!0)),e=t.setVal(e,"top",Math.round(t.getVal(e,"top"))),null==(e=t.setVal(e,"left",Math.round(t.getVal(e,"left")))).x_start&&(e.x_start="inherit"),null==e.y_start&&(e.y_start="inherit"),null==e.z_start&&(e.z_start="inherit"),null==e.x_end&&(e.x_end="inherit"),null==e.y_end&&(e.y_end="inherit"),null==e.z_end&&(e.z_end="inherit"),null==e.opacity_start&&(e.opacity_start=0==r?"0":"inherit"),null==e.opacity_end&&(e.opacity_end=0==r?"0":"inherit"),null==e.blurfilter_start&&(e.blurfilter_start="0"),null==e.blurfilter_end&&(e.blurfilter_end="0"),null==e.grayscalefilter_start&&(e.grayscalefilter_start="0"),null==e.grayscalefilter_end&&(e.grayscalefilter_end="0"),null==e.brightnessfilter_start&&(e.brightnessfilter_start="100"),null==e.brightnessfilter_end&&(e.brightnessfilter_end="100"),null==e.x_rotate_start&&(e.x_rotate_start="inherit"),null==e.y_rotate_start&&(e.y_rotate_start="inherit"),null==e.z_rotate_start&&(e.z_rotate_start="inherit"),null==e.x_rotate_end&&(e.x_rotate_end="inherit"),null==e.y_rotate_end&&(e.y_rotate_end="inherit"),null==e.z_rotate_end&&(e.z_rotate_end="inherit"),null==e.scale_x_start&&(e.scale_x_start="inherit"),null==e.scale_y_start&&(e.scale_y_start="inherit"),null==e.scale_x_end&&(e.scale_x_end="inherit"),null==e.scale_y_end&&(e.scale_y_end="inherit"),null==e.skew_x_start&&(e.skew_x_start="inherit"),null==e.skew_y_start&&(e.skew_y_start="inherit"),null==e.skew_x_end&&(e.skew_x_end="inherit"),null==e.skew_y_end&&(e.skew_y_end="inherit"),null==e.pers_start&&(e.pers_start="inherit"),null==e.pers_end&&(e.pers_end="inherit"),(null==e.deformation||jQuery.isEmptyObject(e.deformation))&&(e.deformation={}),null!=e.deformation["font-family"]||"text"!=e.type&&"button"!=e.type?""!==e.deformation["font-family"]&&-1==sgfamilies.indexOf(e.deformation["font-family"])&&sgfamilies.push(e.deformation["font-family"]):(e.deformation["font-family"]="Open Sans",sgfamilies.push("Open Sans")),null==e.deformation["font-style"]&&(e.deformation["font-style"]="normal"),null==e.deformation["text-decoration"]&&(e.deformation["text-decoration"]="none"),null==e.deformation["vertical-align"]&&(e.deformation["vertical-align"]="top"),null==e.deformation["text-transform"]&&(e.deformation["text-transform"]="none"),null==e.deformation["background-color"]&&(e.deformation["background-color"]="transparent"),null==e.deformation["border-color"]&&(e.deformation["border-color"]="transparent"),null==e.deformation["border-style"]&&(e.deformation["border-style"]="none"),null==e.deformation["border-width"]&&(e.deformation["border-width"]="0"),null==e.deformation["border-width"]){var m=[];jQuery('input[name="css_border-width[]"]').each(function(){m.push(0)}),e.deformation["border-width"]=m}else jQuery.isArray(e.deformation["border-width"])||(e.deformation["border-width"]=[e.deformation["border-width"],e.deformation["border-width"],e.deformation["border-width"],e.deformation["border-width"]]);if(null==e.deformation["border-radius"]){m=[];jQuery('input[name="css_border-radius[]"]').each(function(){m.push(0)}),e.deformation["border-radius"]=m}if(null==e.svg&&(e.svg={}),null==e.svg["svgstroke-color"]&&(e.svg["svgstroke-color"]="transparent"),null==e.svg["svgstroke-dasharray"]&&(e.svg["svgstroke-dasharray"]="0"),null==e.svg["svgstroke-dashoffset"]&&(e.svg["svgstroke-dashoffset"]="0"),null==e.svg["svgstroke-width"]&&(e.svg["svgstroke-width"]="0"),null==e.svg["svgstroke-hover-color"]&&(e.svg["svgstroke-hover-color"]="transparent"),null==e.svg["svgstroke-hover-transparency"]&&(e.svg["svgstroke-hover-transparency"]=1),null==e.svg["svgstroke-hover-dasharray"]&&(e.svg["svgstroke-hover-dasharray"]="0"),null==e.svg["svgstroke-hover-dashoffset"]&&(e.svg["svgstroke-hover-dashoffset"]="0"),null==e.svg["svgstroke-hover-width"]&&(e.svg["svgstroke-hover-width"]="0"),null==e.deformation.x&&(e.deformation.x=0),null==e.deformation.y&&(e.deformation.y=0),null==e.deformation.z&&(e.deformation.z=0),null==e.deformation.skewx&&(e.deformation.skewx=0),null==e.deformation.skewy&&(e.deformation.skewy=0),null==e.deformation.scalex&&(e.deformation.scalex=1),null==e.deformation.scaley&&(e.deformation.scaley=1),null==e.deformation.opacity&&(e.deformation.opacity=1),null==e.deformation.xrotate&&(e.deformation.xrotate=0),null==e.deformation.yrotate&&(e.deformation.yrotate=0),null==e["2d_rotation"]&&(e["2d_rotation"]=0),null==e.deformation["2d_origin_x"]&&(e.deformation["2d_origin_x"]=50),null==e.deformation["2d_origin_y"]&&(e.deformation["2d_origin_y"]=50),null==e.deformation.pers&&(e.deformation.pers=600),null==e.deformation.corner_left&&(e.deformation.corner_left="nothing"),null==e.deformation.corner_right&&(e.deformation.corner_right="nothing"),null==e.deformation.parallax&&(e.deformation.parallax="-"),null==e.deformation.blurfilter&&(e.deformation.blurfilter=0),null==e.deformation.grayscalefilter&&(e.deformation.grayscalefilter=0),null==e.deformation.brightnessfilter&&(e.deformation.brightnessfilter=100),(null==e["deformation-hover"]||jQuery.isEmptyObject(e["deformation-hover"]))&&(e["deformation-hover"]={}),null==e["deformation-hover"].blurfilter&&(e["deformation-hover"].blurfilter=0),null==e["deformation-hover"].grayscalefilter&&(e["deformation-hover"].grayscalefilter=0),null==e["deformation-hover"].brightnessfilter&&(e["deformation-hover"].brightnessfilter=100),null==e["deformation-hover"].color&&(e["deformation-hover"].color="#ffffff"),null==e["deformation-hover"]["text-decoration"]&&(e["deformation-hover"]["text-decoration"]="none"),null==e["deformation-hover"]["background-color"]&&(e["deformation-hover"]["background-color"]="transparent"),null==e["deformation-hover"]["border-color"]&&(e["deformation-hover"]["border-color"]="transparent"),null==e["deformation-hover"]["border-style"]&&(e["deformation-hover"]["border-style"]="none"),null==e["deformation-hover"]["border-width"]&&(e["deformation-hover"]["border-width"]=0),null==e["deformation-hover"]["border-width"]){m=[];jQuery('input[name="hover_css_border-width[]"]').each(function(){m.push(0)}),e["deformation-hover"]["border-width"]=m}else jQuery.isArray(e["deformation-hover"]["border-width"])||(e["deformation-hover"]["border-width"]=[e["deformation-hover"]["border-width"],e["deformation-hover"]["border-width"],e["deformation-hover"]["border-width"],e["deformation-hover"]["border-width"]]);if(null==e["deformation-hover"]["border-radius"]){m=[];jQuery('input[name="hover_css_border-radius[]"]').each(function(){m.push(0)}),e["deformation-hover"]["border-radius"]=m}null!=e.svg&&(null==e.svg["svgstroke-hover-color"]&&(e.svg["svgstroke-hover-color"]="transparent"),null==e.svg["svgstroke-hover-dasharray"]&&(e.svg["svgstroke-hover-dasharray"]="0"),null==e.svg["svgstroke-hover-dashoffset"]&&(e.svg["svgstroke-hover-dashoffset"]="0"),null==e.svg["svgstroke-hover-width"]&&(e.svg["svgstroke-hover-width"]="0")),null==e["deformation-hover"].x&&(e["deformation-hover"].x=0),null==e["deformation-hover"].y&&(e["deformation-hover"].y=0),null==e["deformation-hover"].z&&(e["deformation-hover"].z=0),null==e["deformation-hover"].skewx&&(e["deformation-hover"].skewx=0),null==e["deformation-hover"].skewy&&(e["deformation-hover"].skewy=0),null==e["deformation-hover"].scalex&&(e["deformation-hover"].scalex=1),null==e["deformation-hover"].scaley&&(e["deformation-hover"].scaley=1),null==e["deformation-hover"].opacity&&(e["deformation-hover"].opacity=1),null==e["deformation-hover"].xrotate&&(e["deformation-hover"].xrotate=0),null==e["deformation-hover"].yrotate&&(e["deformation-hover"].yrotate=0),null==e["deformation-hover"]["2d_rotation"]&&(e["deformation-hover"]["2d_rotation"]=0),null==e["deformation-hover"]["2d_origin_x"]&&(e["deformation-hover"]["2d_origin_x"]=50),null==e["deformation-hover"]["2d_origin_y"]&&(e["deformation-hover"]["2d_origin_y"]=50),null==e["deformation-hover"].speed&&(e["deformation-hover"].speed=0),null==e["deformation-hover"].zindex&&(e["deformation-hover"].zindex="auto"),null==e["deformation-hover"].easing&&(e["deformation-hover"].easing="Linear.easeNone"),null==e["deformation-hover"].css_cursor&&(e["deformation-hover"].css_cursor="auto"),null==e["deformation-hover"].pointer_events&&(e["deformation-hover"].pointer_events="auto"),null==e.visible&&(e.visible=!0),null==e.animation_overwrite&&(e.animation_overwrite="wait"),null==e.trigger_memory&&(e.trigger_memory="keep"),e.serial=id_counter,e.isDemo=a;var p=t.makeLayerHtml(id_counter,e,a);return container.append(p),e.references=void 0===e.references?{}:e.references,e.references.htmlLayer=t.getHtmlLayerFromSerial(id_counter,a),jQuery.each(t.addon_callbacks,function(t,r){var a=r.callback,i=r.environment,o=r.function_position;"add_layer_to_stage"===i&&"data_definition"==o&&(e=a(e))}),a?t.arrLayersDemo[id_counter]=jQuery.extend({},e):t.arrLayers[id_counter]=jQuery.extend({},e),a||(u.addToSortbox(id_counter,e),1!=i&&u.timeLineTableDimensionUpdate()),0!=e.visible||a||u.hideLayer(e),a||refreshEvents(id_counter),!1===e.createdOnInit&&(a?t.arrLayersDemo[id_counter].layer_unavailable=!0:t.arrLayers[id_counter].layer_unavailable=!0,t.cloneArrLayers(),a?t.arrLayersDemo[id_counter].layer_unavailable=!1:t.arrLayers[id_counter].layer_unavailable=!1,t.add_layer_change()),id_counter++,jQuery("#button_delete_all").removeClass("button-now-disabled"),u.rebuildLayerIdle(e.references.htmlLayer,0,a),0!=r||a||(t.setLayerSelected(e.serial),jQuery("#layer_text").focus()),o&&(t.reset_to_default_static_styles(e),updateSubStyleParameters(e,!0)),t.cloneArrLayers(),u.timeLineTableDimensionUpdate(),u.organiseGroupsAndLayer(!1,!1,!0),jQuery.each(t.addon_callbacks,function(t,r){var a=r.callback,i=r.environment,o=r.function_position;"add_layer_to_stage"===i&&"end"==o&&(e=a(e))}),e.serial};t.deleteLayer=function(e){var r=t.getLayer(e);r.deleted=!0,forceShowHideLayer(r,"hide"),t.add_layer_change(),u.timeLineTableDimensionUpdate()};var deleteCurrentLayer=function(){if(-1==t.selectedLayerSerial)return!1;t.deleteLayer(t.selectedLayerSerial),t.selectedLayerSerial=-1,disableFormFields()},duplicateLayerIntoNewGroup=function(e){return e.order=void 0,e.time=void 0,e.createdOnInit=!1,unique_layer_id++,e.unique_id=unique_layer_id,addLayer(e,!0),u.timeLineTableDimensionUpdate(),unique_layer_id},duplicateCurrentLayer=function(){if(-1==t.selectedLayerSerial)return!1;var e,r=t.arrLayers[t.selectedLayerSerial],a=UniteAdminRev.duplicateObject(r);a=t.setVal(a,"left",t.getVal(a,"left")+5),(a=t.setVal(a,"top",t.getVal(a,"top")+5)).order=void 0,a.time=void 0,a.createdOnInit=!1,unique_layer_id++,a.unique_id=unique_layer_id,void 0!==a.groupOrder&&"row"===a.type&&(a.groupOrder=t.getHighestGroupOrder(t.getVal(a,"align_vert"))),addLayer(a,!0),initDisallowCaptionsOnClick(),u.timeLineTableDimensionUpdate(),"group"===r.type&&t.getLayersInGroup(r.unique_id).layers.length>0&&(jQuery.each(t.getLayersInGroup(r.unique_id).layers,function(e,t){var r=UniteAdminRev.duplicateObject(t);r.p_uid=a.unique_id,duplicateLayerIntoNewGroup(r)}),t.makeRowSortableDroppable()),"row"===r.type&&t.getLayersInGroup(r.unique_id).layers.length>0&&(jQuery.each(t.getLayersInGroup(r.unique_id).columns,function(e,r){var i=UniteAdminRev.duplicateObject(r);i.p_uid=a.unique_id,i.ref_group=a.unique_id;var o=duplicateLayerIntoNewGroup(i);jQuery.each(t.getLayersInGroup(r.unique_id).layers,function(e,t){var r=UniteAdminRev.duplicateObject(t);r.p_uid=o,duplicateLayerIntoNewGroup(r)})}),t.makeRowSortableDroppable()),jQuery.each(t.getLayers(),function(t,r){e=t}),t.setLayerSelected(e),-1!==a.p_uid&&"group"!==t.getObjLayerType(a.p_uid)&&t.setInnerGroupOrders(a.references.htmlLayer.parent())};t.updateHtmlLayerCorners=function(e){htmlLayer=jQuery(e.references.htmlLayer[0].getElementsByClassName("innerslide_layer")[0]);var t=htmlLayer.outerHeight(),r=htmlLayer.css("backgroundColor"),a=UniteAdminRev.getTransparencyFromRgba(htmlLayer.css("backgroundColor"));if(a=!1===a?1:a,htmlLayer.find(".frontcorner").remove(),htmlLayer.find(".frontcornertop").remove(),htmlLayer.find(".backcorner").remove(),htmlLayer.find(".backcornertop").remove(),null==e||0==e)return!1;switch(e.deformation.corner_left){case"curved":htmlLayer.append("<div class='frontcorner'></div>");break;case"reverced":htmlLayer.append("<div class='frontcornertop'></div>")}switch(e.deformation.corner_right){case"curved":htmlLayer.append("<div class='backcorner'></div>");break;case"reverced":htmlLayer.append("<div class='backcornertop'></div>")}htmlLayer.find(".frontcorner").css({borderWidth:t+"px",left:0-t+"px",borderRight:"0px solid transparent",borderTopColor:r}),htmlLayer.find(".frontcornertop").css({borderWidth:t+"px",left:0-t+"px",borderRight:"0px solid transparent",borderBottomColor:r}),htmlLayer.find(".backcorner").css({borderWidth:t+"px",right:0-t+"px",borderLeft:"0px solid transparent",borderBottomColor:r}),htmlLayer.find(".backcornertop").css({borderWidth:t+"px",right:0-t+"px",borderLeft:"0px solid transparent",borderTopColor:r})};var getjQueryLayer=function(){return jQuery("#slide_layer_"+t.selectedLayerSerial)},updateLayerTextField=function(e,r,a){var i=getjQueryLayer();-1!=t.selectedLayerSerial&&i.length>0&&(i[0].getElementsByClassName("innerslide_layer")[0].innerHTML=a);var o=r.closest("li");a=u.getSortboxText(a),null!=o&&(o[0].getElementsByClassName("timer-layer-text")[0].innerHTML=a)};t.updateCrossIconPosition=function(e){var r=jQuery(e.references.htmlLayer[0].getElementsByClassName("icon_cross")[0]),a=r.width(),i=r.height(),o=e.references.htmlLayer.outerWidth(),s=e.references.htmlLayer.outerHeight(),n=Math.round(a/2),l=Math.round(i/2),d=0,y=0;switch(t.getVal(e,"align_hor")){case"left":d=-n;break;case"center":d=(o-a)/2;break;case"right":d=o-n}switch(t.getVal(e,"align_vert")){case"top":y=-l;break;case"middle":y=(s-i)/2;break;case"bottom":y=s-l}r.css({left:d+"px",top:y+"px"})};var checkUpdateFullwidthVideo=function(e){return"video"!=e.type?e:(void 0!==e.video_data&&e.video_data&&e.video_data.fullwidth&&1==e.video_data.fullwidth&&(e=t.setVal(e,"top",0),e=t.setVal(e,"left",0),e=t.setVal(e,"align_hor","left",!0),(e=t.setVal(e,"align_vert","top",!0)).video_width=container.width(),e.video_height=container.height()),e)},updateHtmlLayersFromObject=function(e,r,a){e||(e=t.selectedLayerSerial);var i=t.getLayer(e,a);if(!i)return!1;var o=i.references.htmlLayer,s="innerslide_layer tp-caption";switch(e==t.selectedLayerSerial&&(o.addClass("layer_selected"),o.closest(".slide_layer_type_row").addClass("layerchild_selected"),o.closest(".slide_layer_type_column").addClass("layerchild_selected")),s+=" "+i.style,i.type){case"button":case"shape":s+=" "+i.internal_class}switch(o[0].getElementsByClassName("innerslide_layer")[0].className=s,i.type||"text"){case"image":break;case"video":i=checkUpdateFullwidthVideo(i);break;case"row":case"group":case"column":break;default:case"text":case"button":o[0].getElementsByClassName("innerslide_layer")[0].innerHTML=i.text,t.makeCurrentLayerRotatable(e),t.updateHtmlLayerCorners(i);break;case"svg":i.svg.renderedData=o[0].getElementsByClassName("innerslide_layer")[0].innerHTML,t.makeCurrentLayerRotatable(e);break;case"audio":}u.rebuildLayerIdle(getjQueryLayer())};t.makeCurrentLayerRotatable=function(e){if(u.checkLoopTab()||u.checkAnimationTab())return t.removeCurrentLayerRotatable(),!1;var r=document.getElementsByClassName("slide_layer layer_selected")[0];if(null!=(r=null!=r?jQuery(r.getElementsByClassName("innerslide_layer")[0]):r)&&null!==r&&r.length>0){try{r.rotatable("destroy")}catch(e){}r.rotatable({angle:r.data("angle"),start:function(e,t){},rotate:function(e,t){jQuery("#layer_2d_rotation").val(getRotationDegrees(t.element)),t.element.data("angle",t.angle.current)},stop:function(e,r){t.updateLayerFromFields()}})}},t.removeCurrentLayerRotatable=function(){jQuery(".slide_layer .ui-rotatable-handle").each(function(){var e=jQuery(this);setTimeout(function(){try{e.parent().rotatable("destroy")}catch(e){}try{e.remove()}catch(e){}try{e.parent().find(".ui-rotatable-handle").remove()}catch(e){}},50)})};var positionChanged=function(e){jQuery("#layer_top, #layer_left").change(function(){setTimeout(function(){updateHtmlLayersFromObject(t.getSerialFromID(jQuery(".layer_selected").attr("id")),!0)},19)})},positionChanged_Core=function(){try{jQuery.each(t.arrLayers,function(e,r){try{t.updateHtmlLayerPosition(!1,r,t.getVal(r,"top"),t.getVal(r,"left"),t.getVal(r,"align_hor"),t.getVal(r,"align_vert"))}catch(e){console.log("Position Changed Core Internal Function Error:"),console.log(e)}})}catch(e){console.log("Position Changed Core Function Error:"),console.log(e)}};t.set_cover_mode=function(){var e=t.getLayer(t.selectedLayerSerial);switch(jQuery("#layer_scaleX").prop("disabled",!1),jQuery("#layer_scaleY").prop("disabled",!1),jQuery("#layer_max_width").prop("disabled",!1),jQuery("#layer_max_height").prop("disabled",!1),e.type){case"shape":case"image":switch(jQuery("#layer_cover_mode option:selected").val()){case"custom":break;case"fullwidth":jQuery("#layer_scaleX").attr("disabled","disabled"),jQuery("#layer_max_width").attr("disabled","disabled");break;case"fullheight":jQuery("#layer_scaleY").attr("disabled","disabled"),jQuery("#layer_max_height").attr("disabled","disabled");break;case"cover":case"cover-proportional":jQuery("#layer_scaleX").attr("disabled","disabled"),jQuery("#layer_scaleY").attr("disabled","disabled"),jQuery("#layer_max_width").attr("disabled","disabled"),jQuery("#layer_max_height").attr("disabled","disabled")}}},t.updateCurrentLayerPosition=function(){var e={};e=t.setVal(e,"top",Number(parseInt(jQuery("#layer_top").val(),0))),e=t.setVal(e,"left",Number(parseInt(jQuery("#layer_left").val(),0))),t.updateCurrentLayer(e),u.rebuildLayerIdle(jQuery(".slide_layer.layer_selected"))},t.updateLayerFromFields=function(){t.updateLayerFromFields_Core()},t.updateLayerFromFields_Core=function(){var e={};if(jQuery.each(t.addon_callbacks,function(t,r){var a=r.callback,i=r.environment,o=r.function_position;"updateLayerFromFields_Core"===i&&"start"==o&&(e=a(e))}),-1==t.selectedLayerSerial)return!1;UniteCssEditorRev.compare_to_original(),e.style=document.getElementById("layer_caption").value,e.hover=document.getElementById("hover_allow").checked,e.toggle=document.getElementById("toggle_allow").checked,e.toggle_use_hover=document.getElementById("toggle_use_hover").checked,e.toggle_inverse_content=document.getElementById("toggle_inverse_content").checked,e["visible-desktop"]=document.getElementById("visible-desktop").checked,e["visible-notebook"]=document.getElementById("visible-notebook").checked,e["visible-tablet"]=document.getElementById("visible-tablet").checked,e["visible-mobile"]=document.getElementById("visible-mobile").checked,e["show-on-hover"]=document.getElementById("layer_on_slider_hover").checked,e["css-position"]=jQuery("#layer-css-position option:selected").val(),e["lazy-load"]=jQuery("#layer-lazy-loading option:selected").val(),e["image-size"]=jQuery("#layer-image-size option:selected").val(),e.text=document.getElementById("layer_text").value,e.texttoggle=document.getElementById("layer_text_toggle").value||"",e.alias=jQuery("#layer_sort_"+t.selectedLayerSerial+">.layer_sort_inner_wrapper .timer-layer-text").val(),jQuery("#layer_text").data("new_content")&&jQuery("#layer_sort_"+t.selectedLayerSerial+">.layer_sort_inner_wrapper .timer-layer-text").val(e.text),jQuery("#layer_quicksort_"+t.selectedLayerSerial+" .layer-title-in-list").val(e.alias),jQuery("#the_current-editing-layer-title").val(e.alias),jQuery("#layer-short-toolbar").data("serial",t.selectedLayerSerial),e=t.setVal(e,"top",Number(parseInt(document.getElementById("layer_top").value,0))),e=t.setVal(e,"left",Number(parseInt(document.getElementById("layer_left").value,0))),e=t.setVal(e,"whitespace",jQuery("#layer_whitespace option:selected").val()),e=t.setVal(e,"display",jQuery("#layer_display option:selected").val()),e=t.setVal(e,"max_height",document.getElementById("layer_max_height").value),e=t.setVal(e,"min_height",document.getElementById("layer_min_height").value),e=t.setVal(e,"max_width",document.getElementById("layer_max_width").value),e=t.setVal(e,"video_height",document.getElementById("layer_video_height").value),e=t.setVal(e,"video_width",document.getElementById("layer_video_width").value),e=t.setVal(e,"scaleX",document.getElementById("layer_scaleX").value),e=t.setVal(e,"scaleY",document.getElementById("layer_scaleY").value),(e=t.setVal(e,"cover_mode",jQuery("#layer_cover_mode option:selected").val()))["2d_rotation"]=parseInt(document.getElementById("layer_2d_rotation").value,0),e["2d_origin_x"]=parseInt(document.getElementById("layer_2d_origin_x").value,0),e["2d_origin_y"]=parseInt(document.getElementById("layer_2d_origin_y").value,0),e.static_start=jQuery("#layer_static_start option:selected").val(),e.static_end=jQuery("#layer_static_end option:selected").val(),e.layer_bg_position=jQuery("#layer_bg_position option:selected").val(),e.layer_bg_size=jQuery("#layer_bg_size option:selected").val(),e.layer_bg_repeat=jQuery("#layer_bg_repeat option:selected").val(),e.loop_animation=jQuery("#layer_loop_animation option:selected").val(),e.loop_easing=document.getElementById("layer_loop_easing").value,e.loop_speed=document.getElementById("layer_loop_speed").value,e.loop_startdeg=parseInt(document.getElementById("layer_loop_startdeg").value,0),e.loop_enddeg=parseInt(document.getElementById("layer_loop_enddeg").value,0),e.loop_xorigin=parseInt(document.getElementById("layer_loop_xorigin").value,0),e.loop_yorigin=parseInt(document.getElementById("layer_loop_yorigin").value,0),e.loop_xstart=parseInt(document.getElementById("layer_loop_xstart").value,0),e.loop_xend=parseInt(document.getElementById("layer_loop_xend").value,0),e.loop_ystart=parseInt(document.getElementById("layer_loop_ystart").value,0),e.loop_yend=parseInt(document.getElementById("layer_loop_yend").value,0),e.loop_zoomstart=document.getElementById("layer_loop_zoomstart").value,e.loop_zoomend=document.getElementById("layer_loop_zoomend").value,e.loop_angle=document.getElementById("layer_loop_angle").value,e.loop_radius=document.getElementById("layer_loop_radius").value,e.html_tag=jQuery("#layer_html_tag option:selected").val(),e.parallax_layer_ddd_zlevel=jQuery("#parallax_layer_ddd_zlevel option:selected").val();var r=t.getHtmlLayerFromSerial(t.selectedLayerSerial);1!=document.getElementById("layer__scalex").value||1!=document.getElementById("layer__scaley").value||0!=parseInt(document.getElementById("layer__skewx").value,0)||0!=parseInt(document.getElementById("layer__skewy").value,0)||0!=parseInt(document.getElementById("layer__xrotate").value,0)||0!=parseInt(document.getElementById("layer__yrotate").value,0)||0!=parseInt(document.getElementById("layer_2d_rotation").value,0)?(jQuery(document.getElementsByClassName("mask-not-available")[0]).show(),jQuery(document.getElementsByClassName("mask-is-available")[0]).hide(),document.getElementById("masking-start").removeAttribute("checked"),document.getElementById("masking-end").removeAttribute("checked"),jQuery(document.getElementsByClassName("mask-start-settings")[0]).hide(),jQuery(document.getElementsByClassName("mask-end-settings")[0]).hide(),jQuery(".tp-showmask").removeClass("tp-showmask"),RevSliderSettings.onoffStatus(jQuery("#masking-start")),RevSliderSettings.onoffStatus(jQuery("#masking-end")),r.find(".tp-mask-wrap").css({overflow:"visible"})):(jQuery(document.getElementsByClassName("mask-not-available")[0]).hide(),jQuery(document.getElementsByClassName("mask-is-available")[0]).show()),e.mask_start=document.getElementById("masking-start").checked,e.mask_end=document.getElementById("masking-end").checked,e.mask_x_start=document.getElementById("mask_anim_xstart").value,e.mask_y_start=document.getElementById("mask_anim_ystart").value,e.mask_x_end=document.getElementById("mask_anim_xend").value,e.mask_y_end=document.getElementById("mask_anim_yend").value,e=t.setVal(e,"align_hor",document.getElementById("layer_align_hor").value),(e=t.setVal(e,"align_vert",document.getElementById("layer_align_vert").value)).hiddenunder=document.getElementById("layer_hidden").checked,e.resizeme=document.getElementById("layer_resizeme").checked,e["resize-full"]=document.getElementById("layer_resize-full").checked,e.basealign=document.getElementById("layer_align_base").value,e.responsive_offset=document.getElementById("layer_resp_offset").checked,e.frames={},e.frames.frame_0={},e.frames.frame_999={},e.frames.frame_0.use_text_c=document.getElementById("use_text_color_start").checked,e.frames.frame_0.use_bg_c=document.getElementById("use_bg_color_start").checked,e.frames.frame_0.text_c=document.getElementById("text_color_start").value,e.frames.frame_0.bg_c=document.getElementById("bg_color_start").value,e.frames.frame_0.animation=jQuery("#layer_animation option:selected").val(),e.frames.frame_0.speed=document.getElementById("layer_speed").value,e.frames.frame_0.easing=document.getElementById("layer_easing").value,e.frames.frame_0.sfx_effect=document.getElementById("sfx_in_effect").value,e.frames.frame_0.sfxcolor=document.getElementById("sfx_color_start").value,e.frames.frame_0.split=document.getElementById("layer_split").value,e.frames.frame_0.split_direction=document.getElementById("layer_split_direction").value,e.frames.frame_0.splitdelay=document.getElementById("layer_splitdelay").value,e.frames.frame_999.split=document.getElementById("layer_endsplit").value,e.frames.frame_999.split_direction=document.getElementById("layer_endsplit_direction").value,e.frames.frame_999.splitdelay=document.getElementById("layer_endsplitdelay").value,e.frames.frame_999.animation=document.getElementById("layer_endanimation").value,e.frames.frame_999.speed=document.getElementById("layer_endspeed").value,e.frames.frame_999.sfxcolor=document.getElementById("sfx_color_end").value,e.frames.frame_999.easing=document.getElementById("layer_endeasing").value,e.frames.frame_999.sfx_effect=document.getElementById("sfx_out_effect").value,e.frames.frame_999.use_text_c=document.getElementById("use_text_color_end").checked,e.frames.frame_999.use_bg_c=document.getElementById("use_bg_color_end").checked,e.frames.frame_999.text_c=document.getElementById("text_color_end").value,e.frames.frame_999.bg_c=document.getElementById("bg_color_end").value,e.alt_option=jQuery("#layer_alt_option option:selected").val(),e.alt=document.getElementById("layer_alt").value,e=t.setVal(e,"scaleX",jQuery("#layer_scaleX").val()),(e=t.setVal(e,"scaleY",jQuery("#layer_scaleY").val())).x_start=document.getElementById("layer_anim_xstart").value,e.y_start=document.getElementById("layer_anim_ystart").value,e.z_start=document.getElementById("layer_anim_zstart").value,e.x_end=document.getElementById("layer_anim_xend").value,e.y_end=document.getElementById("layer_anim_yend").value,e.z_end=document.getElementById("layer_anim_zend").value,e.opacity_start=document.getElementById("layer_opacity_start").value,e.opacity_end=document.getElementById("layer_opacity_end").value,e.blurfilter_start=document.getElementById("blurfilter_start").value,e.blurfilter_end=document.getElementById("blurfilter_end").value,e.grayscalefilter_start=document.getElementById("grayscalefilter_start").value,e.grayscalefilter_end=document.getElementById("grayscalefilter_end").value,e.brightnessfilter_start=document.getElementById("brightnessfilter_start").value,e.brightnessfilter_end=document.getElementById("brightnessfilter_end").value,e.x_rotate_start=document.getElementById("layer_anim_xrotate").value,e.y_rotate_start=document.getElementById("layer_anim_yrotate").value,e.z_rotate_start=document.getElementById("layer_anim_zrotate").value,e.x_rotate_end=document.getElementById("layer_anim_xrotate_end").value,e.y_rotate_end=document.getElementById("layer_anim_yrotate_end").value,e.z_rotate_end=document.getElementById("layer_anim_zrotate_end").value,e.scale_x_start=document.getElementById("layer_scale_xstart").value,e.scale_y_start=document.getElementById("layer_scale_ystart").value,e.scale_x_end=document.getElementById("layer_scale_xend").value,e.scale_y_end=document.getElementById("layer_scale_yend").value,e.skew_x_start=document.getElementById("layer_skew_xstart").value,e.skew_y_start=document.getElementById("layer_skew_ystart").value,e.skew_x_end=document.getElementById("layer_skew_xend").value,e.skew_y_end=document.getElementById("layer_skew_yend").value,e.x_start_reverse=document.getElementById("layer_anim_xstart_reverse").checked||!1,e.y_start_reverse=document.getElementById("layer_anim_ystart_reverse").checked||!1,e.x_end_reverse=document.getElementById("layer_anim_xend_reverse").checked||!1,e.y_end_reverse=document.getElementById("layer_anim_yend_reverse").checked||!1,e.x_rotate_start_reverse=document.getElementById("layer_anim_xrotate_reverse").checked||!1,e.y_rotate_start_reverse=document.getElementById("layer_anim_yrotate_reverse").checked||!1,e.z_rotate_start_reverse=document.getElementById("layer_anim_zrotate_reverse").checked||!1,e.x_rotate_end_reverse=document.getElementById("layer_anim_xrotate_end_reverse").checked||!1,e.y_rotate_end_reverse=document.getElementById("layer_anim_yrotate_end_reverse").checked||!1,e.z_rotate_end_reverse=document.getElementById("layer_anim_zrotate_end_reverse").checked||!1,e.scale_x_start_reverse=document.getElementById("layer_scale_xstart_reverse").checked||!1,e.scale_y_start_reverse=document.getElementById("layer_scale_ystart_reverse").checked||!1,e.scale_x_end_reverse=document.getElementById("layer_scale_xend_reverse").checked||!1,e.scale_y_end_reverse=document.getElementById("layer_scale_yend_reverse").checked||!1,e.skew_x_start_reverse=document.getElementById("layer_skew_xstart_reverse").checked||!1,e.skew_y_start_reverse=document.getElementById("layer_skew_ystart_reverse").checked||!1,e.skew_x_end_reverse=document.getElementById("layer_skew_xend_reverse").checked||!1,e.skew_y_end_reverse=document.getElementById("layer_skew_yend_reverse").checked||!1,e.mask_x_start_reverse=document.getElementById("mask_anim_xstart_reverse").checked||!1,e.mask_y_start_reverse=document.getElementById("mask_anim_ystart_reverse").checked||!1,e.mask_x_end_reverse=document.getElementById("mask_anim_xend_reverse").checked||!1,e.mask_y_end_reverse=document.getElementById("mask_anim_yend_reverse").checked||!1,e.autolinebreak=jQuery("#layer_auto_line_break")[0].checked,e.displaymode=jQuery("#layer_displaymode")[0].checked,e.scaleProportional=jQuery("#layer_proportional_scale")[0].checked,e.attrID=document.getElementById("layer_id").value,e.attrWrapperID=document.getElementById("layer_wrapper_id").value,e.attrClasses=document.getElementById("layer_classes").value,e.attrWrapperClasses=document.getElementById("layer_wrapper_classes").value,e.attrTitle=document.getElementById("layer_title").value,e.attrTabindex=document.getElementById("layer_tabindex").value,e.attrRel=document.getElementById("layer_rel").value,e=t.setVal(e,"scaleY",jQuery("#layer_scaleY").val()),jQuery("#clayer_start_speed").val(e.speed),jQuery("#clayer_end_speed").val(e.endspeed);var a="layer_color_s"==lastchangedinput,i="checked"==jQuery("#on_all_devices_color").attr("checked")||!1,o="layer_font_size_s"==lastchangedinput,s="checked"==jQuery("#on_all_devices_fontsize").attr("checked")||!1,n="layer_font_weight_s"==lastchangedinput,l="checked"==jQuery("#on_all_devices_fontweight").attr("checked")||!1,d="layer_line_height_s"==lastchangedinput,y="checked"==jQuery("#on_all_devices_lineheight").attr("checked")||!1,_="letter_spacing_s"==lastchangedinput,c="checked"==jQuery("#on_all_devices_letterspacing").attr("checked")||!1;null==e.static_styles&&(e.static_styles={}),e.static_styles=t.setVal(e.static_styles,"font-size",jQuery("#layer_font_size_s").val(),o,null,s),e.static_styles=t.setVal(e.static_styles,"line-height",jQuery("#layer_line_height_s").val(),d,null,y),e.static_styles=t.setVal(e.static_styles,"font-weight",jQuery("#layer_font_weight_s option:selected").val(),n,null,l),e.static_styles=t.setVal(e.static_styles,"color",jQuery("#layer_color_s").val(),a,null,i),e.static_styles=t.setVal(e.static_styles,"letter-spacing",jQuery("#letter_spacing_s").val(),_,null,c),null==e.deformation&&(e.deformation={}),null==e.deformation["border-radius"]&&(e.deformation["border-radius"]=["0","0","0","0"]),e["layer-selectable"]=jQuery("#css_layer_selectable option:selected").val(),e.deformation["font-style"]=jQuery("#css_font-style")[0].checked?"italic":"normal",e.deformation["font-family"]=document.getElementById("css_font-family").value;var m=[];jQuery('input[name="css_padding[]"]').each(function(e){m.push(jQuery(this).val())}),e=t.setVal(e,"padding",m,!0);var p=[];jQuery('input[name="css_margin[]"]').each(function(e){p.push(jQuery(this).val())}),e=t.setVal(e,"margin",p,!0),(e=t.setVal(e,"text-align",jQuery("#css_text-align option:selected").val())).deformation["text-decoration"]=jQuery("#css_text-decoration option:selected").val(),e.deformation.overflow=jQuery("#css_overflow option:selected").val(),e.column_break_at=jQuery("#column_break_at option:selected").val(),e.deformation["vertical-align"]=jQuery("#css_vertical-align option:selected").val(),e.deformation["text-transform"]=jQuery("#css_text-transform option:selected").val(),e.deformation["background-color"]=document.getElementById("css_background-color").value,e.deformation["border-color"]=document.getElementById("css_border-color-show").value,e.deformation["border-style"]=jQuery("#css_border-style option:selected").val(),null==e.deformation["border-width"]&&(e.deformation["border-width"]=new Array),jQuery('input[name="css_border-width[]"]').each(function(t){e.deformation["border-width"][t]=jQuery(this).val()}),null==e.deformation["border-radius"]&&(e.deformation["border-radius"]=new Array),jQuery('input[name="css_border-radius[]"]').each(function(t){e.deformation["border-radius"][t]=jQuery(this).val()}),e.layer_blend_mode=jQuery("#layer_blend_mode option:selected").val(),e.deformation.x=0,e.deformation.y=0,e.deformation.z=parseInt(document.getElementById("layer__z").value,0),e.deformation.skewx=document.getElementById("layer__skewx").value,e.deformation.skewy=document.getElementById("layer__skewy").value,e.deformation.scalex=document.getElementById("layer__scalex").value,e.deformation.scaley=document.getElementById("layer__scaley").value,e.deformation.opacity=document.getElementById("layer__opacity").value,e.deformation.xrotate=parseInt(document.getElementById("layer__xrotate").value,0),e.deformation.yrotate=parseInt(document.getElementById("layer__yrotate").value,0),e["2d_rotation"]=parseInt(document.getElementById("layer_2d_rotation").value,0),e.deformation["2d_origin_x"]=document.getElementById("layer_2d_origin_x").value,e.deformation["2d_origin_y"]=document.getElementById("layer_2d_origin_y").value,e.deformation.pers=document.getElementById("layer__pers").value,e.deformation.corner_left=jQuery("#layer_cornerleft option:selected").val(),e.deformation.corner_right=jQuery("#layer_cornerright option:selected").val(),e.deformation.parallax=jQuery("#parallax_level option:selected").val(),e.deformation.blurfilter=document.getElementById("blurfilter_idle").value,e.deformation.grayscalefilter=document.getElementById("grayscalefilter_idle").value,e.deformation.brightnessfilter=document.getElementById("brightnessfilter_idle").value,(null==e["deformation-hover"]||jQuery.isEmptyObject(e["deformation-hover"]))&&(e["deformation-hover"]={}),e["deformation-hover"].color=document.getElementById("hover_layer_color_s").value,e["deformation-hover"]["text-decoration"]=jQuery("#hover_css_text-decoration option:selected").val(),e["deformation-hover"]["background-color"]=document.getElementById("hover_css_background-color").value,e["deformation-hover"].blurfilter=document.getElementById("blurfilter_hover").value,e["deformation-hover"].grayscalefilter=document.getElementById("grayscalefilter_hover").value,e["deformation-hover"].brightnessfilter=document.getElementById("brightnessfilter_hover").value,e["deformation-hover"]["border-color"]=document.getElementById("hover_css_border-color-show").value,e["deformation-hover"]["border-style"]=jQuery("#hover_css_border-style option:selected").val(),null==e["deformation-hover"]["border-width"]&&(e["deformation-hover"]["border-width"]=new Array),jQuery('input[name="hover_css_border-width[]"]').each(function(t){e["deformation-hover"]["border-width"][t]=jQuery(this).val()}),null==e["deformation-hover"]["border-radius"]&&(e["deformation-hover"]["border-radius"]=new Array),jQuery('input[name="hover_css_border-radius[]"]').each(function(t){e["deformation-hover"]["border-radius"][t]=jQuery(this).val()}),e["deformation-hover"].skewx=document.getElementById("hover_layer__skewx").value,e["deformation-hover"].skewy=document.getElementById("hover_layer__skewy").value,e["deformation-hover"].scalex=document.getElementById("hover_layer__scalex").value,e["deformation-hover"].scaley=document.getElementById("hover_layer__scaley").value,e["deformation-hover"].opacity=document.getElementById("hover_layer__opacity").value,e["deformation-hover"].xrotate=parseInt(document.getElementById("hover_layer__xrotate").value,0),e["deformation-hover"].yrotate=parseInt(document.getElementById("hover_layer__yrotate").value,0),e["deformation-hover"]["2d_rotation"]=parseInt(document.getElementById("hover_layer_2d_rotation").value,0),e["deformation-hover"].speed=document.getElementById("hover_speed").value,e["deformation-hover"].zindex=document.getElementById("hover_zindex").value,e["deformation-hover"].pointer_events=document.getElementById("pointer_events").value,e["deformation-hover"].easing=jQuery("#hover_easing option:selected").val(),e["deformation-hover"].css_cursor=jQuery("#css_cursor option:selected").val(),e["deformation-hover"].pointer_events=jQuery("#pointer_events option:selected").val(),null==e.svg&&(e.svg={}),e.svg["svgstroke-color"]=document.getElementById("css_svgstroke-color-show").value,e.svg["svgstroke-dasharray"]=document.getElementById("css_svgstroke-dasharray").value,e.svg["svgstroke-dashoffset"]=document.getElementById("css_svgstroke-dashoffset").value,e.svg["svgstroke-width"]=document.getElementById("css_svgstroke-width").value,e.svg["svgstroke-hover-color"]=document.getElementById("css_svgstroke-hover-color-show").value,e.svg["svgstroke-hover-dasharray"]=document.getElementById("css_svgstroke-hover-dasharray").value,e.svg["svgstroke-hover-dashoffset"]=document.getElementById("css_svgstroke-hover-dashoffset").value,e.svg["svgstroke-hover-width"]=document.getElementById("css_svgstroke-hover-width").value,(null==e.layer_action||jQuery.isEmptyObject(e.layer_action))&&(e.layer_action={}),e.layer_action.tooltip_event=[],jQuery('select[name="layer_tooltip_event[]"] option:selected').each(function(){e.layer_action.tooltip_event.push(jQuery(this).val())}),e.layer_action.action=[],jQuery('select[name="layer_action[]"] option:selected').each(function(){e.layer_action.action.push(jQuery(this).val())}),e.layer_action.image_link=[],jQuery('input[name="layer_image_link[]"]').each(function(){e.layer_action.image_link.push(jQuery(this).val())}),e.layer_action.link_open_in=[],jQuery('select[name="layer_link_open_in[]"] option:selected').each(function(){e.layer_action.link_open_in.push(jQuery(this).val())}),e.layer_action.link_follow=[],jQuery('select[name="layer_link_follow[]"] option:selected').each(function(){e.layer_action.link_follow.push(jQuery(this).val())}),e.layer_action.jump_to_slide=[],jQuery('select[name="jump_to_slide[]"] option:selected').each(function(){e.layer_action.jump_to_slide.push(jQuery(this).val())}),e.layer_action.scrollunder_offset=[],jQuery('input[name="layer_scrolloffset[]"]').each(function(){e.layer_action.scrollunder_offset.push(jQuery(this).val())}),e.layer_action.action_easing=[],jQuery('select[name="layer-action-easing[]"] option:selected').each(function(){e.layer_action.action_easing.push(jQuery(this).val())}),e.layer_action.action_speed=[],jQuery('input[name="layer_action_speed[]"]').each(function(){e.layer_action.action_speed.push(jQuery(this).val())}),e.layer_action.actioncallback=[],jQuery('input[name="layer_actioncallback[]"]').each(function(){e.layer_action.actioncallback.push(jQuery(this).val())}),e.layer_action.layer_target=[],jQuery('select[name="layer_target[]"] option:selected').each(function(){e.layer_action.layer_target.push(jQuery(this).val())}),e.layer_action.link_type=[],jQuery('select[name="layer_link_type[]"] option:selected').each(function(){e.layer_action.link_type.push(jQuery(this).val())}),e.layer_action.action_delay=[],jQuery('input[name="layer_action_delay[]"]').each(function(){e.layer_action.action_delay.push(jQuery(this).val())}),e.layer_action.toggle_layer_type=[],jQuery('select[name="toggle_layer_type[]"] option:selected').each(function(){e.layer_action.toggle_layer_type.push(jQuery(this).val())}),e.layer_action.toggle_class=[],jQuery('input[name="layer_toggleclass[]"]').each(function(){e.layer_action.toggle_class.push(jQuery(this).val())}),e.animation_overwrite=jQuery("#layer-animation-overwrite option:selected").val(),e.trigger_memory=jQuery("#layer-tigger-memory option:selected").val(),jQuery.each(t.addon_callbacks,function(t,r){var a=r.callback,i=r.environment,o=r.function_position;"updateLayerFromFields_Core"===i&&"end"==o&&(e=a(e))}),t.updateCurrentLayer(e,["layer_action"]),t.showHideToggleContent(e),updateHtmlLayersFromObject(),updateHtmlSortboxFromObject(),initDisallowCaptionsOnClick(),u.rebuildLayerIdle(r),t.set_cover_mode()};var updateHtmlSortboxFromObject=function(e){e=null!=e?e:t.selectedLayerSerial;var r=t.getLayer(e),a=u.getHtmlSortItemFromSerial(e);if(!r||!a)return!1;var i=u.getSortboxText(r.alias);a.find(">.layer_sort_inner_wrapper .timer-layer-text").text(i)},redrawAllLayerHtml=function(){jQuery.each(t.arrLayers,function(e,t){redrawLayerHtml(t.serial)}),jQuery.each(t.arrLayersDemo,function(e,t){redrawLayerHtml(t.serial,!0)}),jQuery(".slide_layer").each(function(){u.rebuildLayerIdle(jQuery(this))}),t.updateLayerFormFields(t.selectedLayerSerial)},redrawLayerHtml=function(e,r){null==r&&(r=!1);var a=t.getLayer(e,r),i=t.makeLayerHtml(e,a,r);if("group"!=a.type&&"row"!=a.type&&"column"!=a.type){var o=jQuery(i).html();t.getHtmlLayerFromSerial(e,r);a.references.htmlLayer.html(o)}},nix=function(e){return void 0===e||0==e.length},updateSubStyleParameters=function(e,r){var a=void 0!==r&&!0===r,i=UniteCssEditorRev.getStyleSettingsByHandle(e.style),o=i.params,s=UniteCssEditorRev.getStyleSettingsByHandle(e.svg),n=i.hover;null==n&&(n={});var l=!1;try{l=void 0!==i&&(!!i.hasOwnProperty("settings")&&(void 0!==i.settings&&void 0!==typeof i.settings.hover&&i.settings.hover))}catch(e){}if(null!=e.deformation&&void 0!==o){if((nix(e.deformation["font-style"])||a)&&(e.deformation["font-style"]=nix(o["font-style"])?"normal":o["font-style"]),(nix(e.deformation["font-family"])||a)&&(e.deformation["font-family"]=nix(o["font-family"])?"Arial":o["font-family"]),nix(e.padding)||a)if(nix(o.padding))if(null==e.padding){e.padding={};var d=[];jQuery('input[name="css_padding[]"]').each(function(){d.push(0)}),e=t.setVal(e,"padding",d,!0)}else"object"!=typeof e.padding&&(e=t.setVal(e,"padding",e.padding,!0));else null!=e.padding&&delete e.padding,"object"==typeof o.padding&&null!==o.padding?e.padding=o.padding:e=t.setVal(e,"padding",o.padding.split(" "),!0);if(nix(e.margin)||a)if(nix(o.margin))if(null==e.margin){e.margin={};d=[];jQuery('input[name="css_margin[]"]').each(function(){d.push(0)}),e=t.setVal(e,"margin",d,!0)}else"object"!=typeof e.margin&&(e=t.setVal(e,"margin",e.margin,!0));else null!=e.margin&&delete e.margin,"object"==typeof o.margin&&null!==o.margin?e.margin=o.margin:e=t.setVal(e,"margin",o.margin.split(" "),!0);if(nix(e["text-align"])||a)if(nix(o["text-align"]))if(null==e["text-align"]){e["text-align"]={};d=[];jQuery('input[name="css_text-align[]"]').each(function(){d.push(0)}),e=t.setVal(e,"text-align",d,!0)}else"object"!=typeof e["text-align"]&&(e=t.setVal(e,"text-align",e["text-align"],!0));else null!=e["text-align"]&&delete e["text-align"],"object"==typeof o["text-align"]&&null!==o["text-align"]?e["text-align"]=o["text-align"]:e=t.setVal(e,"text-align",o["text-align"],!0);if((nix(e.deformation["text-decoration"])||a)&&(e.deformation["text-decoration"]=nix(o["text-decoration"])?"none":o["text-decoration"]),(nix(e.deformation["vertical-align"])||a)&&(e.deformation["vertical-align"]=nix(o["vertical-align"])?"top":o["vertical-align"]),(nix(e.deformation.display)||a)&&(e.deformation.display=nix(o.display)?"block":o.display),(nix(e.deformation["background-color"])||a)&&(e.deformation["background-color"]=nix(o["background-color"])?"transparent":o["background-color"]),(nix(e.deformation["border-color"])||a)&&(e.deformation["border-color"]=nix(o["border-color"])?"transparent":o["border-color"]),(nix(e.deformation["border-style"])||a)&&(e.deformation["border-style"]=nix(o["border-style"])?"none":o["border-style"]),nix(e.deformation["border-width"])||nix(e.deformation["border-width"][0])&&nix(e.deformation["border-width"][1])&&nix(e.deformation["border-width"][2])&&nix(e.deformation["border-width"][3])||a){var y=nix(o["border-width"])?["0px","0px","0px","0px"]:"object"!=typeof o["border-width"]?o["border-width"].split(" "):o["border-width"];e.deformation["border-width"]=["0","0","0","0"],jQuery(e.deformation["border-width"]).each(function(t){e.deformation["border-width"][t]=y.length<2?y[0]:y.length<4?0==t||2==t?y[0]:y[1]:y[t]})}if(nix(e.deformation["border-radius"])||nix(e.deformation["border-radius"][0])&&nix(e.deformation["border-radius"][1])&&nix(e.deformation["border-radius"][2])&&nix(e.deformation["border-radius"][3])||a){y=nix(o["border-radius"])?["0px","0px","0px","0px"]:"object"!=typeof o["border-radius"]?o["border-radius"].split(" "):o["border-radius"];e.deformation["border-radius"]=["0","0","0","0"],jQuery(e.deformation["border-radius"]).each(function(t){e.deformation["border-radius"][t]=y.length<2?y[0]:y.length<4?0==t||2==t?y[0]:y[1]:y[t]})}(nix(e.deformation.corner_left)||a)&&(e.deformation.corner_left=nix(o.corner_left)?"nothing":o.corner_left),(nix(e.deformation.corner_right)||a)&&(e.deformation.corner_right=nix(o.corner_right)?"nothing":o.corner_right),(nix(e.deformation.parallax)||a)&&(e.deformation.parallax=nix(o.parallax)?"-":o.parallax),(nix(e.deformation.x)||a)&&(e.deformation.x=nix(o.x)?0:o.x),(nix(e.deformation.y)||a)&&(e.deformation.y=nix(o.y)?0:o.y),(nix(e.deformation.z)||a)&&(e.deformation.z=nix(o.z)?0:o.z),(nix(e.deformation.skewx)||a)&&(e.deformation.skewx=nix(o.skewx)?0:o.skewx),(nix(e.deformation.skewy)||a)&&(e.deformation.skewy=nix(o.skewy)?0:o.skewy),(nix(e.deformation.scalex)||a)&&(e.deformation.scalex=nix(o.scalex)?1:o.scalex),(nix(e.deformation.scaley)||a)&&(e.deformation.scaley=nix(o.scaley)?1:o.scaley),(nix(e.deformation.opacity)||a)&&(e.deformation.opacity=nix(o.opacity)?1:o.opacity),(nix(e.deformation.xrotate)||a)&&(e.deformation.xrotate=nix(o.xrotate)?0:o.xrotate),(nix(e.deformation.yrotate)||a)&&(e.deformation.yrotate=nix(o.yrotate)?0:o.yrotate),(nix(e["2d_rotation"])||a)&&(e["2d_rotation"]=nix(o["2d_rotation"])?0:o["2d_rotation"]),(nix(e.deformation["2d_origin_x"])||a)&&(e.deformation["2d_origin_x"]=nix(o["2d_origin_x"])?50:o["2d_origin_x"]),(nix(e.deformation["2d_origin_y"])||a)&&(e.deformation["2d_origin_y"]=nix(o["2d_origin_y"])?50:o["2d_origin_y"]),(nix(e.deformation.pers)||a)&&(e.deformation.pers=nix(o.pers)?600:o.pers),(nix(e.deformation.blurfilter)||a)&&(e.deformation.blurfilter=nix(o.blurfilter)?0:o.blurfilter),(nix(e.deformation.grayscalefilter)||a)&&(e.deformation.grayscalefilter=nix(o.grayscalefilter)?0:o.grayscalefilter),(nix(e.deformation.brightnessfilter)||a)&&(e.deformation.brightnessfilter=nix(o.brightnessfilter)?100:o.brightnessfilter)}if(null!=e.svg&&null!=s&&((nix(e.svg["svgstroke-color"])||a)&&(e.svg["svgstroke-color"]=nix(s["svgstroke-color"])?"transparent":s["svgstroke-color"]),(nix(e.svg["svgstroke-dasharray"])||a)&&(e.svg["svgstroke-dasharray"]=nix(s["svgstroke-dasharray"])?"0":s["svgstroke-dasharray"]),(nix(e.svg["svgstroke-dashoffset"])||a)&&(e.svg["svgstroke-dashoffset"]=nix(s["svgstroke-dashoffset"])?"0":s["svgstroke-dashoffset"]),(nix(e.svg["svgstroke-width"])||a)&&(e.svg["svgstroke-width"]=nix(s["svgstroke-width"])?"0":s["svgstroke-width"]),(nix(e.svg["svgstroke-hover-color"])||a)&&(e.svg["svgstroke-hover-color"]=nix(s["svgstroke-hover-color"])?"transparent":s["svgstroke-hover-color"]),(nix(e.svg["svgstroke-hover-dasharray"])||a)&&(e.svg["svgstroke-hover-dasharray"]=nix(s["svgstroke-hover-dasharray"])?"0":s["svgstroke-hover-dasharray"]),(nix(e.svg["svgstroke-hover-dashoffset"])||a)&&(e.svg["svgstroke-hover-dashoffset"]=nix(s["svgstroke-hover-dashoffset"])?"0":s["svgstroke-hover-dashoffset"]),(nix(e.svg["svgstroke-hover-width"])||a)&&(e.svg["svgstroke-hover-width"]=nix(s["svgstroke-hover-width"])?"0":s["svgstroke-hover-width"])),a&&("true"===l||!0===l?(jQuery("#hover_allow").attr("checked",!0),jQuery("#idle-hover-swapper").show()):(jQuery("#hover_allow").attr("checked",!1),jQuery("#idle-hover-swapper").hide())),RevSliderSettings.onoffStatus(jQuery("#hover_allow")),RevSliderSettings.onoffStatus(jQuery("#toggle_allow")),RevSliderSettings.onoffStatus(jQuery("#toggle_use_hover")),RevSliderSettings.onoffStatus(jQuery("#toggle_inverse_content")),jQuery('#css_layer_selectable option[value="'+e["layer-selectable"]+'"]').attr("selected",!0),null!=e.deformation){jQuery("#css_font-family").val(e.deformation["font-family"]),void 0===(d=t.getVal(e,"padding"))&&(d=e.deformation.padding),void 0===d&&(d=[0,0,0,0]),jQuery('input[name="css_padding[]"]').each(function(e){jQuery(this).val(d[e])});var _=t.getVal(e,"margin");if(void 0===_&&(_=e.deformation.margin),void 0===_&&(_=[0,0,0,0]),jQuery('input[name="css_margin[]"]').each(function(e){jQuery(this).val(_[e])}),"italic"==e.deformation["font-style"]?jQuery("#css_font-style").attr("checked",!0):jQuery("#css_font-style").attr("checked",!1),RevSliderSettings.onoffStatus(jQuery("#css_font-style")),jQuery('#css_text-align option[value="'+t.getVal(e,"text-align")+'"]').attr("selected",!0),jQuery('#css_text-decoration option[value="'+e.deformation["text-decoration"]+'"]').attr("selected",!0),jQuery('#css_overflow option[value="'+e.deformation.overflow+'"]').attr("selected",!0),jQuery('#column_break_at option[value="'+e.column_break_at+'"]').attr("selected",!0),jQuery('#css_vertical-align option[value="'+e.deformation["vertical-align"]+'"]').attr("selected",!0),jQuery('#css_text-transform option[value="'+e.deformation["text-transform"]+'"]').attr("selected",!0),jQuery("#css_background-color").val(e.deformation["background-color"]).tpColorPicker("refresh"),jQuery("#css_border-color-show").val(e.deformation["border-color"]).tpColorPicker("refresh"),jQuery('#css_border-style option[value="'+e.deformation["border-style"]+'"]').attr("selected",!0),jQuery('input[name="css_border-width[]"]').each(function(t){jQuery(this).val(e.deformation["border-width"][t])}),jQuery('input[name="css_border-radius[]"]').each(function(t){jQuery(this).val(e.deformation["border-radius"][t])}),jQuery('input[name="layer__x"]').val(e.deformation.x),jQuery('input[name="layer__y"]').val(e.deformation.y),jQuery("#layer__z").val(e.deformation.z),jQuery("#layer__skewx").val(e.deformation.skewx),jQuery("#layer__skewy").val(e.deformation.skewy),jQuery("#layer__scalex").val(e.deformation.scalex),jQuery("#layer__scaley").val(e.deformation.scaley),jQuery("#layer__opacity").val(e.deformation.opacity),jQuery("#layer__xrotate").val(e.deformation.xrotate),jQuery("#layer__yrotate").val(e.deformation.yrotate),jQuery("#layer_2d_rotation").val(e["2d_rotation"]),jQuery("#layer_2d_origin_x").val(e.deformation["2d_origin_x"]),jQuery("#layer_2d_origin_y").val(e.deformation["2d_origin_y"]),jQuery("#layer__pers").val(e.deformation.pers),jQuery("#blurfilter_idle").val(e.deformation.blurfilter),jQuery("#grayscalefilter_idle").val(e.deformation.grayscalefilter),jQuery("#brightnessfilter_idle").val(e.deformation.brightnessfilter),jQuery("#blurfilter_hover").val(e["deformation-hover"].blurfilter),jQuery("#grayscalefilter_hover").val(e["deformation-hover"].grayscalefilter),jQuery("#brightnessfilter_hover").val(e["deformation-hover"].brightnessfilter),jQuery('#layer_cornerleft option[value="'+e.deformation.corner_left+'"]').attr("selected",!0),jQuery('#layer_blend_mode option[value="'+e.layer_blend_mode+'"]').attr("selected",!0),jQuery('#layer_cornerright option[value="'+e.deformation.corner_right+'"]').attr("selected",!0),jQuery('#parallax_level option[value="'+e.deformation.parallax+'"]').attr("selected",!0),(nix(e["deformation-hover"].color)||a)&&(e["deformation-hover"].color=nix(n.color)?"#000000":n.color),(nix(e["deformation-hover"]["text-decoration"])||a)&&(e["deformation-hover"]["text-decoration"]=nix(n["text-decoration"])?"none":n["text-decoration"]),(nix(e["deformation-hover"]["background-color"])||a)&&(e["deformation-hover"]["background-color"]=nix(n["background-color"])?"transparent":n["background-color"]),(nix(e["deformation-hover"]["border-color"])||a)&&(e["deformation-hover"]["border-color"]=nix(n["border-color"])?"transparent":n["border-color"]),(nix(e["deformation-hover"]["border-style"])||a)&&(e["deformation-hover"]["border-style"]=nix(n["border-style"])?"none":n["border-style"]),nix(e["deformation-hover"]["border-width"])||nix(e["deformation-hover"]["border-width"][0])&&nix(e["deformation-hover"]["border-width"][1])&&nix(e["deformation-hover"]["border-width"][2])&&nix(e["deformation-hover"]["border-width"][3])||a){y=nix(n["border-width"])?["0px","0px","0px","0px"]:"object"!=typeof n["border-width"]?n["border-width"].split(" "):n["border-width"];e["deformation-hover"]["border-width"]=["0","0","0","0"],jQuery(e["deformation-hover"]["border-width"]).each(function(t){e["deformation-hover"]["border-width"][t]=y.length<2?y[0]:y.length<4?0==t||2==t?y[0]:y[1]:y[t]})}if(nix(e["deformation-hover"]["border-radius"])||nix(e["deformation-hover"]["border-radius"][0])&&nix(e["deformation-hover"]["border-radius"][1])&&nix(e["deformation-hover"]["border-radius"][2])&&nix(e["deformation-hover"]["border-radius"][3])||a){y=nix(n["border-radius"])?["0px","0px","0px","0px"]:"object"!=typeof n["border-radius"]?n["border-radius"].split(" "):n["border-radius"];e["deformation-hover"]["border-radius"]=["0","0","0","0"],jQuery(e["deformation-hover"]["border-radius"]).each(function(t){e["deformation-hover"]["border-radius"][t]=y.length<2?y[0]:y.length<4?0==t||2==t?y[0]:y[1]:y[t]})}(nix(e["deformation-hover"].skewx)||a)&&(e["deformation-hover"].skewx=nix(n.skewx)?0:n.skewx),(nix(e["deformation-hover"].skewy)||a)&&(e["deformation-hover"].skewy=nix(n.skewy)?0:n.skewy),(nix(e["deformation-hover"].scalex)||a)&&(e["deformation-hover"].scalex=nix(n.scalex)?1:n.scalex),(nix(e["deformation-hover"].scaley)||a)&&(e["deformation-hover"].scaley=nix(n.scaley)?1:n.scaley),(nix(e["deformation-hover"].opacity)||a)&&(e["deformation-hover"].opacity=nix(n.opacity)?1:n.opacity),(nix(e["deformation-hover"].xrotate)||a)&&(e["deformation-hover"].xrotate=nix(n.xrotate)?0:n.xrotate),(nix(e["deformation-hover"].yrotate)||a)&&(e["deformation-hover"].yrotate=nix(n.yrotate)?0:n.yrotate),(nix(e["deformation-hover"]["2d_rotation"])||a)&&(e["deformation-hover"]["2d_rotation"]=nix(n["2d_rotation"])?0:n["2d_rotation"]),(nix(e["deformation-hover"].css_cursor)||a)&&(e["deformation-hover"].css_cursor=nix(n.css_cursor)?"auto":n.css_cursor),(nix(e["deformation-hover"].pointer_events)||a)&&(e["deformation-hover"].pointer_events=nix(n.pointer_events)?"auto":n.pointer_events),(nix(e["deformation-hover"].blurfilter)||a)&&(e["deformation-hover"].blurfilter=nix(n.blurfilter)?0:n.blurfilter),(nix(e["deformation-hover"].grayscalefilter)||a)&&(e["deformation-hover"].grayscalefilter=nix(n.grayscalefilter)?0:n.grayscalefilter),(nix(e["deformation-hover"].brightnessfilter)||a)&&(e["deformation-hover"].brightnessfilter=nix(n.brightnessfilter)?100:n.brightnessfilter),(nix(e["deformation-hover"].speed)||a)&&(e["deformation-hover"].speed=nix(n.speed)?"0":n.speed),(nix(e["deformation-hover"].easing)||a)&&(e["deformation-hover"].easing=nix(n.easing)?"Linear.easeNone":n.easing),null!=e["deformation-hover"]&&(jQuery("#hover_layer_color_s").val(e["deformation-hover"].color).tpColorPicker("refresh"),jQuery('#hover_css_text-decoration option[value="'+e["deformation-hover"]["text-decoration"]+'"]').attr("selected",!0),jQuery("#hover_css_background-color").val(e["deformation-hover"]["background-color"]).tpColorPicker("refresh"),jQuery("#hover_css_border-color-show").val(e["deformation-hover"]["border-color"]),jQuery('#hover_css_border-style option[value="'+e["deformation-hover"]["border-style"]+'"]').attr("selected",!0),jQuery('input[name="hover_css_border-width[]"]').each(function(t){jQuery(this).val(e["deformation-hover"]["border-width"][t])}),jQuery('input[name="hover_css_border-radius[]"]').each(function(t){jQuery(this).val(e["deformation-hover"]["border-radius"][t])}),jQuery("#hover_layer__skewx").val(e["deformation-hover"].skewx),jQuery("#hover_layer__skewy").val(e["deformation-hover"].skewy),jQuery("#hover_layer__scalex").val(e["deformation-hover"].scalex),jQuery("#hover_layer__scaley").val(e["deformation-hover"].scaley),jQuery("#hover_layer__opacity").val(e["deformation-hover"].opacity),jQuery("#hover_layer__xrotate").val(e["deformation-hover"].xrotate),jQuery("#hover_layer__yrotate").val(e["deformation-hover"].yrotate),jQuery("#hover_layer_2d_rotation").val(e["deformation-hover"]["2d_rotation"]),jQuery('#css_cursor option[value="'+e["deformation-hover"].css_cursor+'"]').attr("selected",!0),jQuery('#pointer_events option[value="'+e["deformation-hover"].pointer_events+'"]').attr("selected",!0),jQuery("#hover_speed").val(e["deformation-hover"].speed),jQuery("#hover_zindex").val(e["deformation-hover"].zindex),jQuery('#hover_easing option[value="'+e["deformation-hover"].easing+'"]').attr("selected",!0))}return jQuery(".wp-color-result").each(function(){jQuery(this).css("backgroundColor",jQuery(this).parent().find(".my-color-field").val())}),e};t.updateLayerFormFields=function(e){var r=t.arrLayers[e];if(jQuery.each(t.addon_callbacks,function(e,t){var a=t.callback,i=t.environment,o=t.function_position;"updateLayerFormFields"===i&&"start"==o&&(r=a(r))}),void 0===r)return!0;jQuery("#internal_classes").val(r.internal_class),jQuery(".rs-internal-class-wrapper").text(r.internal_class),jQuery("#layer_caption").val(r.style),jQuery("#layer_text").val(UniteAdminRev.stripslashes(r.text)),null!=r.texttoggle?jQuery("#layer_text_toggle").val(UniteAdminRev.stripslashes(r.texttoggle)):jQuery("#layer_text_toggle").val(""),jQuery("#layer_text").data("new_content",!1),jQuery('#layer_alt_option option[value="'+r.alt_option+'"]').attr("selected","selected"),jQuery("#layer_alt").val(r.alt),jQuery("#layer_alias_name").val(r.alias),"true"==r.hover||1==r.hover?(jQuery("#hover_allow").prop("checked",!0),jQuery("#idle-hover-swapper").show()):(jQuery("#hover_allow").prop("checked",!1),jQuery("#idle-hover-swapper").hide()),"true"==r.toggle||1==r.toggle?jQuery("#toggle_allow").prop("checked",!0):jQuery("#toggle_allow").prop("checked",!1),"true"==r.toggle_use_hover||1==r.toggle_use_hover?jQuery("#toggle_use_hover").prop("checked",!0):jQuery("#toggle_use_hover").prop("checked",!1),"true"==r.toggle_inverse_content||1==r.toggle_inverse_content?jQuery("#toggle_inverse_content").prop("checked",!0):jQuery("#toggle_inverse_content").prop("checked",!1),"true"==r["visible-notebook"]||1==r["visible-notebook"]?jQuery("#visible-notebook").prop("checked",!0):jQuery("#visible-notebook").prop("checked",!1),"true"==r["visible-desktop"]||1==r["visible-desktop"]?jQuery("#visible-desktop").prop("checked",!0):jQuery("#visible-desktop").prop("checked",!1),"true"==r["visible-tablet"]||1==r["visible-tablet"]?jQuery("#visible-tablet").prop("checked",!0):jQuery("#visible-tablet").prop("checked",!1),"true"==r["visible-mobile"]||1==r["visible-mobile"]?jQuery("#visible-mobile").prop("checked",!0):jQuery("#visible-mobile").prop("checked",!1),"true"==r["show-on-hover"]||1==r["show-on-hover"]?jQuery("#layer_on_slider_hover").prop("checked",!0):jQuery("#layer_on_slider_hover").prop("checked",!1),jQuery('#layer-lazy-loading option[value="'+r["lazy-load"]+'"]').attr("selected","selected"),jQuery('#layer-image-size option[value="'+r["image-size"]+'"]').attr("selected","selected"),jQuery('#layer-css-position option[value="'+r["css-position"]+'"]').attr("selected","selected"),RevSliderSettings.onoffStatus(jQuery("#hover_allow")),RevSliderSettings.onoffStatus(jQuery("#toggle_allow")),RevSliderSettings.onoffStatus(jQuery("#toggle_use_hover")),RevSliderSettings.onoffStatus(jQuery("#toggle_inverse_content")),RevSliderSettings.onoffStatus(jQuery("#visible-desktop")),RevSliderSettings.onoffStatus(jQuery("#visible-notebook")),RevSliderSettings.onoffStatus(jQuery("#visible-tablet")),RevSliderSettings.onoffStatus(jQuery("#visible-mobile")),RevSliderSettings.onoffStatus(jQuery("#layer_on_slider_hover")),jQuery("#layer_scaleX").val(specOrVal(t.getVal(r,"scaleX"),["auto","#1/1#","#1/2#","#1/3#","#1/4#","#1/5#","#1/6#","#2/3#","#3/4#","#2/5#","#3/5#","#4/5#","#4/6#","#5/6#"],"px")),jQuery("#layer_scaleY").val(specOrVal(t.getVal(r,"scaleY"),["auto","#1/1#","#1/2#","#1/3#","#1/4#","#1/5#","#1/6#","#2/3#","#3/4#","#2/5#","#3/5#","#4/5#","#4/6#","#5/6#"],"px")),jQuery('#layer_cover_mode option[value="'+r.cover_mode+'"]').attr("selected","selected"),jQuery("#layer_max_height").val(specOrVal(t.getVal(r,"max_height"),["auto","#1/1#","#1/2#","#1/3#","#1/4#","#1/5#","#1/6#","#2/3#","#3/4#","#2/5#","#3/5#","#4/5#","#4/6#","#5/6#"],"px")),jQuery("#layer_min_height").val(specOrVal(t.getVal(r,"min_height"),["auto","#1/1#","#1/2#","#1/3#","#1/4#","#1/5#","#1/6#","#2/3#","#3/4#","#2/5#","#3/5#","#4/5#","#4/6#","#5/6#"],"px")),jQuery("#layer_max_width").val(specOrVal(t.getVal(r,"max_width"),["auto","#1/1#","#1/2#","#1/3#","#1/4#","#1/5#","#1/6#","#2/3#","#3/4#","#2/5#","#3/5#","#4/5#","#4/6#","#5/6#"],"px")),jQuery("#layer_video_height").val(specOrVal(t.getVal(r,"video_height"),["auto","#1/1#","#1/2#","#1/3#","#1/4#","#1/5#","#1/6#","#2/3#","#3/4#","#2/5#","#3/5#","#4/5#","#4/6#","#5/6#"],"px")),jQuery("#layer_video_width").val(specOrVal(t.getVal(r,"video_width"),["auto","#1/1#","#1/2#","#1/3#","#1/4#","#1/5#","#1/6#","#2/3#","#3/4#","#2/5#","#3/5#","#4/5#","#4/6#","#5/6#"],"px")),jQuery("#layer_2d_rotation").val(r["2d_rotation"]),jQuery("#layer_2d_origin_x").val(r["2d_origin_x"]),jQuery("#layer_2d_origin_y").val(r["2d_origin_y"]),jQuery("#layer_static_start option[value='"+r.static_start+"']").attr("selected","selected"),changeEndStaticFunctions(),jQuery("#layer_static_end option[value='"+r.static_end+"']").attr("selected","selected"),jQuery("#layer_whitespace option[value='"+t.getVal(r,"whitespace")+"']").attr("selected","selected"),jQuery("#layer_display option[value='"+t.getVal(r,"display")+"']").attr("selected","selected"),"true"==r.scaleProportional||1==r.scaleProportional?(jQuery(".rs-proportion-check").removeClass("notselected"),jQuery("#layer_proportional_scale").prop("checked",!0)):(jQuery("#layer_proportional_scale").prop("checked",!1),jQuery(".rs-proportion-check").addClass("notselected")),"normal"===t.getVal(r,"whitespace")?(jQuery(".rs-linebreak-check").removeClass("notselected"),jQuery("#layer_auto_line_break").prop("checked",!0)):(jQuery(".rs-linebreak-check").addClass("notselected"),jQuery("#layer_auto_line_break").prop("checked",!1)),"block"===t.getVal(r,"display")?(jQuery("#layer-displaymode-wrapper").removeClass("notselected"),jQuery("#layer_displaymode").prop("checked",!0)):(jQuery("#layer-displaymode-wrapper").addClass("notselected"),jQuery("#layer_displaymode").prop("checked",!1)),RevSliderSettings.onoffStatus(jQuery(".rs-proportion-check")),RevSliderSettings.onoffStatus(jQuery(".rs-linebreak-check")),jQuery("#layer_top").val(parseInt(t.getVal(r,"top"),0)+"px"),jQuery("#layer_left").val(parseInt(t.getVal(r,"left"),0)+"px"),jQuery("#layer_bg_position option[value='"+r.layer_bg_position+"']").attr("selected","selected"),jQuery("#layer_bg_size option[value='"+r.layer_bg_size+"']").attr("selected","selected"),jQuery("#layer_bg_repeat option[value='"+r.layer_bg_repeat+"']").attr("selected","selected"),jQuery("#layer_loop_animation option[value='"+r.loop_animation+"']").attr("selected","selected"),jQuery("#layer_loop_easing").val(r.loop_easing),jQuery("#layer_loop_speed").val(r.loop_speed),jQuery("#layer_loop_startdeg").val(r.loop_startdeg),jQuery("#layer_loop_enddeg").val(r.loop_enddeg),jQuery("#layer_loop_xorigin").val(r.loop_xorigin),jQuery("#layer_loop_yorigin").val(r.loop_yorigin),jQuery("#layer_loop_xstart").val(r.loop_xstart),jQuery("#layer_loop_xend").val(r.loop_xend),jQuery("#layer_loop_ystart").val(r.loop_ystart),jQuery("#layer_loop_yend").val(r.loop_yend),jQuery("#layer_loop_zoomstart").val(r.loop_zoomstart),jQuery("#layer_loop_zoomend").val(r.loop_zoomend),jQuery("#layer_loop_angle").val(r.loop_angle),jQuery("#layer_loop_radius").val(r.loop_radius),null!=r.svg&&(jQuery("#css_svgstroke-color-show").val(r.svg["svgstroke-color"]),jQuery("#css_svgstroke-dasharray").val(r.svg["svgstroke-dasharray"]),jQuery("#css_svgstroke-dashoffset").val(r.svg["svgstroke-dashoffset"]),jQuery("#css_svgstroke-width").val(r.svg["svgstroke-width"]),jQuery("#css_svgstroke-hover-color-show").val(r.svg["svgstroke-hover-color"]),jQuery("#css_svgstroke-hover-dasharray").val(r.svg["svgstroke-hover-dasharray"]),jQuery("#css_svgstroke-hover-dashoffset").val(r.svg["svgstroke-hover-dashoffset"]),jQuery("#css_svgstroke-hover-width").val(r.svg["svgstroke-hover-width"])),jQuery("#layer_html_tag option[value='"+r.html_tag+"']").attr("selected","selected"),jQuery("#parallax_layer_ddd_zlevel option[value='"+r.parallax_layer_ddd_zlevel+"']").attr("selected","selected"),"true"==r.mask_start||1==r.mask_start?(jQuery("#masking-start").prop("checked",!1),jQuery(".mask-start-settings").show()):(jQuery("#masking-end").prop("checked",!1),jQuery(".mask-start-settings").hide()),"true"==r.mask_end||1==r.mask_end?(jQuery("#masking-end").attr("checked",!0),jQuery(".mask-end-settings").show()):(jQuery("#masking-end").removeAttr("checked"),jQuery(".mask-end-settings").hide()),"true"==r.frames.frame_0.use_text_c||1==r.frames.frame_0.use_text_c?(jQuery("#use_text_color_start").attr("checked",!0),jQuery(".use_text_color_wrap_start").show()):(jQuery("#use_text_color_start").attr("checked",!1),jQuery(".use_text_color_wrap_start").hide()),"true"==r.frames.frame_0.use_bg_c||1==r.frames.frame_0.use_bg_c?(jQuery("#use_bg_color_start").attr("checked",!0),jQuery(".use_bg_color_wrap_start").show()):(jQuery("#use_bg_color_start").attr("checked",!1),jQuery(".use_bg_color_wrap_start").hide()),"true"==r.frames.frame_999.use_text_c||1==r.frames.frame_999.use_text_c?(jQuery("#use_text_color_end").attr("checked",!0),jQuery(".use_text_color_wrap_end").show()):(jQuery("#use_text_color_end").attr("checked",!1),jQuery(".use_text_color_wrap_end").hide()),"true"==r.frames.frame_999.use_bg_c||1==r.frames.frame_999.use_bg_c?(jQuery("#use_bg_color_end").attr("checked",!0),jQuery(".use_bg_color_wrap_end").show()):(jQuery("#use_bg_color_end").attr("checked",!1),jQuery(".use_bg_color_wrap_end").hide()),RevSliderSettings.onoffStatus(jQuery("#masking-start")),RevSliderSettings.onoffStatus(jQuery("#masking-end")),RevSliderSettings.onoffStatus(jQuery("#use_text_color_start")),RevSliderSettings.onoffStatus(jQuery("#use_bg_color_start")),RevSliderSettings.onoffStatus(jQuery("#use_text_color_end")),RevSliderSettings.onoffStatus(jQuery("#use_bg_color_end")),jQuery("#text_color_start").val(r.frames.frame_0.text_c).tpColorPicker("refresh"),jQuery("#bg_color_start").val(r.frames.frame_0.bg_c).tpColorPicker("refresh"),jQuery("#text_color_end").val(r.frames.frame_999.text_c).tpColorPicker("refresh"),jQuery("#bg_color_end").val(r.frames.frame_999.bg_c).tpColorPicker("refresh"),jQuery("#mask_anim_xstart").val(r.mask_x_start),jQuery("#mask_anim_ystart").val(r.mask_y_start),jQuery("#mask_speed").val(r.mask_speed_start),jQuery("#mask_easing").val(r.mask_ease_start),jQuery("#mask_anim_xend").val(r.mask_x_end),jQuery("#mask_anim_yend").val(r.mask_y_end),jQuery("#layer_animation option[value='"+r.frames.frame_0.animation+"']").attr("selected","selected"),jQuery("#layer_easing").val(r.frames.frame_0.easing),jQuery("#sfx_in_effect").val(r.frames.frame_0.sfx_effect),jQuery("#sfx_out_effect").val(r.frames.frame_999.sfx_effect),jQuery("#layer_split").val(r.frames.frame_0.split),jQuery("#layer_split_direction").val(r.frames.frame_0.split_direction),jQuery("#layer_endsplit").val(r.frames.frame_999.split),jQuery("#layer_endsplit_direction").val(r.frames.frame_999.split_direction),jQuery("#layer_splitdelay").val(r.frames.frame_0.splitdelay),jQuery("#layer_endsplitdelay").val(r.frames.frame_999.splitdelay),jQuery("#layer_speed").val(r.frames.frame_0.speed),jQuery("#sfx_color_start").val(r.frames.frame_0.sfxcolor).tpColorPicker("refresh"),jQuery("#layer_align_hor").val(t.getVal(r,"align_hor")),jQuery("#layer_align_vert").val(t.getVal(r,"align_vert")),"true"==r.hiddenunder||1==r.hiddenunder?jQuery("#layer_hidden").prop("checked",!0):jQuery("#layer_hidden").prop("checked",!1),"true"==r.resizeme||1==r.resizeme?jQuery("#layer_resizeme").prop("checked",!0):jQuery("#layer_resizeme").prop("checked",!1),"true"==r["seo-optimized"]||1==r["seo-optimized"]?jQuery("#layer-seo-optimized").prop("checked",!0):jQuery("#layer-seo-optimized").prop("checked",!1),"true"!=r["resize-full"]&&1!=r["resize-full"]||"row"===r.type||"column"===r.type?(jQuery("#layer_resize-full").prop("checked",!1),jQuery("#layer_resizeme").prop("checked",!1),r.resizeme=!1):(jQuery("#layer_resize-full").prop("checked",!0),"group"===r.type&&(jQuery("#layer_resizeme").prop("checked",!1),r.resizeme=!1)),jQuery("#layer_align_base").val(r.basealign),"true"==r.responsive_offset||1==r.responsive_offset?jQuery("#layer_resp_offset").prop("checked",!0):jQuery("#layer_resp_offset").prop("checked",!1),RevSliderSettings.onoffStatus(jQuery("#layer_hidden")),RevSliderSettings.onoffStatus(jQuery("#layer_resizeme")),RevSliderSettings.onoffStatus(jQuery("#layer_resize-full")),RevSliderSettings.onoffStatus(jQuery("#layer_resp_offset")),RevSliderSettings.onoffStatus(jQuery("#layer-seo-optimized")),jQuery("#layer_image_link").val(r.link),jQuery("#layer_link_follow").val(r.link_follow),jQuery("#layer_link_open_in").val(r.link_open_in),jQuery("#layer_link_id").val(r.link_id),jQuery("#layer_link_class").val(r.link_class),jQuery("#layer_link_title").val(r.link_title),jQuery("#layer_link_rel").val(r.link_rel),jQuery("#layer_auto_line_break").val(r.autolinebreak),jQuery("#layer_displaymode").val(r.displaymode),jQuery("#layer_endanimation").val(r.frames.frame_999.animation),jQuery("#layer_endeasing").val(r.frames.frame_999.easing),jQuery("#layer_endspeed").val(r.frames.frame_999.speed),jQuery("#sfx_color_end").val(r.frames.frame_999.sfxcolor).tpColorPicker("refresh"),jQuery("#layer_anim_xstart").val(r.x_start),jQuery("#layer_anim_ystart").val(r.y_start),jQuery("#layer_anim_zstart").val(r.z_start),jQuery("#layer_anim_xend").val(r.x_end),jQuery("#layer_anim_yend").val(r.y_end),jQuery("#layer_anim_zend").val(r.z_end),jQuery("#layer_opacity_start").val(r.opacity_start),jQuery("#layer_opacity_end").val(r.opacity_end),jQuery("#layer_anim_xrotate").val(r.x_rotate_start),jQuery("#layer_anim_yrotate").val(r.y_rotate_start),jQuery("#layer_anim_zrotate").val(r.z_rotate_start),jQuery("#layer_anim_xrotate_end").val(r.x_rotate_end),jQuery("#layer_anim_yrotate_end").val(r.y_rotate_end),jQuery("#layer_anim_zrotate_end").val(r.z_rotate_end),jQuery("#layer_scale_xstart").val(r.scale_x_start),jQuery("#layer_scale_ystart").val(r.scale_y_start),jQuery("#layer_scale_xend").val(r.scale_x_end),jQuery("#layer_scale_yend").val(r.scale_y_end),jQuery("#layer_skew_xstart").val(r.skew_x_start),jQuery("#layer_skew_ystart").val(r.skew_y_start),jQuery("#layer_skew_xend").val(r.skew_x_end),jQuery("#layer_skew_yend").val(r.skew_y_end),jQuery("#layer_pers_start").val(r.pers_start),jQuery("#layer_pers_end").val(r.pers_end),jQuery("#blurfilter_start").val(r.blurfilter_start),jQuery("#blurfilter_end").val(r.blurfilter_end),jQuery("#grayscalefilter_start").val(r.grayscalefilter_start),jQuery("#grayscalefilter_end").val(r.grayscalefilter_end),jQuery("#brightnessfilter_start").val(r.brightnessfilter_start),jQuery("#brightnessfilter_end").val(r.brightnessfilter_end),void 0!==r["layer-selectable"]?jQuery('#css_layer_selectable option[value="'+r["layer-selectable"]+'"]').attr("selected",!0):jQuery('#css_layer_selectable option[value="default"]').attr("selected",!0),void 0===r.x_start_reverse||"true"!=r.x_start_reverse&&1!=r.x_start_reverse?jQuery("#layer_anim_xstart_reverse").prop("checked",!1):jQuery("#layer_anim_xstart_reverse").attr("checked",!0),void 0===r.y_start_reverse||"true"!=r.y_start_reverse&&1!=r.y_start_reverse?jQuery("#layer_anim_ystart_reverse").prop("checked",!1):jQuery("#layer_anim_ystart_reverse").attr("checked",!0),void 0===r.x_end_reverse||"true"!=r.x_end_reverse&&1!=r.x_end_reverse?jQuery("#layer_anim_xend_reverse").prop("checked",!1):jQuery("#layer_anim_xend_reverse").attr("checked",!0),void 0===r.y_end_reverse||"true"!=r.y_end_reverse&&1!=r.y_end_reverse?jQuery("#layer_anim_yend_reverse").prop("checked",!1):jQuery("#layer_anim_yend_reverse").attr("checked",!0),void 0===r.x_rotate_start_reverse||"true"!=r.x_rotate_start_reverse&&1!=r.x_rotate_start_reverse?jQuery("#layer_anim_xrotate_reverse").prop("checked",!1):jQuery("#layer_anim_xrotate_reverse").attr("checked",!0),void 0===r.y_rotate_start_reverse||"true"!=r.y_rotate_start_reverse&&1!=r.y_rotate_start_reverse?jQuery("#layer_anim_yrotate_reverse").prop("checked",!1):jQuery("#layer_anim_yrotate_reverse").attr("checked",!0),void 0===r.z_rotate_start_reverse||"true"!=r.z_rotate_start_reverse&&1!=r.z_rotate_start_reverse?jQuery("#layer_anim_zrotate_reverse").prop("checked",!1):jQuery("#layer_anim_zrotate_reverse").attr("checked",!0),void 0===r.x_rotate_end_reverse||"true"!=r.x_rotate_end_reverse&&1!=r.x_rotate_end_reverse?jQuery("#layer_anim_xrotate_end_reverse").prop("checked",!1):jQuery("#layer_anim_xrotate_end_reverse").attr("checked",!0),void 0===r.y_rotate_end_reverse||"true"!=r.y_rotate_end_reverse&&1!=r.y_rotate_end_reverse?jQuery("#layer_anim_yrotate_end_reverse").prop("checked",!1):jQuery("#layer_anim_yrotate_end_reverse").attr("checked",!0),void 0===r.z_rotate_end_reverse||"true"!=r.z_rotate_end_reverse&&1!=r.z_rotate_end_reverse?jQuery("#layer_anim_zrotate_end_reverse").prop("checked",!1):jQuery("#layer_anim_zrotate_end_reverse").attr("checked",!0),void 0===r.scale_x_start_reverse||"true"!=r.scale_x_start_reverse&&1!=r.scale_x_start_reverse?jQuery("#layer_scale_xstart_reverse").prop("checked",!1):jQuery("#layer_scale_xstart_reverse").attr("checked",!0),void 0===r.scale_y_start_reverse||"true"!=r.scale_y_start_reverse&&1!=r.scale_y_start_reverse?jQuery("#layer_scale_ystart_reverse").prop("checked",!1):jQuery("#layer_scale_ystart_reverse").attr("checked",!0),void 0===r.scale_x_end_reverse||"true"!=r.scale_x_end_reverse&&1!=r.scale_x_end_reverse?jQuery("#layer_scale_xend_reverse").prop("checked",!1):jQuery("#layer_scale_xend_reverse").attr("checked",!0),void 0===r.scale_y_end_reverse||"true"!=r.scale_y_end_reverse&&1!=r.scale_y_end_reverse?jQuery("#layer_scale_yend_reverse").prop("checked",!1):jQuery("#layer_scale_yend_reverse").attr("checked",!0),void 0===r.skew_x_start_reverse||"true"!=r.skew_x_start_reverse&&1!=r.skew_x_start_reverse?jQuery("#layer_skew_xstart_reverse").prop("checked",!1):jQuery("#layer_skew_xstart_reverse").attr("checked",!0),void 0===r.skew_y_start_reverse||"true"!=r.skew_y_start_reverse&&1!=r.skew_y_start_reverse?jQuery("#layer_skew_ystart_reverse").prop("checked",!1):jQuery("#layer_skew_ystart_reverse").attr("checked",!0),void 0===r.skew_x_end_reverse||"true"!=r.skew_x_end_reverse&&1!=r.skew_x_end_reverse?jQuery("#layer_skew_xend_reverse").prop("checked",!1):jQuery("#layer_skew_xend_reverse").attr("checked",!0),void 0===r.skew_y_end_reverse||"true"!=r.skew_y_end_reverse&&1!=r.skew_y_end_reverse?jQuery("#layer_skew_yend_reverse").prop("checked",!1):jQuery("#layer_skew_yend_reverse").attr("checked",!0),void 0===r.mask_x_start_reverse||"true"!=r.mask_x_start_reverse&&1!=r.mask_x_start_reverse?jQuery("#mask_anim_xstart_reverse").prop("checked",!1):jQuery("#mask_anim_xstart_reverse").attr("checked",!0),void 0===r.mask_y_start_reverse||"true"!=r.mask_y_start_reverse&&1!=r.mask_y_start_reverse?jQuery("#mask_anim_ystart_reverse").prop("checked",!1):jQuery("#mask_anim_ystart_reverse").attr("checked",!0),void 0===r.mask_x_end_reverse||"true"!=r.mask_x_end_reverse&&1!=r.mask_x_end_reverse?jQuery("#mask_anim_xend_reverse").prop("checked",!1):jQuery("#mask_anim_xend_reverse").attr("checked",!0),void 0===r.mask_y_end_reverse||"true"!=r.mask_y_end_reverse&&1!=r.mask_y_end_reverse?jQuery("#mask_anim_yend_reverse").prop("checked",!1):jQuery("#mask_anim_yend_reverse").attr("checked",!0),t.updateReverseList(),null!=r.static_styles&&(jQuery("#layer_font_size_s").val(t.getVal(r.static_styles,"font-size")),jQuery("#layer_line_height_s").val(t.getVal(r.static_styles,"line-height")),jQuery("#layer_font_weight_s option[value='"+t.getVal(r.static_styles,"font-weight")+"']").attr("selected",!0),jQuery("#layer_color_s").val(t.getVal(r.static_styles,"color")).tpColorPicker("refresh"),jQuery("#letter_spacing_s").val(t.getVal(r.static_styles,"letter-spacing"))),null!=r.animation_overwrite?jQuery('#layer-animation-overwrite option[value="'+r.animation_overwrite+'"]').attr("selected",!0):jQuery('#layer-animation-overwrite option[value="wait"]').attr("selected",!0),null!=r.trigger_memory?jQuery('#layer-tigger-memory option[value="'+r.trigger_memory+'"]').attr("selected",!0):jQuery('#layer-tigger-memory option[value="keep"]').attr("selected",!0),t.remove_layer_actions(),t.add_layer_actions(r),r=updateSubStyleParameters(r);var a=t.getVal(r,"align_hor"),i=t.getVal(r,"align_vert");jQuery("#rs-align-wrapper a").removeClass("selected"),jQuery("#rs-align-wrapper-ver a").removeClass("selected"),jQuery("#rs-align-wrapper a[data-hor='"+a+"']").addClass("selected"),jQuery("#rs-align-wrapper-ver a[data-ver='"+i+"']").addClass("selected"),jQuery("#layer_id").val(r.attrID),jQuery("#layer_wrapper_id").val(r.attrWrapperID),jQuery("#layer_classes").val(r.attrClasses),jQuery("#layer_wrapper_classes").val(r.attrWrapperClasses),jQuery("#layer_title").val(r.attrTitle),jQuery("#layer_tabindex").val(r.attrTabindex),jQuery("#layer_rel").val(r.attrRel),jQuery('select[name="layer_action[]"], select[name="no_layer_action[]"]').each(function(){showHideLinkActions(jQuery(this))}),showHideToolTip(),showHideLoopFunctions(),jQuery.each(t.addon_callbacks,function(e,t){var a=t.callback,i=t.environment,o=t.function_position;"updateLayerFormFields"===i&&"end"==o&&(r=a(r))})};var unselectHtmlLayers=function(){jQuery.each(t.addon_callbacks,function(e,t){var r=t.callback,a=t.environment,i=t.function_position;"unselectHtmlLayers"===a&&"start"==i&&r()});for(var e=document.getElementById("divLayers"),r=e.getElementsByClassName("ui-resizable"),a=e.getElementsByClassName("ui-rotatable-handle"),i=0;i<r.length-1;i++)try{jQuery(r[i]).resizable("destroy")}catch(e){}for(i=0;i<a.length-1;i++)try{jQuery(a[i]).rotatable("destroy")}catch(e){}jQuery(containerID+" .slide_layer").removeClass("layer_selected"),jQuery(".layerrow_selected").removeClass("layerrow_selected"),jQuery(".layerchild_selected").removeClass("layerchild_selected"),document.getElementById("id-esw").className="",jQuery.each(t.addon_callbacks,function(e,t){var r=t.callback,a=t.environment,i=t.function_position;"unselectHtmlLayers"===a&&"end"==i&&r()})};t.hideRowLayoutComposer=function(){jQuery("#rs-layout-row-composer").hide();for(var e=document.getElementsByClassName("row_editor open_re"),t=0;t<e.length;t++)e[t].className="row_editor"};var unselectLayers=function(){t.hideRowLayoutComposer(),unselectHtmlLayers(),jQuery("#timline-manual-dialog").appendTo(jQuery("#thelayer-editor-wrapper")).hide();for(var e=document.getElementsByClassName("layerchild_selected"),r=0;r<e.length-1;r++)jQuery(e[r]).removeClass("layerchild_selected");jQuery(".quicksortlayer.selected").removeClass("selected"),jQuery(".layerrow_selected").removeClass("layerrow_selected"),u.unselectSortboxItems(),t.selectedLayerSerial=-1,disableFormFields(),t.showHideContentEditor(!1),jQuery(".form_layers").addClass("notselected"),u.resetIdleSelector(),jQuery("#idle-hover-swapper").hide(),u.allLayerToIdle(),jQuery("#the_current-editing-layer-title").addClass("nolayerselectednow").val("No Layers Selected").attr("disabled","disabled"),jQuery("#layer-short-toolbar").data("serial","")};t.toolbarInPos=function(e){if(e)switch(e.type){case"image":jQuery("#button_change_image_source").show(),jQuery("#button_edit_layer").hide(),jQuery("#button_reset_size").show(),jQuery("#button_change_video_settings").hide(),jQuery("#layer-short-tool-placeholder-a").hide(),jQuery("#layer-short-tool-placeholder-b").hide();break;case"text":case"button":jQuery("#layer_text_wrapper").hasClass("currently_editing_txt")?(jQuery("#button_edit_layer").hide(),jQuery("#button_reset_size").hide()):(jQuery("#button_edit_layer").show(),jQuery("#button_reset_size").show()),jQuery("#button_change_image_source").hide(),jQuery("#button_change_video_settings").hide(),jQuery("#layer-short-tool-placeholder-a").hide(),jQuery("#layer-short-tool-placeholder-b").hide();break;case"audio":case"video":jQuery("#button_edit_layer").hide(),jQuery("#button_change_image_source").hide(),jQuery("#button_reset_size").hide(),jQuery("#button_change_video_settings").show(),jQuery("#layer-short-tool-placeholder-a").show(),jQuery("#layer-short-tool-placeholder-b").hide();break;case"shape":jQuery("#button_change_image_source").hide(),jQuery("#button_change_video_settings").hide(),jQuery("#button_edit_layer").hide(),jQuery("#button_reset_size").hide(),jQuery("#layer-short-tool-placeholder-a").show(),jQuery("#layer-short-tool-placeholder-b").show();break;case"svg":jQuery("#button_change_image_source").hide(),jQuery("#button_edit_layer").hide(),jQuery("#button_reset_size").hide(),jQuery("#button_change_video_settings").hide(),jQuery("#layer-short-tool-placeholder-a").show(),jQuery("#layer-short-tool-placeholder-b").hide(),jQuery("#button_edit_layer").show(),jQuery("#layer-short-tool-placeholder-b").hide()}},t.remove_layer_actions=function(){jQuery(".layer_action_wrap").each(function(){jQuery(this).remove()})},t.checkActionsOnLayers=function(e){var r=t.getSimpleLayers(),a=t.getUniqueIdByLayer(e);for(var i in r)if(r.hasOwnProperty(i)&&void 0!==r[i].layer_action)for(var o in r[i].layer_action.action)if(r[i].layer_action.action.hasOwnProperty(o))switch(r[i].layer_action.action[o]){case"start_in":case"start_out":case"start_video":case"stop_video":case"toggle_layer":case"toggle_video":case"mute_video":case"unmute_video":case"toggle_mute_video":case"toggle_global_mute_video":case"simulate_click":case"toggle_class":a==r[i].layer_action.layer_target[o]&&(r[i].layer_action.action&&r[i].layer_action.action[o]&&delete r[i].layer_action.action[o],r[i].layer_action.tooltip_event&&r[i].layer_action.tooltip_event[o]&&delete r[i].layer_action.tooltip_event[o],r[i].layer_action.image_link&&r[i].layer_action.image_link[o]&&delete r[i].layer_action.image_link[o],r[i].layer_action.link_open_in&&r[i].layer_action.link_open_in[o]&&delete r[i].layer_action.link_open_in[o],r[i].layer_action.link_follow&&r[i].layer_action.link_follow[o]&&delete r[i].layer_action.link_follow[o],r[i].layer_action.jump_to_slide&&r[i].layer_action.jump_to_slide[o]&&delete r[i].layer_action.jump_to_slide[o],r[i].layer_action.scrolloffset&&r[i].layer_action.scrolloffset[o]&&delete r[i].layer_action.scrolloffset[o],r[i].layer_action.actioncallback&&r[i].layer_action.actioncallback[o]&&delete r[i].layer_action.actioncallback[o],r[i].layer_action.layer_target&&r[i].layer_action.layer_target[o]&&delete r[i].layer_action.layer_target[o],r[i].layer_action.action_delay&&r[i].layer_action.action_delay[o]&&delete r[i].layer_action.action_delay[o],r[i].layer_action.link_type&&r[i].layer_action.link_type[o]&&delete r[i].layer_action.link_type[o],r[i].layer_action.toggle_layer_type&&r[i].layer_action.toggle_layer_type[o]&&delete r[i].layer_action.toggle_layer_type[o],r[i].layer_action.toggle_class&&r[i].layer_action.toggle_class[o]&&delete r[i].layer_action.toggle_class[o])}},t.remove_action=function(e){confirm(rev_lang.remove_this_action)&&(e.closest("li").remove(),t.updateLayerFromFields(),u.updateAllLayerTimeline())},t.checkLayerTriggered=function(e){var r=t.getSimpleLayers(),a={in:!1,out:!1};for(var i in r)if(r.hasOwnProperty(i)&&void 0!==r[i].layer_action)for(var o in r[i].layer_action.action)if(r[i].layer_action.action.hasOwnProperty(o))switch(r[i].layer_action.action[o]){case"start_in":case"start_out":case"start_video":case"stop_video":case"toggle_layer":case"toggle_video":case"simulate_click":case"toggle_class":var s=r[i].layer_action.layer_target[o];if(e.unique_id==s)switch(r[i].layer_action.action[o]){case"start_in":a.in=!0;break;case"start_out":a.out=!0;break;case"toggle_layer":a.in=!0,a.out=!0}}return a},t.add_layer_actions=function(e){var r=t.getSimpleLayers();if(void 0===e){var a=global_action_template({edit:!0});jQuery(".layer_action_add_template").before(a),t.updateLayerFromFields()}else{jQuery("#triggered-element-behavior").hide();var i=t.getCurrentLayer();for(var o in r)if(r.hasOwnProperty(o)&&void 0!==r[o].layer_action){var s=!1;for(var n in r[o].layer_action.action)if(r[o].layer_action.action.hasOwnProperty(n))switch(r[o].layer_action.action[n]){case"start_in":case"start_out":case"start_video":case"stop_video":case"toggle_layer":case"toggle_video":case"simulate_click":case"toggle_class":var l=r[o].layer_action.layer_target[n];if(i.unique_id==l){switch(r[o].layer_action.action[n]){case"start_in":case"start_out":case"toggle_layer":s=!0}var d="";switch(r[o].layer_action.action[n]){case"start_in":d=rev_lang.start_layer_in;break;case"start_out":d=rev_lang.start_layer_out;break;case"start_video":d=rev_lang.start_video;break;case"stop_video":d=rev_lang.stop_video;break;case"toggle_layer":d=rev_lang.toggle_layer_anim;break;case"toggle_video":d=rev_lang.toggle_video;break;case"simulate_click":d=rev_lang.simulate_click;break;case"toggle_class":d=rev_lang.toggle_class}jQuery(".layer_action_add_template").before('<li class="layer_is_triggered layer_action_wrap">'+rev_lang.layer_action_by+" <a href=\"javascript:UniteLayersRev.setLayerSelected('"+o+"');void(0);\">"+r[o].alias+"</a> "+rev_lang.due_to_action+" "+d+"</li>")}}s&&jQuery("#triggered-element-behavior").show()}jQuery(".rs_disabled_field").each(function(){jQuery(this).attr("disabled","disabled")})}if(void 0!==e&&void 0!==e.layer_action&&void 0!==e.layer_action.action)for(var o in e.layer_action.action)if(e.layer_action.action.hasOwnProperty(o)){var y={edit:!0};y.tooltip_event=void 0!==e.layer_action.tooltip_event&&void 0!==e.layer_action.tooltip_event[o]?e.layer_action.tooltip_event[o]:"click",y.action=void 0!==e.layer_action.action&&void 0!==e.layer_action.action[o]?e.layer_action.action[o]:"none",y.image_link=void 0!==e.layer_action.image_link&&void 0!==e.layer_action.image_link[o]?e.layer_action.image_link[o]:"",y.link_open_in=void 0!==e.layer_action.link_open_in&&void 0!==e.layer_action.link_open_in[o]?e.layer_action.link_open_in[o]:"same",y.link_follow=void 0!==e.layer_action.link_follow&&void 0!==e.layer_action.link_follow[o]?e.layer_action.link_follow[o]:"same",y.jump_to_slide=void 0!==e.layer_action.jump_to_slide&&void 0!==e.layer_action.jump_to_slide[o]?e.layer_action.jump_to_slide[o]:"",y.scrolloffset=void 0!==e.layer_action.scrollunder_offset&&void 0!==e.layer_action.scrollunder_offset[o]?e.layer_action.scrollunder_offset[o]:"",y.action_easing=void 0!==e.layer_action.action_easing&&void 0!==e.layer_action.action_easing[o]?e.layer_action.action_easing[o]:"Linear.easeNone",y.action_speed=void 0!==e.layer_action.action_speed&&void 0!==e.layer_action.action_speed[o]?e.layer_action.action_speed[o]:"300",y.actioncallback=void 0!==e.layer_action.actioncallback&&void 0!==e.layer_action.actioncallback[o]?e.layer_action.actioncallback[o]:"",y.layer_target=void 0!==e.layer_action.layer_target&&void 0!==e.layer_action.layer_target[o]?e.layer_action.layer_target[o]:"",y.action_delay=void 0!==e.layer_action.action_delay&&void 0!==e.layer_action.action_delay[o]?e.layer_action.action_delay[o]:"",y.link_type=void 0!==e.layer_action.link_type&&void 0!==e.layer_action.link_type[o]?e.layer_action.link_type[o]:"jquery",y.toggle_layer_type=void 0!==e.layer_action.toggle_layer_type&&void 0!==e.layer_action.toggle_layer_type[o]?e.layer_action.toggle_layer_type[o]:"visible",y.toggle_class=void 0!==e.layer_action.toggle_class&&void 0!==e.layer_action.toggle_class[o]?e.layer_action.toggle_class[o]:"",jQuery.each(t.addon_callbacks,function(t,r){var a=r.callback,i=r.environment,s=r.function_position;"add_layer_actions"===i&&"data_definition"==s&&(y=a(y,e,o))});a=global_action_template(y);jQuery(".layer_action_add_template").before(a)}jQuery('select[name="jump_to_slide[]"], select[name="no_jump_to_slide[]"]').each(function(){for(var e in jQuery(this).html(""),slideIDs)if(slideIDs.hasOwnProperty(e))for(var t in slideIDs[e])slideIDs[e].hasOwnProperty(t)&&jQuery(this).append(jQuery("<option></option>").val(t).text("Slide: "+slideIDs[e][t]));var r=jQuery(this).data("selectoption");jQuery(this).find('option[value="'+r+'"]').attr("selected",!0)}),jQuery('select[name="layer_target[]"], select[name="no_layer_target[]"]').each(function(e){for(var a in jQuery(this).html(""),jQuery(this).append(jQuery('<option data-mytype="video-special"></option>').val("backgroundvideo").text(rev_lang.background_video)),jQuery(this).append(jQuery('<option data-mytype="video-special"></option>').val("firstvideo").text(rev_lang.active_video)),r)r.hasOwnProperty(a)&&!0!==r[a].deleted&&jQuery(this).append(jQuery('<option data-mytype="'+r[a].type+'"></option>').val(r[a].unique_id).text(r[a].alias));if(null!==initStaticLayers&&initStaticLayers.length>0)for(var a in jQuery(this).append(jQuery('<option data-mytype="all" disabled="disabled"></option>').text(rev_lang.static_layers)),initStaticLayers)initStaticLayers.hasOwnProperty(a)&&jQuery(this).append(jQuery('<option data-mytype="'+initStaticLayers[a].type+'"></option>').val("static-"+initStaticLayers[a].unique_id).text(initStaticLayers[a].alias));var i=jQuery(this).data("selectoption");jQuery(this).find('option[value="'+i+'"]').attr("selected",!0);var o=t.getAnimTimingAndTrigger(i);jQuery(this).closest("li").find('select[name="do-layer-animation-overwrite[]"] option[value="'+o.animation_overwrite+'"]').attr("selected",!0),jQuery(this).closest("li").find('select[name="do-layer-trigger-memory[]"] option[value="'+o.trigger_memory+'"]').attr("selected",!0)})},t.getAnimTimingAndTrigger=function(e){var r=t.getLayerByUniqueId(e);return{animation_overwrite:r.animation_overwrite,trigger_memory:r.trigger_memory}},t.setAnimTimingAndTrigger=function(e){var r=t.getLayerByUniqueId(e);return{animation_overwrite:r.animation_overwrite,trigger_memory:r.trigger_memory}},t.setLayerResizable=function(e){var r=!1,a=e.references.htmlLayer;(jQuery("#layer_proportional_scale")[0].checked||"svg"===e.type)&&(r=!0);try{a.resizable("destroy")}catch(e){}a.resizable({aspectRatio:r,handles:"all",start:function(t,i){switch(e.type){case"img":case"audio":case"video":var o=r?"auto":"100%";punchgs.TweenLite.set(i.element,{width:i.originalSize.width,height:i.originalSize.height}),punchgs.TweenLite.set(i.element.find("img"),{width:"100%",height:o}),punchgs.TweenLite.set(i.element.find(".innerslide_layer").first(),{width:"100%",height:o});break;default:if("svg"===e)a.find(".innerslide_layer.tp-caption").first().css({maxHeight:"none",minHeight:0,maxWidth:"none"});else{var s=a.outerWidth(),n="shape"===e.type?"none":a.outerHeight();"shape"!==e.type&&a.css({height:"auto"}),punchgs.TweenLite.set(i.element[0].getElementsByClassName("innerslide_layer")[0],{height:"auto",maxHeight:"none",minHeight:n,maxWidth:s})}}},resize:function(a,i){switch(e.type){case"audio":case"video":jQuery("#layer_video_width").val(Math.round(i.size.width)+"px"),jQuery("#layer_video_height").val(Math.round(i.size.height)+"px");var o=r?"auto":"100%";i.element[0].getElementsByTagName("img").length>0&&punchgs.TweenLite.set(i.element[0].getElementsByTagName("img")[0],{width:"100%",height:o}),punchgs.TweenLite.set(i.element[0].getElementsByClassName("innerslide_layer")[0],{maxWidth:"none",maxHeight:"none",width:"100%",height:"100%"}),punchgs.TweenLite.set(i.element[0].getElementsByClassName("slide_layer_video")[0],{width:"100%",height:"100%"});break;case"image":jQuery("#layer_scaleX").val(Math.round(i.size.width)+"px"),jQuery("#layer_scaleY").val(Math.round(i.size.height)+"px");o=r?"auto":"100%";punchgs.TweenLite.set(i.element,{width:Math.round(i.size.width),height:Math.round(i.size.height)}),punchgs.TweenLite.set(i.element[0].getElementsByTagName("img")[0],{width:"100%",height:o}),punchgs.TweenLite.set(i.element[0].getElementsByClassName("innerslide_layer")[0],{maxWidth:"100%",maxHeight:"100%",width:"100%",height:o});break;default:var s=i.element[0].getElementsByClassName("innerslide_layer")[0],n=jQuery(s),l=n.outerHeight(),d=(n.outerWidth(),Math.round(i.size.height)+1),y=Math.round(i.size.width)+1;"shape"!==e.type&&"group"!==e.type?(d=l>=d?"auto":d+"px",punchgs.TweenLite.set(s,{maxWidth:y})):punchgs.TweenLite.set(n,{maxWidth:y,maxHeight:d,minHeight:"none",overwrite:"auto",height:"100%"}),jQuery("#layer_max_width").val(y+"px"),jQuery("#layer_max_height").val(d)}"group"===e.type&&jQuery.each(t.getLayersInGroup(e.unique_id).layers,function(e,r){t.updateHtmlLayerPosition(!1,r,t.getVal(r,"top"),t.getVal(r,"left"),t.getVal(r,"align_hor"),t.getVal(r,"align_vert"))})},stop:function(e,r){layerresized=!0,setTimeout(function(){layerresized=!1},200),t.updateLayerFromFields()}})},t.resetLayerSelected=function(e){var r=e.references.htmlLayer;jQuery("#layer_cornerleft_row").hide(),jQuery("#layer_cornerright_row").hide(),jQuery("#layer_resizeme_row").hide(),jQuery("#layer_alt_row").hide(),jQuery("#layer_max_widthheight_wrapper").hide(),jQuery("#layer_video_widthheight_wrapper").hide(),jQuery("#layer_scaleXY_wrapper").hide(),jQuery("#layer_minwidthheight_wrapper").hide();var a=document.getElementById("id-esw"),i="column"===t.getObjLayerType(e.p_uid);switch(a.className="slt-"+e.type+"-w",i&&(a.className+=" sltic"),e.type){case"audio":case"video":jQuery("#linkInsertTemplate").addClass("disabled"),jQuery("#layer_video_widthheight_wrapper").show(),t.showHideContentEditor(!1),r.width()>=jQuery("#divLayers").width()&&r.height()>=jQuery("#divLayers").height()?r.addClass("fullscreen-video-layer"):r.removeClass("fullscreen-video-layer");break;case"image":jQuery("#layer_scaleXY_wrapper").show(),jQuery("#linkInsertTemplate").addClass("disabled"),jQuery("#layer_alt_row").show(),jQuery(".rs-lazy-load-images-wrap").show(),"custom"==jQuery("#layer_alt_option option:selected").val()?jQuery("#layer_alt").show():jQuery("#layer_alt").hide();break;case"svg":jQuery("#layer_max_widthheight_wrapper").show();break;case"shape":jQuery("#layer-covermode-wrapper").show(),jQuery("#layer_text_wrapper").removeClass("currently_editing_txt"),jQuery("#layer_max_widthheight_wrapper").show();break;case"group":case"row":jQuery("#layer_max_widthheight_wrapper").show();break;case"column":jQuery("#layer_minwidthheight_wrapper").show();break;case"text":case"button":default:jQuery("#layer_text_wrapper").addClass("currently_editing_txt"),jQuery("#layer_cornerleft_row").show(),jQuery("#layer_cornerright_row").show(),jQuery("#layer_resizeme_row").show(),jQuery("#layer_max_widthheight_wrapper").show()}"row"!==e.type&&"column"!==e.type&&(t.setLayerResizable(e),t.makeCurrentLayerRotatable()),jQuery("#hover_allow")[0].checked?jQuery("#idle-hover-swapper").show():jQuery("#idle-hover-swapper").hide(),RevSliderSettings.onoffStatus(jQuery("#hover_allow")),RevSliderSettings.onoffStatus(jQuery("#toggle_allow")),RevSliderSettings.onoffStatus(jQuery("#toggle_use_hover")),RevSliderSettings.onoffStatus(jQuery("#toggle_inverse_content")),jQuery("#layer_caption").catcomplete("close"),jQuery(".wp-color-result").each(function(){jQuery(this).css("backgroundColor",jQuery(this).parent().find(".my-color-field").val())}),u.rebuildLayerIdleProgress(r)},t.setLayerSelected=function(e,r,a){if(t.selectedLayerSerial==e&&!r)return!1;jQuery("#timline-manual-dialog").appendTo(jQuery("#thelayer-editor-wrapper")).hide(),t.hideRowLayoutComposer(),u.resetIdleSelector(),jQuery(".quicksortlayer.selected").removeClass("selected"),jQuery("#layer_quicksort_"+e).addClass("selected"),a||jQuery(".timer-layer-text:focus").blur(),t.remove_layer_actions();var i=t.getLayer(e);"text"!==i.type&&"button"!==i.type||(void 0!==i.deformation&&void 0!==i.deformation["font-family"]?t.check_the_font_bold(i.deformation["font-family"]):t.check_the_font_bold("")),t.showHideContentEditor(!1),t.toolbarInPos(i),unselectHtmlLayers();var o=i.references.htmlLayer,s=jQuery("#layer-short-toolbar");if(jQuery("#the_current-editing-layer-title").val(i.alias).prop("disabled",!1).removeClass("nolayerselectednow"),s.data("serial",e),u.isLayerLocked(o)){var n=(l=s.find(".quick-layer-lock")).find("i");l.addClass("in-off").removeClass("in-on"),n.removeClass("eg-icon-lock-open").addClass("eg-icon-lock")}else{n=(l=s.find(".quick-layer-lock")).find("i");l.addClass("in-on").removeClass("in-off"),n.removeClass("eg-icon-lock").addClass("eg-icon-lock-open")}if(u.isLayerVisible(o)){n=(l=s.find(".quick-layer-view")).find("i");l.addClass("in-on").removeClass("in-off"),n.removeClass("eg-icon-eye-off").addClass("eg-icon-eye")}else{var l;n=(l=s.find(".quick-layer-view")).find("i");l.addClass("in-off").removeClass("in-on"),n.removeClass("eg-icon-eye").addClass("eg-icon-eye-off")}"block"===t.getVal(i,"display")?jQuery("#layer-displaymode-wrapper").removeClass("notselected"):jQuery("#layer-displaymode-wrapper").addClass("notselected"),o.addClass("layer_selected"),o.closest(".slide_layer_type_row").addClass("layerchild_selected"),o.closest(".row-zone-container").addClass("layerrow_selected"),o.closest(".slide_layer_type_column").addClass("layerchild_selected"),u.setSortboxItemSelected(e),t.selectedLayerSerial=e,t.updateLayerFormFields(e),enableFormFields(),jQuery.each(t.addon_callbacks,function(t,r){var a=r.callback,i=r.environment,o=r.function_position;"setLayerSelected"===i&&"start"==o&&a(e)}),jQuery("#layer_text_wrapper").removeClass("currently_editing_txt"),jQuery(".rs-lazy-load-images-wrap").hide(),t.resetLayerSelected(i),checkMaskingAvailabity(),t.checkAvailableAnimationFields(),u.rebuildLayerIdle(o),reAlignAndrePosition(),jQuery(".form_layers").removeClass("notselected"),t.set_cover_mode(),UniteCssEditorRev.updateCaptionsInput(initArrCaptionClasses),t.updateReverseList(),jQuery("#parallax_level").change(),jQuery.each(t.addon_callbacks,function(t,r){var a=r.callback,i=r.environment,o=r.envifunction_position;"setLayerSelected"===i&&"end"==o&&a(e)})};var getRotationDegrees=function(e){var t=e.css("-webkit-transform")||e.css("-moz-transform")||e.css("-ms-transform")||e.css("-o-transform")||e.css("transform");if("none"!==t)var r=t.split("(")[1].split(")")[0].split(","),a=r[0],i=r[1],o=Math.round(Math.atan2(i,a)*(180/Math.PI));else o=0;return o<0?o+=360:o},isLayerSelected=function(e){return e==t.selectedLayerSerial},reAlignAndrePosition=function(){};t.checkAvailableAnimationFields=function(){jQuery.inArray(jQuery("#layer_animation").val(),["blockfromleft","blockfromright","blockfromtop","blockfrombottom"])>=0?(jQuery(".show-on-sfx_in, .hide-on-sfx_in, #add_customanimation_in").addClass("activestatus"),jQuery(".show-on-sfx_in, #add_customanimation_in").addClass("activestatus"),jQuery("#sfx_in_effect").val(jQuery("#layer_animation").val())):(jQuery(".show-on-sfx_in, .hide-on-sfx_in, #add_customanimation_in").removeClass("activestatus"),jQuery("#sfx_in_effect").val("")),jQuery.inArray(jQuery("#layer_endanimation").val(),["blocktoleft","blocktoright","blocktotop","blocktobottom"])>=0?(jQuery(".show-on-sfx_out, .hide-on-sfx_out, #add_customanimation_out").addClass("activestatus"),jQuery("#sfx_out_effect").val(jQuery("#layer_endanimation").val())):(jQuery(".show-on-sfx_out, .hide-on-sfx_out, #add_customanimation_out").removeClass("activestatus"),jQuery("#sfx_out_effect").val(""))};var getNextTime=function(){var e,r=0;for(key in t.arrLayers)if(t.arrLayers.hasOwnProperty(key)){var a=t.arrLayers[key];layerTime=a.time?Number(a.time):0,layerTime>r&&(r=layerTime)}return(e=0==r?g_startTime:Number(r)+Number(g_stepTime))>=g_slideTime&&(e=g_slideTime/2),e};t.updateHtmlLayerPosition=function(e,r,a,i,o,s){if(void 0!==r&&!1!==r){var n=r.references.htmlLayer.outerWidth(),l=r.references.htmlLayer.outerHeight();totalWidth=container.width(),totalHeight=container.height();var d=totalWidth,y=totalHeight;-1!==r.p_uid&&"group"===t.getObjLayerType(r.p_uid)&&(d=t.getLayerByUniqueId(r.p_uid).references.htmlLayer.width(),y=t.getLayerByUniqueId(r.p_uid).references.htmlLayer.height()),1==e&&"image"==r.type&&(-1!=t.getVal(r,"width")&&(n=t.getVal(r,"width")),-1!=t.getVal(r,"height")&&(l=t.getVal(r,"height")));var _={};switch(r.type){case"row":case"column":i=0,a=0}var u=-1!==r.p_uid&&"column"===t.getObjLayerType(r.p_uid);switch(_.position=u||"row"===r.type?"relative":"absolute",o){default:case"left":_.right="auto",_.left=u?"auto":i+"px";break;case"right":_.left="auto",_.right=u?"auto":i+"px";break;case"center":var c=(d-n)/2;c=Math.round(c)+i,_.left=u?"auto":c+"px",_.right="auto"}switch(s){default:case"top":_.bottom="auto",_.top=u?"auto":a+"px";break;case"middle":var m=(y-l)/2;m="row"===r.type?0:Math.round(m)+a,_.top=u?"auto":m+"px",_.bottom="auto";break;case"bottom":_.top="auto",_.bottom=u?"auto":a+"px"}punchgs.TweenLite.set(r.references.htmlLayer,_),"group"===r.type&&jQuery.each(t.getLayersInGroup(r.unique_id).layers,function(e,r){t.updateHtmlLayerPosition(!1,r,t.getVal(r,"top"),t.getVal(r,"left"),t.getVal(r,"align_hor"),t.getVal(r,"align_vert"))})}};var onLayerDragStart=function(){t.justDropped=!1,t.showHideContentEditor(!1),jQuery("#divLayers").addClass("onelayerinmove");var e=t.getSerialFromID(this.id),r=jQuery(this),a=t.getLayer(e);-1==jQuery.inArray(a.unique_id,t.selectedLayers)&&u.checkMultipleSelectedItems(!0),r.closest(".slide_layer_type_group").addClass("dragfromgroup"),r.data("originalPosition",r.position()),r.addClass("draggable_toponall"),selectedLayerWidth=r.outerWidth(),selectedlayerHeight=r.outerHeight(),totalWidth=container.width(),totalHeight=container.height(),r.closest(".slide_layer_type_column").addClass("column_from_draggable"),r.closest(".slide_layer_type_row").css({zIndex:190}),jQuery("#layer_text_wrapper").removeClass("currently_editing_txt"),t.setLayerSelected(e),t.groupMove.x=0,t.groupMove.y=0,t.groupMove.sameGroup=t.getSameLinkedElements(a)};t.getSameLinkedElements=function(e){var r=[];for(var a in t.selectedLayers)if(t.selectedLayers.hasOwnProperty(a)){var i=t.getLayerByUniqueId(t.selectedLayers[a]);i.unique_id!==e.unique_id&&i.p_uid===e.p_uid&&(i.positionCache=i.references.htmlLayer.position(),r.push(i))}return r},t.getCurLinkedElements=function(){var e=[];for(var r in t.selectedLayers)if(t.selectedLayers.hasOwnProperty(r)){var a=t.getLayerByUniqueId(t.selectedLayers[r]);e.push(a)}if(0===e.length){a=t.getLayer(t.selectedLayerSerial);e.push(a)}return e};var onLayerDragEnd=function(e,r){var a=t.getSerialFromID(this.id),i=jQuery(this);if(i.removeClass("draggable_toponall"),jQuery(".row-zone-container").removeClass("nowyouseeme"),jQuery(".row-zone-container").removeClass("layerrow_selected"),jQuery(".column_from_draggable").removeClass("column_from_draggable"),t.droppedContainerCheck(t.getLayer(a)),t.groupMove&&t.groupMove.sameGroup&&t.groupMove.sameGroup.length>0)for(var o=0;o<t.groupMove.sameGroup.length;o++){var s=t.groupMove.sameGroup[o];selectedLayerWidth=s.references.htmlLayer.outerWidth(),selectedlayerHeight=s.references.htmlLayer.outerHeight(),t.onLayerDrag("ende",s.serial,s.references.htmlLayer)}selectedLayerWidth=i.outerWidth(),selectedlayerHeight=i.outerHeight(),t.onLayerDrag("ende",a,i),jQuery(".layer_selected").each(function(){var e=jQuery(this);e.data("serial")!=a&&void 0!==a&&e.removeClass("layer_selected")}),t.setLayerSelected(a),jQuery("#divLayers").removeClass("onelayerinmove"),jQuery(".dragfromgroup").removeClass("dragfromgroup")};t.updateMovedLayers=function(e){jQuery.each(t.keyboardmovedlayers,function(e,r){void 0!==r.references&&(selectedLayerWidth=r.references.htmlLayer.outerWidth(),selectedlayerHeight=r.references.htmlLayer.outerHeight(),t.onLayerDrag("ende",r.serial,r.references.htmlLayer,!0))})},t.adjustSelectedLayerPositions=function(e,r){t.keyboardmovedlayers=t.getCurLinkedElements(),jQuery.each(t.keyboardmovedlayers,function(t,a){void 0!==a.references&&("top"==e?punchgs.TweenLite.set(a.references.htmlLayer,{top:Math.round(a.references.htmlLayer.position().top)+r}):punchgs.TweenLite.set(a.references.htmlLayer,{left:Math.round(a.references.htmlLayer.position().left)+r}))})},t.onLayerDrag=function(e,r,a,i){var o=(a=a||jQuery(this)).position(),s=Math.round(o.top),n=Math.round(o.left),l=0,d=0,y=void 0===r.originapPosition?t.getLayer(r):t.getLayer(t.selectedLayerSerial),_=totalWidth,u=totalHeight;jQuery("#layer_text_wrapper").removeClass("currently_editing_txt"),t.toolbarInPos(y);var c=y.p_uid;if("divLayers"===a.parent().attr("id")&&(y.p_uid=-1),-1!==c&&-1!==y.p_uid||c!=y.p_uid&&("row"===y.type||-1!==y.p_uid&&"column"===t.getObjLayerType(y.p_uid)?punchgs.TweenLite.set(a,{left:"auto",right:"auto",top:"auto",bottom:"auto",display:"inline-block",position:"relative"}):punchgs.TweenLite.set(a,{display:"block",position:"absolute"})),-1!==y.p_uid&&"group"===t.getObjLayerType(y.p_uid)&&(_=t.getLayerByUniqueId(y.p_uid).references.htmlLayer.width(),u=t.getLayerByUniqueId(y.p_uid).references.htmlLayer.height()),void 0!==e.offsetX&&void 0!==r.originalPosition&&t.groupMove.sameGroup.length>0)for(var m=r.originalPosition.left-r.position.left,p=r.originalPosition.top-r.position.top,v=0;v<t.groupMove.sameGroup.length;v++){var g=t.groupMove.sameGroup[v];punchgs.TweenLite.set(g.references.htmlLayer,{left:g.positionCache.left-m,top:g.positionCache.top-p})}switch(t.getVal(y,"align_hor")){case"left":d=n;break;case"right":d=_-n-selectedLayerWidth;break;case"center":d=n-Math.round((_-selectedLayerWidth)/2),d=Math.round(d);break;case"left":default:d=n}switch(t.getVal(y,"align_vert")){case"bottom":l=u-s-selectedlayerHeight;break;case"middle":l=s-Math.round((u-selectedlayerHeight)/2),l=Math.round(l);break;case"top":default:l=s}if(-1!==y.p_uid&&"column"===t.getObjLayerType(y.p_uid)&&(d=0,l=0),jQuery("#layer_left").val(d+"px"),jQuery("#layer_top").val(l+"px"),"ende"===e){var h={};h=t.setVal(h,"left",d),h=t.setVal(h,"top",l),h=t.setVal(h,"width",selectedLayerWidth),h=t.setVal(h,"height",selectedlayerHeight),t.updateLayer(r,h),t.updateHtmlLayerPosition(!1,y,t.getVal(h,"top"),t.getVal(h,"left"),t.getVal(y,"align_hor"),t.getVal(y,"align_vert")),isLayerSelected(r)&&t.updateLayerFormFields(r)}},t.getObjLayerType=function(e){var r=t.getLayerByUniqueId(e);return!!r.type&&r.type};var scaleAndResetLayerInit=function(){jQuery("#layer_scaleX").change(function(){jQuery("#layer_proportional_scale")[0].checked?scaleProportional(!0):scaleNormal()}),jQuery("#layer_scaleY").change(function(){jQuery("#layer_proportional_scale")[0].checked?scaleProportional(!1):scaleNormal()}),jQuery("#layer_video_width").change(function(){jQuery("#layer_proportional_scale")[0].checked?scaleProportionalVideo(!0):scaleNormalVideo()}),jQuery("#layer_video_height").change(function(){jQuery("#layer_proportional_scale")[0].checked?scaleProportionalVideo(!1):scaleNormalVideo()}),jQuery("#layer_proportional_scale").click(function(){var e=t.getLayer(t.selectedLayerSerial);this.checked?(jQuery('#layer_cover_mode option[value="custom"]').attr("selected",!0),jQuery(".rs-proportion-check").removeClass("notselected"),"video"===e.type||"audio"===e.type?scaleProportionalVideo(!1):"image"===e.type&&scaleProportional(!0)):(jQuery(".rs-proportion-check").addClass("notselected"),"video"===e.type||"audio"===e.type?scaleNormalVideo(!1):"image"===e.type&&scaleNormal()),t.setLayerResizable(e)}),jQuery("#layer_auto_line_break").click(function(){t.clickOnAutoLineBreak()}),jQuery("#layer_displaymode").click(function(){jQuery(this)[0].checked?jQuery("#layer_display option[value='block']").attr("selected","selected"):jQuery("#layer_display option[value='inline-block']").attr("selected","selected")}),jQuery("#layer_cover_mode").change(function(){t.set_cover_mode()}),jQuery("#reset-scale, #button_reset_size").click(resetCurrentElementSize)};t.clickOnAutoLineBreak=function(){var e=t.selectedLayerSerial,r=jQuery("#slide_layer_"+e);t.getLayer(t.selectedLayerSerial);if(jQuery("#layer_auto_line_break")[0].checked){jQuery(".rs-linebreak-check").removeClass("notselected"),jQuery("#layer_whitespace option[value='normal']").attr("selected","selected");var a=r.outerWidth()+1,i=r.outerHeight()+1;r.css({height:"auto"}),r.find(".innerslide_layer.tp-caption").first().css({maxHeight:"none",minHeight:i,maxWidth:a}),jQuery("#layer_max_width").val(a+"px"),jQuery("#layer_max_height").val("auto")}else jQuery(".rs-linebreak-check").addClass("notselected"),jQuery("#layer_whitespace option[value='nowrap']").attr("selected","selected"),jQuery("#layer_max_width").val("auto"),jQuery("#layer_max_height").val("auto"),r.css({width:"auto"}),r.find(".innerslide_layer.tp-caption").first().css({maxHeight:"none",minHeight:"none",maxWidth:"none"});t.updateLayerFromFields()};var resetCurrentElementSize=function(){var e=t.getLayer(t.selectedLayerSerial);if("shape"==e.type)return!1;if("svg"==e.type)return!1;if("audio"==e.type)return!1;if("text"==e.type||"button"==e.type){var r=jQuery(".slide_layer.layer_selected .innerslide_layer").first().outerWidth();parseInt(jQuery("#layer_max_width").val(),0)>r&&(r=void 0===r?"auto":r+"px",jQuery("#layer_max_width").val("auto")),jQuery("#layer_max_height").val("auto")}else{"image"==e.type&&jQuery('#layer_cover_mode option[value="custom"]').attr("selected",!0);var a=resetImageDimensions();jQuery("#layer_proportional_scale").attr("checked",!0),jQuery("#layer_scaleX_text").html(jQuery("#layer_scaleX_text").data("textnormal")).css("width","10px");var i=null!=e.image_librarysize?e.image_librarysize.width:a.width,o=null!=e.image_librarysize?e.image_librarysize.height:a.height;jQuery("#layer_scaleX").val(i),jQuery("#layer_scaleY").val(o),jQuery("#slide_layer_"+t.selectedLayerSerial+" img").css("width",i),jQuery("#slide_layer_"+t.selectedLayerSerial+" img").css("height",o)}t.updateLayerFromFields()},scaleProportional=function(e){var r=t.selectedLayerSerial;resetImageDimensions();var a,i,o,s,n,l=new Image;l.src=jQuery("#slide_layer_"+r+" img").attr("src"),e?(n=jQuery("#layer_scaleX").val(),a=parseFloat(n),o=n.replace(a,""),isNaN(a)&&(a="%"===o?100:l.width),i="%"===o?"auto":Math.round(100/l.width*a/100*l.height,0),s=""):(n=jQuery("#layer_scaleY").val(),i=parseFloat(n),s=n.replace(i,""),isNaN(i)&&(i="%"===s?100:l.height),a="%"===s?"auto":Math.round(100/l.height*i/100*l.width,0),o=""),jQuery("#slide_layer_"+r+" img").css("width",a+o),jQuery("#slide_layer_"+r+" img").css("height",i+s),jQuery("#slide_layer_"+r).css("width",jQuery("#slide_layer_"+r+" img").width()+o),jQuery("#slide_layer_"+r).css("height",jQuery("#slide_layer_"+r+" img").height()+s),jQuery("#slide_layer_"+r+" img").css("width","100%"),jQuery("#slide_layer_"+r+" img").css("height","100%"),jQuery("#layer_scaleX").val(a+o),jQuery("#layer_scaleY").val(i+s)},scaleNormal=function(){var e=t.selectedLayerSerial,r=(resetImageDimensions(),jQuery("#slide_layer_"+e)),a=jQuery("#layer_scaleX").val(),i=jQuery("#layer_scaleY").val();a=jQuery.isNumeric(a)?a+"px":a,i=jQuery.isNumeric(i)?i+"px":i,punchgs.TweenLite.set(r,{width:a,height:i}),punchgs.TweenLite.set(r.find(".innerslide_layer").first(),{width:a,height:i}),punchgs.TweenLite.set(r.find("img"),{width:a,height:i})},scaleProportionalVideo=function(e){var r=t.selectedLayerSerial,a=jQuery("#slide_layer_"+r).find(".slide_layer_video");if(e){var i=parseInt(jQuery("#layer_video_width").val());isNaN(i)&&(i=a.width());var o=Math.round(100/a.width()*i/100*a.height(),0)}else{o=parseInt(jQuery("#layer_video_height").val());isNaN(o)&&(o=a.height());i=Math.round(100/a.height()*o/100*a.width(),0)}a.css("width",i+"px"),a.css("height",o+"px"),jQuery("#slide_layer_"+r).css("width",i+"px"),jQuery("#slide_layer_"+r).css("height",o+"px"),jQuery("#layer_video_width").val(i),jQuery("#layer_video_height").val(o)},scaleNormalVideo=function(){var e=t.selectedLayerSerial,r=jQuery("#slide_layer_"+e).find(".slide_layer_video"),a=jQuery("#layer_video_width").val(),i=jQuery("#layer_video_height").val();a=jQuery.isNumeric(a)?a+"px":a,i=jQuery.isNumeric(i)?i+"px":i,r.css("width",a),r.css("height",i),jQuery("#slide_layer_"+e).css("width",a),jQuery("#slide_layer_"+e).css("height",i)},resetImageDimensions=function(){var e=new Image;return e.src=jQuery("#slide_layer_"+t.selectedLayerSerial+" img").attr("src"),jQuery("#slide_layer_"+t.selectedLayerSerial).css("width",e.width+"px"),jQuery("#slide_layer_"+t.selectedLayerSerial).css("height",e.height+"px"),{width:e.width,height:e.height}};t.getLayerGeneralParamsStatus=function(){return layerGeneralParamsStatus},t.startKenBurn=function(e){var t=jQuery("#ken_burn_example"),r=t.data(),a=(t.find(".defaultimg"),r.lastsrc),i=(r.owidth,r.oheight,t.width()),o=t.height();if(t.data("kbtl")&&t.data("kbtl").kill(),e=e||0,void 0!==a){t.find(".tp-kbimg").remove(),0==t.find(".tp-kbimg").length&&(t.append('<div class="tp-kbimg-wrap" style="z-index:2;width:100%;height:100%;top:0px;left:0px;position:absolute;"><img class="tp-kbimg" src="'+a+'" style="position:absolute;" width="'+r.owidth+'" height="'+r.oheight+'"></div>'),t.data("kenburn",t.find(".tp-kbimg")));var s=function(e,t,r,a,i,o,s){var n=e*r,l=t*r,d=Math.abs(a-n),y=Math.abs(i-l),_=new Object;return _.l=(0-o)*d,_.r=_.l+n,_.t=(0-s)*y,_.b=_.t+l,_.h=o,_.v=s,_};null!=t.data("kbtl")&&(t.data("kbtl").kill(),t.removeData("kbtl"));var n=t.data("kenburn"),l=n.parent(),d=function(e,t,r){var a=r.scalestart/100,i=r.scaleend/100,o=null!=r.offsetstart&&r.offsetstart.split(" ")||[0,0],n=null!=r.offsetend&&r.offsetend.split(" ")||[0,0];r.bgposition="center center"==r.bgposition?"50% 50%":r.bgposition;var l=new Object;r.owidth,r.oheight,r.owidth,r.oheight;if(l.start=new Object,l.starto=new Object,l.end=new Object,l.endo=new Object,l.start.width=e,l.start.height=l.start.width/r.owidth*r.oheight,l.start.height<t){var d=t/l.start.height;l.start.height=t,l.start.width=l.start.width*d}l.start.transformOrigin=r.bgposition,l.start.scale=a,l.end.scale=i,l.start.rotation=r.rotatestart+"deg",l.end.rotation=r.rotateend+"deg";var y=function(e,t,r,a,i){var o=e.bgposition.split(" ")||"center center",n="center"==o[0]?"50%":"left"==o[0]||"left"==o[1]?"0%":"right"==o[0]||"right"==o[1]?"100%":o[0],l="center"==o[1]?"50%":"top"==o[0]||"top"==o[1]?"0%":"bottom"==o[0]||"bottom"==o[1]?"100%":o[1];n=parseInt(n,0)/100||0,l=parseInt(l,0)/100||0;var d=new Object;return d.start=s(i.start.width,i.start.height,i.start.scale,t,r,n,l),d.end=s(i.start.width,i.start.height,i.end.scale,t,r,n,l),d}(r,e,t,0,l);o[0]=parseFloat(o[0])+y.start.l,n[0]=parseFloat(n[0])+y.end.l,o[1]=parseFloat(o[1])+y.start.t,n[1]=parseFloat(n[1])+y.end.t;var _=y.start.r-y.start.l,u=y.start.b-y.start.t,c=y.end.r-y.end.l,m=y.end.b-y.end.t;return o[0]=o[0]>0?0:_+o[0]<e?e-_:o[0],n[0]=n[0]>0?0:c+n[0]<e?e-c:n[0],l.starto.x=o[0]+"px",l.endo.x=n[0]+"px",o[1]=o[1]>0?0:u+o[1]<t?t-u:o[1],n[1]=n[1]>0?0:m+n[1]<t?t-m:n[1],l.starto.y=o[1]+"px",l.endo.y=n[1]+"px",l.end.ease=l.endo.ease=r.ease,l.end.force3D=l.endo.force3D=!0,l}(i,o,r),y=new punchgs.TimelineLite;y.pause(),d.start.transformOrigin="0% 0%",d.starto.transformOrigin="0% 0%",y.add(punchgs.TweenLite.fromTo(n,r.duration/1e3,d.start,d.end),0),y.add(punchgs.TweenLite.fromTo(l,r.duration/1e3,d.starto,d.endo),0),y.progress(e),jQuery("#kenburn-playpause-wrapper").hasClass("playing")?y.play(0):y.pause(),y.eventCallback("onComplete",function(){y.play(0)}),t.data("kbtl",y)}},t.updateKenBurnExampleValues=function(){var e=new Image,r=jQuery("#ken_burn_slot_example").data("src"),a=jQuery("#ken_burn_example").data("lastsrc");jQuery("#ken_burn_slot_example").data("inload")||(r!==a?(jQuery("#ken_burn_slot_example").data("inload",!0),e.onload=function(){jQuery("#ken_burn_example").data({lastsrc:this.src,owidth:this.width,oheight:this.height,bgposition:jQuery("#slide_bg_position").val(),duration:parseInt(jQuery("#kb_duration").val(),0),rotatestart:parseInt(jQuery("#kb_start_rotate").val(),0),rotateend:parseInt(jQuery("#kb_end_rotate").val(),0),scalestart:parseInt(jQuery("#kb_start_fit").val(),0),scaleend:parseInt(jQuery("#kb_end_fit").val(),0),offsetstart:jQuery("#kb_start_offset_x").val()+" "+jQuery("#kb_start_offset_y").val(),offsetend:jQuery("#kb_end_offset_x").val()+" "+jQuery("#kb_end_offset_y").val(),blurstart:jQuery("#kb_blur_start").val(),blurend:jQuery("#kb_blur_end").val(),ease:jQuery("#kb_easing").val()}),jQuery("#ken_burn_slot_example").data("inload",!1),t.startKenBurn()},e.onerror=function(){console.log("Ken Burn Demo Image could not be Loaded")},e.onabort=function(){console.log("Ken Burn Demo Image could not be Loaded")},e.src=r):(jQuery("#ken_burn_example").data({lastsrc:jQuery("#ken_burn_slot_example").data("src"),owidth:jQuery("#ken_burn_example").data("owdith"),oheight:jQuery("#ken_burn_example").data("oheight"),bgposition:jQuery("#slide_bg_position").val(),duration:parseInt(jQuery("#kb_duration").val(),0),rotatestart:parseInt(jQuery("#kb_start_rotate").val(),0),rotateend:parseInt(jQuery("#kb_end_rotate").val(),0),scalestart:parseInt(jQuery("#kb_start_fit").val(),0),scaleend:parseInt(jQuery("#kb_end_fit").val(),0),offsetstart:jQuery("#kb_start_offset_x").val()+" "+jQuery("#kb_start_offset_y").val(),offsetend:jQuery("#kb_end_offset_x").val()+" "+jQuery("#kb_end_offset_y").val(),blurstart:jQuery("#kb_blur_start").val(),blurend:jQuery("#kb_blur_end").val(),ease:jQuery("#kb_easing").val()}),t.startKenBurn()))};var buildKenBurnExample=function(){var e=jQuery("#ken_burn_example"),r=(jQuery("#ken_burn_slot_example"),jQuery("#divLayers")),a=e.width()/r.width()*r.height();e.css({height:a+"px"}),t.updateKenBurnExampleValues()},initBackgroundFunctions=function(){jQuery("body").on("change",'select[name="layer_target[]"]',function(){jQuery(this).data("selectoption",jQuery(this).find("option:selected").val());var e=t.getAnimTimingAndTrigger(jQuery(this).find("option:selected").val());jQuery(this).closest("li").find('select[name="do-layer-animation-overwrite[]"] option[value="'+e.animation_overwrite+'"]').attr("selected",!0),jQuery(this).closest("li").find('select[name="do-layer-trigger-memory[]"] option[value="'+e.trigger_memory+'"]').attr("selected",!0)}),jQuery("body").on("change",'select[name="jump_to_slide[]"]',function(){jQuery(this).data("selectoption",jQuery(this).find("option:selected").val())}),jQuery("body").on("change",'select[name="do-layer-animation-overwrite[]"]',function(){var e=jQuery(this).closest("li").find('select[name="layer_target[]"] option:selected').val(),r=jQuery(this).val(),a=t.getLayerIdByUniqueId(e),i={};i.animation_overwrite=r,update_layer_changes=!1,t.updateLayer(a,i),update_layer_changes=!0,t.getLayer(t.selectedLayerSerial).unique_id==e&&jQuery('#layer-animation-overwrite option[value="'+r+'"]').attr("selected",!0),jQuery('select[name="layer_target[]"] option:selected').each(function(){jQuery(this).val()==e&&jQuery(this).closest("li").find('select[name="do-layer-animation-overwrite[]"] option[value="'+r+'"]').attr("selected",!0)})}),jQuery("body").on("change",'select[name="do-layer-trigger-memory[]"]',function(){var e=jQuery(this).closest("li").find('select[name="layer_target[]"] option:selected').val(),r=jQuery(this).val(),a=t.getLayerIdByUniqueId(e),i={};i.trigger_memory=r,update_layer_changes=!1,t.updateLayer(a,i),update_layer_changes=!0,t.getLayer(t.selectedLayerSerial).unique_id==e&&jQuery('#layer-tigger-memory option[value="'+r+'"]').attr("selected",!0),jQuery('select[name="layer_target[]"] option:selected').each(function(){jQuery(this).val()==e&&jQuery(this).closest("li").find('select[name="do-layer-trigger-memory[]"] option[value="'+r+'"]').attr("selected",!0)})}),jQuery("#slide_bg_fit").change(function(){"percentage"==jQuery(this).val()?(jQuery('input[name="bg_fit_x"]').show(),jQuery('input[name="bg_fit_y"]').show(),jQuery("#divbgholder").css("background-size",jQuery('input[name="bg_fit_x"]').val()+"% "+jQuery('input[name="bg_fit_y"]').val()+"%")):(jQuery('input[name="bg_fit_x"]').hide(),jQuery('input[name="bg_fit_y"]').hide(),jQuery("#divbgholder").css("background-size",jQuery(this).val())),"contain"==jQuery(this).val()?jQuery("#divLayers-wrapper").css("maxWidth",jQuery("#divbgholder").css("minWidth")):jQuery("#divLayers-wrapper").css("maxWidth","100%")}),jQuery("#slide_bg_fit").change(),jQuery('input[name="bg_fit_x"]').change(function(){jQuery("#divbgholder").css("background-size",jQuery('input[name="bg_fit_x"]').val()+"% "+jQuery('input[name="bg_fit_y"]').val()+"%")}),jQuery('input[name="bg_fit_y"]').change(function(){jQuery("#divbgholder").css("background-size",jQuery('input[name="bg_fit_x"]').val()+"% "+jQuery('input[name="bg_fit_y"]').val()+"%")}),jQuery("#slide_bg_position").change(function(){"percentage"==jQuery(this).val()?(jQuery('input[name="bg_position_x"]').show(),jQuery('input[name="bg_position_y"]').show(),jQuery("#divbgholder").css("background-position",jQuery('input[name="bg_fit_x"]').val()+"% "+jQuery('input[name="bg_fit_y"]').val()+"%")):(jQuery('input[name="bg_position_x"]').hide(),jQuery('input[name="bg_position_y"]').hide(),jQuery("#divbgholder").css("background-position",jQuery(this).val()))}),jQuery('input[name="bg_position_x"]').change(function(){jQuery("#divbgholder").css("background-position",jQuery('input[name="bg_position_x"]').val()+"% "+jQuery('input[name="bg_position_y"]').val()+"%")}),jQuery('input[name="bg_position_y"]').change(function(){jQuery("#divbgholder").css("background-position",jQuery('input[name="bg_position_x"]').val()+"% "+jQuery('input[name="bg_position_y"]').val()+"%")}),jQuery("#slide_bg_repeat").change(function(){jQuery("#divbgholder").css("background-repeat",jQuery(this).val())}),jQuery('input[name="kenburn_effect"]').change(function(){jQuery(this)[0].checked?(jQuery("#kenburn_wrapper").show(),jQuery("#divbgholder").css("background-repeat",""),jQuery("#divbgholder").css("background-position",""),jQuery("#divbgholder").css("background-size",""),jQuery('input[name="kb_start_fit"]').change(),jQuery("#divLayers-wrapper").css("maxWidth","none"),jQuery("#slide_bg_position").change(),jQuery("#bg-setting-bgfit-wrap").css({display:"none"}),jQuery("#bg-setting-bgrep-wrap").css({display:"none"}),jQuery("#bg-setting-bgpos-wrap").prependTo("#kenburn_wrapper"),buildKenBurnExample()):(jQuery("#kenburn_wrapper").hide(),jQuery("#slide_bg_repeat").change(),jQuery("#slide_bg_position").change(),jQuery("#slide_bg_fit").change(),jQuery("#bg-setting-bgfit-wrap").show(),jQuery("#bg-setting-bgrep-wrap").show(),jQuery("#bg-setting-bgpos-wrap").appendTo("#bg-setting-bgpos-def-wrap"),"contain"==jQuery("#slide_bg_fit").val()?jQuery("#divLayers-wrapper").css("maxWidth",jQuery("#divbgholder").css("minWidth")):jQuery("#divLayers-wrapper").css("maxWidth","100%")),t.changeSlotBGs()}),jQuery('input[name="kenburn_effect"]:checked').change(),jQuery("body").on("mouseenter",".inst-filter-griditem",function(){punchgs.TweenLite.to(jQuery(this).find(".inst-filter-griditem-img"),.5,{autoAlpha:0})}),jQuery("body").on("mouseleave",".inst-filter-griditem",function(){punchgs.TweenLite.to(jQuery(this).find(".inst-filter-griditem-img"),.5,{autoAlpha:1})}),jQuery("body").on("click",".inst-filter-griditem",function(){var e=jQuery(this);jQuery("#media-filter-type option:selected").prop("selected",!1),jQuery('#media-filter-type option[value="'+e.data("type")+'"]').attr("selected","selected"),jQuery(".inst-filter-griditem.selected").removeClass("selected"),e.addClass("selected"),jQuery(".oldslotholder").attr("class","oldslotholder "+e.data("type")),jQuery(".slotholder").attr("class","slotholder "+e.data("type"))});var e=jQuery("#media-filter-type option:selected").val();void 0!==e&&(jQuery(".inst-filter-griditem.selected").removeClass("selected"),jQuery(".inst-filter-griditem.filter_"+e).addClass("selected"),jQuery(".oldslotholder").attr("class","oldslotholder "+e),jQuery(".slotholder").attr("class","slotholder "+e)),jQuery("#slide_bg_end_position").change(function(){"percentage"==jQuery(this).val()?(jQuery('input[name="bg_end_position_x"]').show(),jQuery('input[name="bg_end_position_y"]').show()):(jQuery('input[name="bg_end_position_x"]').hide(),jQuery('input[name="bg_end_position_y"]').hide())}),jQuery('input[name="kb_start_fit"]').change(function(){var e=parseInt(jQuery(this).val()),a=new Image;a.onload=function(){r(e,a.width,a.height,jQuery("#divbgholder"))};var i="";jQuery("#radio_back_image")[0].checked?i=jQuery("#image_url").val():jQuery("#radio_back_external")[0].checked&&(i=jQuery("#slide_bg_external").val()),""!=i&&(a.src=i),t.changeSlotBGs()});var r=function(e,r,a,i){var o=r,s=a;i.width(),i.height();e+="%";jQuery("#divbgholder").css("background-size",e+" auto"),t.changeSlotBGs()};jQuery("#layer_resize-full").change(function(){jQuery(this)[0].checked||(jQuery("#layer_resizeme").prop("checked",!1),RevSliderSettings.onoffStatus(jQuery("#layer_resizeme")))}),jQuery("#layer_resizeme").change(function(){jQuery(this)[0].checked&&(jQuery("#layer_resize-full").prop("checked",!0),RevSliderSettings.onoffStatus(jQuery("#layer_resize-full")))}),jQuery(window).resize(function(){if("on"==jQuery('input[name="kenburn_effect"]:checked').val()){var e=parseInt(jQuery('input[name="kb_start_fit"]').val()),a=new Image;a.onload=function(){r(e,a.width,a.height,jQuery("#divbgholder"))};var i="";jQuery("#radio_back_image")[0].checked?i=jQuery("#image_url").val():jQuery("#radio_back_external")[0].checked&&(i=jQuery("#slide_bg_external").val()),""!=i&&(a.src=i)}t.setObjectLibraryHeight()})},initLoopFunctions=function(){jQuery('select[name="layer_loop_animation"]').change(function(){showHideLoopFunctions()}),jQuery("#layer_static_start").change(function(){changeEndStaticFunctions()})},showHideLoopFunctions=function(){jQuery('select[name="layer_loop_animation"]').each(function(){switch(jQuery("#layer_easing_wrapper").hide(),jQuery("#layer_speed_wrapper").hide(),jQuery("#layer_parameters_wrapper").hide(),jQuery("#layer_degree_wrapper").hide(),jQuery("#layer_origin_wrapper").hide(),jQuery("#layer_x_wrapper").hide(),jQuery("#layer_y_wrapper").hide(),jQuery("#layer_zoom_wrapper").hide(),jQuery("#layer_angle_wrapper").hide(),jQuery("#layer_radius_wrapper").hide(),jQuery(this).val()){case"none":break;case"rs-pendulum":case"rs-rotate":jQuery("#layer_easing_wrapper").show(),jQuery("#layer_speed_wrapper").show(),jQuery("#layer_parameters_wrapper").show(),jQuery("#layer_degree_wrapper").show(),jQuery("#layer_origin_wrapper").show();break;case"rs-slideloop":jQuery("#layer_easing_wrapper").show(),jQuery("#layer_speed_wrapper").show(),jQuery("#layer_parameters_wrapper").show(),jQuery("#layer_x_wrapper").show(),jQuery("#layer_y_wrapper").show();break;case"rs-pulse":jQuery("#layer_easing_wrapper").show(),jQuery("#layer_speed_wrapper").show(),jQuery("#layer_parameters_wrapper").show(),jQuery("#layer_zoom_wrapper").show();break;case"rs-wave":jQuery("#layer_speed_wrapper").show(),jQuery("#layer_parameters_wrapper").show(),jQuery("#layer_angle_wrapper").show(),jQuery("#layer_radius_wrapper").show(),jQuery("#layer_origin_wrapper").show()}})},changeEndStaticFunctions=function(){jQuery("#layer_static_start").each(function(){var e=parseInt(jQuery(this).val()),t=jQuery("#layer_static_end option:selected").val(),r=parseInt(jQuery("#layer_static_start option:last-child").val());jQuery("#layer_static_end").empty();for(var a=e+1;a<=r;a++)jQuery("#layer_static_end").append('<option value="'+a+'">'+a+"</option>");jQuery("#layer_static_end").append('<option value="last">'+rev_lang.last_slide+"</option>"),jQuery("#layer_static_end option[value='"+t+"']").attr("selected","selected")})};t.get_current_selected_layer=function(){return t.selectedLayerSerial},t.addPreventLeave=function(){window.onbeforeunload=function(e){if(save_needed){var t=rev_lang.leave_not_saved;return(e=e||window.event)&&(e.returnValue=t),t}}}}}($nwd_jQuery);