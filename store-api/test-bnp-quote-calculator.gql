# Test queries for the new BNP Quote Calculator resolver

# Test 1: Get credit calculator for quote
query TestGetCreditCalculatorBNPParibasForQuote {
  getCreditCalculatorBNPParibasForQuote(
    cartToken: "your-cart-token-here",
    downPayment: 100.0
  ) {
    schemeId
    variants {
      id
      apr
      correctDownpaymentAmount
      installmentAmount
      maturity
      nir
      pricingSchemeId
      pricingSchemeName
      totalRepaymentAmount
    }
  }
}

# Test 2: Compare with original single product calculator
query TestGetCreditCalculatorBNPParibas {
  getCreditCalculatorBNPParibas(
    sku: "your-product-sku-here",
    downPayment: 100.0,
    qty: 1
  ) {
    schemeId
    variants {
      id
      apr
      correctDownpaymentAmount
      installmentAmount
      maturity
      nir
      pricingSchemeId
      pricingSchemeName
      totalRepaymentAmount
    }
  }
}
