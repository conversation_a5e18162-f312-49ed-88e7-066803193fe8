version: prak-dev-1.0.0
app:
  module:
    name: praktis.bg/store-api
    root_path: "${cwd}"

outputType: file

entity:
  package:
    alias: entity
    path: praktis.bg/store-api/graphql/entity
  db:
    package:
      path: praktis.bg/store-api/internal/praktis
      alias: praktis
    use_func: GetDbClient()
  cache:
    package:
      path: github.com/siper92/api-base/cache
      alias: redis
    use_func: praktis.bg/store-api/internal/praktis.GetCacheClient()
  model:
    path: praktis.bg/store-api/graphql/model

custom_imports:
  - path: "praktis.bg/store-api/internal/auth"