//go:build seeder

package main

import (
	"flag"
	"github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/entity"
	"praktis.bg/store-api/internal/config"
	"praktis.bg/store-api/packages/magento-core/storage"
)

func main() {
	file := flag.String("c", ".conf.yaml", "Config file")
	flag.Parse()

	core_utils.Debug("Init Config: %s", *file)
	config.LoadConfig(*file)

	configInfo := config.GetConfig()

	dbAdapter, err := storage.InitNewConnection(
		configInfo.Database.GetDNS(), true, 100,
	)
	core_utils.StopOnError(err)

	core_utils.PrintMessage("Migrating database...")
	core_utils.StopOnError(dbAdapter.GetConn().AutoMigrate(
		&entity.PraktisStoreEntity{},
		//&entity.StoreAvailabilityEntity{},
		&entity.StoreImageEntity{},
	))
	core_utils.PrintMessage("Seeding database...")
}
