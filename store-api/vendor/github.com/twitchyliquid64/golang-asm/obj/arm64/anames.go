// Code generated by stringer -i a.out.go -o anames.go -p arm64; DO NOT EDIT.

package arm64

import "github.com/twitchyliquid64/golang-asm/obj"

var Anames = []string{
	obj.A_ARCHSPECIFIC: "ADC",
	"ADCS",
	"ADCSW",
	"ADCW",
	"ADD",
	"ADDS",
	"ADDSW",
	"ADDW",
	"ADR",
	"ADRP",
	"AND",
	"ANDS",
	"ANDSW",
	"ANDW",
	"ASR",
	"ASRW",
	"AT",
	"BFI",
	"BFIW",
	"BFM",
	"BFMW",
	"BFXIL",
	"BFXILW",
	"BIC",
	"BICS",
	"BICSW",
	"BICW",
	"BR<PERSON>",
	"<PERSON><PERSON>",
	"CBNZ<PERSON>",
	"CBZ",
	"CBZW",
	"CCMN",
	"CCMNW",
	"CCMP",
	"CCMPW",
	"CINC",
	"CINCW",
	"CINV",
	"CINVW",
	"CLREX",
	"<PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>Z<PERSON>",
	"CM<PERSON>",
	"<PERSON><PERSON>N<PERSON>",
	"C<PERSON>",
	"CMPW",
	"CNEG",
	"<PERSON>NEG<PERSON>",
	"CRC32B",
	"CRC32CB",
	"CRC32CH",
	"CRC32CW",
	"CRC32CX",
	"CRC32H",
	"CRC32W",
	"CRC32X",
	"CSEL",
	"CSELW",
	"CSET",
	"CSETM",
	"CSETMW",
	"CSETW",
	"CSINC",
	"CSINCW",
	"CSINV",
	"CSINVW",
	"CSNEG",
	"CSNEGW",
	"DC",
	"DCPS1",
	"DCPS2",
	"DCPS3",
	"DMB",
	"DRPS",
	"DSB",
	"EON",
	"EONW",
	"EOR",
	"EORW",
	"ERET",
	"EXTR",
	"EXTRW",
	"HINT",
	"HLT",
	"HVC",
	"IC",
	"ISB",
	"LDADDAB",
	"LDADDAD",
	"LDADDAH",
	"LDADDAW",
	"LDADDALB",
	"LDADDALD",
	"LDADDALH",
	"LDADDALW",
	"LDADDB",
	"LDADDD",
	"LDADDH",
	"LDADDW",
	"LDADDLB",
	"LDADDLD",
	"LDADDLH",
	"LDADDLW",
	"LDANDAB",
	"LDANDAD",
	"LDANDAH",
	"LDANDAW",
	"LDANDALB",
	"LDANDALD",
	"LDANDALH",
	"LDANDALW",
	"LDANDB",
	"LDANDD",
	"LDANDH",
	"LDANDW",
	"LDANDLB",
	"LDANDLD",
	"LDANDLH",
	"LDANDLW",
	"LDAR",
	"LDARB",
	"LDARH",
	"LDARW",
	"LDAXP",
	"LDAXPW",
	"LDAXR",
	"LDAXRB",
	"LDAXRH",
	"LDAXRW",
	"LDEORAB",
	"LDEORAD",
	"LDEORAH",
	"LDEORAW",
	"LDEORALB",
	"LDEORALD",
	"LDEORALH",
	"LDEORALW",
	"LDEORB",
	"LDEORD",
	"LDEORH",
	"LDEORW",
	"LDEORLB",
	"LDEORLD",
	"LDEORLH",
	"LDEORLW",
	"LDORAB",
	"LDORAD",
	"LDORAH",
	"LDORAW",
	"LDORALB",
	"LDORALD",
	"LDORALH",
	"LDORALW",
	"LDORB",
	"LDORD",
	"LDORH",
	"LDORW",
	"LDORLB",
	"LDORLD",
	"LDORLH",
	"LDORLW",
	"LDP",
	"LDPW",
	"LDPSW",
	"LDXR",
	"LDXRB",
	"LDXRH",
	"LDXRW",
	"LDXP",
	"LDXPW",
	"LSL",
	"LSLW",
	"LSR",
	"LSRW",
	"MADD",
	"MADDW",
	"MNEG",
	"MNEGW",
	"MOVK",
	"MOVKW",
	"MOVN",
	"MOVNW",
	"MOVZ",
	"MOVZW",
	"MRS",
	"MSR",
	"MSUB",
	"MSUBW",
	"MUL",
	"MULW",
	"MVN",
	"MVNW",
	"NEG",
	"NEGS",
	"NEGSW",
	"NEGW",
	"NGC",
	"NGCS",
	"NGCSW",
	"NGCW",
	"NOOP",
	"ORN",
	"ORNW",
	"ORR",
	"ORRW",
	"PRFM",
	"PRFUM",
	"RBIT",
	"RBITW",
	"REM",
	"REMW",
	"REV",
	"REV16",
	"REV16W",
	"REV32",
	"REVW",
	"ROR",
	"RORW",
	"SBC",
	"SBCS",
	"SBCSW",
	"SBCW",
	"SBFIZ",
	"SBFIZW",
	"SBFM",
	"SBFMW",
	"SBFX",
	"SBFXW",
	"SDIV",
	"SDIVW",
	"SEV",
	"SEVL",
	"SMADDL",
	"SMC",
	"SMNEGL",
	"SMSUBL",
	"SMULH",
	"SMULL",
	"STXR",
	"STXRB",
	"STXRH",
	"STXP",
	"STXPW",
	"STXRW",
	"STLP",
	"STLPW",
	"STLR",
	"STLRB",
	"STLRH",
	"STLRW",
	"STLXP",
	"STLXPW",
	"STLXR",
	"STLXRB",
	"STLXRH",
	"STLXRW",
	"STP",
	"STPW",
	"SUB",
	"SUBS",
	"SUBSW",
	"SUBW",
	"SVC",
	"SXTB",
	"SXTBW",
	"SXTH",
	"SXTHW",
	"SXTW",
	"SYS",
	"SYSL",
	"TBNZ",
	"TBZ",
	"TLBI",
	"TST",
	"TSTW",
	"UBFIZ",
	"UBFIZW",
	"UBFM",
	"UBFMW",
	"UBFX",
	"UBFXW",
	"UDIV",
	"UDIVW",
	"UMADDL",
	"UMNEGL",
	"UMSUBL",
	"UMULH",
	"UMULL",
	"UREM",
	"UREMW",
	"UXTB",
	"UXTH",
	"UXTW",
	"UXTBW",
	"UXTHW",
	"WFE",
	"WFI",
	"YIELD",
	"MOVB",
	"MOVBU",
	"MOVH",
	"MOVHU",
	"MOVW",
	"MOVWU",
	"MOVD",
	"MOVNP",
	"MOVNPW",
	"MOVP",
	"MOVPD",
	"MOVPQ",
	"MOVPS",
	"MOVPSW",
	"MOVPW",
	"SWPAD",
	"SWPAW",
	"SWPAH",
	"SWPAB",
	"SWPALD",
	"SWPALW",
	"SWPALH",
	"SWPALB",
	"SWPD",
	"SWPW",
	"SWPH",
	"SWPB",
	"SWPLD",
	"SWPLW",
	"SWPLH",
	"SWPLB",
	"BEQ",
	"BNE",
	"BCS",
	"BHS",
	"BCC",
	"BLO",
	"BMI",
	"BPL",
	"BVS",
	"BVC",
	"BHI",
	"BLS",
	"BGE",
	"BLT",
	"BGT",
	"BLE",
	"FABSD",
	"FABSS",
	"FADDD",
	"FADDS",
	"FCCMPD",
	"FCCMPED",
	"FCCMPS",
	"FCCMPES",
	"FCMPD",
	"FCMPED",
	"FCMPES",
	"FCMPS",
	"FCVTSD",
	"FCVTDS",
	"FCVTZSD",
	"FCVTZSDW",
	"FCVTZSS",
	"FCVTZSSW",
	"FCVTZUD",
	"FCVTZUDW",
	"FCVTZUS",
	"FCVTZUSW",
	"FDIVD",
	"FDIVS",
	"FLDPD",
	"FLDPS",
	"FMOVD",
	"FMOVS",
	"FMOVQ",
	"FMULD",
	"FMULS",
	"FNEGD",
	"FNEGS",
	"FSQRTD",
	"FSQRTS",
	"FSTPD",
	"FSTPS",
	"FSUBD",
	"FSUBS",
	"SCVTFD",
	"SCVTFS",
	"SCVTFWD",
	"SCVTFWS",
	"UCVTFD",
	"UCVTFS",
	"UCVTFWD",
	"UCVTFWS",
	"WORD",
	"DWORD",
	"FCSELS",
	"FCSELD",
	"FMAXS",
	"FMINS",
	"FMAXD",
	"FMIND",
	"FMAXNMS",
	"FMAXNMD",
	"FNMULS",
	"FNMULD",
	"FRINTNS",
	"FRINTND",
	"FRINTPS",
	"FRINTPD",
	"FRINTMS",
	"FRINTMD",
	"FRINTZS",
	"FRINTZD",
	"FRINTAS",
	"FRINTAD",
	"FRINTXS",
	"FRINTXD",
	"FRINTIS",
	"FRINTID",
	"FMADDS",
	"FMADDD",
	"FMSUBS",
	"FMSUBD",
	"FNMADDS",
	"FNMADDD",
	"FNMSUBS",
	"FNMSUBD",
	"FMINNMS",
	"FMINNMD",
	"FCVTDH",
	"FCVTHS",
	"FCVTHD",
	"FCVTSH",
	"AESD",
	"AESE",
	"AESIMC",
	"AESMC",
	"SHA1C",
	"SHA1H",
	"SHA1M",
	"SHA1P",
	"SHA1SU0",
	"SHA1SU1",
	"SHA256H",
	"SHA256H2",
	"SHA256SU0",
	"SHA256SU1",
	"SHA512H",
	"SHA512H2",
	"SHA512SU0",
	"SHA512SU1",
	"VADD",
	"VADDP",
	"VAND",
	"VBIF",
	"VCMEQ",
	"VCNT",
	"VEOR",
	"VMOV",
	"VLD1",
	"VLD2",
	"VLD3",
	"VLD4",
	"VLD1R",
	"VLD2R",
	"VLD3R",
	"VLD4R",
	"VORR",
	"VREV16",
	"VREV32",
	"VREV64",
	"VST1",
	"VST2",
	"VST3",
	"VST4",
	"VDUP",
	"VADDV",
	"VMOVI",
	"VUADDLV",
	"VSUB",
	"VFMLA",
	"VFMLS",
	"VPMULL",
	"VPMULL2",
	"VEXT",
	"VRBIT",
	"VUSHR",
	"VUSHLL",
	"VUSHLL2",
	"VUXTL",
	"VUXTL2",
	"VUZP1",
	"VUZP2",
	"VSHL",
	"VSRI",
	"VBSL",
	"VBIT",
	"VTBL",
	"VZIP1",
	"VZIP2",
	"VCMTST",
	"LAST",
}
