// Code generated by arm64gen -i ./files -o sysRegEnc.go. DO NOT EDIT.

package arm64

const (
	SYSREG_BEGIN = REG_SPECIAL + iota
	REG_ACTLR_EL1
	REG_AFSR0_EL1
	REG_AFSR1_EL1
	REG_AIDR_EL1
	REG_AMAIR_EL1
	REG_AMCFGR_EL0
	REG_AMCGCR_EL0
	REG_AMCNTENCLR0_EL0
	REG_AMCNTENCLR1_EL0
	REG_AMCNTENSET0_EL0
	REG_AMCNTENSET1_EL0
	REG_AMCR_EL0
	REG_AMEVCNTR00_EL0
	REG_AMEVCNTR01_EL0
	REG_AMEVCNTR02_EL0
	REG_AMEVCNTR03_EL0
	REG_AMEVCNTR04_EL0
	REG_AMEVCNTR05_EL0
	REG_AMEVCNTR06_EL0
	REG_AMEVCNTR07_EL0
	REG_AMEVCNTR08_EL0
	REG_AMEVCNTR09_EL0
	REG_AMEVCNTR010_EL0
	REG_AMEVCNTR011_EL0
	REG_AMEVCNTR012_EL0
	REG_AMEVCNTR013_EL0
	REG_AMEVCNTR014_EL0
	REG_AMEVCNTR015_EL0
	REG_AMEVCNTR10_EL0
	REG_AMEVCNTR11_EL0
	REG_AMEVCNTR12_EL0
	REG_AMEVCNTR13_EL0
	REG_AMEVCNTR14_EL0
	REG_AMEVCNTR15_EL0
	REG_AMEVCNTR16_EL0
	REG_AMEVCNTR17_EL0
	REG_AMEVCNTR18_EL0
	REG_AMEVCNTR19_EL0
	REG_AMEVCNTR110_EL0
	REG_AMEVCNTR111_EL0
	REG_AMEVCNTR112_EL0
	REG_AMEVCNTR113_EL0
	REG_AMEVCNTR114_EL0
	REG_AMEVCNTR115_EL0
	REG_AMEVTYPER00_EL0
	REG_AMEVTYPER01_EL0
	REG_AMEVTYPER02_EL0
	REG_AMEVTYPER03_EL0
	REG_AMEVTYPER04_EL0
	REG_AMEVTYPER05_EL0
	REG_AMEVTYPER06_EL0
	REG_AMEVTYPER07_EL0
	REG_AMEVTYPER08_EL0
	REG_AMEVTYPER09_EL0
	REG_AMEVTYPER010_EL0
	REG_AMEVTYPER011_EL0
	REG_AMEVTYPER012_EL0
	REG_AMEVTYPER013_EL0
	REG_AMEVTYPER014_EL0
	REG_AMEVTYPER015_EL0
	REG_AMEVTYPER10_EL0
	REG_AMEVTYPER11_EL0
	REG_AMEVTYPER12_EL0
	REG_AMEVTYPER13_EL0
	REG_AMEVTYPER14_EL0
	REG_AMEVTYPER15_EL0
	REG_AMEVTYPER16_EL0
	REG_AMEVTYPER17_EL0
	REG_AMEVTYPER18_EL0
	REG_AMEVTYPER19_EL0
	REG_AMEVTYPER110_EL0
	REG_AMEVTYPER111_EL0
	REG_AMEVTYPER112_EL0
	REG_AMEVTYPER113_EL0
	REG_AMEVTYPER114_EL0
	REG_AMEVTYPER115_EL0
	REG_AMUSERENR_EL0
	REG_APDAKeyHi_EL1
	REG_APDAKeyLo_EL1
	REG_APDBKeyHi_EL1
	REG_APDBKeyLo_EL1
	REG_APGAKeyHi_EL1
	REG_APGAKeyLo_EL1
	REG_APIAKeyHi_EL1
	REG_APIAKeyLo_EL1
	REG_APIBKeyHi_EL1
	REG_APIBKeyLo_EL1
	REG_CCSIDR2_EL1
	REG_CCSIDR_EL1
	REG_CLIDR_EL1
	REG_CNTFRQ_EL0
	REG_CNTKCTL_EL1
	REG_CNTP_CTL_EL0
	REG_CNTP_CVAL_EL0
	REG_CNTP_TVAL_EL0
	REG_CNTPCT_EL0
	REG_CNTPS_CTL_EL1
	REG_CNTPS_CVAL_EL1
	REG_CNTPS_TVAL_EL1
	REG_CNTV_CTL_EL0
	REG_CNTV_CVAL_EL0
	REG_CNTV_TVAL_EL0
	REG_CNTVCT_EL0
	REG_CONTEXTIDR_EL1
	REG_CPACR_EL1
	REG_CSSELR_EL1
	REG_CTR_EL0
	REG_CurrentEL
	REG_DAIF
	REG_DBGAUTHSTATUS_EL1
	REG_DBGBCR0_EL1
	REG_DBGBCR1_EL1
	REG_DBGBCR2_EL1
	REG_DBGBCR3_EL1
	REG_DBGBCR4_EL1
	REG_DBGBCR5_EL1
	REG_DBGBCR6_EL1
	REG_DBGBCR7_EL1
	REG_DBGBCR8_EL1
	REG_DBGBCR9_EL1
	REG_DBGBCR10_EL1
	REG_DBGBCR11_EL1
	REG_DBGBCR12_EL1
	REG_DBGBCR13_EL1
	REG_DBGBCR14_EL1
	REG_DBGBCR15_EL1
	REG_DBGBVR0_EL1
	REG_DBGBVR1_EL1
	REG_DBGBVR2_EL1
	REG_DBGBVR3_EL1
	REG_DBGBVR4_EL1
	REG_DBGBVR5_EL1
	REG_DBGBVR6_EL1
	REG_DBGBVR7_EL1
	REG_DBGBVR8_EL1
	REG_DBGBVR9_EL1
	REG_DBGBVR10_EL1
	REG_DBGBVR11_EL1
	REG_DBGBVR12_EL1
	REG_DBGBVR13_EL1
	REG_DBGBVR14_EL1
	REG_DBGBVR15_EL1
	REG_DBGCLAIMCLR_EL1
	REG_DBGCLAIMSET_EL1
	REG_DBGDTR_EL0
	REG_DBGDTRRX_EL0
	REG_DBGDTRTX_EL0
	REG_DBGPRCR_EL1
	REG_DBGWCR0_EL1
	REG_DBGWCR1_EL1
	REG_DBGWCR2_EL1
	REG_DBGWCR3_EL1
	REG_DBGWCR4_EL1
	REG_DBGWCR5_EL1
	REG_DBGWCR6_EL1
	REG_DBGWCR7_EL1
	REG_DBGWCR8_EL1
	REG_DBGWCR9_EL1
	REG_DBGWCR10_EL1
	REG_DBGWCR11_EL1
	REG_DBGWCR12_EL1
	REG_DBGWCR13_EL1
	REG_DBGWCR14_EL1
	REG_DBGWCR15_EL1
	REG_DBGWVR0_EL1
	REG_DBGWVR1_EL1
	REG_DBGWVR2_EL1
	REG_DBGWVR3_EL1
	REG_DBGWVR4_EL1
	REG_DBGWVR5_EL1
	REG_DBGWVR6_EL1
	REG_DBGWVR7_EL1
	REG_DBGWVR8_EL1
	REG_DBGWVR9_EL1
	REG_DBGWVR10_EL1
	REG_DBGWVR11_EL1
	REG_DBGWVR12_EL1
	REG_DBGWVR13_EL1
	REG_DBGWVR14_EL1
	REG_DBGWVR15_EL1
	REG_DCZID_EL0
	REG_DISR_EL1
	REG_DIT
	REG_DLR_EL0
	REG_DSPSR_EL0
	REG_ELR_EL1
	REG_ERRIDR_EL1
	REG_ERRSELR_EL1
	REG_ERXADDR_EL1
	REG_ERXCTLR_EL1
	REG_ERXFR_EL1
	REG_ERXMISC0_EL1
	REG_ERXMISC1_EL1
	REG_ERXMISC2_EL1
	REG_ERXMISC3_EL1
	REG_ERXPFGCDN_EL1
	REG_ERXPFGCTL_EL1
	REG_ERXPFGF_EL1
	REG_ERXSTATUS_EL1
	REG_ESR_EL1
	REG_FAR_EL1
	REG_FPCR
	REG_FPSR
	REG_GCR_EL1
	REG_GMID_EL1
	REG_ICC_AP0R0_EL1
	REG_ICC_AP0R1_EL1
	REG_ICC_AP0R2_EL1
	REG_ICC_AP0R3_EL1
	REG_ICC_AP1R0_EL1
	REG_ICC_AP1R1_EL1
	REG_ICC_AP1R2_EL1
	REG_ICC_AP1R3_EL1
	REG_ICC_ASGI1R_EL1
	REG_ICC_BPR0_EL1
	REG_ICC_BPR1_EL1
	REG_ICC_CTLR_EL1
	REG_ICC_DIR_EL1
	REG_ICC_EOIR0_EL1
	REG_ICC_EOIR1_EL1
	REG_ICC_HPPIR0_EL1
	REG_ICC_HPPIR1_EL1
	REG_ICC_IAR0_EL1
	REG_ICC_IAR1_EL1
	REG_ICC_IGRPEN0_EL1
	REG_ICC_IGRPEN1_EL1
	REG_ICC_PMR_EL1
	REG_ICC_RPR_EL1
	REG_ICC_SGI0R_EL1
	REG_ICC_SGI1R_EL1
	REG_ICC_SRE_EL1
	REG_ICV_AP0R0_EL1
	REG_ICV_AP0R1_EL1
	REG_ICV_AP0R2_EL1
	REG_ICV_AP0R3_EL1
	REG_ICV_AP1R0_EL1
	REG_ICV_AP1R1_EL1
	REG_ICV_AP1R2_EL1
	REG_ICV_AP1R3_EL1
	REG_ICV_BPR0_EL1
	REG_ICV_BPR1_EL1
	REG_ICV_CTLR_EL1
	REG_ICV_DIR_EL1
	REG_ICV_EOIR0_EL1
	REG_ICV_EOIR1_EL1
	REG_ICV_HPPIR0_EL1
	REG_ICV_HPPIR1_EL1
	REG_ICV_IAR0_EL1
	REG_ICV_IAR1_EL1
	REG_ICV_IGRPEN0_EL1
	REG_ICV_IGRPEN1_EL1
	REG_ICV_PMR_EL1
	REG_ICV_RPR_EL1
	REG_ID_AA64AFR0_EL1
	REG_ID_AA64AFR1_EL1
	REG_ID_AA64DFR0_EL1
	REG_ID_AA64DFR1_EL1
	REG_ID_AA64ISAR0_EL1
	REG_ID_AA64ISAR1_EL1
	REG_ID_AA64MMFR0_EL1
	REG_ID_AA64MMFR1_EL1
	REG_ID_AA64MMFR2_EL1
	REG_ID_AA64PFR0_EL1
	REG_ID_AA64PFR1_EL1
	REG_ID_AA64ZFR0_EL1
	REG_ID_AFR0_EL1
	REG_ID_DFR0_EL1
	REG_ID_ISAR0_EL1
	REG_ID_ISAR1_EL1
	REG_ID_ISAR2_EL1
	REG_ID_ISAR3_EL1
	REG_ID_ISAR4_EL1
	REG_ID_ISAR5_EL1
	REG_ID_ISAR6_EL1
	REG_ID_MMFR0_EL1
	REG_ID_MMFR1_EL1
	REG_ID_MMFR2_EL1
	REG_ID_MMFR3_EL1
	REG_ID_MMFR4_EL1
	REG_ID_PFR0_EL1
	REG_ID_PFR1_EL1
	REG_ID_PFR2_EL1
	REG_ISR_EL1
	REG_LORC_EL1
	REG_LOREA_EL1
	REG_LORID_EL1
	REG_LORN_EL1
	REG_LORSA_EL1
	REG_MAIR_EL1
	REG_MDCCINT_EL1
	REG_MDCCSR_EL0
	REG_MDRAR_EL1
	REG_MDSCR_EL1
	REG_MIDR_EL1
	REG_MPAM0_EL1
	REG_MPAM1_EL1
	REG_MPAMIDR_EL1
	REG_MPIDR_EL1
	REG_MVFR0_EL1
	REG_MVFR1_EL1
	REG_MVFR2_EL1
	REG_NZCV
	REG_OSDLR_EL1
	REG_OSDTRRX_EL1
	REG_OSDTRTX_EL1
	REG_OSECCR_EL1
	REG_OSLAR_EL1
	REG_OSLSR_EL1
	REG_PAN
	REG_PAR_EL1
	REG_PMBIDR_EL1
	REG_PMBLIMITR_EL1
	REG_PMBPTR_EL1
	REG_PMBSR_EL1
	REG_PMCCFILTR_EL0
	REG_PMCCNTR_EL0
	REG_PMCEID0_EL0
	REG_PMCEID1_EL0
	REG_PMCNTENCLR_EL0
	REG_PMCNTENSET_EL0
	REG_PMCR_EL0
	REG_PMEVCNTR0_EL0
	REG_PMEVCNTR1_EL0
	REG_PMEVCNTR2_EL0
	REG_PMEVCNTR3_EL0
	REG_PMEVCNTR4_EL0
	REG_PMEVCNTR5_EL0
	REG_PMEVCNTR6_EL0
	REG_PMEVCNTR7_EL0
	REG_PMEVCNTR8_EL0
	REG_PMEVCNTR9_EL0
	REG_PMEVCNTR10_EL0
	REG_PMEVCNTR11_EL0
	REG_PMEVCNTR12_EL0
	REG_PMEVCNTR13_EL0
	REG_PMEVCNTR14_EL0
	REG_PMEVCNTR15_EL0
	REG_PMEVCNTR16_EL0
	REG_PMEVCNTR17_EL0
	REG_PMEVCNTR18_EL0
	REG_PMEVCNTR19_EL0
	REG_PMEVCNTR20_EL0
	REG_PMEVCNTR21_EL0
	REG_PMEVCNTR22_EL0
	REG_PMEVCNTR23_EL0
	REG_PMEVCNTR24_EL0
	REG_PMEVCNTR25_EL0
	REG_PMEVCNTR26_EL0
	REG_PMEVCNTR27_EL0
	REG_PMEVCNTR28_EL0
	REG_PMEVCNTR29_EL0
	REG_PMEVCNTR30_EL0
	REG_PMEVTYPER0_EL0
	REG_PMEVTYPER1_EL0
	REG_PMEVTYPER2_EL0
	REG_PMEVTYPER3_EL0
	REG_PMEVTYPER4_EL0
	REG_PMEVTYPER5_EL0
	REG_PMEVTYPER6_EL0
	REG_PMEVTYPER7_EL0
	REG_PMEVTYPER8_EL0
	REG_PMEVTYPER9_EL0
	REG_PMEVTYPER10_EL0
	REG_PMEVTYPER11_EL0
	REG_PMEVTYPER12_EL0
	REG_PMEVTYPER13_EL0
	REG_PMEVTYPER14_EL0
	REG_PMEVTYPER15_EL0
	REG_PMEVTYPER16_EL0
	REG_PMEVTYPER17_EL0
	REG_PMEVTYPER18_EL0
	REG_PMEVTYPER19_EL0
	REG_PMEVTYPER20_EL0
	REG_PMEVTYPER21_EL0
	REG_PMEVTYPER22_EL0
	REG_PMEVTYPER23_EL0
	REG_PMEVTYPER24_EL0
	REG_PMEVTYPER25_EL0
	REG_PMEVTYPER26_EL0
	REG_PMEVTYPER27_EL0
	REG_PMEVTYPER28_EL0
	REG_PMEVTYPER29_EL0
	REG_PMEVTYPER30_EL0
	REG_PMINTENCLR_EL1
	REG_PMINTENSET_EL1
	REG_PMMIR_EL1
	REG_PMOVSCLR_EL0
	REG_PMOVSSET_EL0
	REG_PMSCR_EL1
	REG_PMSELR_EL0
	REG_PMSEVFR_EL1
	REG_PMSFCR_EL1
	REG_PMSICR_EL1
	REG_PMSIDR_EL1
	REG_PMSIRR_EL1
	REG_PMSLATFR_EL1
	REG_PMSWINC_EL0
	REG_PMUSERENR_EL0
	REG_PMXEVCNTR_EL0
	REG_PMXEVTYPER_EL0
	REG_REVIDR_EL1
	REG_RGSR_EL1
	REG_RMR_EL1
	REG_RNDR
	REG_RNDRRS
	REG_RVBAR_EL1
	REG_SCTLR_EL1
	REG_SCXTNUM_EL0
	REG_SCXTNUM_EL1
	REG_SP_EL0
	REG_SP_EL1
	REG_SPSel
	REG_SPSR_abt
	REG_SPSR_EL1
	REG_SPSR_fiq
	REG_SPSR_irq
	REG_SPSR_und
	REG_SSBS
	REG_TCO
	REG_TCR_EL1
	REG_TFSR_EL1
	REG_TFSRE0_EL1
	REG_TPIDR_EL0
	REG_TPIDR_EL1
	REG_TPIDRRO_EL0
	REG_TRFCR_EL1
	REG_TTBR0_EL1
	REG_TTBR1_EL1
	REG_UAO
	REG_VBAR_EL1
	REG_ZCR_EL1
	SYSREG_END
)

const (
	SR_READ = 1 << iota
	SR_WRITE
)

var SystemReg = []struct {
	Name string
	Reg  int16
	Enc  uint32
	// AccessFlags is the readable and writeable property of system register.
	AccessFlags uint8
}{
	{"ACTLR_EL1", REG_ACTLR_EL1, 0x181020, SR_READ | SR_WRITE},
	{"AFSR0_EL1", REG_AFSR0_EL1, 0x185100, SR_READ | SR_WRITE},
	{"AFSR1_EL1", REG_AFSR1_EL1, 0x185120, SR_READ | SR_WRITE},
	{"AIDR_EL1", REG_AIDR_EL1, 0x1900e0, SR_READ},
	{"AMAIR_EL1", REG_AMAIR_EL1, 0x18a300, SR_READ | SR_WRITE},
	{"AMCFGR_EL0", REG_AMCFGR_EL0, 0x1bd220, SR_READ},
	{"AMCGCR_EL0", REG_AMCGCR_EL0, 0x1bd240, SR_READ},
	{"AMCNTENCLR0_EL0", REG_AMCNTENCLR0_EL0, 0x1bd280, SR_READ | SR_WRITE},
	{"AMCNTENCLR1_EL0", REG_AMCNTENCLR1_EL0, 0x1bd300, SR_READ | SR_WRITE},
	{"AMCNTENSET0_EL0", REG_AMCNTENSET0_EL0, 0x1bd2a0, SR_READ | SR_WRITE},
	{"AMCNTENSET1_EL0", REG_AMCNTENSET1_EL0, 0x1bd320, SR_READ | SR_WRITE},
	{"AMCR_EL0", REG_AMCR_EL0, 0x1bd200, SR_READ | SR_WRITE},
	{"AMEVCNTR00_EL0", REG_AMEVCNTR00_EL0, 0x1bd400, SR_READ | SR_WRITE},
	{"AMEVCNTR01_EL0", REG_AMEVCNTR01_EL0, 0x1bd420, SR_READ | SR_WRITE},
	{"AMEVCNTR02_EL0", REG_AMEVCNTR02_EL0, 0x1bd440, SR_READ | SR_WRITE},
	{"AMEVCNTR03_EL0", REG_AMEVCNTR03_EL0, 0x1bd460, SR_READ | SR_WRITE},
	{"AMEVCNTR04_EL0", REG_AMEVCNTR04_EL0, 0x1bd480, SR_READ | SR_WRITE},
	{"AMEVCNTR05_EL0", REG_AMEVCNTR05_EL0, 0x1bd4a0, SR_READ | SR_WRITE},
	{"AMEVCNTR06_EL0", REG_AMEVCNTR06_EL0, 0x1bd4c0, SR_READ | SR_WRITE},
	{"AMEVCNTR07_EL0", REG_AMEVCNTR07_EL0, 0x1bd4e0, SR_READ | SR_WRITE},
	{"AMEVCNTR08_EL0", REG_AMEVCNTR08_EL0, 0x1bd500, SR_READ | SR_WRITE},
	{"AMEVCNTR09_EL0", REG_AMEVCNTR09_EL0, 0x1bd520, SR_READ | SR_WRITE},
	{"AMEVCNTR010_EL0", REG_AMEVCNTR010_EL0, 0x1bd540, SR_READ | SR_WRITE},
	{"AMEVCNTR011_EL0", REG_AMEVCNTR011_EL0, 0x1bd560, SR_READ | SR_WRITE},
	{"AMEVCNTR012_EL0", REG_AMEVCNTR012_EL0, 0x1bd580, SR_READ | SR_WRITE},
	{"AMEVCNTR013_EL0", REG_AMEVCNTR013_EL0, 0x1bd5a0, SR_READ | SR_WRITE},
	{"AMEVCNTR014_EL0", REG_AMEVCNTR014_EL0, 0x1bd5c0, SR_READ | SR_WRITE},
	{"AMEVCNTR015_EL0", REG_AMEVCNTR015_EL0, 0x1bd5e0, SR_READ | SR_WRITE},
	{"AMEVCNTR10_EL0", REG_AMEVCNTR10_EL0, 0x1bdc00, SR_READ | SR_WRITE},
	{"AMEVCNTR11_EL0", REG_AMEVCNTR11_EL0, 0x1bdc20, SR_READ | SR_WRITE},
	{"AMEVCNTR12_EL0", REG_AMEVCNTR12_EL0, 0x1bdc40, SR_READ | SR_WRITE},
	{"AMEVCNTR13_EL0", REG_AMEVCNTR13_EL0, 0x1bdc60, SR_READ | SR_WRITE},
	{"AMEVCNTR14_EL0", REG_AMEVCNTR14_EL0, 0x1bdc80, SR_READ | SR_WRITE},
	{"AMEVCNTR15_EL0", REG_AMEVCNTR15_EL0, 0x1bdca0, SR_READ | SR_WRITE},
	{"AMEVCNTR16_EL0", REG_AMEVCNTR16_EL0, 0x1bdcc0, SR_READ | SR_WRITE},
	{"AMEVCNTR17_EL0", REG_AMEVCNTR17_EL0, 0x1bdce0, SR_READ | SR_WRITE},
	{"AMEVCNTR18_EL0", REG_AMEVCNTR18_EL0, 0x1bdd00, SR_READ | SR_WRITE},
	{"AMEVCNTR19_EL0", REG_AMEVCNTR19_EL0, 0x1bdd20, SR_READ | SR_WRITE},
	{"AMEVCNTR110_EL0", REG_AMEVCNTR110_EL0, 0x1bdd40, SR_READ | SR_WRITE},
	{"AMEVCNTR111_EL0", REG_AMEVCNTR111_EL0, 0x1bdd60, SR_READ | SR_WRITE},
	{"AMEVCNTR112_EL0", REG_AMEVCNTR112_EL0, 0x1bdd80, SR_READ | SR_WRITE},
	{"AMEVCNTR113_EL0", REG_AMEVCNTR113_EL0, 0x1bdda0, SR_READ | SR_WRITE},
	{"AMEVCNTR114_EL0", REG_AMEVCNTR114_EL0, 0x1bddc0, SR_READ | SR_WRITE},
	{"AMEVCNTR115_EL0", REG_AMEVCNTR115_EL0, 0x1bdde0, SR_READ | SR_WRITE},
	{"AMEVTYPER00_EL0", REG_AMEVTYPER00_EL0, 0x1bd600, SR_READ},
	{"AMEVTYPER01_EL0", REG_AMEVTYPER01_EL0, 0x1bd620, SR_READ},
	{"AMEVTYPER02_EL0", REG_AMEVTYPER02_EL0, 0x1bd640, SR_READ},
	{"AMEVTYPER03_EL0", REG_AMEVTYPER03_EL0, 0x1bd660, SR_READ},
	{"AMEVTYPER04_EL0", REG_AMEVTYPER04_EL0, 0x1bd680, SR_READ},
	{"AMEVTYPER05_EL0", REG_AMEVTYPER05_EL0, 0x1bd6a0, SR_READ},
	{"AMEVTYPER06_EL0", REG_AMEVTYPER06_EL0, 0x1bd6c0, SR_READ},
	{"AMEVTYPER07_EL0", REG_AMEVTYPER07_EL0, 0x1bd6e0, SR_READ},
	{"AMEVTYPER08_EL0", REG_AMEVTYPER08_EL0, 0x1bd700, SR_READ},
	{"AMEVTYPER09_EL0", REG_AMEVTYPER09_EL0, 0x1bd720, SR_READ},
	{"AMEVTYPER010_EL0", REG_AMEVTYPER010_EL0, 0x1bd740, SR_READ},
	{"AMEVTYPER011_EL0", REG_AMEVTYPER011_EL0, 0x1bd760, SR_READ},
	{"AMEVTYPER012_EL0", REG_AMEVTYPER012_EL0, 0x1bd780, SR_READ},
	{"AMEVTYPER013_EL0", REG_AMEVTYPER013_EL0, 0x1bd7a0, SR_READ},
	{"AMEVTYPER014_EL0", REG_AMEVTYPER014_EL0, 0x1bd7c0, SR_READ},
	{"AMEVTYPER015_EL0", REG_AMEVTYPER015_EL0, 0x1bd7e0, SR_READ},
	{"AMEVTYPER10_EL0", REG_AMEVTYPER10_EL0, 0x1bde00, SR_READ | SR_WRITE},
	{"AMEVTYPER11_EL0", REG_AMEVTYPER11_EL0, 0x1bde20, SR_READ | SR_WRITE},
	{"AMEVTYPER12_EL0", REG_AMEVTYPER12_EL0, 0x1bde40, SR_READ | SR_WRITE},
	{"AMEVTYPER13_EL0", REG_AMEVTYPER13_EL0, 0x1bde60, SR_READ | SR_WRITE},
	{"AMEVTYPER14_EL0", REG_AMEVTYPER14_EL0, 0x1bde80, SR_READ | SR_WRITE},
	{"AMEVTYPER15_EL0", REG_AMEVTYPER15_EL0, 0x1bdea0, SR_READ | SR_WRITE},
	{"AMEVTYPER16_EL0", REG_AMEVTYPER16_EL0, 0x1bdec0, SR_READ | SR_WRITE},
	{"AMEVTYPER17_EL0", REG_AMEVTYPER17_EL0, 0x1bdee0, SR_READ | SR_WRITE},
	{"AMEVTYPER18_EL0", REG_AMEVTYPER18_EL0, 0x1bdf00, SR_READ | SR_WRITE},
	{"AMEVTYPER19_EL0", REG_AMEVTYPER19_EL0, 0x1bdf20, SR_READ | SR_WRITE},
	{"AMEVTYPER110_EL0", REG_AMEVTYPER110_EL0, 0x1bdf40, SR_READ | SR_WRITE},
	{"AMEVTYPER111_EL0", REG_AMEVTYPER111_EL0, 0x1bdf60, SR_READ | SR_WRITE},
	{"AMEVTYPER112_EL0", REG_AMEVTYPER112_EL0, 0x1bdf80, SR_READ | SR_WRITE},
	{"AMEVTYPER113_EL0", REG_AMEVTYPER113_EL0, 0x1bdfa0, SR_READ | SR_WRITE},
	{"AMEVTYPER114_EL0", REG_AMEVTYPER114_EL0, 0x1bdfc0, SR_READ | SR_WRITE},
	{"AMEVTYPER115_EL0", REG_AMEVTYPER115_EL0, 0x1bdfe0, SR_READ | SR_WRITE},
	{"AMUSERENR_EL0", REG_AMUSERENR_EL0, 0x1bd260, SR_READ | SR_WRITE},
	{"APDAKeyHi_EL1", REG_APDAKeyHi_EL1, 0x182220, SR_READ | SR_WRITE},
	{"APDAKeyLo_EL1", REG_APDAKeyLo_EL1, 0x182200, SR_READ | SR_WRITE},
	{"APDBKeyHi_EL1", REG_APDBKeyHi_EL1, 0x182260, SR_READ | SR_WRITE},
	{"APDBKeyLo_EL1", REG_APDBKeyLo_EL1, 0x182240, SR_READ | SR_WRITE},
	{"APGAKeyHi_EL1", REG_APGAKeyHi_EL1, 0x182320, SR_READ | SR_WRITE},
	{"APGAKeyLo_EL1", REG_APGAKeyLo_EL1, 0x182300, SR_READ | SR_WRITE},
	{"APIAKeyHi_EL1", REG_APIAKeyHi_EL1, 0x182120, SR_READ | SR_WRITE},
	{"APIAKeyLo_EL1", REG_APIAKeyLo_EL1, 0x182100, SR_READ | SR_WRITE},
	{"APIBKeyHi_EL1", REG_APIBKeyHi_EL1, 0x182160, SR_READ | SR_WRITE},
	{"APIBKeyLo_EL1", REG_APIBKeyLo_EL1, 0x182140, SR_READ | SR_WRITE},
	{"CCSIDR2_EL1", REG_CCSIDR2_EL1, 0x190040, SR_READ},
	{"CCSIDR_EL1", REG_CCSIDR_EL1, 0x190000, SR_READ},
	{"CLIDR_EL1", REG_CLIDR_EL1, 0x190020, SR_READ},
	{"CNTFRQ_EL0", REG_CNTFRQ_EL0, 0x1be000, SR_READ | SR_WRITE},
	{"CNTKCTL_EL1", REG_CNTKCTL_EL1, 0x18e100, SR_READ | SR_WRITE},
	{"CNTP_CTL_EL0", REG_CNTP_CTL_EL0, 0x1be220, SR_READ | SR_WRITE},
	{"CNTP_CVAL_EL0", REG_CNTP_CVAL_EL0, 0x1be240, SR_READ | SR_WRITE},
	{"CNTP_TVAL_EL0", REG_CNTP_TVAL_EL0, 0x1be200, SR_READ | SR_WRITE},
	{"CNTPCT_EL0", REG_CNTPCT_EL0, 0x1be020, SR_READ},
	{"CNTPS_CTL_EL1", REG_CNTPS_CTL_EL1, 0x1fe220, SR_READ | SR_WRITE},
	{"CNTPS_CVAL_EL1", REG_CNTPS_CVAL_EL1, 0x1fe240, SR_READ | SR_WRITE},
	{"CNTPS_TVAL_EL1", REG_CNTPS_TVAL_EL1, 0x1fe200, SR_READ | SR_WRITE},
	{"CNTV_CTL_EL0", REG_CNTV_CTL_EL0, 0x1be320, SR_READ | SR_WRITE},
	{"CNTV_CVAL_EL0", REG_CNTV_CVAL_EL0, 0x1be340, SR_READ | SR_WRITE},
	{"CNTV_TVAL_EL0", REG_CNTV_TVAL_EL0, 0x1be300, SR_READ | SR_WRITE},
	{"CNTVCT_EL0", REG_CNTVCT_EL0, 0x1be040, SR_READ},
	{"CONTEXTIDR_EL1", REG_CONTEXTIDR_EL1, 0x18d020, SR_READ | SR_WRITE},
	{"CPACR_EL1", REG_CPACR_EL1, 0x181040, SR_READ | SR_WRITE},
	{"CSSELR_EL1", REG_CSSELR_EL1, 0x1a0000, SR_READ | SR_WRITE},
	{"CTR_EL0", REG_CTR_EL0, 0x1b0020, SR_READ},
	{"CurrentEL", REG_CurrentEL, 0x184240, SR_READ},
	{"DAIF", REG_DAIF, 0x1b4220, SR_READ | SR_WRITE},
	{"DBGAUTHSTATUS_EL1", REG_DBGAUTHSTATUS_EL1, 0x107ec0, SR_READ},
	{"DBGBCR0_EL1", REG_DBGBCR0_EL1, 0x1000a0, SR_READ | SR_WRITE},
	{"DBGBCR1_EL1", REG_DBGBCR1_EL1, 0x1001a0, SR_READ | SR_WRITE},
	{"DBGBCR2_EL1", REG_DBGBCR2_EL1, 0x1002a0, SR_READ | SR_WRITE},
	{"DBGBCR3_EL1", REG_DBGBCR3_EL1, 0x1003a0, SR_READ | SR_WRITE},
	{"DBGBCR4_EL1", REG_DBGBCR4_EL1, 0x1004a0, SR_READ | SR_WRITE},
	{"DBGBCR5_EL1", REG_DBGBCR5_EL1, 0x1005a0, SR_READ | SR_WRITE},
	{"DBGBCR6_EL1", REG_DBGBCR6_EL1, 0x1006a0, SR_READ | SR_WRITE},
	{"DBGBCR7_EL1", REG_DBGBCR7_EL1, 0x1007a0, SR_READ | SR_WRITE},
	{"DBGBCR8_EL1", REG_DBGBCR8_EL1, 0x1008a0, SR_READ | SR_WRITE},
	{"DBGBCR9_EL1", REG_DBGBCR9_EL1, 0x1009a0, SR_READ | SR_WRITE},
	{"DBGBCR10_EL1", REG_DBGBCR10_EL1, 0x100aa0, SR_READ | SR_WRITE},
	{"DBGBCR11_EL1", REG_DBGBCR11_EL1, 0x100ba0, SR_READ | SR_WRITE},
	{"DBGBCR12_EL1", REG_DBGBCR12_EL1, 0x100ca0, SR_READ | SR_WRITE},
	{"DBGBCR13_EL1", REG_DBGBCR13_EL1, 0x100da0, SR_READ | SR_WRITE},
	{"DBGBCR14_EL1", REG_DBGBCR14_EL1, 0x100ea0, SR_READ | SR_WRITE},
	{"DBGBCR15_EL1", REG_DBGBCR15_EL1, 0x100fa0, SR_READ | SR_WRITE},
	{"DBGBVR0_EL1", REG_DBGBVR0_EL1, 0x100080, SR_READ | SR_WRITE},
	{"DBGBVR1_EL1", REG_DBGBVR1_EL1, 0x100180, SR_READ | SR_WRITE},
	{"DBGBVR2_EL1", REG_DBGBVR2_EL1, 0x100280, SR_READ | SR_WRITE},
	{"DBGBVR3_EL1", REG_DBGBVR3_EL1, 0x100380, SR_READ | SR_WRITE},
	{"DBGBVR4_EL1", REG_DBGBVR4_EL1, 0x100480, SR_READ | SR_WRITE},
	{"DBGBVR5_EL1", REG_DBGBVR5_EL1, 0x100580, SR_READ | SR_WRITE},
	{"DBGBVR6_EL1", REG_DBGBVR6_EL1, 0x100680, SR_READ | SR_WRITE},
	{"DBGBVR7_EL1", REG_DBGBVR7_EL1, 0x100780, SR_READ | SR_WRITE},
	{"DBGBVR8_EL1", REG_DBGBVR8_EL1, 0x100880, SR_READ | SR_WRITE},
	{"DBGBVR9_EL1", REG_DBGBVR9_EL1, 0x100980, SR_READ | SR_WRITE},
	{"DBGBVR10_EL1", REG_DBGBVR10_EL1, 0x100a80, SR_READ | SR_WRITE},
	{"DBGBVR11_EL1", REG_DBGBVR11_EL1, 0x100b80, SR_READ | SR_WRITE},
	{"DBGBVR12_EL1", REG_DBGBVR12_EL1, 0x100c80, SR_READ | SR_WRITE},
	{"DBGBVR13_EL1", REG_DBGBVR13_EL1, 0x100d80, SR_READ | SR_WRITE},
	{"DBGBVR14_EL1", REG_DBGBVR14_EL1, 0x100e80, SR_READ | SR_WRITE},
	{"DBGBVR15_EL1", REG_DBGBVR15_EL1, 0x100f80, SR_READ | SR_WRITE},
	{"DBGCLAIMCLR_EL1", REG_DBGCLAIMCLR_EL1, 0x1079c0, SR_READ | SR_WRITE},
	{"DBGCLAIMSET_EL1", REG_DBGCLAIMSET_EL1, 0x1078c0, SR_READ | SR_WRITE},
	{"DBGDTR_EL0", REG_DBGDTR_EL0, 0x130400, SR_READ | SR_WRITE},
	{"DBGDTRRX_EL0", REG_DBGDTRRX_EL0, 0x130500, SR_READ},
	{"DBGDTRTX_EL0", REG_DBGDTRTX_EL0, 0x130500, SR_WRITE},
	{"DBGPRCR_EL1", REG_DBGPRCR_EL1, 0x101480, SR_READ | SR_WRITE},
	{"DBGWCR0_EL1", REG_DBGWCR0_EL1, 0x1000e0, SR_READ | SR_WRITE},
	{"DBGWCR1_EL1", REG_DBGWCR1_EL1, 0x1001e0, SR_READ | SR_WRITE},
	{"DBGWCR2_EL1", REG_DBGWCR2_EL1, 0x1002e0, SR_READ | SR_WRITE},
	{"DBGWCR3_EL1", REG_DBGWCR3_EL1, 0x1003e0, SR_READ | SR_WRITE},
	{"DBGWCR4_EL1", REG_DBGWCR4_EL1, 0x1004e0, SR_READ | SR_WRITE},
	{"DBGWCR5_EL1", REG_DBGWCR5_EL1, 0x1005e0, SR_READ | SR_WRITE},
	{"DBGWCR6_EL1", REG_DBGWCR6_EL1, 0x1006e0, SR_READ | SR_WRITE},
	{"DBGWCR7_EL1", REG_DBGWCR7_EL1, 0x1007e0, SR_READ | SR_WRITE},
	{"DBGWCR8_EL1", REG_DBGWCR8_EL1, 0x1008e0, SR_READ | SR_WRITE},
	{"DBGWCR9_EL1", REG_DBGWCR9_EL1, 0x1009e0, SR_READ | SR_WRITE},
	{"DBGWCR10_EL1", REG_DBGWCR10_EL1, 0x100ae0, SR_READ | SR_WRITE},
	{"DBGWCR11_EL1", REG_DBGWCR11_EL1, 0x100be0, SR_READ | SR_WRITE},
	{"DBGWCR12_EL1", REG_DBGWCR12_EL1, 0x100ce0, SR_READ | SR_WRITE},
	{"DBGWCR13_EL1", REG_DBGWCR13_EL1, 0x100de0, SR_READ | SR_WRITE},
	{"DBGWCR14_EL1", REG_DBGWCR14_EL1, 0x100ee0, SR_READ | SR_WRITE},
	{"DBGWCR15_EL1", REG_DBGWCR15_EL1, 0x100fe0, SR_READ | SR_WRITE},
	{"DBGWVR0_EL1", REG_DBGWVR0_EL1, 0x1000c0, SR_READ | SR_WRITE},
	{"DBGWVR1_EL1", REG_DBGWVR1_EL1, 0x1001c0, SR_READ | SR_WRITE},
	{"DBGWVR2_EL1", REG_DBGWVR2_EL1, 0x1002c0, SR_READ | SR_WRITE},
	{"DBGWVR3_EL1", REG_DBGWVR3_EL1, 0x1003c0, SR_READ | SR_WRITE},
	{"DBGWVR4_EL1", REG_DBGWVR4_EL1, 0x1004c0, SR_READ | SR_WRITE},
	{"DBGWVR5_EL1", REG_DBGWVR5_EL1, 0x1005c0, SR_READ | SR_WRITE},
	{"DBGWVR6_EL1", REG_DBGWVR6_EL1, 0x1006c0, SR_READ | SR_WRITE},
	{"DBGWVR7_EL1", REG_DBGWVR7_EL1, 0x1007c0, SR_READ | SR_WRITE},
	{"DBGWVR8_EL1", REG_DBGWVR8_EL1, 0x1008c0, SR_READ | SR_WRITE},
	{"DBGWVR9_EL1", REG_DBGWVR9_EL1, 0x1009c0, SR_READ | SR_WRITE},
	{"DBGWVR10_EL1", REG_DBGWVR10_EL1, 0x100ac0, SR_READ | SR_WRITE},
	{"DBGWVR11_EL1", REG_DBGWVR11_EL1, 0x100bc0, SR_READ | SR_WRITE},
	{"DBGWVR12_EL1", REG_DBGWVR12_EL1, 0x100cc0, SR_READ | SR_WRITE},
	{"DBGWVR13_EL1", REG_DBGWVR13_EL1, 0x100dc0, SR_READ | SR_WRITE},
	{"DBGWVR14_EL1", REG_DBGWVR14_EL1, 0x100ec0, SR_READ | SR_WRITE},
	{"DBGWVR15_EL1", REG_DBGWVR15_EL1, 0x100fc0, SR_READ | SR_WRITE},
	{"DCZID_EL0", REG_DCZID_EL0, 0x1b00e0, SR_READ},
	{"DISR_EL1", REG_DISR_EL1, 0x18c120, SR_READ | SR_WRITE},
	{"DIT", REG_DIT, 0x1b42a0, SR_READ | SR_WRITE},
	{"DLR_EL0", REG_DLR_EL0, 0x1b4520, SR_READ | SR_WRITE},
	{"DSPSR_EL0", REG_DSPSR_EL0, 0x1b4500, SR_READ | SR_WRITE},
	{"ELR_EL1", REG_ELR_EL1, 0x184020, SR_READ | SR_WRITE},
	{"ERRIDR_EL1", REG_ERRIDR_EL1, 0x185300, SR_READ},
	{"ERRSELR_EL1", REG_ERRSELR_EL1, 0x185320, SR_READ | SR_WRITE},
	{"ERXADDR_EL1", REG_ERXADDR_EL1, 0x185460, SR_READ | SR_WRITE},
	{"ERXCTLR_EL1", REG_ERXCTLR_EL1, 0x185420, SR_READ | SR_WRITE},
	{"ERXFR_EL1", REG_ERXFR_EL1, 0x185400, SR_READ},
	{"ERXMISC0_EL1", REG_ERXMISC0_EL1, 0x185500, SR_READ | SR_WRITE},
	{"ERXMISC1_EL1", REG_ERXMISC1_EL1, 0x185520, SR_READ | SR_WRITE},
	{"ERXMISC2_EL1", REG_ERXMISC2_EL1, 0x185540, SR_READ | SR_WRITE},
	{"ERXMISC3_EL1", REG_ERXMISC3_EL1, 0x185560, SR_READ | SR_WRITE},
	{"ERXPFGCDN_EL1", REG_ERXPFGCDN_EL1, 0x1854c0, SR_READ | SR_WRITE},
	{"ERXPFGCTL_EL1", REG_ERXPFGCTL_EL1, 0x1854a0, SR_READ | SR_WRITE},
	{"ERXPFGF_EL1", REG_ERXPFGF_EL1, 0x185480, SR_READ},
	{"ERXSTATUS_EL1", REG_ERXSTATUS_EL1, 0x185440, SR_READ | SR_WRITE},
	{"ESR_EL1", REG_ESR_EL1, 0x185200, SR_READ | SR_WRITE},
	{"FAR_EL1", REG_FAR_EL1, 0x186000, SR_READ | SR_WRITE},
	{"FPCR", REG_FPCR, 0x1b4400, SR_READ | SR_WRITE},
	{"FPSR", REG_FPSR, 0x1b4420, SR_READ | SR_WRITE},
	{"GCR_EL1", REG_GCR_EL1, 0x1810c0, SR_READ | SR_WRITE},
	{"GMID_EL1", REG_GMID_EL1, 0x31400, SR_READ},
	{"ICC_AP0R0_EL1", REG_ICC_AP0R0_EL1, 0x18c880, SR_READ | SR_WRITE},
	{"ICC_AP0R1_EL1", REG_ICC_AP0R1_EL1, 0x18c8a0, SR_READ | SR_WRITE},
	{"ICC_AP0R2_EL1", REG_ICC_AP0R2_EL1, 0x18c8c0, SR_READ | SR_WRITE},
	{"ICC_AP0R3_EL1", REG_ICC_AP0R3_EL1, 0x18c8e0, SR_READ | SR_WRITE},
	{"ICC_AP1R0_EL1", REG_ICC_AP1R0_EL1, 0x18c900, SR_READ | SR_WRITE},
	{"ICC_AP1R1_EL1", REG_ICC_AP1R1_EL1, 0x18c920, SR_READ | SR_WRITE},
	{"ICC_AP1R2_EL1", REG_ICC_AP1R2_EL1, 0x18c940, SR_READ | SR_WRITE},
	{"ICC_AP1R3_EL1", REG_ICC_AP1R3_EL1, 0x18c960, SR_READ | SR_WRITE},
	{"ICC_ASGI1R_EL1", REG_ICC_ASGI1R_EL1, 0x18cbc0, SR_WRITE},
	{"ICC_BPR0_EL1", REG_ICC_BPR0_EL1, 0x18c860, SR_READ | SR_WRITE},
	{"ICC_BPR1_EL1", REG_ICC_BPR1_EL1, 0x18cc60, SR_READ | SR_WRITE},
	{"ICC_CTLR_EL1", REG_ICC_CTLR_EL1, 0x18cc80, SR_READ | SR_WRITE},
	{"ICC_DIR_EL1", REG_ICC_DIR_EL1, 0x18cb20, SR_WRITE},
	{"ICC_EOIR0_EL1", REG_ICC_EOIR0_EL1, 0x18c820, SR_WRITE},
	{"ICC_EOIR1_EL1", REG_ICC_EOIR1_EL1, 0x18cc20, SR_WRITE},
	{"ICC_HPPIR0_EL1", REG_ICC_HPPIR0_EL1, 0x18c840, SR_READ},
	{"ICC_HPPIR1_EL1", REG_ICC_HPPIR1_EL1, 0x18cc40, SR_READ},
	{"ICC_IAR0_EL1", REG_ICC_IAR0_EL1, 0x18c800, SR_READ},
	{"ICC_IAR1_EL1", REG_ICC_IAR1_EL1, 0x18cc00, SR_READ},
	{"ICC_IGRPEN0_EL1", REG_ICC_IGRPEN0_EL1, 0x18ccc0, SR_READ | SR_WRITE},
	{"ICC_IGRPEN1_EL1", REG_ICC_IGRPEN1_EL1, 0x18cce0, SR_READ | SR_WRITE},
	{"ICC_PMR_EL1", REG_ICC_PMR_EL1, 0x184600, SR_READ | SR_WRITE},
	{"ICC_RPR_EL1", REG_ICC_RPR_EL1, 0x18cb60, SR_READ},
	{"ICC_SGI0R_EL1", REG_ICC_SGI0R_EL1, 0x18cbe0, SR_WRITE},
	{"ICC_SGI1R_EL1", REG_ICC_SGI1R_EL1, 0x18cba0, SR_WRITE},
	{"ICC_SRE_EL1", REG_ICC_SRE_EL1, 0x18cca0, SR_READ | SR_WRITE},
	{"ICV_AP0R0_EL1", REG_ICV_AP0R0_EL1, 0x18c880, SR_READ | SR_WRITE},
	{"ICV_AP0R1_EL1", REG_ICV_AP0R1_EL1, 0x18c8a0, SR_READ | SR_WRITE},
	{"ICV_AP0R2_EL1", REG_ICV_AP0R2_EL1, 0x18c8c0, SR_READ | SR_WRITE},
	{"ICV_AP0R3_EL1", REG_ICV_AP0R3_EL1, 0x18c8e0, SR_READ | SR_WRITE},
	{"ICV_AP1R0_EL1", REG_ICV_AP1R0_EL1, 0x18c900, SR_READ | SR_WRITE},
	{"ICV_AP1R1_EL1", REG_ICV_AP1R1_EL1, 0x18c920, SR_READ | SR_WRITE},
	{"ICV_AP1R2_EL1", REG_ICV_AP1R2_EL1, 0x18c940, SR_READ | SR_WRITE},
	{"ICV_AP1R3_EL1", REG_ICV_AP1R3_EL1, 0x18c960, SR_READ | SR_WRITE},
	{"ICV_BPR0_EL1", REG_ICV_BPR0_EL1, 0x18c860, SR_READ | SR_WRITE},
	{"ICV_BPR1_EL1", REG_ICV_BPR1_EL1, 0x18cc60, SR_READ | SR_WRITE},
	{"ICV_CTLR_EL1", REG_ICV_CTLR_EL1, 0x18cc80, SR_READ | SR_WRITE},
	{"ICV_DIR_EL1", REG_ICV_DIR_EL1, 0x18cb20, SR_WRITE},
	{"ICV_EOIR0_EL1", REG_ICV_EOIR0_EL1, 0x18c820, SR_WRITE},
	{"ICV_EOIR1_EL1", REG_ICV_EOIR1_EL1, 0x18cc20, SR_WRITE},
	{"ICV_HPPIR0_EL1", REG_ICV_HPPIR0_EL1, 0x18c840, SR_READ},
	{"ICV_HPPIR1_EL1", REG_ICV_HPPIR1_EL1, 0x18cc40, SR_READ},
	{"ICV_IAR0_EL1", REG_ICV_IAR0_EL1, 0x18c800, SR_READ},
	{"ICV_IAR1_EL1", REG_ICV_IAR1_EL1, 0x18cc00, SR_READ},
	{"ICV_IGRPEN0_EL1", REG_ICV_IGRPEN0_EL1, 0x18ccc0, SR_READ | SR_WRITE},
	{"ICV_IGRPEN1_EL1", REG_ICV_IGRPEN1_EL1, 0x18cce0, SR_READ | SR_WRITE},
	{"ICV_PMR_EL1", REG_ICV_PMR_EL1, 0x184600, SR_READ | SR_WRITE},
	{"ICV_RPR_EL1", REG_ICV_RPR_EL1, 0x18cb60, SR_READ},
	{"ID_AA64AFR0_EL1", REG_ID_AA64AFR0_EL1, 0x180580, SR_READ},
	{"ID_AA64AFR1_EL1", REG_ID_AA64AFR1_EL1, 0x1805a0, SR_READ},
	{"ID_AA64DFR0_EL1", REG_ID_AA64DFR0_EL1, 0x180500, SR_READ},
	{"ID_AA64DFR1_EL1", REG_ID_AA64DFR1_EL1, 0x180520, SR_READ},
	{"ID_AA64ISAR0_EL1", REG_ID_AA64ISAR0_EL1, 0x180600, SR_READ},
	{"ID_AA64ISAR1_EL1", REG_ID_AA64ISAR1_EL1, 0x180620, SR_READ},
	{"ID_AA64MMFR0_EL1", REG_ID_AA64MMFR0_EL1, 0x180700, SR_READ},
	{"ID_AA64MMFR1_EL1", REG_ID_AA64MMFR1_EL1, 0x180720, SR_READ},
	{"ID_AA64MMFR2_EL1", REG_ID_AA64MMFR2_EL1, 0x180740, SR_READ},
	{"ID_AA64PFR0_EL1", REG_ID_AA64PFR0_EL1, 0x180400, SR_READ},
	{"ID_AA64PFR1_EL1", REG_ID_AA64PFR1_EL1, 0x180420, SR_READ},
	{"ID_AA64ZFR0_EL1", REG_ID_AA64ZFR0_EL1, 0x180480, SR_READ},
	{"ID_AFR0_EL1", REG_ID_AFR0_EL1, 0x180160, SR_READ},
	{"ID_DFR0_EL1", REG_ID_DFR0_EL1, 0x180140, SR_READ},
	{"ID_ISAR0_EL1", REG_ID_ISAR0_EL1, 0x180200, SR_READ},
	{"ID_ISAR1_EL1", REG_ID_ISAR1_EL1, 0x180220, SR_READ},
	{"ID_ISAR2_EL1", REG_ID_ISAR2_EL1, 0x180240, SR_READ},
	{"ID_ISAR3_EL1", REG_ID_ISAR3_EL1, 0x180260, SR_READ},
	{"ID_ISAR4_EL1", REG_ID_ISAR4_EL1, 0x180280, SR_READ},
	{"ID_ISAR5_EL1", REG_ID_ISAR5_EL1, 0x1802a0, SR_READ},
	{"ID_ISAR6_EL1", REG_ID_ISAR6_EL1, 0x1802e0, SR_READ},
	{"ID_MMFR0_EL1", REG_ID_MMFR0_EL1, 0x180180, SR_READ},
	{"ID_MMFR1_EL1", REG_ID_MMFR1_EL1, 0x1801a0, SR_READ},
	{"ID_MMFR2_EL1", REG_ID_MMFR2_EL1, 0x1801c0, SR_READ},
	{"ID_MMFR3_EL1", REG_ID_MMFR3_EL1, 0x1801e0, SR_READ},
	{"ID_MMFR4_EL1", REG_ID_MMFR4_EL1, 0x1802c0, SR_READ},
	{"ID_PFR0_EL1", REG_ID_PFR0_EL1, 0x180100, SR_READ},
	{"ID_PFR1_EL1", REG_ID_PFR1_EL1, 0x180120, SR_READ},
	{"ID_PFR2_EL1", REG_ID_PFR2_EL1, 0x180380, SR_READ},
	{"ISR_EL1", REG_ISR_EL1, 0x18c100, SR_READ},
	{"LORC_EL1", REG_LORC_EL1, 0x18a460, SR_READ | SR_WRITE},
	{"LOREA_EL1", REG_LOREA_EL1, 0x18a420, SR_READ | SR_WRITE},
	{"LORID_EL1", REG_LORID_EL1, 0x18a4e0, SR_READ},
	{"LORN_EL1", REG_LORN_EL1, 0x18a440, SR_READ | SR_WRITE},
	{"LORSA_EL1", REG_LORSA_EL1, 0x18a400, SR_READ | SR_WRITE},
	{"MAIR_EL1", REG_MAIR_EL1, 0x18a200, SR_READ | SR_WRITE},
	{"MDCCINT_EL1", REG_MDCCINT_EL1, 0x100200, SR_READ | SR_WRITE},
	{"MDCCSR_EL0", REG_MDCCSR_EL0, 0x130100, SR_READ},
	{"MDRAR_EL1", REG_MDRAR_EL1, 0x101000, SR_READ},
	{"MDSCR_EL1", REG_MDSCR_EL1, 0x100240, SR_READ | SR_WRITE},
	{"MIDR_EL1", REG_MIDR_EL1, 0x180000, SR_READ},
	{"MPAM0_EL1", REG_MPAM0_EL1, 0x18a520, SR_READ | SR_WRITE},
	{"MPAM1_EL1", REG_MPAM1_EL1, 0x18a500, SR_READ | SR_WRITE},
	{"MPAMIDR_EL1", REG_MPAMIDR_EL1, 0x18a480, SR_READ},
	{"MPIDR_EL1", REG_MPIDR_EL1, 0x1800a0, SR_READ},
	{"MVFR0_EL1", REG_MVFR0_EL1, 0x180300, SR_READ},
	{"MVFR1_EL1", REG_MVFR1_EL1, 0x180320, SR_READ},
	{"MVFR2_EL1", REG_MVFR2_EL1, 0x180340, SR_READ},
	{"NZCV", REG_NZCV, 0x1b4200, SR_READ | SR_WRITE},
	{"OSDLR_EL1", REG_OSDLR_EL1, 0x101380, SR_READ | SR_WRITE},
	{"OSDTRRX_EL1", REG_OSDTRRX_EL1, 0x100040, SR_READ | SR_WRITE},
	{"OSDTRTX_EL1", REG_OSDTRTX_EL1, 0x100340, SR_READ | SR_WRITE},
	{"OSECCR_EL1", REG_OSECCR_EL1, 0x100640, SR_READ | SR_WRITE},
	{"OSLAR_EL1", REG_OSLAR_EL1, 0x101080, SR_WRITE},
	{"OSLSR_EL1", REG_OSLSR_EL1, 0x101180, SR_READ},
	{"PAN", REG_PAN, 0x184260, SR_READ | SR_WRITE},
	{"PAR_EL1", REG_PAR_EL1, 0x187400, SR_READ | SR_WRITE},
	{"PMBIDR_EL1", REG_PMBIDR_EL1, 0x189ae0, SR_READ},
	{"PMBLIMITR_EL1", REG_PMBLIMITR_EL1, 0x189a00, SR_READ | SR_WRITE},
	{"PMBPTR_EL1", REG_PMBPTR_EL1, 0x189a20, SR_READ | SR_WRITE},
	{"PMBSR_EL1", REG_PMBSR_EL1, 0x189a60, SR_READ | SR_WRITE},
	{"PMCCFILTR_EL0", REG_PMCCFILTR_EL0, 0x1befe0, SR_READ | SR_WRITE},
	{"PMCCNTR_EL0", REG_PMCCNTR_EL0, 0x1b9d00, SR_READ | SR_WRITE},
	{"PMCEID0_EL0", REG_PMCEID0_EL0, 0x1b9cc0, SR_READ},
	{"PMCEID1_EL0", REG_PMCEID1_EL0, 0x1b9ce0, SR_READ},
	{"PMCNTENCLR_EL0", REG_PMCNTENCLR_EL0, 0x1b9c40, SR_READ | SR_WRITE},
	{"PMCNTENSET_EL0", REG_PMCNTENSET_EL0, 0x1b9c20, SR_READ | SR_WRITE},
	{"PMCR_EL0", REG_PMCR_EL0, 0x1b9c00, SR_READ | SR_WRITE},
	{"PMEVCNTR0_EL0", REG_PMEVCNTR0_EL0, 0x1be800, SR_READ | SR_WRITE},
	{"PMEVCNTR1_EL0", REG_PMEVCNTR1_EL0, 0x1be820, SR_READ | SR_WRITE},
	{"PMEVCNTR2_EL0", REG_PMEVCNTR2_EL0, 0x1be840, SR_READ | SR_WRITE},
	{"PMEVCNTR3_EL0", REG_PMEVCNTR3_EL0, 0x1be860, SR_READ | SR_WRITE},
	{"PMEVCNTR4_EL0", REG_PMEVCNTR4_EL0, 0x1be880, SR_READ | SR_WRITE},
	{"PMEVCNTR5_EL0", REG_PMEVCNTR5_EL0, 0x1be8a0, SR_READ | SR_WRITE},
	{"PMEVCNTR6_EL0", REG_PMEVCNTR6_EL0, 0x1be8c0, SR_READ | SR_WRITE},
	{"PMEVCNTR7_EL0", REG_PMEVCNTR7_EL0, 0x1be8e0, SR_READ | SR_WRITE},
	{"PMEVCNTR8_EL0", REG_PMEVCNTR8_EL0, 0x1be900, SR_READ | SR_WRITE},
	{"PMEVCNTR9_EL0", REG_PMEVCNTR9_EL0, 0x1be920, SR_READ | SR_WRITE},
	{"PMEVCNTR10_EL0", REG_PMEVCNTR10_EL0, 0x1be940, SR_READ | SR_WRITE},
	{"PMEVCNTR11_EL0", REG_PMEVCNTR11_EL0, 0x1be960, SR_READ | SR_WRITE},
	{"PMEVCNTR12_EL0", REG_PMEVCNTR12_EL0, 0x1be980, SR_READ | SR_WRITE},
	{"PMEVCNTR13_EL0", REG_PMEVCNTR13_EL0, 0x1be9a0, SR_READ | SR_WRITE},
	{"PMEVCNTR14_EL0", REG_PMEVCNTR14_EL0, 0x1be9c0, SR_READ | SR_WRITE},
	{"PMEVCNTR15_EL0", REG_PMEVCNTR15_EL0, 0x1be9e0, SR_READ | SR_WRITE},
	{"PMEVCNTR16_EL0", REG_PMEVCNTR16_EL0, 0x1bea00, SR_READ | SR_WRITE},
	{"PMEVCNTR17_EL0", REG_PMEVCNTR17_EL0, 0x1bea20, SR_READ | SR_WRITE},
	{"PMEVCNTR18_EL0", REG_PMEVCNTR18_EL0, 0x1bea40, SR_READ | SR_WRITE},
	{"PMEVCNTR19_EL0", REG_PMEVCNTR19_EL0, 0x1bea60, SR_READ | SR_WRITE},
	{"PMEVCNTR20_EL0", REG_PMEVCNTR20_EL0, 0x1bea80, SR_READ | SR_WRITE},
	{"PMEVCNTR21_EL0", REG_PMEVCNTR21_EL0, 0x1beaa0, SR_READ | SR_WRITE},
	{"PMEVCNTR22_EL0", REG_PMEVCNTR22_EL0, 0x1beac0, SR_READ | SR_WRITE},
	{"PMEVCNTR23_EL0", REG_PMEVCNTR23_EL0, 0x1beae0, SR_READ | SR_WRITE},
	{"PMEVCNTR24_EL0", REG_PMEVCNTR24_EL0, 0x1beb00, SR_READ | SR_WRITE},
	{"PMEVCNTR25_EL0", REG_PMEVCNTR25_EL0, 0x1beb20, SR_READ | SR_WRITE},
	{"PMEVCNTR26_EL0", REG_PMEVCNTR26_EL0, 0x1beb40, SR_READ | SR_WRITE},
	{"PMEVCNTR27_EL0", REG_PMEVCNTR27_EL0, 0x1beb60, SR_READ | SR_WRITE},
	{"PMEVCNTR28_EL0", REG_PMEVCNTR28_EL0, 0x1beb80, SR_READ | SR_WRITE},
	{"PMEVCNTR29_EL0", REG_PMEVCNTR29_EL0, 0x1beba0, SR_READ | SR_WRITE},
	{"PMEVCNTR30_EL0", REG_PMEVCNTR30_EL0, 0x1bebc0, SR_READ | SR_WRITE},
	{"PMEVTYPER0_EL0", REG_PMEVTYPER0_EL0, 0x1bec00, SR_READ | SR_WRITE},
	{"PMEVTYPER1_EL0", REG_PMEVTYPER1_EL0, 0x1bec20, SR_READ | SR_WRITE},
	{"PMEVTYPER2_EL0", REG_PMEVTYPER2_EL0, 0x1bec40, SR_READ | SR_WRITE},
	{"PMEVTYPER3_EL0", REG_PMEVTYPER3_EL0, 0x1bec60, SR_READ | SR_WRITE},
	{"PMEVTYPER4_EL0", REG_PMEVTYPER4_EL0, 0x1bec80, SR_READ | SR_WRITE},
	{"PMEVTYPER5_EL0", REG_PMEVTYPER5_EL0, 0x1beca0, SR_READ | SR_WRITE},
	{"PMEVTYPER6_EL0", REG_PMEVTYPER6_EL0, 0x1becc0, SR_READ | SR_WRITE},
	{"PMEVTYPER7_EL0", REG_PMEVTYPER7_EL0, 0x1bece0, SR_READ | SR_WRITE},
	{"PMEVTYPER8_EL0", REG_PMEVTYPER8_EL0, 0x1bed00, SR_READ | SR_WRITE},
	{"PMEVTYPER9_EL0", REG_PMEVTYPER9_EL0, 0x1bed20, SR_READ | SR_WRITE},
	{"PMEVTYPER10_EL0", REG_PMEVTYPER10_EL0, 0x1bed40, SR_READ | SR_WRITE},
	{"PMEVTYPER11_EL0", REG_PMEVTYPER11_EL0, 0x1bed60, SR_READ | SR_WRITE},
	{"PMEVTYPER12_EL0", REG_PMEVTYPER12_EL0, 0x1bed80, SR_READ | SR_WRITE},
	{"PMEVTYPER13_EL0", REG_PMEVTYPER13_EL0, 0x1beda0, SR_READ | SR_WRITE},
	{"PMEVTYPER14_EL0", REG_PMEVTYPER14_EL0, 0x1bedc0, SR_READ | SR_WRITE},
	{"PMEVTYPER15_EL0", REG_PMEVTYPER15_EL0, 0x1bede0, SR_READ | SR_WRITE},
	{"PMEVTYPER16_EL0", REG_PMEVTYPER16_EL0, 0x1bee00, SR_READ | SR_WRITE},
	{"PMEVTYPER17_EL0", REG_PMEVTYPER17_EL0, 0x1bee20, SR_READ | SR_WRITE},
	{"PMEVTYPER18_EL0", REG_PMEVTYPER18_EL0, 0x1bee40, SR_READ | SR_WRITE},
	{"PMEVTYPER19_EL0", REG_PMEVTYPER19_EL0, 0x1bee60, SR_READ | SR_WRITE},
	{"PMEVTYPER20_EL0", REG_PMEVTYPER20_EL0, 0x1bee80, SR_READ | SR_WRITE},
	{"PMEVTYPER21_EL0", REG_PMEVTYPER21_EL0, 0x1beea0, SR_READ | SR_WRITE},
	{"PMEVTYPER22_EL0", REG_PMEVTYPER22_EL0, 0x1beec0, SR_READ | SR_WRITE},
	{"PMEVTYPER23_EL0", REG_PMEVTYPER23_EL0, 0x1beee0, SR_READ | SR_WRITE},
	{"PMEVTYPER24_EL0", REG_PMEVTYPER24_EL0, 0x1bef00, SR_READ | SR_WRITE},
	{"PMEVTYPER25_EL0", REG_PMEVTYPER25_EL0, 0x1bef20, SR_READ | SR_WRITE},
	{"PMEVTYPER26_EL0", REG_PMEVTYPER26_EL0, 0x1bef40, SR_READ | SR_WRITE},
	{"PMEVTYPER27_EL0", REG_PMEVTYPER27_EL0, 0x1bef60, SR_READ | SR_WRITE},
	{"PMEVTYPER28_EL0", REG_PMEVTYPER28_EL0, 0x1bef80, SR_READ | SR_WRITE},
	{"PMEVTYPER29_EL0", REG_PMEVTYPER29_EL0, 0x1befa0, SR_READ | SR_WRITE},
	{"PMEVTYPER30_EL0", REG_PMEVTYPER30_EL0, 0x1befc0, SR_READ | SR_WRITE},
	{"PMINTENCLR_EL1", REG_PMINTENCLR_EL1, 0x189e40, SR_READ | SR_WRITE},
	{"PMINTENSET_EL1", REG_PMINTENSET_EL1, 0x189e20, SR_READ | SR_WRITE},
	{"PMMIR_EL1", REG_PMMIR_EL1, 0x189ec0, SR_READ},
	{"PMOVSCLR_EL0", REG_PMOVSCLR_EL0, 0x1b9c60, SR_READ | SR_WRITE},
	{"PMOVSSET_EL0", REG_PMOVSSET_EL0, 0x1b9e60, SR_READ | SR_WRITE},
	{"PMSCR_EL1", REG_PMSCR_EL1, 0x189900, SR_READ | SR_WRITE},
	{"PMSELR_EL0", REG_PMSELR_EL0, 0x1b9ca0, SR_READ | SR_WRITE},
	{"PMSEVFR_EL1", REG_PMSEVFR_EL1, 0x1899a0, SR_READ | SR_WRITE},
	{"PMSFCR_EL1", REG_PMSFCR_EL1, 0x189980, SR_READ | SR_WRITE},
	{"PMSICR_EL1", REG_PMSICR_EL1, 0x189940, SR_READ | SR_WRITE},
	{"PMSIDR_EL1", REG_PMSIDR_EL1, 0x1899e0, SR_READ},
	{"PMSIRR_EL1", REG_PMSIRR_EL1, 0x189960, SR_READ | SR_WRITE},
	{"PMSLATFR_EL1", REG_PMSLATFR_EL1, 0x1899c0, SR_READ | SR_WRITE},
	{"PMSWINC_EL0", REG_PMSWINC_EL0, 0x1b9c80, SR_WRITE},
	{"PMUSERENR_EL0", REG_PMUSERENR_EL0, 0x1b9e00, SR_READ | SR_WRITE},
	{"PMXEVCNTR_EL0", REG_PMXEVCNTR_EL0, 0x1b9d40, SR_READ | SR_WRITE},
	{"PMXEVTYPER_EL0", REG_PMXEVTYPER_EL0, 0x1b9d20, SR_READ | SR_WRITE},
	{"REVIDR_EL1", REG_REVIDR_EL1, 0x1800c0, SR_READ},
	{"RGSR_EL1", REG_RGSR_EL1, 0x1810a0, SR_READ | SR_WRITE},
	{"RMR_EL1", REG_RMR_EL1, 0x18c040, SR_READ | SR_WRITE},
	{"RNDR", REG_RNDR, 0x1b2400, SR_READ},
	{"RNDRRS", REG_RNDRRS, 0x1b2420, SR_READ},
	{"RVBAR_EL1", REG_RVBAR_EL1, 0x18c020, SR_READ},
	{"SCTLR_EL1", REG_SCTLR_EL1, 0x181000, SR_READ | SR_WRITE},
	{"SCXTNUM_EL0", REG_SCXTNUM_EL0, 0x1bd0e0, SR_READ | SR_WRITE},
	{"SCXTNUM_EL1", REG_SCXTNUM_EL1, 0x18d0e0, SR_READ | SR_WRITE},
	{"SP_EL0", REG_SP_EL0, 0x184100, SR_READ | SR_WRITE},
	{"SP_EL1", REG_SP_EL1, 0x1c4100, SR_READ | SR_WRITE},
	{"SPSel", REG_SPSel, 0x184200, SR_READ | SR_WRITE},
	{"SPSR_abt", REG_SPSR_abt, 0x1c4320, SR_READ | SR_WRITE},
	{"SPSR_EL1", REG_SPSR_EL1, 0x184000, SR_READ | SR_WRITE},
	{"SPSR_fiq", REG_SPSR_fiq, 0x1c4360, SR_READ | SR_WRITE},
	{"SPSR_irq", REG_SPSR_irq, 0x1c4300, SR_READ | SR_WRITE},
	{"SPSR_und", REG_SPSR_und, 0x1c4340, SR_READ | SR_WRITE},
	{"SSBS", REG_SSBS, 0x1b42c0, SR_READ | SR_WRITE},
	{"TCO", REG_TCO, 0x1b42e0, SR_READ | SR_WRITE},
	{"TCR_EL1", REG_TCR_EL1, 0x182040, SR_READ | SR_WRITE},
	{"TFSR_EL1", REG_TFSR_EL1, 0x185600, SR_READ | SR_WRITE},
	{"TFSRE0_EL1", REG_TFSRE0_EL1, 0x185620, SR_READ | SR_WRITE},
	{"TPIDR_EL0", REG_TPIDR_EL0, 0x1bd040, SR_READ | SR_WRITE},
	{"TPIDR_EL1", REG_TPIDR_EL1, 0x18d080, SR_READ | SR_WRITE},
	{"TPIDRRO_EL0", REG_TPIDRRO_EL0, 0x1bd060, SR_READ | SR_WRITE},
	{"TRFCR_EL1", REG_TRFCR_EL1, 0x181220, SR_READ | SR_WRITE},
	{"TTBR0_EL1", REG_TTBR0_EL1, 0x182000, SR_READ | SR_WRITE},
	{"TTBR1_EL1", REG_TTBR1_EL1, 0x182020, SR_READ | SR_WRITE},
	{"UAO", REG_UAO, 0x184280, SR_READ | SR_WRITE},
	{"VBAR_EL1", REG_VBAR_EL1, 0x18c000, SR_READ | SR_WRITE},
	{"ZCR_EL1", REG_ZCR_EL1, 0x181200, SR_READ | SR_WRITE},
}

func SysRegEnc(r int16) (string, uint32, uint8) {
	// The automatic generator guarantees that the order
	// of Reg in SystemReg struct is consistent with the
	// order of system register declarations
	if r <= SYSREG_BEGIN || r >= SYSREG_END {
		return "", 0, 0
	}
	v := SystemReg[r-SYSREG_BEGIN-1]
	return v.Name, v.Enc, v.AccessFlags
}
