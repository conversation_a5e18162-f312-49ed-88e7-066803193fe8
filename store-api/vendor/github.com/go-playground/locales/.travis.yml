language: go
go:
  - 1.13.1
  - tip
matrix:
  allow_failures:
    - go: tip

notifications:
  email:
    recipients: <EMAIL>
    on_success: change
    on_failure: always

before_install:
  - go install github.com/mattn/goveralls

# Only clone the most recent commit.
git:
  depth: 1

script:
  - go test -v -race -covermode=atomic -coverprofile=coverage.coverprofile ./...

after_success: |
  goveralls -coverprofile=coverage.coverprofile -service travis-ci -repotoken $COVERALLS_TOKEN