// Code generated by "stringer -type=SyncMarker -trimprefix=Sync"; DO NOT EDIT.

package pkgbits

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[SyncEOF-1]
	_ = x[SyncBool-2]
	_ = x[SyncInt64-3]
	_ = x[SyncUint64-4]
	_ = x[SyncString-5]
	_ = x[SyncValue-6]
	_ = x[SyncVal-7]
	_ = x[SyncRelocs-8]
	_ = x[SyncReloc-9]
	_ = x[SyncUseReloc-10]
	_ = x[SyncPublic-11]
	_ = x[SyncPos-12]
	_ = x[SyncPosBase-13]
	_ = x[SyncObject-14]
	_ = x[SyncObject1-15]
	_ = x[SyncPkg-16]
	_ = x[SyncPkgDef-17]
	_ = x[SyncMethod-18]
	_ = x[SyncType-19]
	_ = x[SyncTypeIdx-20]
	_ = x[SyncTypeParamNames-21]
	_ = x[SyncSignature-22]
	_ = x[SyncParams-23]
	_ = x[SyncParam-24]
	_ = x[SyncCodeObj-25]
	_ = x[SyncSym-26]
	_ = x[SyncLocalIdent-27]
	_ = x[SyncSelector-28]
	_ = x[SyncPrivate-29]
	_ = x[SyncFuncExt-30]
	_ = x[SyncVarExt-31]
	_ = x[SyncTypeExt-32]
	_ = x[SyncPragma-33]
	_ = x[SyncExprList-34]
	_ = x[SyncExprs-35]
	_ = x[SyncExpr-36]
	_ = x[SyncExprType-37]
	_ = x[SyncAssign-38]
	_ = x[SyncOp-39]
	_ = x[SyncFuncLit-40]
	_ = x[SyncCompLit-41]
	_ = x[SyncDecl-42]
	_ = x[SyncFuncBody-43]
	_ = x[SyncOpenScope-44]
	_ = x[SyncCloseScope-45]
	_ = x[SyncCloseAnotherScope-46]
	_ = x[SyncDeclNames-47]
	_ = x[SyncDeclName-48]
	_ = x[SyncStmts-49]
	_ = x[SyncBlockStmt-50]
	_ = x[SyncIfStmt-51]
	_ = x[SyncForStmt-52]
	_ = x[SyncSwitchStmt-53]
	_ = x[SyncRangeStmt-54]
	_ = x[SyncCaseClause-55]
	_ = x[SyncCommClause-56]
	_ = x[SyncSelectStmt-57]
	_ = x[SyncDecls-58]
	_ = x[SyncLabeledStmt-59]
	_ = x[SyncUseObjLocal-60]
	_ = x[SyncAddLocal-61]
	_ = x[SyncLinkname-62]
	_ = x[SyncStmt1-63]
	_ = x[SyncStmtsEnd-64]
	_ = x[SyncLabel-65]
	_ = x[SyncOptLabel-66]
}

const _SyncMarker_name = "EOFBoolInt64Uint64StringValueValRelocsRelocUseRelocPublicPosPosBaseObjectObject1PkgPkgDefMethodTypeTypeIdxTypeParamNamesSignatureParamsParamCodeObjSymLocalIdentSelectorPrivateFuncExtVarExtTypeExtPragmaExprListExprsExprExprTypeAssignOpFuncLitCompLitDeclFuncBodyOpenScopeCloseScopeCloseAnotherScopeDeclNamesDeclNameStmtsBlockStmtIfStmtForStmtSwitchStmtRangeStmtCaseClauseCommClauseSelectStmtDeclsLabeledStmtUseObjLocalAddLocalLinknameStmt1StmtsEndLabelOptLabel"

var _SyncMarker_index = [...]uint16{0, 3, 7, 12, 18, 24, 29, 32, 38, 43, 51, 57, 60, 67, 73, 80, 83, 89, 95, 99, 106, 120, 129, 135, 140, 147, 150, 160, 168, 175, 182, 188, 195, 201, 209, 214, 218, 226, 232, 234, 241, 248, 252, 260, 269, 279, 296, 305, 313, 318, 327, 333, 340, 350, 359, 369, 379, 389, 394, 405, 416, 424, 432, 437, 445, 450, 458}

func (i SyncMarker) String() string {
	i -= 1
	if i < 0 || i >= SyncMarker(len(_SyncMarker_index)-1) {
		return "SyncMarker(" + strconv.FormatInt(int64(i+1), 10) + ")"
	}
	return _SyncMarker_name[_SyncMarker_index[i]:_SyncMarker_index[i+1]]
}
