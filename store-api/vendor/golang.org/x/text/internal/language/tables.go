// Code generated by running "go generate" in golang.org/x/text. DO NOT EDIT.

package language

import "golang.org/x/text/internal/tag"

// CLDRVersion is the CLDR version from which the tables in this package are derived.
const CLDRVersion = "32"

const NumLanguages = 8798

const NumScripts = 261

const NumRegions = 358

type FromTo struct {
	From uint16
	To   uint16
}

const nonCanonicalUnd = 1201
const (
	_af  = 22
	_am  = 39
	_ar  = 58
	_az  = 88
	_bg  = 126
	_bn  = 165
	_ca  = 215
	_cs  = 250
	_da  = 257
	_de  = 269
	_el  = 310
	_en  = 313
	_es  = 318
	_et  = 320
	_fa  = 328
	_fi  = 337
	_fil = 339
	_fr  = 350
	_gu  = 420
	_he  = 444
	_hi  = 446
	_hr  = 465
	_hu  = 469
	_hy  = 471
	_id  = 481
	_is  = 504
	_it  = 505
	_ja  = 512
	_ka  = 528
	_kk  = 578
	_km  = 586
	_kn  = 593
	_ko  = 596
	_ky  = 650
	_lo  = 696
	_lt  = 704
	_lv  = 711
	_mk  = 767
	_ml  = 772
	_mn  = 779
	_mo  = 784
	_mr  = 795
	_ms  = 799
	_mul = 806
	_my  = 817
	_nb  = 839
	_ne  = 849
	_nl  = 871
	_no  = 879
	_pa  = 925
	_pl  = 947
	_pt  = 960
	_ro  = 988
	_ru  = 994
	_sh  = 1031
	_si  = 1036
	_sk  = 1042
	_sl  = 1046
	_sq  = 1073
	_sr  = 1074
	_sv  = 1092
	_sw  = 1093
	_ta  = 1104
	_te  = 1121
	_th  = 1131
	_tl  = 1146
	_tn  = 1152
	_tr  = 1162
	_uk  = 1198
	_ur  = 1204
	_uz  = 1212
	_vi  = 1219
	_zh  = 1321
	_zu  = 1327
	_jbo = 515
	_ami = 1650
	_bnn = 2357
	_hak = 438
	_tlh = 14467
	_lb  = 661
	_nv  = 899
	_pwn = 12055
	_tao = 14188
	_tay = 14198
	_tsu = 14662
	_nn  = 874
	_sfb = 13629
	_vgt = 15701
	_sgg = 13660
	_cmn = 3007
	_nan = 835
	_hsn = 467
)

const langPrivateStart = 0x2f72

const langPrivateEnd = 0x3179

// lang holds an alphabetically sorted list of ISO-639 language identifiers.
// All entries are 4 bytes. The index of the identifier (divided by 4) is the language tag.
// For 2-byte language identifiers, the two successive bytes have the following meaning:
//   - if the first letter of the 2- and 3-letter ISO codes are the same:
//     the second and third letter of the 3-letter ISO code.
//   - otherwise: a 0 and a by 2 bits right-shifted index into altLangISO3.
//
// For 3-byte language identifiers the 4th byte is 0.
const lang tag.Index = "" + // Size: 5324 bytes
	"---\x00aaaraai\x00aak\x00aau\x00abbkabi\x00abq\x00abr\x00abt\x00aby\x00a" +
	"cd\x00ace\x00ach\x00ada\x00ade\x00adj\x00ady\x00adz\x00aeveaeb\x00aey" +
	"\x00affragc\x00agd\x00agg\x00agm\x00ago\x00agq\x00aha\x00ahl\x00aho\x00a" +
	"jg\x00akkaakk\x00ala\x00ali\x00aln\x00alt\x00ammhamm\x00amn\x00amo\x00am" +
	"p\x00anrganc\x00ank\x00ann\x00any\x00aoj\x00aom\x00aoz\x00apc\x00apd\x00" +
	"ape\x00apr\x00aps\x00apz\x00arraarc\x00arh\x00arn\x00aro\x00arq\x00ars" +
	"\x00ary\x00arz\x00assmasa\x00ase\x00asg\x00aso\x00ast\x00ata\x00atg\x00a" +
	"tj\x00auy\x00avvaavl\x00avn\x00avt\x00avu\x00awa\x00awb\x00awo\x00awx" +
	"\x00ayymayb\x00azzebaakbal\x00ban\x00bap\x00bar\x00bas\x00bav\x00bax\x00" +
	"bba\x00bbb\x00bbc\x00bbd\x00bbj\x00bbp\x00bbr\x00bcf\x00bch\x00bci\x00bc" +
	"m\x00bcn\x00bco\x00bcq\x00bcu\x00bdd\x00beelbef\x00beh\x00bej\x00bem\x00" +
	"bet\x00bew\x00bex\x00bez\x00bfd\x00bfq\x00bft\x00bfy\x00bgulbgc\x00bgn" +
	"\x00bgx\x00bhihbhb\x00bhg\x00bhi\x00bhk\x00bhl\x00bho\x00bhy\x00biisbib" +
	"\x00big\x00bik\x00bim\x00bin\x00bio\x00biq\x00bjh\x00bji\x00bjj\x00bjn" +
	"\x00bjo\x00bjr\x00bjt\x00bjz\x00bkc\x00bkm\x00bkq\x00bku\x00bkv\x00blt" +
	"\x00bmambmh\x00bmk\x00bmq\x00bmu\x00bnenbng\x00bnm\x00bnp\x00boodboj\x00" +
	"bom\x00bon\x00bpy\x00bqc\x00bqi\x00bqp\x00bqv\x00brrebra\x00brh\x00brx" +
	"\x00brz\x00bsosbsj\x00bsq\x00bss\x00bst\x00bto\x00btt\x00btv\x00bua\x00b" +
	"uc\x00bud\x00bug\x00buk\x00bum\x00buo\x00bus\x00buu\x00bvb\x00bwd\x00bwr" +
	"\x00bxh\x00bye\x00byn\x00byr\x00bys\x00byv\x00byx\x00bza\x00bze\x00bzf" +
	"\x00bzh\x00bzw\x00caatcan\x00cbj\x00cch\x00ccp\x00ceheceb\x00cfa\x00cgg" +
	"\x00chhachk\x00chm\x00cho\x00chp\x00chr\x00cja\x00cjm\x00cjv\x00ckb\x00c" +
	"kl\x00cko\x00cky\x00cla\x00cme\x00cmg\x00cooscop\x00cps\x00crrecrh\x00cr" +
	"j\x00crk\x00crl\x00crm\x00crs\x00csescsb\x00csw\x00ctd\x00cuhucvhvcyymda" +
	"andad\x00daf\x00dag\x00dah\x00dak\x00dar\x00dav\x00dbd\x00dbq\x00dcc\x00" +
	"ddn\x00deeuded\x00den\x00dga\x00dgh\x00dgi\x00dgl\x00dgr\x00dgz\x00dia" +
	"\x00dje\x00dnj\x00dob\x00doi\x00dop\x00dow\x00dri\x00drs\x00dsb\x00dtm" +
	"\x00dtp\x00dts\x00dty\x00dua\x00duc\x00dud\x00dug\x00dvivdva\x00dww\x00d" +
	"yo\x00dyu\x00dzzodzg\x00ebu\x00eeweefi\x00egl\x00egy\x00eka\x00eky\x00el" +
	"llema\x00emi\x00enngenn\x00enq\x00eopoeri\x00es\x00\x05esu\x00etstetr" +
	"\x00ett\x00etu\x00etx\x00euusewo\x00ext\x00faasfaa\x00fab\x00fag\x00fai" +
	"\x00fan\x00ffulffi\x00ffm\x00fiinfia\x00fil\x00fit\x00fjijflr\x00fmp\x00" +
	"foaofod\x00fon\x00for\x00fpe\x00fqs\x00frrafrc\x00frp\x00frr\x00frs\x00f" +
	"ub\x00fud\x00fue\x00fuf\x00fuh\x00fuq\x00fur\x00fuv\x00fuy\x00fvr\x00fyr" +
	"ygalegaa\x00gaf\x00gag\x00gah\x00gaj\x00gam\x00gan\x00gaw\x00gay\x00gba" +
	"\x00gbf\x00gbm\x00gby\x00gbz\x00gcr\x00gdlagde\x00gdn\x00gdr\x00geb\x00g" +
	"ej\x00gel\x00gez\x00gfk\x00ggn\x00ghs\x00gil\x00gim\x00gjk\x00gjn\x00gju" +
	"\x00gkn\x00gkp\x00gllgglk\x00gmm\x00gmv\x00gnrngnd\x00gng\x00god\x00gof" +
	"\x00goi\x00gom\x00gon\x00gor\x00gos\x00got\x00grb\x00grc\x00grt\x00grw" +
	"\x00gsw\x00guujgub\x00guc\x00gud\x00gur\x00guw\x00gux\x00guz\x00gvlvgvf" +
	"\x00gvr\x00gvs\x00gwc\x00gwi\x00gwt\x00gyi\x00haauhag\x00hak\x00ham\x00h" +
	"aw\x00haz\x00hbb\x00hdy\x00heebhhy\x00hiinhia\x00hif\x00hig\x00hih\x00hi" +
	"l\x00hla\x00hlu\x00hmd\x00hmt\x00hnd\x00hne\x00hnj\x00hnn\x00hno\x00homo" +
	"hoc\x00hoj\x00hot\x00hrrvhsb\x00hsn\x00htathuunhui\x00hyyehzerianaian" +
	"\x00iar\x00iba\x00ibb\x00iby\x00ica\x00ich\x00idndidd\x00idi\x00idu\x00i" +
	"eleife\x00igboigb\x00ige\x00iiiiijj\x00ikpkikk\x00ikt\x00ikw\x00ikx\x00i" +
	"lo\x00imo\x00inndinh\x00iodoiou\x00iri\x00isslittaiukuiw\x00\x03iwm\x00i" +
	"ws\x00izh\x00izi\x00japnjab\x00jam\x00jbo\x00jbu\x00jen\x00jgk\x00jgo" +
	"\x00ji\x00\x06jib\x00jmc\x00jml\x00jra\x00jut\x00jvavjwavkaatkaa\x00kab" +
	"\x00kac\x00kad\x00kai\x00kaj\x00kam\x00kao\x00kbd\x00kbm\x00kbp\x00kbq" +
	"\x00kbx\x00kby\x00kcg\x00kck\x00kcl\x00kct\x00kde\x00kdh\x00kdl\x00kdt" +
	"\x00kea\x00ken\x00kez\x00kfo\x00kfr\x00kfy\x00kgonkge\x00kgf\x00kgp\x00k" +
	"ha\x00khb\x00khn\x00khq\x00khs\x00kht\x00khw\x00khz\x00kiikkij\x00kiu" +
	"\x00kiw\x00kjuakjd\x00kjg\x00kjs\x00kjy\x00kkazkkc\x00kkj\x00klalkln\x00" +
	"klq\x00klt\x00klx\x00kmhmkmb\x00kmh\x00kmo\x00kms\x00kmu\x00kmw\x00knank" +
	"nf\x00knp\x00koorkoi\x00kok\x00kol\x00kos\x00koz\x00kpe\x00kpf\x00kpo" +
	"\x00kpr\x00kpx\x00kqb\x00kqf\x00kqs\x00kqy\x00kraukrc\x00kri\x00krj\x00k" +
	"rl\x00krs\x00kru\x00ksasksb\x00ksd\x00ksf\x00ksh\x00ksj\x00ksr\x00ktb" +
	"\x00ktm\x00kto\x00kuurkub\x00kud\x00kue\x00kuj\x00kum\x00kun\x00kup\x00k" +
	"us\x00kvomkvg\x00kvr\x00kvx\x00kw\x00\x01kwj\x00kwo\x00kxa\x00kxc\x00kxm" +
	"\x00kxp\x00kxw\x00kxz\x00kyirkye\x00kyx\x00kzr\x00laatlab\x00lad\x00lag" +
	"\x00lah\x00laj\x00las\x00lbtzlbe\x00lbu\x00lbw\x00lcm\x00lcp\x00ldb\x00l" +
	"ed\x00lee\x00lem\x00lep\x00leq\x00leu\x00lez\x00lguglgg\x00liimlia\x00li" +
	"d\x00lif\x00lig\x00lih\x00lij\x00lis\x00ljp\x00lki\x00lkt\x00lle\x00lln" +
	"\x00lmn\x00lmo\x00lmp\x00lninlns\x00lnu\x00loaoloj\x00lok\x00lol\x00lor" +
	"\x00los\x00loz\x00lrc\x00ltitltg\x00luublua\x00luo\x00luy\x00luz\x00lvav" +
	"lwl\x00lzh\x00lzz\x00mad\x00maf\x00mag\x00mai\x00mak\x00man\x00mas\x00ma" +
	"w\x00maz\x00mbh\x00mbo\x00mbq\x00mbu\x00mbw\x00mci\x00mcp\x00mcq\x00mcr" +
	"\x00mcu\x00mda\x00mde\x00mdf\x00mdh\x00mdj\x00mdr\x00mdx\x00med\x00mee" +
	"\x00mek\x00men\x00mer\x00met\x00meu\x00mfa\x00mfe\x00mfn\x00mfo\x00mfq" +
	"\x00mglgmgh\x00mgl\x00mgo\x00mgp\x00mgy\x00mhahmhi\x00mhl\x00mirimif\x00" +
	"min\x00mis\x00miw\x00mkkdmki\x00mkl\x00mkp\x00mkw\x00mlalmle\x00mlp\x00m" +
	"ls\x00mmo\x00mmu\x00mmx\x00mnonmna\x00mnf\x00mni\x00mnw\x00moolmoa\x00mo" +
	"e\x00moh\x00mos\x00mox\x00mpp\x00mps\x00mpt\x00mpx\x00mql\x00mrarmrd\x00" +
	"mrj\x00mro\x00mssamtltmtc\x00mtf\x00mti\x00mtr\x00mua\x00mul\x00mur\x00m" +
	"us\x00mva\x00mvn\x00mvy\x00mwk\x00mwr\x00mwv\x00mxc\x00mxm\x00myyamyk" +
	"\x00mym\x00myv\x00myw\x00myx\x00myz\x00mzk\x00mzm\x00mzn\x00mzp\x00mzw" +
	"\x00mzz\x00naaunac\x00naf\x00nah\x00nak\x00nan\x00nap\x00naq\x00nas\x00n" +
	"bobnca\x00nce\x00ncf\x00nch\x00nco\x00ncu\x00nddendc\x00nds\x00neepneb" +
	"\x00new\x00nex\x00nfr\x00ngdonga\x00ngb\x00ngl\x00nhb\x00nhe\x00nhw\x00n" +
	"if\x00nii\x00nij\x00nin\x00niu\x00niy\x00niz\x00njo\x00nkg\x00nko\x00nll" +
	"dnmg\x00nmz\x00nnnonnf\x00nnh\x00nnk\x00nnm\x00noornod\x00noe\x00non\x00" +
	"nop\x00nou\x00nqo\x00nrblnrb\x00nsk\x00nsn\x00nso\x00nss\x00ntm\x00ntr" +
	"\x00nui\x00nup\x00nus\x00nuv\x00nux\x00nvavnwb\x00nxq\x00nxr\x00nyyanym" +
	"\x00nyn\x00nzi\x00occiogc\x00ojjiokr\x00okv\x00omrmong\x00onn\x00ons\x00" +
	"opm\x00orrioro\x00oru\x00osssosa\x00ota\x00otk\x00ozm\x00paanpag\x00pal" +
	"\x00pam\x00pap\x00pau\x00pbi\x00pcd\x00pcm\x00pdc\x00pdt\x00ped\x00peo" +
	"\x00pex\x00pfl\x00phl\x00phn\x00pilipil\x00pip\x00pka\x00pko\x00plolpla" +
	"\x00pms\x00png\x00pnn\x00pnt\x00pon\x00ppo\x00pra\x00prd\x00prg\x00psusp" +
	"ss\x00ptorptp\x00puu\x00pwa\x00quuequc\x00qug\x00rai\x00raj\x00rao\x00rc" +
	"f\x00rej\x00rel\x00res\x00rgn\x00rhg\x00ria\x00rif\x00rjs\x00rkt\x00rmoh" +
	"rmf\x00rmo\x00rmt\x00rmu\x00rnunrna\x00rng\x00roonrob\x00rof\x00roo\x00r" +
	"ro\x00rtm\x00ruusrue\x00rug\x00rw\x00\x04rwk\x00rwo\x00ryu\x00saansaf" +
	"\x00sah\x00saq\x00sas\x00sat\x00sav\x00saz\x00sba\x00sbe\x00sbp\x00scrds" +
	"ck\x00scl\x00scn\x00sco\x00scs\x00sdndsdc\x00sdh\x00semesef\x00seh\x00se" +
	"i\x00ses\x00sgagsga\x00sgs\x00sgw\x00sgz\x00sh\x00\x02shi\x00shk\x00shn" +
	"\x00shu\x00siinsid\x00sig\x00sil\x00sim\x00sjr\x00sklkskc\x00skr\x00sks" +
	"\x00sllvsld\x00sli\x00sll\x00sly\x00smmosma\x00smi\x00smj\x00smn\x00smp" +
	"\x00smq\x00sms\x00snnasnc\x00snk\x00snp\x00snx\x00sny\x00soomsok\x00soq" +
	"\x00sou\x00soy\x00spd\x00spl\x00sps\x00sqqisrrpsrb\x00srn\x00srr\x00srx" +
	"\x00ssswssd\x00ssg\x00ssy\x00stotstk\x00stq\x00suunsua\x00sue\x00suk\x00" +
	"sur\x00sus\x00svweswwaswb\x00swc\x00swg\x00swp\x00swv\x00sxn\x00sxw\x00s" +
	"yl\x00syr\x00szl\x00taamtaj\x00tal\x00tan\x00taq\x00tbc\x00tbd\x00tbf" +
	"\x00tbg\x00tbo\x00tbw\x00tbz\x00tci\x00tcy\x00tdd\x00tdg\x00tdh\x00teelt" +
	"ed\x00tem\x00teo\x00tet\x00tfi\x00tggktgc\x00tgo\x00tgu\x00thhathl\x00th" +
	"q\x00thr\x00tiirtif\x00tig\x00tik\x00tim\x00tio\x00tiv\x00tkuktkl\x00tkr" +
	"\x00tkt\x00tlgltlf\x00tlx\x00tly\x00tmh\x00tmy\x00tnsntnh\x00toontof\x00" +
	"tog\x00toq\x00tpi\x00tpm\x00tpz\x00tqo\x00trurtru\x00trv\x00trw\x00tssot" +
	"sd\x00tsf\x00tsg\x00tsj\x00tsw\x00ttatttd\x00tte\x00ttj\x00ttr\x00tts" +
	"\x00ttt\x00tuh\x00tul\x00tum\x00tuq\x00tvd\x00tvl\x00tvu\x00twwitwh\x00t" +
	"wq\x00txg\x00tyahtya\x00tyv\x00tzm\x00ubu\x00udm\x00ugiguga\x00ukkruli" +
	"\x00umb\x00und\x00unr\x00unx\x00urrduri\x00urt\x00urw\x00usa\x00utr\x00u" +
	"vh\x00uvl\x00uzzbvag\x00vai\x00van\x00veenvec\x00vep\x00viievic\x00viv" +
	"\x00vls\x00vmf\x00vmw\x00voolvot\x00vro\x00vun\x00vut\x00walnwae\x00waj" +
	"\x00wal\x00wan\x00war\x00wbp\x00wbq\x00wbr\x00wci\x00wer\x00wgi\x00whg" +
	"\x00wib\x00wiu\x00wiv\x00wja\x00wji\x00wls\x00wmo\x00wnc\x00wni\x00wnu" +
	"\x00woolwob\x00wos\x00wrs\x00wsk\x00wtm\x00wuu\x00wuv\x00wwa\x00xav\x00x" +
	"bi\x00xcr\x00xes\x00xhhoxla\x00xlc\x00xld\x00xmf\x00xmn\x00xmr\x00xna" +
	"\x00xnr\x00xog\x00xon\x00xpr\x00xrb\x00xsa\x00xsi\x00xsm\x00xsr\x00xwe" +
	"\x00yam\x00yao\x00yap\x00yas\x00yat\x00yav\x00yay\x00yaz\x00yba\x00ybb" +
	"\x00yby\x00yer\x00ygr\x00ygw\x00yiidyko\x00yle\x00ylg\x00yll\x00yml\x00y" +
	"ooryon\x00yrb\x00yre\x00yrl\x00yss\x00yua\x00yue\x00yuj\x00yut\x00yuw" +
	"\x00zahazag\x00zbl\x00zdj\x00zea\x00zgh\x00zhhozhx\x00zia\x00zlm\x00zmi" +
	"\x00zne\x00zuulzxx\x00zza\x00\xff\xff\xff\xff"

const langNoIndexOffset = 1330

// langNoIndex is a bit vector of all 3-letter language codes that are not used as an index
// in lookup tables. The language ids for these language codes are derived directly
// from the letters and are not consecutive.
// Size: 2197 bytes, 2197 elements
var langNoIndex = [2197]uint8{
	// Entry 0 - 3F
	0xff, 0xf8, 0xed, 0xfe, 0xeb, 0xd3, 0x3b, 0xd2,
	0xfb, 0xbf, 0x7a, 0xfa, 0x37, 0x1d, 0x3c, 0x57,
	0x6e, 0x97, 0x73, 0x38, 0xfb, 0xea, 0xbf, 0x70,
	0xad, 0x03, 0xff, 0xff, 0xcf, 0x05, 0x84, 0x72,
	0xe9, 0xbf, 0xfd, 0xbf, 0xbf, 0xf7, 0xfd, 0x77,
	0x0f, 0xff, 0xef, 0x6f, 0xff, 0xfb, 0xdf, 0xe2,
	0xc9, 0xf8, 0x7f, 0x7e, 0x4d, 0xbc, 0x0a, 0x6a,
	0x7c, 0xea, 0xe3, 0xfa, 0x7a, 0xbf, 0x67, 0xff,
	// Entry 40 - 7F
	0xff, 0xff, 0xff, 0xdf, 0x2a, 0x54, 0x91, 0xc0,
	0x5d, 0xe3, 0x97, 0x14, 0x07, 0x20, 0xdd, 0xed,
	0x9f, 0x3f, 0xc9, 0x21, 0xf8, 0x3f, 0x94, 0x35,
	0x7c, 0x5f, 0xff, 0x5f, 0x8e, 0x6e, 0xdf, 0xff,
	0xff, 0xff, 0x55, 0x7c, 0xd3, 0xfd, 0xbf, 0xb5,
	0x7b, 0xdf, 0x7f, 0xf7, 0xca, 0xfe, 0xdb, 0xa3,
	0xa8, 0xff, 0x1f, 0x67, 0x7d, 0xeb, 0xef, 0xce,
	0xff, 0xff, 0x9f, 0xff, 0xb7, 0xef, 0xfe, 0xcf,
	// Entry 80 - BF
	0xdb, 0xff, 0xf3, 0xcd, 0xfb, 0x7f, 0xff, 0xff,
	0xbb, 0xee, 0xf7, 0xbd, 0xdb, 0xff, 0x5f, 0xf7,
	0xfd, 0xf2, 0xfd, 0xff, 0x5e, 0x2f, 0x3b, 0xba,
	0x7e, 0xff, 0xff, 0xfe, 0xf7, 0xff, 0xdd, 0xff,
	0xfd, 0xdf, 0xfb, 0xfe, 0x9d, 0xb4, 0xd3, 0xff,
	0xef, 0xff, 0xdf, 0xf7, 0x7f, 0xb7, 0xfd, 0xd5,
	0xa5, 0x77, 0x40, 0xff, 0x9c, 0xc1, 0x41, 0x2c,
	0x08, 0x21, 0x41, 0x00, 0x50, 0x40, 0x00, 0x80,
	// Entry C0 - FF
	0xfb, 0x4a, 0xf2, 0x9f, 0xb4, 0x42, 0x41, 0x96,
	0x1b, 0x14, 0x08, 0xf3, 0x2b, 0xe7, 0x17, 0x56,
	0x05, 0x7d, 0x0e, 0x1c, 0x37, 0x7f, 0xf3, 0xef,
	0x97, 0xff, 0x5d, 0x38, 0x64, 0x08, 0x00, 0x10,
	0xbc, 0x85, 0xaf, 0xdf, 0xff, 0xff, 0x7b, 0x35,
	0x3e, 0xc7, 0xc7, 0xdf, 0xff, 0x01, 0x81, 0x00,
	0xb0, 0x05, 0x80, 0x00, 0x20, 0x00, 0x00, 0x03,
	0x40, 0x00, 0x40, 0x92, 0x21, 0x50, 0xb1, 0x5d,
	// Entry 100 - 13F
	0xfd, 0xdc, 0xbe, 0x5e, 0x00, 0x00, 0x02, 0x64,
	0x0d, 0x19, 0x41, 0xdf, 0x79, 0x22, 0x00, 0x00,
	0x00, 0x5e, 0x64, 0xdc, 0x24, 0xe5, 0xd9, 0xe3,
	0xfe, 0xff, 0xfd, 0xcb, 0x9f, 0x14, 0x41, 0x0c,
	0x86, 0x00, 0xd1, 0x00, 0xf0, 0xc7, 0x67, 0x5f,
	0x56, 0x99, 0x5e, 0xb5, 0x6c, 0xaf, 0x03, 0x00,
	0x02, 0x00, 0x00, 0x00, 0xc0, 0x37, 0xda, 0x56,
	0x90, 0x6d, 0x01, 0x2e, 0x96, 0x69, 0x20, 0xfb,
	// Entry 140 - 17F
	0xff, 0x3f, 0x00, 0x00, 0x00, 0x01, 0x0c, 0x16,
	0x03, 0x00, 0x00, 0xb0, 0x14, 0x23, 0x50, 0x06,
	0x0a, 0x00, 0x01, 0x00, 0x00, 0x10, 0x11, 0x09,
	0x00, 0x00, 0x60, 0x10, 0x00, 0x00, 0x00, 0x10,
	0x00, 0x00, 0x44, 0x00, 0x00, 0x10, 0x00, 0x05,
	0x08, 0x00, 0x00, 0x05, 0x00, 0x80, 0x28, 0x04,
	0x00, 0x00, 0x40, 0xd5, 0x2d, 0x00, 0x64, 0x35,
	0x24, 0x52, 0xf4, 0xd5, 0xbf, 0x62, 0xc9, 0x03,
	// Entry 180 - 1BF
	0x00, 0x80, 0x00, 0x40, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x04, 0x13, 0x39, 0x01, 0xdd, 0x57, 0x98,
	0x21, 0x18, 0x81, 0x08, 0x00, 0x01, 0x40, 0x82,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x01, 0x40, 0x00, 0x44, 0x00, 0x00, 0x80, 0xea,
	0xa9, 0x39, 0x00, 0x02, 0x00, 0x00, 0x00, 0x04,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x02, 0x00, 0x00, 0x00,
	// Entry 1C0 - 1FF
	0x00, 0x03, 0x28, 0x05, 0x00, 0x00, 0x00, 0x00,
	0x04, 0x20, 0x04, 0xa6, 0x00, 0x04, 0x00, 0x00,
	0x81, 0x50, 0x00, 0x00, 0x00, 0x11, 0x84, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06, 0x55,
	0x02, 0x10, 0x08, 0x04, 0x00, 0x00, 0x00, 0x40,
	0x30, 0x83, 0x01, 0x00, 0x00, 0x00, 0x11, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x1e, 0xcd, 0xbf, 0x7a, 0xbf,
	// Entry 200 - 23F
	0xdf, 0xc3, 0x83, 0x82, 0xc0, 0xfb, 0x57, 0x27,
	0xed, 0x55, 0xe7, 0x01, 0x00, 0x20, 0xb2, 0xc5,
	0xa4, 0x45, 0x25, 0x9b, 0x02, 0xdf, 0xe1, 0xdf,
	0x03, 0x44, 0x08, 0x90, 0x01, 0x04, 0x81, 0xe3,
	0x92, 0x54, 0xdb, 0x28, 0xd3, 0x5f, 0xfe, 0x6d,
	0x79, 0xed, 0x1c, 0x7f, 0x04, 0x08, 0x00, 0x01,
	0x21, 0x12, 0x64, 0x5f, 0xdd, 0x0e, 0x85, 0x4f,
	0x40, 0x40, 0x00, 0x04, 0xf1, 0xfd, 0x3d, 0x54,
	// Entry 240 - 27F
	0xe8, 0x03, 0xb4, 0x27, 0x23, 0x0d, 0x00, 0x00,
	0x20, 0x7b, 0x78, 0x02, 0x07, 0x84, 0x00, 0xf0,
	0xbb, 0x7e, 0x5a, 0x00, 0x18, 0x04, 0x81, 0x00,
	0x00, 0x00, 0x80, 0x10, 0x90, 0x1c, 0x01, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x10, 0x40, 0x00, 0x04,
	0x08, 0xa0, 0x70, 0xa5, 0x0c, 0x40, 0x00, 0x00,
	0x91, 0x24, 0x04, 0x68, 0x00, 0x20, 0x70, 0xff,
	0x7b, 0x7f, 0x70, 0x00, 0x05, 0x9b, 0xdd, 0x66,
	// Entry 280 - 2BF
	0x03, 0x00, 0x11, 0x00, 0x00, 0x00, 0x40, 0x05,
	0xb5, 0xb6, 0x80, 0x08, 0x04, 0x00, 0x04, 0x51,
	0xe2, 0xef, 0xfd, 0x3f, 0x05, 0x09, 0x08, 0x05,
	0x40, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00,
	0x0c, 0x00, 0x00, 0x00, 0x00, 0x81, 0x00, 0x60,
	0xe7, 0x48, 0x00, 0x81, 0x20, 0xc0, 0x05, 0x80,
	0x03, 0x00, 0x00, 0x00, 0x8c, 0x50, 0x40, 0x04,
	0x84, 0x47, 0x84, 0x40, 0x20, 0x10, 0x00, 0x20,
	// Entry 2C0 - 2FF
	0x02, 0x50, 0x80, 0x11, 0x00, 0x99, 0x6c, 0xe2,
	0x50, 0x27, 0x1d, 0x11, 0x29, 0x0e, 0x59, 0xe9,
	0x33, 0x08, 0x00, 0x20, 0x04, 0x40, 0x10, 0x00,
	0x00, 0x00, 0x50, 0x44, 0x92, 0x49, 0xd6, 0x5d,
	0xa7, 0x81, 0x47, 0x97, 0xfb, 0x00, 0x10, 0x00,
	0x08, 0x00, 0x80, 0x00, 0x40, 0x04, 0x00, 0x01,
	0x02, 0x00, 0x01, 0x40, 0x80, 0x00, 0x40, 0x08,
	0xd8, 0xeb, 0xf6, 0x39, 0xc4, 0x8d, 0x12, 0x00,
	// Entry 300 - 33F
	0x00, 0x0c, 0x04, 0x01, 0x20, 0x20, 0xdd, 0xa0,
	0x01, 0x00, 0x00, 0x00, 0x12, 0x00, 0x00, 0x00,
	0x04, 0x10, 0xd0, 0x9d, 0x95, 0x13, 0x04, 0x80,
	0x00, 0x01, 0xd0, 0x16, 0x40, 0x00, 0x10, 0xb0,
	0x10, 0x62, 0x4c, 0xd2, 0x02, 0x01, 0x4a, 0x00,
	0x46, 0x04, 0x00, 0x08, 0x02, 0x00, 0x20, 0x80,
	0x00, 0x80, 0x06, 0x00, 0x08, 0x00, 0x00, 0x00,
	0x00, 0xf0, 0xd8, 0x6f, 0x15, 0x02, 0x08, 0x00,
	// Entry 340 - 37F
	0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x10, 0x01,
	0x00, 0x10, 0x00, 0x00, 0x00, 0xf0, 0x84, 0xe3,
	0xdd, 0xbf, 0xf9, 0xf9, 0x3b, 0x7f, 0x7f, 0xdb,
	0xfd, 0xfc, 0xfe, 0xdf, 0xff, 0xfd, 0xff, 0xf6,
	0xfb, 0xfc, 0xf7, 0x1f, 0xff, 0xb3, 0x6c, 0xff,
	0xd9, 0xad, 0xdf, 0xfe, 0xef, 0xba, 0xdf, 0xff,
	0xff, 0xff, 0xb7, 0xdd, 0x7d, 0xbf, 0xab, 0x7f,
	0xfd, 0xfd, 0xdf, 0x2f, 0x9c, 0xdf, 0xf3, 0x6f,
	// Entry 380 - 3BF
	0xdf, 0xdd, 0xff, 0xfb, 0xee, 0xd2, 0xab, 0x5f,
	0xd5, 0xdf, 0x7f, 0xff, 0xeb, 0xff, 0xe4, 0x4d,
	0xf9, 0xff, 0xfe, 0xf7, 0xfd, 0xdf, 0xfb, 0xbf,
	0xee, 0xdb, 0x6f, 0xef, 0xff, 0x7f, 0xff, 0xff,
	0xf7, 0x5f, 0xd3, 0x3b, 0xfd, 0xd9, 0xdf, 0xeb,
	0xbc, 0x08, 0x05, 0x24, 0xff, 0x07, 0x70, 0xfe,
	0xe6, 0x5e, 0x00, 0x08, 0x00, 0x83, 0x7d, 0x1f,
	0x06, 0xe6, 0x72, 0x60, 0xd1, 0x3c, 0x7f, 0x44,
	// Entry 3C0 - 3FF
	0x02, 0x30, 0x9f, 0x7a, 0x16, 0xbd, 0x7f, 0x57,
	0xf2, 0xff, 0x31, 0xff, 0xf2, 0x1e, 0x90, 0xf7,
	0xf1, 0xf9, 0x45, 0x80, 0x01, 0x02, 0x00, 0x20,
	0x40, 0x54, 0x9f, 0x8a, 0xdf, 0xf9, 0x6e, 0x11,
	0x86, 0x51, 0xc0, 0xf3, 0xfb, 0x47, 0x40, 0x03,
	0x05, 0xd1, 0x50, 0x5c, 0x00, 0x40, 0x00, 0x10,
	0x04, 0x02, 0x00, 0x00, 0x0a, 0x00, 0x17, 0xd2,
	0xb9, 0xfd, 0xfc, 0xba, 0xfe, 0xef, 0xc7, 0xbe,
	// Entry 400 - 43F
	0x53, 0x6f, 0xdf, 0xe7, 0xdb, 0x65, 0xbb, 0x7f,
	0xfa, 0xff, 0x77, 0xf3, 0xef, 0xbf, 0xfd, 0xf7,
	0xdf, 0xdf, 0x9b, 0x7f, 0xff, 0xff, 0x7f, 0x6f,
	0xf7, 0xfb, 0xeb, 0xdf, 0xbc, 0xff, 0xbf, 0x6b,
	0x7b, 0xfb, 0xff, 0xce, 0x76, 0xbd, 0xf7, 0xf7,
	0xdf, 0xdc, 0xf7, 0xf7, 0xff, 0xdf, 0xf3, 0xfe,
	0xef, 0xff, 0xff, 0xff, 0xb6, 0x7f, 0x7f, 0xde,
	0xf7, 0xb9, 0xeb, 0x77, 0xff, 0xfb, 0xbf, 0xdf,
	// Entry 440 - 47F
	0xfd, 0xfe, 0xfb, 0xff, 0xfe, 0xeb, 0x1f, 0x7d,
	0x2f, 0xfd, 0xb6, 0xb5, 0xa5, 0xfc, 0xff, 0xfd,
	0x7f, 0x4e, 0xbf, 0x8f, 0xae, 0xff, 0xee, 0xdf,
	0x7f, 0xf7, 0x73, 0x02, 0x02, 0x04, 0xfc, 0xf7,
	0xff, 0xb7, 0xd7, 0xef, 0xfe, 0xcd, 0xf5, 0xce,
	0xe2, 0x8e, 0xe7, 0xbf, 0xb7, 0xff, 0x56, 0xfd,
	0xcd, 0xff, 0xfb, 0xff, 0xdf, 0xd7, 0xea, 0xff,
	0xe5, 0x5f, 0x6d, 0x0f, 0xa7, 0x51, 0x06, 0xc4,
	// Entry 480 - 4BF
	0x93, 0x50, 0x5d, 0xaf, 0xa6, 0xff, 0x99, 0xfb,
	0x63, 0x1d, 0x53, 0xff, 0xef, 0xb7, 0x35, 0x20,
	0x14, 0x00, 0x55, 0x51, 0xc2, 0x65, 0xf5, 0x41,
	0xe2, 0xff, 0xfc, 0xdf, 0x02, 0x85, 0xc5, 0x05,
	0x00, 0x22, 0x00, 0x74, 0x69, 0x10, 0x08, 0x05,
	0x41, 0x00, 0x01, 0x06, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x51, 0x20, 0x05, 0x04, 0x01, 0x00, 0x00,
	0x06, 0x11, 0x20, 0x00, 0x18, 0x01, 0x92, 0xf1,
	// Entry 4C0 - 4FF
	0xfd, 0x47, 0x69, 0x06, 0x95, 0x06, 0x57, 0xed,
	0xfb, 0x4d, 0x1c, 0x6b, 0x83, 0x04, 0x62, 0x40,
	0x00, 0x11, 0x42, 0x00, 0x00, 0x00, 0x54, 0x83,
	0xb8, 0x4f, 0x10, 0x8e, 0x89, 0x46, 0xde, 0xf7,
	0x13, 0x31, 0x00, 0x20, 0x00, 0x00, 0x00, 0x90,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x10, 0x00,
	0x01, 0x00, 0x00, 0xf0, 0x5b, 0xf4, 0xbe, 0x3d,
	0xbe, 0xcf, 0xf7, 0xaf, 0x42, 0x04, 0x84, 0x41,
	// Entry 500 - 53F
	0x30, 0xff, 0x79, 0x72, 0x04, 0x00, 0x00, 0x49,
	0x2d, 0x14, 0x27, 0x5f, 0xed, 0xf1, 0x3f, 0xe7,
	0x3f, 0x00, 0x00, 0x02, 0xc6, 0xa0, 0x1e, 0xf8,
	0xbb, 0xff, 0xfd, 0xfb, 0xb7, 0xfd, 0xe7, 0xf7,
	0xfd, 0xfc, 0xd5, 0xed, 0x47, 0xf4, 0x7e, 0x10,
	0x01, 0x01, 0x84, 0x6d, 0xff, 0xf7, 0xdd, 0xf9,
	0x5b, 0x05, 0x86, 0xed, 0xf5, 0x77, 0xbd, 0x3c,
	0x00, 0x00, 0x00, 0x42, 0x71, 0x42, 0x00, 0x40,
	// Entry 540 - 57F
	0x00, 0x00, 0x01, 0x43, 0x19, 0x24, 0x08, 0x00,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	// Entry 580 - 5BF
	0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
	0xff, 0xab, 0xbd, 0xe7, 0x57, 0xee, 0x13, 0x5d,
	0x09, 0xc1, 0x40, 0x21, 0xfa, 0x17, 0x01, 0x80,
	0x00, 0x00, 0x00, 0x00, 0xf0, 0xce, 0xfb, 0xbf,
	0x00, 0x23, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00,
	0x00, 0x30, 0x15, 0xa3, 0x10, 0x00, 0x00, 0x00,
	0x11, 0x04, 0x16, 0x00, 0x00, 0x02, 0x20, 0x81,
	0xa3, 0x01, 0x50, 0x00, 0x00, 0x83, 0x11, 0x40,
	// Entry 5C0 - 5FF
	0x00, 0x00, 0x00, 0xf0, 0xdd, 0x7b, 0xbe, 0x02,
	0xaa, 0x10, 0x5d, 0x98, 0x52, 0x00, 0x80, 0x20,
	0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x02, 0x02,
	0x3d, 0x40, 0x10, 0x02, 0x10, 0x61, 0x5a, 0x9d,
	0x31, 0x00, 0x00, 0x00, 0x01, 0x18, 0x02, 0x20,
	0x00, 0x00, 0x01, 0x00, 0x42, 0x00, 0x20, 0x00,
	0x00, 0x1f, 0xdf, 0xd2, 0xb9, 0xff, 0xfd, 0x3f,
	0x1f, 0x98, 0xcf, 0x9c, 0xff, 0xaf, 0x5f, 0xfe,
	// Entry 600 - 63F
	0x7b, 0x4b, 0x40, 0x10, 0xe1, 0xfd, 0xaf, 0xd9,
	0xb7, 0xf6, 0xfb, 0xb3, 0xc7, 0xff, 0x6f, 0xf1,
	0x73, 0xb1, 0x7f, 0x9f, 0x7f, 0xbd, 0xfc, 0xb7,
	0xee, 0x1c, 0xfa, 0xcb, 0xef, 0xdd, 0xf9, 0xbd,
	0x6e, 0xae, 0x55, 0xfd, 0x6e, 0x81, 0x76, 0x9f,
	0xd4, 0x77, 0xf5, 0x7d, 0xfb, 0xff, 0xeb, 0xfe,
	0xbe, 0x5f, 0x46, 0x5b, 0xe9, 0x5f, 0x50, 0x18,
	0x02, 0xfa, 0xf7, 0x9d, 0x15, 0x97, 0x05, 0x0f,
	// Entry 640 - 67F
	0x75, 0xc4, 0x7d, 0x81, 0x92, 0xf5, 0x57, 0x6c,
	0xff, 0xe4, 0xef, 0x6f, 0xff, 0xfc, 0xdd, 0xde,
	0xfc, 0xfd, 0x76, 0x5f, 0x7a, 0x3f, 0x00, 0x98,
	0x02, 0xfb, 0xa3, 0xef, 0xf3, 0xd6, 0xf2, 0xff,
	0xb9, 0xda, 0x7d, 0xd0, 0x3e, 0x15, 0x7b, 0xb4,
	0xf5, 0x3e, 0xff, 0xff, 0xf1, 0xf7, 0xff, 0xe7,
	0x5f, 0xff, 0xff, 0x9e, 0xdf, 0xf6, 0xd7, 0xb9,
	0xef, 0x27, 0x80, 0xbb, 0xc5, 0xff, 0xff, 0xe3,
	// Entry 680 - 6BF
	0x97, 0x9d, 0xbf, 0x9f, 0xf7, 0xc7, 0xfd, 0x37,
	0xce, 0x7f, 0x44, 0x1d, 0x73, 0x7f, 0xf8, 0xda,
	0x5d, 0xce, 0x7d, 0x06, 0xb9, 0xea, 0x79, 0xa0,
	0x1a, 0x20, 0x00, 0x30, 0x02, 0x04, 0x24, 0x08,
	0x04, 0x00, 0x00, 0x40, 0xd4, 0x02, 0x04, 0x00,
	0x00, 0x04, 0x00, 0x04, 0x00, 0x20, 0x09, 0x06,
	0x50, 0x00, 0x08, 0x00, 0x00, 0x00, 0x24, 0x00,
	0x04, 0x00, 0x10, 0xdc, 0x58, 0xd7, 0x0d, 0x0f,
	// Entry 6C0 - 6FF
	0x54, 0x4d, 0xf1, 0x16, 0x44, 0xd5, 0x42, 0x08,
	0x40, 0x02, 0x00, 0x40, 0x00, 0x08, 0x00, 0x00,
	0x00, 0xdc, 0xfb, 0xcb, 0x0e, 0x58, 0x48, 0x41,
	0x24, 0x20, 0x04, 0x00, 0x30, 0x12, 0x40, 0x00,
	0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x01, 0x00, 0x00, 0x00, 0x80, 0x10, 0x10, 0xab,
	0x6d, 0x93, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x80, 0x80, 0x25, 0x00, 0x00,
	// Entry 700 - 73F
	0x00, 0x00, 0x00, 0x00, 0x0a, 0x00, 0x00, 0x00,
	0x80, 0x86, 0xc2, 0x00, 0x00, 0x01, 0x00, 0x01,
	0xff, 0x18, 0x02, 0x00, 0x02, 0xf0, 0xfd, 0x79,
	0x3b, 0x00, 0x25, 0x00, 0x00, 0x00, 0x02, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x00,
	0x03, 0x00, 0x09, 0x20, 0x00, 0x00, 0x01, 0x00,
	0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 740 - 77F
	0x00, 0x00, 0x00, 0xef, 0xd5, 0xfd, 0xcf, 0x7e,
	0xb0, 0x11, 0x00, 0x00, 0x00, 0x92, 0x01, 0x46,
	0xcd, 0xf9, 0x5c, 0x00, 0x01, 0x00, 0x30, 0x04,
	0x04, 0x55, 0x00, 0x01, 0x04, 0xf4, 0x3f, 0x4a,
	0x01, 0x00, 0x00, 0xb0, 0x80, 0x20, 0x55, 0x75,
	0x97, 0x7c, 0xdf, 0x31, 0xcc, 0x68, 0xd1, 0x03,
	0xd5, 0x57, 0x27, 0x14, 0x01, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x2c, 0xf7, 0xcb, 0x1f, 0x14, 0x60,
	// Entry 780 - 7BF
	0x83, 0x68, 0x01, 0x10, 0x8b, 0x38, 0x8a, 0x01,
	0x00, 0x00, 0x20, 0x00, 0x24, 0x44, 0x00, 0x00,
	0x10, 0x03, 0x31, 0x02, 0x01, 0x00, 0x00, 0xf0,
	0xf5, 0xff, 0xd5, 0x97, 0xbc, 0x70, 0xd6, 0x78,
	0x78, 0x15, 0x50, 0x05, 0xa4, 0x84, 0xa9, 0x41,
	0x00, 0x00, 0x00, 0x6b, 0x39, 0x52, 0x74, 0x40,
	0xe8, 0x30, 0x90, 0x6a, 0x92, 0x00, 0x00, 0x02,
	0xff, 0xef, 0xff, 0x4b, 0x85, 0x53, 0xf4, 0xed,
	// Entry 7C0 - 7FF
	0xdd, 0xbf, 0xf2, 0x5d, 0xc7, 0x0c, 0xd5, 0x42,
	0xfc, 0xff, 0xf7, 0x1f, 0x00, 0x80, 0x40, 0x56,
	0xcc, 0x16, 0x9e, 0xea, 0x35, 0x7d, 0xef, 0xff,
	0xbd, 0xa4, 0xaf, 0x01, 0x44, 0x18, 0x01, 0x4d,
	0x4e, 0x4a, 0x08, 0x50, 0x28, 0x30, 0xe0, 0x80,
	0x10, 0x20, 0x24, 0x00, 0xff, 0x2f, 0xd3, 0x60,
	0xfe, 0x01, 0x02, 0x88, 0x2a, 0x40, 0x16, 0x01,
	0x01, 0x15, 0x2b, 0x3c, 0x01, 0x00, 0x00, 0x10,
	// Entry 800 - 83F
	0x90, 0x49, 0x41, 0x02, 0x02, 0x01, 0xe1, 0xbf,
	0xbf, 0x03, 0x00, 0x00, 0x10, 0xdc, 0xa3, 0xd1,
	0x40, 0x9c, 0x44, 0xdf, 0xf5, 0x8f, 0x66, 0xb3,
	0x55, 0x20, 0xd4, 0xc1, 0xd8, 0x30, 0x3d, 0x80,
	0x00, 0x00, 0x00, 0x04, 0xd4, 0x11, 0xc5, 0x84,
	0x2f, 0x50, 0x00, 0x22, 0x50, 0x6e, 0xbd, 0x93,
	0x07, 0x00, 0x20, 0x10, 0x84, 0xb2, 0x45, 0x10,
	0x06, 0x44, 0x00, 0x00, 0x12, 0x02, 0x11, 0x00,
	// Entry 840 - 87F
	0xf0, 0xfb, 0xfd, 0x7f, 0x05, 0x00, 0x16, 0x89,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0c, 0x03,
	0x00, 0x00, 0x00, 0x00, 0x03, 0x30, 0x02, 0x28,
	0x84, 0x00, 0x21, 0xc0, 0x23, 0x24, 0x00, 0x00,
	0x00, 0xcb, 0xe4, 0x3a, 0x46, 0x88, 0x54, 0xf1,
	0xef, 0xff, 0x7f, 0x12, 0x01, 0x01, 0x84, 0x50,
	0x07, 0xfc, 0xff, 0xff, 0x0f, 0x01, 0x00, 0x40,
	0x10, 0x38, 0x01, 0x01, 0x1c, 0x12, 0x40, 0xe1,
	// Entry 880 - 8BF
	0x76, 0x16, 0x08, 0x03, 0x10, 0x00, 0x00, 0x00,
	0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x24,
	0x0a, 0x00, 0x80, 0x00, 0x00,
}

// altLangISO3 holds an alphabetically sorted list of 3-letter language code alternatives
// to 2-letter language codes that cannot be derived using the method described above.
// Each 3-letter code is followed by its 1-byte langID.
const altLangISO3 tag.Index = "---\x00cor\x00hbs\x01heb\x02kin\x03spa\x04yid\x05\xff\xff\xff\xff"

// altLangIndex is used to convert indexes in altLangISO3 to langIDs.
// Size: 12 bytes, 6 elements
var altLangIndex = [6]uint16{
	0x0281, 0x0407, 0x01fb, 0x03e5, 0x013e, 0x0208,
}

// AliasMap maps langIDs to their suggested replacements.
// Size: 772 bytes, 193 elements
var AliasMap = [193]FromTo{
	0:   {From: 0x82, To: 0x88},
	1:   {From: 0x187, To: 0x1ae},
	2:   {From: 0x1f3, To: 0x1e1},
	3:   {From: 0x1fb, To: 0x1bc},
	4:   {From: 0x208, To: 0x512},
	5:   {From: 0x20f, To: 0x20e},
	6:   {From: 0x310, To: 0x3dc},
	7:   {From: 0x347, To: 0x36f},
	8:   {From: 0x407, To: 0x432},
	9:   {From: 0x47a, To: 0x153},
	10:  {From: 0x490, To: 0x451},
	11:  {From: 0x4a2, To: 0x21},
	12:  {From: 0x53e, To: 0x544},
	13:  {From: 0x58f, To: 0x12d},
	14:  {From: 0x62b, To: 0x34},
	15:  {From: 0x62f, To: 0x14},
	16:  {From: 0x630, To: 0x1eb1},
	17:  {From: 0x651, To: 0x431},
	18:  {From: 0x662, To: 0x431},
	19:  {From: 0x6ed, To: 0x3a},
	20:  {From: 0x6f8, To: 0x1d7},
	21:  {From: 0x709, To: 0x3625},
	22:  {From: 0x73e, To: 0x21a1},
	23:  {From: 0x7b3, To: 0x56},
	24:  {From: 0x7b9, To: 0x299b},
	25:  {From: 0x7c5, To: 0x58},
	26:  {From: 0x7e6, To: 0x145},
	27:  {From: 0x80c, To: 0x5a},
	28:  {From: 0x815, To: 0x8d},
	29:  {From: 0x87e, To: 0x810},
	30:  {From: 0x8a8, To: 0x8b7},
	31:  {From: 0x8c3, To: 0xee3},
	32:  {From: 0x8fa, To: 0x1dc},
	33:  {From: 0x9ef, To: 0x331},
	34:  {From: 0xa36, To: 0x2c5},
	35:  {From: 0xa3d, To: 0xbf},
	36:  {From: 0xabe, To: 0x3322},
	37:  {From: 0xb38, To: 0x529},
	38:  {From: 0xb75, To: 0x265a},
	39:  {From: 0xb7e, To: 0xbc3},
	40:  {From: 0xb9b, To: 0x44e},
	41:  {From: 0xbbc, To: 0x4229},
	42:  {From: 0xbbf, To: 0x529},
	43:  {From: 0xbfe, To: 0x2da7},
	44:  {From: 0xc2e, To: 0x3181},
	45:  {From: 0xcb9, To: 0xf3},
	46:  {From: 0xd08, To: 0xfa},
	47:  {From: 0xdc8, To: 0x11a},
	48:  {From: 0xdd7, To: 0x32d},
	49:  {From: 0xdf8, To: 0xdfb},
	50:  {From: 0xdfe, To: 0x531},
	51:  {From: 0xe01, To: 0xdf3},
	52:  {From: 0xedf, To: 0x205a},
	53:  {From: 0xee9, To: 0x222e},
	54:  {From: 0xeee, To: 0x2e9a},
	55:  {From: 0xf39, To: 0x367},
	56:  {From: 0x10d0, To: 0x140},
	57:  {From: 0x1104, To: 0x2d0},
	58:  {From: 0x11a0, To: 0x1ec},
	59:  {From: 0x1279, To: 0x21},
	60:  {From: 0x1424, To: 0x15e},
	61:  {From: 0x1470, To: 0x14e},
	62:  {From: 0x151f, To: 0xd9b},
	63:  {From: 0x1523, To: 0x390},
	64:  {From: 0x1532, To: 0x19f},
	65:  {From: 0x1580, To: 0x210},
	66:  {From: 0x1583, To: 0x10d},
	67:  {From: 0x15a3, To: 0x3caf},
	68:  {From: 0x1630, To: 0x222e},
	69:  {From: 0x166a, To: 0x19b},
	70:  {From: 0x16c8, To: 0x136},
	71:  {From: 0x1700, To: 0x29f8},
	72:  {From: 0x1718, To: 0x194},
	73:  {From: 0x1727, To: 0xf3f},
	74:  {From: 0x177a, To: 0x178},
	75:  {From: 0x1809, To: 0x17b6},
	76:  {From: 0x1816, To: 0x18f3},
	77:  {From: 0x188a, To: 0x436},
	78:  {From: 0x1979, To: 0x1d01},
	79:  {From: 0x1a74, To: 0x2bb0},
	80:  {From: 0x1a8a, To: 0x1f8},
	81:  {From: 0x1b5a, To: 0x1fa},
	82:  {From: 0x1b86, To: 0x1515},
	83:  {From: 0x1d64, To: 0x2c9b},
	84:  {From: 0x2038, To: 0x37b1},
	85:  {From: 0x203d, To: 0x20dd},
	86:  {From: 0x2042, To: 0x2e00},
	87:  {From: 0x205a, To: 0x30b},
	88:  {From: 0x20e3, To: 0x274},
	89:  {From: 0x20ee, To: 0x263},
	90:  {From: 0x20f2, To: 0x22d},
	91:  {From: 0x20f9, To: 0x256},
	92:  {From: 0x210f, To: 0x21eb},
	93:  {From: 0x2135, To: 0x27d},
	94:  {From: 0x2160, To: 0x913},
	95:  {From: 0x2199, To: 0x121},
	96:  {From: 0x21ce, To: 0x1561},
	97:  {From: 0x21e6, To: 0x504},
	98:  {From: 0x21f4, To: 0x49f},
	99:  {From: 0x21fb, To: 0x269},
	100: {From: 0x222d, To: 0x121},
	101: {From: 0x2237, To: 0x121},
	102: {From: 0x2248, To: 0x217d},
	103: {From: 0x2262, To: 0x92a},
	104: {From: 0x2316, To: 0x3226},
	105: {From: 0x236a, To: 0x2835},
	106: {From: 0x2382, To: 0x3365},
	107: {From: 0x2472, To: 0x2c7},
	108: {From: 0x24e4, To: 0x2ff},
	109: {From: 0x24f0, To: 0x2fa},
	110: {From: 0x24fa, To: 0x31f},
	111: {From: 0x2550, To: 0xb5b},
	112: {From: 0x25a9, To: 0xe2},
	113: {From: 0x263e, To: 0x2d0},
	114: {From: 0x26c9, To: 0x26b4},
	115: {From: 0x26f9, To: 0x3c8},
	116: {From: 0x2727, To: 0x3caf},
	117: {From: 0x2755, To: 0x6a4},
	118: {From: 0x2765, To: 0x26b4},
	119: {From: 0x2789, To: 0x4358},
	120: {From: 0x27c9, To: 0x2001},
	121: {From: 0x28ea, To: 0x27b1},
	122: {From: 0x28ef, To: 0x2837},
	123: {From: 0x28fe, To: 0xaa5},
	124: {From: 0x2914, To: 0x351},
	125: {From: 0x2986, To: 0x2da7},
	126: {From: 0x29f0, To: 0x96b},
	127: {From: 0x2b1a, To: 0x38d},
	128: {From: 0x2bfc, To: 0x395},
	129: {From: 0x2c3f, To: 0x3caf},
	130: {From: 0x2ce1, To: 0x2201},
	131: {From: 0x2cfc, To: 0x3be},
	132: {From: 0x2d13, To: 0x597},
	133: {From: 0x2d47, To: 0x148},
	134: {From: 0x2d48, To: 0x148},
	135: {From: 0x2dff, To: 0x2f1},
	136: {From: 0x2e08, To: 0x19cc},
	137: {From: 0x2e10, To: 0xc45},
	138: {From: 0x2e1a, To: 0x2d95},
	139: {From: 0x2e21, To: 0x292},
	140: {From: 0x2e54, To: 0x7d},
	141: {From: 0x2e65, To: 0x2282},
	142: {From: 0x2e97, To: 0x1a4},
	143: {From: 0x2ea0, To: 0x2e9b},
	144: {From: 0x2eef, To: 0x2ed7},
	145: {From: 0x3193, To: 0x3c4},
	146: {From: 0x3366, To: 0x338e},
	147: {From: 0x342a, To: 0x3dc},
	148: {From: 0x34ee, To: 0x18d0},
	149: {From: 0x35c8, To: 0x2c9b},
	150: {From: 0x35e6, To: 0x412},
	151: {From: 0x35f5, To: 0x24b},
	152: {From: 0x360d, To: 0x1dc},
	153: {From: 0x3658, To: 0x246},
	154: {From: 0x3676, To: 0x3f4},
	155: {From: 0x36fd, To: 0x445},
	156: {From: 0x3747, To: 0x3b42},
	157: {From: 0x37c0, To: 0x121},
	158: {From: 0x3816, To: 0x38f2},
	159: {From: 0x382a, To: 0x2b48},
	160: {From: 0x382b, To: 0x2c9b},
	161: {From: 0x382f, To: 0xa9},
	162: {From: 0x3832, To: 0x3228},
	163: {From: 0x386c, To: 0x39a6},
	164: {From: 0x3892, To: 0x3fc0},
	165: {From: 0x38a0, To: 0x45f},
	166: {From: 0x38a5, To: 0x39d7},
	167: {From: 0x38b4, To: 0x1fa4},
	168: {From: 0x38b5, To: 0x2e9a},
	169: {From: 0x38fa, To: 0x38f1},
	170: {From: 0x395c, To: 0x47e},
	171: {From: 0x3b4e, To: 0xd91},
	172: {From: 0x3b78, To: 0x137},
	173: {From: 0x3c99, To: 0x4bc},
	174: {From: 0x3fbd, To: 0x100},
	175: {From: 0x4208, To: 0xa91},
	176: {From: 0x42be, To: 0x573},
	177: {From: 0x42f9, To: 0x3f60},
	178: {From: 0x4378, To: 0x25a},
	179: {From: 0x43b8, To: 0xe6c},
	180: {From: 0x43cd, To: 0x10f},
	181: {From: 0x43d4, To: 0x4848},
	182: {From: 0x44af, To: 0x3322},
	183: {From: 0x44e3, To: 0x512},
	184: {From: 0x45ca, To: 0x2409},
	185: {From: 0x45dd, To: 0x26dc},
	186: {From: 0x4610, To: 0x48ae},
	187: {From: 0x46ae, To: 0x46a0},
	188: {From: 0x473e, To: 0x4745},
	189: {From: 0x4817, To: 0x3503},
	190: {From: 0x483b, To: 0x208b},
	191: {From: 0x4916, To: 0x31f},
	192: {From: 0x49a7, To: 0x523},
}

// Size: 193 bytes, 193 elements
var AliasTypes = [193]AliasType{
	// Entry 0 - 3F
	1, 0, 0, 0, 0, 0, 0, 1, 2, 2, 0, 1, 0, 0, 0, 0,
	1, 2, 1, 1, 2, 0, 0, 1, 0, 1, 2, 1, 1, 0, 0, 0,
	0, 2, 1, 1, 0, 2, 0, 0, 1, 0, 1, 0, 0, 1, 2, 1,
	1, 1, 1, 0, 0, 0, 0, 2, 1, 1, 1, 1, 2, 1, 0, 1,
	// Entry 40 - 7F
	1, 2, 2, 0, 0, 1, 2, 0, 1, 0, 1, 1, 1, 1, 0, 0,
	2, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 0, 1, 0, 0,
	0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 2, 2, 2, 0,
	1, 1, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1,
	// Entry 80 - BF
	1, 0, 0, 1, 0, 2, 1, 1, 0, 0, 0, 1, 0, 0, 0, 0,
	0, 1, 1, 2, 0, 0, 2, 0, 0, 1, 1, 1, 0, 0, 0, 0,
	0, 2, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 0, 1, 2, 0,
	0, 0, 1, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1,
	// Entry C0 - FF
	1,
}

const (
	_Latn = 91
	_Hani = 57
	_Hans = 59
	_Hant = 60
	_Qaaa = 149
	_Qaai = 157
	_Qabx = 198
	_Zinh = 255
	_Zyyy = 260
	_Zzzz = 261
)

// script is an alphabetically sorted list of ISO 15924 codes. The index
// of the script in the string, divided by 4, is the internal scriptID.
const script tag.Index = "" + // Size: 1052 bytes
	"----AdlmAfakAghbAhomArabAranArmiArmnAvstBaliBamuBassBatkBengBhksBlisBopo" +
	"BrahBraiBugiBuhdCakmCansCariChamCherChrsCirtCoptCpmnCprtCyrlCyrsDevaDiak" +
	"DogrDsrtDuplEgydEgyhEgypElbaElymEthiGeokGeorGlagGongGonmGothGranGrekGujr" +
	"GuruHanbHangHaniHanoHansHantHatrHebrHiraHluwHmngHmnpHrktHungIndsItalJamo" +
	"JavaJpanJurcKaliKanaKawiKharKhmrKhojKitlKitsKndaKoreKpelKthiLanaLaooLatf" +
	"LatgLatnLekeLepcLimbLinaLinbLisuLomaLyciLydiMahjMakaMandManiMarcMayaMedf" +
	"MendMercMeroMlymModiMongMoonMrooMteiMultMymrNagmNandNarbNbatNewaNkdbNkgb" +
	"NkooNshuOgamOlckOrkhOryaOsgeOsmaOugrPalmPaucPcunPelmPermPhagPhliPhlpPhlv" +
	"PhnxPiqdPlrdPrtiPsinQaaaQaabQaacQaadQaaeQaafQaagQaahQaaiQaajQaakQaalQaam" +
	"QaanQaaoQaapQaaqQaarQaasQaatQaauQaavQaawQaaxQaayQaazQabaQabbQabcQabdQabe" +
	"QabfQabgQabhQabiQabjQabkQablQabmQabnQaboQabpQabqQabrQabsQabtQabuQabvQabw" +
	"QabxRanjRjngRohgRoroRunrSamrSaraSarbSaurSgnwShawShrdShuiSiddSindSinhSogd" +
	"SogoSoraSoyoSundSunuSyloSyrcSyreSyrjSyrnTagbTakrTaleTaluTamlTangTavtTelu" +
	"TengTfngTglgThaaThaiTibtTirhTnsaTotoUgarVaiiVispVithWaraWchoWoleXpeoXsux" +
	"YeziYiiiZanbZinhZmthZsyeZsymZxxxZyyyZzzz\xff\xff\xff\xff"

// suppressScript is an index from langID to the dominant script for that language,
// if it exists.  If a script is given, it should be suppressed from the language tag.
// Size: 1330 bytes, 1330 elements
var suppressScript = [1330]uint8{
	// Entry 0 - 3F
	0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x2c,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x05, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 40 - 7F
	0x00, 0x00, 0x00, 0x0e, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00,
	// Entry 80 - BF
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x0e, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry C0 - FF
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 100 - 13F
	0x5b, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xed, 0x00, 0x00, 0x00, 0x00, 0xef, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, 0x00,
	0x00, 0x5b, 0x00, 0x00, 0x5b, 0x00, 0x5b, 0x00,
	// Entry 140 - 17F
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00,
	0x05, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x5b, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00,
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00,
	0x00, 0x5b, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x5b, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 180 - 1BF
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x5b, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x5b, 0x35, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x3e, 0x00, 0x22, 0x00,
	// Entry 1C0 - 1FF
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x5b, 0x5b, 0x00, 0x5b, 0x5b, 0x00, 0x08,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00,
	0x5b, 0x5b, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00,
	// Entry 200 - 23F
	0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 240 - 27F
	0x00, 0x00, 0x20, 0x00, 0x00, 0x5b, 0x00, 0x00,
	0x00, 0x00, 0x4f, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x53, 0x00, 0x00, 0x54, 0x00, 0x22, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 280 - 2BF
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00,
	0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 2C0 - 2FF
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20,
	// Entry 300 - 33F
	0x00, 0x00, 0x00, 0x00, 0x6f, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x22, 0x00, 0x00, 0x00, 0x5b,
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x76, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00,
	// Entry 340 - 37F
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x00,
	0x5b, 0x22, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x7e, 0x5b, 0x00,
	0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 380 - 3BF
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x83, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00,
	// Entry 3C0 - 3FF
	0x5b, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00,
	0x00, 0x5b, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x20, 0x00, 0x00, 0x5b, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 400 - 43F
	0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0xd6, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x5b, 0x00,
	0x00, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00,
	// Entry 440 - 47F
	0x00, 0x00, 0x00, 0x00, 0x5b, 0x5b, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0xe6, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0xe9, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0xee, 0x00, 0x00, 0x00, 0x2c,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x5b, 0x00,
	// Entry 480 - 4BF
	0x5b, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x5b, 0x00,
	0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x5b, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 4C0 - 4FF
	0x5b, 0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	// Entry 500 - 53F
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x3e, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b,
	0x00, 0x00,
}

const (
	_001 = 1
	_419 = 31
	_BR  = 65
	_CA  = 73
	_ES  = 111
	_GB  = 124
	_MD  = 189
	_PT  = 239
	_UK  = 307
	_US  = 310
	_ZZ  = 358
	_XA  = 324
	_XC  = 326
	_XK  = 334
)

// isoRegionOffset needs to be added to the index of regionISO to obtain the regionID
// for 2-letter ISO codes. (The first isoRegionOffset regionIDs are reserved for
// the UN.M49 codes used for groups.)
const isoRegionOffset = 32

// regionTypes defines the status of a region for various standards.
// Size: 359 bytes, 359 elements
var regionTypes = [359]uint8{
	// Entry 0 - 3F
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
	0x05, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	// Entry 40 - 7F
	0x06, 0x06, 0x06, 0x06, 0x04, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x04, 0x04, 0x06,
	0x04, 0x00, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x04, 0x06, 0x04, 0x06, 0x06, 0x06, 0x06, 0x00,
	0x06, 0x04, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x04, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x00, 0x06, 0x04, 0x06, 0x06, 0x06, 0x06, 0x06,
	// Entry 80 - BF
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x00, 0x04, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x00, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	// Entry C0 - FF
	0x06, 0x06, 0x00, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x00, 0x06, 0x06, 0x06, 0x06, 0x00, 0x06, 0x04,
	0x06, 0x06, 0x06, 0x06, 0x00, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x00, 0x06, 0x06, 0x00, 0x06, 0x05, 0x05, 0x05,
	0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
	// Entry 100 - 13F
	0x05, 0x05, 0x05, 0x06, 0x00, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x04,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x02, 0x06, 0x04, 0x06, 0x06,
	0x06, 0x06, 0x06, 0x00, 0x06, 0x06, 0x06, 0x06,
	// Entry 140 - 17F
	0x06, 0x06, 0x00, 0x06, 0x05, 0x05, 0x05, 0x05,
	0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
	0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x05,
	0x05, 0x05, 0x05, 0x05, 0x05, 0x05, 0x04, 0x06,
	0x06, 0x04, 0x06, 0x06, 0x04, 0x06, 0x05,
}

// regionISO holds a list of alphabetically sorted 2-letter ISO region codes.
// Each 2-letter codes is followed by two bytes with the following meaning:
//   - [A-Z}{2}: the first letter of the 2-letter code plus these two
//     letters form the 3-letter ISO code.
//   - 0, n:     index into altRegionISO3.
const regionISO tag.Index = "" + // Size: 1312 bytes
	"AAAAACSCADNDAEREAFFGAGTGAIIAALLBAMRMANNTAOGOAQTAARRGASSMATUTAUUSAWBWAXLA" +
	"AZZEBAIHBBRBBDGDBEELBFFABGGRBHHRBIDIBJENBLLMBMMUBNRNBOOLBQESBRRABSHSBTTN" +
	"BUURBVVTBWWABYLRBZLZCAANCCCKCDODCFAFCGOGCHHECIIVCKOKCLHLCMMRCNHNCOOLCPPT" +
	"CQ  CRRICS\x00\x00CTTECUUBCVPVCWUWCXXRCYYPCZZEDDDRDEEUDGGADJJIDKNKDMMADO" +
	"OMDYHYDZZAEA  ECCUEESTEGGYEHSHERRIESSPETTHEU\x00\x03EZ  FIINFJJIFKLKFMSM" +
	"FOROFQ\x00\x18FRRAFXXXGAABGBBRGDRDGEEOGFUFGGGYGHHAGIIBGLRLGMMBGNINGPLPGQ" +
	"NQGRRCGS\x00\x06GTTMGUUMGWNBGYUYHKKGHMMDHNNDHRRVHTTIHUUNHVVOIC  IDDNIERL" +
	"ILSRIMMNINNDIOOTIQRQIRRNISSLITTAJEEYJMAMJOORJPPNJTTNKEENKGGZKHHMKIIRKM" +
	"\x00\x09KNNAKP\x00\x0cKRORKWWTKY\x00\x0fKZAZLAAOLBBNLCCALIIELKKALRBRLSSO" +
	"LTTULUUXLVVALYBYMAARMCCOMDDAMENEMFAFMGDGMHHLMIIDMKKDMLLIMMMRMNNGMOACMPNP" +
	"MQTQMRRTMSSRMTLTMUUSMVDVMWWIMXEXMYYSMZOZNAAMNCCLNEERNFFKNGGANHHBNIICNLLD" +
	"NOORNPPLNQ\x00\x1eNRRUNTTZNUIUNZZLOMMNPAANPCCIPEERPFYFPGNGPHHLPKAKPLOLPM" +
	"\x00\x12PNCNPRRIPSSEPTRTPUUSPWLWPYRYPZCZQAATQMMMQNNNQOOOQPPPQQQQQRRRQSSS" +
	"QTTTQU\x00\x03QVVVQWWWQXXXQYYYQZZZREEURHHOROOURS\x00\x15RUUSRWWASAAUSBLB" +
	"SCYCSDDNSEWESGGPSHHNSIVNSJJMSKVKSLLESMMRSNENSOOMSRURSSSDSTTPSUUNSVLVSXXM" +
	"SYYRSZWZTAAATCCATDCDTF\x00\x18TGGOTHHATJJKTKKLTLLSTMKMTNUNTOONTPMPTRURTT" +
	"TOTVUVTWWNTZZAUAKRUGGAUK  UMMIUN  USSAUYRYUZZBVAATVCCTVDDRVEENVGGBVIIRVN" +
	"NMVUUTWFLFWKAKWSSMXAAAXBBBXCCCXDDDXEEEXFFFXGGGXHHHXIIIXJJJXKKKXLLLXMMMXN" +
	"NNXOOOXPPPXQQQXRRRXSSSXTTTXUUUXVVVXWWWXXXXXYYYXZZZYDMDYEEMYT\x00\x1bYUUG" +
	"ZAAFZMMBZRARZWWEZZZZ\xff\xff\xff\xff"

// altRegionISO3 holds a list of 3-letter region codes that cannot be
// mapped to 2-letter codes using the default algorithm. This is a short list.
const altRegionISO3 string = "SCGQUUSGSCOMPRKCYMSPMSRBATFMYTATN"

// altRegionIDs holds a list of regionIDs the positions of which match those
// of the 3-letter ISO codes in altRegionISO3.
// Size: 22 bytes, 11 elements
var altRegionIDs = [11]uint16{
	0x0058, 0x0071, 0x0089, 0x00a9, 0x00ab, 0x00ae, 0x00eb, 0x0106,
	0x0122, 0x0160, 0x00dd,
}

// Size: 80 bytes, 20 elements
var regionOldMap = [20]FromTo{
	0:  {From: 0x44, To: 0xc5},
	1:  {From: 0x59, To: 0xa8},
	2:  {From: 0x60, To: 0x61},
	3:  {From: 0x67, To: 0x3b},
	4:  {From: 0x7a, To: 0x79},
	5:  {From: 0x94, To: 0x37},
	6:  {From: 0xa4, To: 0x134},
	7:  {From: 0xc2, To: 0x134},
	8:  {From: 0xd8, To: 0x140},
	9:  {From: 0xdd, To: 0x2b},
	10: {From: 0xf0, To: 0x134},
	11: {From: 0xf3, To: 0xe3},
	12: {From: 0xfd, To: 0x71},
	13: {From: 0x104, To: 0x165},
	14: {From: 0x12b, To: 0x127},
	15: {From: 0x133, To: 0x7c},
	16: {From: 0x13b, To: 0x13f},
	17: {From: 0x142, To: 0x134},
	18: {From: 0x15e, To: 0x15f},
	19: {From: 0x164, To: 0x4b},
}

// m49 maps regionIDs to UN.M49 codes. The first isoRegionOffset entries are
// codes indicating collections of regions.
// Size: 718 bytes, 359 elements
var m49 = [359]int16{
	// Entry 0 - 3F
	0, 1, 2, 3, 5, 9, 11, 13,
	14, 15, 17, 18, 19, 21, 29, 30,
	34, 35, 39, 53, 54, 57, 61, 142,
	143, 145, 150, 151, 154, 155, 202, 419,
	958, 0, 20, 784, 4, 28, 660, 8,
	51, 530, 24, 10, 32, 16, 40, 36,
	533, 248, 31, 70, 52, 50, 56, 854,
	100, 48, 108, 204, 652, 60, 96, 68,
	// Entry 40 - 7F
	535, 76, 44, 64, 104, 74, 72, 112,
	84, 124, 166, 180, 140, 178, 756, 384,
	184, 152, 120, 156, 170, 0, 0, 188,
	891, 296, 192, 132, 531, 162, 196, 203,
	278, 276, 0, 262, 208, 212, 214, 204,
	12, 0, 218, 233, 818, 732, 232, 724,
	231, 967, 0, 246, 242, 238, 583, 234,
	0, 250, 249, 266, 826, 308, 268, 254,
	// Entry 80 - BF
	831, 288, 292, 304, 270, 324, 312, 226,
	300, 239, 320, 316, 624, 328, 344, 334,
	340, 191, 332, 348, 854, 0, 360, 372,
	376, 833, 356, 86, 368, 364, 352, 380,
	832, 388, 400, 392, 581, 404, 417, 116,
	296, 174, 659, 408, 410, 414, 136, 398,
	418, 422, 662, 438, 144, 430, 426, 440,
	442, 428, 434, 504, 492, 498, 499, 663,
	// Entry C0 - FF
	450, 584, 581, 807, 466, 104, 496, 446,
	580, 474, 478, 500, 470, 480, 462, 454,
	484, 458, 508, 516, 540, 562, 574, 566,
	548, 558, 528, 578, 524, 10, 520, 536,
	570, 554, 512, 591, 0, 604, 258, 598,
	608, 586, 616, 666, 612, 630, 275, 620,
	581, 585, 600, 591, 634, 959, 960, 961,
	962, 963, 964, 965, 966, 967, 968, 969,
	// Entry 100 - 13F
	970, 971, 972, 638, 716, 642, 688, 643,
	646, 682, 90, 690, 729, 752, 702, 654,
	705, 744, 703, 694, 674, 686, 706, 740,
	728, 678, 810, 222, 534, 760, 748, 0,
	796, 148, 260, 768, 764, 762, 772, 626,
	795, 788, 776, 626, 792, 780, 798, 158,
	834, 804, 800, 826, 581, 0, 840, 858,
	860, 336, 670, 704, 862, 92, 850, 704,
	// Entry 140 - 17F
	548, 876, 581, 882, 973, 974, 975, 976,
	977, 978, 979, 980, 981, 982, 983, 984,
	985, 986, 987, 988, 989, 990, 991, 992,
	993, 994, 995, 996, 997, 998, 720, 887,
	175, 891, 710, 894, 180, 716, 999,
}

// m49Index gives indexes into fromM49 based on the three most significant bits
// of a 10-bit UN.M49 code. To search an UN.M49 code in fromM49, search in
//
//	fromM49[m49Index[msb39(code)]:m49Index[msb3(code)+1]]
//
// for an entry where the first 7 bits match the 7 lsb of the UN.M49 code.
// The region code is stored in the 9 lsb of the indexed value.
// Size: 18 bytes, 9 elements
var m49Index = [9]int16{
	0, 59, 108, 143, 181, 220, 259, 291,
	333,
}

// fromM49 contains entries to map UN.M49 codes to regions. See m49Index for details.
// Size: 666 bytes, 333 elements
var fromM49 = [333]uint16{
	// Entry 0 - 3F
	0x0201, 0x0402, 0x0603, 0x0824, 0x0a04, 0x1027, 0x1205, 0x142b,
	0x1606, 0x1868, 0x1a07, 0x1c08, 0x1e09, 0x202d, 0x220a, 0x240b,
	0x260c, 0x2822, 0x2a0d, 0x302a, 0x3825, 0x3a0e, 0x3c0f, 0x3e32,
	0x402c, 0x4410, 0x4611, 0x482f, 0x4e12, 0x502e, 0x5842, 0x6039,
	0x6435, 0x6628, 0x6834, 0x6a13, 0x6c14, 0x7036, 0x7215, 0x783d,
	0x7a16, 0x8043, 0x883f, 0x8c33, 0x9046, 0x9445, 0x9841, 0xa848,
	0xac9b, 0xb50a, 0xb93d, 0xc03e, 0xc838, 0xd0c5, 0xd83a, 0xe047,
	0xe8a7, 0xf052, 0xf849, 0x085b, 0x10ae, 0x184c, 0x1c17, 0x1e18,
	// Entry 40 - 7F
	0x20b4, 0x2219, 0x2921, 0x2c1a, 0x2e1b, 0x3051, 0x341c, 0x361d,
	0x3853, 0x3d2f, 0x445d, 0x4c4a, 0x5454, 0x5ca9, 0x5f60, 0x644d,
	0x684b, 0x7050, 0x7857, 0x7e91, 0x805a, 0x885e, 0x941e, 0x965f,
	0x983b, 0xa064, 0xa865, 0xac66, 0xb46a, 0xbd1b, 0xc487, 0xcc70,
	0xce70, 0xd06e, 0xd26b, 0xd477, 0xdc75, 0xde89, 0xe474, 0xec73,
	0xf031, 0xf27a, 0xf479, 0xfc7f, 0x04e6, 0x0922, 0x0c63, 0x147b,
	0x187e, 0x1c84, 0x26ee, 0x2861, 0x2c60, 0x3061, 0x4081, 0x4882,
	0x50a8, 0x5888, 0x6083, 0x687d, 0x7086, 0x788b, 0x808a, 0x8885,
	// Entry 80 - BF
	0x908d, 0x9892, 0x9c8f, 0xa139, 0xa890, 0xb08e, 0xb893, 0xc09e,
	0xc89a, 0xd096, 0xd89d, 0xe09c, 0xe897, 0xf098, 0xf89f, 0x004f,
	0x08a1, 0x10a3, 0x1caf, 0x20a2, 0x28a5, 0x30ab, 0x34ac, 0x3cad,
	0x42a6, 0x44b0, 0x461f, 0x4cb1, 0x54b6, 0x58b9, 0x5cb5, 0x64ba,
	0x6cb3, 0x70b7, 0x74b8, 0x7cc7, 0x84c0, 0x8ccf, 0x94d1, 0x9cce,
	0xa4c4, 0xaccc, 0xb4c9, 0xbcca, 0xc0cd, 0xc8d0, 0xd8bc, 0xe0c6,
	0xe4bd, 0xe6be, 0xe8cb, 0xf0bb, 0xf8d2, 0x00e2, 0x08d3, 0x10de,
	0x18dc, 0x20da, 0x2429, 0x265c, 0x2a30, 0x2d1c, 0x2e40, 0x30df,
	// Entry C0 - FF
	0x38d4, 0x4940, 0x54e1, 0x5cd9, 0x64d5, 0x6cd7, 0x74e0, 0x7cd6,
	0x84db, 0x88c8, 0x8b34, 0x8e76, 0x90c1, 0x92f1, 0x94e9, 0x9ee3,
	0xace7, 0xb0f2, 0xb8e5, 0xc0e8, 0xc8ec, 0xd0ea, 0xd8ef, 0xe08c,
	0xe527, 0xeced, 0xf4f4, 0xfd03, 0x0505, 0x0707, 0x0d08, 0x183c,
	0x1d0f, 0x26aa, 0x2826, 0x2cb2, 0x2ebf, 0x34eb, 0x3d3a, 0x4514,
	0x4d19, 0x5509, 0x5d15, 0x6106, 0x650b, 0x6d13, 0x7d0e, 0x7f12,
	0x813f, 0x8310, 0x8516, 0x8d62, 0x9965, 0xa15e, 0xa86f, 0xb118,
	0xb30c, 0xb86d, 0xc10c, 0xc917, 0xd111, 0xd91e, 0xe10d, 0xe84e,
	// Entry 100 - 13F
	0xf11d, 0xf525, 0xf924, 0x0123, 0x0926, 0x112a, 0x192d, 0x2023,
	0x2929, 0x312c, 0x3728, 0x3920, 0x3d2e, 0x4132, 0x4931, 0x4ec3,
	0x551a, 0x646c, 0x747c, 0x7e80, 0x80a0, 0x8299, 0x8530, 0x9136,
	0xa53e, 0xac37, 0xb537, 0xb938, 0xbd3c, 0xd941, 0xe543, 0xed5f,
	0xef5f, 0xf658, 0xfd63, 0x7c20, 0x7ef5, 0x80f6, 0x82f7, 0x84f8,
	0x86f9, 0x88fa, 0x8afb, 0x8cfc, 0x8e71, 0x90fe, 0x92ff, 0x9500,
	0x9701, 0x9902, 0x9b44, 0x9d45, 0x9f46, 0xa147, 0xa348, 0xa549,
	0xa74a, 0xa94b, 0xab4c, 0xad4d, 0xaf4e, 0xb14f, 0xb350, 0xb551,
	// Entry 140 - 17F
	0xb752, 0xb953, 0xbb54, 0xbd55, 0xbf56, 0xc157, 0xc358, 0xc559,
	0xc75a, 0xc95b, 0xcb5c, 0xcd5d, 0xcf66,
}

// Size: 2128 bytes
var variantIndex = map[string]uint8{
	"1606nict": 0x0,
	"1694acad": 0x1,
	"1901":     0x2,
	"1959acad": 0x3,
	"1994":     0x67,
	"1996":     0x4,
	"abl1943":  0x5,
	"akuapem":  0x6,
	"alalc97":  0x69,
	"aluku":    0x7,
	"ao1990":   0x8,
	"aranes":   0x9,
	"arevela":  0xa,
	"arevmda":  0xb,
	"arkaika":  0xc,
	"asante":   0xd,
	"auvern":   0xe,
	"baku1926": 0xf,
	"balanka":  0x10,
	"barla":    0x11,
	"basiceng": 0x12,
	"bauddha":  0x13,
	"bciav":    0x14,
	"bcizbl":   0x15,
	"biscayan": 0x16,
	"biske":    0x62,
	"bohoric":  0x17,
	"boont":    0x18,
	"bornholm": 0x19,
	"cisaup":   0x1a,
	"colb1945": 0x1b,
	"cornu":    0x1c,
	"creiss":   0x1d,
	"dajnko":   0x1e,
	"ekavsk":   0x1f,
	"emodeng":  0x20,
	"fonipa":   0x6a,
	"fonkirsh": 0x6b,
	"fonnapa":  0x6c,
	"fonupa":   0x6d,
	"fonxsamp": 0x6e,
	"gallo":    0x21,
	"gascon":   0x22,
	"grclass":  0x23,
	"grital":   0x24,
	"grmistr":  0x25,
	"hepburn":  0x26,
	"heploc":   0x68,
	"hognorsk": 0x27,
	"hsistemo": 0x28,
	"ijekavsk": 0x29,
	"itihasa":  0x2a,
	"ivanchov": 0x2b,
	"jauer":    0x2c,
	"jyutping": 0x2d,
	"kkcor":    0x2e,
	"kociewie": 0x2f,
	"kscor":    0x30,
	"laukika":  0x31,
	"lemosin":  0x32,
	"lengadoc": 0x33,
	"lipaw":    0x63,
	"ltg1929":  0x34,
	"ltg2007":  0x35,
	"luna1918": 0x36,
	"metelko":  0x37,
	"monoton":  0x38,
	"ndyuka":   0x39,
	"nedis":    0x3a,
	"newfound": 0x3b,
	"nicard":   0x3c,
	"njiva":    0x64,
	"nulik":    0x3d,
	"osojs":    0x65,
	"oxendict": 0x3e,
	"pahawh2":  0x3f,
	"pahawh3":  0x40,
	"pahawh4":  0x41,
	"pamaka":   0x42,
	"peano":    0x43,
	"petr1708": 0x44,
	"pinyin":   0x45,
	"polyton":  0x46,
	"provenc":  0x47,
	"puter":    0x48,
	"rigik":    0x49,
	"rozaj":    0x4a,
	"rumgr":    0x4b,
	"scotland": 0x4c,
	"scouse":   0x4d,
	"simple":   0x6f,
	"solba":    0x66,
	"sotav":    0x4e,
	"spanglis": 0x4f,
	"surmiran": 0x50,
	"sursilv":  0x51,
	"sutsilv":  0x52,
	"synnejyl": 0x53,
	"tarask":   0x54,
	"tongyong": 0x55,
	"tunumiit": 0x56,
	"uccor":    0x57,
	"ucrcor":   0x58,
	"ulster":   0x59,
	"unifon":   0x5a,
	"vaidika":  0x5b,
	"valencia": 0x5c,
	"vallader": 0x5d,
	"vecdruka": 0x5e,
	"vivaraup": 0x5f,
	"wadegile": 0x60,
	"xsistemo": 0x61,
}

// variantNumSpecialized is the number of specialized variants in variants.
const variantNumSpecialized = 105

// nRegionGroups is the number of region groups.
const nRegionGroups = 33

type likelyLangRegion struct {
	lang   uint16
	region uint16
}

// likelyScript is a lookup table, indexed by scriptID, for the most likely
// languages and regions given a script.
// Size: 1052 bytes, 263 elements
var likelyScript = [263]likelyLangRegion{
	1:   {lang: 0x14e, region: 0x85},
	3:   {lang: 0x2a2, region: 0x107},
	4:   {lang: 0x1f, region: 0x9a},
	5:   {lang: 0x3a, region: 0x6c},
	7:   {lang: 0x3b, region: 0x9d},
	8:   {lang: 0x1d7, region: 0x28},
	9:   {lang: 0x13, region: 0x9d},
	10:  {lang: 0x5b, region: 0x96},
	11:  {lang: 0x60, region: 0x52},
	12:  {lang: 0xb9, region: 0xb5},
	13:  {lang: 0x63, region: 0x96},
	14:  {lang: 0xa5, region: 0x35},
	15:  {lang: 0x3e9, region: 0x9a},
	17:  {lang: 0x529, region: 0x12f},
	18:  {lang: 0x3b1, region: 0x9a},
	19:  {lang: 0x15e, region: 0x79},
	20:  {lang: 0xc2, region: 0x96},
	21:  {lang: 0x9d, region: 0xe8},
	22:  {lang: 0xdb, region: 0x35},
	23:  {lang: 0xf3, region: 0x49},
	24:  {lang: 0x4f0, region: 0x12c},
	25:  {lang: 0xe7, region: 0x13f},
	26:  {lang: 0xe5, region: 0x136},
	29:  {lang: 0xf1, region: 0x6c},
	31:  {lang: 0x1a0, region: 0x5e},
	32:  {lang: 0x3e2, region: 0x107},
	34:  {lang: 0x1be, region: 0x9a},
	38:  {lang: 0x15e, region: 0x79},
	41:  {lang: 0x133, region: 0x6c},
	42:  {lang: 0x431, region: 0x27},
	44:  {lang: 0x27, region: 0x70},
	46:  {lang: 0x210, region: 0x7e},
	47:  {lang: 0xfe, region: 0x38},
	49:  {lang: 0x19b, region: 0x9a},
	50:  {lang: 0x19e, region: 0x131},
	51:  {lang: 0x3e9, region: 0x9a},
	52:  {lang: 0x136, region: 0x88},
	53:  {lang: 0x1a4, region: 0x9a},
	54:  {lang: 0x39d, region: 0x9a},
	55:  {lang: 0x529, region: 0x12f},
	56:  {lang: 0x254, region: 0xac},
	57:  {lang: 0x529, region: 0x53},
	58:  {lang: 0x1cb, region: 0xe8},
	59:  {lang: 0x529, region: 0x53},
	60:  {lang: 0x529, region: 0x12f},
	61:  {lang: 0x2fd, region: 0x9c},
	62:  {lang: 0x1bc, region: 0x98},
	63:  {lang: 0x200, region: 0xa3},
	64:  {lang: 0x1c5, region: 0x12c},
	65:  {lang: 0x1ca, region: 0xb0},
	68:  {lang: 0x1d5, region: 0x93},
	70:  {lang: 0x142, region: 0x9f},
	71:  {lang: 0x254, region: 0xac},
	72:  {lang: 0x20e, region: 0x96},
	73:  {lang: 0x200, region: 0xa3},
	75:  {lang: 0x135, region: 0xc5},
	76:  {lang: 0x200, region: 0xa3},
	78:  {lang: 0x3bb, region: 0xe9},
	79:  {lang: 0x24a, region: 0xa7},
	80:  {lang: 0x3fa, region: 0x9a},
	83:  {lang: 0x251, region: 0x9a},
	84:  {lang: 0x254, region: 0xac},
	86:  {lang: 0x88, region: 0x9a},
	87:  {lang: 0x370, region: 0x124},
	88:  {lang: 0x2b8, region: 0xb0},
	93:  {lang: 0x29f, region: 0x9a},
	94:  {lang: 0x2a8, region: 0x9a},
	95:  {lang: 0x28f, region: 0x88},
	96:  {lang: 0x1a0, region: 0x88},
	97:  {lang: 0x2ac, region: 0x53},
	99:  {lang: 0x4f4, region: 0x12c},
	100: {lang: 0x4f5, region: 0x12c},
	101: {lang: 0x1be, region: 0x9a},
	103: {lang: 0x337, region: 0x9d},
	104: {lang: 0x4f7, region: 0x53},
	105: {lang: 0xa9, region: 0x53},
	108: {lang: 0x2e8, region: 0x113},
	109: {lang: 0x4f8, region: 0x10c},
	110: {lang: 0x4f8, region: 0x10c},
	111: {lang: 0x304, region: 0x9a},
	112: {lang: 0x31b, region: 0x9a},
	113: {lang: 0x30b, region: 0x53},
	115: {lang: 0x31e, region: 0x35},
	116: {lang: 0x30e, region: 0x9a},
	117: {lang: 0x414, region: 0xe9},
	118: {lang: 0x331, region: 0xc5},
	121: {lang: 0x4f9, region: 0x109},
	122: {lang: 0x3b, region: 0xa2},
	123: {lang: 0x353, region: 0xdc},
	126: {lang: 0x2d0, region: 0x85},
	127: {lang: 0x52a, region: 0x53},
	128: {lang: 0x403, region: 0x97},
	129: {lang: 0x3ee, region: 0x9a},
	130: {lang: 0x39b, region: 0xc6},
	131: {lang: 0x395, region: 0x9a},
	132: {lang: 0x399, region: 0x136},
	133: {lang: 0x429, region: 0x116},
	135: {lang: 0x3b, region: 0x11d},
	136: {lang: 0xfd, region: 0xc5},
	139: {lang: 0x27d, region: 0x107},
	140: {lang: 0x2c9, region: 0x53},
	141: {lang: 0x39f, region: 0x9d},
	142: {lang: 0x39f, region: 0x53},
	144: {lang: 0x3ad, region: 0xb1},
	146: {lang: 0x1c6, region: 0x53},
	147: {lang: 0x4fd, region: 0x9d},
	200: {lang: 0x3cb, region: 0x96},
	203: {lang: 0x372, region: 0x10d},
	204: {lang: 0x420, region: 0x98},
	206: {lang: 0x4ff, region: 0x15f},
	207: {lang: 0x3f0, region: 0x9a},
	208: {lang: 0x45, region: 0x136},
	209: {lang: 0x139, region: 0x7c},
	210: {lang: 0x3e9, region: 0x9a},
	212: {lang: 0x3e9, region: 0x9a},
	213: {lang: 0x3fa, region: 0x9a},
	214: {lang: 0x40c, region: 0xb4},
	217: {lang: 0x433, region: 0x9a},
	218: {lang: 0xef, region: 0xc6},
	219: {lang: 0x43e, region: 0x96},
	221: {lang: 0x44d, region: 0x35},
	222: {lang: 0x44e, region: 0x9c},
	226: {lang: 0x45a, region: 0xe8},
	227: {lang: 0x11a, region: 0x9a},
	228: {lang: 0x45e, region: 0x53},
	229: {lang: 0x232, region: 0x53},
	230: {lang: 0x450, region: 0x9a},
	231: {lang: 0x4a5, region: 0x53},
	232: {lang: 0x9f, region: 0x13f},
	233: {lang: 0x461, region: 0x9a},
	235: {lang: 0x528, region: 0xbb},
	236: {lang: 0x153, region: 0xe8},
	237: {lang: 0x128, region: 0xce},
	238: {lang: 0x46b, region: 0x124},
	239: {lang: 0xa9, region: 0x53},
	240: {lang: 0x2ce, region: 0x9a},
	243: {lang: 0x4ad, region: 0x11d},
	244: {lang: 0x4be, region: 0xb5},
	247: {lang: 0x1ce, region: 0x9a},
	250: {lang: 0x3a9, region: 0x9d},
	251: {lang: 0x22, region: 0x9c},
	253: {lang: 0x1ea, region: 0x53},
	254: {lang: 0xef, region: 0xc6},
}

type likelyScriptRegion struct {
	region uint16
	script uint16
	flags  uint8
}

// likelyLang is a lookup table, indexed by langID, for the most likely
// scripts and regions given incomplete information. If more entries exist for a
// given language, region and script are the index and size respectively
// of the list in likelyLangList.
// Size: 7980 bytes, 1330 elements
var likelyLang = [1330]likelyScriptRegion{
	0:    {region: 0x136, script: 0x5b, flags: 0x0},
	1:    {region: 0x70, script: 0x5b, flags: 0x0},
	2:    {region: 0x166, script: 0x5b, flags: 0x0},
	3:    {region: 0x166, script: 0x5b, flags: 0x0},
	4:    {region: 0x166, script: 0x5b, flags: 0x0},
	5:    {region: 0x7e, script: 0x20, flags: 0x0},
	6:    {region: 0x166, script: 0x5b, flags: 0x0},
	7:    {region: 0x166, script: 0x20, flags: 0x0},
	8:    {region: 0x81, script: 0x5b, flags: 0x0},
	9:    {region: 0x166, script: 0x5b, flags: 0x0},
	10:   {region: 0x166, script: 0x5b, flags: 0x0},
	11:   {region: 0x166, script: 0x5b, flags: 0x0},
	12:   {region: 0x96, script: 0x5b, flags: 0x0},
	13:   {region: 0x132, script: 0x5b, flags: 0x0},
	14:   {region: 0x81, script: 0x5b, flags: 0x0},
	15:   {region: 0x166, script: 0x5b, flags: 0x0},
	16:   {region: 0x166, script: 0x5b, flags: 0x0},
	17:   {region: 0x107, script: 0x20, flags: 0x0},
	18:   {region: 0x166, script: 0x5b, flags: 0x0},
	19:   {region: 0x9d, script: 0x9, flags: 0x0},
	20:   {region: 0x129, script: 0x5, flags: 0x0},
	21:   {region: 0x166, script: 0x5b, flags: 0x0},
	22:   {region: 0x162, script: 0x5b, flags: 0x0},
	23:   {region: 0x166, script: 0x5b, flags: 0x0},
	24:   {region: 0x166, script: 0x5b, flags: 0x0},
	25:   {region: 0x166, script: 0x5b, flags: 0x0},
	26:   {region: 0x166, script: 0x5b, flags: 0x0},
	27:   {region: 0x166, script: 0x5b, flags: 0x0},
	28:   {region: 0x52, script: 0x5b, flags: 0x0},
	29:   {region: 0x166, script: 0x5b, flags: 0x0},
	30:   {region: 0x166, script: 0x5b, flags: 0x0},
	31:   {region: 0x9a, script: 0x4, flags: 0x0},
	32:   {region: 0x166, script: 0x5b, flags: 0x0},
	33:   {region: 0x81, script: 0x5b, flags: 0x0},
	34:   {region: 0x9c, script: 0xfb, flags: 0x0},
	35:   {region: 0x166, script: 0x5b, flags: 0x0},
	36:   {region: 0x166, script: 0x5b, flags: 0x0},
	37:   {region: 0x14e, script: 0x5b, flags: 0x0},
	38:   {region: 0x107, script: 0x20, flags: 0x0},
	39:   {region: 0x70, script: 0x2c, flags: 0x0},
	40:   {region: 0x166, script: 0x5b, flags: 0x0},
	41:   {region: 0x166, script: 0x5b, flags: 0x0},
	42:   {region: 0xd7, script: 0x5b, flags: 0x0},
	43:   {region: 0x166, script: 0x5b, flags: 0x0},
	45:   {region: 0x166, script: 0x5b, flags: 0x0},
	46:   {region: 0x166, script: 0x5b, flags: 0x0},
	47:   {region: 0x166, script: 0x5b, flags: 0x0},
	48:   {region: 0x166, script: 0x5b, flags: 0x0},
	49:   {region: 0x166, script: 0x5b, flags: 0x0},
	50:   {region: 0x166, script: 0x5b, flags: 0x0},
	51:   {region: 0x96, script: 0x5b, flags: 0x0},
	52:   {region: 0x166, script: 0x5, flags: 0x0},
	53:   {region: 0x123, script: 0x5, flags: 0x0},
	54:   {region: 0x166, script: 0x5b, flags: 0x0},
	55:   {region: 0x166, script: 0x5b, flags: 0x0},
	56:   {region: 0x166, script: 0x5b, flags: 0x0},
	57:   {region: 0x166, script: 0x5b, flags: 0x0},
	58:   {region: 0x6c, script: 0x5, flags: 0x0},
	59:   {region: 0x0, script: 0x3, flags: 0x1},
	60:   {region: 0x166, script: 0x5b, flags: 0x0},
	61:   {region: 0x51, script: 0x5b, flags: 0x0},
	62:   {region: 0x3f, script: 0x5b, flags: 0x0},
	63:   {region: 0x68, script: 0x5, flags: 0x0},
	65:   {region: 0xbb, script: 0x5, flags: 0x0},
	66:   {region: 0x6c, script: 0x5, flags: 0x0},
	67:   {region: 0x9a, script: 0xe, flags: 0x0},
	68:   {region: 0x130, script: 0x5b, flags: 0x0},
	69:   {region: 0x136, script: 0xd0, flags: 0x0},
	70:   {region: 0x166, script: 0x5b, flags: 0x0},
	71:   {region: 0x166, script: 0x5b, flags: 0x0},
	72:   {region: 0x6f, script: 0x5b, flags: 0x0},
	73:   {region: 0x166, script: 0x5b, flags: 0x0},
	74:   {region: 0x166, script: 0x5b, flags: 0x0},
	75:   {region: 0x49, script: 0x5b, flags: 0x0},
	76:   {region: 0x166, script: 0x5b, flags: 0x0},
	77:   {region: 0x107, script: 0x20, flags: 0x0},
	78:   {region: 0x166, script: 0x5, flags: 0x0},
	79:   {region: 0x166, script: 0x5b, flags: 0x0},
	80:   {region: 0x166, script: 0x5b, flags: 0x0},
	81:   {region: 0x166, script: 0x5b, flags: 0x0},
	82:   {region: 0x9a, script: 0x22, flags: 0x0},
	83:   {region: 0x166, script: 0x5b, flags: 0x0},
	84:   {region: 0x166, script: 0x5b, flags: 0x0},
	85:   {region: 0x166, script: 0x5b, flags: 0x0},
	86:   {region: 0x3f, script: 0x5b, flags: 0x0},
	87:   {region: 0x166, script: 0x5b, flags: 0x0},
	88:   {region: 0x3, script: 0x5, flags: 0x1},
	89:   {region: 0x107, script: 0x20, flags: 0x0},
	90:   {region: 0xe9, script: 0x5, flags: 0x0},
	91:   {region: 0x96, script: 0x5b, flags: 0x0},
	92:   {region: 0xdc, script: 0x22, flags: 0x0},
	93:   {region: 0x2e, script: 0x5b, flags: 0x0},
	94:   {region: 0x52, script: 0x5b, flags: 0x0},
	95:   {region: 0x166, script: 0x5b, flags: 0x0},
	96:   {region: 0x52, script: 0xb, flags: 0x0},
	97:   {region: 0x166, script: 0x5b, flags: 0x0},
	98:   {region: 0x166, script: 0x5b, flags: 0x0},
	99:   {region: 0x96, script: 0x5b, flags: 0x0},
	100:  {region: 0x166, script: 0x5b, flags: 0x0},
	101:  {region: 0x52, script: 0x5b, flags: 0x0},
	102:  {region: 0x166, script: 0x5b, flags: 0x0},
	103:  {region: 0x166, script: 0x5b, flags: 0x0},
	104:  {region: 0x166, script: 0x5b, flags: 0x0},
	105:  {region: 0x166, script: 0x5b, flags: 0x0},
	106:  {region: 0x4f, script: 0x5b, flags: 0x0},
	107:  {region: 0x166, script: 0x5b, flags: 0x0},
	108:  {region: 0x166, script: 0x5b, flags: 0x0},
	109:  {region: 0x166, script: 0x5b, flags: 0x0},
	110:  {region: 0x166, script: 0x2c, flags: 0x0},
	111:  {region: 0x166, script: 0x5b, flags: 0x0},
	112:  {region: 0x166, script: 0x5b, flags: 0x0},
	113:  {region: 0x47, script: 0x20, flags: 0x0},
	114:  {region: 0x166, script: 0x5b, flags: 0x0},
	115:  {region: 0x166, script: 0x5b, flags: 0x0},
	116:  {region: 0x10c, script: 0x5, flags: 0x0},
	117:  {region: 0x163, script: 0x5b, flags: 0x0},
	118:  {region: 0x166, script: 0x5b, flags: 0x0},
	119:  {region: 0x96, script: 0x5b, flags: 0x0},
	120:  {region: 0x166, script: 0x5b, flags: 0x0},
	121:  {region: 0x130, script: 0x5b, flags: 0x0},
	122:  {region: 0x52, script: 0x5b, flags: 0x0},
	123:  {region: 0x9a, script: 0xe6, flags: 0x0},
	124:  {region: 0xe9, script: 0x5, flags: 0x0},
	125:  {region: 0x9a, script: 0x22, flags: 0x0},
	126:  {region: 0x38, script: 0x20, flags: 0x0},
	127:  {region: 0x9a, script: 0x22, flags: 0x0},
	128:  {region: 0xe9, script: 0x5, flags: 0x0},
	129:  {region: 0x12c, script: 0x34, flags: 0x0},
	131:  {region: 0x9a, script: 0x22, flags: 0x0},
	132:  {region: 0x166, script: 0x5b, flags: 0x0},
	133:  {region: 0x9a, script: 0x22, flags: 0x0},
	134:  {region: 0xe8, script: 0x5b, flags: 0x0},
	135:  {region: 0x166, script: 0x5b, flags: 0x0},
	136:  {region: 0x9a, script: 0x22, flags: 0x0},
	137:  {region: 0x166, script: 0x5b, flags: 0x0},
	138:  {region: 0x140, script: 0x5b, flags: 0x0},
	139:  {region: 0x166, script: 0x5b, flags: 0x0},
	140:  {region: 0x166, script: 0x5b, flags: 0x0},
	141:  {region: 0xe8, script: 0x5b, flags: 0x0},
	142:  {region: 0x166, script: 0x5b, flags: 0x0},
	143:  {region: 0xd7, script: 0x5b, flags: 0x0},
	144:  {region: 0x166, script: 0x5b, flags: 0x0},
	145:  {region: 0x166, script: 0x5b, flags: 0x0},
	146:  {region: 0x166, script: 0x5b, flags: 0x0},
	147:  {region: 0x166, script: 0x2c, flags: 0x0},
	148:  {region: 0x9a, script: 0x22, flags: 0x0},
	149:  {region: 0x96, script: 0x5b, flags: 0x0},
	150:  {region: 0x166, script: 0x5b, flags: 0x0},
	151:  {region: 0x166, script: 0x5b, flags: 0x0},
	152:  {region: 0x115, script: 0x5b, flags: 0x0},
	153:  {region: 0x166, script: 0x5b, flags: 0x0},
	154:  {region: 0x166, script: 0x5b, flags: 0x0},
	155:  {region: 0x52, script: 0x5b, flags: 0x0},
	156:  {region: 0x166, script: 0x5b, flags: 0x0},
	157:  {region: 0xe8, script: 0x5b, flags: 0x0},
	158:  {region: 0x166, script: 0x5b, flags: 0x0},
	159:  {region: 0x13f, script: 0xe8, flags: 0x0},
	160:  {region: 0xc4, script: 0x5b, flags: 0x0},
	161:  {region: 0x166, script: 0x5b, flags: 0x0},
	162:  {region: 0x166, script: 0x5b, flags: 0x0},
	163:  {region: 0xc4, script: 0x5b, flags: 0x0},
	164:  {region: 0x166, script: 0x5b, flags: 0x0},
	165:  {region: 0x35, script: 0xe, flags: 0x0},
	166:  {region: 0x166, script: 0x5b, flags: 0x0},
	167:  {region: 0x166, script: 0x5b, flags: 0x0},
	168:  {region: 0x166, script: 0x5b, flags: 0x0},
	169:  {region: 0x53, script: 0xef, flags: 0x0},
	170:  {region: 0x166, script: 0x5b, flags: 0x0},
	171:  {region: 0x166, script: 0x5b, flags: 0x0},
	172:  {region: 0x166, script: 0x5b, flags: 0x0},
	173:  {region: 0x9a, script: 0xe, flags: 0x0},
	174:  {region: 0x166, script: 0x5b, flags: 0x0},
	175:  {region: 0x9d, script: 0x5, flags: 0x0},
	176:  {region: 0x166, script: 0x5b, flags: 0x0},
	177:  {region: 0x4f, script: 0x5b, flags: 0x0},
	178:  {region: 0x79, script: 0x5b, flags: 0x0},
	179:  {region: 0x9a, script: 0x22, flags: 0x0},
	180:  {region: 0xe9, script: 0x5, flags: 0x0},
	181:  {region: 0x9a, script: 0x22, flags: 0x0},
	182:  {region: 0x166, script: 0x5b, flags: 0x0},
	183:  {region: 0x33, script: 0x5b, flags: 0x0},
	184:  {region: 0x166, script: 0x5b, flags: 0x0},
	185:  {region: 0xb5, script: 0xc, flags: 0x0},
	186:  {region: 0x52, script: 0x5b, flags: 0x0},
	187:  {region: 0x166, script: 0x2c, flags: 0x0},
	188:  {region: 0xe8, script: 0x5b, flags: 0x0},
	189:  {region: 0x166, script: 0x5b, flags: 0x0},
	190:  {region: 0xe9, script: 0x22, flags: 0x0},
	191:  {region: 0x107, script: 0x20, flags: 0x0},
	192:  {region: 0x160, script: 0x5b, flags: 0x0},
	193:  {region: 0x166, script: 0x5b, flags: 0x0},
	194:  {region: 0x96, script: 0x5b, flags: 0x0},
	195:  {region: 0x166, script: 0x5b, flags: 0x0},
	196:  {region: 0x52, script: 0x5b, flags: 0x0},
	197:  {region: 0x166, script: 0x5b, flags: 0x0},
	198:  {region: 0x166, script: 0x5b, flags: 0x0},
	199:  {region: 0x166, script: 0x5b, flags: 0x0},
	200:  {region: 0x87, script: 0x5b, flags: 0x0},
	201:  {region: 0x166, script: 0x5b, flags: 0x0},
	202:  {region: 0x166, script: 0x5b, flags: 0x0},
	203:  {region: 0x166, script: 0x5b, flags: 0x0},
	204:  {region: 0x166, script: 0x5b, flags: 0x0},
	205:  {region: 0x6e, script: 0x2c, flags: 0x0},
	206:  {region: 0x166, script: 0x5b, flags: 0x0},
	207:  {region: 0x166, script: 0x5b, flags: 0x0},
	208:  {region: 0x52, script: 0x5b, flags: 0x0},
	209:  {region: 0x166, script: 0x5b, flags: 0x0},
	210:  {region: 0x166, script: 0x5b, flags: 0x0},
	211:  {region: 0xc4, script: 0x5b, flags: 0x0},
	212:  {region: 0x166, script: 0x5b, flags: 0x0},
	213:  {region: 0x166, script: 0x5b, flags: 0x0},
	214:  {region: 0x166, script: 0x5b, flags: 0x0},
	215:  {region: 0x6f, script: 0x5b, flags: 0x0},
	216:  {region: 0x166, script: 0x5b, flags: 0x0},
	217:  {region: 0x166, script: 0x5b, flags: 0x0},
	218:  {region: 0xd7, script: 0x5b, flags: 0x0},
	219:  {region: 0x35, script: 0x16, flags: 0x0},
	220:  {region: 0x107, script: 0x20, flags: 0x0},
	221:  {region: 0xe8, script: 0x5b, flags: 0x0},
	222:  {region: 0x166, script: 0x5b, flags: 0x0},
	223:  {region: 0x132, script: 0x5b, flags: 0x0},
	224:  {region: 0x8b, script: 0x5b, flags: 0x0},
	225:  {region: 0x76, script: 0x5b, flags: 0x0},
	226:  {region: 0x107, script: 0x20, flags: 0x0},
	227:  {region: 0x136, script: 0x5b, flags: 0x0},
	228:  {region: 0x49, script: 0x5b, flags: 0x0},
	229:  {region: 0x136, script: 0x1a, flags: 0x0},
	230:  {region: 0xa7, script: 0x5, flags: 0x0},
	231:  {region: 0x13f, script: 0x19, flags: 0x0},
	232:  {region: 0x166, script: 0x5b, flags: 0x0},
	233:  {region: 0x9c, script: 0x5, flags: 0x0},
	234:  {region: 0x166, script: 0x5b, flags: 0x0},
	235:  {region: 0x166, script: 0x5b, flags: 0x0},
	236:  {region: 0x166, script: 0x5b, flags: 0x0},
	237:  {region: 0x166, script: 0x5b, flags: 0x0},
	238:  {region: 0x166, script: 0x5b, flags: 0x0},
	239:  {region: 0xc6, script: 0xda, flags: 0x0},
	240:  {region: 0x79, script: 0x5b, flags: 0x0},
	241:  {region: 0x6c, script: 0x1d, flags: 0x0},
	242:  {region: 0xe8, script: 0x5b, flags: 0x0},
	243:  {region: 0x49, script: 0x17, flags: 0x0},
	244:  {region: 0x131, script: 0x20, flags: 0x0},
	245:  {region: 0x49, script: 0x17, flags: 0x0},
	246:  {region: 0x49, script: 0x17, flags: 0x0},
	247:  {region: 0x49, script: 0x17, flags: 0x0},
	248:  {region: 0x49, script: 0x17, flags: 0x0},
	249:  {region: 0x10b, script: 0x5b, flags: 0x0},
	250:  {region: 0x5f, script: 0x5b, flags: 0x0},
	251:  {region: 0xea, script: 0x5b, flags: 0x0},
	252:  {region: 0x49, script: 0x17, flags: 0x0},
	253:  {region: 0xc5, script: 0x88, flags: 0x0},
	254:  {region: 0x8, script: 0x2, flags: 0x1},
	255:  {region: 0x107, script: 0x20, flags: 0x0},
	256:  {region: 0x7c, script: 0x5b, flags: 0x0},
	257:  {region: 0x64, script: 0x5b, flags: 0x0},
	258:  {region: 0x166, script: 0x5b, flags: 0x0},
	259:  {region: 0x166, script: 0x5b, flags: 0x0},
	260:  {region: 0x166, script: 0x5b, flags: 0x0},
	261:  {region: 0x166, script: 0x5b, flags: 0x0},
	262:  {region: 0x136, script: 0x5b, flags: 0x0},
	263:  {region: 0x107, script: 0x20, flags: 0x0},
	264:  {region: 0xa5, script: 0x5b, flags: 0x0},
	265:  {region: 0x166, script: 0x5b, flags: 0x0},
	266:  {region: 0x166, script: 0x5b, flags: 0x0},
	267:  {region: 0x9a, script: 0x5, flags: 0x0},
	268:  {region: 0x166, script: 0x5b, flags: 0x0},
	269:  {region: 0x61, script: 0x5b, flags: 0x0},
	270:  {region: 0x166, script: 0x5b, flags: 0x0},
	271:  {region: 0x49, script: 0x5b, flags: 0x0},
	272:  {region: 0x166, script: 0x5b, flags: 0x0},
	273:  {region: 0x166, script: 0x5b, flags: 0x0},
	274:  {region: 0x166, script: 0x5b, flags: 0x0},
	275:  {region: 0x166, script: 0x5, flags: 0x0},
	276:  {region: 0x49, script: 0x5b, flags: 0x0},
	277:  {region: 0x166, script: 0x5b, flags: 0x0},
	278:  {region: 0x166, script: 0x5b, flags: 0x0},
	279:  {region: 0xd5, script: 0x5b, flags: 0x0},
	280:  {region: 0x4f, script: 0x5b, flags: 0x0},
	281:  {region: 0x166, script: 0x5b, flags: 0x0},
	282:  {region: 0x9a, script: 0x5, flags: 0x0},
	283:  {region: 0x166, script: 0x5b, flags: 0x0},
	284:  {region: 0x166, script: 0x5b, flags: 0x0},
	285:  {region: 0x166, script: 0x5b, flags: 0x0},
	286:  {region: 0x166, script: 0x2c, flags: 0x0},
	287:  {region: 0x61, script: 0x5b, flags: 0x0},
	288:  {region: 0xc4, script: 0x5b, flags: 0x0},
	289:  {region: 0xd1, script: 0x5b, flags: 0x0},
	290:  {region: 0x166, script: 0x5b, flags: 0x0},
	291:  {region: 0xdc, script: 0x22, flags: 0x0},
	292:  {region: 0x52, script: 0x5b, flags: 0x0},
	293:  {region: 0x166, script: 0x5b, flags: 0x0},
	294:  {region: 0x166, script: 0x5b, flags: 0x0},
	295:  {region: 0x166, script: 0x5b, flags: 0x0},
	296:  {region: 0xce, script: 0xed, flags: 0x0},
	297:  {region: 0x166, script: 0x5b, flags: 0x0},
	298:  {region: 0x166, script: 0x5b, flags: 0x0},
	299:  {region: 0x115, script: 0x5b, flags: 0x0},
	300:  {region: 0x37, script: 0x5b, flags: 0x0},
	301:  {region: 0x43, script: 0xef, flags: 0x0},
	302:  {region: 0x166, script: 0x5b, flags: 0x0},
	303:  {region: 0xa5, script: 0x5b, flags: 0x0},
	304:  {region: 0x81, script: 0x5b, flags: 0x0},
	305:  {region: 0xd7, script: 0x5b, flags: 0x0},
	306:  {region: 0x9f, script: 0x5b, flags: 0x0},
	307:  {region: 0x6c, script: 0x29, flags: 0x0},
	308:  {region: 0x166, script: 0x5b, flags: 0x0},
	309:  {region: 0xc5, script: 0x4b, flags: 0x0},
	310:  {region: 0x88, script: 0x34, flags: 0x0},
	311:  {region: 0x166, script: 0x5b, flags: 0x0},
	312:  {region: 0x166, script: 0x5b, flags: 0x0},
	313:  {region: 0xa, script: 0x2, flags: 0x1},
	314:  {region: 0x166, script: 0x5b, flags: 0x0},
	315:  {region: 0x166, script: 0x5b, flags: 0x0},
	316:  {region: 0x1, script: 0x5b, flags: 0x0},
	317:  {region: 0x166, script: 0x5b, flags: 0x0},
	318:  {region: 0x6f, script: 0x5b, flags: 0x0},
	319:  {region: 0x136, script: 0x5b, flags: 0x0},
	320:  {region: 0x6b, script: 0x5b, flags: 0x0},
	321:  {region: 0x166, script: 0x5b, flags: 0x0},
	322:  {region: 0x9f, script: 0x46, flags: 0x0},
	323:  {region: 0x166, script: 0x5b, flags: 0x0},
	324:  {region: 0x166, script: 0x5b, flags: 0x0},
	325:  {region: 0x6f, script: 0x5b, flags: 0x0},
	326:  {region: 0x52, script: 0x5b, flags: 0x0},
	327:  {region: 0x6f, script: 0x5b, flags: 0x0},
	328:  {region: 0x9d, script: 0x5, flags: 0x0},
	329:  {region: 0x166, script: 0x5b, flags: 0x0},
	330:  {region: 0x166, script: 0x5b, flags: 0x0},
	331:  {region: 0x166, script: 0x5b, flags: 0x0},
	332:  {region: 0x166, script: 0x5b, flags: 0x0},
	333:  {region: 0x87, script: 0x5b, flags: 0x0},
	334:  {region: 0xc, script: 0x2, flags: 0x1},
	335:  {region: 0x166, script: 0x5b, flags: 0x0},
	336:  {region: 0xc4, script: 0x5b, flags: 0x0},
	337:  {region: 0x73, script: 0x5b, flags: 0x0},
	338:  {region: 0x10c, script: 0x5, flags: 0x0},
	339:  {region: 0xe8, script: 0x5b, flags: 0x0},
	340:  {region: 0x10d, script: 0x5b, flags: 0x0},
	341:  {region: 0x74, script: 0x5b, flags: 0x0},
	342:  {region: 0x166, script: 0x5b, flags: 0x0},
	343:  {region: 0x166, script: 0x5b, flags: 0x0},
	344:  {region: 0x77, script: 0x5b, flags: 0x0},
	345:  {region: 0x166, script: 0x5b, flags: 0x0},
	346:  {region: 0x3b, script: 0x5b, flags: 0x0},
	347:  {region: 0x166, script: 0x5b, flags: 0x0},
	348:  {region: 0x166, script: 0x5b, flags: 0x0},
	349:  {region: 0x166, script: 0x5b, flags: 0x0},
	350:  {region: 0x79, script: 0x5b, flags: 0x0},
	351:  {region: 0x136, script: 0x5b, flags: 0x0},
	352:  {region: 0x79, script: 0x5b, flags: 0x0},
	353:  {region: 0x61, script: 0x5b, flags: 0x0},
	354:  {region: 0x61, script: 0x5b, flags: 0x0},
	355:  {region: 0x52, script: 0x5, flags: 0x0},
	356:  {region: 0x141, script: 0x5b, flags: 0x0},
	357:  {region: 0x166, script: 0x5b, flags: 0x0},
	358:  {region: 0x85, script: 0x5b, flags: 0x0},
	359:  {region: 0x166, script: 0x5b, flags: 0x0},
	360:  {region: 0xd5, script: 0x5b, flags: 0x0},
	361:  {region: 0x9f, script: 0x5b, flags: 0x0},
	362:  {region: 0xd7, script: 0x5b, flags: 0x0},
	363:  {region: 0x166, script: 0x5b, flags: 0x0},
	364:  {region: 0x10c, script: 0x5b, flags: 0x0},
	365:  {region: 0xda, script: 0x5b, flags: 0x0},
	366:  {region: 0x97, script: 0x5b, flags: 0x0},
	367:  {region: 0x81, script: 0x5b, flags: 0x0},
	368:  {region: 0x166, script: 0x5b, flags: 0x0},
	369:  {region: 0xbd, script: 0x5b, flags: 0x0},
	370:  {region: 0x166, script: 0x5b, flags: 0x0},
	371:  {region: 0x166, script: 0x5b, flags: 0x0},
	372:  {region: 0x166, script: 0x5b, flags: 0x0},
	373:  {region: 0x53, script: 0x3b, flags: 0x0},
	374:  {region: 0x166, script: 0x5b, flags: 0x0},
	375:  {region: 0x96, script: 0x5b, flags: 0x0},
	376:  {region: 0x166, script: 0x5b, flags: 0x0},
	377:  {region: 0x166, script: 0x5b, flags: 0x0},
	378:  {region: 0x9a, script: 0x22, flags: 0x0},
	379:  {region: 0x166, script: 0x5b, flags: 0x0},
	380:  {region: 0x9d, script: 0x5, flags: 0x0},
	381:  {region: 0x7f, script: 0x5b, flags: 0x0},
	382:  {region: 0x7c, script: 0x5b, flags: 0x0},
	383:  {region: 0x166, script: 0x5b, flags: 0x0},
	384:  {region: 0x166, script: 0x5b, flags: 0x0},
	385:  {region: 0x166, script: 0x5b, flags: 0x0},
	386:  {region: 0x166, script: 0x5b, flags: 0x0},
	387:  {region: 0x166, script: 0x5b, flags: 0x0},
	388:  {region: 0x166, script: 0x5b, flags: 0x0},
	389:  {region: 0x70, script: 0x2c, flags: 0x0},
	390:  {region: 0x166, script: 0x5b, flags: 0x0},
	391:  {region: 0xdc, script: 0x22, flags: 0x0},
	392:  {region: 0x166, script: 0x5b, flags: 0x0},
	393:  {region: 0xa8, script: 0x5b, flags: 0x0},
	394:  {region: 0x166, script: 0x5b, flags: 0x0},
	395:  {region: 0xe9, script: 0x5, flags: 0x0},
	396:  {region: 0x166, script: 0x5b, flags: 0x0},
	397:  {region: 0xe9, script: 0x5, flags: 0x0},
	398:  {region: 0x166, script: 0x5b, flags: 0x0},
	399:  {region: 0x166, script: 0x5b, flags: 0x0},
	400:  {region: 0x6f, script: 0x5b, flags: 0x0},
	401:  {region: 0x9d, script: 0x5, flags: 0x0},
	402:  {region: 0x166, script: 0x5b, flags: 0x0},
	403:  {region: 0x166, script: 0x2c, flags: 0x0},
	404:  {region: 0xf2, script: 0x5b, flags: 0x0},
	405:  {region: 0x166, script: 0x5b, flags: 0x0},
	406:  {region: 0x166, script: 0x5b, flags: 0x0},
	407:  {region: 0x166, script: 0x5b, flags: 0x0},
	408:  {region: 0x166, script: 0x2c, flags: 0x0},
	409:  {region: 0x166, script: 0x5b, flags: 0x0},
	410:  {region: 0x9a, script: 0x22, flags: 0x0},
	411:  {region: 0x9a, script: 0xe9, flags: 0x0},
	412:  {region: 0x96, script: 0x5b, flags: 0x0},
	413:  {region: 0xda, script: 0x5b, flags: 0x0},
	414:  {region: 0x131, script: 0x32, flags: 0x0},
	415:  {region: 0x166, script: 0x5b, flags: 0x0},
	416:  {region: 0xe, script: 0x2, flags: 0x1},
	417:  {region: 0x9a, script: 0xe, flags: 0x0},
	418:  {region: 0x166, script: 0x5b, flags: 0x0},
	419:  {region: 0x4e, script: 0x5b, flags: 0x0},
	420:  {region: 0x9a, script: 0x35, flags: 0x0},
	421:  {region: 0x41, script: 0x5b, flags: 0x0},
	422:  {region: 0x54, script: 0x5b, flags: 0x0},
	423:  {region: 0x166, script: 0x5b, flags: 0x0},
	424:  {region: 0x81, script: 0x5b, flags: 0x0},
	425:  {region: 0x166, script: 0x5b, flags: 0x0},
	426:  {region: 0x166, script: 0x5b, flags: 0x0},
	427:  {region: 0xa5, script: 0x5b, flags: 0x0},
	428:  {region: 0x99, script: 0x5b, flags: 0x0},
	429:  {region: 0x166, script: 0x5b, flags: 0x0},
	430:  {region: 0xdc, script: 0x22, flags: 0x0},
	431:  {region: 0x166, script: 0x5b, flags: 0x0},
	432:  {region: 0x166, script: 0x5, flags: 0x0},
	433:  {region: 0x49, script: 0x5b, flags: 0x0},
	434:  {region: 0x166, script: 0x5, flags: 0x0},
	435:  {region: 0x166, script: 0x5b, flags: 0x0},
	436:  {region: 0x10, script: 0x3, flags: 0x1},
	437:  {region: 0x166, script: 0x5b, flags: 0x0},
	438:  {region: 0x53, script: 0x3b, flags: 0x0},
	439:  {region: 0x166, script: 0x5b, flags: 0x0},
	440:  {region: 0x136, script: 0x5b, flags: 0x0},
	441:  {region: 0x24, script: 0x5, flags: 0x0},
	442:  {region: 0x166, script: 0x5b, flags: 0x0},
	443:  {region: 0x166, script: 0x2c, flags: 0x0},
	444:  {region: 0x98, script: 0x3e, flags: 0x0},
	445:  {region: 0x166, script: 0x5b, flags: 0x0},
	446:  {region: 0x9a, script: 0x22, flags: 0x0},
	447:  {region: 0x166, script: 0x5b, flags: 0x0},
	448:  {region: 0x74, script: 0x5b, flags: 0x0},
	449:  {region: 0x166, script: 0x5b, flags: 0x0},
	450:  {region: 0x166, script: 0x5b, flags: 0x0},
	451:  {region: 0xe8, script: 0x5b, flags: 0x0},
	452:  {region: 0x166, script: 0x5b, flags: 0x0},
	453:  {region: 0x12c, script: 0x40, flags: 0x0},
	454:  {region: 0x53, script: 0x92, flags: 0x0},
	455:  {region: 0x166, script: 0x5b, flags: 0x0},
	456:  {region: 0xe9, script: 0x5, flags: 0x0},
	457:  {region: 0x9a, script: 0x22, flags: 0x0},
	458:  {region: 0xb0, script: 0x41, flags: 0x0},
	459:  {region: 0xe8, script: 0x5b, flags: 0x0},
	460:  {region: 0xe9, script: 0x5, flags: 0x0},
	461:  {region: 0xe7, script: 0x5b, flags: 0x0},
	462:  {region: 0x9a, script: 0x22, flags: 0x0},
	463:  {region: 0x9a, script: 0x22, flags: 0x0},
	464:  {region: 0x166, script: 0x5b, flags: 0x0},
	465:  {region: 0x91, script: 0x5b, flags: 0x0},
	466:  {region: 0x61, script: 0x5b, flags: 0x0},
	467:  {region: 0x53, script: 0x3b, flags: 0x0},
	468:  {region: 0x92, script: 0x5b, flags: 0x0},
	469:  {region: 0x93, script: 0x5b, flags: 0x0},
	470:  {region: 0x166, script: 0x5b, flags: 0x0},
	471:  {region: 0x28, script: 0x8, flags: 0x0},
	472:  {region: 0xd3, script: 0x5b, flags: 0x0},
	473:  {region: 0x79, script: 0x5b, flags: 0x0},
	474:  {region: 0x166, script: 0x5b, flags: 0x0},
	475:  {region: 0x166, script: 0x5b, flags: 0x0},
	476:  {region: 0xd1, script: 0x5b, flags: 0x0},
	477:  {region: 0xd7, script: 0x5b, flags: 0x0},
	478:  {region: 0x166, script: 0x5b, flags: 0x0},
	479:  {region: 0x166, script: 0x5b, flags: 0x0},
	480:  {region: 0x166, script: 0x5b, flags: 0x0},
	481:  {region: 0x96, script: 0x5b, flags: 0x0},
	482:  {region: 0x166, script: 0x5b, flags: 0x0},
	483:  {region: 0x166, script: 0x5b, flags: 0x0},
	484:  {region: 0x166, script: 0x5b, flags: 0x0},
	486:  {region: 0x123, script: 0x5b, flags: 0x0},
	487:  {region: 0xd7, script: 0x5b, flags: 0x0},
	488:  {region: 0x166, script: 0x5b, flags: 0x0},
	489:  {region: 0x166, script: 0x5b, flags: 0x0},
	490:  {region: 0x53, script: 0xfd, flags: 0x0},
	491:  {region: 0x166, script: 0x5b, flags: 0x0},
	492:  {region: 0x136, script: 0x5b, flags: 0x0},
	493:  {region: 0x166, script: 0x5b, flags: 0x0},
	494:  {region: 0x49, script: 0x5b, flags: 0x0},
	495:  {region: 0x166, script: 0x5b, flags: 0x0},
	496:  {region: 0x166, script: 0x5b, flags: 0x0},
	497:  {region: 0xe8, script: 0x5b, flags: 0x0},
	498:  {region: 0x166, script: 0x5b, flags: 0x0},
	499:  {region: 0x96, script: 0x5b, flags: 0x0},
	500:  {region: 0x107, script: 0x20, flags: 0x0},
	501:  {region: 0x1, script: 0x5b, flags: 0x0},
	502:  {region: 0x166, script: 0x5b, flags: 0x0},
	503:  {region: 0x166, script: 0x5b, flags: 0x0},
	504:  {region: 0x9e, script: 0x5b, flags: 0x0},
	505:  {region: 0x9f, script: 0x5b, flags: 0x0},
	506:  {region: 0x49, script: 0x17, flags: 0x0},
	507:  {region: 0x98, script: 0x3e, flags: 0x0},
	508:  {region: 0x166, script: 0x5b, flags: 0x0},
	509:  {region: 0x166, script: 0x5b, flags: 0x0},
	510:  {region: 0x107, script: 0x5b, flags: 0x0},
	511:  {region: 0x166, script: 0x5b, flags: 0x0},
	512:  {region: 0xa3, script: 0x49, flags: 0x0},
	513:  {region: 0x166, script: 0x5b, flags: 0x0},
	514:  {region: 0xa1, script: 0x5b, flags: 0x0},
	515:  {region: 0x1, script: 0x5b, flags: 0x0},
	516:  {region: 0x166, script: 0x5b, flags: 0x0},
	517:  {region: 0x166, script: 0x5b, flags: 0x0},
	518:  {region: 0x166, script: 0x5b, flags: 0x0},
	519:  {region: 0x52, script: 0x5b, flags: 0x0},
	520:  {region: 0x131, script: 0x3e, flags: 0x0},
	521:  {region: 0x166, script: 0x5b, flags: 0x0},
	522:  {region: 0x130, script: 0x5b, flags: 0x0},
	523:  {region: 0xdc, script: 0x22, flags: 0x0},
	524:  {region: 0x166, script: 0x5b, flags: 0x0},
	525:  {region: 0x64, script: 0x5b, flags: 0x0},
	526:  {region: 0x96, script: 0x5b, flags: 0x0},
	527:  {region: 0x96, script: 0x5b, flags: 0x0},
	528:  {region: 0x7e, script: 0x2e, flags: 0x0},
	529:  {region: 0x138, script: 0x20, flags: 0x0},
	530:  {region: 0x68, script: 0x5b, flags: 0x0},
	531:  {region: 0xc5, script: 0x5b, flags: 0x0},
	532:  {region: 0x166, script: 0x5b, flags: 0x0},
	533:  {region: 0x166, script: 0x5b, flags: 0x0},
	534:  {region: 0xd7, script: 0x5b, flags: 0x0},
	535:  {region: 0xa5, script: 0x5b, flags: 0x0},
	536:  {region: 0xc4, script: 0x5b, flags: 0x0},
	537:  {region: 0x107, script: 0x20, flags: 0x0},
	538:  {region: 0x166, script: 0x5b, flags: 0x0},
	539:  {region: 0x166, script: 0x5b, flags: 0x0},
	540:  {region: 0x166, script: 0x5b, flags: 0x0},
	541:  {region: 0x166, script: 0x5b, flags: 0x0},
	542:  {region: 0xd5, script: 0x5, flags: 0x0},
	543:  {region: 0xd7, script: 0x5b, flags: 0x0},
	544:  {region: 0x165, script: 0x5b, flags: 0x0},
	545:  {region: 0x166, script: 0x5b, flags: 0x0},
	546:  {region: 0x166, script: 0x5b, flags: 0x0},
	547:  {region: 0x130, script: 0x5b, flags: 0x0},
	548:  {region: 0x123, script: 0x5, flags: 0x0},
	549:  {region: 0x166, script: 0x5b, flags: 0x0},
	550:  {region: 0x124, script: 0xee, flags: 0x0},
	551:  {region: 0x5b, script: 0x5b, flags: 0x0},
	552:  {region: 0x52, script: 0x5b, flags: 0x0},
	553:  {region: 0x166, script: 0x5b, flags: 0x0},
	554:  {region: 0x4f, script: 0x5b, flags: 0x0},
	555:  {region: 0x9a, script: 0x22, flags: 0x0},
	556:  {region: 0x9a, script: 0x22, flags: 0x0},
	557:  {region: 0x4b, script: 0x5b, flags: 0x0},
	558:  {region: 0x96, script: 0x5b, flags: 0x0},
	559:  {region: 0x166, script: 0x5b, flags: 0x0},
	560:  {region: 0x41, script: 0x5b, flags: 0x0},
	561:  {region: 0x9a, script: 0x5b, flags: 0x0},
	562:  {region: 0x53, script: 0xe5, flags: 0x0},
	563:  {region: 0x9a, script: 0x22, flags: 0x0},
	564:  {region: 0xc4, script: 0x5b, flags: 0x0},
	565:  {region: 0x166, script: 0x5b, flags: 0x0},
	566:  {region: 0x9a, script: 0x76, flags: 0x0},
	567:  {region: 0xe9, script: 0x5, flags: 0x0},
	568:  {region: 0x166, script: 0x5b, flags: 0x0},
	569:  {region: 0xa5, script: 0x5b, flags: 0x0},
	570:  {region: 0x166, script: 0x5b, flags: 0x0},
	571:  {region: 0x12c, script: 0x5b, flags: 0x0},
	572:  {region: 0x166, script: 0x5b, flags: 0x0},
	573:  {region: 0xd3, script: 0x5b, flags: 0x0},
	574:  {region: 0x166, script: 0x5b, flags: 0x0},
	575:  {region: 0xb0, script: 0x58, flags: 0x0},
	576:  {region: 0x166, script: 0x5b, flags: 0x0},
	577:  {region: 0x166, script: 0x5b, flags: 0x0},
	578:  {region: 0x13, script: 0x6, flags: 0x1},
	579:  {region: 0x166, script: 0x5b, flags: 0x0},
	580:  {region: 0x52, script: 0x5b, flags: 0x0},
	581:  {region: 0x83, script: 0x5b, flags: 0x0},
	582:  {region: 0xa5, script: 0x5b, flags: 0x0},
	583:  {region: 0x166, script: 0x5b, flags: 0x0},
	584:  {region: 0x166, script: 0x5b, flags: 0x0},
	585:  {region: 0x166, script: 0x5b, flags: 0x0},
	586:  {region: 0xa7, script: 0x4f, flags: 0x0},
	587:  {region: 0x2a, script: 0x5b, flags: 0x0},
	588:  {region: 0x166, script: 0x5b, flags: 0x0},
	589:  {region: 0x166, script: 0x5b, flags: 0x0},
	590:  {region: 0x166, script: 0x5b, flags: 0x0},
	591:  {region: 0x166, script: 0x5b, flags: 0x0},
	592:  {region: 0x166, script: 0x5b, flags: 0x0},
	593:  {region: 0x9a, script: 0x53, flags: 0x0},
	594:  {region: 0x8c, script: 0x5b, flags: 0x0},
	595:  {region: 0x166, script: 0x5b, flags: 0x0},
	596:  {region: 0xac, script: 0x54, flags: 0x0},
	597:  {region: 0x107, script: 0x20, flags: 0x0},
	598:  {region: 0x9a, script: 0x22, flags: 0x0},
	599:  {region: 0x166, script: 0x5b, flags: 0x0},
	600:  {region: 0x76, script: 0x5b, flags: 0x0},
	601:  {region: 0x166, script: 0x5b, flags: 0x0},
	602:  {region: 0xb5, script: 0x5b, flags: 0x0},
	603:  {region: 0x166, script: 0x5b, flags: 0x0},
	604:  {region: 0x166, script: 0x5b, flags: 0x0},
	605:  {region: 0x166, script: 0x5b, flags: 0x0},
	606:  {region: 0x166, script: 0x5b, flags: 0x0},
	607:  {region: 0x166, script: 0x5b, flags: 0x0},
	608:  {region: 0x166, script: 0x5b, flags: 0x0},
	609:  {region: 0x166, script: 0x5b, flags: 0x0},
	610:  {region: 0x166, script: 0x2c, flags: 0x0},
	611:  {region: 0x166, script: 0x5b, flags: 0x0},
	612:  {region: 0x107, script: 0x20, flags: 0x0},
	613:  {region: 0x113, script: 0x5b, flags: 0x0},
	614:  {region: 0xe8, script: 0x5b, flags: 0x0},
	615:  {region: 0x107, script: 0x5b, flags: 0x0},
	616:  {region: 0x166, script: 0x5b, flags: 0x0},
	617:  {region: 0x9a, script: 0x22, flags: 0x0},
	618:  {region: 0x9a, script: 0x5, flags: 0x0},
	619:  {region: 0x130, script: 0x5b, flags: 0x0},
	620:  {region: 0x166, script: 0x5b, flags: 0x0},
	621:  {region: 0x52, script: 0x5b, flags: 0x0},
	622:  {region: 0x61, script: 0x5b, flags: 0x0},
	623:  {region: 0x166, script: 0x5b, flags: 0x0},
	624:  {region: 0x166, script: 0x5b, flags: 0x0},
	625:  {region: 0x166, script: 0x2c, flags: 0x0},
	626:  {region: 0x166, script: 0x5b, flags: 0x0},
	627:  {region: 0x166, script: 0x5b, flags: 0x0},
	628:  {region: 0x19, script: 0x3, flags: 0x1},
	629:  {region: 0x166, script: 0x5b, flags: 0x0},
	630:  {region: 0x166, script: 0x5b, flags: 0x0},
	631:  {region: 0x166, script: 0x5b, flags: 0x0},
	632:  {region: 0x166, script: 0x5b, flags: 0x0},
	633:  {region: 0x107, script: 0x20, flags: 0x0},
	634:  {region: 0x166, script: 0x5b, flags: 0x0},
	635:  {region: 0x166, script: 0x5b, flags: 0x0},
	636:  {region: 0x166, script: 0x5b, flags: 0x0},
	637:  {region: 0x107, script: 0x20, flags: 0x0},
	638:  {region: 0x166, script: 0x5b, flags: 0x0},
	639:  {region: 0x96, script: 0x5b, flags: 0x0},
	640:  {region: 0xe9, script: 0x5, flags: 0x0},
	641:  {region: 0x7c, script: 0x5b, flags: 0x0},
	642:  {region: 0x166, script: 0x5b, flags: 0x0},
	643:  {region: 0x166, script: 0x5b, flags: 0x0},
	644:  {region: 0x166, script: 0x5b, flags: 0x0},
	645:  {region: 0x166, script: 0x2c, flags: 0x0},
	646:  {region: 0x124, script: 0xee, flags: 0x0},
	647:  {region: 0xe9, script: 0x5, flags: 0x0},
	648:  {region: 0x166, script: 0x5b, flags: 0x0},
	649:  {region: 0x166, script: 0x5b, flags: 0x0},
	650:  {region: 0x1c, script: 0x5, flags: 0x1},
	651:  {region: 0x166, script: 0x5b, flags: 0x0},
	652:  {region: 0x166, script: 0x5b, flags: 0x0},
	653:  {region: 0x166, script: 0x5b, flags: 0x0},
	654:  {region: 0x139, script: 0x5b, flags: 0x0},
	655:  {region: 0x88, script: 0x5f, flags: 0x0},
	656:  {region: 0x98, script: 0x3e, flags: 0x0},
	657:  {region: 0x130, script: 0x5b, flags: 0x0},
	658:  {region: 0xe9, script: 0x5, flags: 0x0},
	659:  {region: 0x132, script: 0x5b, flags: 0x0},
	660:  {region: 0x166, script: 0x5b, flags: 0x0},
	661:  {region: 0xb8, script: 0x5b, flags: 0x0},
	662:  {region: 0x107, script: 0x20, flags: 0x0},
	663:  {region: 0x166, script: 0x5b, flags: 0x0},
	664:  {region: 0x96, script: 0x5b, flags: 0x0},
	665:  {region: 0x166, script: 0x5b, flags: 0x0},
	666:  {region: 0x53, script: 0xee, flags: 0x0},
	667:  {region: 0x166, script: 0x5b, flags: 0x0},
	668:  {region: 0x166, script: 0x5b, flags: 0x0},
	669:  {region: 0x166, script: 0x5b, flags: 0x0},
	670:  {region: 0x166, script: 0x5b, flags: 0x0},
	671:  {region: 0x9a, script: 0x5d, flags: 0x0},
	672:  {region: 0x166, script: 0x5b, flags: 0x0},
	673:  {region: 0x166, script: 0x5b, flags: 0x0},
	674:  {region: 0x107, script: 0x20, flags: 0x0},
	675:  {region: 0x132, script: 0x5b, flags: 0x0},
	676:  {region: 0x166, script: 0x5b, flags: 0x0},
	677:  {region: 0xda, script: 0x5b, flags: 0x0},
	678:  {region: 0x166, script: 0x5b, flags: 0x0},
	679:  {region: 0x166, script: 0x5b, flags: 0x0},
	680:  {region: 0x21, script: 0x2, flags: 0x1},
	681:  {region: 0x166, script: 0x5b, flags: 0x0},
	682:  {region: 0x166, script: 0x5b, flags: 0x0},
	683:  {region: 0x9f, script: 0x5b, flags: 0x0},
	684:  {region: 0x53, script: 0x61, flags: 0x0},
	685:  {region: 0x96, script: 0x5b, flags: 0x0},
	686:  {region: 0x9d, script: 0x5, flags: 0x0},
	687:  {region: 0x136, script: 0x5b, flags: 0x0},
	688:  {region: 0x166, script: 0x5b, flags: 0x0},
	689:  {region: 0x166, script: 0x5b, flags: 0x0},
	690:  {region: 0x9a, script: 0xe9, flags: 0x0},
	691:  {region: 0x9f, script: 0x5b, flags: 0x0},
	692:  {region: 0x166, script: 0x5b, flags: 0x0},
	693:  {region: 0x4b, script: 0x5b, flags: 0x0},
	694:  {region: 0x166, script: 0x5b, flags: 0x0},
	695:  {region: 0x166, script: 0x5b, flags: 0x0},
	696:  {region: 0xb0, script: 0x58, flags: 0x0},
	697:  {region: 0x166, script: 0x5b, flags: 0x0},
	698:  {region: 0x166, script: 0x5b, flags: 0x0},
	699:  {region: 0x4b, script: 0x5b, flags: 0x0},
	700:  {region: 0x166, script: 0x5b, flags: 0x0},
	701:  {region: 0x166, script: 0x5b, flags: 0x0},
	702:  {region: 0x163, script: 0x5b, flags: 0x0},
	703:  {region: 0x9d, script: 0x5, flags: 0x0},
	704:  {region: 0xb7, script: 0x5b, flags: 0x0},
	705:  {region: 0xb9, script: 0x5b, flags: 0x0},
	706:  {region: 0x4b, script: 0x5b, flags: 0x0},
	707:  {region: 0x4b, script: 0x5b, flags: 0x0},
	708:  {region: 0xa5, script: 0x5b, flags: 0x0},
	709:  {region: 0xa5, script: 0x5b, flags: 0x0},
	710:  {region: 0x9d, script: 0x5, flags: 0x0},
	711:  {region: 0xb9, script: 0x5b, flags: 0x0},
	712:  {region: 0x124, script: 0xee, flags: 0x0},
	713:  {region: 0x53, script: 0x3b, flags: 0x0},
	714:  {region: 0x12c, script: 0x5b, flags: 0x0},
	715:  {region: 0x96, script: 0x5b, flags: 0x0},
	716:  {region: 0x52, script: 0x5b, flags: 0x0},
	717:  {region: 0x9a, script: 0x22, flags: 0x0},
	718:  {region: 0x9a, script: 0x22, flags: 0x0},
	719:  {region: 0x96, script: 0x5b, flags: 0x0},
	720:  {region: 0x23, script: 0x3, flags: 0x1},
	721:  {region: 0xa5, script: 0x5b, flags: 0x0},
	722:  {region: 0x166, script: 0x5b, flags: 0x0},
	723:  {region: 0xd0, script: 0x5b, flags: 0x0},
	724:  {region: 0x166, script: 0x5b, flags: 0x0},
	725:  {region: 0x166, script: 0x5b, flags: 0x0},
	726:  {region: 0x166, script: 0x5b, flags: 0x0},
	727:  {region: 0x166, script: 0x5b, flags: 0x0},
	728:  {region: 0x166, script: 0x5b, flags: 0x0},
	729:  {region: 0x166, script: 0x5b, flags: 0x0},
	730:  {region: 0x166, script: 0x5b, flags: 0x0},
	731:  {region: 0x166, script: 0x5b, flags: 0x0},
	732:  {region: 0x166, script: 0x5b, flags: 0x0},
	733:  {region: 0x166, script: 0x5b, flags: 0x0},
	734:  {region: 0x166, script: 0x5b, flags: 0x0},
	735:  {region: 0x166, script: 0x5, flags: 0x0},
	736:  {region: 0x107, script: 0x20, flags: 0x0},
	737:  {region: 0xe8, script: 0x5b, flags: 0x0},
	738:  {region: 0x166, script: 0x5b, flags: 0x0},
	739:  {region: 0x96, script: 0x5b, flags: 0x0},
	740:  {region: 0x166, script: 0x2c, flags: 0x0},
	741:  {region: 0x166, script: 0x5b, flags: 0x0},
	742:  {region: 0x166, script: 0x5b, flags: 0x0},
	743:  {region: 0x166, script: 0x5b, flags: 0x0},
	744:  {region: 0x113, script: 0x5b, flags: 0x0},
	745:  {region: 0xa5, script: 0x5b, flags: 0x0},
	746:  {region: 0x166, script: 0x5b, flags: 0x0},
	747:  {region: 0x166, script: 0x5b, flags: 0x0},
	748:  {region: 0x124, script: 0x5, flags: 0x0},
	749:  {region: 0xcd, script: 0x5b, flags: 0x0},
	750:  {region: 0x166, script: 0x5b, flags: 0x0},
	751:  {region: 0x166, script: 0x5b, flags: 0x0},
	752:  {region: 0x166, script: 0x5b, flags: 0x0},
	753:  {region: 0xc0, script: 0x5b, flags: 0x0},
	754:  {region: 0xd2, script: 0x5b, flags: 0x0},
	755:  {region: 0x166, script: 0x5b, flags: 0x0},
	756:  {region: 0x52, script: 0x5b, flags: 0x0},
	757:  {region: 0xdc, script: 0x22, flags: 0x0},
	758:  {region: 0x130, script: 0x5b, flags: 0x0},
	759:  {region: 0xc1, script: 0x5b, flags: 0x0},
	760:  {region: 0x166, script: 0x5b, flags: 0x0},
	761:  {region: 0x166, script: 0x5b, flags: 0x0},
	762:  {region: 0xe1, script: 0x5b, flags: 0x0},
	763:  {region: 0x166, script: 0x5b, flags: 0x0},
	764:  {region: 0x96, script: 0x5b, flags: 0x0},
	765:  {region: 0x9c, script: 0x3d, flags: 0x0},
	766:  {region: 0x166, script: 0x5b, flags: 0x0},
	767:  {region: 0xc3, script: 0x20, flags: 0x0},
	768:  {region: 0x166, script: 0x5, flags: 0x0},
	769:  {region: 0x166, script: 0x5b, flags: 0x0},
	770:  {region: 0x166, script: 0x5b, flags: 0x0},
	771:  {region: 0x166, script: 0x5b, flags: 0x0},
	772:  {region: 0x9a, script: 0x6f, flags: 0x0},
	773:  {region: 0x166, script: 0x5b, flags: 0x0},
	774:  {region: 0x166, script: 0x5b, flags: 0x0},
	775:  {region: 0x10c, script: 0x5b, flags: 0x0},
	776:  {region: 0x166, script: 0x5b, flags: 0x0},
	777:  {region: 0x166, script: 0x5b, flags: 0x0},
	778:  {region: 0x166, script: 0x5b, flags: 0x0},
	779:  {region: 0x26, script: 0x3, flags: 0x1},
	780:  {region: 0x166, script: 0x5b, flags: 0x0},
	781:  {region: 0x166, script: 0x5b, flags: 0x0},
	782:  {region: 0x9a, script: 0xe, flags: 0x0},
	783:  {region: 0xc5, script: 0x76, flags: 0x0},
	785:  {region: 0x166, script: 0x5b, flags: 0x0},
	786:  {region: 0x49, script: 0x5b, flags: 0x0},
	787:  {region: 0x49, script: 0x5b, flags: 0x0},
	788:  {region: 0x37, script: 0x5b, flags: 0x0},
	789:  {region: 0x166, script: 0x5b, flags: 0x0},
	790:  {region: 0x166, script: 0x5b, flags: 0x0},
	791:  {region: 0x166, script: 0x5b, flags: 0x0},
	792:  {region: 0x166, script: 0x5b, flags: 0x0},
	793:  {region: 0x166, script: 0x5b, flags: 0x0},
	794:  {region: 0x166, script: 0x5b, flags: 0x0},
	795:  {region: 0x9a, script: 0x22, flags: 0x0},
	796:  {region: 0xdc, script: 0x22, flags: 0x0},
	797:  {region: 0x107, script: 0x20, flags: 0x0},
	798:  {region: 0x35, script: 0x73, flags: 0x0},
	799:  {region: 0x29, script: 0x3, flags: 0x1},
	800:  {region: 0xcc, script: 0x5b, flags: 0x0},
	801:  {region: 0x166, script: 0x5b, flags: 0x0},
	802:  {region: 0x166, script: 0x5b, flags: 0x0},
	803:  {region: 0x166, script: 0x5b, flags: 0x0},
	804:  {region: 0x9a, script: 0x22, flags: 0x0},
	805:  {region: 0x52, script: 0x5b, flags: 0x0},
	807:  {region: 0x166, script: 0x5b, flags: 0x0},
	808:  {region: 0x136, script: 0x5b, flags: 0x0},
	809:  {region: 0x166, script: 0x5b, flags: 0x0},
	810:  {region: 0x166, script: 0x5b, flags: 0x0},
	811:  {region: 0xe9, script: 0x5, flags: 0x0},
	812:  {region: 0xc4, script: 0x5b, flags: 0x0},
	813:  {region: 0x9a, script: 0x22, flags: 0x0},
	814:  {region: 0x96, script: 0x5b, flags: 0x0},
	815:  {region: 0x165, script: 0x5b, flags: 0x0},
	816:  {region: 0x166, script: 0x5b, flags: 0x0},
	817:  {region: 0xc5, script: 0x76, flags: 0x0},
	818:  {region: 0x166, script: 0x5b, flags: 0x0},
	819:  {region: 0x166, script: 0x2c, flags: 0x0},
	820:  {region: 0x107, script: 0x20, flags: 0x0},
	821:  {region: 0x166, script: 0x5b, flags: 0x0},
	822:  {region: 0x132, script: 0x5b, flags: 0x0},
	823:  {region: 0x9d, script: 0x67, flags: 0x0},
	824:  {region: 0x166, script: 0x5b, flags: 0x0},
	825:  {region: 0x166, script: 0x5b, flags: 0x0},
	826:  {region: 0x9d, script: 0x5, flags: 0x0},
	827:  {region: 0x166, script: 0x5b, flags: 0x0},
	828:  {region: 0x166, script: 0x5b, flags: 0x0},
	829:  {region: 0x166, script: 0x5b, flags: 0x0},
	830:  {region: 0xde, script: 0x5b, flags: 0x0},
	831:  {region: 0x166, script: 0x5b, flags: 0x0},
	832:  {region: 0x166, script: 0x5b, flags: 0x0},
	834:  {region: 0x166, script: 0x5b, flags: 0x0},
	835:  {region: 0x53, script: 0x3b, flags: 0x0},
	836:  {region: 0x9f, script: 0x5b, flags: 0x0},
	837:  {region: 0xd3, script: 0x5b, flags: 0x0},
	838:  {region: 0x166, script: 0x5b, flags: 0x0},
	839:  {region: 0xdb, script: 0x5b, flags: 0x0},
	840:  {region: 0x166, script: 0x5b, flags: 0x0},
	841:  {region: 0x166, script: 0x5b, flags: 0x0},
	842:  {region: 0x166, script: 0x5b, flags: 0x0},
	843:  {region: 0xd0, script: 0x5b, flags: 0x0},
	844:  {region: 0x166, script: 0x5b, flags: 0x0},
	845:  {region: 0x166, script: 0x5b, flags: 0x0},
	846:  {region: 0x165, script: 0x5b, flags: 0x0},
	847:  {region: 0xd2, script: 0x5b, flags: 0x0},
	848:  {region: 0x61, script: 0x5b, flags: 0x0},
	849:  {region: 0xdc, script: 0x22, flags: 0x0},
	850:  {region: 0x166, script: 0x5b, flags: 0x0},
	851:  {region: 0xdc, script: 0x22, flags: 0x0},
	852:  {region: 0x166, script: 0x5b, flags: 0x0},
	853:  {region: 0x166, script: 0x5b, flags: 0x0},
	854:  {region: 0xd3, script: 0x5b, flags: 0x0},
	855:  {region: 0x166, script: 0x5b, flags: 0x0},
	856:  {region: 0x166, script: 0x5b, flags: 0x0},
	857:  {region: 0xd2, script: 0x5b, flags: 0x0},
	858:  {region: 0x166, script: 0x5b, flags: 0x0},
	859:  {region: 0xd0, script: 0x5b, flags: 0x0},
	860:  {region: 0xd0, script: 0x5b, flags: 0x0},
	861:  {region: 0x166, script: 0x5b, flags: 0x0},
	862:  {region: 0x166, script: 0x5b, flags: 0x0},
	863:  {region: 0x96, script: 0x5b, flags: 0x0},
	864:  {region: 0x166, script: 0x5b, flags: 0x0},
	865:  {region: 0xe0, script: 0x5b, flags: 0x0},
	866:  {region: 0x166, script: 0x5b, flags: 0x0},
	867:  {region: 0x166, script: 0x5b, flags: 0x0},
	868:  {region: 0x9a, script: 0x5b, flags: 0x0},
	869:  {region: 0x166, script: 0x5b, flags: 0x0},
	870:  {region: 0x166, script: 0x5b, flags: 0x0},
	871:  {region: 0xda, script: 0x5b, flags: 0x0},
	872:  {region: 0x52, script: 0x5b, flags: 0x0},
	873:  {region: 0x166, script: 0x5b, flags: 0x0},
	874:  {region: 0xdb, script: 0x5b, flags: 0x0},
	875:  {region: 0x166, script: 0x5b, flags: 0x0},
	876:  {region: 0x52, script: 0x5b, flags: 0x0},
	877:  {region: 0x166, script: 0x5b, flags: 0x0},
	878:  {region: 0x166, script: 0x5b, flags: 0x0},
	879:  {region: 0xdb, script: 0x5b, flags: 0x0},
	880:  {region: 0x124, script: 0x57, flags: 0x0},
	881:  {region: 0x9a, script: 0x22, flags: 0x0},
	882:  {region: 0x10d, script: 0xcb, flags: 0x0},
	883:  {region: 0x166, script: 0x5b, flags: 0x0},
	884:  {region: 0x166, script: 0x5b, flags: 0x0},
	885:  {region: 0x85, script: 0x7e, flags: 0x0},
	886:  {region: 0x162, script: 0x5b, flags: 0x0},
	887:  {region: 0x166, script: 0x5b, flags: 0x0},
	888:  {region: 0x49, script: 0x17, flags: 0x0},
	889:  {region: 0x166, script: 0x5b, flags: 0x0},
	890:  {region: 0x162, script: 0x5b, flags: 0x0},
	891:  {region: 0x166, script: 0x5b, flags: 0x0},
	892:  {region: 0x166, script: 0x5b, flags: 0x0},
	893:  {region: 0x166, script: 0x5b, flags: 0x0},
	894:  {region: 0x166, script: 0x5b, flags: 0x0},
	895:  {region: 0x166, script: 0x5b, flags: 0x0},
	896:  {region: 0x118, script: 0x5b, flags: 0x0},
	897:  {region: 0x166, script: 0x5b, flags: 0x0},
	898:  {region: 0x166, script: 0x5b, flags: 0x0},
	899:  {region: 0x136, script: 0x5b, flags: 0x0},
	900:  {region: 0x166, script: 0x5b, flags: 0x0},
	901:  {region: 0x53, script: 0x5b, flags: 0x0},
	902:  {region: 0x166, script: 0x5b, flags: 0x0},
	903:  {region: 0xcf, script: 0x5b, flags: 0x0},
	904:  {region: 0x130, script: 0x5b, flags: 0x0},
	905:  {region: 0x132, script: 0x5b, flags: 0x0},
	906:  {region: 0x81, script: 0x5b, flags: 0x0},
	907:  {region: 0x79, script: 0x5b, flags: 0x0},
	908:  {region: 0x166, script: 0x5b, flags: 0x0},
	910:  {region: 0x166, script: 0x5b, flags: 0x0},
	911:  {region: 0x166, script: 0x5b, flags: 0x0},
	912:  {region: 0x70, script: 0x5b, flags: 0x0},
	913:  {region: 0x166, script: 0x5b, flags: 0x0},
	914:  {region: 0x166, script: 0x5b, flags: 0x0},
	915:  {region: 0x166, script: 0x5b, flags: 0x0},
	916:  {region: 0x166, script: 0x5b, flags: 0x0},
	917:  {region: 0x9a, script: 0x83, flags: 0x0},
	918:  {region: 0x166, script: 0x5b, flags: 0x0},
	919:  {region: 0x166, script: 0x5, flags: 0x0},
	920:  {region: 0x7e, script: 0x20, flags: 0x0},
	921:  {region: 0x136, script: 0x84, flags: 0x0},
	922:  {region: 0x166, script: 0x5, flags: 0x0},
	923:  {region: 0xc6, script: 0x82, flags: 0x0},
	924:  {region: 0x166, script: 0x5b, flags: 0x0},
	925:  {region: 0x2c, script: 0x3, flags: 0x1},
	926:  {region: 0xe8, script: 0x5b, flags: 0x0},
	927:  {region: 0x2f, script: 0x2, flags: 0x1},
	928:  {region: 0xe8, script: 0x5b, flags: 0x0},
	929:  {region: 0x30, script: 0x5b, flags: 0x0},
	930:  {region: 0xf1, script: 0x5b, flags: 0x0},
	931:  {region: 0x166, script: 0x5b, flags: 0x0},
	932:  {region: 0x79, script: 0x5b, flags: 0x0},
	933:  {region: 0xd7, script: 0x5b, flags: 0x0},
	934:  {region: 0x136, script: 0x5b, flags: 0x0},
	935:  {region: 0x49, script: 0x5b, flags: 0x0},
	936:  {region: 0x166, script: 0x5b, flags: 0x0},
	937:  {region: 0x9d, script: 0xfa, flags: 0x0},
	938:  {region: 0x166, script: 0x5b, flags: 0x0},
	939:  {region: 0x61, script: 0x5b, flags: 0x0},
	940:  {region: 0x166, script: 0x5, flags: 0x0},
	941:  {region: 0xb1, script: 0x90, flags: 0x0},
	943:  {region: 0x166, script: 0x5b, flags: 0x0},
	944:  {region: 0x166, script: 0x5b, flags: 0x0},
	945:  {region: 0x9a, script: 0x12, flags: 0x0},
	946:  {region: 0xa5, script: 0x5b, flags: 0x0},
	947:  {region: 0xea, script: 0x5b, flags: 0x0},
	948:  {region: 0x166, script: 0x5b, flags: 0x0},
	949:  {region: 0x9f, script: 0x5b, flags: 0x0},
	950:  {region: 0x166, script: 0x5b, flags: 0x0},
	951:  {region: 0x166, script: 0x5b, flags: 0x0},
	952:  {region: 0x88, script: 0x34, flags: 0x0},
	953:  {region: 0x76, script: 0x5b, flags: 0x0},
	954:  {region: 0x166, script: 0x5b, flags: 0x0},
	955:  {region: 0xe9, script: 0x4e, flags: 0x0},
	956:  {region: 0x9d, script: 0x5, flags: 0x0},
	957:  {region: 0x1, script: 0x5b, flags: 0x0},
	958:  {region: 0x24, script: 0x5, flags: 0x0},
	959:  {region: 0x166, script: 0x5b, flags: 0x0},
	960:  {region: 0x41, script: 0x5b, flags: 0x0},
	961:  {region: 0x166, script: 0x5b, flags: 0x0},
	962:  {region: 0x7b, script: 0x5b, flags: 0x0},
	963:  {region: 0x166, script: 0x5b, flags: 0x0},
	964:  {region: 0xe5, script: 0x5b, flags: 0x0},
	965:  {region: 0x8a, script: 0x5b, flags: 0x0},
	966:  {region: 0x6a, script: 0x5b, flags: 0x0},
	967:  {region: 0x166, script: 0x5b, flags: 0x0},
	968:  {region: 0x9a, script: 0x22, flags: 0x0},
	969:  {region: 0x166, script: 0x5b, flags: 0x0},
	970:  {region: 0x103, script: 0x5b, flags: 0x0},
	971:  {region: 0x96, script: 0x5b, flags: 0x0},
	972:  {region: 0x166, script: 0x5b, flags: 0x0},
	973:  {region: 0x166, script: 0x5b, flags: 0x0},
	974:  {region: 0x9f, script: 0x5b, flags: 0x0},
	975:  {region: 0x166, script: 0x5, flags: 0x0},
	976:  {region: 0x9a, script: 0x5b, flags: 0x0},
	977:  {region: 0x31, script: 0x2, flags: 0x1},
	978:  {region: 0xdc, script: 0x22, flags: 0x0},
	979:  {region: 0x35, script: 0xe, flags: 0x0},
	980:  {region: 0x4e, script: 0x5b, flags: 0x0},
	981:  {region: 0x73, script: 0x5b, flags: 0x0},
	982:  {region: 0x4e, script: 0x5b, flags: 0x0},
	983:  {region: 0x9d, script: 0x5, flags: 0x0},
	984:  {region: 0x10d, script: 0x5b, flags: 0x0},
	985:  {region: 0x3a, script: 0x5b, flags: 0x0},
	986:  {region: 0x166, script: 0x5b, flags: 0x0},
	987:  {region: 0xd2, script: 0x5b, flags: 0x0},
	988:  {region: 0x105, script: 0x5b, flags: 0x0},
	989:  {region: 0x96, script: 0x5b, flags: 0x0},
	990:  {region: 0x130, script: 0x5b, flags: 0x0},
	991:  {region: 0x166, script: 0x5b, flags: 0x0},
	992:  {region: 0x166, script: 0x5b, flags: 0x0},
	993:  {region: 0x74, script: 0x5b, flags: 0x0},
	994:  {region: 0x107, script: 0x20, flags: 0x0},
	995:  {region: 0x131, script: 0x20, flags: 0x0},
	996:  {region: 0x10a, script: 0x5b, flags: 0x0},
	997:  {region: 0x108, script: 0x5b, flags: 0x0},
	998:  {region: 0x130, script: 0x5b, flags: 0x0},
	999:  {region: 0x166, script: 0x5b, flags: 0x0},
	1000: {region: 0xa3, script: 0x4c, flags: 0x0},
	1001: {region: 0x9a, script: 0x22, flags: 0x0},
	1002: {region: 0x81, script: 0x5b, flags: 0x0},
	1003: {region: 0x107, script: 0x20, flags: 0x0},
	1004: {region: 0xa5, script: 0x5b, flags: 0x0},
	1005: {region: 0x96, script: 0x5b, flags: 0x0},
	1006: {region: 0x9a, script: 0x5b, flags: 0x0},
	1007: {region: 0x115, script: 0x5b, flags: 0x0},
	1008: {region: 0x9a, script: 0xcf, flags: 0x0},
	1009: {region: 0x166, script: 0x5b, flags: 0x0},
	1010: {region: 0x166, script: 0x5b, flags: 0x0},
	1011: {region: 0x130, script: 0x5b, flags: 0x0},
	1012: {region: 0x9f, script: 0x5b, flags: 0x0},
	1013: {region: 0x9a, script: 0x22, flags: 0x0},
	1014: {region: 0x166, script: 0x5, flags: 0x0},
	1015: {region: 0x9f, script: 0x5b, flags: 0x0},
	1016: {region: 0x7c, script: 0x5b, flags: 0x0},
	1017: {region: 0x49, script: 0x5b, flags: 0x0},
	1018: {region: 0x33, script: 0x4, flags: 0x1},
	1019: {region: 0x9f, script: 0x5b, flags: 0x0},
	1020: {region: 0x9d, script: 0x5, flags: 0x0},
	1021: {region: 0xdb, script: 0x5b, flags: 0x0},
	1022: {region: 0x4f, script: 0x5b, flags: 0x0},
	1023: {region: 0xd2, script: 0x5b, flags: 0x0},
	1024: {region: 0xd0, script: 0x5b, flags: 0x0},
	1025: {region: 0xc4, script: 0x5b, flags: 0x0},
	1026: {region: 0x4c, script: 0x5b, flags: 0x0},
	1027: {region: 0x97, script: 0x80, flags: 0x0},
	1028: {region: 0xb7, script: 0x5b, flags: 0x0},
	1029: {region: 0x166, script: 0x2c, flags: 0x0},
	1030: {region: 0x166, script: 0x5b, flags: 0x0},
	1032: {region: 0xbb, script: 0xeb, flags: 0x0},
	1033: {region: 0x166, script: 0x5b, flags: 0x0},
	1034: {region: 0xc5, script: 0x76, flags: 0x0},
	1035: {region: 0x166, script: 0x5, flags: 0x0},
	1036: {region: 0xb4, script: 0xd6, flags: 0x0},
	1037: {region: 0x70, script: 0x5b, flags: 0x0},
	1038: {region: 0x166, script: 0x5b, flags: 0x0},
	1039: {region: 0x166, script: 0x5b, flags: 0x0},
	1040: {region: 0x166, script: 0x5b, flags: 0x0},
	1041: {region: 0x166, script: 0x5b, flags: 0x0},
	1042: {region: 0x112, script: 0x5b, flags: 0x0},
	1043: {region: 0x166, script: 0x5b, flags: 0x0},
	1044: {region: 0xe9, script: 0x5, flags: 0x0},
	1045: {region: 0x166, script: 0x5b, flags: 0x0},
	1046: {region: 0x110, script: 0x5b, flags: 0x0},
	1047: {region: 0x166, script: 0x5b, flags: 0x0},
	1048: {region: 0xea, script: 0x5b, flags: 0x0},
	1049: {region: 0x166, script: 0x5b, flags: 0x0},
	1050: {region: 0x96, script: 0x5b, flags: 0x0},
	1051: {region: 0x143, script: 0x5b, flags: 0x0},
	1052: {region: 0x10d, script: 0x5b, flags: 0x0},
	1054: {region: 0x10d, script: 0x5b, flags: 0x0},
	1055: {region: 0x73, script: 0x5b, flags: 0x0},
	1056: {region: 0x98, script: 0xcc, flags: 0x0},
	1057: {region: 0x166, script: 0x5b, flags: 0x0},
	1058: {region: 0x73, script: 0x5b, flags: 0x0},
	1059: {region: 0x165, script: 0x5b, flags: 0x0},
	1060: {region: 0x166, script: 0x5b, flags: 0x0},
	1061: {region: 0xc4, script: 0x5b, flags: 0x0},
	1062: {region: 0x166, script: 0x5b, flags: 0x0},
	1063: {region: 0x166, script: 0x5b, flags: 0x0},
	1064: {region: 0x166, script: 0x5b, flags: 0x0},
	1065: {region: 0x116, script: 0x5b, flags: 0x0},
	1066: {region: 0x166, script: 0x5b, flags: 0x0},
	1067: {region: 0x166, script: 0x5b, flags: 0x0},
	1068: {region: 0x124, script: 0xee, flags: 0x0},
	1069: {region: 0x166, script: 0x5b, flags: 0x0},
	1070: {region: 0x166, script: 0x5b, flags: 0x0},
	1071: {region: 0x166, script: 0x5b, flags: 0x0},
	1072: {region: 0x166, script: 0x5b, flags: 0x0},
	1073: {region: 0x27, script: 0x5b, flags: 0x0},
	1074: {region: 0x37, script: 0x5, flags: 0x1},
	1075: {region: 0x9a, script: 0xd9, flags: 0x0},
	1076: {region: 0x117, script: 0x5b, flags: 0x0},
	1077: {region: 0x115, script: 0x5b, flags: 0x0},
	1078: {region: 0x9a, script: 0x22, flags: 0x0},
	1079: {region: 0x162, script: 0x5b, flags: 0x0},
	1080: {region: 0x166, script: 0x5b, flags: 0x0},
	1081: {region: 0x166, script: 0x5b, flags: 0x0},
	1082: {region: 0x6e, script: 0x5b, flags: 0x0},
	1083: {region: 0x162, script: 0x5b, flags: 0x0},
	1084: {region: 0x166, script: 0x5b, flags: 0x0},
	1085: {region: 0x61, script: 0x5b, flags: 0x0},
	1086: {region: 0x96, script: 0x5b, flags: 0x0},
	1087: {region: 0x166, script: 0x5b, flags: 0x0},
	1088: {region: 0x166, script: 0x5b, flags: 0x0},
	1089: {region: 0x130, script: 0x5b, flags: 0x0},
	1090: {region: 0x166, script: 0x5b, flags: 0x0},
	1091: {region: 0x85, script: 0x5b, flags: 0x0},
	1092: {region: 0x10d, script: 0x5b, flags: 0x0},
	1093: {region: 0x130, script: 0x5b, flags: 0x0},
	1094: {region: 0x160, script: 0x5, flags: 0x0},
	1095: {region: 0x4b, script: 0x5b, flags: 0x0},
	1096: {region: 0x61, script: 0x5b, flags: 0x0},
	1097: {region: 0x166, script: 0x5b, flags: 0x0},
	1098: {region: 0x9a, script: 0x22, flags: 0x0},
	1099: {region: 0x96, script: 0x5b, flags: 0x0},
	1100: {region: 0x166, script: 0x5b, flags: 0x0},
	1101: {region: 0x35, script: 0xe, flags: 0x0},
	1102: {region: 0x9c, script: 0xde, flags: 0x0},
	1103: {region: 0xea, script: 0x5b, flags: 0x0},
	1104: {region: 0x9a, script: 0xe6, flags: 0x0},
	1105: {region: 0xdc, script: 0x22, flags: 0x0},
	1106: {region: 0x166, script: 0x5b, flags: 0x0},
	1107: {region: 0x166, script: 0x5b, flags: 0x0},
	1108: {region: 0x166, script: 0x5b, flags: 0x0},
	1109: {region: 0x166, script: 0x5b, flags: 0x0},
	1110: {region: 0x166, script: 0x5b, flags: 0x0},
	1111: {region: 0x166, script: 0x5b, flags: 0x0},
	1112: {region: 0x166, script: 0x5b, flags: 0x0},
	1113: {region: 0x166, script: 0x5b, flags: 0x0},
	1114: {region: 0xe8, script: 0x5b, flags: 0x0},
	1115: {region: 0x166, script: 0x5b, flags: 0x0},
	1116: {region: 0x166, script: 0x5b, flags: 0x0},
	1117: {region: 0x9a, script: 0x53, flags: 0x0},
	1118: {region: 0x53, script: 0xe4, flags: 0x0},
	1119: {region: 0xdc, script: 0x22, flags: 0x0},
	1120: {region: 0xdc, script: 0x22, flags: 0x0},
	1121: {region: 0x9a, script: 0xe9, flags: 0x0},
	1122: {region: 0x166, script: 0x5b, flags: 0x0},
	1123: {region: 0x113, script: 0x5b, flags: 0x0},
	1124: {region: 0x132, script: 0x5b, flags: 0x0},
	1125: {region: 0x127, script: 0x5b, flags: 0x0},
	1126: {region: 0x166, script: 0x5b, flags: 0x0},
	1127: {region: 0x3c, script: 0x3, flags: 0x1},
	1128: {region: 0x166, script: 0x5b, flags: 0x0},
	1129: {region: 0x166, script: 0x5b, flags: 0x0},
	1130: {region: 0x166, script: 0x5b, flags: 0x0},
	1131: {region: 0x124, script: 0xee, flags: 0x0},
	1132: {region: 0xdc, script: 0x22, flags: 0x0},
	1133: {region: 0xdc, script: 0x22, flags: 0x0},
	1134: {region: 0xdc, script: 0x22, flags: 0x0},
	1135: {region: 0x70, script: 0x2c, flags: 0x0},
	1136: {region: 0x166, script: 0x5b, flags: 0x0},
	1137: {region: 0x6e, script: 0x2c, flags: 0x0},
	1138: {region: 0x166, script: 0x5b, flags: 0x0},
	1139: {region: 0x166, script: 0x5b, flags: 0x0},
	1140: {region: 0x166, script: 0x5b, flags: 0x0},
	1141: {region: 0xd7, script: 0x5b, flags: 0x0},
	1142: {region: 0x128, script: 0x5b, flags: 0x0},
	1143: {region: 0x126, script: 0x5b, flags: 0x0},
	1144: {region: 0x32, script: 0x5b, flags: 0x0},
	1145: {region: 0xdc, script: 0x22, flags: 0x0},
	1146: {region: 0xe8, script: 0x5b, flags: 0x0},
	1147: {region: 0x166, script: 0x5b, flags: 0x0},
	1148: {region: 0x166, script: 0x5b, flags: 0x0},
	1149: {region: 0x32, script: 0x5b, flags: 0x0},
	1150: {region: 0xd5, script: 0x5b, flags: 0x0},
	1151: {region: 0x166, script: 0x5b, flags: 0x0},
	1152: {region: 0x162, script: 0x5b, flags: 0x0},
	1153: {region: 0x166, script: 0x5b, flags: 0x0},
	1154: {region: 0x12a, script: 0x5b, flags: 0x0},
	1155: {region: 0x166, script: 0x5b, flags: 0x0},
	1156: {region: 0xcf, script: 0x5b, flags: 0x0},
	1157: {region: 0x166, script: 0x5b, flags: 0x0},
	1158: {region: 0xe7, script: 0x5b, flags: 0x0},
	1159: {region: 0x166, script: 0x5b, flags: 0x0},
	1160: {region: 0x166, script: 0x5b, flags: 0x0},
	1161: {region: 0x166, script: 0x5b, flags: 0x0},
	1162: {region: 0x12c, script: 0x5b, flags: 0x0},
	1163: {region: 0x12c, script: 0x5b, flags: 0x0},
	1164: {region: 0x12f, script: 0x5b, flags: 0x0},
	1165: {region: 0x166, script: 0x5, flags: 0x0},
	1166: {region: 0x162, script: 0x5b, flags: 0x0},
	1167: {region: 0x88, script: 0x34, flags: 0x0},
	1168: {region: 0xdc, script: 0x22, flags: 0x0},
	1169: {region: 0xe8, script: 0x5b, flags: 0x0},
	1170: {region: 0x43, script: 0xef, flags: 0x0},
	1171: {region: 0x166, script: 0x5b, flags: 0x0},
	1172: {region: 0x107, script: 0x20, flags: 0x0},
	1173: {region: 0x166, script: 0x5b, flags: 0x0},
	1174: {region: 0x166, script: 0x5b, flags: 0x0},
	1175: {region: 0x132, script: 0x5b, flags: 0x0},
	1176: {region: 0x166, script: 0x5b, flags: 0x0},
	1177: {region: 0x124, script: 0xee, flags: 0x0},
	1178: {region: 0x32, script: 0x5b, flags: 0x0},
	1179: {region: 0x166, script: 0x5b, flags: 0x0},
	1180: {region: 0x166, script: 0x5b, flags: 0x0},
	1181: {region: 0xcf, script: 0x5b, flags: 0x0},
	1182: {region: 0x166, script: 0x5b, flags: 0x0},
	1183: {region: 0x166, script: 0x5b, flags: 0x0},
	1184: {region: 0x12e, script: 0x5b, flags: 0x0},
	1185: {region: 0x166, script: 0x5b, flags: 0x0},
	1187: {region: 0x166, script: 0x5b, flags: 0x0},
	1188: {region: 0xd5, script: 0x5b, flags: 0x0},
	1189: {region: 0x53, script: 0xe7, flags: 0x0},
	1190: {region: 0xe6, script: 0x5b, flags: 0x0},
	1191: {region: 0x166, script: 0x5b, flags: 0x0},
	1192: {region: 0x107, script: 0x20, flags: 0x0},
	1193: {region: 0xbb, script: 0x5b, flags: 0x0},
	1194: {region: 0x166, script: 0x5b, flags: 0x0},
	1195: {region: 0x107, script: 0x20, flags: 0x0},
	1196: {region: 0x3f, script: 0x4, flags: 0x1},
	1197: {region: 0x11d, script: 0xf3, flags: 0x0},
	1198: {region: 0x131, script: 0x20, flags: 0x0},
	1199: {region: 0x76, script: 0x5b, flags: 0x0},
	1200: {region: 0x2a, script: 0x5b, flags: 0x0},
	1202: {region: 0x43, script: 0x3, flags: 0x1},
	1203: {region: 0x9a, script: 0xe, flags: 0x0},
	1204: {region: 0xe9, script: 0x5, flags: 0x0},
	1205: {region: 0x166, script: 0x5b, flags: 0x0},
	1206: {region: 0x166, script: 0x5b, flags: 0x0},
	1207: {region: 0x166, script: 0x5b, flags: 0x0},
	1208: {region: 0x166, script: 0x5b, flags: 0x0},
	1209: {region: 0x166, script: 0x5b, flags: 0x0},
	1210: {region: 0x166, script: 0x5b, flags: 0x0},
	1211: {region: 0x166, script: 0x5b, flags: 0x0},
	1212: {region: 0x46, script: 0x4, flags: 0x1},
	1213: {region: 0x166, script: 0x5b, flags: 0x0},
	1214: {region: 0xb5, script: 0xf4, flags: 0x0},
	1215: {region: 0x166, script: 0x5b, flags: 0x0},
	1216: {region: 0x162, script: 0x5b, flags: 0x0},
	1217: {region: 0x9f, script: 0x5b, flags: 0x0},
	1218: {region: 0x107, script: 0x5b, flags: 0x0},
	1219: {region: 0x13f, script: 0x5b, flags: 0x0},
	1220: {region: 0x11c, script: 0x5b, flags: 0x0},
	1221: {region: 0x166, script: 0x5b, flags: 0x0},
	1222: {region: 0x36, script: 0x5b, flags: 0x0},
	1223: {region: 0x61, script: 0x5b, flags: 0x0},
	1224: {region: 0xd2, script: 0x5b, flags: 0x0},
	1225: {region: 0x1, script: 0x5b, flags: 0x0},
	1226: {region: 0x107, script: 0x5b, flags: 0x0},
	1227: {region: 0x6b, script: 0x5b, flags: 0x0},
	1228: {region: 0x130, script: 0x5b, flags: 0x0},
	1229: {region: 0x166, script: 0x5b, flags: 0x0},
	1230: {region: 0x36, script: 0x5b, flags: 0x0},
	1231: {region: 0x4e, script: 0x5b, flags: 0x0},
	1232: {region: 0x166, script: 0x5b, flags: 0x0},
	1233: {region: 0x70, script: 0x2c, flags: 0x0},
	1234: {region: 0x166, script: 0x5b, flags: 0x0},
	1235: {region: 0xe8, script: 0x5b, flags: 0x0},
	1236: {region: 0x2f, script: 0x5b, flags: 0x0},
	1237: {region: 0x9a, script: 0xe9, flags: 0x0},
	1238: {region: 0x9a, script: 0x22, flags: 0x0},
	1239: {region: 0x166, script: 0x5b, flags: 0x0},
	1240: {region: 0x166, script: 0x5b, flags: 0x0},
	1241: {region: 0x166, script: 0x5b, flags: 0x0},
	1242: {region: 0x166, script: 0x5b, flags: 0x0},
	1243: {region: 0x166, script: 0x5b, flags: 0x0},
	1244: {region: 0x166, script: 0x5b, flags: 0x0},
	1245: {region: 0x166, script: 0x5b, flags: 0x0},
	1246: {region: 0x166, script: 0x5b, flags: 0x0},
	1247: {region: 0x166, script: 0x5b, flags: 0x0},
	1248: {region: 0x141, script: 0x5b, flags: 0x0},
	1249: {region: 0x166, script: 0x5b, flags: 0x0},
	1250: {region: 0x166, script: 0x5b, flags: 0x0},
	1251: {region: 0xa9, script: 0x5, flags: 0x0},
	1252: {region: 0x166, script: 0x5b, flags: 0x0},
	1253: {region: 0x115, script: 0x5b, flags: 0x0},
	1254: {region: 0x166, script: 0x5b, flags: 0x0},
	1255: {region: 0x166, script: 0x5b, flags: 0x0},
	1256: {region: 0x166, script: 0x5b, flags: 0x0},
	1257: {region: 0x166, script: 0x5b, flags: 0x0},
	1258: {region: 0x9a, script: 0x22, flags: 0x0},
	1259: {region: 0x53, script: 0x3b, flags: 0x0},
	1260: {region: 0x166, script: 0x5b, flags: 0x0},
	1261: {region: 0x166, script: 0x5b, flags: 0x0},
	1262: {region: 0x41, script: 0x5b, flags: 0x0},
	1263: {region: 0x166, script: 0x5b, flags: 0x0},
	1264: {region: 0x12c, script: 0x18, flags: 0x0},
	1265: {region: 0x166, script: 0x5b, flags: 0x0},
	1266: {region: 0x162, script: 0x5b, flags: 0x0},
	1267: {region: 0x166, script: 0x5b, flags: 0x0},
	1268: {region: 0x12c, script: 0x63, flags: 0x0},
	1269: {region: 0x12c, script: 0x64, flags: 0x0},
	1270: {region: 0x7e, script: 0x2e, flags: 0x0},
	1271: {region: 0x53, script: 0x68, flags: 0x0},
	1272: {region: 0x10c, script: 0x6d, flags: 0x0},
	1273: {region: 0x109, script: 0x79, flags: 0x0},
	1274: {region: 0x9a, script: 0x22, flags: 0x0},
	1275: {region: 0x132, script: 0x5b, flags: 0x0},
	1276: {region: 0x166, script: 0x5b, flags: 0x0},
	1277: {region: 0x9d, script: 0x93, flags: 0x0},
	1278: {region: 0x166, script: 0x5b, flags: 0x0},
	1279: {region: 0x15f, script: 0xce, flags: 0x0},
	1280: {region: 0x166, script: 0x5b, flags: 0x0},
	1281: {region: 0x166, script: 0x5b, flags: 0x0},
	1282: {region: 0xdc, script: 0x22, flags: 0x0},
	1283: {region: 0x166, script: 0x5b, flags: 0x0},
	1284: {region: 0x166, script: 0x5b, flags: 0x0},
	1285: {region: 0xd2, script: 0x5b, flags: 0x0},
	1286: {region: 0x76, script: 0x5b, flags: 0x0},
	1287: {region: 0x166, script: 0x5b, flags: 0x0},
	1288: {region: 0x166, script: 0x5b, flags: 0x0},
	1289: {region: 0x52, script: 0x5b, flags: 0x0},
	1290: {region: 0x166, script: 0x5b, flags: 0x0},
	1291: {region: 0x166, script: 0x5b, flags: 0x0},
	1292: {region: 0x166, script: 0x5b, flags: 0x0},
	1293: {region: 0x52, script: 0x5b, flags: 0x0},
	1294: {region: 0x166, script: 0x5b, flags: 0x0},
	1295: {region: 0x166, script: 0x5b, flags: 0x0},
	1296: {region: 0x166, script: 0x5b, flags: 0x0},
	1297: {region: 0x166, script: 0x5b, flags: 0x0},
	1298: {region: 0x1, script: 0x3e, flags: 0x0},
	1299: {region: 0x166, script: 0x5b, flags: 0x0},
	1300: {region: 0x166, script: 0x5b, flags: 0x0},
	1301: {region: 0x166, script: 0x5b, flags: 0x0},
	1302: {region: 0x166, script: 0x5b, flags: 0x0},
	1303: {region: 0x166, script: 0x5b, flags: 0x0},
	1304: {region: 0xd7, script: 0x5b, flags: 0x0},
	1305: {region: 0x166, script: 0x5b, flags: 0x0},
	1306: {region: 0x166, script: 0x5b, flags: 0x0},
	1307: {region: 0x166, script: 0x5b, flags: 0x0},
	1308: {region: 0x41, script: 0x5b, flags: 0x0},
	1309: {region: 0x166, script: 0x5b, flags: 0x0},
	1310: {region: 0xd0, script: 0x5b, flags: 0x0},
	1311: {region: 0x4a, script: 0x3, flags: 0x1},
	1312: {region: 0x166, script: 0x5b, flags: 0x0},
	1313: {region: 0x166, script: 0x5b, flags: 0x0},
	1314: {region: 0x166, script: 0x5b, flags: 0x0},
	1315: {region: 0x53, script: 0x5b, flags: 0x0},
	1316: {region: 0x10c, script: 0x5b, flags: 0x0},
	1318: {region: 0xa9, script: 0x5, flags: 0x0},
	1319: {region: 0xda, script: 0x5b, flags: 0x0},
	1320: {region: 0xbb, script: 0xeb, flags: 0x0},
	1321: {region: 0x4d, script: 0x14, flags: 0x1},
	1322: {region: 0x53, script: 0x7f, flags: 0x0},
	1323: {region: 0x166, script: 0x5b, flags: 0x0},
	1324: {region: 0x123, script: 0x5b, flags: 0x0},
	1325: {region: 0xd1, script: 0x5b, flags: 0x0},
	1326: {region: 0x166, script: 0x5b, flags: 0x0},
	1327: {region: 0x162, script: 0x5b, flags: 0x0},
	1329: {region: 0x12c, script: 0x5b, flags: 0x0},
}

// likelyLangList holds lists info associated with likelyLang.
// Size: 582 bytes, 97 elements
var likelyLangList = [97]likelyScriptRegion{
	0:  {region: 0x9d, script: 0x7, flags: 0x0},
	1:  {region: 0xa2, script: 0x7a, flags: 0x2},
	2:  {region: 0x11d, script: 0x87, flags: 0x2},
	3:  {region: 0x32, script: 0x5b, flags: 0x0},
	4:  {region: 0x9c, script: 0x5, flags: 0x4},
	5:  {region: 0x9d, script: 0x5, flags: 0x4},
	6:  {region: 0x107, script: 0x20, flags: 0x4},
	7:  {region: 0x9d, script: 0x5, flags: 0x2},
	8:  {region: 0x107, script: 0x20, flags: 0x0},
	9:  {region: 0x38, script: 0x2f, flags: 0x2},
	10: {region: 0x136, script: 0x5b, flags: 0x0},
	11: {region: 0x7c, script: 0xd1, flags: 0x2},
	12: {region: 0x115, script: 0x5b, flags: 0x0},
	13: {region: 0x85, script: 0x1, flags: 0x2},
	14: {region: 0x5e, script: 0x1f, flags: 0x0},
	15: {region: 0x88, script: 0x60, flags: 0x2},
	16: {region: 0xd7, script: 0x5b, flags: 0x0},
	17: {region: 0x52, script: 0x5, flags: 0x4},
	18: {region: 0x10c, script: 0x5, flags: 0x4},
	19: {region: 0xaf, script: 0x20, flags: 0x0},
	20: {region: 0x24, script: 0x5, flags: 0x4},
	21: {region: 0x53, script: 0x5, flags: 0x4},
	22: {region: 0x9d, script: 0x5, flags: 0x4},
	23: {region: 0xc6, script: 0x5, flags: 0x4},
	24: {region: 0x53, script: 0x5, flags: 0x2},
	25: {region: 0x12c, script: 0x5b, flags: 0x0},
	26: {region: 0xb1, script: 0x5, flags: 0x4},
	27: {region: 0x9c, script: 0x5, flags: 0x2},
	28: {region: 0xa6, script: 0x20, flags: 0x0},
	29: {region: 0x53, script: 0x5, flags: 0x4},
	30: {region: 0x12c, script: 0x5b, flags: 0x4},
	31: {region: 0x53, script: 0x5, flags: 0x2},
	32: {region: 0x12c, script: 0x5b, flags: 0x2},
	33: {region: 0xdc, script: 0x22, flags: 0x0},
	34: {region: 0x9a, script: 0x5e, flags: 0x2},
	35: {region: 0x84, script: 0x5b, flags: 0x0},
	36: {region: 0x85, script: 0x7e, flags: 0x4},
	37: {region: 0x85, script: 0x7e, flags: 0x2},
	38: {region: 0xc6, script: 0x20, flags: 0x0},
	39: {region: 0x53, script: 0x71, flags: 0x4},
	40: {region: 0x53, script: 0x71, flags: 0x2},
	41: {region: 0xd1, script: 0x5b, flags: 0x0},
	42: {region: 0x4a, script: 0x5, flags: 0x4},
	43: {region: 0x96, script: 0x5, flags: 0x4},
	44: {region: 0x9a, script: 0x36, flags: 0x0},
	45: {region: 0xe9, script: 0x5, flags: 0x4},
	46: {region: 0xe9, script: 0x5, flags: 0x2},
	47: {region: 0x9d, script: 0x8d, flags: 0x0},
	48: {region: 0x53, script: 0x8e, flags: 0x2},
	49: {region: 0xbb, script: 0xeb, flags: 0x0},
	50: {region: 0xda, script: 0x5b, flags: 0x4},
	51: {region: 0xe9, script: 0x5, flags: 0x0},
	52: {region: 0x9a, script: 0x22, flags: 0x2},
	53: {region: 0x9a, script: 0x50, flags: 0x2},
	54: {region: 0x9a, script: 0xd5, flags: 0x2},
	55: {region: 0x106, script: 0x20, flags: 0x0},
	56: {region: 0xbe, script: 0x5b, flags: 0x4},
	57: {region: 0x105, script: 0x5b, flags: 0x4},
	58: {region: 0x107, script: 0x5b, flags: 0x4},
	59: {region: 0x12c, script: 0x5b, flags: 0x4},
	60: {region: 0x125, script: 0x20, flags: 0x0},
	61: {region: 0xe9, script: 0x5, flags: 0x4},
	62: {region: 0xe9, script: 0x5, flags: 0x2},
	63: {region: 0x53, script: 0x5, flags: 0x0},
	64: {region: 0xaf, script: 0x20, flags: 0x4},
	65: {region: 0xc6, script: 0x20, flags: 0x4},
	66: {region: 0xaf, script: 0x20, flags: 0x2},
	67: {region: 0x9a, script: 0xe, flags: 0x0},
	68: {region: 0xdc, script: 0x22, flags: 0x4},
	69: {region: 0xdc, script: 0x22, flags: 0x2},
	70: {region: 0x138, script: 0x5b, flags: 0x0},
	71: {region: 0x24, script: 0x5, flags: 0x4},
	72: {region: 0x53, script: 0x20, flags: 0x4},
	73: {region: 0x24, script: 0x5, flags: 0x2},
	74: {region: 0x8e, script: 0x3c, flags: 0x0},
	75: {region: 0x53, script: 0x3b, flags: 0x4},
	76: {region: 0x53, script: 0x3b, flags: 0x2},
	77: {region: 0x53, script: 0x3b, flags: 0x0},
	78: {region: 0x2f, script: 0x3c, flags: 0x4},
	79: {region: 0x3e, script: 0x3c, flags: 0x4},
	80: {region: 0x7c, script: 0x3c, flags: 0x4},
	81: {region: 0x7f, script: 0x3c, flags: 0x4},
	82: {region: 0x8e, script: 0x3c, flags: 0x4},
	83: {region: 0x96, script: 0x3c, flags: 0x4},
	84: {region: 0xc7, script: 0x3c, flags: 0x4},
	85: {region: 0xd1, script: 0x3c, flags: 0x4},
	86: {region: 0xe3, script: 0x3c, flags: 0x4},
	87: {region: 0xe6, script: 0x3c, flags: 0x4},
	88: {region: 0xe8, script: 0x3c, flags: 0x4},
	89: {region: 0x117, script: 0x3c, flags: 0x4},
	90: {region: 0x124, script: 0x3c, flags: 0x4},
	91: {region: 0x12f, script: 0x3c, flags: 0x4},
	92: {region: 0x136, script: 0x3c, flags: 0x4},
	93: {region: 0x13f, script: 0x3c, flags: 0x4},
	94: {region: 0x12f, script: 0x11, flags: 0x2},
	95: {region: 0x12f, script: 0x37, flags: 0x2},
	96: {region: 0x12f, script: 0x3c, flags: 0x2},
}

type likelyLangScript struct {
	lang   uint16
	script uint16
	flags  uint8
}

// likelyRegion is a lookup table, indexed by regionID, for the most likely
// languages and scripts given incomplete information. If more entries exist
// for a given regionID, lang and script are the index and size respectively
// of the list in likelyRegionList.
// TODO: exclude containers and user-definable regions from the list.
// Size: 2154 bytes, 359 elements
var likelyRegion = [359]likelyLangScript{
	34:  {lang: 0xd7, script: 0x5b, flags: 0x0},
	35:  {lang: 0x3a, script: 0x5, flags: 0x0},
	36:  {lang: 0x0, script: 0x2, flags: 0x1},
	39:  {lang: 0x2, script: 0x2, flags: 0x1},
	40:  {lang: 0x4, script: 0x2, flags: 0x1},
	42:  {lang: 0x3c0, script: 0x5b, flags: 0x0},
	43:  {lang: 0x0, script: 0x5b, flags: 0x0},
	44:  {lang: 0x13e, script: 0x5b, flags: 0x0},
	45:  {lang: 0x41b, script: 0x5b, flags: 0x0},
	46:  {lang: 0x10d, script: 0x5b, flags: 0x0},
	48:  {lang: 0x367, script: 0x5b, flags: 0x0},
	49:  {lang: 0x444, script: 0x5b, flags: 0x0},
	50:  {lang: 0x58, script: 0x5b, flags: 0x0},
	51:  {lang: 0x6, script: 0x2, flags: 0x1},
	53:  {lang: 0xa5, script: 0xe, flags: 0x0},
	54:  {lang: 0x367, script: 0x5b, flags: 0x0},
	55:  {lang: 0x15e, script: 0x5b, flags: 0x0},
	56:  {lang: 0x7e, script: 0x20, flags: 0x0},
	57:  {lang: 0x3a, script: 0x5, flags: 0x0},
	58:  {lang: 0x3d9, script: 0x5b, flags: 0x0},
	59:  {lang: 0x15e, script: 0x5b, flags: 0x0},
	60:  {lang: 0x15e, script: 0x5b, flags: 0x0},
	62:  {lang: 0x31f, script: 0x5b, flags: 0x0},
	63:  {lang: 0x13e, script: 0x5b, flags: 0x0},
	64:  {lang: 0x3a1, script: 0x5b, flags: 0x0},
	65:  {lang: 0x3c0, script: 0x5b, flags: 0x0},
	67:  {lang: 0x8, script: 0x2, flags: 0x1},
	69:  {lang: 0x0, script: 0x5b, flags: 0x0},
	71:  {lang: 0x71, script: 0x20, flags: 0x0},
	73:  {lang: 0x512, script: 0x3e, flags: 0x2},
	74:  {lang: 0x31f, script: 0x5, flags: 0x2},
	75:  {lang: 0x445, script: 0x5b, flags: 0x0},
	76:  {lang: 0x15e, script: 0x5b, flags: 0x0},
	77:  {lang: 0x15e, script: 0x5b, flags: 0x0},
	78:  {lang: 0x10d, script: 0x5b, flags: 0x0},
	79:  {lang: 0x15e, script: 0x5b, flags: 0x0},
	81:  {lang: 0x13e, script: 0x5b, flags: 0x0},
	82:  {lang: 0x15e, script: 0x5b, flags: 0x0},
	83:  {lang: 0xa, script: 0x4, flags: 0x1},
	84:  {lang: 0x13e, script: 0x5b, flags: 0x0},
	85:  {lang: 0x0, script: 0x5b, flags: 0x0},
	87:  {lang: 0x13e, script: 0x5b, flags: 0x0},
	90:  {lang: 0x13e, script: 0x5b, flags: 0x0},
	91:  {lang: 0x3c0, script: 0x5b, flags: 0x0},
	92:  {lang: 0x3a1, script: 0x5b, flags: 0x0},
	94:  {lang: 0xe, script: 0x2, flags: 0x1},
	95:  {lang: 0xfa, script: 0x5b, flags: 0x0},
	97:  {lang: 0x10d, script: 0x5b, flags: 0x0},
	99:  {lang: 0x1, script: 0x5b, flags: 0x0},
	100: {lang: 0x101, script: 0x5b, flags: 0x0},
	102: {lang: 0x13e, script: 0x5b, flags: 0x0},
	104: {lang: 0x10, script: 0x2, flags: 0x1},
	105: {lang: 0x13e, script: 0x5b, flags: 0x0},
	106: {lang: 0x13e, script: 0x5b, flags: 0x0},
	107: {lang: 0x140, script: 0x5b, flags: 0x0},
	108: {lang: 0x3a, script: 0x5, flags: 0x0},
	109: {lang: 0x3a, script: 0x5, flags: 0x0},
	110: {lang: 0x46f, script: 0x2c, flags: 0x0},
	111: {lang: 0x13e, script: 0x5b, flags: 0x0},
	112: {lang: 0x12, script: 0x2, flags: 0x1},
	114: {lang: 0x10d, script: 0x5b, flags: 0x0},
	115: {lang: 0x151, script: 0x5b, flags: 0x0},
	116: {lang: 0x1c0, script: 0x22, flags: 0x2},
	119: {lang: 0x158, script: 0x5b, flags: 0x0},
	121: {lang: 0x15e, script: 0x5b, flags: 0x0},
	123: {lang: 0x15e, script: 0x5b, flags: 0x0},
	124: {lang: 0x14, script: 0x2, flags: 0x1},
	126: {lang: 0x16, script: 0x3, flags: 0x1},
	127: {lang: 0x15e, script: 0x5b, flags: 0x0},
	129: {lang: 0x21, script: 0x5b, flags: 0x0},
	131: {lang: 0x245, script: 0x5b, flags: 0x0},
	133: {lang: 0x15e, script: 0x5b, flags: 0x0},
	134: {lang: 0x15e, script: 0x5b, flags: 0x0},
	135: {lang: 0x13e, script: 0x5b, flags: 0x0},
	136: {lang: 0x19, script: 0x2, flags: 0x1},
	137: {lang: 0x0, script: 0x5b, flags: 0x0},
	138: {lang: 0x13e, script: 0x5b, flags: 0x0},
	140: {lang: 0x3c0, script: 0x5b, flags: 0x0},
	142: {lang: 0x529, script: 0x3c, flags: 0x0},
	143: {lang: 0x0, script: 0x5b, flags: 0x0},
	144: {lang: 0x13e, script: 0x5b, flags: 0x0},
	145: {lang: 0x1d1, script: 0x5b, flags: 0x0},
	146: {lang: 0x1d4, script: 0x5b, flags: 0x0},
	147: {lang: 0x1d5, script: 0x5b, flags: 0x0},
	149: {lang: 0x13e, script: 0x5b, flags: 0x0},
	150: {lang: 0x1b, script: 0x2, flags: 0x1},
	152: {lang: 0x1bc, script: 0x3e, flags: 0x0},
	154: {lang: 0x1d, script: 0x3, flags: 0x1},
	156: {lang: 0x3a, script: 0x5, flags: 0x0},
	157: {lang: 0x20, script: 0x2, flags: 0x1},
	158: {lang: 0x1f8, script: 0x5b, flags: 0x0},
	159: {lang: 0x1f9, script: 0x5b, flags: 0x0},
	162: {lang: 0x3a, script: 0x5, flags: 0x0},
	163: {lang: 0x200, script: 0x49, flags: 0x0},
	165: {lang: 0x445, script: 0x5b, flags: 0x0},
	166: {lang: 0x28a, script: 0x20, flags: 0x0},
	167: {lang: 0x22, script: 0x3, flags: 0x1},
	169: {lang: 0x25, script: 0x2, flags: 0x1},
	171: {lang: 0x254, script: 0x54, flags: 0x0},
	172: {lang: 0x254, script: 0x54, flags: 0x0},
	173: {lang: 0x3a, script: 0x5, flags: 0x0},
	175: {lang: 0x3e2, script: 0x20, flags: 0x0},
	176: {lang: 0x27, script: 0x2, flags: 0x1},
	177: {lang: 0x3a, script: 0x5, flags: 0x0},
	179: {lang: 0x10d, script: 0x5b, flags: 0x0},
	180: {lang: 0x40c, script: 0xd6, flags: 0x0},
	182: {lang: 0x43b, script: 0x5b, flags: 0x0},
	183: {lang: 0x2c0, script: 0x5b, flags: 0x0},
	184: {lang: 0x15e, script: 0x5b, flags: 0x0},
	185: {lang: 0x2c7, script: 0x5b, flags: 0x0},
	186: {lang: 0x3a, script: 0x5, flags: 0x0},
	187: {lang: 0x29, script: 0x2, flags: 0x1},
	188: {lang: 0x15e, script: 0x5b, flags: 0x0},
	189: {lang: 0x2b, script: 0x2, flags: 0x1},
	190: {lang: 0x432, script: 0x5b, flags: 0x0},
	191: {lang: 0x15e, script: 0x5b, flags: 0x0},
	192: {lang: 0x2f1, script: 0x5b, flags: 0x0},
	195: {lang: 0x2d, script: 0x2, flags: 0x1},
	196: {lang: 0xa0, script: 0x5b, flags: 0x0},
	197: {lang: 0x2f, script: 0x2, flags: 0x1},
	198: {lang: 0x31, script: 0x2, flags: 0x1},
	199: {lang: 0x33, script: 0x2, flags: 0x1},
	201: {lang: 0x15e, script: 0x5b, flags: 0x0},
	202: {lang: 0x35, script: 0x2, flags: 0x1},
	204: {lang: 0x320, script: 0x5b, flags: 0x0},
	205: {lang: 0x37, script: 0x3, flags: 0x1},
	206: {lang: 0x128, script: 0xed, flags: 0x0},
	208: {lang: 0x13e, script: 0x5b, flags: 0x0},
	209: {lang: 0x31f, script: 0x5b, flags: 0x0},
	210: {lang: 0x3c0, script: 0x5b, flags: 0x0},
	211: {lang: 0x16, script: 0x5b, flags: 0x0},
	212: {lang: 0x15e, script: 0x5b, flags: 0x0},
	213: {lang: 0x1b4, script: 0x5b, flags: 0x0},
	215: {lang: 0x1b4, script: 0x5, flags: 0x2},
	217: {lang: 0x13e, script: 0x5b, flags: 0x0},
	218: {lang: 0x367, script: 0x5b, flags: 0x0},
	219: {lang: 0x347, script: 0x5b, flags: 0x0},
	220: {lang: 0x351, script: 0x22, flags: 0x0},
	226: {lang: 0x3a, script: 0x5, flags: 0x0},
	227: {lang: 0x13e, script: 0x5b, flags: 0x0},
	229: {lang: 0x13e, script: 0x5b, flags: 0x0},
	230: {lang: 0x15e, script: 0x5b, flags: 0x0},
	231: {lang: 0x486, script: 0x5b, flags: 0x0},
	232: {lang: 0x153, script: 0x5b, flags: 0x0},
	233: {lang: 0x3a, script: 0x3, flags: 0x1},
	234: {lang: 0x3b3, script: 0x5b, flags: 0x0},
	235: {lang: 0x15e, script: 0x5b, flags: 0x0},
	237: {lang: 0x13e, script: 0x5b, flags: 0x0},
	238: {lang: 0x3a, script: 0x5, flags: 0x0},
	239: {lang: 0x3c0, script: 0x5b, flags: 0x0},
	241: {lang: 0x3a2, script: 0x5b, flags: 0x0},
	242: {lang: 0x194, script: 0x5b, flags: 0x0},
	244: {lang: 0x3a, script: 0x5, flags: 0x0},
	259: {lang: 0x15e, script: 0x5b, flags: 0x0},
	261: {lang: 0x3d, script: 0x2, flags: 0x1},
	262: {lang: 0x432, script: 0x20, flags: 0x0},
	263: {lang: 0x3f, script: 0x2, flags: 0x1},
	264: {lang: 0x3e5, script: 0x5b, flags: 0x0},
	265: {lang: 0x3a, script: 0x5, flags: 0x0},
	267: {lang: 0x15e, script: 0x5b, flags: 0x0},
	268: {lang: 0x3a, script: 0x5, flags: 0x0},
	269: {lang: 0x41, script: 0x2, flags: 0x1},
	272: {lang: 0x416, script: 0x5b, flags: 0x0},
	273: {lang: 0x347, script: 0x5b, flags: 0x0},
	274: {lang: 0x43, script: 0x2, flags: 0x1},
	276: {lang: 0x1f9, script: 0x5b, flags: 0x0},
	277: {lang: 0x15e, script: 0x5b, flags: 0x0},
	278: {lang: 0x429, script: 0x5b, flags: 0x0},
	279: {lang: 0x367, script: 0x5b, flags: 0x0},
	281: {lang: 0x3c0, script: 0x5b, flags: 0x0},
	283: {lang: 0x13e, script: 0x5b, flags: 0x0},
	285: {lang: 0x45, script: 0x2, flags: 0x1},
	289: {lang: 0x15e, script: 0x5b, flags: 0x0},
	290: {lang: 0x15e, script: 0x5b, flags: 0x0},
	291: {lang: 0x47, script: 0x2, flags: 0x1},
	292: {lang: 0x49, script: 0x3, flags: 0x1},
	293: {lang: 0x4c, script: 0x2, flags: 0x1},
	294: {lang: 0x477, script: 0x5b, flags: 0x0},
	295: {lang: 0x3c0, script: 0x5b, flags: 0x0},
	296: {lang: 0x476, script: 0x5b, flags: 0x0},
	297: {lang: 0x4e, script: 0x2, flags: 0x1},
	298: {lang: 0x482, script: 0x5b, flags: 0x0},
	300: {lang: 0x50, script: 0x4, flags: 0x1},
	302: {lang: 0x4a0, script: 0x5b, flags: 0x0},
	303: {lang: 0x54, script: 0x2, flags: 0x1},
	304: {lang: 0x445, script: 0x5b, flags: 0x0},
	305: {lang: 0x56, script: 0x3, flags: 0x1},
	306: {lang: 0x445, script: 0x5b, flags: 0x0},
	310: {lang: 0x512, script: 0x3e, flags: 0x2},
	311: {lang: 0x13e, script: 0x5b, flags: 0x0},
	312: {lang: 0x4bc, script: 0x5b, flags: 0x0},
	313: {lang: 0x1f9, script: 0x5b, flags: 0x0},
	316: {lang: 0x13e, script: 0x5b, flags: 0x0},
	319: {lang: 0x4c3, script: 0x5b, flags: 0x0},
	320: {lang: 0x8a, script: 0x5b, flags: 0x0},
	321: {lang: 0x15e, script: 0x5b, flags: 0x0},
	323: {lang: 0x41b, script: 0x5b, flags: 0x0},
	334: {lang: 0x59, script: 0x2, flags: 0x1},
	351: {lang: 0x3a, script: 0x5, flags: 0x0},
	352: {lang: 0x5b, script: 0x2, flags: 0x1},
	357: {lang: 0x423, script: 0x5b, flags: 0x0},
}

// likelyRegionList holds lists info associated with likelyRegion.
// Size: 558 bytes, 93 elements
var likelyRegionList = [93]likelyLangScript{
	0:  {lang: 0x148, script: 0x5, flags: 0x0},
	1:  {lang: 0x476, script: 0x5b, flags: 0x0},
	2:  {lang: 0x431, script: 0x5b, flags: 0x0},
	3:  {lang: 0x2ff, script: 0x20, flags: 0x0},
	4:  {lang: 0x1d7, script: 0x8, flags: 0x0},
	5:  {lang: 0x274, script: 0x5b, flags: 0x0},
	6:  {lang: 0xb7, script: 0x5b, flags: 0x0},
	7:  {lang: 0x432, script: 0x20, flags: 0x0},
	8:  {lang: 0x12d, script: 0xef, flags: 0x0},
	9:  {lang: 0x351, script: 0x22, flags: 0x0},
	10: {lang: 0x529, script: 0x3b, flags: 0x0},
	11: {lang: 0x4ac, script: 0x5, flags: 0x0},
	12: {lang: 0x523, script: 0x5b, flags: 0x0},
	13: {lang: 0x29a, script: 0xee, flags: 0x0},
	14: {lang: 0x136, script: 0x34, flags: 0x0},
	15: {lang: 0x48a, script: 0x5b, flags: 0x0},
	16: {lang: 0x3a, script: 0x5, flags: 0x0},
	17: {lang: 0x15e, script: 0x5b, flags: 0x0},
	18: {lang: 0x27, script: 0x2c, flags: 0x0},
	19: {lang: 0x139, script: 0x5b, flags: 0x0},
	20: {lang: 0x26a, script: 0x5, flags: 0x2},
	21: {lang: 0x512, script: 0x3e, flags: 0x2},
	22: {lang: 0x210, script: 0x2e, flags: 0x0},
	23: {lang: 0x5, script: 0x20, flags: 0x0},
	24: {lang: 0x274, script: 0x5b, flags: 0x0},
	25: {lang: 0x136, script: 0x34, flags: 0x0},
	26: {lang: 0x2ff, script: 0x20, flags: 0x0},
	27: {lang: 0x1e1, script: 0x5b, flags: 0x0},
	28: {lang: 0x31f, script: 0x5, flags: 0x0},
	29: {lang: 0x1be, script: 0x22, flags: 0x0},
	30: {lang: 0x4b4, script: 0x5, flags: 0x0},
	31: {lang: 0x236, script: 0x76, flags: 0x0},
	32: {lang: 0x148, script: 0x5, flags: 0x0},
	33: {lang: 0x476, script: 0x5b, flags: 0x0},
	34: {lang: 0x24a, script: 0x4f, flags: 0x0},
	35: {lang: 0xe6, script: 0x5, flags: 0x0},
	36: {lang: 0x226, script: 0xee, flags: 0x0},
	37: {lang: 0x3a, script: 0x5, flags: 0x0},
	38: {lang: 0x15e, script: 0x5b, flags: 0x0},
	39: {lang: 0x2b8, script: 0x58, flags: 0x0},
	40: {lang: 0x226, script: 0xee, flags: 0x0},
	41: {lang: 0x3a, script: 0x5, flags: 0x0},
	42: {lang: 0x15e, script: 0x5b, flags: 0x0},
	43: {lang: 0x3dc, script: 0x5b, flags: 0x0},
	44: {lang: 0x4ae, script: 0x20, flags: 0x0},
	45: {lang: 0x2ff, script: 0x20, flags: 0x0},
	46: {lang: 0x431, script: 0x5b, flags: 0x0},
	47: {lang: 0x331, script: 0x76, flags: 0x0},
	48: {lang: 0x213, script: 0x5b, flags: 0x0},
	49: {lang: 0x30b, script: 0x20, flags: 0x0},
	50: {lang: 0x242, script: 0x5, flags: 0x0},
	51: {lang: 0x529, script: 0x3c, flags: 0x0},
	52: {lang: 0x3c0, script: 0x5b, flags: 0x0},
	53: {lang: 0x3a, script: 0x5, flags: 0x0},
	54: {lang: 0x15e, script: 0x5b, flags: 0x0},
	55: {lang: 0x2ed, script: 0x5b, flags: 0x0},
	56: {lang: 0x4b4, script: 0x5, flags: 0x0},
	57: {lang: 0x88, script: 0x22, flags: 0x0},
	58: {lang: 0x4b4, script: 0x5, flags: 0x0},
	59: {lang: 0x4b4, script: 0x5, flags: 0x0},
	60: {lang: 0xbe, script: 0x22, flags: 0x0},
	61: {lang: 0x3dc, script: 0x5b, flags: 0x0},
	62: {lang: 0x7e, script: 0x20, flags: 0x0},
	63: {lang: 0x3e2, script: 0x20, flags: 0x0},
	64: {lang: 0x267, script: 0x5b, flags: 0x0},
	65: {lang: 0x444, script: 0x5b, flags: 0x0},
	66: {lang: 0x512, script: 0x3e, flags: 0x0},
	67: {lang: 0x412, script: 0x5b, flags: 0x0},
	68: {lang: 0x4ae, script: 0x20, flags: 0x0},
	69: {lang: 0x3a, script: 0x5, flags: 0x0},
	70: {lang: 0x15e, script: 0x5b, flags: 0x0},
	71: {lang: 0x15e, script: 0x5b, flags: 0x0},
	72: {lang: 0x35, script: 0x5, flags: 0x0},
	73: {lang: 0x46b, script: 0xee, flags: 0x0},
	74: {lang: 0x2ec, script: 0x5, flags: 0x0},
	75: {lang: 0x30f, script: 0x76, flags: 0x0},
	76: {lang: 0x467, script: 0x20, flags: 0x0},
	77: {lang: 0x148, script: 0x5, flags: 0x0},
	78: {lang: 0x3a, script: 0x5, flags: 0x0},
	79: {lang: 0x15e, script: 0x5b, flags: 0x0},
	80: {lang: 0x48a, script: 0x5b, flags: 0x0},
	81: {lang: 0x58, script: 0x5, flags: 0x0},
	82: {lang: 0x219, script: 0x20, flags: 0x0},
	83: {lang: 0x81, script: 0x34, flags: 0x0},
	84: {lang: 0x529, script: 0x3c, flags: 0x0},
	85: {lang: 0x48c, script: 0x5b, flags: 0x0},
	86: {lang: 0x4ae, script: 0x20, flags: 0x0},
	87: {lang: 0x512, script: 0x3e, flags: 0x0},
	88: {lang: 0x3b3, script: 0x5b, flags: 0x0},
	89: {lang: 0x431, script: 0x5b, flags: 0x0},
	90: {lang: 0x432, script: 0x20, flags: 0x0},
	91: {lang: 0x15e, script: 0x5b, flags: 0x0},
	92: {lang: 0x446, script: 0x5, flags: 0x0},
}

type likelyTag struct {
	lang   uint16
	region uint16
	script uint16
}

// Size: 198 bytes, 33 elements
var likelyRegionGroup = [33]likelyTag{
	1:  {lang: 0x139, region: 0xd7, script: 0x5b},
	2:  {lang: 0x139, region: 0x136, script: 0x5b},
	3:  {lang: 0x3c0, region: 0x41, script: 0x5b},
	4:  {lang: 0x139, region: 0x2f, script: 0x5b},
	5:  {lang: 0x139, region: 0xd7, script: 0x5b},
	6:  {lang: 0x13e, region: 0xd0, script: 0x5b},
	7:  {lang: 0x445, region: 0x130, script: 0x5b},
	8:  {lang: 0x3a, region: 0x6c, script: 0x5},
	9:  {lang: 0x445, region: 0x4b, script: 0x5b},
	10: {lang: 0x139, region: 0x162, script: 0x5b},
	11: {lang: 0x139, region: 0x136, script: 0x5b},
	12: {lang: 0x139, region: 0x136, script: 0x5b},
	13: {lang: 0x13e, region: 0x5a, script: 0x5b},
	14: {lang: 0x529, region: 0x53, script: 0x3b},
	15: {lang: 0x1be, region: 0x9a, script: 0x22},
	16: {lang: 0x1e1, region: 0x96, script: 0x5b},
	17: {lang: 0x1f9, region: 0x9f, script: 0x5b},
	18: {lang: 0x139, region: 0x2f, script: 0x5b},
	19: {lang: 0x139, region: 0xe7, script: 0x5b},
	20: {lang: 0x139, region: 0x8b, script: 0x5b},
	21: {lang: 0x41b, region: 0x143, script: 0x5b},
	22: {lang: 0x529, region: 0x53, script: 0x3b},
	23: {lang: 0x4bc, region: 0x138, script: 0x5b},
	24: {lang: 0x3a, region: 0x109, script: 0x5},
	25: {lang: 0x3e2, region: 0x107, script: 0x20},
	26: {lang: 0x3e2, region: 0x107, script: 0x20},
	27: {lang: 0x139, region: 0x7c, script: 0x5b},
	28: {lang: 0x10d, region: 0x61, script: 0x5b},
	29: {lang: 0x139, region: 0xd7, script: 0x5b},
	30: {lang: 0x13e, region: 0x1f, script: 0x5b},
	31: {lang: 0x139, region: 0x9b, script: 0x5b},
	32: {lang: 0x139, region: 0x7c, script: 0x5b},
}

// Size: 264 bytes, 33 elements
var regionContainment = [33]uint64{
	// Entry 0 - 1F
	0x00000001ffffffff, 0x00000000200007a2, 0x0000000000003044, 0x0000000000000008,
	0x00000000803c0010, 0x0000000000000020, 0x0000000000000040, 0x0000000000000080,
	0x0000000000000100, 0x0000000000000200, 0x0000000000000400, 0x000000004000384c,
	0x0000000000001000, 0x0000000000002000, 0x0000000000004000, 0x0000000000008000,
	0x0000000000010000, 0x0000000000020000, 0x0000000000040000, 0x0000000000080000,
	0x0000000000100000, 0x0000000000200000, 0x0000000001c1c000, 0x0000000000800000,
	0x0000000001000000, 0x000000001e020000, 0x0000000004000000, 0x0000000008000000,
	0x0000000010000000, 0x00000000200006a0, 0x0000000040002048, 0x0000000080000000,
	// Entry 20 - 3F
	0x0000000100000000,
}

// regionInclusion maps region identifiers to sets of regions in regionInclusionBits,
// where each set holds all groupings that are directly connected in a region
// containment graph.
// Size: 359 bytes, 359 elements
var regionInclusion = [359]uint8{
	// Entry 0 - 3F
	0x00, 0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06,
	0x07, 0x08, 0x09, 0x0a, 0x0b, 0x0c, 0x0d, 0x0e,
	0x0f, 0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16,
	0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e,
	0x21, 0x22, 0x23, 0x24, 0x25, 0x26, 0x26, 0x23,
	0x24, 0x26, 0x27, 0x22, 0x28, 0x29, 0x2a, 0x2b,
	0x26, 0x2c, 0x24, 0x23, 0x26, 0x25, 0x2a, 0x2d,
	0x2e, 0x24, 0x2f, 0x2d, 0x26, 0x30, 0x31, 0x28,
	// Entry 40 - 7F
	0x26, 0x28, 0x26, 0x25, 0x31, 0x22, 0x32, 0x33,
	0x34, 0x30, 0x22, 0x27, 0x27, 0x27, 0x35, 0x2d,
	0x29, 0x28, 0x27, 0x36, 0x28, 0x22, 0x21, 0x34,
	0x23, 0x21, 0x26, 0x2d, 0x26, 0x22, 0x37, 0x2e,
	0x35, 0x2a, 0x22, 0x2f, 0x38, 0x26, 0x26, 0x21,
	0x39, 0x39, 0x28, 0x38, 0x39, 0x39, 0x2f, 0x3a,
	0x2f, 0x20, 0x21, 0x38, 0x3b, 0x28, 0x3c, 0x2c,
	0x21, 0x2a, 0x35, 0x27, 0x38, 0x26, 0x24, 0x28,
	// Entry 80 - BF
	0x2c, 0x2d, 0x23, 0x30, 0x2d, 0x2d, 0x26, 0x27,
	0x3a, 0x22, 0x34, 0x3c, 0x2d, 0x28, 0x36, 0x22,
	0x34, 0x3a, 0x26, 0x2e, 0x21, 0x39, 0x31, 0x38,
	0x24, 0x2c, 0x25, 0x22, 0x24, 0x25, 0x2c, 0x3a,
	0x2c, 0x26, 0x24, 0x36, 0x21, 0x2f, 0x3d, 0x31,
	0x3c, 0x2f, 0x26, 0x36, 0x36, 0x24, 0x26, 0x3d,
	0x31, 0x24, 0x26, 0x35, 0x25, 0x2d, 0x32, 0x38,
	0x2a, 0x38, 0x39, 0x39, 0x35, 0x33, 0x23, 0x26,
	// Entry C0 - FF
	0x2f, 0x3c, 0x21, 0x23, 0x2d, 0x31, 0x36, 0x36,
	0x3c, 0x26, 0x2d, 0x26, 0x3a, 0x2f, 0x25, 0x2f,
	0x34, 0x31, 0x2f, 0x32, 0x3b, 0x2d, 0x2b, 0x2d,
	0x21, 0x34, 0x2a, 0x2c, 0x25, 0x21, 0x3c, 0x24,
	0x29, 0x2b, 0x24, 0x34, 0x21, 0x28, 0x29, 0x3b,
	0x31, 0x25, 0x2e, 0x30, 0x29, 0x26, 0x24, 0x3a,
	0x21, 0x3c, 0x28, 0x21, 0x24, 0x21, 0x21, 0x1f,
	0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21,
	// Entry 100 - 13F
	0x21, 0x21, 0x21, 0x2f, 0x21, 0x2e, 0x23, 0x33,
	0x2f, 0x24, 0x3b, 0x2f, 0x39, 0x38, 0x31, 0x2d,
	0x3a, 0x2c, 0x2e, 0x2d, 0x23, 0x2d, 0x2f, 0x28,
	0x2f, 0x27, 0x33, 0x34, 0x26, 0x24, 0x32, 0x22,
	0x26, 0x27, 0x22, 0x2d, 0x31, 0x3d, 0x29, 0x31,
	0x3d, 0x39, 0x29, 0x31, 0x24, 0x26, 0x29, 0x36,
	0x2f, 0x33, 0x2f, 0x21, 0x22, 0x21, 0x30, 0x28,
	0x3d, 0x23, 0x26, 0x21, 0x28, 0x26, 0x26, 0x31,
	// Entry 140 - 17F
	0x3b, 0x29, 0x21, 0x29, 0x21, 0x21, 0x21, 0x21,
	0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x23, 0x21,
	0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x21,
	0x21, 0x21, 0x21, 0x21, 0x21, 0x21, 0x24, 0x24,
	0x2f, 0x23, 0x32, 0x2f, 0x27, 0x2f, 0x21,
}

// regionInclusionBits is an array of bit vectors where every vector represents
// a set of region groupings.  These sets are used to compute the distance
// between two regions for the purpose of language matching.
// Size: 584 bytes, 73 elements
var regionInclusionBits = [73]uint64{
	// Entry 0 - 1F
	0x0000000102400813, 0x00000000200007a3, 0x0000000000003844, 0x0000000040000808,
	0x00000000803c0011, 0x0000000020000022, 0x0000000040000844, 0x0000000020000082,
	0x0000000000000102, 0x0000000020000202, 0x0000000020000402, 0x000000004000384d,
	0x0000000000001804, 0x0000000040002804, 0x0000000000404000, 0x0000000000408000,
	0x0000000000410000, 0x0000000002020000, 0x0000000000040010, 0x0000000000080010,
	0x0000000000100010, 0x0000000000200010, 0x0000000001c1c001, 0x0000000000c00000,
	0x0000000001400000, 0x000000001e020001, 0x0000000006000000, 0x000000000a000000,
	0x0000000012000000, 0x00000000200006a2, 0x0000000040002848, 0x0000000080000010,
	// Entry 20 - 3F
	0x0000000100000001, 0x0000000000000001, 0x0000000080000000, 0x0000000000020000,
	0x0000000001000000, 0x0000000000008000, 0x0000000000002000, 0x0000000000000200,
	0x0000000000000008, 0x0000000000200000, 0x0000000110000000, 0x0000000000040000,
	0x0000000008000000, 0x0000000000000020, 0x0000000104000000, 0x0000000000000080,
	0x0000000000001000, 0x0000000000010000, 0x0000000000000400, 0x0000000004000000,
	0x0000000000000040, 0x0000000010000000, 0x0000000000004000, 0x0000000101000000,
	0x0000000108000000, 0x0000000000000100, 0x0000000100020000, 0x0000000000080000,
	0x0000000000100000, 0x0000000000800000, 0x00000001ffffffff, 0x0000000122400fb3,
	// Entry 40 - 5F
	0x00000001827c0813, 0x000000014240385f, 0x0000000103c1c813, 0x000000011e420813,
	0x0000000112000001, 0x0000000106000001, 0x0000000101400001, 0x000000010a000001,
	0x0000000102020001,
}

// regionInclusionNext marks, for each entry in regionInclusionBits, the set of
// all groups that are reachable from the groups set in the respective entry.
// Size: 73 bytes, 73 elements
var regionInclusionNext = [73]uint8{
	// Entry 0 - 3F
	0x3e, 0x3f, 0x0b, 0x0b, 0x40, 0x01, 0x0b, 0x01,
	0x01, 0x01, 0x01, 0x41, 0x0b, 0x0b, 0x16, 0x16,
	0x16, 0x19, 0x04, 0x04, 0x04, 0x04, 0x42, 0x16,
	0x16, 0x43, 0x19, 0x19, 0x19, 0x01, 0x0b, 0x04,
	0x00, 0x00, 0x1f, 0x11, 0x18, 0x0f, 0x0d, 0x09,
	0x03, 0x15, 0x44, 0x12, 0x1b, 0x05, 0x45, 0x07,
	0x0c, 0x10, 0x0a, 0x1a, 0x06, 0x1c, 0x0e, 0x46,
	0x47, 0x08, 0x48, 0x13, 0x14, 0x17, 0x3e, 0x3e,
	// Entry 40 - 7F
	0x3e, 0x3e, 0x3e, 0x3e, 0x43, 0x43, 0x42, 0x43,
	0x43,
}

type parentRel struct {
	lang       uint16
	script     uint16
	maxScript  uint16
	toRegion   uint16
	fromRegion []uint16
}

// Size: 414 bytes, 5 elements
var parents = [5]parentRel{
	0: {lang: 0x139, script: 0x0, maxScript: 0x5b, toRegion: 0x1, fromRegion: []uint16{0x1a, 0x25, 0x26, 0x2f, 0x34, 0x36, 0x3d, 0x42, 0x46, 0x48, 0x49, 0x4a, 0x50, 0x52, 0x5d, 0x5e, 0x62, 0x65, 0x6e, 0x74, 0x75, 0x76, 0x7c, 0x7d, 0x80, 0x81, 0x82, 0x84, 0x8d, 0x8e, 0x97, 0x98, 0x99, 0x9a, 0x9b, 0xa0, 0xa1, 0xa5, 0xa8, 0xaa, 0xae, 0xb2, 0xb5, 0xb6, 0xc0, 0xc7, 0xcb, 0xcc, 0xcd, 0xcf, 0xd1, 0xd3, 0xd6, 0xd7, 0xde, 0xe0, 0xe1, 0xe7, 0xe8, 0xe9, 0xec, 0xf1, 0x108, 0x10a, 0x10b, 0x10c, 0x10e, 0x10f, 0x113, 0x118, 0x11c, 0x11e, 0x120, 0x126, 0x12a, 0x12d, 0x12e, 0x130, 0x132, 0x13a, 0x13d, 0x140, 0x143, 0x162, 0x163, 0x165}},
	1: {lang: 0x139, script: 0x0, maxScript: 0x5b, toRegion: 0x1a, fromRegion: []uint16{0x2e, 0x4e, 0x61, 0x64, 0x73, 0xda, 0x10d, 0x110}},
	2: {lang: 0x13e, script: 0x0, maxScript: 0x5b, toRegion: 0x1f, fromRegion: []uint16{0x2c, 0x3f, 0x41, 0x48, 0x51, 0x54, 0x57, 0x5a, 0x66, 0x6a, 0x8a, 0x90, 0xd0, 0xd9, 0xe3, 0xe5, 0xed, 0xf2, 0x11b, 0x136, 0x137, 0x13c}},
	3: {lang: 0x3c0, script: 0x0, maxScript: 0x5b, toRegion: 0xef, fromRegion: []uint16{0x2a, 0x4e, 0x5b, 0x87, 0x8c, 0xb8, 0xc7, 0xd2, 0x119, 0x127}},
	4: {lang: 0x529, script: 0x3c, maxScript: 0x3c, toRegion: 0x8e, fromRegion: []uint16{0xc7}},
}

// Total table size 30466 bytes (29KiB); checksum: 7544152B
