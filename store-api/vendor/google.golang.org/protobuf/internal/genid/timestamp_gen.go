// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Code generated by generate-protos. DO NOT EDIT.

package genid

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
)

const File_google_protobuf_timestamp_proto = "google/protobuf/timestamp.proto"

// Names for google.protobuf.Timestamp.
const (
	Timestamp_message_name     protoreflect.Name     = "Timestamp"
	Timestamp_message_fullname protoreflect.FullName = "google.protobuf.Timestamp"
)

// Field names for google.protobuf.Timestamp.
const (
	Timestamp_Seconds_field_name protoreflect.Name = "seconds"
	Timestamp_Nanos_field_name   protoreflect.Name = "nanos"

	Timestamp_Seconds_field_fullname protoreflect.FullName = "google.protobuf.Timestamp.seconds"
	Timestamp_Nanos_field_fullname   protoreflect.FullName = "google.protobuf.Timestamp.nanos"
)

// Field numbers for google.protobuf.Timestamp.
const (
	Timestamp_Seconds_field_number protoreflect.FieldNumber = 1
	Timestamp_Nanos_field_number   protoreflect.FieldNumber = 2
)
