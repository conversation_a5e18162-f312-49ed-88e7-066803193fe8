// Copyright 2019 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Package genid contains constants for declarations in descriptor.proto
// and the well-known types.
package genid

import protoreflect "google.golang.org/protobuf/reflect/protoreflect"

const GoogleProtobuf_package protoreflect.FullName = "google.protobuf"
