package main

import (
	"errors"
	"flag"
	"github.com/99designs/gqlgen/graphql/handler"
	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"github.com/siper92/core-utils"
	"github.com/siper92/core-utils/config_utils"
	"log"
	"praktis.bg/store-api/graphql/directives"
	"praktis.bg/store-api/graphql/generated"
	"praktis.bg/store-api/graphql/middleware"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/resolver"
	"praktis.bg/store-api/internal/config"
	"praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/types"
	"time"
)

func init() {
	file := flag.String("c", ".conf.yaml", "Config file")
	flag.Parse()

	core_utils.Debug("Init Config: %s", *file)
	config.LoadConfig(*file)

	configInfo := config.GetConfig()
	initOptions := magento_core.InitMagentoOptions{
		Mode:      configInfo.Api.Mode,
		StoreCode: "bg",
		Cache: config_utils.RedisConfig{
			Host:     configInfo.Cache.Host,
			Port:     configInfo.Cache.Port,
			Database: configInfo.Cache.Database,
			Pass:     configInfo.Cache.Pass,
		},
		CustomerCacheDB: types.ToInt(configInfo.Customer.RedisDB),
		Database: struct {
			DSN            string
			MacConnections int
		}{
			DSN:            configInfo.Database.GetDNS(),
			MacConnections: 300,
		},
		JWTSecret: configInfo.Security.JWTSecret,
	}
	err := magento_core.InitStoreClient(
		initOptions,
	)
	core_utils.StopOnError(err)
}

func main() {
	r := gin.Default()
	r.Use(middleware.GinContextToContextMiddleware())
	r.Use(request_context.UseMiddleware())
	r.Use(getCorsHandler(
		config.GetConfig().Api.AllowOrigins,
	))
	core_utils.StopOnError(r.SetTrustedProxies(nil))

	r.POST("/graphql", graphqlHandler())
	r.NoRoute(func(c *gin.Context) {
		c.JSON(404, gin.H{
			"code":    "PAGE_NOT_FOUND",
			"message": "Page not found: " + c.FullPath(),
		})
	})

	conf := config.GetConfig()
	log.Printf(
		"Conntextion statded at http://localhost:%d for GraphQL queries",
		conf.Api.Port,
	)
	core_utils.StopOnError(
		r.Run(":" + core_utils.IntToString(conf.Api.Port)),
	)
}

func getCorsHandler(origins []string) gin.HandlerFunc {
	if len(origins) < 1 {
		core_utils.ErrorWarning(errors.New("no allowed origins for CORS"))
		return nil
	}

	return cors.New(cors.Config{
		AllowOrigins:     origins,
		AllowMethods:     []string{"POST", "GET"},
		AllowHeaders:     []string{"*"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * time.Hour,
	})
}

func graphqlHandler() gin.HandlerFunc {
	srv := handler.NewDefaultServer(
		generated.NewExecutableSchema(
			generated.Config{
				Resolvers: &resolver.Resolver{},
				Directives: generated.DirectiveRoot{
					HasValidCaptcha: directives.HasValidCaptcha(),
					HasValidToken:   directives.HasValidToken(),
				},
			},
		),
	)

	return func(c *gin.Context) {
		srv.ServeHTTP(c.Writer, c.Request)
	}
}
