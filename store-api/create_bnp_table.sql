-- Create table for storing BNP payment data persistently
-- This table will not be affected by external Magento API calls

CREATE TABLE IF NOT EXISTS `bnp_payment_data_storage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `quote_id` bigint(20) NOT NULL,
  `payment_data` text NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_quote_id` (`quote_id`),
  KEY `idx_quote_id` (`quote_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
