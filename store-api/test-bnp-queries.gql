# Test queries for debugging the BNP Good Types issue

# Test 1: Get categories (this should work)
query TestGetBNPGoodCategories {
  getBNPGoodCategories {
    id
    name
  }
}

# Test 2: Get all good types (no categoryId parameter)
query TestGetAllBNPGoodTypes {
  getBNPGoodTypes {
    id
    name
    categoryId
  }
}

# Test 3: Get good types for a specific category
query TestGetBNPGoodTypesForCategory {
  getBNPGoodTypes(categoryId: "1") {
    id
    name
    categoryId
  }
}

# Test 4: Get good types with null categoryId (explicit null)
query TestGetBNPGoodTypesWithNull {
  getBNPGoodTypes(categoryId: null) {
    id
    name
    categoryId
  }
}
