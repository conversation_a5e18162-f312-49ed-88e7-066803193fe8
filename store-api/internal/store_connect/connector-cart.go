package store_connect

import (
	"errors"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/packages/magento-core/api"
	"strings"
)

var _ StoreCartConnect = (*ThemeAPICartConnector)(nil)

const (
	CartAddItem        MagentoEndpoint = "sales_cart/addItem"
	CartRemoveItem     MagentoEndpoint = "sales_cart/removeItem"
	CartUpdateItem     MagentoEndpoint = "sales_cart/updateItem"
	ApplyCouponCode    MagentoEndpoint = "sales_cart/applyCouponCode"
	RecalculateTotals  MagentoEndpoint = "sales_cart/calculateTotals"
	GetShippingMethods MagentoEndpoint = "sales_cart/getShippingMethods"
	PlaceOrder         MagentoEndpoint = "sales_order/createNewOrder"
)

type ThemeAPICartConnector struct {
	apiConnector *api.ExternalApi
}

func (t *ThemeAPICartConnector) Init(
	baseURL string,
	apiSecret string,
) (StoreCartConnect, error) {
	if strings.HasPrefix(baseURL, "http") == false {
		return nil, errors.New("invalid url: " + baseURL)
	} else if len(apiSecret) < 1 {
		return nil, errors.New("magento store connect invalid api secret")
	}

	return &ThemeAPICartConnector{
		apiConnector: &api.ExternalApi{
			BaseUrl: baseURL,
			Headers: map[string]string{
				AuthThemeHeaderKey:        apiSecret,
				APICallerServiceHeaderKey: StoreAPIService,
				"Content-Type":            "application/json",
			},
		},
	}, nil
}

func (t *ThemeAPICartConnector) AddToCart(cartId string, productSku string, quantity float64) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(CartAddItem),
		struct {
			CartID string  `json:"cartId"`
			Sku    string  `json:"sku"`
			Qty    float64 `json:"qty"`
		}{
			CartID: cartId,
			Sku:    productSku,
			Qty:    quantity,
		},
	)
	if err != nil {
		return false, err
	}

	if !res.IsSuccess() {
		if responseErr := res.GetError(); responseErr != nil {
			return false, responseErr
		}
	}

	return res.IsSuccess(), nil
}

func (t *ThemeAPICartConnector) RemoveFromCart(cartId string, productSku string) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(CartRemoveItem),
		struct {
			CartID string `json:"cartId"`
			Sku    string `json:"sku"`
		}{
			CartID: cartId,
			Sku:    productSku,
		},
	)
	if err != nil {
		return false, err
	}

	return res.IsSuccess(), nil
}

func (t *ThemeAPICartConnector) UpdateQuantity(cartId string, productSku string, quantity float64) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(CartUpdateItem),
		struct {
			CartID string  `json:"cartId"`
			Sku    string  `json:"sku"`
			Qty    float64 `json:"qty"`
		}{
			CartID: cartId,
			Sku:    productSku,
			Qty:    quantity,
		},
	)
	if err != nil {
		return false, err
	}

	if !res.IsSuccess() {
		if responseErr := res.GetError(); responseErr != nil {
			return false, responseErr
		}
	}

	return res.IsSuccess(), nil
}

func (t *ThemeAPICartConnector) ApplyCouponCode(cartId string, couponCode string, customerId int64) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(ApplyCouponCode),
		struct {
			CartID     string `json:"cartId"`
			CouponCode string `json:"promoCode"`
			CustomerID int64  `json:"customerId"`
		}{
			CartID:     cartId,
			CouponCode: couponCode,
			CustomerID: customerId,
		},
	)
	if err != nil {
		return false, err
	}

	return res.IsSuccess(), nil
}

func (t *ThemeAPICartConnector) RecalculateTotals(cartId string) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(RecalculateTotals),
		struct {
			CartID string `json:"cartId"`
		}{
			CartID: cartId,
		},
	)
	if err != nil {
		return false, err
	}

	return res.IsSuccess(), nil
}

func (t *ThemeAPICartConnector) GetAvailableShippingMethods(cartId string) ([]*model.AvailableShippingMethod, error) {
	res, err := makePost(
		t.apiConnector,
		string(GetShippingMethods),
		struct {
			CartID string `json:"cartId"`
		}{
			CartID: cartId,
		},
	)
	if err != nil {
		return nil, err
	}

	var methods []*model.AvailableShippingMethod
	for key, item := range res {
		if strings.Contains(key, "method_") {
			inputMap, ok := item.(map[string]interface{})
			if ok {
				code, _ := inputMap["code"].(string)
				title, _ := inputMap["title"].(string)
				price, _ := inputMap["price"].(float64)
				methods = append(methods, &model.AvailableShippingMethod{
					Method: &model.ShippingMethod{
						Name: title,
						Code: code,
					},
					Price: &model.Price{
						Value:    price,
						Currency: "BGN",
					},
				})
			}
		}
	}

	return methods, nil
}

func (t *ThemeAPICartConnector) PlaceOrder(cartId string, data model.NewOrderInput) (PlaceOrderResponse, error) {
	var res PlaceOrderResponse
	response, err := makePost(
		t.apiConnector,
		string(PlaceOrder),
		struct {
			CartID string `json:"cartId"`
			Data   model.NewOrderInput
		}{
			CartID: cartId,
			Data:   data,
		},
	)
	if err != nil {
		core_utils.ErrorWarning(err)
		return res, err
	}

	res.Order.OrderID = response.GetIntVal("orderId")
	res.Order.IncrementID = response.GetStringVal("incrementId")

	redirectData := response.GetMap("redirect")
	if len(redirectData) > 0 {
		var redirectUrl string
		for key, value := range redirectData {
			if key == "_redirect" {
				redirectUrl = value
			} else {
				res.Redirect.Params = append(res.Redirect.Params, &model.MapValue{
					Key:   key,
					Value: value,
				})
			}
		}

		res.Redirect.RedirectURL = redirectUrl
	}

	return res, err
}
