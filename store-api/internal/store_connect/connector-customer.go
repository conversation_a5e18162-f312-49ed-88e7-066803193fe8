package store_connect

import (
	"errors"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/packages/magento-core/api"
	"strings"
)

var _ StoreCustomerConnect = (*ThemeAPICustomerConnector)(nil)

const (
	CustomerLogin          MagentoEndpoint = "customer_auth/login"
	CustomerForgotPassword MagentoEndpoint = "customer_auth/forgotPassword"
	CustomerResetPassword  MagentoEndpoint = "customer_auth/resetPassword"

	CustomerRegister       MagentoEndpoint = "customer_account/register"
	CustomerUpdatePassword MagentoEndpoint = "customer_account/updatePassword"
	CustomerUpdateInfo     MagentoEndpoint = "customer_account/updateInfo"

	CustomerCreateAddress MagentoEndpoint = "customer_address/createAddress"
	CustomerUpdateAddress MagentoEndpoint = "customer_address/updateAddress"
	CustomerDeleteAddress MagentoEndpoint = "customer_address/deleteAddress"

	CustomerCreateInvoice MagentoEndpoint = "customer_invoice/createInvoice"
	CustomerUpdateInvoice MagentoEndpoint = "customer_invoice/updateInvoice"
	CustomerDeleteInvoice MagentoEndpoint = "customer_invoice/deleteInvoice"
)

type ThemeAPICustomerConnector struct {
	apiConnector *api.ExternalApi
}

func (t ThemeAPICustomerConnector) Init(baseURL string, apiSecret string) (StoreCustomerConnect, error) {
	if strings.HasPrefix(baseURL, "http") == false {
		return nil, errors.New("invalid url: " + baseURL)
	} else if len(apiSecret) < 1 {
		return nil, errors.New("magento store connect invalid api secret")
	}

	return &ThemeAPICustomerConnector{
		apiConnector: &api.ExternalApi{
			BaseUrl: baseURL,
			Headers: map[string]string{
				AuthThemeHeaderKey:        apiSecret,
				APICallerServiceHeaderKey: StoreAPIService,
				"Content-Type":            "application/json",
			},
		},
	}, nil
}

func (t ThemeAPICustomerConnector) Register(data model.CustomerRegistrationData) (CustomerRegisterResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerRegister),
		data,
	)
	if err != nil {
		return CustomerRegisterResult{}, err
	}

	return CustomerRegisterResult{
		RequireConfirmation: res.GetIntVal("confirm") == 1,
		CustomerID:          int64(res.GetIntVal("customer_id")),
	}, nil
}

func (t ThemeAPICustomerConnector) Login(email, pass string) (CustomerActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerLogin),
		struct {
			User string `json:"email"`
			Pass string `json:"password"`
		}{
			User: email,
			Pass: pass,
		},
	)
	if err != nil {
		return CustomerActionResult{}, err
	}

	return CustomerActionResult{
		Success:    res.IsSuccess(),
		CustomerID: int64(res.GetIntVal("customer_id")),
	}, nil
}

func (t ThemeAPICustomerConnector) ForgotPassword(email string) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerForgotPassword),
		struct {
			User string `json:"email"`
		}{
			User: email,
		},
	)
	if err != nil {
		return false, err
	}

	return res.GetIntVal("customer_id") > 0, err
}

func (t ThemeAPICustomerConnector) ResetPassword(customerID int64, resetToken, newPassword string) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerResetPassword),
		struct {
			ID    int64  `json:"id"`
			Token string `json:"resetToken"`
			Pass  string `json:"password"`
		}{
			ID:    customerID,
			Token: resetToken,
			Pass:  newPassword,
		},
	)
	if err != nil {
		return false, err
	}

	return res.GetIntVal("customer_id") == int(customerID), err
}

func (t ThemeAPICustomerConnector) UpdatePassword(email, oldPassword, newPassword string) (CustomerActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerUpdatePassword),
		struct {
			Email   string `json:"email"`
			OldPass string `json:"oldPassword"`
			NewPass string `json:"newPassword"`
		}{
			Email:   email,
			OldPass: oldPassword,
			NewPass: newPassword,
		},
	)
	if err != nil {
		return CustomerActionResult{
			Success:    false,
			CustomerID: 0,
		}, err
	}

	return CustomerActionResult{
		Success:    res.GetIntVal("customer_id") > 0,
		CustomerID: int64(res.GetIntVal("customer_id")),
	}, err
}

func (t ThemeAPICustomerConnector) UpdateInfo(customerID int64, data *model.CustomerUpdateInput) (CustomerActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerUpdateInfo),
		struct {
			ID   int64                      `json:"id"`
			Data *model.CustomerUpdateInput `json:"data"`
		}{
			ID:   customerID,
			Data: data,
		},
	)
	if err != nil {
		return CustomerActionResult{
			Success:    false,
			CustomerID: 0,
		}, err
	}

	return CustomerActionResult{
		Success:    res.GetIntVal("customer_id") > 0,
		CustomerID: int64(res.GetIntVal("customer_id")),
	}, err
}

func (t ThemeAPICustomerConnector) CreateCustomerAddress(customerID int64, data model.CustomerAddressInput) (CustomerAddressActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerCreateAddress),
		struct {
			ID   int64                      `json:"id"`
			Data model.CustomerAddressInput `json:"data"`
		}{
			ID:   customerID,
			Data: data,
		},
	)
	if err != nil {
		return CustomerAddressActionResult{
			Success:    false,
			CustomerID: 0,
			AddressID:  0,
		}, err
	}

	return CustomerAddressActionResult{
		Success:    res.GetIntVal("customer_id") > 0,
		CustomerID: int64(res.GetIntVal("customer_id")),
		AddressID:  int64(res.GetIntVal("address_id")),
	}, err
}

func (t ThemeAPICustomerConnector) UpdateCustomerAddress(customerID, addressID int64, data model.CustomerAddressInput) (CustomerAddressActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerUpdateAddress),
		struct {
			ID        int64                      `json:"id"`
			AddressID int64                      `json:"address_id"`
			Data      model.CustomerAddressInput `json:"data"`
		}{
			ID:        customerID,
			AddressID: addressID,
			Data:      data,
		},
	)
	if err != nil {
		return CustomerAddressActionResult{
			Success:    false,
			CustomerID: 0,
			AddressID:  0,
		}, err
	}

	return CustomerAddressActionResult{
		Success:    res.GetIntVal("customer_id") > 0,
		CustomerID: int64(res.GetIntVal("customer_id")),
		AddressID:  int64(res.GetIntVal("address_id")),
	}, err
}

func (t ThemeAPICustomerConnector) DeleteCustomerAddress(customerID, addressID int64) (CustomerActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerDeleteAddress),
		struct {
			ID        int64 `json:"id"`
			AddressID int64 `json:"address_id"`
		}{
			ID:        customerID,
			AddressID: addressID,
		},
	)
	if err != nil {
		return CustomerActionResult{
			Success:    false,
			CustomerID: 0,
		}, err
	}

	return CustomerActionResult{
		Success:    res.GetIntVal("customer_id") > 0,
		CustomerID: int64(res.GetIntVal("customer_id")),
	}, err
}

func (t ThemeAPICustomerConnector) CreateCustomerInvoice(customerID int64, data model.InvoiceInput) (CustomerInvoiceActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerCreateInvoice),
		struct {
			ID   int64              `json:"id"`
			Data model.InvoiceInput `json:"data"`
		}{
			ID:   customerID,
			Data: data,
		},
	)
	if err != nil {
		return CustomerInvoiceActionResult{
			Success:    false,
			CustomerID: 0,
			InvoiceID:  0,
		}, err
	}

	return CustomerInvoiceActionResult{
		Success:    res.GetIntVal("customer_id") > 0,
		CustomerID: int64(res.GetIntVal("customer_id")),
		InvoiceID:  int64(res.GetIntVal("address_id")),
	}, err
}

func (t ThemeAPICustomerConnector) UpdateCustomerInvoice(customerID, invoiceID int64, data model.InvoiceInput) (CustomerInvoiceActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerUpdateInvoice),
		struct {
			ID        int64              `json:"id"`
			InvoiceID int64              `json:"invoice_id"`
			Data      model.InvoiceInput `json:"data"`
		}{
			ID:        customerID,
			InvoiceID: invoiceID,
			Data:      data,
		},
	)
	if err != nil {
		return CustomerInvoiceActionResult{
			Success:    false,
			CustomerID: 0,
			InvoiceID:  0,
		}, err
	}

	return CustomerInvoiceActionResult{
		Success:    res.GetIntVal("customer_id") > 0,
		CustomerID: int64(res.GetIntVal("customer_id")),
		InvoiceID:  int64(res.GetIntVal("address_id")),
	}, err
}

func (t ThemeAPICustomerConnector) DeleteCustomerInvoice(customerID, invoiceID int64) (CustomerActionResult, error) {
	res, err := makePost(
		t.apiConnector,
		string(CustomerDeleteInvoice),
		struct {
			ID        int64 `json:"id"`
			InvoiceID int64 `json:"invoice_id"`
		}{
			ID:        customerID,
			InvoiceID: invoiceID,
		},
	)
	if err != nil {
		return CustomerActionResult{
			Success:    false,
			CustomerID: 0,
		}, err
	}

	return CustomerActionResult{
		Success:    res.GetIntVal("customer_id") > 0,
		CustomerID: int64(res.GetIntVal("customer_id")),
	}, err
}
