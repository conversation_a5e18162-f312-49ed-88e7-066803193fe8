package store_connect

import (
	"errors"
	"fmt"
	"strconv"
)

type MagentoResponse map[string]interface{}

func (r MagentoResponse) GetIntVal(key string) int {
	var val int
	// check key types
	switch r[key].(type) {
	case int:
		val = r[key].(int)
	case float64:
		val = int(r[key].(float64))
	case string:
		var err error
		_val := r[key].(string)
		val, err = strconv.Atoi(_val)
		if err != nil {
			return 0
		}
	default:
		val = 0
	}

	return val
}

func (r MagentoResponse) GetStringVal(key string) string {
	val, ok := r[key].(string)
	if !ok {
		return ""
	}

	return val
}

func (r MagentoResponse) GetMap(key string) map[string]string {
	val, ok := r[key].(map[string]interface{})
	if !ok {
		return map[string]string{}
	}

	var res = make(map[string]string)
	for k, v := range val {
		res[k] = fmt.Sprintf("%v", v)
	}

	return res
}

func (r MagentoResponse) GetFloatVal(key string) float64 {
	var res float64
	// check key types
	switch r[key].(type) {
	case int:
		res = float64(r[key].(int))
	case float64:
		res = r[key].(float64)
	case string:
		_val, ok2 := r[key].(string)
		if !ok2 {
			return 0
		}
		var err error
		res, err = strconv.ParseFloat(_val, 64)
		if err != nil {
			return 0
		}
	default:
		res = 0
	}

	return res
}

func (r MagentoResponse) GetBoolVal(key string) bool {
	val, ok := r[key].(bool)
	if !ok {
		return false
	}

	return val
}

func (r MagentoResponse) IsSuccess() bool {
	return r.GetBoolVal("success")
}

func (r MagentoResponse) GetError() error {
	if r.IsSuccess() {
		return nil
	}

	errorMsg, ok := r["error"].(string)
	if ok && errorMsg != "" {
		return errors.New(errorMsg)
	}

	return errors.New("unknown error")
}
