package store_connect

import (
	"errors"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/packages/magento-core/api"
	"strings"
)

var _ StoreActionsConnect = (*ThemeAPIStoreConnector)(nil)

const (
	StoreSendContactMessage       MagentoEndpoint = "store_contacts/sendMessage"
	StoreSendEmailTemplateMessage MagentoEndpoint = "store_contacts/sendEmailTemplate"
	StoreNewsletterSubscribe      MagentoEndpoint = "store_newsletter/subscribe"
	StoreNewsletterUnsubscribe    MagentoEndpoint = "store_newsletter/unsubscribe"
)

type ThemeAPIStoreConnector struct {
	apiConnector *api.ExternalApi
}

func (t ThemeAPIStoreConnector) Init(baseURL string, apiSecret string) (StoreActionsConnect, error) {
	if strings.HasPrefix(baseURL, "http") == false {
		return nil, errors.New("invalid url: " + baseURL)
	} else if len(apiSecret) < 1 {
		return nil, errors.New("magento store connect invalid api secret")
	}

	return &ThemeAPIStoreConnector{
		apiConnector: &api.ExternalApi{
			BaseUrl: baseURL,
			Headers: map[string]string{
				AuthThemeHeaderKey:        apiSecret,
				APICallerServiceHeaderKey: StoreAPIService,
				"Content-Type":            "application/json",
			},
		},
	}, nil
}

func (t ThemeAPIStoreConnector) SendContactMessage(data model.ContactInput) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(StoreSendContactMessage),
		data,
	)
	if err != nil {
		return false, err
	}

	return res.IsSuccess(), nil
}

func (t ThemeAPIStoreConnector) NewsletterSubscribe(email string) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(StoreNewsletterSubscribe),
		struct {
			Email string `json:"email"`
		}{
			Email: email,
		},
	)
	if err != nil {
		return false, err
	}

	return res.IsSuccess(), nil
}

func (t ThemeAPIStoreConnector) NewsletterUnsubscribe(id int64, code string) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(StoreNewsletterUnsubscribe),
		struct {
			ID   int64  `json:"id"`
			Code string `json:"code"`
		}{
			ID:   id,
			Code: code,
		},
	)
	if err != nil {
		return false, err
	}

	return res.IsSuccess(), nil
}

func (t ThemeAPIStoreConnector) SendEmailTemplateMessage(
	template string,
	to string,
	data map[string]string,
) (bool, error) {
	res, err := makePost(
		t.apiConnector,
		string(StoreSendEmailTemplateMessage),
		struct {
			Template string            `json:"template_id"`
			To       string            `json:"to"`
			Data     map[string]string `json:"data"`
		}{
			Template: template,
			To:       to,
			Data:     data,
		},
	)
	if err != nil {
		return false, err
	}

	return res.IsSuccess(), nil
}
