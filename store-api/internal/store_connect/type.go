package store_connect

import "praktis.bg/store-api/graphql/model"

type MagentoEndpoint string

type MethodResponse struct {
	Code  string
	Title string
	Price float64
}

type StoreCartConnect interface {
	Init(baseURL string, apiSecret string) (StoreCartConnect, error)
	AddToCart(
		cartId string,
		productSku string,
		quantity float64,
	) (bool, error)
	RecalculateTotals(cartId string) (bool, error)
	GetAvailableShippingMethods(
		cartId string,
	) ([]*model.AvailableShippingMethod, error)
	RemoveFromCart(
		cartId string,
		productSku string,
	) (bool, error)
	UpdateQuantity(
		cartId string,
		productSku string,
		quantity float64,
	) (bool, error)
	ApplyCouponCode(
		cartId string,
		couponCode string,
		customerId int64,
	) (bool, error)
	PlaceOrder(cartId string, data model.NewOrderInput) (PlaceOrderResponse, error)
}

type PlaceOrderResponse struct {
	Order struct {
		OrderID     int
		IncrementID string
	}
	Redirect struct {
		RedirectURL string
		Params      []*model.MapValue
	}
}

type CustomerActionResult struct {
	Success    bool  `json:"success"`
	CustomerID int64 `json:"customer_id"`
}

type CustomerAddressActionResult struct {
	Success    bool  `json:"success"`
	CustomerID int64 `json:"customer_id"`
	AddressID  int64 `json:"address_id"`
}

type CustomerInvoiceActionResult struct {
	Success    bool  `json:"success"`
	CustomerID int64 `json:"customer_id"`
	InvoiceID  int64 `json:"invoice_id"`
}

type CustomerRegisterResult struct {
	RequireConfirmation bool  `json:"require_confirmation"`
	CustomerID          int64 `json:"customer_id"`
}

type StoreCustomerConnect interface {
	Init(baseURL string, apiSecret string) (StoreCustomerConnect, error)
	// auth
	Register(data model.CustomerRegistrationData) (CustomerRegisterResult, error)
	Login(email, pass string) (CustomerActionResult, error)
	ForgotPassword(email string) (bool, error)
	ResetPassword(customerID int64, resetToken, newPassword string) (bool, error)
	// account and profile
	UpdatePassword(email, oldPassword, newPassword string) (CustomerActionResult, error)
	UpdateInfo(customerID int64, data *model.CustomerUpdateInput) (CustomerActionResult, error)
	CreateCustomerAddress(customerID int64, data model.CustomerAddressInput) (CustomerAddressActionResult, error)
	UpdateCustomerAddress(customerID, addressID int64, data model.CustomerAddressInput) (CustomerAddressActionResult, error)
	DeleteCustomerAddress(customerID, addressID int64) (CustomerActionResult, error)
	CreateCustomerInvoice(customerID int64, data model.InvoiceInput) (CustomerInvoiceActionResult, error)
	UpdateCustomerInvoice(customerID, invoiceID int64, data model.InvoiceInput) (CustomerInvoiceActionResult, error)
	DeleteCustomerInvoice(customerID, invoiceID int64) (CustomerActionResult, error)
}

type StoreActionsConnect interface {
	Init(baseURL string, apiSecret string) (StoreActionsConnect, error)
	SendContactMessage(data model.ContactInput) (bool, error)
	SendEmailTemplateMessage(template, to string, data map[string]string) (bool, error)
	NewsletterSubscribe(email string) (bool, error)
	NewsletterUnsubscribe(id int64, code string) (bool, error)
}

type StoreContentConnect interface {
	Init(baseURL string, apiSecret string) (StoreContentConnect, error)
	GetPageContent(pageID string) (string, error)
	GetBlockContent(blockID string) (string, error)
}
