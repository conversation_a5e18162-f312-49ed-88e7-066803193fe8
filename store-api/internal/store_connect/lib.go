package store_connect

import (
	"encoding/json"
	"errors"
	"fmt"
	core_api "praktis.bg/store-api/packages/magento-core/api"
)

const AuthThemeHeaderKey = "pfg-auth-service-token"
const APICallerServiceHeaderKey = "x-pfg-service"
const StoreAPIService = "graphql-api"

func makePost(api *core_api.ExternalApi, path string, data interface{}) (MagentoResponse, error) {
	result := make(MagentoResponse)
	if api == nil {
		return result, errors.New("api is nil")
	}

	body, err := api.Post(path, data)
	if err != nil {
		return result, err
	}

	if err2 := json.Unmarshal(body, &result); err2 != nil {
		return result, fmt.Errorf("магенто грешка: %w", err2)
	}

	return result, nil
}
