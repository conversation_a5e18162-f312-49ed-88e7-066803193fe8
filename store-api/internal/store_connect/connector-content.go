package store_connect

import (
	"errors"
	"praktis.bg/store-api/packages/magento-core/api"
	"strings"
)

var _ StoreContentConnect = (*ThemeAPIStoreContentConnector)(nil)

const (
	GetCMSPage  MagentoEndpoint = "store_content/getPage"
	GetCMSBlock MagentoEndpoint = "store_content/getBlock"
)

type ThemeAPIStoreContentConnector struct {
	apiConnector *api.ExternalApi
}

func (t *ThemeAPIStoreContentConnector) Init(
	baseURL string,
	apiSecret string,
) (StoreContentConnect, error) {
	if strings.HasPrefix(baseURL, "http") == false {
		return nil, errors.New("invalid url: " + baseURL)
	} else if len(apiSecret) < 1 {
		return nil, errors.New("magento store connect invalid api secret")
	}

	return &ThemeAPIStoreContentConnector{
		apiConnector: &api.ExternalApi{
			BaseUrl: baseURL,
			Headers: map[string]string{
				AuthThemeHeaderKey:        apiSecret,
				APICallerServiceHeaderKey: StoreAPIService,
				"Content-Type":            "application/json",
			},
		},
	}, nil
}

func (t *ThemeAPIStoreContentConnector) GetPageContent(pageID string) (string, error) {
	res, err := makePost(
		t.apiConnector,
		string(GetCMSPage),
		struct {
			ID string `json:"id"`
		}{
			ID: pageID,
		},
	)
	if err != nil {
		return "", err
	}

	return res.GetStringVal("content"), nil
}

func (t *ThemeAPIStoreContentConnector) GetBlockContent(blockID string) (string, error) {
	res, err := makePost(
		t.apiConnector,
		string(GetCMSBlock),
		struct {
			ID string `json:"id"`
		}{
			ID: blockID,
		},
	)
	if err != nil {
		return "", err
	}

	return res.GetStringVal("content"), nil
}
