package config

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"os"
	"path/filepath"
	"time"
)

type MySQLConfig struct {
	User     string `yaml:"user"  validate:"required"`
	Pass     string `yaml:"pass"  validate:"required"`
	Host     string `yaml:"host"  validate:"required"`
	Post     int    `yaml:"port"  validate:"required"`
	Database string `yaml:"database"  validate:"required"`
}

type CacheConfig struct {
	Pass     string `yaml:"pass"`
	Host     string `yaml:"host" validate:"required"`
	Port     int    `yaml:"port" validate:"required"`
	Database int    `yaml:"database"`
}

const ProductUseAttributesInCache = false
const UseStoreCodeInCache = true

func (dbConf MySQLConfig) GetDNS() string {
	return fmt.Sprintf(
		"%s:%s@tcp(%s:%d)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbConf.User,
		dbConf.Pass,
		dbConf.Host,
		dbConf.Post,
		dbConf.Database,
	)
}

type CustomerConfig struct {
	RedisDB       string `yaml:"redis_db" validate:"omitempty"`
	SessionLength int    `yaml:"session_length" validate:"omitempty"`
}

type MainConfig struct {
	Version string
	Api     struct {
		Port int    `yaml:"port" validate:"required"`
		Mode string `yaml:"mode" validate:"required"`
		// backend API url
		StoreAPIUrl string `yaml:"store_api_url" validate:"required"`
		// time in seconds
		CartTokenExpiration int64 `yaml:"cart_token_expiration" validate:"required"`

		CertificatesRootPath string   `yaml:"certificates_root_path" validate:"required"`
		ProductsImageUrl     string   `yaml:"products_image_url"`
		AllowOrigins         []string `yaml:"allow_origins"`
	}
	Customer CustomerConfig `yaml:"customer" validate:"omitempty"`
	Security struct {
		JWTSecret     string `yaml:"jwt_secret" validate:"required"`
		CaptchaSecret string `yaml:"captcha_secret" validate:"required"`
	}
	Search struct {
		Endpoint string `yaml:"endpoint" validate:"required"`
	} `yaml:"search" validate:"required"`
	Database MySQLConfig `yaml:"database" validate:"required"`
	Cache    CacheConfig `yaml:"cache" validate:"required"`
}

func (c MainConfig) IsDevMode() bool {
	return c.Api.Mode == "dev"
}

func (c MainConfig) CartTokenExpirationSeconds() time.Duration {
	return time.Duration(c.Api.CartTokenExpiration) * time.Second
}

var config MainConfig

func LoadConfig(file string) {
	cwd, err := os.Getwd()
	core_utils.StopOnError(err)

	err = core_utils.LoadStructFromFile(&config, filepath.Join(cwd, file))
	core_utils.StopOnError(err)

	if config.Api.Port < 1 {
		config.Api.Port = 9420
	}

	core_utils.AllowNotice()

	if config.IsDevMode() {
		core_utils.AllowDebug()
	}

	now := time.Now()
	config.Version = fmt.Sprintf("version-build-%s", now.Format("20060102-15:04:05"))

	core_utils.StopOnError(err)
}

func GetConfig() MainConfig {
	return config
}
