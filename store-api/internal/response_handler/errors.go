package response_handler

import (
	"context"
	"github.com/99designs/gqlgen/graphql"
	"github.com/go-playground/validator/v10"
	"github.com/vektah/gqlparser/v2/gqlerror"
)

func AddErrorsToResponse(ctx context.Context, err error) error {
	errors, ok := err.(validator.ValidationErrors)
	if !ok {
		return err
	}

	for _, err := range errors {
		graphql.AddError(ctx, &gqlerror.Error{
			Message: getMessage(err),
			Extensions: map[string]interface{}{
				"field": err.Field(),
			},
		})
	}

	return gqlerror.<PERSON>rf("Validation errors")
}

func getMessage(err validator.FieldError) string {
	switch err.Tag() {
	case "required":
		return "Field is required"
	case "email":
		return "Field must be a valid email"
	case "min":
		return "Field must be at least " + err.Param() + " characters long"
	case "max":
		return "Field must be at most " + err.Param() + " characters long"
	default:
		return "Field is invalid"
	}
}

const NotFoundError = "not found"
