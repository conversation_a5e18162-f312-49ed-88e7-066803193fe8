package response_handler

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

const MagentoRequestContextKey = "magento_request_context"

func AddToRequestContext(
	key string,
	value interface{},
) gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.WithValue(
			c.Request.Context(),
			key,
			value,
		)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

func CtxStore(ctx context.Context) *magento_core.StoreClient {
	storeE := &magento_core.StoreClient{}
	magentoCtx := ctx.Value(MagentoRequestContextKey)
	if magentoCtx == nil {
		core_utils.ErrorWarning(
			fmt.Errorf("could not retrieve mage-store from context"),
		)
		return storeE
	}

	magentoStore, ok := magentoCtx.(*magento_core.StoreClient)
	if !ok {
		core_utils.ErrorWarning(
			fmt.<PERSON><PERSON><PERSON>("mage-store context has wrong type"),
		)
		return storeE
	}

	return magentoStore
}
