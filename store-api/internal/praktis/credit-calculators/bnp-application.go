package credit_calculators

import (
	"bytes"
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"strings"
	"text/template"
	"time"
)

// extractEGNFromPaymentData extracts EGN from payment data (supports both PHP serialized and JSON)
func extractEGNFromPaymentData(paymentData string) string {
	// Try JSON first (for backward compatibility)
	var paymentInfo map[string]interface{}
	if err := json.Unmarshal([]byte(paymentData), &paymentInfo); err == nil {
		// Try nested customer_data structure first (Go implementation)
		if customerData, ok := paymentInfo["customer_data"].(map[string]interface{}); ok {
			if egn, ok := customerData["egn"].(string); ok && egn != "" {
				return egn
			}
		}
		// Try root level pin field (Magento compatibility)
		if pin, ok := paymentInfo["pin"].(string); ok && pin != "" {
			return pin
		}
		return ""
	}

	// If JSON parsing fails, try PHP serialized data
	if strings.HasPrefix(paymentData, "a:") {
		// Extract EGN from PHP serialized data using string search
		// Look for the pin field first (most reliable)
		if pinMatch := extractPHPSerializedValue(paymentData, "pin"); pinMatch != "" {
			return pinMatch
		}
		// Fallback to nested customer_data egn
		if egnMatch := extractPHPSerializedValue(paymentData, "egn"); egnMatch != "" {
			return egnMatch
		}
	}

	return ""
}

// extractPHPSerializedValue extracts a string value from PHP serialized data
func extractPHPSerializedValue(data, key string) string {
	// This is a simplified extraction - in production you'd want a proper PHP unserializer
	// For now, we'll use string matching for the specific fields we need

	// Look for the key in the serialized data
	keySearch := fmt.Sprintf(`s:%d:"%s";`, len(key), key)
	keyIndex := strings.Index(data, keySearch)
	if keyIndex == -1 {
		return ""
	}

	// Find the value after the key
	valueStart := keyIndex + len(keySearch)
	if valueStart >= len(data) {
		return ""
	}

	// Look for the value pattern: s:length:"value";
	if !strings.HasPrefix(data[valueStart:], "s:") {
		return ""
	}

	// Extract the length
	lengthStart := valueStart + 2
	lengthEnd := strings.Index(data[lengthStart:], ":")
	if lengthEnd == -1 {
		return ""
	}
	lengthEnd += lengthStart

	// Extract the value
	valueQuoteStart := lengthEnd + 2 // Skip :"
	if valueQuoteStart >= len(data) || data[valueQuoteStart-1] != '"' {
		return ""
	}

	valueQuoteEnd := strings.Index(data[valueQuoteStart:], `";`)
	if valueQuoteEnd == -1 {
		return ""
	}

	return data[valueQuoteStart : valueQuoteStart+valueQuoteEnd]
}

// extractAddressFromPaymentData extracts address from payment data (supports both PHP serialized and JSON)
func extractAddressFromPaymentData(paymentData string) string {
	// Try JSON first (for backward compatibility)
	var paymentInfo map[string]interface{}
	if err := json.Unmarshal([]byte(paymentData), &paymentInfo); err == nil {
		// Try nested customer_data structure (Go implementation)
		if customerData, ok := paymentInfo["customer_data"].(map[string]interface{}); ok {
			if address, ok := customerData["address"].(string); ok && address != "" {
				return address
			}
		}
		return ""
	}

	// If JSON parsing fails, try PHP serialized data
	if strings.HasPrefix(paymentData, "a:") {
		// Extract address from PHP serialized data
		if addressMatch := extractPHPSerializedValue(paymentData, "address"); addressMatch != "" {
			return addressMatch
		}
	}

	return ""
}

const (
	XMLPathApplicationEmail = "stenik_leasingjetcredit/config/application_email"
	XMLPathSendCopyTo       = "stenik_leasingjetcredit/config/send_copy_to"
)

type BNPApplicationData struct {
	Order             *sales.MagentoOrder
	CustomerFirstName string
	CustomerLastName  string
	CustomerPhone     string
	CustomerEGN       string
	CustomerAddress   string
	Subtotal          string
	Items             string
	OrderItems        string
	PaymentInfo       map[string]interface{}
}

// SubmitBNPApplication submits a BNP Paribas application via email
func SubmitBNPApplication(orderNumber string) error {
	startTime := time.Now()
	log.Printf("[INFO] BNP: === APPLICATION SUBMISSION START ===")
	log.Printf("[INFO] BNP: Order Number: %s", orderNumber)
	log.Printf("[INFO] BNP: Timestamp: %s", startTime.Format(time.RFC3339))

	// Validate input parameters
	if orderNumber == "" {
		log.Printf("[ERROR] BNP: Order number is empty")
		return fmt.Errorf("order number cannot be empty")
	}

	// Load order with detailed logging
	log.Printf("[DEBUG] BNP: Loading order from database...")
	order, err := sales.NewOrderRepository(praktis.GetDbClient()).GetByIncrementID(orderNumber)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to load order %s: %v", orderNumber, err)
		log.Printf("[ERROR] BNP: Error type: %T", err)
		return fmt.Errorf("failed to load order: %w", err)
	}

	log.Printf("[INFO] BNP: Order loaded successfully - ID: %d, Status: %s, Total: %.2f",
		order.EntityID, order.Status.String, order.GrandTotal.Float64)

	// Manually load the payment if it's not loaded by GORM preload (same fix as in post-order processing)
	if order.Payment == nil {
		log.Printf("[DEBUG] BNP: Payment not loaded by preload, manually loading payment for order %d", order.EntityID)
		var payment sales.MagentoOrderPayment
		paymentErr := praktis.GetDbClient().Where("parent_id = ?", order.EntityID).First(&payment).Error
		if paymentErr != nil {
			log.Printf("[ERROR] BNP: Failed to manually load payment for order %d: %v", order.EntityID, paymentErr)
			log.Printf("[ERROR] BNP: Order %s has no payment information", orderNumber)
			return fmt.Errorf("order has no payment information")
		} else {
			order.Payment = &payment
			log.Printf("[DEBUG] BNP: Payment manually loaded successfully - ID: %d, Method: %s", payment.EntityID, payment.Method)
		}
	} else {
		log.Printf("[DEBUG] BNP: Payment already loaded by preload - ID: %d, Method: %s", order.Payment.EntityID, order.Payment.Method)
	}

	// Validate payment method with detailed logging
	log.Printf("[DEBUG] BNP: Validating payment method...")
	if order.Payment == nil {
		log.Printf("[ERROR] BNP: Order %s has no payment information after manual loading attempt", orderNumber)
		return fmt.Errorf("order has no payment information")
	}

	log.Printf("[DEBUG] BNP: Order payment method: %s", order.Payment.Method)
	if order.Payment.Method != "stenik_leasingjetcredit" {
		log.Printf("[ERROR] BNP: Order %s payment method is '%s', expected 'stenik_leasingjetcredit'",
			orderNumber, order.Payment.Method)
		return fmt.Errorf("order payment method is not BNP Paribas leasing")
	}

	log.Printf("[INFO] BNP: Payment method validation successful")

	// Get payment additional information from quote payment with detailed logging
	log.Printf("[DEBUG] BNP: Loading payment additional information...")
	var paymentInfo map[string]interface{}
	var quotePayment sales.MagentoQuotePayment

	log.Printf("[DEBUG] BNP: Searching for quote payment with quote_id: %d", order.QuoteID.Int32)
	err = praktis.GetDbClient().Where("quote_id = ?", order.QuoteID.Int32).First(&quotePayment).Error
	if err == nil && quotePayment.AdditionalInformation != "" {
		log.Printf("[DEBUG] BNP: Found quote payment with additional information (%d bytes)",
			len(quotePayment.AdditionalInformation))

		err = json.Unmarshal([]byte(quotePayment.AdditionalInformation), &paymentInfo)
		if err != nil {
			log.Printf("[WARNING] BNP: Failed to parse payment additional information: %v", err)
			log.Printf("[DEBUG] BNP: Raw additional information: %s", quotePayment.AdditionalInformation)
			paymentInfo = make(map[string]interface{})
		} else {
			log.Printf("[INFO] BNP: Successfully parsed payment additional information")
			// Log payment info keys without sensitive data
			keys := make([]string, 0, len(paymentInfo))
			for key := range paymentInfo {
				keys = append(keys, key)
			}
			log.Printf("[DEBUG] BNP: Payment info contains keys: %v", keys)
		}
	} else {
		if err != nil {
			log.Printf("[WARNING] BNP: Failed to load quote payment: %v", err)
		} else {
			log.Printf("[WARNING] BNP: Quote payment has no additional information")
		}
		paymentInfo = make(map[string]interface{})
	}

	// Prepare application data with detailed logging
	log.Printf("[DEBUG] BNP: Preparing application data...")

	customerFirstName := getCustomerFirstName(order)
	customerLastName := getCustomerLastName(order)
	customerPhone := getCustomerPhone(order)
	customerEGN := getCustomerEGN(order)
	customerAddress := getCustomerAddress(order)
	subtotal := fmt.Sprintf("%.2f", order.Subtotal.Float64)
	items := getOrderItemsSKUs(order)
	orderItems := getOrderItemsHTML(order)

	log.Printf("[INFO] BNP: Application data prepared - Customer: %s %s, Phone: %s, EGN: %s, Subtotal: %s BGN",
		customerFirstName, customerLastName, customerPhone, customerEGN, subtotal)
	log.Printf("[DEBUG] BNP: Customer Address: %s", customerAddress)
	log.Printf("[DEBUG] BNP: Order items: %s", items)

	appData := &BNPApplicationData{
		Order:             order,
		CustomerFirstName: customerFirstName,
		CustomerLastName:  customerLastName,
		CustomerPhone:     customerPhone,
		CustomerEGN:       customerEGN,
		CustomerAddress:   customerAddress,
		Subtotal:          subtotal,
		Items:             items,
		OrderItems:        orderItems,
		PaymentInfo:       paymentInfo,
	}

	// Get email configuration with detailed logging
	log.Printf("[DEBUG] BNP: Getting email configuration...")
	recipientEmail := "<EMAIL>" // Default email for now
	log.Printf("[INFO] BNP: Application will be sent to: %s", recipientEmail)

	if recipientEmail == "" {
		log.Printf("[ERROR] BNP: BNP application email is not configured")
		return fmt.Errorf("BNP application email is not configured")
	}

	// Send application email with detailed logging
	log.Printf("[DEBUG] BNP: Sending application email...")
	err = sendBNPApplicationEmail(appData, recipientEmail, order)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to send application email: %v", err)
		log.Printf("[ERROR] BNP: Email sending error type: %T", err)
		return err
	}

	log.Printf("[INFO] BNP: Application email sent successfully")

	// Send copy emails if configured - simplified for now
	// TODO: Implement copy email configuration when store config is available

	duration := time.Since(startTime)
	log.Printf("[INFO] BNP: === APPLICATION SUBMISSION COMPLETED ===")
	log.Printf("[INFO] BNP: Order: %s, Duration: %v, Status: SUCCESS", orderNumber, duration)
	return nil
}

func getCustomerFirstName(order *sales.MagentoOrder) string {
	// Try to get from payment additional information first
	var quotePayment sales.MagentoQuotePayment
	err := praktis.GetDbClient().Where("quote_id = ?", order.QuoteID.Int32).First(&quotePayment).Error
	if err == nil && quotePayment.AdditionalInformation != "" {
		var paymentInfo map[string]interface{}
		if err := json.Unmarshal([]byte(quotePayment.AdditionalInformation), &paymentInfo); err == nil {
			// Try nested customer_data structure first (Go implementation)
			if customerData, ok := paymentInfo["customer_data"].(map[string]interface{}); ok {
				if firstName, ok := customerData["first_name"].(string); ok && firstName != "" {
					return firstName
				}
			}

			// Try to extract from combined "names" field (Magento compatibility)
			if names, ok := paymentInfo["names"].(string); ok && names != "" {
				// Split the combined name and return the first part
				parts := strings.Fields(names)
				if len(parts) > 0 {
					return parts[0]
				}
			}
		}
	}

	// Fallback to order customer data
	if order.CustomerFirstname.Valid {
		return order.CustomerFirstname.String
	}

	return ""
}

func getCustomerLastName(order *sales.MagentoOrder) string {
	// Try to get from payment additional information first
	var quotePayment sales.MagentoQuotePayment
	err := praktis.GetDbClient().Where("quote_id = ?", order.QuoteID.Int32).First(&quotePayment).Error
	if err == nil && quotePayment.AdditionalInformation != "" {
		var paymentInfo map[string]interface{}
		if err := json.Unmarshal([]byte(quotePayment.AdditionalInformation), &paymentInfo); err == nil {
			// Try nested customer_data structure first (Go implementation)
			if customerData, ok := paymentInfo["customer_data"].(map[string]interface{}); ok {
				if lastName, ok := customerData["last_name"].(string); ok && lastName != "" {
					return lastName
				}
			}

			// Try to extract from combined "names" field (Magento compatibility)
			if names, ok := paymentInfo["names"].(string); ok && names != "" {
				// Split the combined name and return the last part
				parts := strings.Fields(names)
				if len(parts) > 1 {
					return parts[len(parts)-1]
				}
			}
		}
	}

	// Fallback to order customer data
	if order.CustomerLastname.Valid {
		return order.CustomerLastname.String
	}

	return ""
}

// getCustomerPhone extracts customer phone from payment data with fallback mechanisms
func getCustomerPhone(order *sales.MagentoOrder) string {
	// Try to get from payment additional information first
	var quotePayment sales.MagentoQuotePayment
	err := praktis.GetDbClient().Where("quote_id = ?", order.QuoteID.Int32).First(&quotePayment).Error
	if err == nil && quotePayment.AdditionalInformation != "" {
		var paymentInfo map[string]interface{}
		if err := json.Unmarshal([]byte(quotePayment.AdditionalInformation), &paymentInfo); err == nil {
			// Try nested customer_data structure first (Go implementation)
			if customerData, ok := paymentInfo["customer_data"].(map[string]interface{}); ok {
				if phone, ok := customerData["phone"].(string); ok && phone != "" {
					return phone
				}
			}

			// Try root level phone field (Magento compatibility)
			if phone, ok := paymentInfo["phone"].(string); ok && phone != "" {
				return phone
			}
		}
	}

	return ""
}

// getCustomerEGN extracts customer EGN/PIN from payment data with fallback mechanisms
func getCustomerEGN(order *sales.MagentoOrder) string {
	// Try to get from order payment additional information first using direct database query
	if order.Payment != nil {
		var additionalInfo sql.NullString
		err := praktis.GetDbClient().Raw(`
			SELECT additional_information
			FROM sales_flat_order_payment
			WHERE parent_id = ?
		`, order.EntityID).Row().Scan(&additionalInfo)

		if err == nil && additionalInfo.Valid && additionalInfo.String != "" {
			egn := extractEGNFromPaymentData(additionalInfo.String)
			if egn != "" {
				return egn
			}
		}
	}

	// Fallback: Try to get from quote payment additional information
	var quotePayment sales.MagentoQuotePayment
	err := praktis.GetDbClient().Where("quote_id = ?", order.QuoteID.Int32).First(&quotePayment).Error
	if err == nil && quotePayment.AdditionalInformation != "" {
		egn := extractEGNFromPaymentData(quotePayment.AdditionalInformation)
		if egn != "" {
			return egn
		}
	}

	return ""
}

// getCustomerAddress extracts customer address from payment data
func getCustomerAddress(order *sales.MagentoOrder) string {
	// Try to get from order payment additional information first using direct database query
	if order.Payment != nil {
		var additionalInfo sql.NullString
		err := praktis.GetDbClient().Raw(`
			SELECT additional_information
			FROM sales_flat_order_payment
			WHERE parent_id = ?
		`, order.EntityID).Row().Scan(&additionalInfo)

		if err == nil && additionalInfo.Valid && additionalInfo.String != "" {
			address := extractAddressFromPaymentData(additionalInfo.String)
			if address != "" {
				return address
			}
		}
	}

	// Fallback: Try to get from quote payment additional information
	var quotePayment sales.MagentoQuotePayment
	err := praktis.GetDbClient().Where("quote_id = ?", order.QuoteID.Int32).First(&quotePayment).Error
	if err == nil && quotePayment.AdditionalInformation != "" {
		address := extractAddressFromPaymentData(quotePayment.AdditionalInformation)
		if address != "" {
			return address
		}
	}

	return ""
}

func getOrderItemsSKUs(order *sales.MagentoOrder) string {
	var skus []string

	// Load order items
	var items []*sales.MagentoOrderItem
	praktis.GetDbClient().Where("order_id = ?", order.EntityID).Find(&items)

	for _, item := range items {
		skus = append(skus, item.Sku)
	}

	return strings.Join(skus, ", ")
}

func getOrderItemsHTML(order *sales.MagentoOrder) string {
	var items []*sales.MagentoOrderItem
	praktis.GetDbClient().Where("order_id = ?", order.EntityID).Find(&items)

	var html strings.Builder
	html.WriteString("<table border='1' cellpadding='5' cellspacing='0'>")
	html.WriteString("<tr><th>Product</th><th>SKU</th><th>Qty</th><th>Price</th></tr>")

	for _, item := range items {
		html.WriteString("<tr>")
		html.WriteString(fmt.Sprintf("<td>%s</td>", item.Sku)) // Use SKU as product name for now
		html.WriteString(fmt.Sprintf("<td>%s</td>", item.Sku))
		html.WriteString(fmt.Sprintf("<td>%.0f</td>", item.QtyOrdered))
		html.WriteString(fmt.Sprintf("<td>%.2f BGN</td>", item.Price))
		html.WriteString("</tr>")
	}

	html.WriteString("</table>")
	return html.String()
}

const emailTemplate = `
Subject: BNP Paribas Leasing Application - Order {{.Order.IncrementID}}

Dear BNP Paribas Team,

A new leasing application has been submitted through our online store.

Order Details:
- Order Number: {{.Order.IncrementID}}
- Order Date: {{if .Order.CreatedAt.Valid}}{{.Order.CreatedAt.Time.Format "2006-01-02 15:04:05"}}{{else}}N/A{{end}}
- Subtotal: {{.Subtotal}} BGN

Customer Information:
- Name: {{.CustomerFirstName}} {{.CustomerLastName}}
- Email: {{.Order.CustomerEmail}}
{{if .CustomerPhone}}- Phone: {{.CustomerPhone}}{{end}}
{{if .CustomerEGN}}- EGN/PIN: {{.CustomerEGN}}{{end}}
{{if .CustomerAddress}}- Address: {{.CustomerAddress}}{{end}}

Products:
{{.Items}}

{{if .PaymentInfo.loan}}
Loan Details:
{{range $key, $value := .PaymentInfo.loan}}
- {{$key}}: {{$value}}
{{end}}
{{end}}

{{if .PaymentInfo.customer_data}}
Additional Customer Data:
{{range $key, $value := .PaymentInfo.customer_data}}
{{if $value}}- {{$key}}: {{$value}}{{end}}
{{end}}
{{end}}

Order Items Details:
{{.OrderItems}}

Best regards,
Praktis.bg Team
`

func sendBNPApplicationEmail(data *BNPApplicationData, recipientEmail string, store *sales.MagentoOrder) error {
	log.Printf("[INFO] BNP: Sending application email to %s", recipientEmail)

	// Parse email template
	tmpl, err := template.New("bnp-application").Parse(emailTemplate)
	if err != nil {
		return fmt.Errorf("failed to parse email template: %w", err)
	}

	// Execute template
	var emailBody bytes.Buffer
	err = tmpl.Execute(&emailBody, data)
	if err != nil {
		return fmt.Errorf("failed to execute email template: %w", err)
	}

	// Simplified email sending for now
	// TODO: Implement proper email sending when email service is available
	log.Printf("[INFO] BNP: Would send email to %s with content:\n%s", recipientEmail, emailBody.String())

	log.Printf("[INFO] BNP: Application email sent successfully to %s", recipientEmail)
	return nil
}


