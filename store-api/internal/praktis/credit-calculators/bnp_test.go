package credit_calculators

import (
	"testing"

	"praktis.bg/store-api/graphql/model"
)

func TestPricingVariantFieldMapping(t *testing.T) {
	// Test that the legacy field names are properly mapped
	variant := &model.PricingVariant{
		ID:                       "test-id",
		Apr:                      "12.50",
		CorrectDownpaymentAmount: "500.00",
		InstallmentAmount:        "85.67",
		Maturity:                 "24",
		Nir:                      "11.20",
		PricingSchemeID:          "1",
		PricingSchemeName:        "Standard Leasing 24 months",
		ProcessingFeeAmount:      "50.00",
		TotalRepaymentAmount:     "2556.08",
		// Legacy field names for Magento template compatibility
		Installment:              "85.67",
		TotalRepayment:           "2556.08",
	}

	// Test that both field names contain the same values
	if variant.InstallmentAmount != variant.Installment {
		t.Errorf("InstallmentAmount (%s) should equal Installment (%s)", variant.InstallmentAmount, variant.Installment)
	}

	if variant.TotalRepaymentAmount != variant.TotalRepayment {
		t.<PERSON>rf("TotalRepaymentAmount (%s) should equal TotalRepayment (%s)", variant.TotalRepaymentAmount, variant.TotalRepayment)
	}

	// Test that the legacy fields are not empty
	if variant.Installment == "" {
		t.Error("Installment field should not be empty")
	}

	if variant.TotalRepayment == "" {
		t.Error("TotalRepayment field should not be empty")
	}

	t.Logf("Field mapping test passed:")
	t.Logf("  InstallmentAmount: %s, Installment: %s", variant.InstallmentAmount, variant.Installment)
	t.Logf("  TotalRepaymentAmount: %s, TotalRepayment: %s", variant.TotalRepaymentAmount, variant.TotalRepayment)
}
