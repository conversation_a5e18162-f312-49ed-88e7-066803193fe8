package credit_calculators

import (
	"crypto/tls"
	"fmt"
	"io"
	"net/http"
	"time"
)

func GetTBISchemaResponse(cid string) ([]byte, error) {
	transport := &http.Transport{
		TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		MaxIdleConns:    10,
		IdleConnTimeout: 30 * time.Second,
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   30 * time.Second,
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 3 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	resp, err := client.Get(
		fmt.Sprintf("https://bnpl.tbibank.support/function/getproduct.php?cid=%s",
			cid,
		),
	)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	return io.ReadAll(resp.Body)
}
