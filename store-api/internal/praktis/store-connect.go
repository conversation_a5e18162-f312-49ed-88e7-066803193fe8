package praktis

import (
	"errors"
	"praktis.bg/store-api/internal/config"
	store_connect2 "praktis.bg/store-api/internal/store_connect"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

const APIConnectSecretConfigPath magento_core.ConfigValuePath = "pfg_theme/config/auth_token"

func GetNewCartConnector() (store_connect2.StoreCartConnect, error) {
	store := magento_core.GetStoreClient().GetStore()
	secret := store.GetConfig(APIConnectSecretConfigPath, "")
	if secret == "" {
		return nil, errors.New("missing secret")
	}

	connector, err := (&store_connect2.ThemeAPICartConnector{}).Init(
		config.GetConfig().Api.StoreAPIUrl,
		store.GetConfig(APIConnectSecretConfigPath, ""),
	)
	if err != nil {
		return nil, err
	}

	return connector, err
}

func GetNewStoreConnector() (store_connect2.StoreActionsConnect, error) {
	store := magento_core.GetStoreClient().GetStore()
	secret := store.GetConfig(APIConnectSecretConfigPath, "")
	if secret == "" {
		return nil, errors.New("missing secret")
	}

	connector, err := (&store_connect2.ThemeAPIStoreConnector{}).Init(
		config.GetConfig().Api.StoreAPIUrl,
		store.GetConfig(APIConnectSecretConfigPath, ""),
	)
	if err != nil {
		return nil, err
	}

	return connector, err
}

func GetNewCustomerConnector() (store_connect2.StoreCustomerConnect, error) {
	store := magento_core.GetStoreClient().GetStore()
	secret := store.GetConfig(APIConnectSecretConfigPath, "")
	if secret == "" {
		return nil, errors.New("missing secret")
	}

	connector, err := (&store_connect2.ThemeAPICustomerConnector{}).Init(
		config.GetConfig().Api.StoreAPIUrl,
		store.GetConfig(APIConnectSecretConfigPath, ""),
	)
	if err != nil {
		return nil, err
	}

	return connector, err
}

func GetNewStoreContentConnector() (store_connect2.StoreContentConnect, error) {
	store := magento_core.GetStoreClient().GetStore()
	secret := store.GetConfig(APIConnectSecretConfigPath, "")
	if secret == "" {
		return nil, errors.New("missing secret")
	}

	connector, err := (&store_connect2.ThemeAPIStoreContentConnector{}).Init(
		config.GetConfig().Api.StoreAPIUrl,
		store.GetConfig(APIConnectSecretConfigPath, ""),
	)
	if err != nil {
		return nil, err
	}

	return connector, err
}
