package catalog

import (
	"encoding/json"
	"fmt"
	cache "github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
	"strings"
)

func CategoryToModelCategory(category *mage_entity.CategoryEntity) *model.Category {
	result := &model.Category{
		ID:          int64(category.EntityID),
		URL:         category.UrlPath,
		Name:        category.Name,
		Description: category.GetAttributeValueP("description"),
		Image: &model.Image{
			Src:       "",
			MobileSrc: core_utils.ToStringPointer(""),
			Alt:       core_utils.ToStringPointer(""),
			Title:     core_utils.ToStringPointer(""),
		},
		Widgets: make([]model.CategoryWidget, 0),
		Icon: &model.Image{
			Alt: core_utils.ToStringPointer(category.Name),
			Src: category.GetImageAttribute("thumbnail"),
		},
	}

	result.ID = int64(category.EntityID)
	result.Name = category.Name
	result.URL = category.UrlPath
	result.Description = category.GetAttributeValueP("description")

	return result
}

func GetCategoryBanner(categoryId int64) (map[string]string, error) {
	banner := make(map[string]interface{})
	db := magento_core.GetStoreClient().DbConn().
		Select("title, image, mobile_image, link").
		Table("pfg_catalogimages_catalogimage").
		Where("status = 1").
		Where(fmt.Sprintf(
			"category_select like '%%|%d|%%'", categoryId,
		)).
		Where("(active_from_date IS NULL OR active_from_date <= CURDATE()) AND (active_to_date IS NULL OR active_to_date >= CURDATE())").
		Order("catalogimage_id DESC").
		Scan(&banner)

	store := praktis.GetPraktisStore()

	return map[string]string{
		"title":        types.ToStr(banner["title"]),
		"image":        store.GetMedialUrl(types.ToStr(banner["image"])),
		"mobile_image": store.GetMedialUrl(types.ToStr(banner["mobile_image"])),
		"link":         types.ToStr(banner["link"]),
	}, db.Error
}

func GetCachedCategory(id types.EntityID) (*mage_entity.CategoryEntity, error) {
	category := &mage_entity.CategoryEntity{
		EntityID: id,
	}
	category.SetCacheKey(fmt.Sprintf("praktis_category:%d", id))
	cacheClient := praktis.GetCacheClient()
	if cacheClient.MustExists(category.CacheKey()) {
		err := cacheClient.LoadObj(category)
		if err != nil {
			return nil, err
		}

		if category.UrlPath == "" {
			return nil, fmt.Errorf("invalid category in cache")
		}

		return category, nil
	}

	return nil, nil
}

func GetCategoryBreadcrumbs(id types.EntityID) []*model.Breadcrumb {
	cacheKey := fmt.Sprintf("praktis_category_breadcrumbs:%d", id)
	result := []*model.Breadcrumb{
		{
			Label: "Начало",
			URL:   "/",
		},
	}
	cacheClient := praktis.GetCacheClient()
	if cacheClient.MustExists(cacheKey) {
		rawData, err := cacheClient.Get(cacheKey)
		if err == nil {
			err = json.Unmarshal([]byte(rawData), &result)
			if err == nil {
				return result
			} else {
				core_utils.ErrorWarning(err)
			}
		}
	}

	entity, err := GetCategoryEntity(id)
	if err != nil {
		core_utils.ErrorWarning(err)
		return result
	}

	parents := entity.Path.GetParentIds()
	if len(parents) == 0 {
		return result
	}

	if len(parents) > 2 {
		viableParents := parents[2:]
		if len(viableParents) > 0 {
			for _, parentId := range viableParents {
				parent, err := GetCategoryEntity(
					types.ToEntityID(parentId),
				)
				if err != nil {
					core_utils.ErrorWarning(err)
					continue
				}

				result = append(result, &model.Breadcrumb{
					Label:    parent.Name,
					URL:      "/" + strings.TrimRight(parent.UrlPath, "/"),
					Siblings: getSiblingsBreadcrumbs(parent),
					Children: getChildrenBreadcrumbs(parent),
				})
			}
		}
	}

	result = append(
		result,
		&model.Breadcrumb{
			Label:    entity.Name,
			URL:      "#",
			Siblings: getSiblingsBreadcrumbs(entity),
			Children: getChildrenBreadcrumbs(entity),
		},
	)

	cacheData, _ := json.Marshal(&result)
	if len(cacheData) > 0 {
		_ = cacheClient.Save(cacheKey, cacheData, cache.InfiniteTTL)
	}

	return result
}

func andCond(vals []string) string {
	return strings.Join(vals, " && ")
}

func getSiblingsBreadcrumbs(c *mage_entity.CategoryEntity) []*model.Breadcrumb {
	result := make([]*model.Breadcrumb, 0)
	cacheKey := fmt.Sprintf("praktis_category_breadcrumbs_siblings:%d", c.GetID())
	cacheClient := praktis.GetCacheClient()
	if cacheClient.MustExists(cacheKey) {
		rawData, err := cacheClient.Get(cacheKey)
		if err == nil {
			err = json.Unmarshal([]byte(rawData), &result)
			if err == nil {
				return result
			} else {
				core_utils.ErrorWarning(err)
			}
		}
	}

	attr := mage_entity.NewAttributeRepository(1).GetAttribute(types.CategoryEntityType, "is_active")

	var siblings []int64
	praktis.GetDbClient().Table("catalog_category_entity as e").
		Select("e.entity_id").
		Where("parent_id = ?", c.GetParentID().Int64()).
		Joins("JOIN `" + attr.GetMagentoTable().String() + "` AS active_v ON " + andCond([]string{
			"active_v.entity_id = e.entity_id",
			fmt.Sprintf("active_v.attribute_id = %d", attr.GetID()),
			"active_v.value = 1"})).
		Group("e.entity_id").
		Scan(&siblings)

	catRepo := mage_entity.NewCategoryRepository()
	for _, sibling := range siblings {
		cat, _ := catRepo.Get(types.ToEntityID(sibling))
		result = append(result, &model.Breadcrumb{
			Label: cat.Name,
			URL:   cat.UrlPath,
		})
	}

	if len(result) > 0 {
		cacheData, _ := json.Marshal(&result)
		if len(cacheData) > 0 {
			_ = cacheClient.Save(cacheKey, cacheData, cache.InfiniteTTL)
		}
	}

	return result
}

func getChildrenBreadcrumbs(cat *mage_entity.CategoryEntity) []*model.Breadcrumb {
	result := make([]*model.Breadcrumb, 0)
	children, err := mage_entity.NewCategoryRepository().
		GetChildren(cat.GetID(), "name", "url_path")
	core_utils.ErrorWarning(err)
	if len(*children) > 0 {
		for _, child := range *children {
			result = append(result, &model.Breadcrumb{
				Label: child.Name,
				URL:   child.UrlPath,
			})
		}
	}

	return result
}

func GetCategoryEntity(id types.EntityID) (*mage_entity.CategoryEntity, error) {
	var err error
	var category *mage_entity.CategoryEntity

	category, err = GetCachedCategory(id)
	if err != nil {
		return category, err
	}

	categoryRepo := mage_entity.NewCategoryRepository()
	category, err = categoryRepo.Get(id,
		"name",
		"url_path",
		"description",
		"thumbnail",
		"custom_template",
	)

	if err != nil {
		return category, err
	} else if category == nil || category.Path == "" {
		return category, fmt.Errorf("category not found: " + id.String())
	}

	category.SaveInCache()
	return category, err
}
