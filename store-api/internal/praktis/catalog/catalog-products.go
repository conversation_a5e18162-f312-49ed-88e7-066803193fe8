package catalog

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	catalog "praktis.bg/store-api/internal/praktis/catalog/brands"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	mage_product "praktis.bg/store-api/packages/magento-core/mage-store/mage-product"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"strings"
	"sync"
)

const FilterProductsByAttribute = "65"
const FilterProductsByDiscount = "9"

type ProductIndexCollection struct {
	appliedFilters  []*model.AppliedFilter
	filters         []*model.AvailableFilter
	category        *mage_entity.CategoryEntity
	attributeFilter *model.AppliedFilter
	store           *magento_core.StoreEntity

	isLoaded bool

	pager     *model.Pager
	sortOrder *model.CollectionSort

	lock  sync.Mutex
	items []*product.PraktisProduct
}

func getValidPageSize(size int) int {
	for _, s := range []int{12, 24, 48, 96} {
		if s == size {
			return s
		}
	}

	return 24
}

func NewProductCollection(
	store *magento_core.StoreEntity,
	state *model.CatalogState,
) *ProductIndexCollection {
	pager := state.Pager
	if pager == nil {
		pager = &model.Pager{
			Page:     1,
			PageSize: 24,
		}
	}

	pager.PageSize = getValidPageSize(pager.PageSize)

	return &ProductIndexCollection{
		appliedFilters: state.Filters.Applied,
		isLoaded:       false,
		pager:          pager,
		sortOrder:      state.Sort,
		filters:        nil,
		items:          nil,
		store:          store,
		lock:           sync.Mutex{},
	}
}

func NewCategoryProductCollection(
	store *magento_core.StoreEntity,
	state *model.CatalogState,
	categoryId int64,
) (*ProductIndexCollection, error) {
	category, err := mage_entity.NewCategoryRepository().Get(
		types.EntityID(categoryId),
		"dynamic_mode_filter", "dynamic_mode",
	)
	if err != nil {
		return &ProductIndexCollection{}, err
	} else if category.Validate() != nil {
		return &ProductIndexCollection{}, fmt.Errorf("category not found")
	}

	col := NewProductCollection(store, state)
	col.category = category
	return col, nil
}

func NewSplashPageProductCollection(
	store *magento_core.StoreEntity,
	state *model.CatalogState,
	splashPageID int64,
) (*ProductIndexCollection, error) {
	page, err := catalog.NewSplashPageRepository(nil).GetByID(splashPageID)
	if err != nil {
		return &ProductIndexCollection{}, err
	}
	col := NewProductCollection(store, state)
	col.attributeFilter = &model.AppliedFilter{
		AttributeCode: page.AttributeCode,
		Value:         types.ToStr(page.OptionID),
	}

	return col, nil
}

func (c *ProductIndexCollection) GetFilteredSelect() *gorm.DB {
	qb := c.getSelect()
	for _, filter := range c.appliedFilters {
		qb = applyFilter(qb, filter)
	}

	return qb
}

func (c *ProductIndexCollection) GetFilteredSelectExcluding(excluded ...string) *gorm.DB {
	qb := c.getSelect()
	for _, filter := range c.appliedFilters {
		exclude := false
		for _, key := range excluded {
			if exclude = filter.AttributeCode == key; exclude {
				break
			}
		}

		if !exclude {
			qb = applyFilter(qb, filter)
		}
	}

	return qb
}

func (c *ProductIndexCollection) GetPageSize() int {
	return c.pager.PageSize
}

func (c *ProductIndexCollection) GetPage() int {
	return c.pager.Page
}

func (c *ProductIndexCollection) GetItemsCount() int {
	if c.usesCache() {
		cacheKey := c.getCachePrefix(ProductCountCacheType)
		cacheClient := praktis.GetCacheClient()
		if cacheClient.MustExists(cacheKey) {
			val, _ := cacheClient.Get(cacheKey)
			if val != "" {
				valInt, _ := strconv.Atoi(val)
				if valInt > 0 {
					return valInt
				}
			}
		}
	}

	var count int64
	c.GetFilteredSelect().Select("sku").Count(&count)

	if c.usesCache() {
		cacheKey := c.getCachePrefix(ProductCountCacheType)
		cacheClient := praktis.GetCacheClient()
		_ = cacheClient.Save(cacheKey, count, c.CacheTTL())
	}

	return int(count)
}

func (c *ProductIndexCollection) GetAvailableSorts() []*model.SortField {
	return GetAvailableSorts()
}

func GetAvailableSorts() []*model.SortField {
	return []*model.SortField{
		{
			Label: "Най-нови",
			Value: "position",
			Dir:   model.SortDirectionDesc,
		},
		{
			Label: "Цена възходяща",
			Value: "price",
			Dir:   model.SortDirectionAsc,
		},
		{
			Label: "Цена низходяща",
			Value: "price",
			Dir:   model.SortDirectionDesc,
		},
		{
			Label: "Най-голяма отстъпка",
			Value: "best_value",
			Dir:   model.SortDirectionDesc,
		},
	}
}

func (c *ProductIndexCollection) GetSort() *model.CollectionSort {
	return c.sortOrder
}

func (c *ProductIndexCollection) GetItems() ([]*product.PraktisProduct, error) {
	if !c.isLoaded {
		err := c.loadItems()
		if err != nil {
			return c.items, err
		}
		c.isLoaded = true
	}

	return c.items, nil
}

func (c *ProductIndexCollection) getSelect() *gorm.DB {
	c.lock.Lock()

	db := praktis.GetDbClient().
		Select("e.*").
		Table(
			fmt.Sprintf("%s AS e", (&product.PraktisProduct{}).TableName()),
		)

	if c.category.IsValid() {
		mode := c.category.GetAttributeValue("dynamic_mode")
		filterVal := c.category.GetAttributeValue("dynamic_mode_filter")
		if mode == FilterProductsByAttribute && filterVal != "" {
			filters := dynamicFilter(filterVal).getFilters()
			for _, filter := range filters {
				db = filter.applyFilter(db)
			}
		} else if mode == FilterProductsByDiscount {
			db = db.Where("e.special_price IS NOT NULL AND e.special_price < e.price")
		} else {
			path := c.category.Path
			db = db.Joins("INNER JOIN catalog_category_product AS ccp ON ccp.product_id = e.entity_id").
				Joins("INNER JOIN catalog_category_entity AS cpe ON cpe.entity_id = ccp.category_id").
				Where("cpe.path = ? OR cpe.path LIKE ? ", path, path+"/%")
		}
	} else if c.attributeFilter != nil && c.attributeFilter.AttributeCode == "brand" {
		db = db.Where("e.brand = ?", c.attributeFilter.Value)
	} else {
		core_utils.Debug("No category or attribute filter")
	}

	qb := db.Where("e.visibility IN (?)", []int{
		mage_product.VisibilityInCatalog,
		mage_product.VisibilityBoth,
	})

	// This will filter only active products (status = 1)
	qb = qb.Joins(`INNER JOIN catalog_product_entity_int AS status_attr 
		ON status_attr.entity_id = e.entity_id 
		AND status_attr.attribute_id = (
			SELECT attribute_id FROM eav_attribute 
			WHERE attribute_code = 'status' AND entity_type_id = 4
		) 
		AND status_attr.value = 1`)

	// Add stock availability check
	// For catalog pages, products must be in stock
	qb = qb.Joins("INNER JOIN cataloginventory_stock_item AS csi ON csi.product_id = e.entity_id")

	// Always hide products that are out of stock and have manage_stock=1
	qb = qb.Where("NOT (csi.manage_stock = 1 AND csi.is_in_stock = 0)")

	// Filter out products with zeron_site_status 1 or 2, managed_stock = yes/1 and out of stock
	attr, _ := mage_entity.ProductAttributes.GetAttribute("zeron_site_status")
	if attr.AttributeID > 0 {
		qb = qb.Joins(fmt.Sprintf(`LEFT JOIN %s zss_filter
			ON zss_filter.entity_id = e.entity_id 
			AND zss_filter.attribute_id = %d 
			AND zss_filter.store_id = 0`,
			attr.GetMagentoTable(),
			attr.AttributeID,
		))

		// Additional specific filtering for zeron_site_status 1 or 2
		// This condition is redundant with the one above but kept for compatibility
		qb = qb.Where(`
			NOT (
				(zss_filter.value IN ('1','2')) AND 
				csi.manage_stock = 1 AND 
				csi.is_in_stock = 0
			)
		`)
	}

	c.lock.Unlock()
	return qb
}

func (c *ProductIndexCollection) getCollectionAttributes() ([]string, error) {
	var result []string
	var err error

	if c.usesCache() {
		cacheKey := c.getCachePrefix(AttributeCacheType)
		cacheClient := praktis.GetCacheClient()
		if cacheClient.MustExists(cacheKey) {
			return cacheClient.GetSet(cacheKey)
		}
	}

	var setIds []int
	err = c.getSelect().Select("DISTINCT e.attribute_set_id").Scan(&setIds).Error
	if err != nil {
		return result, err
	}

	available := GetAvailableFilters()
	var added = map[string]struct{}{}
	for _, id := range setIds {
		set := mage_entity.AttributeSetEntity{
			ID: types.ToEntityID(id),
		}

		var attributes []string
		attributes, err = set.GetAttributesCodes()
		if err != nil {
			return result, err
		}

		for _, attr := range attributes {
			if _, ok := available[attr]; ok && !isFieldExcludedFromAttributes(attr) {
				if _, ok = added[attr]; ok {
					continue
				}

				added[attr] = struct{}{}
				result = append(result, attr)
			}
		}
	}

	if c.usesCache() {
		cacheKey := c.getCachePrefix(AttributeCacheType)
		cacheClient := praktis.GetCacheClient()
		_ = cacheClient.Delete(cacheKey)
		_, err = cacheClient.AddSetMember(cacheKey, result...)
		if err != nil {
			_, err = cacheClient.UpdateTTl(cacheKey, cache.InfiniteTTL)
		}
	}

	return result, err
}

func applyFilter(qb *gorm.DB, filter *model.AppliedFilter) *gorm.DB {
	val := filter.Value
	switch filter.AttributeCode {
	case "price":
		minPrice, maxPrice := getMinMax(val)
		qb = qb.Where(`(e.special_price IS NOT NULL AND e.special_price >= ? AND e.special_price <= ?) 
OR (e.special_price IS NULL AND e.price >= ? AND e.price <= ?)`,
			minPrice, maxPrice, minPrice, maxPrice,
		)
	case OnSaleFilterCode:
		var values []string
		if strings.Contains(val, ",") {
			values = strings.Split(val, ",")
		} else {
			values = []string{val}
		}

		for _, v := range values {
			switch v {
			case OnSaleFilterOnSale:
				qb = qb.Where(`e.special_price > 0 && 
(e.special_from_date IS NULL OR e.special_from_date <= NOW()) && 
(e.special_to_date IS NULL OR e.special_to_date >= NOW())`)
			case OnSaleFilterOnlinePromo:
				qb = qb.Where("e.is_bestselling > 0")
			case OnSaleFilterBrochure:
				qb = qb.Where("e.zeron_in_broschure > 0")
			case OnSaleFilterBuyCheap:
				qb = qb.Where("e.price_group_type = 20")
			default:
				core_utils.Debug("Unknown OnSaleFilterCode code: " + filter.Value)
			}
		}
	case AvailabilityFilterCode:
		var values []string
		if strings.Contains(val, ",") {
			values = strings.Split(val, ",")
		} else {
			values = []string{val}
		}

		var prakSiteStatusFilter []int
		var warehouseStatusFilter []int
		for _, v := range values {
			intV, _ := strconv.Atoi(v)
			switch intV {
			case OrderOnline, GetFromStore:
				prakSiteStatusFilter = append(prakSiteStatusFilter, intV)
			case ByRequest:
				qb = qb.Joins("INNER JOIN cataloginventory_stock_item AS csi ON csi.product_id = e.entity_id")
				qb = qb.Joins(`INNER JOIN catalog_product_entity_int AS status_attr2 
					ON status_attr2.entity_id = e.entity_id 
					AND status_attr2.attribute_id = (
						SELECT attribute_id FROM eav_attribute 
						WHERE attribute_code = 'status' AND entity_type_id = 4
					) 
					AND status_attr2.value = 1`)
			default:
				if wh, ok := warehousesToStore[intV]; ok {
					warehouseStatusFilter = append(warehouseStatusFilter, wh...)
				} else {
					core_utils.Debug("Unknown AvailabilityFilterCode code: " + filter.Value)
				}
			}
		}

		if len(prakSiteStatusFilter) > 0 {
			attr, err := mage_entity.ProductAttributes.GetAttribute("zeron_site_status")
			core_utils.ErrorWarning(err)
			if attr.AttributeID > 0 {
				qb = qb.Joins(fmt.Sprintf(`INNER JOIN %s zss
ON zss.entity_id = e.entity_id AND zss.attribute_id = %d AND zss.value IN (?) and zss.store_id = 0`,
					attr.GetMagentoTable(),
					attr.AttributeID,
				), prakSiteStatusFilter)
			}
		}

		if len(warehouseStatusFilter) > 0 {
			qb = qb.Joins(`INNER JOIN stenik_zeron_warehouse_product AS zwp 
ON zwp.product_id = e.entity_id AND zwp.warehouse_code in (?)`,
				warehouseStatusFilter)
		}
	default:
		if strings.Contains(val, ",") {
			var values []string
			values = strings.Split(val, ",")
			qb = qb.Where(fmt.Sprintf("e.%s IN (?)", filter.AttributeCode), values)
		} else {
			qb = qb.Where(fmt.Sprintf("e.%s = ?", filter.AttributeCode), val)
		}
	}

	return qb
}

func getMinMax(val string) (float64, float64) {
	var minPrice, maxPrice float64
	if strings.Contains(val, "-") {
		vals := strings.Split(val, "-")
		minPrice, _ = strconv.ParseFloat(vals[0], 64)
		maxPrice, _ = strconv.ParseFloat(vals[1], 64)
	} else {
		maxPrice, _ = strconv.ParseFloat(val, 64)
		minPrice = 0
	}

	return minPrice, maxPrice
}

func applySortOrder(qb *gorm.DB, sort *model.CollectionSort) *gorm.DB {
	if sort != nil && sort.Value != "" {
		if sort.Value == "position" {
			return qb.Order("e.entity_id " + sort.Dir.String())
		} else if sort.Value == "price" {
			return qb.Order("IFNULL(e.special_price, e.price) " + sort.Dir.String())
		} else if sort.Value == "best_value" {
			return qb.Order("(e.price / IFNULL(e.special_price, e.price)) " + sort.Dir.String())
		} else {
			return qb.Order("e.entity_id DESC")
		}
	} else {
		return qb.Order("e.entity_id DESC")
	}
}

func (c *ProductIndexCollection) loadItems() error {
	qb := c.getSelect()
	for _, filter := range c.appliedFilters {
		qb = applyFilter(qb, filter)
	}

	if c.pager.Page > 1 {
		qb = qb.Offset((c.pager.Page - 1) * c.pager.PageSize)
	}

	qb = applySortOrder(qb, c.sortOrder)
	qb = qb.Limit(c.pager.PageSize)

	qb = qb.Group("e.entity_id")

	c.items = []*product.PraktisProduct{}
	return qb.Find(&c.items).Error
}

type MinMaxPriceRange struct {
	MinPrice        float64 `gorm:"column:min_price"`
	MaxPrice        float64 `gorm:"column:max_price"`
	HasSpecialPrice bool
}

func (c *ProductIndexCollection) getProductsPricesRange() (MinMaxPriceRange, error) {
	var err error
	var result MinMaxPriceRange
	qb := c.GetFilteredSelectExcluding("price").
		Select("MIN(price) as min_price, MAX(price) as max_price, min(special_price) as min_special_price, max(special_price) as max_special_price")
	priceResult := struct {
		MinPrice        float64 `gorm:"column:min_price"`
		MaxPrice        float64 `gorm:"column:max_price"`
		MinSpecialPrice float64 `gorm:"column:min_special_price"`
		MaxSpecialPrice float64 `gorm:"column:max_special_price"`
	}{}
	err = qb.Scan(&priceResult).Error
	if err != nil {
		return result, err
	}

	minPrice := priceResult.MinPrice
	if priceResult.MinSpecialPrice < minPrice && priceResult.MinSpecialPrice > 0 {
		minPrice = priceResult.MinSpecialPrice
	}
	maxPrice := priceResult.MaxPrice
	if priceResult.MaxSpecialPrice > maxPrice && priceResult.MaxSpecialPrice > 0 {
		maxPrice = priceResult.MaxSpecialPrice
	}

	result.MinPrice = minPrice
	result.MaxPrice = maxPrice
	result.HasSpecialPrice = priceResult.MinSpecialPrice > 0 || priceResult.MaxSpecialPrice > 0
	return result, nil
}

func (c *ProductIndexCollection) getAllIds() ([]int64, error) {
	var result []int64
	err := c.GetFilteredSelect().
		Select("e.entity_id").
		Group("e.entity_id").
		Scan(&result).Error

	return result, err
}
