package catalog

import (
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
)

func ToProductStockModel(data map[string]string) *model.ProductStock {
	result := &model.ProductStock{
		Qty:                  0,
		MinQty:               0.0,
		BlockForSale:         false,
		ZeronSiteStatus:      "",
		ZeronBlockedDelivery: false,
		ManageStock:          false,
		InStock:              false,
		HasImages:            false,
	}

	if v, ok := data["qty"]; ok {
		result.Qty = types.ToFloat(v)
	}

	if v, ok := data["min_qty"]; ok {
		result.MinQty = types.ToFloat(v) // TO DO: data validation is missing
	}

	if v, ok := data["block_for_sale"]; ok {
		result.BlockForSale = types.ToBool(v)
	}

	if v, ok := data["zeron_site_status"]; ok {
		result.ZeronSiteStatus = v
	}

	if v, ok := data["zeron_blocked_delivery"]; ok {
		result.ZeronBlockedDelivery = types.ToBool(v)
	}

	if v, ok := data["manage_stock"]; ok {
		result.ManageStock = types.ToBool(v)
	}

	if v, ok := data["in_stock"]; ok {
		result.InStock = types.ToBool(v)
	}

	if v, ok := data["has_images"]; ok {
		result.HasImages = types.ToBool(v)
	}

	return result
}

func ProductStockToMap(data *model.ProductStock) map[string]string {
	result := make(map[string]string)
	result["qty"] = types.ToStr(data.Qty)
	result["min_qty"] = types.ToStr(data.MinQty)
	result["block_for_sale"] = types.ToStr(data.BlockForSale)
	result["zeron_site_status"] = data.ZeronSiteStatus
	result["zeron_blocked_delivery"] = types.ToStr(data.ZeronBlockedDelivery)
	result["manage_stock"] = types.ToStr(data.ManageStock)
	result["in_stock"] = types.ToStr(data.InStock)
	result["has_images"] = types.ToStr(data.HasImages)

	return result
}

func GetProductStockData(productID int64) (*model.ProductStock, error) {
	var stockResult = struct {
		Qty                  float64 `json:"qty"`
		MinQty               float64 `json:"min_qty"`
		BlockForSale         bool    `json:"block_for_sale"`
		ZeronSiteStatus      string  `json:"zeron_site_status"`
		ZeronBlockedDelivery bool    `json:"zeron_blocked_delivery"`
		ManageStock          bool    `json:"manage_stock"`
		IsInStock            bool    `json:"is_in_stock"`
	}{}

	attr1, err := mage_store.ProductAttributes.GetAttribute("zeron_blockedforsale")
	if err != nil {
		return nil, err
	}
	attr2, err := mage_store.ProductAttributes.GetAttribute("zeron_blockeddelivery")
	if err != nil {
		return nil, err
	}
	attr3, err := mage_store.ProductAttributes.GetAttribute("zeron_site_status")
	if err != nil {
		return nil, err
	}

	err = praktis.GetDbClient().Raw(`select
    qty,
    min_qty,
    manage_stock,
    is_in_stock,
    zeron_blockedforsale.value as zeron_blockedforsale,
    zeron_blockeddelivery.value as zeron_blockeddelivery,
    zeron_site_status.value as zeron_site_status
from cataloginventory_stock_item si
    inner join catalog_product_entity_int zeron_blockedforsale on
        zeron_blockedforsale.entity_id = si.product_id and 
        zeron_blockedforsale.attribute_id = ? and 
        zeron_blockedforsale.store_id = 0
    inner join catalog_product_entity_int zeron_blockeddelivery on
    	zeron_blockeddelivery.entity_id = si.product_id and 
    	zeron_blockeddelivery.attribute_id = ? and 
    	zeron_blockeddelivery.store_id = 0
    inner join catalog_product_entity_varchar zeron_site_status on
    	zeron_site_status.entity_id = si.product_id and 
    	zeron_site_status.attribute_id = ? and 
    	zeron_site_status.store_id = 0
where si.product_id = ?
`, attr1.AttributeID, attr2.AttributeID, attr3.AttributeID, productID).Scan(&stockResult).Error

	return &model.ProductStock{
		Qty:                  stockResult.Qty,
		MinQty:               stockResult.MinQty,
		BlockForSale:         stockResult.BlockForSale,
		ZeronSiteStatus:      stockResult.ZeronSiteStatus,
		ZeronBlockedDelivery: stockResult.ZeronBlockedDelivery,
		ManageStock:          stockResult.ManageStock,
		InStock:              stockResult.IsInStock,
		HasImages:            false,
	}, err
}
