package product

import (
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	"praktis.bg/store-api/internal/praktis"
	mage_product "praktis.bg/store-api/packages/magento-core/mage-store/mage-product"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
)

func NewPraktisProductRepository(db *gorm.DB) *PraktisProductRepository {
	return &PraktisProductRepository{
		db: db,
	}
}

type IPraktisProductRepository interface {
	mage_types.CacheProvider
	mage_types.DBProvider
	GetID(id int64, attrs []string) (*PraktisProduct, error)
	GetSKU(sku string, attrs []string) (*PraktisProduct, error)
	GetIDs(ids []int64, attrs []string) ([]*PraktisProduct, error)
	GetSKUs(skus []string, attrs []string) ([]*PraktisProduct, error)
}

var _ IPraktisProductRepository = (*PraktisProductRepository)(nil)

type PraktisProductRepository struct {
	db             *gorm.DB
	coreRepository mage_product.IProductRepository
}

func (r *PraktisProductRepository) DB() *gorm.DB {
	if r.db == nil {
		return praktis.GetDbClient()
	}
	return r.db
}

func (r *PraktisProductRepository) Cache() *cache.RedisCacheProvider {
	return praktis.GetCacheClient()
}

func (r *PraktisProductRepository) getCoreRepository() mage_product.IProductRepository {
	if r.coreRepository == nil {
		r.coreRepository = mage_product.NewProductRepository()
	}

	return r.coreRepository
}

func (r *PraktisProductRepository) newProduct(
	id int64,
	attrs []string,
) (*PraktisProduct, []string, error) {
	product := &PraktisProduct{EntityID: id}
	toLoad, err := product.LoadFromCache(attrs)
	if err != nil {
		return nil, toLoad, err
	} else if product.isFullCacheLoad {
		return product, []string{}, nil
	}

	return product, toLoad, nil
}

func (r *PraktisProductRepository) GetID(id int64, attrs []string) (*PraktisProduct, error) {
	if len(attrs) == 0 {
		attrs = requiredProductLoadAttributes
	}

	loadProduct := false
	product, toLoad, err := r.newProduct(id, attrs)
	core_utils.ErrorWarning(err)

	if len(toLoad) > 0 {
		_toLoad := make([]string, 0, len(toLoad))
		for _, t := range toLoad {
			if product.AttributeIsField(t) {
				loadProduct = true
			} else {
				_toLoad = append(_toLoad, t)
			}
		}
		toLoad = _toLoad
	}

	if loadProduct == false && len(toLoad) == 0 {
		return product, nil
	}

	var productErr error
	productDone := make(chan struct{})
	if loadProduct {
		go func() {
			defer close(productDone)
			productErr = r.DB().Table((&PraktisProduct{}).TableName()).
				Find(product, id).Error
			if productErr == nil {
				productErr = product.GetEntity().LoadGallery()
			}
		}()
	} else {
		close(productDone)
	}

	attrsChan := make(chan map[string]string)
	attrErrChan := make(chan error, 1)

	if len(toLoad) > 0 {
		attrsChan, attrErrChan = r.getCoreRepository().GetProductAttributesValueChannel(
			id,
			toLoad,
			product.GetStoreID(),
		)
	} else {
		close(attrsChan)
		close(attrErrChan)
	}

	<-productDone
	if productErr != nil {
		for range attrsChan {
		}
		for range attrErrChan {
		}
		return nil, productErr
	}

	for attrsTableData := range attrsChan {
		if err = product.SetCacheObject(attrsTableData); err != nil {
			return nil, err
		}
	}

	if attrErr := <-attrErrChan; attrErr != nil {
		return nil, attrErr
	}

	if loadProduct {
		err = product.SaveInCache()
	} else {
		err = product.SaveAttributes(toLoad...)
	}

	return product, err
}

func (r *PraktisProductRepository) GetSkuID(sku string) (int64, error) {
	return (&mage_product.ProductRepository{}).GetSkuID(sku)
}

func (r *PraktisProductRepository) GetSKU(sku string, attrs []string) (*PraktisProduct, error) {
	id, err := r.GetSkuID(sku)
	if err != nil {
		return nil, err
	}

	return r.GetID(id, attrs)
}

func (r *PraktisProductRepository) GetIDs(ids []int64, attrs []string) ([]*PraktisProduct, error) {
	if len(ids) == 0 {
		return []*PraktisProduct{}, nil
	}

	if len(attrs) == 0 {
		attrs = requiredProductLoadAttributes
	}

	dataCh := make(chan struct {
		index int
		id    int64
		data  map[string]string
		err   error
	})

	go func() {
		for i, id := range ids {
			data, err := (&PraktisProduct{
				EntityID: id,
			}).loadCacheData()

			dataCh <- struct {
				index int
				id    int64
				data  map[string]string
				err   error
			}{index: i, id: id, data: data, err: err}
		}
		close(dataCh)
	}()

	result := make([]*PraktisProduct, len(ids))
	idToResultIndex := make(map[int64]int)
	loadProductIds := make(map[int64]struct{})
	attrToLoad := map[string]struct{}{}
	for item := range dataCh {
		if item.err != nil {
			core_utils.ErrorWarning(item.err)
			continue
		}

		p := &PraktisProduct{EntityID: item.id}
		for _, a := range attrs {
			if v, ok := item.data[a]; ok {
				core_utils.DebugError(
					p.SetVal(a, v),
				)
			} else {
				if p.AttributeIsField(a) {
					loadProductIds[p.EntityID] = struct{}{}
				} else {
					attrToLoad[a] = struct{}{}
				}
			}
		}

		result[item.index] = p
		idToResultIndex[p.EntityID] = item.index
	}

	if len(attrToLoad) == 0 && len(loadProductIds) == 0 {
		return result, nil
	}

	var productErr error
	productsDone := make(chan struct{})
	if len(loadProductIds) > 0 {
		go func() {
			products := make([]*PraktisProduct, 0) // Initialize with a non-nil slice
			defer close(productsDone)
			ids = []int64{}
			for id, _ := range loadProductIds {
				ids = append(ids, id)
			}

			productErr = r.DB().Table((&PraktisProduct{}).TableName()).
				Find(&products, ids).Error

			if productErr == nil {
				for _, p := range products {
					if index, ok := idToResultIndex[p.EntityID]; ok {
						result[index] = p
					}
				}
			}
		}()
	} else {
		close(productsDone)
	}

	attrsChan := make(chan map[int64]map[string]string)
	attrErrChan := make(chan error, 1)

	_toLoad := make([]string, 0, len(attrToLoad))
	for a := range attrToLoad {
		_toLoad = append(_toLoad, a)
	}

	if len(attrToLoad) > 0 {
		attrsChan, attrErrChan = r.getCoreRepository().GetMassProductAttributesValueChannel(
			ids,
			_toLoad,
			(&PraktisProduct{}).GetStoreID(),
		)
	} else {
		close(attrsChan)
		close(attrErrChan)
	}

	<-productsDone
	if productErr != nil {
		for range attrsChan {
		}
		for range attrErrChan {
		}
		return nil, productErr
	}

	for attrsTableData := range attrsChan {
		for productID, values := range attrsTableData {
			if index, ok := idToResultIndex[productID]; ok {
				err := result[index].SetCacheObject(values)
				core_utils.ErrorWarning(err)
			}
		}
	}

	if attrErr := <-attrErrChan; attrErr != nil {
		return nil, attrErr
	}

	var err error
	for _, res := range result {
		if _, ok := loadProductIds[res.EntityID]; ok {
			err = res.SaveInCache()
		} else {
			err = res.SaveAttributes(_toLoad...)
		}
		core_utils.ErrorWarning(err)
	}

	return result, nil
}

func (r *PraktisProductRepository) GetIDsBySkus(skus []string) ([]int64, error) {
	return (&mage_product.ProductRepository{}).GetIDsBySkus(skus)
}

func (r *PraktisProductRepository) GetSKUs(skus []string, attrs []string) ([]*PraktisProduct, error) {
	ids, err := r.GetIDsBySkus(skus)
	if err != nil {
		return nil, err
	}

	return r.GetIDs(ids, attrs)
}

type PreloadDataOptions struct {
	LoadLabels  bool
	LoadBrands  bool
	LoadGallery bool
}

func (r *PraktisProductRepository) preloadData(preload PreloadDataOptions, preloadChan chan struct{}) {
	defer close(preloadChan)

}
