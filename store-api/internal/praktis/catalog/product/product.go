package product

import (
	"errors"
	"fmt"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm/schema"
	"praktis.bg/store-api/internal/praktis"
	mage_core "praktis.bg/store-api/packages/magento-core"
	mage_product "praktis.bg/store-api/packages/magento-core/mage-store/mage-product"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/storage"
	"praktis.bg/store-api/packages/magento-core/types"
	"time"
)

type TypeID uint

const (
	TypeIDSimple       TypeID = 0
	TypeIDConfigurable        = 1
	TypeIDBundle              = 2
)

const CatalogTableCacheKey = storage.MagentoCachePrefix + "pfg_theme_catalog_table_name"

const BuyCheapPriceGroup = 20

var _ cache.CacheableObject = (*PraktisProduct)(nil)
var _ schema.Tabler = (*PraktisProduct)(nil)
var _ mage_types.Entity[*PraktisProduct] = (*PraktisProduct)(nil)

type PraktisProduct struct {
	// loaded from cache - required by - cache.CacheableObject
	isCacheLoaded bool `gorm:"-"`
	// all attributes loaded from cache
	isFullCacheLoad bool                        `gorm:"-"`
	Entity          *mage_product.ProductEntity `gorm:"-"`
	EntityID        int64                       `gorm:"column:entity_id;primaryKey"`
	ProductType     TypeID                      `gorm:"column:type_id"`
	Sku             string                      `gorm:"column:sku"`
	Name            string                      `gorm:"column:name"`
	Thumbnail       string                      `gorm:"column:thumbnail"`
	URLKey          string                      `gorm:"column:url_key"`
	Price           float64                     `gorm:"column:price"`
	SpecialPrice    float64                     `gorm:"column:special_price"`
	SpecialFrom     time.Time                   `gorm:"column:special_from_date"`
	SpecialTo       time.Time                   `gorm:"column:special_to_date"`
	BrandID         int                         `gorm:"column:brand"`
	Brand           Brand                       `gorm:"-"`

	InBrochure      bool `gorm:"column:zeron_in_broschure"`
	PriceGroupType  int  `gorm:"column:price_group_type"`
	HasFreeShipping bool `gorm:"column:has_free_shipping"`

	EnergyLabel *EnergyLabel `gorm:"-"`
}

func (p *PraktisProduct) New() *PraktisProduct {
	return &PraktisProduct{}
}

func (p *PraktisProduct) NewSlice() []*PraktisProduct {
	return []*PraktisProduct{}
}

const ConfigPathKey = "pfg_theme/catalog/table_name"

func getProductIndexTable() string {
	val := praktis.GetPraktisStore().GetConfig(ConfigPathKey, "")
	if val == "" {
		panic("catalog table name is not set")
	}

	return val
}

func (p *PraktisProduct) TableName() string {
	return getProductIndexTable()
}

func (p *PraktisProduct) GetStore() *mage_core.StoreEntity {
	return praktis.GetPraktisStore()
}

func (p *PraktisProduct) GetStoreID() int {
	return p.GetStore().GetID()
}

func (p *PraktisProduct) GetID() int64 {
	if p == nil {
		core_utils.ErrorWarning(fmt.Errorf("GetID called on nil product"))
		return 0
	}
	return p.EntityID
}

func (p *PraktisProduct) GetEntity() *mage_product.ProductEntity {
	if p == nil {
		core_utils.ErrorWarning(fmt.Errorf("GetEntity called on nil product"))
		return nil
	}

	if p.Entity == nil {
		p.Entity = &mage_product.ProductEntity{
			EntityID:   p.EntityID,
			Sku:        p.Sku,
			Attributes: make(map[string]string),
		}
	}

	return p.Entity
}

func (p *PraktisProduct) GetSkuIDMapKey() string {
	if p == nil {
		core_utils.ErrorWarning(fmt.Errorf("GetSkuIDMapKey called on nil product"))
		return ""
	}

	entity := p.GetEntity()
	if entity == nil {
		return ""
	}

	return entity.GetSkuIDMapKey()
}

func (p *PraktisProduct) GetHasFreeShipping() bool {
	if p.EntityID < 1 {
		core_utils.Debug("GetHasFreeShipping: EntityID is 0")
		return false
	} else if p.HasFreeShipping {
		return true
	}

	hasShippingDiscount, err := p.GetVal("praktis_shipping_discount")
	var praktisDiscountStart, praktisDiscountEnd string
	if err != nil {
		if errors.Is(err, mage_types.FieldNotFound) {
			prod, err := NewPraktisProductRepository(nil).GetID(p.EntityID, []string{
				"has_free_shipping",
				"praktis_shipping_discount",
				"praktis_discount_start",
				"praktis_discount_end",
			})

			if err != nil || prod == nil {
				return false
			} else if prod.HasFreeShipping {
				return true
			}

			hasShippingDiscount, _ = prod.GetVal("praktis_shipping_discount")
			praktisDiscountStart, _ = prod.GetVal("praktis_discount_start")
			praktisDiscountEnd, _ = prod.GetVal("praktis_discount_emd")
		} else {
			return false
		}
	} else {
		praktisDiscountStart, _ = p.GetVal("praktis_discount_start")
		praktisDiscountEnd, _ = p.GetVal("praktis_discount_emd")
	}

	if hasShippingDiscount == "" && praktisDiscountStart != "" && praktisDiscountEnd != "" {
		startTime := types.ToDateTime(praktisDiscountStart)
		endTime := types.ToDateTime(praktisDiscountEnd)
		now := time.Now()

		return startTime.Before(now) && endTime.After(now)
	}

	return false
}

var PraktisProductFieldsKeys = []string{
	"entity_id", "product_type",
	"sku", "name", "thumbnail", "url_key",
	"price", "special_price", "special_from", "special_to",
	"brand_id", "brand",
	"in_brochure", "price_group", "has_free_shipping",
}

var requiredProductLoadAttributes = append(PraktisProductFieldsKeys, []string{
	"gallery",
	"zeron_base_measure",
	"zeron_use_second_measure",
	"zeron_measure",
	"zeron_measure_qty",
	"zeron_warranty",
	"short_description",
	"description",
}...)

var PraktisDefaultAttributeMap = map[string]bool{
	"entity_id":         true,
	"product_type":      true,
	"sku":               true,
	"name":              true,
	"thumbnail":         true,
	"url_key":           true,
	"price":             true,
	"special_price":     true,
	"special_from":      true,
	"special_to":        true,
	"brand_id":          true,
	"brand":             true,
	"in_brochure":       true,
	"price_group":       true,
	"has_free_shipping": true,
}

func (p *PraktisProduct) AttributeIsField(key string) bool {
	val, ok := PraktisDefaultAttributeMap[key]
	return ok && val
}

func (p *PraktisProduct) GetFinalPrice() float64 {
	if p.SpecialPrice > 0 && p.SpecialFrom.Before(time.Now()) && p.SpecialTo.After(time.Now()) {
		return p.SpecialPrice
	}

	return p.Price
}

func (p *PraktisProduct) UsesSecondMeasure() bool {
	return p.MustGetVal("zeron_use_second_measure") == "1"
}
