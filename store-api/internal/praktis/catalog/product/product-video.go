package product

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
)

type ProductVideo struct {
	VideoID        int64  `gorm:"column:video_id"`
	ProductID      int64  `gorm:"column:product_id"`
	Title          string `gorm:"column:video_title"`
	Description    string `gorm:"column:video_description"`
	URL            string `gorm:"column:video_url"`
	VideoType      string `gorm:"column:video_type"`
	VideoStoreView string `gorm:"column:video_store_view"`
	Status         int    `gorm:"column:video_status"`
	Thumbnail      string `gorm:"column:video_thumbnail"`
	Position       int    `gorm:"column:video_position"`
}

func (p *PraktisProduct) LoadVideos() []*ProductVideo {
	if p == nil || p.EntityID < 1 {
		core_utils.ErrorWarning(fmt.Errorf("cannot load videos for nil product or product with invalid ID"))
		return nil
	}

	db := praktis.GetDbClient()
	videos := make([]*ProductVideo, 0)

	err := db.Raw(`
		SELECT
			cpe.entity_id as product_id,
			iv.video_id,
			iv.title as video_title,
			iv.description as video_description,
			iv.url as video_url,
			iv.video_type,
			iv.video_store_view,
			iv.video_status,
			iv.image as video_thumbnail,
			ipv.video_position
		FROM catalog_product_entity cpe
			INNER JOIN iwd_product_video ipv ON cpe.entity_id = ipv.product_id
			INNER JOIN iwd_video iv ON ipv.video_id = iv.video_id
		WHERE cpe.entity_id = ?
			AND iv.video_status = 1  -- Only active videos
		ORDER BY ipv.video_position
	`, p.EntityID).Scan(&videos).Error

	if err != nil {
		core_utils.ErrorWarning(err)
		return nil
	}

	return videos
}

// ToVideoModel converts a ProductVideo to a GraphQL model
func (v *ProductVideo) ToVideoModel() *model.ProductVideo {
	if v == nil {
		return nil
	}

	return &model.ProductVideo{
		ID:          v.VideoID,
		Title:       v.Title,
		Description: core_utils.ToStringPointer(v.Description),
		URL:         v.URL,
		VideoType:   v.VideoType,
		Thumbnail:   core_utils.ToStringPointer(v.Thumbnail),
		Position:    v.Position,
	}
}

// ToVideosModel converts a slice of ProductVideo to GraphQL models
func ToVideosModel(videos []*ProductVideo) []*model.ProductVideo {
	if videos == nil {
		return nil
	}

	result := make([]*model.ProductVideo, 0, len(videos))
	for _, video := range videos {
		if model := video.ToVideoModel(); model != nil {
			result = append(result, model)
		}
	}

	if len(result) == 0 {
		return nil
	}

	return result
}
