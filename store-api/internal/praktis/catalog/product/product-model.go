package product

import (
	"errors"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	mage_product "praktis.bg/store-api/packages/magento-core/mage-store/mage-product"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"time"
)

func (p *PraktisProduct) toGalleyModel() []*model.GalleryImage {
	gallery := make([]*model.GalleryImage, 0)
	for _, img := range p.GetEntity().GalleryImages {
		if img.Value == "" {
			continue
		}

		label := core_utils.IF(img.Label == "", p.Name, img.Label)
		gallery = append(gallery, &model.GalleryImage{
			Image: &model.Image{
				Src:   img.Value.GetMedialUrl(),
				Alt:   &label,
				Title: &label,
			},
			Position: img.Position,
		})
	}

	if len(gallery) == 0 {
		gallery = append(gallery, &model.GalleryImage{
			Image:    p.toImageModel(),
			Position: 0,
		})
	}

	return gallery
}

func (p *PraktisProduct) toPrice() *model.ProductPrice {
	priceM := &model.Price{
		Value:    p.Price,
		Currency: "BGN",
	}

	result := &model.ProductPrice{
		Price: priceM,
	}

	if p.SpecialPrice > 0 {
		now := time.Now()
		specialPriceIsActive := true

		if !p.SpecialFrom.IsZero() {
			specialPriceIsActive = p.SpecialFrom.Before(now)
		}

		if specialPriceIsActive && !p.SpecialTo.IsZero() {
			specialPriceIsActive = p.SpecialTo.After(now)
		}

		if specialPriceIsActive {
			specialM := &model.Price{
				Value:    p.SpecialPrice,
				Currency: "BGN",
			}

			fromDateStr := p.SpecialFrom.Format("2006-01-02")
			toDateStr := p.SpecialTo.Format("2006-01-02")

			result.Special = specialM
			result.SpecialFrom = &fromDateStr
			result.SpecialTo = &toDateStr
		}
	}

	return result
}

func (p *PraktisProduct) ToModel() model.Product {
	buyCheap := p.PriceGroupType == BuyCheapPriceGroup

	p.LoadEnergyLabel()
	p.LoadBrand()

	// Ensure gallery images are loaded fresh from database before conversion
	if p.GetEntity() != nil {
		_ = p.GetEntity().LoadGallery()
	}

	// Load videos for the product
	videos := p.LoadVideos()

	measure := &model.ProductMeasures{
		Base:                 p.MustGetVal("zeron_base_measure"),
		SecondaryMeasureUsed: p.MustGetVal("zeron_use_second_measure") == "1",
		Secondary:            "",
		SecondaryQty:         0,
	}

	if p.UsesSecondMeasure() {
		var secondaryQty float64
		secondaryQtyStr := p.MustGetVal("zeron_measure_qty")
		if secondaryQtyStr != "" {
			var err error
			secondaryQty, err = strconv.ParseFloat(secondaryQtyStr, 64)
			core_utils.ErrorWarning(err)
		}

		measure.Secondary = p.MustGetVal("zeron_measure")
		measure.SecondaryQty = secondaryQty
	}

	var result = &model.SimpleProduct{
		ID:               p.GetID(),
		Sku:              p.Sku,
		Name:             p.Name,
		Price:            p.toPrice(),
		URLKey:           p.URLKey,
		ShortDescription: p.MustGetVal("short_description"),
		Description:      p.MustGetVal("description"),
		Brand:            p.Brand.ToModel(),
		Measures:         measure,
		Labels: &model.MagentoLabels{
			WarrantyMonths:    types.ToInt(p.MustGetVal("zeron_warranty")),
			FreeDeliveryUntil: core_utils.ToStringPointer(""),
			FromBrochure:      &p.InBrochure,
			FreeDelivery:      &p.HasFreeShipping,
			BuyCheap:          &buyCheap,
			Other:             nil,
		},
		EnergyLabel: core_utils.IF(p.EnergyLabel == nil, nil, p.EnergyLabel.ToModel()),
		Image:       p.toImageModel(),
		Gallery:     p.toGalleyModel(),
		Videos:      ToVideosModel(videos),
	}

	return result
}

func (p *PraktisProduct) toImageModel() *model.Image {
	return &model.Image{
		Src: core_utils.IF(
			p.Thumbnail != "",
			mage_product.ProductImage(p.Thumbnail).GetMedialUrl(),
			p.GetEntity().Image.Value.GetMedialUrl(),
		),
		Title: &p.Name,
		Alt:   &p.Name,
	}
}

func GetProductModelID(p model.Product) (int64, error) {
	if p == nil {
		return 0, errors.New("GetIDFromModel: product is nil")
	}

	_p, ok := p.(*model.SimpleProduct)
	if !ok {
		return 0, fmt.Errorf("invalid product type: %T", p)
	}

	return _p.ID, nil
}
