package product

import (
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"time"
)

func (p *PraktisProduct) Cache() *cache.RedisCacheProvider {
	storeClient := magento_core.GetStoreClient()
	return storeClient.GetCacheClient()
}

func (p *PraktisProduct) CacheKey() string {
	return p.GetEntity().CacheKey()
}

func (p *PraktisProduct) CacheTTL() time.Duration {
	return p.GetEntity().CacheTTL()
}

func (p *PraktisProduct) SetCacheKey(key string) {
	p.GetEntity().SetCacheKey(key)
}

func (p *PraktisProduct) IsCacheLoaded() bool {
	return p.isCacheLoaded
}

func (p *PraktisProduct) GetCacheObject() map[string]string {
	res, err := p.GetData()
	core_utils.ErrorWarning(err)
	return res
}

func (p *PraktisProduct) SetCacheObject(cacheData map[string]string) error {
	for k, v := range cacheData {
		err := p.SetVal(k, v)
		if err != nil {
			return err
		}
	}

	return nil
}
