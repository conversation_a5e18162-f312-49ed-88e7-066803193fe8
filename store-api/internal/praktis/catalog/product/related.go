package product

import (
	"fmt"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	"strconv"
	"strings"
	"time"
)

func GetPraktisRelatedProducts(id int64, limit int, variancePercent int) ([]int64, error) {
	cacheKey := fmt.Sprintf("praktis_related_products_%d", id)
	cacheClient := praktis.GetCacheClient()
	if cacheClient.MustExists(cacheKey) {
		cached, err := cacheClient.Get(cacheKey)
		if err == nil {
			var ids []int64
			idsStr := strings.Split(cached, ",")
			for _, idStr := range idsStr {
				id, err = strconv.ParseInt(idStr, 10, 64)
				if err != nil {
					continue
				}
				ids = append(ids, id)
			}

			return ids, nil
		}
	}

	priceAttr, err := mage_store.ProductAttributes.GetAttribute("price")
	if err != nil {
		return nil, err
	}

	var result struct {
		Category int64
		Price    float64
	}
	err = praktis.GetDbClient().Raw(`select 
    c.entity_id as category, cpi.value as price 
from catalog_category_product p
    inner join catalog_category_entity c on c.entity_id = p.category_id
    inner join catalog_product_entity_decimal cpi on 
        cpi.entity_id = p.product_id and cpi.attribute_id = ? and cpi.store_id = 0
where p.product_id = ?
order by c.level desc, c.entity_id desc
limit 1`, priceAttr.AttributeID, id).Scan(&result).Error
	if err != nil {
		return nil, err
	}

	var ids []int64
	maxPrice := result.Price + (result.Price * float64(variancePercent) / 100)
	err = praktis.GetDbClient().Raw(fmt.Sprintf(`select p.entity_id from %s p
 inner join catalog_category_product c on c.product_id = p.entity_id
where c.category_id = ?
and (p.price >= ? and p.price <= ?)
order by p.price asc
limit ?
`, getProductIndexTable()),
		result.Category, result.Price, maxPrice, limit,
	).Scan(&ids).Error

	idsToCache := ""
	for _, id = range ids {
		idsToCache += fmt.Sprintf("%d,", id)
	}

	_ = cacheClient.Save(cacheKey, strings.Trim(idsToCache, ","), 1*time.Hour)

	return ids, err
}

func IsValidProduct(p *model.SimpleProduct) bool {
	if p == nil || p.Price == nil || p.Price.Price == nil || p.Price.Price.Value <= 0 {
		return false
	}

	if p.Name == "" {
		return false
	}

	if p.Stock != nil {
		if p.Stock.BlockForSale {
			return false
		}

		if p.Stock.ManageStock && !p.Stock.InStock && !p.Stock.ShowOutOfStock {
			return false
		}

		if p.Stock.ZeronSiteStatus != "" && p.Stock.ZeronSiteStatus != "1" {
			return false
		}
	}

	return true
}
