package product

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/entity"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"sort"
	"sync"
)

type WarehouseProduct struct {
	ProductSKU    string  `gorm:"-"`
	ProductID     int64   `gorm:"column:product_id"`
	WarehouseCode string  `gorm:"column:warehouse_code"`
	Qty           float64 `gorm:"column:qty"`
	HasSample     int     `gorm:"column:has_sample"`
}

func (wp *WarehouseProduct) TableName() string {
	return "stenik_zeron_warehouse_product"
}

func (wp *WarehouseProduct) GetStatus() model.AvailabilityStatus {
	if wp.Qty >= 2 {
		return model.AvailabilityStatusAvailable
	} else if wp.Qty > 0 && wp.Qty < 2 {
		return model.AvailabilityStatusLimitedAvailability
	}

	return model.AvailabilityStatusUnavailable
}

func GetProductAvailability(productId int64) ([]*WarehouseProduct, error) {
	var result []*WarehouseProduct
	err := praktis.GetDbClient().Model(&WarehouseProduct{}).
		Where("product_id = ?", productId).Find(&result).Error
	return result, err
}

func GetSkuAvailability(sku string) ([]*WarehouseProduct, error) {
	id, err := NewPraktisProductRepository(nil).GetSkuID(sku)
	if err != nil {
		return nil, err
	}

	return GetProductAvailability(id)
}

func GetStoreAvailabilityModel(sku string) ([]*model.StoreAvailabilityItem, error) {
	var items []*model.StoreAvailabilityItem
	var err error
	var warehouseData []*WarehouseProduct
	warehouseData, err = GetSkuAvailability(sku)
	if err != nil {
		return items, err
	}

	var warehouseMap = map[string]*WarehouseProduct{}
	for _, result := range warehouseData {
		if _, ok := warehouseMap[result.WarehouseCode]; !ok {
			warehouseMap[result.WarehouseCode] = result
		}
	}

	activeStores := GetActiveStores()
	for _, store := range activeStores {
		if warehouseResult, ok := warehouseMap[store.WarehouseCode]; ok {
			items = append(items, &model.StoreAvailabilityItem{
				Available: warehouseResult.GetStatus(),
				Sample:    warehouseResult.HasSample == 1,
				Store:     store.ToModel(),
			})
		} else {
			items = append(items, &model.StoreAvailabilityItem{
				Available: model.AvailabilityStatusUnavailable,
				Sample:    false,
				Store:     store.ToModel(),
			})
		}
	}

	return items, err
}

const availableStores = "praktis_stores:warehouse_codes"

var storeMutex sync.RWMutex

func GetActiveStores() []*entity.PraktisStoreEntity {
	var result []*entity.PraktisStoreEntity

	// Add debugging for DB connection
	db := praktis.GetDbClient()
	if db == nil {
		core_utils.ErrorWarning(fmt.Errorf("database client is nil"))
		return nil
	}

	err := db.Model(&entity.PraktisStoreEntity{}).
		Order("display_order ASC").
		Debug(). // Add debug mode to see the actual SQL query
		Find(&result).Error

	if err != nil {
		core_utils.ErrorWarning(fmt.Errorf("failed to get active stores: %w", err))
		return nil
	}

	if len(result) == 0 {
		core_utils.Debug("no active stores found in database")
		return nil
	}

	// Add debugging for results
	core_utils.Debug("Found %d active stores", len(result))

	var codes []string
	for _, store := range result {
		codes = append(codes, store.WarehouseCode)
		core_utils.Debug("Store: ID=%d, Name=%s, WarehouseCode=%s",
			store.ID, store.Name, store.WarehouseCode)
	}

	// Sort by DisplayOrder for cached stores
	if len(result) > 1 {
		sortStoresByDisplayOrder(result)
	}

	return result
}

// sortStoresByDisplayOrder sorts stores by their DisplayOrder field in ascending order
func sortStoresByDisplayOrder(stores []*entity.PraktisStoreEntity) {
	sort.Slice(stores, func(i, j int) bool {
		return stores[i].DisplayOrder < stores[j].DisplayOrder
	})
}

func GetPickupStores(productIds map[int64]float64) ([]*entity.PraktisStoreEntity, error) {
	var result []*entity.PraktisStoreEntity
	var stockData []*WarehouseProduct
	ids := make([]int64, 0, len(productIds))

	for productId := range productIds {
		ids = append(ids, productId)
	}

	err := praktis.GetDbClient().Model(&WarehouseProduct{}).
		Where("product_id in ?", ids).Find(&stockData).Error
	if err != nil {
		return nil, err
	}

	availableCodes := map[string]map[int64]struct{}{}
	for _, row := range stockData {
		if _, ok := availableCodes[row.WarehouseCode]; !ok {
			availableCodes[row.WarehouseCode] = map[int64]struct{}{}
		}

		orderedQty, ok := productIds[row.ProductID]
		if ok && row.Qty >= orderedQty {
			availableCodes[row.WarehouseCode][row.ProductID] = struct{}{}
		}
	}

	for warehouseCode, availableProducts := range availableCodes {
		if len(availableProducts) == len(productIds) {
			store, err := entity.GetStoreByWarehouseCode(warehouseCode)
			if err != nil {
				core_utils.DebugError(err)
				continue
			}
			// Only include stores that have AcceptOrders enabled
			if store.AcceptOrders {
				result = append(result, store)
			}
		}
	}

	// Sort stores by DisplayOrder
	if len(result) > 1 {
		sortStoresByDisplayOrder(result)
	}

	return result, nil
}
