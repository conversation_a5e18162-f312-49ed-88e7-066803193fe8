package product

import (
	"fmt"
	"github.com/siper92/api-base/api_entity"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"strings"
	"sync"
)

const AllBrandCacheKey = "product:brands"
const FrontendBrandCacheKey = "product:featured_brand"

var _ api_entity.ConvertToModel[*model.Brand] = (*Brand)(nil)

type Brand struct {
	BrandID   int    `gorm:"column:option_id"`
	PageID    int    `gorm:"column:page_id"`
	Name      string `gorm:"column:display_name"`
	Url<PERSON>ey    string `gorm:"column:url_key"`
	Thumbnail string `gorm:"column:thumbnail"`
	Position  int    `gorm:"column:position"`
}

func (b Brand) String() string {
	return fmt.Sprintf(`%d|%d|%s|%s|%s|%d`,
		b.<PERSON>, b.<PERSON>, b.<PERSON>, b.<PERSON>, b.<PERSON>, b.<PERSON>,
	)
}

func strToBrand(row string) (Brand, error) {
	parts := strings.Split(row, "|")
	if len(parts) != 6 {
		return Brand{}, fmt.Errorf("invalid row: %s", row)
	}

	return Brand{
		BrandID:   core_utils.StringToInt(parts[0]),
		PageID:    core_utils.StringToInt(parts[1]),
		Name:      parts[2],
		UrlKey:    parts[3],
		Thumbnail: parts[4],
		Position:  core_utils.StringToInt(parts[5]),
	}, nil
}

func (b Brand) ToModel() *model.Brand {
	if b.BrandID < 1 {
		return nil
	}

	return &model.Brand{
		Name: b.Name,
		Image: &model.Image{
			Src:   b.Thumbnail,
			Alt:   &b.Name,
			Title: &b.Name,
		},
		URL: &model.Link{
			Href:  "/" + b.UrlKey,
			Text:  b.Name,
			Title: &b.Name,
		},
	}
}

func (p *PraktisProduct) LoadBrand() {
	if p.BrandID != p.Brand.BrandID {
		brand, err := getBrandFromCache(p.BrandID)
		core_utils.ErrorWarning(err)

		if brand.BrandID > 0 {
			p.Brand = brand
			return
		}

		brands, err := GetAllBrands(false)
		if err != nil {
			core_utils.ErrorWarning(err)
		}

		for _, b := range brands {
			if b.BrandID == p.BrandID {
				p.Brand = b
				break
			}
		}
	}
}

func getBrandFromCache(brandId int) (Brand, error) {
	row, err := praktis.GetCacheClient().GetMapValue(AllBrandCacheKey, core_utils.ToString(brandId))
	if row != "" {
		return strToBrand(row)
	}

	return Brand{}, err
}

func GetCacheBrands(key string) ([]Brand, error) {
	cacheClient := praktis.GetCacheClient()
	if cacheClient.MustExists(key) {
		brandsData, err := cacheClient.GetMap(key)
		if err != nil {
			return nil, fmt.Errorf("GetCacheBrands: %w", err)
		}

		brands := make([]Brand, 0, len(brandsData))
		for _, brandCache := range brandsData {
			brand, err := strToBrand(brandCache)
			if err != nil {
				return nil, fmt.Errorf("GetCacheBrands: %w", err)
			}

			brands = append(brands, brand)
		}

		return brands, nil
	}

	return nil, nil
}

var brandsLock = sync.RWMutex{}

func GetAllBrands(featured bool) ([]Brand, error) {
	brandsLock.RLock()

	key := AllBrandCacheKey
	if featured {
		key = FrontendBrandCacheKey
	}

	cacheBrands, err := GetCacheBrands(key)
	if err != nil {
		brandsLock.RUnlock()
		return nil, fmt.Errorf("GetAllBrands: %w", err)
	} else if cacheBrands != nil {
		brandsLock.RUnlock()
		return cacheBrands, nil
	}

	brandsLock.RUnlock()
	brandsLock.Lock()
	defer brandsLock.Unlock()

	var whereClause string
	if featured {
		whereClause = "is_enabled = 1 and is_featured = 1"
	} else {
		whereClause = "is_enabled = 1"
	}

	var brands []Brand
	err = praktis.GetDbClient().
		Raw(fmt.Sprintf(`select option_id, page_id, display_name, url_key, thumbnail, position from attributesplash_page 
where %s order by position asc;`, whereClause)).Scan(&brands).Error
	if err != nil {
		return nil, fmt.Errorf("GetAllBrands: %w", err)
	}

	store := praktis.GetPraktisStore()
	cacheData := make(map[string]string)
	result := make([]Brand, len(brands))
	for i, brand := range brands {
		brand.Thumbnail = store.GetMedialUrl(fmt.Sprintf("/attributesplash/%s", brand.Thumbnail))
		result[i] = brand
		cacheData[core_utils.ToString(brand.BrandID)] = brand.String()
	}

	cacheClient := praktis.GetCacheClient()
	err = cacheClient.SaveMap(key, cacheData, cache.InfiniteTTL)

	return result, err
}
