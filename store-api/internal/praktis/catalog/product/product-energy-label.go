package product

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"strings"
	"sync"
)

type EnergyLabel struct {
	Sku                    string
	EnergyClass            string
	SlipFileUrl            string
	EnergyLabelUrl         string
	InstallInstructionsUrl string
}

func (e *EnergyLabel) String() string {
	if e == nil {
		return ""
	}

	return fmt.Sprintf("%s|%s|%s|%s|%s",
		e.Sku, e.EnergyClass, e.SlipFileUrl, e.EnergyLabelUrl, e.InstallInstructionsUrl,
	)
}

func (e *EnergyLabel) InitFromCacheVal(val string) error {
	parts := strings.Split(val, "|")
	if len(parts) != 5 {
		return fmt.Errorf("invalid cache value: %s", val)
	}

	e.Sku = parts[0]
	e.EnergyClass = parts[1]
	e.SlipFileUrl = parts[2]
	e.EnergyLabelUrl = parts[3]
	e.InstallInstructionsUrl = parts[4]

	return nil

}

func (e *EnergyLabel) ToModel() *model.EnergyLabel {
	if e == nil {
		return nil
	}

	// Check if this energy label has any actual data
	if e.EnergyClass == "" && e.SlipFileUrl == "" && e.EnergyLabelUrl == "" && e.InstallInstructionsUrl == "" {
		return nil
	}

	store := praktis.GetPraktisStore()

	// Only include URLs for files that actually exist
	var labelURL *string
	if e.EnergyLabelUrl != "" {
		labelURL = core_utils.ToStringPointer(e.EnergyLabelUrl)
	}

	var infoURL *string
	if e.SlipFileUrl != "" {
		infoURL = core_utils.ToStringPointer(e.SlipFileUrl)
	}

	result := &model.EnergyLabel{
		LabelURL: labelURL,
		InfoURL:  infoURL,
	}

	if e.EnergyClass != "" {
		result.Image = &model.Image{
			Src: store.GetBaseUrl(
				fmt.Sprintf("/skin/frontend/praktis/default/images/energy-label-%s.png",
					strings.ToLower(e.EnergyClass),
				),
			),
			Alt:   &e.EnergyClass,
			Title: &e.EnergyClass,
		}
	} else {
		// Even when the energy class is empty, we must provide an image
		// because it's required by the GraphQL schema
		result.Image = &model.Image{
			Src:   "",
			Alt:   core_utils.ToStringPointer(""),
			Title: core_utils.ToStringPointer(""),
		}
	}

	// If we don't have any relevant URLs or energy class, return nil
	// so the entire energy label will be nil in the response
	if e.EnergyClass == "" && labelURL == nil && infoURL == nil {
		return nil
	}

	return result
}

func (p *PraktisProduct) LoadEnergyLabel() {
	if p.EnergyLabel != nil && p.EnergyLabel.Sku != "" {
		return
	}

	labelObj, err := getEnergyLabel(p.Sku)
	if err != nil {
		core_utils.ErrorWarning(fmt.Errorf("LoadEnergyLabel: %s", err))
	}

	// If no energy label data was found in the database, keep EnergyLabel as nil
	// When no energy label exists, the field should be nil in the API response
	if labelObj == nil {
		p.EnergyLabel = nil
		return
	}

	// If we have an energy label object but all its fields are empty,
	// it's effectively the same as having no energy label
	if labelObj.EnergyClass == "" &&
		labelObj.SlipFileUrl == "" &&
		labelObj.EnergyLabelUrl == "" &&
		labelObj.InstallInstructionsUrl == "" {
		p.EnergyLabel = nil
		return
	}

	// Only set the energy label if it has actual data
	p.EnergyLabel = labelObj
}

const labelsCacheKey = "energy_labels"

func getCachedEnergyLabel(sku string) (*EnergyLabel, error, bool) {
	cacheClient := praktis.GetCacheClient()
	if cacheClient.MustExists(labelsCacheKey) {
		val, err := cacheClient.GetMapValue(labelsCacheKey, sku)
		if err != nil && err.Error() != "redis: nil" {
			return nil, err, true
		} else if val != "" {
			label := &EnergyLabel{}
			err = label.InitFromCacheVal(val)
			return label, err, true
		}

		return nil, nil, true
	}

	return nil, nil, false
}

var labelLock sync.RWMutex

// GetEnergyLabel retrieves energy label information for a product by SKU
// This function is exported to allow direct access from outside the package
func GetEnergyLabel(sku string) (*EnergyLabel, error) {
	labelLock.RLock()
	label, err, hasCache := getCachedEnergyLabel(sku)
	if hasCache {
		labelLock.RUnlock()
		return label, err
	}
	labelLock.RUnlock()

	labelLock.Lock()
	defer labelLock.Unlock()

	label, err, hasCache = getCachedEnergyLabel(sku)
	if hasCache {
		return label, err
	}

	labels, err := getLabels()
	if err != nil {
		return nil, err
	}

	var ok bool
	label, ok = labels[sku]
	err = saveLabelCache(labels)
	core_utils.ErrorWarning(err)

	if !ok {
		return nil, nil
	}

	return label, nil
}

func getEnergyLabel(sku string) (*EnergyLabel, error) {
	return GetEnergyLabel(sku)
}

type EnergyLabelRow struct {
	ID          int
	Sku         string
	FileName    string
	FileType    string
	EnergyClass string
}

func getLabels() (map[string]*EnergyLabel, error) {
	dbLabels := make([]*EnergyLabelRow, 0)
	db := praktis.GetDbClient()

	err := db.Raw(`
		SELECT id, sku, file_name, 
		       CASE 
		           WHEN file_type IS NOT NULL AND file_type != '' THEN file_type 
		           ELSE file_subtype 
		       END as file_type, 
		       energy_class 
		FROM pfg_zeron_inventory_file;
	`).Scan(&dbLabels).Error

	if err != nil {
		err = db.Raw(`SELECT id, sku, file_name, file_type, energy_class FROM pfg_zeron_inventory_file;`).
			Scan(&dbLabels).Error

		if err != nil {
			return nil, err
		}
	}

	mapLabels := make(map[string]*EnergyLabel)
	for _, row := range dbLabels {
		if row.Sku == "" {
			continue
		}

		_label, ok := mapLabels[row.Sku]
		if !ok {
			_label = &EnergyLabel{
				Sku:                    row.Sku,
				EnergyClass:            "",
				SlipFileUrl:            "",
				EnergyLabelUrl:         "",
				InstallInstructionsUrl: "",
			}
		}

		if row.EnergyClass != "" {
			_label.EnergyClass = row.EnergyClass
		}

		fileType := strings.ToLower(row.FileType)
		url := toUrl(row)

		if url != "" {
			if strings.Contains(fileType, "slip") || strings.Contains(fileType, "productslip") {
				_label.SlipFileUrl = url
			} else if strings.Contains(fileType, "label") || strings.Contains(fileType, "energylabel") {
				_label.EnergyLabelUrl = url
			} else if strings.Contains(fileType, "install") || strings.Contains(fileType, "instructions") {
				_label.InstallInstructionsUrl = url
			}
		}

		mapLabels[row.Sku] = _label
	}

	return mapLabels, nil
}

func toUrl(row *EnergyLabelRow) string {
	if row == nil {
		return ""
	}

	fileName := getFileName(row)

	if fileName == "" {
		return ""
	}

	return praktis.GetPraktisStore().GetMedialUrl("/zeron/" + fileName)
}

func getFileName(row *EnergyLabelRow) string {
	if row == nil {
		return ""
	}

	if row.Sku == "" || row.FileType == "" || row.FileName == "" {
		return ""
	}

	name := fmt.Sprintf("%s-%s-%s", row.Sku, row.FileType, row.FileName)
	return strings.ReplaceAll(name, " ", "_")
}

func saveLabelCache(labels map[string]*EnergyLabel) error {
	cacheClient := praktis.GetCacheClient()
	cacheData := make(map[string]string)
	for sku, label := range labels {
		cacheData[sku] = label.String()
	}

	return cacheClient.SaveMap(labelsCacheKey, cacheData, cache.InfiniteTTL)
}
