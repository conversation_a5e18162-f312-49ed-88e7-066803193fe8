package product

import (
	core_utils "github.com/siper92/core-utils"
	"strings"
)

func (r *PraktisProductRepository) GetBundledProducts(id int64) []string {
	product, err := r.GetID(id, []string{"product_bundle_products"})
	if err != nil || product == nil {
		core_utils.ErrorWarning(err)
		return []string{} // Return empty list if product not found or error
	}

	skus := product.MustGetVal("product_bundle_products")
	var listOfSkus []string
	if skus == "" {
		return listOfSkus
	}

	for _, sku := range strings.Split(skus, ",") {
		sku = strings.TrimSpace(sku)
		if sku != "" {
			listOfSkus = append(listOfSkus, sku)
		}
	}

	return listOfSkus
}
