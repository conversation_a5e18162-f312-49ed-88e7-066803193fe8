package catalog

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	"strings"
)

type FilterOperator string

const (
	OperatorEq    FilterOperator = "="
	OperatorNe    FilterOperator = "!="
	OperatorLike  FilterOperator = "LIKE"
	OperatorNlike FilterOperator = "NOT LIKE"
	OperatorGt    FilterOperator = ">"
	OperatorGte   FilterOperator = ">="
	OperatorLt    FilterOperator = "<"
	OperatorLte   FilterOperator = "<="
	OperatorIn    FilterOperator = "IN"
	OperatorNin   FilterOperator = "NOT IN"
)

type Condition string

func (c Condition) String() string {
	return string(c)
}

const (
	ANDCondition Condition = "AND"
	ORCondition  Condition = "OR"
)

type CatalogFilter interface {
	applyFilter(db *gorm.DB) *gorm.DB
}

var _ CatalogFilter = (*catalogFilter)(nil)

type catalogFilter struct {
	Condition     Condition
	AttributeCode string
	Operator      FilterOperator
	Value         string
}

// dynamicFilter val example: "brand = 1 OR color != 2 AND size IN (3,4)"
type dynamicFilter string

func (d dynamicFilter) String() string {
	return string(d)
}

func (d dynamicFilter) getFilters() []CatalogFilter {
	var filters []CatalogFilter
	var currPart string
	if strings.Contains(string(d), "OR") || strings.Contains(string(d), "AND") {
		for _, part := range strings.Split(d.String(), " ") {
			part = strings.TrimSpace(part)
			switch part {
			case "AND", "OR":
				filters = append(
					filters,
					dynamicFilterPart(currPart).getFilter(Condition(part)),
				)
				currPart = ""
			default:
				currPart += part + " "
			}
		}
	} else {
		filters = append(
			filters,
			dynamicFilterPart(d).getFilter(ANDCondition),
		)
	}

	return filters
}

// dynamicFilterPart val example: "brand = 1" or "color != 2" or "size IN (3,4)" - <attr> <FilterOperator> <val>
type dynamicFilterPart string

func (d dynamicFilterPart) String() string {
	return string(d)
}

func (d dynamicFilterPart) getFilter(con Condition) CatalogFilter {
	var filter CatalogFilter
	parts := strings.Split(d.String(), " ")
	if len(parts) < 3 {
		core_utils.Debug("Invalid filter format for: %s", d.String())
		return filter
	}

	switch con {
	case ANDCondition, ORCondition:
		filter = catalogFilter{
			Condition:     con,
			AttributeCode: strings.TrimSpace(parts[0]),
			Operator:      FilterOperator(strings.TrimSpace(parts[1])),
			Value:         strings.TrimSpace(strings.Join(parts[2:], " ")),
		}
	default:
		core_utils.Debug("Unknown filter type: %s", con)
	}

	return filter
}

func (c catalogFilter) applyFilter(qb *gorm.DB) *gorm.DB {
	availableFilters := GetAvailableFilters()

	switch c.Condition {
	case ANDCondition:
		_, ok := availableFilters[c.AttributeCode]
		if ok || isFieldExcludedFromAttributes(c.AttributeCode) {
			sql := fmt.Sprintf(
				"e.%s %s %s", c.AttributeCode, c.Operator, c.Value,
			)

			qb = qb.Where(sql)
		} else {
			attr, err := mage_store.ProductAttributes.GetAttribute(c.AttributeCode)
			if err != nil {
				core_utils.ErrorWarning(err)
				return qb
			}

			sql := fmt.Sprintf(`INNER JOIN %s AS %s 
ON %s.entity_id = e.entity_id AND %s.store_id = %d AND %s.attribute_id = %d AND %s.value %s %s`,
				attr.GetMagentoTable(),
				attr.GetAlias(1),
				attr.GetAlias(1),
				attr.GetAlias(1),
				1,
				attr.GetAlias(1),
				attr.GetID(),
				attr.GetAlias(1),
				c.Operator,
				c.Value,
			)

			qb = qb.InnerJoins(sql)
		}
	case ORCondition:
		if _, ok := availableFilters[c.AttributeCode]; ok {
			sql := fmt.Sprintf(
				"e.%s %s %s", c.AttributeCode, c.Operator, c.Value,
			)

			qb = qb.Or(sql)
		} else {
			core_utils.Warning("OR condition not supported for attribute: %s", c.AttributeCode)
		}
	default:
		core_utils.Debug("Unknown condition: %s", c.Condition)
	}

	return qb
}
