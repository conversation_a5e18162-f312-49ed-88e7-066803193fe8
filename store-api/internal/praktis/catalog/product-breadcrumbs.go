package catalog

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	mage_product "praktis.bg/store-api/packages/magento-core/mage-store/mage-product"
)

func GetProductBreadcrumbs(productId int64) []*model.Breadcrumb {
	cacheKey := fmt.Sprintf("praktis_category_breadcrumbs:%d", productId)
	_ = cacheKey

	result := []*model.Breadcrumb{
		{
			Label: "Начало",
			URL:   "/",
		},
	}

	categories, err := mage_product.GetProductCategories(productId)
	if err != nil {
		core_utils.ErrorWarning(err)
		return result
	}

	for _, cat := range categories {
		if cat < 3 {
			continue
		}

		category, err := GetCategoryEntity(cat)
		if err != nil {
			core_utils.ErrorWarning(err)
			continue
		}

		result = append(result, &model.Breadcrumb{
			Label:    category.GetAttributeValue("name"),
			URL:      category.GetRelPath(),
			Siblings: getSiblingsBreadcrumbs(category),
			Children: getChildrenBreadcrumbs(category),
		})
	}

	return result
}
