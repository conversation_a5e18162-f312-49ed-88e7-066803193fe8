package catalog

import (
	"github.com/siper92/api-base/cache"
	"gorm.io/gorm"
	"praktis.bg/store-api/internal/praktis"
)

type SplashPageRepository struct {
	db *gorm.DB
}

func (r *SplashPageRepository) DB() *gorm.DB {
	if r.db == nil {
		r.db = praktis.GetDbClient()
	}

	return r.db
}

func (r *SplashPageRepository) Cache() *cache.RedisCacheProvider {
	return praktis.GetCacheClient()
}

func (r *SplashPageRepository) GetByID(id int64) (*SplashPage, error) {
	page := &SplashPage{
		ID: id,
	}
	if r.Cache().MustExists(page.CacheKey()) {
		err := r.Cache().LoadObj(page)
		if err != nil {
			return nil, err
		}
		return page, nil
	}

	if err := r.DB().
		Table((&SplashPage{}).TableName()+" as page").
		Select("page.*, a.attribute_code").
		Joins("inner join eav_attribute_option o on page.option_id = o.option_id").
		Joins("inner join eav_attribute a on o.attribute_id = a.attribute_id").
		Find(page, id).Error; err != nil {
		return page, err
	}

	if err := r.Cache().SaveObj(page); err != nil {
		return page, err
	}

	return page, nil
}

func NewSplashPageRepository(db *gorm.DB) *SplashPageRepository {
	return &SplashPageRepository{
		db: db,
	}
}
