package catalog

import (
	"fmt"
	"github.com/siper92/api-base/cache"
	"strconv"
	"time"
)

var _ cache.CacheableObject = (*SplashPage)(nil)

func (s *SplashPage) CacheKey() string {
	return fmt.Sprintf("brand_page:%d", s.ID)
}

func (s *SplashPage) CacheTTL() time.Duration {
	return cache.InfiniteTTL
}

func (s *SplashPage) SetCacheKey(key string) {
	s.cacheKey = key
}

func (s *SplashPage) IsCacheLoaded() bool {
	return s.isCacheLoaded
}

func (s *SplashPage) GetCacheObject() map[string]string {
	return map[string]string{
		"id":                strconv.FormatInt(s.ID, 10),
		"title":             s.Title,
		"url":               s.URL,
		"icon_url":          s.IconURL,
		"option_id":         strconv.FormatInt(s.OptionID, 10),
		"attribute_code":    s.AttributeCode,
		"meta_title":        s.<PERSON>,
		"meta_description":  s.MetaDescription,
		"meta_keywords":     s.MetaKeywords,
		"description":       s.Description,
		"short_description": s.ShortDescription,
	}
}

func (s *SplashPage) SetCacheObject(cacheData map[string]string) error {
	for key, value := range cacheData {
		switch key {
		case "id":
			s.ID, _ = strconv.ParseInt(value, 10, 64)
		case "title":
			s.Title = value
		case "url":
			s.URL = value
		case "icon_url":
			s.IconURL = value
		case "option_id":
			s.OptionID, _ = strconv.ParseInt(value, 10, 64)
		case "attribute_code":
			s.AttributeCode = value
		case "meta_title":
			s.MetaTitle = value
		case "meta_description":
			s.MetaDescription = value
		case "meta_keywords":
			s.MetaKeywords = value
		case "description":
			s.Description = value
		case "short_description":
			s.ShortDescription = value
		default:
			return fmt.Errorf("unknown property '%s' for SplashPage", key)
		}
	}

	s.isCacheLoaded = true

	return nil
}
