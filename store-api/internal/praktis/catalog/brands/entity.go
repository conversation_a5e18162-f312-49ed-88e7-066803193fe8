package catalog

import (
	"github.com/siper92/api-base/cache"
)

var _ cache.CacheableObject = (*SplashPage)(nil)

type SplashPage struct {
	cacheKey      string
	isCacheLoaded bool

	ID      int64  `gorm:"column:page_id"`
	Title   string `gorm:"column:display_name"`
	URL     string `gorm:"column:url_key"`
	IconURL string `gorm:"column:thumbnail"`

	OptionID      int64  `gorm:"column:option_id"`
	AttributeCode string `gorm:"column:attribute_code"`

	// meta
	MetaTitle       string `gorm:"column:page_title"`
	MetaDescription string `gorm:"column:meta_description"`
	MetaKeywords    string `gorm:"column:meta_keywords"`

	// content
	Description      string `gorm:"column:description"`
	ShortDescription string `gorm:"column:short_description"`
}

func (*SplashPage) TableName() string {
	return "attributesplash_page"
}
