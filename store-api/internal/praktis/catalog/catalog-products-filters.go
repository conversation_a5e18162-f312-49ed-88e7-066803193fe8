package catalog

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
	"sort"
	"strings"
	"time"
)

func (c *ProductIndexCollection) GetFilters() ([]*model.AvailableFilter, error) {
	if c.filters == nil {
		var filters []*model.AvailableFilter
		attributes, err := c.getCollectionAttributes()
		if err != nil {
			return c.filters, err
		} else if len(attributes) < 1 {
			return c.filters, nil
		}

		attributeObjects, filtersSelect := c.getAttributeSelect(attributes)
		var result map[string]interface{}
		err = c.GetFilteredSelect().Select(filtersSelect).Scan(&result).Error
		if err != nil {
			return c.filters, err
		}

		var priceFilter *model.AvailableFilter
		var availabilityFilter *model.AvailableFilter
		var haveSpecialPrice bool
		var priceGetError error
		var priceDone = make(chan struct{})
		go func() {
			defer close(priceDone)
			priceRange, err := c.getProductsPricesRange()
			if err != nil {
				priceGetError = err
				return
			}

			haveSpecialPrice = priceRange.HasSpecialPrice
			priceFilter = newPriceFilter(priceRange.MinPrice, priceRange.MaxPrice)

			ids, err := c.getAllIds()
			core_utils.ErrorWarning(err)
			availabilityFilter, err = newAvailabilityFilter(ids)
			core_utils.ErrorWarning(err)
		}()

		for _, attr := range attributeObjects {
			if attr.BackendType != "int" {
				core_utils.Debug("Attribute %s has no options", attr.AttributeCode)
				continue
			}

			attributeCode := attr.AttributeCode.String()
			values, ok := result[attributeCode+"_uniques"]
			if !ok {
				core_utils.Debug("No values for attribute %s", attributeCode)
				continue
			}

			var collectionOptions []*model.AttributeOption
			for _, option := range attr.GetSortedOptions(values) {
				collectionOptions = append(collectionOptions, &model.AttributeOption{
					Label: option.Label,
					Value: types.ToStr(option.Value),
					Order: option.SortOrder,
				})
			}

			sort.Slice(collectionOptions, func(i, j int) bool {
				if collectionOptions[i].Order == collectionOptions[j].Order {
					return collectionOptions[i].Label < collectionOptions[j].Label
				}

				return collectionOptions[i].Order < collectionOptions[j].Order
			})

			if len(collectionOptions) < 1 {
				//core_utils.Debug("No options for attribute %s", attributeCode)
				continue
			}

			filters = append(filters, &model.AvailableFilter{
				AttributeCode: attributeCode,
				Label:         attr.FrontendLabel,
				Type:          model.FilterRenderTypeList,
				RequestVar:    attributeCode,
				Position:      attr.Position,
				Options:       collectionOptions,
			})
		}

		<-priceDone
		if priceGetError == nil && priceFilter != nil {
			filters = append(filters, priceFilter)
		}

		f := getOnSaleFilter(result, haveSpecialPrice)
		if f != nil && f.AttributeCode != "" {
			filters = append(filters, f)
		}

		if availabilityFilter != nil {
			filters = append(filters, availabilityFilter)
		}

		c.filters = filters
		sort.Slice(c.filters, func(i, j int) bool {
			if c.filters[i].Position == c.filters[j].Position {
				return c.filters[i].Label < c.filters[j].Label
			}

			return c.filters[i].Position < c.filters[j].Position
		})
	}

	return c.filters, nil
}

func (c *ProductIndexCollection) getAttributeSelect(attributes []string) ([]mage_entity.CoreAttribute, string) {
	var attributeObjects []mage_entity.CoreAttribute
	selectU := `
GROUP_CONCAT(DISTINCT zeron_in_broschure SEPARATOR ',') as on_sales_in_broschure,
GROUP_CONCAT(DISTINCT is_bestselling SEPARATOR ',') as on_sales_is_bestselling,
GROUP_CONCAT(DISTINCT price_group_type SEPARATOR ',') as on_sales_price_group_type,
`
	for _, attrCode := range attributes {
		attr, err := mage_entity.ProductAttributes.GetAttribute(attrCode)
		if err != nil || attr.AttributeCode == "" {
			core_utils.ErrorWarning(err)
			continue
		}

		attributeObjects = append(attributeObjects, attr)
		selectU += fmt.Sprintf(
			"\nGROUP_CONCAT(DISTINCT `%s` SEPARATOR ',') as %s_uniques,",
			attr.AttributeCode,
			attr.AttributeCode,
		)
	}

	return attributeObjects, strings.Trim(selectU, ",")
}

func GetAvailableFilters() map[string]string {
	var result map[string]string
	var err error

	p := &product.PraktisProduct{}
	cacheKey := "available-filters"
	cacheClient := praktis.GetCacheClient()
	result, err = cacheClient.GetMap(cacheKey)
	if err != nil {
		core_utils.ErrorWarning(err)
		return result
	} else if len(result) < 1 {
		// describe table and get columns
		var results []struct {
			Field string
			Type  string
		}

		praktis.GetDbClient().Raw("describe " + p.TableName()).Scan(&results)

		filters := make(map[string]string)
		for _, field := range results {
			if isFieldExcludedFromAttributes(field.Field) {
				continue
			}

			filters[field.Field] = field.Type
		}

		err = cacheClient.Save(cacheKey, filters, 10*time.Minute)
		if err != nil {
			core_utils.ErrorWarning(err)
		}

		return filters
	}

	return result
}

func newPriceFilter(min float64, max float64) *model.AvailableFilter {
	if min == max {
		return nil
	}

	return &model.AvailableFilter{
		AttributeCode: "price",
		RequestVar:    "price",
		Label:         "Цена",
		Position:      getFilterPosition("price"),
		Type:          model.FilterRenderTypeSlider,
		Options: []*model.AttributeOption{
			{
				Label: "min",
				Value: fmt.Sprintf("%.2f", min),
			},
			{
				Label: "max",
				Value: fmt.Sprintf("%.2f", max),
			},
		},
	}
}

func getFilterPosition(attrCode string) int {
	switch attrCode {
	case "price":
		return -10
	case OnSaleFilterCode:
		return -9
	case AvailabilityFilterCode:
		return -8
	case "cat":
		return -1000
	default:
		return 0
	}
}

func isFieldExcludedFromAttributes(fieldKey string) bool {
	return fieldKey == "entity_id" ||
		fieldKey == "sku" ||
		fieldKey == "price_group_type" ||
		//fieldKey == "price" ||
		fieldKey == "status" ||
		fieldKey == "special_price" ||
		fieldKey == "is_bestselling" ||
		fieldKey == "zeron_in_broschure" ||
		fieldKey == "name" ||
		fieldKey == "attribute_set_id" ||
		fieldKey == "qty" ||
		fieldKey == "special_from_date" ||
		fieldKey == "special_to_date" ||
		fieldKey == "thumbnail" ||
		fieldKey == "visibility" ||
		fieldKey == "created_at" ||
		fieldKey == "product_bundle_products" ||
		fieldKey == "url_key"
}

// OnSaleFilterCode On sale filter
const OnSaleFilterCode = "onSale"

const (
	OnSaleFilterOnSale      = "1"
	OnSaleFilterOnlinePromo = "2"
	OnSaleFilterBrochure    = "3"
	OnSaleFilterBuyCheap    = "4"
)

func getOnSaleOptions(value string) *model.AttributeOption {
	label := ""
	order := 0 // Default order

	switch value {
	case OnSaleFilterOnSale:
		label = "На промоция"
		order = 1
	case OnSaleFilterOnlinePromo:
		label = "Онлайн промоция"
		order = 2
	case OnSaleFilterBrochure:
		label = "Продукт от брошура"
		order = 3
	case OnSaleFilterBuyCheap:
		label = "Купи изгодно"
		order = 4
	default:
		return nil
	}

	return &model.AttributeOption{
		Label: label,
		Value: value,
		Order: order, // Set order so we can sort these options consistently
	}
}

func getOnSaleFilter(result map[string]interface{}, haveSpecialPrice bool) *model.AvailableFilter {
	inBroschureRaw, _ := result["on_sales_in_broschure"]
	inBroschure := types.ToStr(inBroschureRaw)
	isBestsellingRaw, _ := result["on_sales_is_bestselling"]
	isBestselling := types.ToStr(isBestsellingRaw)
	priceGroupTypeRaw, _ := result["on_sales_price_group_type"]
	priceGroupType := types.ToStr(priceGroupTypeRaw)

	var options []*model.AttributeOption

	if haveSpecialPrice {
		v := getOnSaleOptions(OnSaleFilterOnSale)
		if v != nil {
			options = append(options, v)
		}
	}

	if len(inBroschure) > 0 {
		vals := strings.Split(inBroschure, ",")
		for _, val := range vals {
			if val == "1" {
				v := getOnSaleOptions(OnSaleFilterBrochure)
				if v != nil {
					options = append(options, v)
				}
				break
			}
		}
	}

	if len(isBestselling) > 0 {
		vals := strings.Split(isBestselling, ",")
		for _, val := range vals {
			if val == "1" {
				v := getOnSaleOptions(OnSaleFilterOnlinePromo)
				if v != nil {
					options = append(options, v)
				}
				break
			}
		}
	}

	if len(priceGroupType) > 0 {
		vals := strings.Split(priceGroupType, ",")
		for _, val := range vals {
			if val == "20" {
				v := getOnSaleOptions(OnSaleFilterBuyCheap)
				if v != nil {
					options = append(options, v)
				}
				break
			}
		}
	}

	if len(options) < 1 {
		return nil
	}

	// Sort options by Order field
	sort.Slice(options, func(i, j int) bool {
		return options[i].Order < options[j].Order
	})

	return &model.AvailableFilter{
		AttributeCode: OnSaleFilterCode,
		RequestVar:    OnSaleFilterCode,
		Label:         "Промоции",
		Type:          model.FilterRenderTypeList,
		Position:      getFilterPosition(OnSaleFilterCode),
		Options:       options,
	}
}

const AvailabilityFilterCode = "available"

const (
	OrderOnline            = 1
	GetFromStore           = 2
	ByRequest              = 3
	WarehouseSofia         = 21
	WarehousePlovdiv       = 22
	WarehouseStaraZagora   = 23
	WarehouseVelikoTurnovo = 24
	WarehouseHaskovo       = 25
	WarehouseVidin         = 26
	WarehouseRuse          = 27
	WarehouseMega          = 28
)

var warehousesToStore = map[int][]int{
	WarehouseSofia:         []int{101, 301},
	WarehousePlovdiv:       []int{201},
	WarehouseStaraZagora:   []int{401, 441, 420},
	WarehouseVelikoTurnovo: []int{501},
	WarehouseHaskovo:       []int{601},
	WarehouseVidin:         []int{801},
	WarehouseRuse:          []int{901},
	WarehouseMega:          []int{1001},
}

var labelMap = map[int]string{
	OrderOnline:          "Може да се поръча онлайн",
	GetFromStore:         "За взимане от обект",
	ByRequest:            "По поръчка",
	WarehouseSofia:       "София",
	WarehousePlovdiv:     "Пловдив",
	WarehouseStaraZagora: "Стара Загора",
	WarehouseHaskovo:     "Хасково",
	WarehouseVidin:       "Видин",
	WarehouseRuse:        "Русе",
	WarehouseMega:        "Практис Мега",
}

func newAvailabilityFilter(ids []int64) (*model.AvailableFilter, error) {
	attr, err := mage_entity.ProductAttributes.GetAttribute("zeron_site_status")
	if err != nil {
		return nil, err
	}

	var finalOptions []*model.AttributeOption

	onlineOptions := make(map[int]*model.AttributeOption)

	var siteStatuses []int
	err = praktis.GetDbClient().Raw(`select value as count_value
		from catalog_product_entity_varchar
		where entity_id in ?
		and attribute_id = ? and value in (1, 2)
		group by value;`, ids, attr.AttributeID).Scan(&siteStatuses).Error
	if err != nil {
		return nil, err
	} else {
		for _, status := range siteStatuses {
			switch status {
			case OrderOnline, GetFromStore:
				onlineOptions[status] = &model.AttributeOption{
					Label: labelMap[status],
					Value: fmt.Sprintf("%d", status),
					Order: status, // Will be used for sorting
				}
			}
		}
	}

	byRequest := 0
	err = praktis.GetDbClient().Raw(`select count(*) as count from cataloginventory_stock_item 
	where manage_stock = 0 and product_id in ?`, ids).Scan(&byRequest).Error
	if err != nil {
		return nil, err
	} else if byRequest > 0 {
		onlineOptions[ByRequest] = &model.AttributeOption{
			Label: labelMap[ByRequest],
			Value: fmt.Sprintf("%d", ByRequest),
			Order: ByRequest,
		}
	}

	var storeOptions []*model.AttributeOption
	warehousesCodes := make([]int, 0)
	err = praktis.GetDbClient().Raw(`select p.warehouse_code as warehouse_code
		from stenik_zeron_warehouse_product p 
		where product_id in ? and (p.qty > 0 or p.has_sample = 1)
		group by warehouse_code;`, ids).Scan(&warehousesCodes).Error
	if err != nil {
		return nil, err
	}

	if len(warehousesCodes) > 0 {
		storeFilterMap := make(map[int]bool)
		for _, warehouse := range warehousesCodes {
			for store, codes := range warehousesToStore {
				for _, code := range codes {
					if code == warehouse {
						storeFilterMap[store] = true
						break
					}
				}
			}
		}

		// First add OrderOnline and GetFromStore in a specific order
		// These should always be the first two options
		// TODO: doesnt add the GetFromStore option, HARDCODED!!!
		onlineOptions[GetFromStore] = &model.AttributeOption{
			Label: labelMap[GetFromStore],
			Value: fmt.Sprintf("%d", GetFromStore),
			Order: GetFromStore, // Will be used for sorting
		}

		for _, status := range []int{OrderOnline, GetFromStore} {
			if option, exists := onlineOptions[status]; exists {
				finalOptions = append(finalOptions, option)
			}
		}

		// Then add ByRequest if it exists
		if option, exists := onlineOptions[ByRequest]; exists {
			finalOptions = append(finalOptions, option)
		}

		activeStores := product.GetActiveStores()

		inlineStoreDisplayOrders := make(map[int64]int)
		inlineStoreFilterMap := make(map[int]bool)
		inlineLableStoreMap := make(map[int]string)
		for _, store := range activeStores {
			inlineStoreFilterMap[int(store.ID)] = store.AcceptOrders
			inlineLableStoreMap[int(store.ID)] = store.Name
			inlineStoreDisplayOrders[store.ID] = store.DisplayOrder
		}

		for storeID := range inlineStoreFilterMap {
			if label, ok := inlineLableStoreMap[storeID]; ok {
				displayOrder, _ := inlineStoreDisplayOrders[int64(storeID)]
				storeOptions = append(storeOptions, &model.AttributeOption{
					Label: label,
					Value: fmt.Sprintf("%d", storeID),
					Order: displayOrder + len(finalOptions),
				})
			}
		}

		sort.Slice(storeOptions, func(i, j int) bool {
			return storeOptions[i].Order < storeOptions[j].Order
		})
	}

	finalOptions = append(finalOptions, storeOptions...)

	return &model.AvailableFilter{
		AttributeCode: AvailabilityFilterCode,
		RequestVar:    AvailabilityFilterCode,
		Label:         "Наличност",
		Type:          model.FilterRenderTypeList,
		Position:      getFilterPosition(AvailabilityFilterCode),
		Options:       finalOptions,
	}, nil
}
