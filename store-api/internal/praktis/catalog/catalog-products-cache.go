package catalog

import (
	"fmt"
	"praktis.bg/store-api/packages/magento-core/types"
	"time"
)

type CachePrefixType string

const (
	AttributeCacheType    CachePrefixType = "attribute"
	ProductCountCacheType CachePrefixType = "product_count"
)

func (c *ProductIndexCollection) getCachePrefix(t CachePrefixType) string {
	var catID types.EntityID
	cacheKeyPrefix := fmt.Sprintf("product_index_no_filter_%d", 0)
	if c.category != nil {
		catID = c.category.EntityID
		cacheKeyPrefix = fmt.Sprintf("product_index_cat_%d", catID)
	} else if c.attributeFilter != nil && c.attributeFilter.AttributeCode != "" {
		cacheKeyPrefix = fmt.Sprintf("product_index_%s_%s",
			c.attributeFilter.AttributeCode,
			c.attributeFilter.Value,
		)
	}

	return fmt.Sprintf("%s_%s", cacheKeyPrefix, t)
}

func (c *ProductIndexCollection) usesCache() bool {
	if c.category != nil || c.attributeFilter != nil {
		if len(c.appliedFilters) > 0 {
			return false
		}

		return true
	}

	return false
}

func (c *ProductIndexCollection) CacheTTL() time.Duration {
	return time.Minute * 10
}
