package stock

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/catalog"
	"praktis.bg/store-api/internal/praktis/catalog/product"
)

func GetProductStock(id int64, hasImages bool) (*model.ProductStock, error) {
	var err error
	result := &model.ProductStock{
		Qty:                  0,
		BlockForSale:         false,
		ZeronSiteStatus:      "",
		ZeronBlockedDelivery: false,
		ManageStock:          false,
		InStock:              false,
		HasImages:            hasImages,
		ShowOutOfStock:       praktis.GetPraktisStore().GetConfigBool("cataloginventory/options/show_out_of_stock", false),
	}

	cacheKey := fmt.Sprintf("product:stock:%d", id)
	cacheClient := praktis.GetCacheClient()
	if cacheClient.MustExists(cacheKey) {
		data, _ := cacheClient.GetMap(cacheKey)
		if len(data) > 0 {
			return catalog.ToProductStockModel(data), nil
		}
	} else {
		result, err = catalog.GetProductStockData(id)
		if err != nil {
			return nil, err
		}
		result.HasImages = hasImages

		mapData := catalog.ProductStockToMap(result)
		if len(mapData) > 0 {
			core_utils.ErrorWarning(cacheClient.SaveMap(
				cacheKey,
				mapData,
				(&product.PraktisProduct{}).CacheTTL(),
			))
		}
	}

	return result, nil
}

type SiteStatus string

const (
	NOT_ACTIVE               SiteStatus = "not_active"
	DEPLETED                            = "depleted"
	AVAILABLE                           = "available"
	AWAITING_DELIVERY                   = "awaiting_delivery"
	ONLY_IN_STORE                       = "only_in_store"
	LIMITED_VISIBILITY                  = "limited_visibility"
	AVAILABLE_PICKUP                    = "available_pickup"
	AWAITING_DELIVERY_PICKUP            = "awaiting_delivery_pickup"
	INDIVIDUAL_ORDER                    = "individual_order"
	INDIVIDUAL_ORDER_PICKUP             = "individual_order_pickup"
)

// Constants for Zeron site status
const (
	ZERON_STITE_STATUS_1 = "1"
	ZERON_STITE_STATUS_3 = "3"
	ZERON_STITE_STATUS_4 = "4"
	ZERON_STITE_STATUS_5 = "5"
)

func GetZeronSiteStatus(stock *model.ProductStock) SiteStatus {
	if stock == nil {
		return NOT_ACTIVE
	}

	if !stock.HasImages {
		// no images
		return NOT_ACTIVE
	} else if stock.BlockForSale {
		// Block for sale = YES
		return DEPLETED
	} else if stock.ManageStock {
		// product stock is managed
		if stock.ZeronSiteStatus == ZERON_STITE_STATUS_1 {
			// Site status 1
			if stock.InStock {
				return AVAILABLE
			} else if stock.ShowOutOfStock {
				if stock.ZeronBlockedDelivery {
					return DEPLETED
				} else {
					return AWAITING_DELIVERY
				}
			} else {
				return DEPLETED
			}
		} else if stock.ZeronSiteStatus == ZERON_STITE_STATUS_3 {
			if !stock.InStock {
				return DEPLETED
			} else {
				return ONLY_IN_STORE
			}
		} else if stock.ZeronSiteStatus == ZERON_STITE_STATUS_4 {
			return LIMITED_VISIBILITY
		} else if stock.ZeronSiteStatus == ZERON_STITE_STATUS_5 {
			return LIMITED_VISIBILITY
		} else {
			// Site status != 1, 3, 4, 5 (most likely 2)
			if stock.InStock {
				return AVAILABLE_PICKUP
			} else if stock.ShowOutOfStock {
				if stock.ZeronBlockedDelivery {
					return DEPLETED
				} else {
					return AWAITING_DELIVERY_PICKUP
				}
			} else {
				return DEPLETED
			}
		}
	} else {
		// Block for sales is NO and stock is not managed
		if stock.ZeronBlockedDelivery {
			return DEPLETED
		} else if stock.ZeronSiteStatus == ZERON_STITE_STATUS_3 {
			if !stock.InStock {
				return DEPLETED
			} else {
				return ONLY_IN_STORE
			}
		} else if stock.ZeronSiteStatus == ZERON_STITE_STATUS_4 {
			return LIMITED_VISIBILITY
		} else if stock.ZeronSiteStatus == ZERON_STITE_STATUS_5 {
			return LIMITED_VISIBILITY
		} else {
			if stock.ZeronSiteStatus == ZERON_STITE_STATUS_1 {
				return INDIVIDUAL_ORDER
			} else {
				return INDIVIDUAL_ORDER_PICKUP
			}
		}
	}
}

func (s SiteStatus) IsOnlyAvailableForStorePickup() bool {
	return s == AWAITING_DELIVERY_PICKUP || s == AVAILABLE_PICKUP || s == INDIVIDUAL_ORDER_PICKUP
}
