package router

import (
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/catalog"
	"praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
)

type RouteParams []*model.QueryParam

func (r RouteParams) GetParam(key string) *model.QueryParam {
	for _, param := range r {
		if param.Name == key {
			return param
		}
	}

	return nil
}

func (r RouteParams) GetAppliedFilters() []*model.AppliedFilter {
	appliedFilters := make([]*model.AppliedFilter, 0)

	available := catalog.GetAvailableFilters()

	storeID := praktis.GetPraktisStore().GetID()
	attrRepo := mage_store.NewAttributeRepository(storeID)

	if len(available) > 0 && len(r) > 0 {
		for _, param := range r {
			label := param.Name
			var add bool
			switch param.Name {
			case catalog.OnSaleFilterCode:
				label = "Промоции"
				add = true
			case catalog.AvailabilityFilterCode:
				label = "Наличност"
				add = true
			default:
				_, add = available[param.Name]
				if add {
					attrEntity := attrRepo.GetAttribute(types.ProductEntityType, param.Name)
					if attrEntity != nil {
						label = attrEntity.FrontendLabel
					}
				}
			}

			if add {
				appliedFilters = append(appliedFilters, &model.AppliedFilter{
					AttributeCode: param.Name,
					RequestVar:    param.Name,
					Label:         label,
					Value:         param.Value,
				})
			}
		}
	}

	// all available catalog filters

	return appliedFilters
}

func (r RouteParams) GetPageSize() int {
	limit := r.GetIntValue("limit", 24)
	if limit%12 == 0 {
		return limit
	}

	return 24
}

func (r RouteParams) GetPage() int {
	page := r.GetIntValue("p", 1)
	if page > 0 {
		return page
	}

	return 1
}

func (r RouteParams) GetSort() *model.CollectionSort {
	field := r.GetStrValue("sort", "position")
	dir := model.SortDirection(r.GetStrValue("order", model.SortDirectionDesc.String()))

	found := false
	for _, sort := range catalog.GetAvailableSorts() {
		if sort.Value == field && sort.Dir == dir {
			found = true
			break
		}
	}

	if found {
		return &model.CollectionSort{
			Value: field,
			Dir:   dir,
		}
	}

	return &model.CollectionSort{
		Value: "position",
		Dir:   model.SortDirectionDesc,
	}
}

func (r RouteParams) GetIntValue(s string, i int) int {
	param := r.GetParam(s)
	if param != nil {
		intValue, err := strconv.Atoi(param.Value)
		if err == nil {
			return intValue
		}
	}

	return i
}

func (r RouteParams) GetStrValue(s string, i string) string {
	param := r.GetParam(s)
	if param != nil {
		return param.Value
	}

	return i
}

func (r RouteParams) GetState() *model.CatalogState {
	return &model.CatalogState{
		Filters: &model.Filters{
			Applied: r.GetAppliedFilters(),
		},
		Pager: &model.Pager{
			Page:       r.GetPage(),
			PageSize:   r.GetPageSize(),
			TotalItems: 0,
		},
		Sort: &model.CollectionSort{
			Value: r.GetStrValue("sort", "position"),
			Dir: model.SortDirection(
				r.GetStrValue("order", model.SortDirectionDesc.String()),
			),
		},
	}
}
