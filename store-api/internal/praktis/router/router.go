package router

import (
	"fmt"
	"net/http"

	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/catalog"
	brands "praktis.bg/store-api/internal/praktis/catalog/brands"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
)

func GetRoutePage(
	url *magento_core.UrlRewrite,
	params RouteParams,
) (*model.Page, error) {
	idPath := url.IDPath
	id := idPath.GetID()

	switch idPath.Type() {
	case magento_core.CategoryUrl:
		pageData, err := getCategoryPage(id, params)
		if err != nil {
			return nil, err
		}

		return &model.Page{
			Status: &model.PageStatus{
				StatusCode: http.StatusOK,
			},
			Breadcrumbs: catalog.GetCategoryBreadcrumbs(id),
			Data:        pageData,
		}, nil
	case magento_core.ProductUrl:
		return &model.Page{
			Status: &model.PageStatus{
				StatusCode: http.StatusOK,
			},
			Breadcrumbs: catalog.GetProductBreadcrumbs(id.Int64()),
			Data: &model.ProductPage{
				Product: &model.SimpleProduct{
					ID: int64(id),
				},
			},
		}, nil
	case magento_core.CmsPageUrl:
		store := magento_core.GetStoreClient().GetStore()
		cmsPage, err := mage_entity.NewCMSPageRepository(store).Get(int64(id))
		if err != nil {
			return nil, err
		}

		return &model.Page{
			Status: &model.PageStatus{
				StatusCode: http.StatusOK,
			},
			Breadcrumbs: []*model.Breadcrumb{
				{
					Label: "Начало",
					URL:   "/",
				},
				{
					Label: cmsPage.Title,
					URL:   "#",
				},
			},
			Data: &model.CMSPage{
				Links:      getCmsLinks(),
				Identifier: cmsPage.Identifier,
				Title:      cmsPage.Title,
				Content:    cmsPage.Content,
			},
		}, nil
	case magento_core.SplashPageUrl:
		splashPage, err := brands.NewSplashPageRepository(nil).GetByID(url.EntityID())
		if err != nil {
			return nil, err
		}

		return &model.Page{
			Status: &model.PageStatus{
				StatusCode: http.StatusOK,
			},
			Breadcrumbs: []*model.Breadcrumb{
				{
					Label: "Начало",
					URL:   "/",
				},
				{
					Label: "Марки",
					URL:   "/marki",
				},
				{
					Label: splashPage.Title,
					URL:   "#",
				},
			},
			Data: &model.CatalogPage{
				Layout:   model.CatalogLayoutProductCatalog,
				State:    params.GetState(),
				Category: splashPage.ToCategory(),
				Products: make([]model.Product, 0),
			},
		}, nil
	default:
		return &model.Page{
			Status: &model.PageStatus{
				StatusCode: http.StatusBadRequest,
				Error:      core_utils.ToStringPointer("unknown url type"),
			},
			Breadcrumbs: nil,
			Data:        nil,
		}, fmt.Errorf("unknown url type: %s", idPath.Type())
	}
}

func getCmsLinks() []*model.Link {
	return []*model.Link{
		{
			Href: "/about",
			Text: "За нас",
		},
		{
			Href: "/uslugi",
			Text: "Услуги",
		},
		{
			Href: "/obshti-uslovia",
			Text: "Общи условия",
		},
		{
			Href: "/plashtane-i-dostavka",
			Text: "Плащане и доставка",
		},
		{
			Href: "/vrashtane-i-zamqna",
			Text: "Връщане и замяна",
		},
		{
			Href: "/garancionni-uslovia",
			Text: "Гаранционини условия",
		},
		{
			Href: "/clickandcolect",
			Text: "Кликни и вземи",
		},
	}
}
