package router

import (
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/gdpr"
	mage_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	mega_menu "praktis.bg/store-api/packages/magento-core/mega-menu"
	"strconv"
	"strings"
	"time"
)

var StaticContentCacheKey = "Store_StaticContent"
var StaticContentCacheLifetime = time.Duration(60) * time.Minute

func GetStaticContent(storeObj *mage_core.StoreEntity) *model.StaticContent {
	return &model.StaticContent{
		Header: &model.HeaderData{
			Scripts: make([]string, 0),
		},
		Menu:   GetMenuData(storeObj),
		Footer: GetFooterData(storeObj),
		Store:  GetStoreData(storeObj),
		Messages: &model.PageMessages{
			Newsletter:         storeObj.GetConfig("pfg_theme/messages/newsletter", ""),
			SendInquiryMessage: storeObj.GetConfig("pfg_theme/messages/send_request_message", ""),
		},
		APIKeys: &model.APIKeys{
			GoogleMaps:          storeObj.GetConfig("pfg_theme/api_keys/google_maps", ""),
			GoogleLoginClientID: storeObj.GetConfig("pfg_theme/api_keys/google_login", ""),
			GoogleRecaptchaKey:  storeObj.GetConfig("pfg_theme/api_keys/google_recaptcha_key", ""),
			FacebookLoginAppID:  storeObj.GetConfig("pfg_theme/api_keys/facebook_login", ""),
		},
		Gdpr: gdpr.GetGDPRModalConfig(storeObj),
		Logo: GetStoreLogo(storeObj),
	}
}

func GetDefaultMeta(store *mage_core.StoreEntity, url string) *model.PageMeta {
	return &model.PageMeta{
		Title:       store.GetConfig("design/head/default_title", "praktis.bg"),
		Description: store.GetConfig("design/head/default_description", "praktis.bg mageClient"),
		Keywords:    store.GetConfig("design/head/default_keywords", "praktis.bg mageClient"),
		Image: &model.Image{
			Src: "",
		},
		CanonicalURL: url,
	}
}

func GetStoreData(store *mage_core.StoreEntity) *model.StoreInfo {
	lat := store.GetConfig("stenik_sitesettings/settings/latitude", "")
	latFloat, err := strconv.ParseFloat(lat, 64)
	if err != nil {
		latFloat = 0
	}
	lon := store.GetConfig("stenik_sitesettings/settings/longitude", "")
	lonFloat, err := strconv.ParseFloat(lon, 64)
	if err != nil {
		lonFloat = 0
	}

	return &model.StoreInfo{
		BaseURL: store.GetConfig("web/secure/base_url", ""),
		Location: &model.MapLocation{
			Lat: latFloat,
			Lng: lonFloat,
		},
		Contacts: &model.StoreContacts{
			General: &model.ContactInfo{
				Phone: store.GetConfigP(
					"general/store_information/phone",
					"0700 45 004",
				),
			},
			OnlineStore: &model.ContactInfo{
				Phone: store.GetConfigP(
					"general/store_information/online_store",
					"0894 198 027",
				),
			},
		},
	}
}

func GetFooterData(store *mage_core.StoreEntity) *model.Footer {
	return &model.Footer{
		Columns: []*model.FooterColumn{
			{
				Title: core_utils.ToStringPointer("За Практис"),
				Links: []*model.Link{
					{
						Text: "За нас",
						Href: "/about",
					},
					{
						Text: "Услуги",
						Href: "/uslugi",
					},
					{
						Text: "Магазини",
						Href: "/shops",
					},
					{
						Text: "Майстори",
						Href: "/maistori",
					},
					{
						Text: "Кариери",
						Href: "/karieri",
					},
				},
			},
			{
				Title: core_utils.ToStringPointer("За клиента"),
				Links: []*model.Link{
					{
						Text: "Общи условия за пазаруване",
						Href: "/obshti-uslovia",
					},
					{
						Text: "Връщане и замяна",
						Href: "/vrashtane-i-zamqna",
					},
					{
						Text: "Плащане и доставка",
						Href: "/plashtane-i-dostavka",
					},
					{
						Text: "Гаранционни условия",
						Href: "/garancionni-uslovia",
					},
					{
						Text: "Кликни и вземи",
						Href: "/clickandcolect",
					},
					{
						Text: "Гаранционни условия ламиниран паркет",
						Href: "/garancionni-uslovia-parket",
					},
					{
						Text: "олямата пролетна акция",
						Href: "/politika-za-signalizirane",
					},
				},
			},
		},
		Social: &model.SocialLinks{
			Facebook: store.GetConfigP("stenik_sitesettings/settings/facebookurl", ""),
			Youtube:  store.GetConfigP("stenik_sitesettings/settings/youtubeurl", ""),
			Viber:    core_utils.ToStringPointer("viber://chat?number=%2B359892204040"),
		},
		AppLinks: &model.ApplicationLinks{
			Android: store.GetConfigP("stenik_sitesettings/settings/androidurl", ""),
			Ios:     store.GetConfigP("stenik_sitesettings/settings/iosurl", ""),
		},
		Contacts: &model.StoreContacts{
			General: &model.ContactInfo{
				Phone: store.GetConfigP(
					"general/store_information/phone",
					"0700 45 004",
				),
			},
			OnlineStore: &model.ContactInfo{
				Phone: store.GetConfigP(
					"general/store_information/online_store",
					"0894 198 027",
				),
			},
		},
	}
}

func toUrl(url string) string {
	if strings.Contains(url, "http") {
		return url
	}

	if strings.HasPrefix(url, "/") {
		return url
	}

	return "/" + url
}

func GetMenuData(store *mage_core.StoreEntity) *model.Menu {
	tree, err := mega_menu.LoadCategoryTree(
		store,
		[]mage_entity.AttributeCode{
			"url_path",
			"name",
			"thumbnail",
			"banner_url",
			"banner_image",
		},
	)
	if err != nil {
		return &model.Menu{}
	}

	var categories []*model.MenuItem
	for _, child := range tree.RootNode.Children {
		var childChildren []*model.MenuItem
		childCategory := child.Category

		for _, childChild := range child.Children {
			childChildCategory := childChild.Category
			childChildren = append(
				childChildren,
				&model.MenuItem{
					ID:          int64(childChildCategory.EntityID),
					Thumbnail:   childChildCategory.GetImageAttribute("thumbnail"),
					Name:        childChildCategory.Name,
					URL:         toUrl(childChildCategory.UrlPath),
					SideSection: getSideSection(childChildCategory),
					Children:    nil,
				},
			)
		}

		categories = append(categories, &model.MenuItem{
			ID:          int64(childCategory.EntityID),
			Thumbnail:   childCategory.GetImageAttribute("thumbnail"),
			Name:        childCategory.Name,
			URL:         toUrl(childCategory.UrlPath),
			SideSection: getSideSection(childCategory),
			Children:    childChildren,
		})
	}

	return &model.Menu{
		Categories: categories,
	}
}

func getSideSection(cat *mage_entity.CategoryEntity) *model.MenuSideSection {
	img := cat.GetImageAttribute("banner_image")
	if img == "" {
		return nil
	}

	return &model.MenuSideSection{
		Image:   img,
		URL:     cat.GetAttributeValue("banner_url"),
		RawHTML: "",
	}
}

func GetStoreLogo(store *mage_core.StoreEntity) *model.StoreLogo {
	// Fetch the logo path from Magento config
	logoPath := store.GetConfig("design/header/logo_src", "")

	// Check if the specific image ID is stored
	logoImageID := store.GetConfig("design/header/logo_src_image", "")

	// If logo path is empty but we have an image ID, try to build the full URL
	if logoPath == "" && logoImageID != "" {
		// Assuming the image ID needs to be combined with a base URL
		baseMediaURL := store.GetConfig("web/secure/base_media_url", "")
		if baseMediaURL != "" {
			logoPath = baseMediaURL + "logo/" + logoImageID
		}
	}

	// Get optional alt text
	logoAlt := store.GetConfig("design/header/logo_alt", "")

	// Get optional width and height (if configured)
	widthStr := store.GetConfig("design/header/logo_width", "")
	heightStr := store.GetConfig("design/header/logo_height", "")

	var width, height *int
	if widthStr != "" {
		if widthVal, err := strconv.Atoi(widthStr); err == nil {
			width = &widthVal
		}
	}

	if heightStr != "" {
		if heightVal, err := strconv.Atoi(heightStr); err == nil {
			height = &heightVal
		}
	}

	var alt *string
	if logoAlt != "" {
		alt = &logoAlt
	}

	return &model.StoreLogo{
		URL:    logoPath,
		Width:  width,
		Height: height,
		Alt:    alt,
	}
}
