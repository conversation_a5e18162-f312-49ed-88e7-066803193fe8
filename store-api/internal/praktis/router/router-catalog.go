package router

import (
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/catalog"
	"praktis.bg/store-api/packages/magento-core/types"
)

func getCategoryPage(categoryId types.EntityID, params RouteParams) (*model.CatalogPage, error) {
	pageData := &model.CatalogPage{
		Layout: model.CatalogLayoutProductCatalog,
		Category: &model.Category{
			ID: int64(categoryId),
		},
		State: params.GetState(),
	}

	pageCategory, err := catalog.GetCategoryEntity(categoryId)
	if err != nil {
		return pageData, err
	}

	if pageCategory.GetAttributeValue("custom_template") == "landing" {
		pageData.Layout = model.CatalogLayoutLandingPage
	}

	pageData.Category = catalog.CategoryToModelCategory(pageCategory)

	return pageData, nil
}
