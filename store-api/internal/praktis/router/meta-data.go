package router

import (
	"fmt"
	"praktis.bg/store-api/graphql/model"
	brands "praktis.bg/store-api/internal/praktis/catalog/brands"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	mage_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
	"strings"
)

func IsHomePage(url string) types.EntityID {
	if url == "" || url == "home" || url == "index" {
		return 1
	}

	return 0
}

func prepText(text string, product *product.PraktisProduct) string {
	name := product.Name
	return strings.Replace(text, "{{var product.name}}", name, -1)
}

func GetProductMeta(id types.EntityID, store *mage_core.StoreEntity) (*model.PageMeta, error) {
	prod, err := product.NewPraktisProductRepository(nil).GetID(
		id.Int64(),
		[]string{
			"url_path",
			"name",
			"meta_title",
			"meta_description",
			"meta_keyword",
		},
	)
	if err != nil {
		return nil, err
	}

	title, _ := prod.GetVal("meta_title")
	if title == "" {
		title = prepText(
			store.GetConfig("stenik_seobase/product_view/meta_title", ""),
			prod,
		)
	}

	description, _ := prod.GetVal("meta_description")
	if description == "" {
		description = prepText(
			store.GetConfig("stenik_seobase/product_view/meta_description", ""),
			prod,
		)
	}

	keywords, _ := prod.GetVal("meta_keyword")
	if keywords == "" {
		keywords = prepText(
			store.GetConfig("design/head/default_keywords", ""),
			prod,
		)
	}

	return &model.PageMeta{
		Robots:      "INDEX,FOLLOW",
		Title:       title,
		Description: description,
		Keywords:    keywords,
		Image: &model.Image{
			//Src:   prod.GetMedialUrl(),
			Alt:   &title,
			Title: &title,
		},
		CanonicalURL: prod.MustGetVal("url_path"),
	}, nil
}

func GetCmsPageMeta(id types.EntityID, store *mage_core.StoreEntity) (*model.PageMeta, error) {
	cmsPage, err := mage_entity.NewCMSPageRepository(store).Get(int64(id))
	if err != nil {
		return &model.PageMeta{
			Title: id.String(),
		}, err
	}

	return &model.PageMeta{
		Title:        cmsPage.Title,
		Description:  cmsPage.MetaDescription,
		Keywords:     cmsPage.MetaKeywords,
		CanonicalURL: fmt.Sprintf("/%s", cmsPage.Identifier),
	}, nil
}

func GetCategoryMeta(id types.EntityID, store *mage_core.StoreEntity) (*model.PageMeta, error) {
	repo := mage_entity.NewCategoryRepository()
	category, err := repo.Get(id,
		"meta_title",
		"meta_description",
		"meta_keywords",
		"url_path",
	)
	if err != nil {
		return nil, err
	}

	return &model.PageMeta{
		Title:        category.GetAttributeValue("meta_title"),
		Description:  category.GetAttributeValue("meta_description"),
		Keywords:     category.GetAttributeValue("meta_keyword"),
		CanonicalURL: category.UrlPath,
	}, nil
}

func GetSplashPageMeta(id types.EntityID, store *mage_core.StoreEntity) (*model.PageMeta, error) {
	splashPage, err := brands.NewSplashPageRepository(nil).GetByID(id.Int64())
	if err != nil {
		return nil, err
	}

	return &model.PageMeta{
		Title:        splashPage.MetaTitle,
		Description:  splashPage.MetaDescription,
		Keywords:     splashPage.MetaKeywords,
		CanonicalURL: splashPage.URL,
	}, nil
}
