package gdpr

import (
	"fmt"
	"praktis.bg/store-api/graphql/model"
	mage_core "praktis.bg/store-api/packages/magento-core"
	"strings"
)

func GetGDPRModalConfig(storeObj *mage_core.StoreEntity) *model.GDPRConfig {
	gtag := storeObj.GetConfig("pfg_cookieconsent_tracking/settings/gtag_id", "")
	var extraIds []string
	if strings.Contains(gtag, ",") {
		parts := strings.Split(gtag, ",")
		gtag = strings.TrimSpace(parts[0])
		for _, part := range parts[1:] {
			extraIds = append(extraIds, strings.TrimSpace(part))
		}
	}

	qb := mage_core.GetStoreClient().DbConn()

	groups := []*CookieGroup{}
	groupsData := make([]*model.CookieGroup, 0)
	err := qb.Model(&CookieGroup{}).
		Preload("CookieVendors").
		Preload("CookieVendors.CookieDetails").
		Find(&groups).Error
	if err != nil {
		return nil
	}

	for _, group := range groups {
		groupsData = append(groupsData, &model.CookieGroup{
			ID:             fmt.Sprintf("%d", group.ID),
			Title:          group.Title,
			Content:        group.Text,
			Grants:         group.GetGrantList(),
			CookiePatterns: group.GetCookiePattern(),
			Vendors: &model.GDPRCookieVendors{
				Keys: group.GetVendorsList(),
				Config: (func() []*model.GDPRCookieVendorConfig {
					vendorsData := make([]*model.GDPRCookieVendorConfig, 0)
					for _, vendor := range group.CookieVendors {
						vendorsData = append(vendorsData, &model.GDPRCookieVendorConfig{
							ID:          fmt.Sprintf("%d", vendor.ID),
							Name:        vendor.Name,
							URL:         vendor.URL,
							CookieCount: vendor.CookieCount,
							CookieDetails: (func() []*model.GDPRCookieDetails {
								details := make([]*model.GDPRCookieDetails, 0)
								for _, detail := range vendor.CookieDetails {
									details = append(details, &model.GDPRCookieDetails{
										ID:          fmt.Sprintf("%d", detail.ID),
										Name:        detail.Name,
										Description: detail.Text,
										Type:        detail.Type,
										Expiration:  detail.Expiration,
									})
								}
								return details
							})(),
						})
					}
					return vendorsData
				})(),
			},
		})
	}

	return &model.GDPRConfig{
		RootID:        "pfg-cookieconsent-overlay",
		ClarityID:     storeObj.GetConfig("pfg_cookieconsent_tracking/settings/clarity_id", ""),
		PixelID:       storeObj.GetConfig("pfg_cookieconsent_tracking/settings/pixel_id", ""),
		GtagID:        gtag,
		ExtraGTagsIds: extraIds,
		Modal: &model.GDPRModalConfig{
			Title:        storeObj.GetConfig("pfg_cookieconsent/settings/popup_title", ""),
			Content:      storeObj.GetConfig("pfg_cookieconsent/settings/popup_text", ""),
			CookieGroups: groupsData,
		},
	}
}
