package shipping_utils

import (
	"fmt"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"strings"
)

const PraktisStoreShippingCarrierCode = "stenik_siteshipping_from_store"
const PraktisStoreShippingCarrierMethod = "stenik_siteshipping_from_store"

const PraktisWeightPriceShippingCarrierCode = "praktis_weightpriceshipping"
const PraktisWeightPriceShippingCarrierMethod = "weight_price"

const PraktisPalletCarrierCode = "praktis_palletshipping"
const PraktisPalletCarrierMethod = "large"

const ExtensaEcontCarrierCode = "extensa_econt"
const ExtensaEcontCarrierMethodToOffice = "econt_office"
const ExtensaEcontCarrierMethodToDoor = "econt_door"

const ShippingTypeToDore = 1

const ShippingTypeToOffice = 2

const ShippingTypeGetFromStore = 22

func NewPraktisShipping(store *magento_core.StoreEntity) sales.IShippingCarrier {
	return sales.InitNewStoreShipping(
		store,
		PraktisStoreShippingCarrierCode,
		PraktisStoreShippingCarrierMethod,
	)
}

func GetAllowedShippingCarriers(store *magento_core.StoreEntity) []sales.IShippingCarrier {
	var result []sales.IShippingCarrier
	for _, data := range [][]string{
		{
			PraktisStoreShippingCarrierCode,
			PraktisStoreShippingCarrierMethod,
		},
		{
			PraktisWeightPriceShippingCarrierCode,
			PraktisWeightPriceShippingCarrierMethod,
		},
		{
			PraktisPalletCarrierCode,
			PraktisPalletCarrierMethod,
		},
		{
			ExtensaEcontCarrierCode,
			ExtensaEcontCarrierMethodToOffice,
		},
	} {
		carrier := sales.InitNewStoreShipping(
			store,
			data[0],
			data[1],
		)
		if carrier != nil && carrier.IsActive() {
			result = append(result, carrier)
		}
	}

	return result
}

func AddOfficeCodeToStreet(officeCode, street string) string {
	return fmt.Sprintf("[%s] %s", officeCode, street)
}

func extractOfficeCode(street string) string {
	start := strings.Index(street, "[")
	if start == -1 {
		return ""
	}

	end := strings.Index(street[start:], "]")
	if end == -1 {
		return ""
	}

	return street[start+1 : start+end]
}

func GetOfficeCodeFromStreet(street string) string {
	code := extractOfficeCode(street)
	if code != "" {
		return code
	}
	return ""
}

func AddStoreCodeToStreet(storeCode, street string) string {
	return fmt.Sprintf("(%s) %s", storeCode, street)
}

func extractStoreContent(street string) string {
	start := strings.Index(street, "(")
	if start == -1 {
		return ""
	}

	end := strings.Index(street[start:], ")")
	if end == -1 {
		return ""
	}

	return street[start+1 : start+end]
}

func GetStoreCodeFromStreet(street string) string {
	code := extractStoreContent(street)
	if code != "" {
		return code
	}
	return ""
}
