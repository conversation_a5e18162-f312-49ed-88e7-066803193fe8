package order_utils

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"math"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/store_connect"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"praktis.bg/store-api/packages/magento-core/types"
)

func GetOrderStatus(o *sales.MagentoOrder) *model.OrderStatus {
	return &model.OrderStatus{
		Label: sales.GetStatusLabel(o.Status.String),
		Code:  o.Status.String,
	}
}

func ToOrderModel(o *sales.MagentoOrder) *model.StoreOrder {
	var payment *model.PaymentMethod

	method := ""
	if o.Payment != nil {
		method = o.Payment.Method
	} else {
		var paymentMethod sales.MagentoOrderPayment
		_ = praktis.GetDbClient().Model(o).Association("Payment").Find(&paymentMethod)
		if paymentMethod.EntityID > 0 {
			method = paymentMethod.Method
		}
	}

	if method != "" {
		p := sales.InitNewStorePayment(o.GetStore(), method)
		payment = &model.PaymentMethod{
			Code: p.GetCode(),
			Name: p.GetTitle(),
		}
	}

	return &model.StoreOrder{
		ID:          o.EntityID,
		IncrementID: o.IncrementID.String,
		State:       o.State.String,
		Status:      GetOrderStatus(o),
		CouponCode:  o.CouponCode.String,
		ProtectCode: o.ProtectCode.String,
		CreateAt:    types.ToStr(o.CreatedAt.Time),
		Items:       []*model.OrderItem{},
		Currency:    o.OrderCurrencyCode.String,
		Totals:      getOrderTotals(o),
		ShippingMethod: &model.ShippingMethod{
			Name: o.ShippingDescription.String,
			Code: o.ShippingMethod.String,
		},
		PaymentMethod: payment,
		Note:          o.CustomerNote.String,
		ShippingAddress: &model.OrderAddress{
			ID: o.ShippingAddressID,
		},
		Invoice: &model.CustomerInvoice{
			ID: o.BillingAddressID,
		},
	}
}

func getOrderTotals(o *sales.MagentoOrder) []*model.CartTotal {
	totals := make([]*model.CartTotal, 0)
	if o == nil {
		return totals
	}

	totals = append(totals, &model.CartTotal{
		Code:  model.CartTotalCodeSubTotal,
		Label: "Обща сума",
		Amount: &model.Price{
			Value:    o.Subtotal.Float64,
			Currency: o.OrderCurrencyCode.String,
		},
		Order: 0,
	})

	totals = append(totals, &model.CartTotal{
		Code: model.CartTotalCodeDiscountTotal,
		Label: core_utils.IF(
			o.DiscountDescription.String != "",
			o.DiscountDescription.String,
			"Промо",
		),
		Amount: &model.Price{
			Value:    o.DiscountAmount.Float64,
			Currency: o.OrderCurrencyCode.String,
		},
		Order: 10,
	})

	if o.PraktisShippingDiscountAmount < 0 {
		totals = append(totals, &model.CartTotal{
			Code:  model.CartTotalCodeDiscountTotal,
			Label: "Praktis Отстъпка от доставката",
			Amount: &model.Price{
				Value:    o.PraktisShippingDiscountAmount,
				Currency: o.OrderCurrencyCode.String,
			},
			Order: 20,
		})
	}

	if o.CashOnDeliveryTaxAmount > 0 {
		totals = append(totals, &model.CartTotal{
			Code:  model.CartTotalCodeOther,
			Label: "Такса наложен платеж",
			Amount: &model.Price{
				Value:    o.CashOnDeliveryTaxAmount,
				Currency: o.OrderCurrencyCode.String,
			},
			Order: 25,
		})
	}

	totals = append(totals, &model.CartTotal{
		Code:  model.CartTotalCodeGrantTotal,
		Label: "Крайна сума",
		Amount: &model.Price{
			Value:    o.GrandTotal.Float64,
			Currency: o.OrderCurrencyCode.String,
		},
		Order: 99999,
	})

	return totals
}

func NewPlaceOrderResponse(
	response store_connect.PlaceOrderResponse,
	order *sales.MagentoOrder,
) *model.PlaceOrderResponse {
	return &model.PlaceOrderResponse{
		OrderNumber: response.Order.IncrementID,
		Status:      GetOrderStatus(order),
		Redirect: &model.OrderRedirect{
			URL:  response.Redirect.RedirectURL,
			Data: response.Redirect.Params,
		},
		Order: ToOrderModel(order),
	}
}

func OrderDoneError(incrementID string, err error) error {
	if err == nil {
		return nil
	}

	return fmt.Errorf(
		"поръчка %s е зъдадена, ще получите мейл, но възникна следната грешка: %w",
		incrementID,
		err,
	)
}

func ToOrderItemModel(item *sales.MagentoOrderItem, currency string) *model.OrderItem {
	if item == nil {
		return nil
	}

	price := item.Price
	priceWithoutDiscount := price
	rowTotal := item.RowTotal
	rowTotalWithoutDiscount := rowTotal
	if item.DiscountPct > 0 {
		discountFactor := 1.0 - item.DiscountPct
		priceWithoutDiscount = math.Round(price*discountFactor*100) / 100
		rowTotalWithoutDiscount = math.Round(rowTotal*discountFactor*100) / 100
	}

	return &model.OrderItem{
		ID:  item.ItemID,
		Sku: item.Sku,
		Product: model.SimpleProduct{
			ID:  item.ProductID,
			Sku: item.Sku,
		},
		BaseQty: item.QtyOrdered,
		Price: &model.Price{
			Value:    item.Price,
			Currency: currency,
		},
		PriceWithoutDiscount: &model.Price{
			Value:    priceWithoutDiscount,
			Currency: currency,
		},
		RowTotal: &model.Price{
			Value:    item.RowTotal,
			Currency: currency,
		},
		RowTotalWithoutDiscount: &model.Price{
			Value:    rowTotalWithoutDiscount,
			Currency: currency,
		},
		DiscountAmount: &model.Price{
			Value:    item.DiscountAmt,
			Currency: currency,
		},
		DiscountPercent: item.DiscountPct,
	}
}

func ToOrderShippingAddress(address *sales.MagentoOrderAddress) *model.OrderAddress {
	return &model.OrderAddress{
		ID:        address.EntityID,
		FirstName: address.Firstname.String,
		LastName:  address.Lastname.String,
		Email:     address.Email.String,
		Telephone: address.Telephone.String,
		City:      address.City.String,
		Postcode:  address.Postcode.String,
		Street:    address.Street.String,
	}
}

func ToOrderInvoice(address *sales.MagentoOrderAddress) *model.CustomerInvoice {
	if address.Invoice.String != "1" {
		return nil
	}

	switch address.InvoiceType.String {
	case model.InvoiceTypeCompany.String():
		return &model.CustomerInvoice{
			ID:      address.EntityID,
			Type:    model.InvoiceTypeCompany,
			City:    address.InvoiceCompanyCity.String,
			Address: address.InvoiceCompanyAddress.String,
			Company: &model.CompanyInvoice{
				Name: address.InvoiceCompanyName.String,
				Mol:  address.InvoiceCompanyMol.String,
				Eik:  address.InvoiceCompanyBulstat.String,
				Vat:  address.InvoiceCompanyVat.String,
			},
		}
	case model.InvoiceTypePersonal.String():
		return &model.CustomerInvoice{
			ID:      address.EntityID,
			Type:    model.InvoiceTypePersonal,
			City:    address.InvoicePersonalCity.String,
			Address: address.InvoicePersonalAddress.String,
			Individual: &model.IndividualInvoice{
				Name: address.InvoicePersonalName.String,
				Egn:  address.InvoicePersonalPin.String,
				Vat:  address.InvoicePersonalVat.String,
			},
		}
	default:
		return nil
	}
}
