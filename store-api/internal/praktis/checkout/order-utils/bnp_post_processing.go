package order_utils

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"

	cart_utils "praktis.bg/store-api/internal/praktis/checkout/cart-utils"
	credit_calculators "praktis.bg/store-api/internal/praktis/credit-calculators"
	praktis "praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
)

// HandlePostOrderProcessing handles post-order processing for BNP payments
// This function is called after successful order placement to automatically
// submit BNP applications for orders using BNP payment method.
func HandlePostOrderProcessing(order *sales.MagentoOrder) error {
	incrementID := order.IncrementID.String
	log.Printf("[INFO] Post-order processing started for order %s", incrementID)
	log.Printf("[DEBUG] BNP: Order details - ID: %d, IncrementID: %s", order.EntityID, incrementID)

	// Check if this is a BNP payment order
	if order.Payment == nil {
		log.Printf("[DEBUG] Order %s has no payment information, skipping post-processing", incrementID)
		log.Printf("[DEBUG] BNP: Order payment object is nil - this indicates the payment was not loaded with the order")
		return nil
	}

	log.Printf("[DEBUG] BNP: Order payment found - ID: %d, Method: %s", order.Payment.EntityID, order.Payment.Method)

	if order.Payment.Method != cart_utils.StenikLeasingJetCreditCode {
		log.Printf("[DEBUG] Order %s payment method is %s, not BNP, skipping post-processing",
			incrementID, order.Payment.Method)
		return nil
	}

	log.Printf("[INFO] Order %s uses BNP payment method, processing BNP data transfer and application", incrementID)

	// Verify BNP payment data by reading directly from database
	log.Printf("[INFO] BNP: === POST-ORDER PAYMENT DATA VERIFICATION ===")
	log.Printf("[INFO] BNP: Order: %s, Order ID: %d, Payment ID: %d", incrementID, order.EntityID, order.Payment.EntityID)

	// Read payment data directly from database using raw SQL to ensure compatibility
	var dbAdditionalInfo sql.NullString
	var dbPaymentID int64
	dbErr := praktis.GetDbClient().Raw(`
		SELECT entity_id, additional_information
		FROM sales_flat_order_payment
		WHERE parent_id = ?
	`, order.EntityID).Row().Scan(&dbPaymentID, &dbAdditionalInfo)

	if dbErr != nil {
		log.Printf("[ERROR] BNP: Failed to read payment data from database: %v", dbErr)
		return fmt.Errorf("failed to read BNP payment data from database: %w", dbErr)
	}

	if !dbAdditionalInfo.Valid || dbAdditionalInfo.String == "" {
		log.Printf("[ERROR] BNP: CRITICAL - Order payment additional_information is EMPTY!")
		log.Printf("[ERROR] BNP: This means the admin panel will NOT display BNP applicant data")
		log.Printf("[ERROR] BNP: Order: %s, Payment ID: %d", incrementID, dbPaymentID)
		return fmt.Errorf("BNP payment data is missing from order payment record")
	}

	// Log successful verification with data preview
	dataLength := len(dbAdditionalInfo.String)
	dataPreview := dbAdditionalInfo.String
	if len(dataPreview) > 100 {
		dataPreview = dataPreview[:100] + "..."
	}

	log.Printf("[SUCCESS] BNP: Payment data verified in database")
	log.Printf("[SUCCESS] BNP: Order: %s, Payment ID: %d, Data Length: %d bytes", incrementID, dbPaymentID, dataLength)
	log.Printf("[SUCCESS] BNP: Data Preview: %s", dataPreview)

	// Parse and display key BNP applicant information
	var bnpData map[string]interface{}
	err := json.Unmarshal([]byte(dbAdditionalInfo.String), &bnpData)
	if err != nil {
		log.Printf("[WARNING] BNP: Failed to parse payment data JSON: %v", err)
	} else {
		log.Printf("[INFO] BNP: === APPLICANT INFORMATION EXTRACTED ===")

		// Extract customer data
		if customerData, ok := bnpData["customer_data"].(map[string]interface{}); ok {
			firstName, _ := customerData["first_name"].(string)
			lastName, _ := customerData["last_name"].(string)
			email, _ := customerData["email"].(string)
			phone, _ := customerData["phone"].(string)
			log.Printf("[INFO] BNP: Customer: %s %s", firstName, lastName)
			log.Printf("[INFO] BNP: Email: %s, Phone: %s", email, phone)
		}

		// Extract loan data
		if loanData, ok := bnpData["loan"].(map[string]interface{}); ok {
			installmentAmount, _ := loanData["installment_amount"].(string)
			maturity, _ := loanData["maturity"].(string)
			totalRepayment, _ := loanData["total_repayment_amount"].(string)
			schemeName, _ := loanData["pricing_scheme_name"].(string)
			log.Printf("[INFO] BNP: Loan: %s BGN x %s months = %s BGN total", installmentAmount, maturity, totalRepayment)
			log.Printf("[INFO] BNP: Scheme: %s", schemeName)
		}

		// Extract principal amount
		if principal, ok := bnpData["principal"].(float64); ok {
			log.Printf("[INFO] BNP: Principal: %.2f BGN", principal)
		}
	}

	log.Printf("[SUCCESS] BNP: Database verification PASSED")
	log.Printf("[SUCCESS] BNP: Admin panel will be able to display BNP applicant information")

	// Submit BNP application
	err = credit_calculators.SubmitBNPApplication(incrementID)
	if err != nil {
		log.Printf("[ERROR] Failed to submit BNP application for order %s: %v", incrementID, err)
		return fmt.Errorf("failed to submit BNP application: %w", err)
	}

	log.Printf("[INFO] BNP application submitted successfully for order %s", incrementID)
	return nil
}
