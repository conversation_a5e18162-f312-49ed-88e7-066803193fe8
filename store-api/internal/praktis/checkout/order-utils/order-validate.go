package order_utils

import (
	"encoding/json"
	"errors"
	"fmt"
	"log"
	"praktis.bg/store-api/graphql/model"
	cart_utils "praktis.bg/store-api/internal/praktis/checkout/cart-utils"
	"praktis.bg/store-api/internal/praktis/checkout/shipping-utils"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"strings"
)

func ValidateNewOrderData(
	store *magento_core.StoreEntity,
	data model.NewOrderInput) (err error) {
	if data.Shipping != nil {
		if err = cart_utils.ValidateCheckoutShippingInfo(*data.Shipping); err != nil {
			return err
		}
	} else {
		return errors.New("shipping info is required")
	}

	if data.Client != nil {
		if err = cart_utils.ValidateCheckoutCustomerInfo(*data.Client); err != nil {
			return err
		}
	}

	err = ValidateShippingMethod(store, data.ShippingMethodCode)
	if err != nil {
		return err
	}

	err = ValidatePaymentMethod(store, data.PaymentMethodCode)
	if err != nil {
		return err
	}

	if data.Note != nil && len(*data.Note) > 255 {
		return errors.New("order comment is too long")
	}

	return nil
}

// ValidateQuoteForBNPPayment validates that a quote has proper BNP payment data
func ValidateQuoteForBNPPayment(quote *sales.MagentoQuote) error {
	log.Printf("[DEBUG] BNP: === VALIDATING QUOTE FOR BNP PAYMENT ===")
	log.Printf("[DEBUG] BNP: Quote ID: %d", quote.EntityID)

	if quote.Payment == nil {
		log.Printf("[ERROR] BNP: Quote has no payment information")
		return errors.New("quote has no payment information")
	}

	log.Printf("[DEBUG] BNP: Payment ID: %d", quote.Payment.PaymentID)
	log.Printf("[DEBUG] BNP: Payment method: %s", quote.Payment.Method)
	if quote.Payment.Method != cart_utils.StenikLeasingJetCreditCode {
		log.Printf("[DEBUG] BNP: Not a BNP payment, skipping validation")
		return nil // Not a BNP payment, no validation needed
	}

	// Check if additional information contains loan data
	log.Printf("[DEBUG] BNP: Additional information length: %d", len(quote.Payment.AdditionalInformation))
	log.Printf("[DEBUG] BNP: Additional information content: %s", quote.Payment.AdditionalInformation)
	if quote.Payment.AdditionalInformation == "" {
		log.Printf("[ERROR] BNP: Additional information is empty")
		return errors.New("BNP payment requires loan calculation data")
	}

	var additionalInfo map[string]interface{}

	// Try to parse as JSON first (for backward compatibility)
	err := json.Unmarshal([]byte(quote.Payment.AdditionalInformation), &additionalInfo)
	if err != nil {
		// If JSON parsing fails, check if it's PHP serialized data
		if strings.HasPrefix(quote.Payment.AdditionalInformation, "a:") {
			// This is PHP serialized data, which is expected for Magento compatibility
			// For validation purposes, we'll do basic checks on the serialized string
			log.Printf("[DEBUG] BNP: Payment data is in PHP serialized format (expected for Magento)")

			// Basic validation: check if it contains required sections
			requiredSections := []string{
				"s:4:\"loan\"",           // loan data section
				"s:13:\"customer_data\"", // customer data section
				"s:5:\"names\"",          // names field
				"s:3:\"pin\"",            // pin field
			}

			for _, section := range requiredSections {
				if !strings.Contains(quote.Payment.AdditionalInformation, section) {
					return fmt.Errorf("missing required BNP payment data section: %s", section)
				}
			}

			log.Printf("[DEBUG] BNP: PHP serialized payment data validation passed")
			return nil // PHP serialized data is valid
		} else {
			return errors.New("invalid payment additional information format")
		}
	}

	// Validate loan data exists
	loanData, exists := additionalInfo["loan"]
	if !exists {
		return errors.New("BNP payment requires loan calculation data")
	}

	loanMap, ok := loanData.(map[string]interface{})
	if !ok {
		return errors.New("invalid loan data format")
	}

	// Validate required loan fields
	requiredFields := []string{
		"pricing_variant_id",
		"installment_amount",
		"total_repayment_amount",
	}

	for _, field := range requiredFields {
		if _, exists := loanMap[field]; !exists {
			return fmt.Errorf("missing required loan field: %s", field)
		}
	}

	// Validate customer data exists
	customerData, exists := additionalInfo["customer_data"]
	if !exists {
		return errors.New("BNP payment requires customer data")
	}

	customerMap, ok := customerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid customer data format")
	}

	// Validate required customer fields
	requiredCustomerFields := []string{
		"first_name",
		"last_name",
		"phone",
		"email",
	}

	for _, field := range requiredCustomerFields {
		if value, exists := customerMap[field]; !exists || value == "" {
			return fmt.Errorf("missing required customer field: %s", field)
		}
	}

	return nil
}

func ValidateShippingMethod(store *magento_core.StoreEntity, code string) error {
	if code != "" {
		for _, carrier := range shipping_utils.GetAllowedShippingCarriers(store) {
			if carrier.GetShippingMethod() == code {
				return nil
			}
		}
	}

	return fmt.Errorf("shipping method code %s not allowed", code)
}

func ValidatePaymentMethod(store *magento_core.StoreEntity, code string) error {
	methods := cart_utils.GetAvailablePraktisPayments(store)
	for _, method := range methods {
		if method.GetCode() == code {
			return nil
		}
	}

	return fmt.Errorf("payment method not found: %s", code)
}
