package cart_utils

import (
	"database/sql"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
)

func SetAddressCustomerData(data model.ClientInput, address *sales.MagentoQuoteAddress) *sales.MagentoQuoteAddress {
	address.Firstname = sql.NullString{
		String: data.FirstName,
		Valid:  true,
	}
	address.Lastname = sql.NullString{
		String: data.LastName,
		Valid:  true,
	}
	address.Email = sql.NullString{
		String: data.Email,
		Valid:  true,
	}
	address.Telephone = sql.NullString{
		String: data.Phone,
		Valid:  true,
	}

	address = clearAddressInvoice(address)
	if data.Invoice != nil {
		switch data.Invoice.Type {
		case model.InvoiceTypeCompany:
			address = setCompanyInvoiceData(data.Invoice, address)
		case model.InvoiceTypePersonal:
			address = setIndividualInvoiceData(data.Invoice, address)
		}
	}

	return address
}

func clearAddressInvoice(address *sales.MagentoQuoteAddress) *sales.MagentoQuoteAddress {
	address.Invoice = sql.NullString{String: "0", Valid: true}

	address = setCompanyInvoiceData(nil, address)
	address = setIndividualInvoiceData(nil, address)

	return address
}

func setCompanyInvoiceData(data *model.InvoiceInput, address *sales.MagentoQuoteAddress) *sales.MagentoQuoteAddress {
	var invoiceType, name, mol, city, addressStreet, bulstatm, vat string
	if data != nil && data.Company != nil {
		invoiceType = string(model.InvoiceTypeCompany)
		name = data.Company.Name
		mol = data.Company.Mol
		city = data.City
		addressStreet = data.Address
		bulstatm = data.Company.Eik
		vat = data.Company.Vat

		address.Invoice = sql.NullString{
			String: "1",
			Valid:  true,
		}
	}

	address.InvoiceType = sql.NullString{
		String: invoiceType,
		Valid:  true,
	}
	address.InvoiceCompanyName = sql.NullString{
		String: name,
		Valid:  true,
	}
	address.InvoiceCompanyMol = sql.NullString{
		String: mol,
		Valid:  true,
	}
	address.InvoiceCompanyCity = sql.NullString{
		String: city,
		Valid:  true,
	}
	address.InvoiceCompanyAddress = sql.NullString{
		String: addressStreet,
		Valid:  true,
	}
	address.InvoiceCompanyBulstat = sql.NullString{
		String: bulstatm,
		Valid:  true,
	}
	address.InvoiceCompanyVat = sql.NullString{
		String: vat,
		Valid:  true,
	}

	return address
}

func setIndividualInvoiceData(data *model.InvoiceInput, address *sales.MagentoQuoteAddress) *sales.MagentoQuoteAddress {
	var invoiceType, name, egn, city, addressStreet, vat string
	if data != nil && data.Individual != nil {
		invoiceType = string(model.InvoiceTypePersonal)
		city = data.City
		addressStreet = data.Address
		name = data.Individual.Name
		egn = data.Individual.Egn
		vat = data.Individual.Vat

		address.Invoice = sql.NullString{
			String: "1",
			Valid:  true,
		}
	}

	address.InvoiceType = sql.NullString{
		String: invoiceType,
		Valid:  true,
	}
	address.InvoicePersonalName = sql.NullString{
		String: name,
		Valid:  true,
	}
	address.InvoicePersonalCity = sql.NullString{
		String: city,
		Valid:  true,
	}
	address.InvoicePersonalAddress = sql.NullString{
		String: addressStreet,
		Valid:  true,
	}
	address.InvoicePersonalPin = sql.NullString{
		String: egn,
		Valid:  true,
	}
	address.InvoicePersonalVat = sql.NullString{
		String: vat,
		Valid:  true,
	}

	return address
}
