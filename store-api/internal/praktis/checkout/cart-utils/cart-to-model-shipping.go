package cart_utils

import (
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/checkout/shipping-utils"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"strings"
)

const XMLPathPraktisFreeShippingActive = "shipping/praktis_freeshipping/active"
const XMLPathPraktisFreeShippingFreeShippingAfter = "shipping/praktis_freeshipping/free_shipping_after"
const XMLPathPraktisFreeShippingShowMessageAfter = "shipping/praktis_freeshipping/show_free_shipping_message_after"

func extractCodeFromStreet(s string) (string, string) {
	startIndex := strings.Index(s, "[")
	if startIndex == -1 {
		return "", s
	}

	endIndex := strings.Index(s[startIndex+1:], "]")
	if endIndex == -1 {
		return "", s
	}

	code := s[startIndex+1 : startIndex+1+endIndex]
	remaining := s[:startIndex] + s[startIndex+1+endIndex+1:]

	return code, remaining
}

func addressToModel(address *sales.MagentoQuoteAddress, skus []string) *model.ShippingAddress {
	method := model.ShippingMethodTypeEcontToAddress
	if address.ShippingType.Int32 > 0 {
		switch address.ShippingType.Int32 {
		case shipping_utils.ShippingTypeGetFromStore:
			method = model.ShippingMethodTypeToStore
		case shipping_utils.ShippingTypeToDore:
			method = model.ShippingMethodTypeEcontToAddress
		case shipping_utils.ShippingTypeToOffice:
			method = model.ShippingMethodTypeEcontToOffice
		}
	} else {
		methodStr := address.ShippingMethod.String
		if methodStr != "" {
			if strings.Contains(methodStr, shipping_utils.PraktisStoreShippingCarrierCode) {
				method = model.ShippingMethodTypeToStore
			} else if strings.Contains(methodStr, "econt_office") {
				method = model.ShippingMethodTypeEcontToOffice
			}
		}
	}

	addressE := &model.ShippingAddress{
		ID:     address.AddressID,
		Skus:   skus,
		Method: method,
	}

	if addressE.Method == model.ShippingMethodTypeToStore {
		addressE.StoreCode = address.StoreCode.String
	} else {
		addressE.OfficeCode = address.OfficeCode.String
		addressE.CityID = address.CityID.String
		addressE.City = address.City.String
		addressE.PostCode = address.Postcode.String
		addressE.Street = address.Street.String
	}

	return addressE
}
