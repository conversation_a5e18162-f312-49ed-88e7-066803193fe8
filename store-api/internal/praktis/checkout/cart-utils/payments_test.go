package cart_utils

import (
	"testing"
	"strconv"
)

func TestBNPVariantDataStructure(t *testing.T) {
	// Test that the payment data structure includes all variants with correct field names
	
	// Mock variant data (simulating what callMagentoBNPVariantsAPI would return)
	allVariants := []BNPVariant{
		{
			ID:                       481435,
			APR:                      23.73,
			CorrectDownpaymentAmount: 0.00,
			Installment:              76.32,
			Maturity:                 3,
			NIR:                      21.47,
			PricingSchemeID:          1695,
			PricingSchemeName:        "1,2% месечно оскъпяване",
			TotalRepayment:           228.96,
		},
		{
			ID:                       481436,
			APR:                      27.11,
			CorrectDownpaymentAmount: 0.00,
			Installment:              39.48,
			Maturity:                 6,
			NIR:                      24.28,
			PricingSchemeID:          1695,
			PricingSchemeName:        "1,2% месечно оскъпяване",
			TotalRepayment:           236.91,
		},
		{
			ID:                       792441,
			APR:                      0.00,
			CorrectDownpaymentAmount: 0.00,
			Installment:              110.50,
			Maturity:                 2,
			NIR:                      0.00,
			PricingSchemeID:          2791,
			PricingSchemeName:        "Вземи сега, плати после:0% за 2м. с кредитна карта",
			TotalRepayment:           221.00,
		},
		{
			ID:                       792443,
			APR:                      0.03,
			CorrectDownpaymentAmount: 0.00,
			Installment:              73.67,
			Maturity:                 3,
			NIR:                      0.00,
			PricingSchemeID:          2792,
			PricingSchemeName:        "Вземи сега, плати после:0% за 3м. с кредитна карта",
			TotalRepayment:           221.00,
		},
	}

	// Create the payment data structure as the Go API would
	additionalInfo := map[string]interface{}{}

	// Add all available variants using numeric keys (Magento template expects this format)
	for i, variant := range allVariants {
		variantKey := strconv.Itoa(i)
		additionalInfo[variantKey] = map[string]interface{}{
			"id":                         variant.ID,
			"apr":                        variant.APR,
			"installment":                variant.Installment,
			"total_repayment":            variant.TotalRepayment,
			"maturity":                   variant.Maturity,
			"nir":                        variant.NIR,
			"pricing_scheme_id":          variant.PricingSchemeID,
			"pricing_scheme_name":        variant.PricingSchemeName,
			"correct_downpayment_amount": variant.CorrectDownpaymentAmount,
		}
	}

	// Test that all variants are present with correct keys
	if len(additionalInfo) != len(allVariants) {
		t.Errorf("Expected %d variants in additionalInfo, got %d", len(allVariants), len(additionalInfo))
	}

	// Test each variant has the correct data structure
	for i := range allVariants {
		variantKey := strconv.Itoa(i)
		
		if _, exists := additionalInfo[variantKey]; !exists {
			t.Errorf("Variant with key %s not found in additionalInfo", variantKey)
			continue
		}

		variantData, ok := additionalInfo[variantKey].(map[string]interface{})
		if !ok {
			t.Errorf("Variant %s is not a map[string]interface{}", variantKey)
			continue
		}

		// Test that the variant has the expected field names (what Magento template expects)
		expectedFields := []string{"id", "apr", "installment", "total_repayment", "maturity", "nir", "pricing_scheme_id", "pricing_scheme_name", "correct_downpayment_amount"}
		for _, field := range expectedFields {
			if _, exists := variantData[field]; !exists {
				t.Errorf("Variant %s missing expected field: %s", variantKey, field)
			}
		}

		// Test specific values for the first variant
		if i == 0 {
			if variantData["installment"] != 76.32 {
				t.Errorf("Expected installment 76.32, got %v", variantData["installment"])
			}
			if variantData["total_repayment"] != 228.96 {
				t.Errorf("Expected total_repayment 228.96, got %v", variantData["total_repayment"])
			}
			if variantData["maturity"] != 3 {
				t.Errorf("Expected maturity 3, got %v", variantData["maturity"])
			}
		}

		// Test specific values for the second variant (different values)
		if i == 1 {
			if variantData["installment"] != 39.48 {
				t.Errorf("Expected installment 39.48, got %v", variantData["installment"])
			}
			if variantData["total_repayment"] != 236.91 {
				t.Errorf("Expected total_repayment 236.91, got %v", variantData["total_repayment"])
			}
			if variantData["maturity"] != 6 {
				t.Errorf("Expected maturity 6, got %v", variantData["maturity"])
			}
		}
	}

	t.Logf("Payment data structure test passed:")
	t.Logf("  Total variants: %d", len(allVariants))
	t.Logf("  Variant 0: installment=%.2f, total_repayment=%.2f, maturity=%d", 
		allVariants[0].Installment, allVariants[0].TotalRepayment, allVariants[0].Maturity)
	t.Logf("  Variant 1: installment=%.2f, total_repayment=%.2f, maturity=%d", 
		allVariants[1].Installment, allVariants[1].TotalRepayment, allVariants[1].Maturity)
	t.Logf("  All variants have correct field names for Magento template compatibility")
}
