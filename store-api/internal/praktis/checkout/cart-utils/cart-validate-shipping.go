package cart_utils

import (
	"fmt"
	"praktis.bg/store-api/graphql/model"
)

func ValidateCheckoutShippingInfo(data model.ShippingInput) error {
	switch data.Type {
	case model.ShippingMethodTypeEcontToAddress,
		model.ShippingMethodTypeEcontToOffice:
		if hasValidCity(data) == false {
			return fmt.Errorf("градът е невалиден")
		} else if hasValidPostCode(data) == false {
			return fmt.Errorf("невалиден пощенски код")
		} else if len(data.Address) < 3 || len(data.Address) > 255 {
			return fmt.Errorf("адресът е невалиден")
		}

		if data.Type == model.ShippingMethodTypeEcontToOffice {
			if hasValidOfficeID(data) == false {
				return fmt.Errorf("невалиден еконт офис")
			}
		}
	case model.ShippingMethodTypeToStore:
		if hasValidPraktisStore(data) == false {
			return fmt.<PERSON><PERSON><PERSON>("липсва код на магазина")
		}
	default:
		return fmt.Errorf("невалиден метод: %s", data.Type)
	}

	return nil
}

func hasValidPraktisStore(data model.ShippingInput) bool {
	if data.StoreCode == nil {
		return false
	}

	return true
}

func hasValidPostCode(data model.ShippingInput) bool {
	if len(data.PostCode) < 3 || len(data.PostCode) > 10 {
		return false
	}

	return true
}

func hasValidCity(data model.ShippingInput) bool {
	if len(data.City) < 3 || len(data.City) > 100 {
		return false
	}

	return true
}

func hasValidOfficeID(data model.ShippingInput) bool {
	if data.OfficeCode == nil || *data.OfficeCode == "" {
		return false
	}

	return true
}
