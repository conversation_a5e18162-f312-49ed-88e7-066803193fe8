package cart_utils

import (
	"database/sql"
	"errors"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	"praktis.bg/store-api/graphql/entity"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/checkout/shipping-utils"
	"praktis.bg/store-api/internal/praktis/econt"
	"praktis.bg/store-api/internal/store_connect"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"praktis.bg/store-api/packages/magento-core/types"
)

func SetQuoteShippingData(
	tx *gorm.DB,
	quote *sales.MagentoQuote,
	data model.ShippingInput,
	method string,
) (*sales.MagentoQuote, error) {
	var err error
	for _, address := range quote.GetAddresses() {
		if address, err = setAddressData(
			tx,
			address,
			data,
		); err != nil {
			return quote, err
		}
		if method != "" {
			address.ShippingMethod = sql.NullString{
				String: method,
				Valid:  true,
			}
		} else {
			address.ShippingMethod = sql.NullString{
				String: getMethodCode(data.Type),
				Valid:  true,
			}
		}

		// Use Select to only save address fields, not related entities like Payment
		err = tx.Omit("Quote", "Quote.Payment").Save(address).Error
		if err != nil {
			return quote, err
		}
	}

	return quote, nil
}

func getMethodCode(methodType model.ShippingMethodType) string {
	switch methodType {
	case model.ShippingMethodTypeEcontToAddress:
		return fmt.Sprintf("%s_%s",
			shipping_utils.PraktisWeightPriceShippingCarrierCode,
			shipping_utils.PraktisWeightPriceShippingCarrierMethod,
		)
	case model.ShippingMethodTypeEcontToOffice:
		return fmt.Sprintf("%s_%s",
			shipping_utils.ExtensaEcontCarrierCode,
			shipping_utils.ExtensaEcontCarrierMethodToOffice,
		)
	case model.ShippingMethodTypeToStore:
		return fmt.Sprintf("%s_%s",
			shipping_utils.PraktisStoreShippingCarrierCode,
			shipping_utils.PraktisStoreShippingCarrierMethod,
		)
	default:
		return fmt.Sprintf("%s_%s",
			shipping_utils.PraktisWeightPriceShippingCarrierCode,
			shipping_utils.PraktisWeightPriceShippingCarrierMethod,
		)
	}
}

func setAddressData(
	transaction *gorm.DB,
	address *sales.MagentoQuoteAddress,
	data model.ShippingInput,
) (*sales.MagentoQuoteAddress, error) {
	var shippingStreet = data.Address
	if data.Type == model.ShippingMethodTypeEcontToOffice {
		officeCode := types.ToStr(data.OfficeCode)
		if officeCode == "" {
			return nil, errors.New("липсва код на офис")
		}

		office := &econt.EcontOffice{}
		err := office.SetDB(transaction).Load(officeCode)
		if err != nil {
			core_utils.DebugError(err)
			return nil, errors.New("невалиден код на офис")
		} else if office.OfficeID < 1 {
			return nil, fmt.Errorf("невалиден код %s на офис", officeCode)
		}

		city := types.ToStr(office.CityID)
		address.CityID = sql.NullString{
			String: city,
			Valid:  true,
		}

		if office.City != nil {
			city = office.City.Name
			address.Postcode = sql.NullString{
				String: office.City.PostCode,
				Valid:  true,
			}
		} else {
			address.Postcode = sql.NullString{
				String: data.PostCode,
				Valid:  true,
			}
		}

		shippingStreet = shipping_utils.AddOfficeCodeToStreet(officeCode, office.Address)
		address.City = sql.NullString{
			String: city,
			Valid:  true,
		}
		address.OfficeCode = sql.NullString{
			String: officeCode,
			Valid:  true,
		}
		address.StoreCode = sql.NullString{
			String: "",
			Valid:  false,
		}
		address.ShippingType = sql.NullInt32{
			Int32: shipping_utils.ShippingTypeToOffice,
			Valid: true,
		}
	} else if data.Type == model.ShippingMethodTypeToStore {
		warehouseCode := types.ToStr(data.StoreCode)
		if warehouseCode == "" {
			return nil, errors.New("лопсва код на магазин")
		}

		store, err := entity.GetStoreByWarehouseCodeInTransaction(transaction, warehouseCode)
		if err != nil || store == nil || store.ID < 1 {
			core_utils.DebugError(err)
			return nil, fmt.Errorf("невалиден код %s на магазин", warehouseCode)
		}

		shippingStreet = shipping_utils.AddStoreCodeToStreet(warehouseCode, store.Address)
		address.StoreCode = sql.NullString{
			String: types.ToStr(data.StoreCode),
			Valid:  true,
		}
		address.City = sql.NullString{
			String: store.City,
			Valid:  true,
		}
		address.Postcode = sql.NullString{
			String: "1000",
			Valid:  true,
		}
		address.OfficeCode = sql.NullString{
			String: "",
			Valid:  false,
		}
		address.ShippingType = sql.NullInt32{
			Int32: shipping_utils.ShippingTypeGetFromStore,
			Valid: true,
		}
	} else if data.Type == model.ShippingMethodTypeEcontToAddress {
		if data.City == "" {
			return nil, errors.New("град е задължителен")
		} else {
			address.City = sql.NullString{
				String: data.City,
				Valid:  true,
			}
			address.CityID = sql.NullString{
				String: types.ToStr(data.CityID),
				Valid:  true,
			}
		}

		if data.PostCode == "" {
			return nil, errors.New("пощенски код е задължителен")
		} else {
			address.Postcode = sql.NullString{
				String: data.PostCode,
				Valid:  true,
			}
		}

		address.OfficeCode = sql.NullString{
			String: "",
			Valid:  false,
		}
		address.StoreCode = sql.NullString{
			String: "",
			Valid:  false,
		}

		address.ShippingType = sql.NullInt32{
			Int32: shipping_utils.ShippingTypeToDore,
			Valid: true,
		}
	} else {
		return nil, errors.New("невалиден тип на доставка")
	}

	if shippingStreet == "" {
		return nil, errors.New("улица е задължителна")
	} else {
		address.Street = sql.NullString{
			String: shippingStreet,
			Valid:  true,
		}
	}

	return address, nil
}

func SaveClientData(
	tx *gorm.DB,
	quote *sales.MagentoQuote,
	data model.ClientInput,
	customerID int64,
	customerIP string,
) (*sales.MagentoQuote, error) {
	var err error
	quote.CustomerEmail = sql.NullString{
		String: data.Email,
		Valid:  true,
	}
	quote.CustomerFirstName = sql.NullString{
		String: data.FirstName,
		Valid:  true,
	}
	quote.CustomerLastName = sql.NullString{
		String: data.LastName,
		Valid:  true,
	}

	quote.CustomerID = customerID
	var isGuest int16 = 1
	if customerID < 1 {
		isGuest = 0
	}

	quote.CustomerIsGuest = sql.NullInt16{
		Int16: isGuest,
		Valid: true,
	}

	// Update only quote fields, not related entities like Payment
	err = tx.Model(&quote).Select(
		"customer_email", "customer_firstname", "customer_lastname",
		"customer_id", "customer_is_guest", "remote_ip",
	).Updates(map[string]interface{}{
		"customer_email":     quote.CustomerEmail,
		"customer_firstname": quote.CustomerFirstName,
		"customer_lastname":  quote.CustomerLastName,
		"customer_id":        quote.CustomerID,
		"customer_is_guest":  quote.CustomerIsGuest,
		"remote_ip":          customerIP,
	}).Error
	if err != nil {
		return quote, err
	}

	for _, address := range quote.GetAddresses() {
		address = SetAddressCustomerData(data, address)

		// Use Omit to prevent saving related entities like Payment
		err = tx.Omit("Quote", "Quote.Payment").Save(address).Error
		if err != nil {
			return quote, err
		}
	}

	return quote, nil
}

func RegisterClientViaMagento(data model.CustomerRegistrationData) (*model.RegistrationResponse, int64, error) {
	result := &model.RegistrationResponse{
		Create:              false,
		RequireConfirmation: false,
	}

	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return result, 0, err
	}

	var registered store_connect.CustomerRegisterResult
	if registered, err = connector.Register(data); err != nil {
		return result, 0, err
	} else if registered.CustomerID < 1 {
		return result, 0, errors.New("failed to register customer")
	}

	result.Create = registered.CustomerID > 0
	result.RequireConfirmation = registered.RequireConfirmation

	return result, registered.CustomerID, nil
}

func RegisterClientOnOrder(data model.NewOrderInput) (int64, error) {
	registerData := model.CustomerRegistrationData{
		Email:               data.Client.Email,
		Firstname:           data.Client.FirstName,
		Lastname:            data.Client.LastName,
		Password:            data.Client.Password,
		NewsletterSubscribe: false,
		Invoice:             data.Client.Invoice,
	}

	_, customerID, err := RegisterClientViaMagento(registerData)
	return customerID, err
}
