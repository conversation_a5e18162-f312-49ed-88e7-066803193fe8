package cart_utils

import (
	"math"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
)

const XMLPathPraktisWeightShippingActive = "praktis_shipping/weight_shipping/enabled"
const XMLPathPraktisWeightShippingThreshold = "praktis_shipping/weight_shipping/big_pallet"

func QuoteItemToCartItem(item *sales.MagentoQuoteItem, currency string) *model.StoreCartItem {
	if item == nil {
		return nil
	}

	price := item.Price
	priceWithoutDiscount := price
	rowTotal := item.RowTotal
	rowTotalWithoutDiscount := rowTotal
	if item.DiscountPct > 0 {
		discountFactor := 1.0 - item.DiscountPct
		priceWithoutDiscount = math.Round(price*discountFactor*100) / 100
		rowTotalWithoutDiscount = math.Round(rowTotal*discountFactor*100) / 100
	}

	return &model.StoreCartItem{
		ID:  item.ItemID,
		Sku: item.Sku,
		Product: &model.SimpleProduct{
			//ID:  item.ProductID,
			Sku: item.Sku,
		},
		Labels: &model.StoreCartItemLabels{
			FreeShipping: QuoteItemHasFreeShipping(item),
			UsePallet:    QuoteItemHasPalletShipping(item),
		},
		BaseQty: item.Qty,
		Price: &model.Price{
			Value:    item.Price,
			Currency: currency,
		},
		PriceWithoutDiscount: &model.Price{
			Value:    priceWithoutDiscount,
			Currency: currency,
		},
		RowTotal: &model.Price{
			Value:    item.RowTotal,
			Currency: currency,
		},
		RowTotalWithoutDiscount: &model.Price{
			Value:    rowTotalWithoutDiscount,
			Currency: currency,
		},
		DiscountAmount: &model.Price{
			Value:    item.DiscountAmt,
			Currency: currency,
		},
		DiscountPercent: item.DiscountPct,
	}
}

func QuoteItemHasPalletShipping(item *sales.MagentoQuoteItem) bool {
	store := item.GetStore()
	if store == nil {
		return false
	} else if store.GetConfigBool(XMLPathPraktisWeightShippingActive, true) == false {
		return false
	}

	threshold := store.GetConfigInt(XMLPathPraktisWeightShippingThreshold, 60)
	totalWeight := item.Weight * item.Qty

	return float64(threshold) <= totalWeight
}

func QuoteItemHasFreeShipping(item *sales.MagentoQuoteItem) bool {
	return (&product.PraktisProduct{EntityID: item.ProductID}).GetHasFreeShipping()
}
