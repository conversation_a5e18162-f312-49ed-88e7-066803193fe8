package cart_utils

import (
	"database/sql"
	"encoding/json"
	"errors"
	"fmt"
	"gorm.io/gorm"
	"log"
	"praktis.bg/store-api/graphql/model"
	credit_calculators "praktis.bg/store-api/internal/praktis/credit-calculators"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"strconv"
	"strings"
)

// getStringValue safely extracts string value from pointer, returning empty string for nil
func getStringValue(ptr *string) string {
	if ptr == nil {
		return ""
	}
	return *ptr
}

// phpSerialize converts a Go map to PHP serialized format for Magento compatibility
func phpSerialize(data map[string]interface{}) string {
	var result strings.Builder
	result.WriteString("a:")
	result.WriteString(strconv.Itoa(len(data)))
	result.WriteString(":{")

	for key, value := range data {
		// Serialize key
		result.WriteString("s:")
		result.WriteString(strconv.Itoa(len(key)))
		result.WriteString(":\"")
		result.WriteString(key)
		result.WriteString("\";")

		// Serialize value
		serializeValue(&result, value)
	}

	result.WriteString("}")
	return result.String()
}

// serializeValue serializes a single value in PHP format
func serializeValue(result *strings.Builder, value interface{}) {
	switch v := value.(type) {
	case string:
		result.WriteString("s:")
		result.WriteString(strconv.Itoa(len(v)))
		result.WriteString(":\"")
		result.WriteString(v)
		result.WriteString("\";")
	case int:
		result.WriteString("i:")
		result.WriteString(strconv.Itoa(v))
		result.WriteString(";")
	case float64:
		// Convert to string to match Magento's behavior
		result.WriteString("s:")
		str := strconv.FormatFloat(v, 'f', -1, 64)
		result.WriteString(strconv.Itoa(len(str)))
		result.WriteString(":\"")
		result.WriteString(str)
		result.WriteString("\";")
	case map[string]interface{}:
		result.WriteString("a:")
		result.WriteString(strconv.Itoa(len(v)))
		result.WriteString(":{")
		for k, val := range v {
			result.WriteString("s:")
			result.WriteString(strconv.Itoa(len(k)))
			result.WriteString(":\"")
			result.WriteString(k)
			result.WriteString("\";")
			serializeValue(result, val)
		}
		result.WriteString("}")
	case nil:
		result.WriteString("N;")
	default:
		// Handle special Go values that should be null
		str := fmt.Sprintf("%v", v)
		if str == "<nil>" || str == "0xc" || strings.HasPrefix(str, "0xc") {
			result.WriteString("N;")
		} else {
			// Special handling for variant_id to ensure it's always an integer in PHP serialization
			if strings.Contains(fmt.Sprintf("%T", value), "int") {
				// Try to convert to integer if it looks like a number
				if intVal, err := strconv.Atoi(str); err == nil {
					result.WriteString("i:")
					result.WriteString(strconv.Itoa(intVal))
					result.WriteString(";")
					return
				}
			}
			// Fallback to string representation
			result.WriteString("s:")
			result.WriteString(strconv.Itoa(len(str)))
			result.WriteString(":\"")
			result.WriteString(str)
			result.WriteString("\";")
		}
	}
}

const BankTransferCode = "banktransfer"
const CacheOnDeliveryCode = "cashondelivery"
const DebitOrCreditCardCode = "extensa_rbb"
const TBICreditCardCode = "paymentmethodbnpl"
const StenikLeasingJetCreditCode = "stenik_leasingjetcredit"

var allowedCodes = []string{
	BankTransferCode,
	CacheOnDeliveryCode,
	DebitOrCreditCardCode,
	TBICreditCardCode,
	StenikLeasingJetCreditCode,
}

func GetAvailablePraktisPayments(s *magento_core.StoreEntity) []sales.IPaymentMethod {
	var result []sales.IPaymentMethod
	for _, code := range allowedCodes {
		p := sales.InitNewStorePayment(s, code)
		if p.IsActive() {
			result = append(result, p)
		}
	}

	return result
}

func ToMethodModel(p sales.IPaymentMethod) *model.PaymentMethod {
	return &model.PaymentMethod{
		Name:         p.GetTitle(),
		Code:         p.GetCode(),
		ExtraContent: p.GetInstructions(),
	}
}

func UpdateQuotePaymentMethod(tx *gorm.DB, quote *sales.MagentoQuote, method string) error {
	var payments []*sales.MagentoQuotePayment
	if quote.Payment != nil {
		payments = append(payments, quote.Payment)
	} else {
		tx.Model(&sales.MagentoQuotePayment{}).
			Where("quote_id = ?", quote.EntityID).
			Find(&payments)
	}

	if len(payments) == 0 {
		return fmt.Errorf("няма намерени плащания за поръчката")
	}

	for _, payment := range payments {
		log.Printf("[DEBUG] UpdateQuotePaymentMethod: Processing payment ID %d, current method: %s, target method: %s",
			payment.PaymentID, payment.Method, method)

		// If the payment method is already correct, skip the update entirely
		if payment.Method == method {
			log.Printf("[DEBUG] UpdateQuotePaymentMethod: Payment method already correct for payment ID %d, skipping update", payment.PaymentID)
			continue
		}

		// Special handling for BNP payment method to preserve additional information
		if method == StenikLeasingJetCreditCode && payment.Method == StenikLeasingJetCreditCode {
			// Payment method is already BNP and we want to keep it BNP
			// Don't update anything to preserve the additional_information (loan data)
			log.Printf("[DEBUG] BNP: Preserving existing BNP payment data for payment ID %d", payment.PaymentID)
			continue
		}

		log.Printf("[DEBUG] UpdateQuotePaymentMethod: Updating payment ID %d method from %s to %s",
			payment.PaymentID, payment.Method, method)

		// Use Update instead of UpdateColumn to ensure we don't clear other fields
		if err := tx.Model(payment).Update("method", method).Error; err != nil {
			return err
		}

		log.Printf("[DEBUG] UpdateQuotePaymentMethod: Successfully updated payment ID %d", payment.PaymentID)
	}
	return nil
}

// UpdateQuotePaymentMethodWithBNPData updates quote payment method and stores BNP payment data
func UpdateQuotePaymentMethodWithBNPData(tx *gorm.DB, quote *sales.MagentoQuote, paymentData model.BNPPaymentInput) error {
	log.Printf("[INFO] BNP: === PAYMENT DATA UPDATE START ===")
	log.Printf("[INFO] BNP: Quote ID: %d", quote.EntityID)
	log.Printf("[INFO] BNP: Variant ID: %d, Down Payment: %.2f",
		paymentData.PricingVariantID, paymentData.DownPayment)

	// First update the payment method
	log.Printf("[DEBUG] BNP: Updating payment method to BNP Paribas...")
	err := UpdateQuotePaymentMethod(tx, quote, StenikLeasingJetCreditCode)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to update payment method: %v", err)
		return err
	}
	log.Printf("[INFO] BNP: Payment method updated successfully")

	// Calculate principal from cart subtotal
	principal := quote.Subtotal.Float64
	if principal <= 0 {
		log.Printf("[ERROR] BNP: Cart subtotal is zero or negative: %.2f", principal)
		return fmt.Errorf("cart total must be greater than zero for BNP financing")
	}
	log.Printf("[INFO] BNP: Calculated principal from cart subtotal: %.2f", principal)

	// Determine good type IDs from cart products
	goodTypeIds, err := determineGoodTypeIdsFromCart(quote)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to determine good type IDs: %v", err)
		return fmt.Errorf("failed to determine good type IDs: %w", err)
	}
	log.Printf("[INFO] BNP: Determined good type IDs: %s", goodTypeIds)

	// Calculate loan details with detailed logging
	log.Printf("[DEBUG] BNP: Calculating loan details...")
	log.Printf("[DEBUG] BNP: Loan parameters - GoodTypeIds: %s, Principal: %.2f, DownPayment: %.2f, VariantID: %d",
		goodTypeIds, principal, paymentData.DownPayment, paymentData.PricingVariantID)

	loanCalculation, err := credit_calculators.CalculateBNPLoanLegacy(
		goodTypeIds,
		principal,
		paymentData.DownPayment,
		paymentData.PricingVariantID,
	)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to calculate loan: %v", err)
		log.Printf("[ERROR] BNP: Loan calculation error type: %T", err)
		return fmt.Errorf("failed to calculate loan: %w", err)
	}

	log.Printf("[INFO] BNP: Loan calculation successful - APR: %s, Installment: %s, Total: %s",
		loanCalculation.Apr, loanCalculation.InstallmentAmount, loanCalculation.TotalRepaymentAmount)

	// Prepare additional information with detailed logging
	log.Printf("[DEBUG] BNP: Preparing payment additional information...")

	// Log customer data
	log.Printf("[DEBUG] BNP: Customer data - Name: %s %s, Email: %s, Phone: %s",
		paymentData.CustomerData.FirstName, paymentData.CustomerData.LastName,
		paymentData.CustomerData.Email, paymentData.CustomerData.Phone)

	// Prepare combined name field for Magento compatibility
	combinedName := paymentData.CustomerData.FirstName
	if paymentData.CustomerData.LastName != "" {
		if combinedName != "" {
			combinedName += " " + paymentData.CustomerData.LastName
		} else {
			combinedName = paymentData.CustomerData.LastName
		}
	}

	// Use EGN as PIN for Magento compatibility (Bulgarian personal identification number)
	pin := ""
	if paymentData.CustomerData.Egn != nil {
		pin = *paymentData.CustomerData.Egn
	}

	// Get all available variants and determine array index for the selected variant
	log.Printf("[INFO] BNP: Determining array index for variant %d by calling Magento API", paymentData.PricingVariantID)
	allVariants, err := callMagentoBNPVariantsAPI(paymentData.DownPayment, goodTypeIds, principal)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to get all variants from Magento API: %v", err)
		return fmt.Errorf("failed to get all variants: %w", err)
	}

	// Find the array index for the selected variant
	arrayIndex := -1
	for i, variant := range allVariants {
		if variant.ID == paymentData.PricingVariantID {
			arrayIndex = i
			break
		}
	}

	if arrayIndex == -1 {
		log.Printf("[ERROR] BNP: Variant %d not found in available variants", paymentData.PricingVariantID)
		return fmt.Errorf("variant %d not found in available variants", paymentData.PricingVariantID)
	}

	log.Printf("[DEBUG] BNP: Found variant %d at array index %d", paymentData.PricingVariantID, arrayIndex)
	log.Printf("[DEBUG] BNP: Retrieved %d variants from Magento API", len(allVariants))

	additionalInfo := map[string]interface{}{
		"loan": map[string]interface{}{
			"apr":                        loanCalculation.Apr,
			"correct_downpayment_amount": loanCalculation.CorrectDownpaymentAmount,
			"installment_amount":         loanCalculation.InstallmentAmount,
			"maturity":                   loanCalculation.Maturity,
			"nir":                        loanCalculation.Nir,
			"pricing_scheme_id":          loanCalculation.PricingSchemeID,
			"pricing_scheme_name":        loanCalculation.PricingSchemeName,
			"pricing_variant_id":         loanCalculation.PricingVariantID,
			"processing_fee_amount":      loanCalculation.ProcessingFeeAmount,
			"total_repayment_amount":     loanCalculation.TotalRepaymentAmount,
		},
		"customer_data": map[string]interface{}{
			"first_name": paymentData.CustomerData.FirstName,
			"last_name":  paymentData.CustomerData.LastName,
			"phone":      paymentData.CustomerData.Phone,
			"email":      paymentData.CustomerData.Email,
			"address":    paymentData.CustomerData.Address,
			"city":       paymentData.CustomerData.City,
			"post_code":  paymentData.CustomerData.PostCode,
			"egn":        getStringValue(paymentData.CustomerData.Egn),
			"company_name": getStringValue(paymentData.CustomerData.CompanyName),
			"eik":        getStringValue(paymentData.CustomerData.Eik),
			"mol":        getStringValue(paymentData.CustomerData.Mol),
		},
		// Magento-compatible fields at root level for backward compatibility
		"variant_id":    arrayIndex, // Store array index for admin panel variant selection compatibility
		"downpayment":   paymentData.DownPayment,
		"names":         combinedName,  // Magento expects combined name (plural)
		"name":          combinedName,  // Admin form expects singular 'name' field
		"pin":           pin,           // Magento expects PIN (EGN in Bulgaria)
		"email":         paymentData.CustomerData.Email,  // Magento expects email at root level
		"phone":         paymentData.CustomerData.Phone,  // Magento expects phone at root level
		"good_type_ids": goodTypeIds,
		"principal":     principal,
	}

	// Add all available variants for admin panel display using numeric keys (Magento template expects this format)
	for i, variant := range allVariants {
		variantKey := strconv.Itoa(i)

		// Clean up illogical negative APR values for promotional schemes
		cleanAPR := variant.APR
		if cleanAPR < 0 {
			log.Printf("[INFO] BNP: Normalizing negative APR %.2f%% to 0.00%% for variant %d (promotional scheme)", cleanAPR, variant.ID)
			cleanAPR = 0.0
		}

		additionalInfo[variantKey] = map[string]interface{}{
			"id":                         variant.ID,
			"apr":                        cleanAPR,
			"installment":                variant.Installment,
			"total_repayment":            variant.TotalRepayment,
			"maturity":                   variant.Maturity,
			"nir":                        variant.NIR,
			"pricing_scheme_id":          variant.PricingSchemeID,
			"pricing_scheme_name":        variant.PricingSchemeName,
			"correct_downpayment_amount": variant.CorrectDownpaymentAmount,
		}
	}
	log.Printf("[DEBUG] BNP: Added %d variants to payment data with numeric keys for admin panel display", len(allVariants))

	// Add selected variant data for admin panel display (fallback when variants array is not used)
	additionalInfo["selected_variant"] = map[string]interface{}{
		"id":                       loanCalculation.PricingVariantID,
		"apr":                      loanCalculation.Apr,
		"installment":              loanCalculation.InstallmentAmount,
		"total_repayment":          loanCalculation.TotalRepaymentAmount,
		"maturity":                 loanCalculation.Maturity,
		"nir":                      loanCalculation.Nir,
		"pricing_scheme_id":        loanCalculation.PricingSchemeID,
		"pricing_scheme_name":      loanCalculation.PricingSchemeName,
		"correct_downpayment_amount": loanCalculation.CorrectDownpaymentAmount,
	}

	// Convert to PHP serialized format for Magento compatibility
	log.Printf("[DEBUG] BNP: Serializing payment data to PHP format for Magento compatibility...")
	additionalInfoSerialized := phpSerialize(additionalInfo)
	log.Printf("[DEBUG] BNP: Payment data serialized successfully (%d bytes)", len(additionalInfoSerialized))

	// Also keep JSON version for backup storage
	additionalInfoJSON, err := json.Marshal(additionalInfo)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to marshal additional information to JSON: %v", err)
		return fmt.Errorf("failed to marshal additional information to JSON: %w", err)
	}

	// Update payment additional information with detailed logging
	log.Printf("[DEBUG] BNP: Updating quote payment records...")

	var payments []*sales.MagentoQuotePayment
	if quote.Payment != nil {
		log.Printf("[DEBUG] BNP: Using existing quote payment record")
		payments = append(payments, quote.Payment)
	} else {
		log.Printf("[DEBUG] BNP: Loading quote payment records from database...")
		tx.Model(&sales.MagentoQuotePayment{}).
			Where("quote_id = ?", quote.EntityID).
			Find(&payments)
		log.Printf("[DEBUG] BNP: Found %d payment records", len(payments))
	}

	if len(payments) == 0 {
		log.Printf("[ERROR] BNP: No payment records found for quote %d", quote.EntityID)
		return fmt.Errorf("no payment records found for quote")
	}

	for i, payment := range payments {
		log.Printf("[DEBUG] BNP: Updating payment record %d (ID: %d)", i+1, payment.PaymentID)
		err = tx.Model(payment).UpdateColumn("additional_information", additionalInfoSerialized).Error
		if err != nil {
			log.Printf("[ERROR] BNP: Failed to update payment %d additional information: %v", payment.PaymentID, err)
			return fmt.Errorf("failed to update payment additional information: %w", err)
		}
		log.Printf("[DEBUG] BNP: Payment record %d updated successfully", payment.PaymentID)
	}

	// Store BNP payment data in a persistent location that won't be affected by external Magento API
	err = storeBNPPaymentDataPersistent(tx, quote.EntityID, string(additionalInfoJSON))
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to store persistent payment data: %v", err)
		return fmt.Errorf("failed to store persistent BNP payment data: %w", err)
	}

	log.Printf("[INFO] BNP: === PAYMENT DATA UPDATE COMPLETED ===")
	log.Printf("[INFO] BNP: Quote: %d, Updated %d payment records, Status: SUCCESS", quote.EntityID, len(payments))

	// Debug: Verify payment data is still in database immediately after save
	var verifyInfo sql.NullString
	err = tx.Model(&sales.MagentoQuotePayment{}).
		Where("quote_id = ?", quote.EntityID).
		Select("additional_information").
		Scan(&verifyInfo).Error
	if err == nil {
		log.Printf("[DEBUG] BNP: Immediate verification after save - Valid: %v, Length: %d", verifyInfo.Valid, len(verifyInfo.String))
	}
	return nil
}

// BNPPaymentDataStorage represents the dedicated table for storing BNP payment data
type BNPPaymentDataStorage struct {
	ID          int64  `gorm:"primaryKey;autoIncrement;column:id"`
	QuoteID     int64  `gorm:"column:quote_id;uniqueIndex"`
	PaymentData string `gorm:"column:payment_data;type:text"`
	CreatedAt   string `gorm:"column:created_at"`
	UpdatedAt   string `gorm:"column:updated_at"`
}

func (BNPPaymentDataStorage) TableName() string {
	return "bnp_payment_data_storage"
}

// ensureBNPTableExists creates the BNP payment data storage table if it doesn't exist
func ensureBNPTableExists(tx *gorm.DB) error {
	// Create table if it doesn't exist
	err := tx.Exec(`
		CREATE TABLE IF NOT EXISTS bnp_payment_data_storage (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			quote_id bigint(20) NOT NULL,
			payment_data text NOT NULL,
			created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			UNIQUE KEY unique_quote_id (quote_id),
			KEY idx_quote_id (quote_id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
	`).Error

	return err
}

// storeBNPPaymentDataPersistent stores BNP payment data in a dedicated table that won't be affected by external Magento API
func storeBNPPaymentDataPersistent(tx *gorm.DB, quoteID int64, paymentData string) error {
	log.Printf("[INFO] BNP: Storing persistent payment data for quote %d (%d bytes)", quoteID, len(paymentData))

	// Ensure the table exists
	err := ensureBNPTableExists(tx)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to ensure table exists: %v", err)
		return fmt.Errorf("failed to ensure BNP table exists: %w", err)
	}

	// Use ON DUPLICATE KEY UPDATE to handle both insert and update cases
	err = tx.Exec(`
		INSERT INTO bnp_payment_data_storage (quote_id, payment_data, created_at, updated_at)
		VALUES (?, ?, NOW(), NOW())
		ON DUPLICATE KEY UPDATE
		payment_data = VALUES(payment_data),
		updated_at = NOW()
	`, quoteID, paymentData).Error

	if err != nil {
		return fmt.Errorf("failed to store persistent BNP payment data: %w", err)
	}

	log.Printf("[INFO] BNP: Persistent payment data stored successfully in dedicated table")
	return nil
}

// RetrieveBNPPaymentDataPersistent retrieves BNP payment data from persistent storage
func RetrieveBNPPaymentDataPersistent(tx *gorm.DB, quoteID int64) (string, error) {
	log.Printf("[INFO] BNP: Retrieving persistent payment data for quote %d", quoteID)

	var storage BNPPaymentDataStorage
	err := tx.Where("quote_id = ?", quoteID).First(&storage).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Printf("[DEBUG] BNP: No persistent payment data found for quote %d", quoteID)
			return "", nil
		}
		return "", fmt.Errorf("failed to retrieve persistent BNP payment data: %w", err)
	}

	if storage.PaymentData == "" {
		log.Printf("[DEBUG] BNP: Empty persistent payment data found for quote %d", quoteID)
		return "", nil
	}

	log.Printf("[INFO] BNP: Retrieved persistent payment data (%d bytes) for quote %d", len(storage.PaymentData), quoteID)
	return storage.PaymentData, nil
}

// TransferBNPPaymentDataToOrder transfers BNP payment data from backup storage to the order payment record
func TransferBNPPaymentDataToOrder(db *gorm.DB, quoteID int64, orderID int64) error {
	log.Printf("[INFO] BNP: Transferring payment data from quote %d to order %d", quoteID, orderID)

	// Retrieve BNP payment data from persistent storage
	paymentData, err := RetrieveBNPPaymentDataPersistent(db, quoteID)
	if err != nil {
		return fmt.Errorf("failed to retrieve BNP payment data: %w", err)
	}

	if paymentData == "" {
		log.Printf("[DEBUG] BNP: No payment data found for quote %d, skipping transfer", quoteID)
		return nil
	}

	log.Printf("[INFO] BNP: Found BNP payment data (%d bytes), transferring to order payment", len(paymentData))

	// Convert JSON backup data to PHP serialized format for Magento compatibility
	var additionalInfo map[string]interface{}
	err = json.Unmarshal([]byte(paymentData), &additionalInfo)
	if err != nil {
		return fmt.Errorf("failed to unmarshal backup payment data: %w", err)
	}

	// Ensure variant_id is an integer for proper admin panel comparison (it should already be an array index)
	if variantID, exists := additionalInfo["variant_id"]; exists {
		if variantFloat, ok := variantID.(float64); ok {
			additionalInfo["variant_id"] = int(variantFloat)
			log.Printf("[DEBUG] BNP: Converted variant_id from float64 %.0f to int %d for admin panel compatibility", variantFloat, int(variantFloat))
		} else if variantString, ok := variantID.(string); ok {
			// If it's a string, try to parse it as an integer
			if parsedInt, err := strconv.Atoi(variantString); err == nil {
				additionalInfo["variant_id"] = parsedInt
				log.Printf("[DEBUG] BNP: Converted variant_id from string %s to int %d for admin panel compatibility", variantString, parsedInt)
			} else {
				log.Printf("[WARNING] BNP: Could not parse variant_id string %s as integer, keeping as string", variantString)
			}
		}
		// If it's already an int, leave it as is
	}

	// Convert to PHP serialized format
	phpSerializedData := phpSerialize(additionalInfo)
	log.Printf("[INFO] BNP: Converted payment data to PHP serialized format (%d bytes)", len(phpSerializedData))

	// Update the order payment record with the PHP serialized BNP payment data
	result := db.Exec(`
		UPDATE sales_flat_order_payment
		SET additional_information = ?
		WHERE parent_id = ?
	`, phpSerializedData, orderID)

	if result.Error != nil {
		return fmt.Errorf("failed to update order payment additional information: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		log.Printf("[WARNING] BNP: No order payment records found for order %d", orderID)
		return fmt.Errorf("no order payment records found for order %d", orderID)
	}

	log.Printf("[INFO] BNP: Payment data successfully transferred to order %d (%d records updated)", orderID, result.RowsAffected)

	// Verify the transfer by reading back the data from the order payment record using raw SQL
	var verificationData sql.NullString
	var paymentID int64
	err = db.Raw(`
		SELECT entity_id, additional_information
		FROM sales_flat_order_payment
		WHERE parent_id = ?
	`, orderID).Row().Scan(&paymentID, &verificationData)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to verify transferred payment data: %v", err)
		return fmt.Errorf("failed to verify transferred payment data: %w", err)
	}

	if !verificationData.Valid || verificationData.String == "" {
		log.Printf("[ERROR] BNP: VERIFICATION FAILED - Order payment additional_information is empty after transfer!")
		log.Printf("[ERROR] BNP: Order ID: %d, Payment ID: %d", orderID, paymentID)
		return fmt.Errorf("verification failed: order payment additional_information is empty after transfer")
	}

	// Log verification success with data preview
	dataPreview := verificationData.String
	if len(dataPreview) > 100 {
		dataPreview = dataPreview[:100] + "..."
	}
	log.Printf("[SUCCESS] BNP: VERIFICATION PASSED - Payment data confirmed in database")
	log.Printf("[SUCCESS] BNP: Order ID: %d, Payment ID: %d, Data Length: %d bytes", orderID, paymentID, len(verificationData.String))
	log.Printf("[SUCCESS] BNP: Data Preview: %s", dataPreview)

	// Clean up the backup data since it's no longer needed
	err = db.Where("quote_id = ?", quoteID).Delete(&BNPPaymentDataStorage{}).Error
	if err != nil {
		log.Printf("[WARNING] BNP: Failed to clean up backup data for quote %d: %v", quoteID, err)
		// Don't return error for cleanup failure
	} else {
		log.Printf("[INFO] BNP: Backup data cleaned up for quote %d", quoteID)
	}

	return nil
}

// ValidateBNPPaymentData validates BNP payment data before processing
func ValidateBNPPaymentData(data model.BNPPaymentInput) error {
	log.Printf("[INFO] BNP: === PAYMENT DATA VALIDATION START ===")
	log.Printf("[DEBUG] BNP: Validating BNP payment data...")

	// Validate basic payment parameters
	log.Printf("[DEBUG] BNP: Validating payment parameters...")

	if data.DownPayment < 0 {
		log.Printf("[ERROR] BNP: Down payment is negative: %.2f", data.DownPayment)
		return fmt.Errorf("down payment cannot be negative")
	}

	if data.PricingVariantID <= 0 {
		log.Printf("[ERROR] BNP: Pricing variant ID is invalid: %d", data.PricingVariantID)
		return fmt.Errorf("pricing variant ID is required")
	}

	log.Printf("[DEBUG] BNP: Payment parameters valid - DownPayment: %.2f, VariantID: %d",
		data.DownPayment, data.PricingVariantID)

	// Validate customer data
	log.Printf("[DEBUG] BNP: Validating customer data...")
	if data.CustomerData == nil {
		log.Printf("[ERROR] BNP: Customer data is nil")
		return fmt.Errorf("customer data is required")
	}

	if data.CustomerData.FirstName == "" {
		log.Printf("[ERROR] BNP: Customer first name is empty")
		return fmt.Errorf("customer first name is required")
	}

	if data.CustomerData.LastName == "" {
		log.Printf("[ERROR] BNP: Customer last name is empty")
		return fmt.Errorf("customer last name is required")
	}

	if data.CustomerData.Phone == "" {
		log.Printf("[ERROR] BNP: Customer phone is empty")
		return fmt.Errorf("customer phone is required")
	}

	if data.CustomerData.Email == "" {
		log.Printf("[ERROR] BNP: Customer email is empty")
		return fmt.Errorf("customer email is required")
	}

	if data.CustomerData.Address == "" {
		log.Printf("[ERROR] BNP: Customer address is empty")
		return fmt.Errorf("customer address is required")
	}

	if data.CustomerData.City == "" {
		log.Printf("[ERROR] BNP: Customer city is empty")
		return fmt.Errorf("customer city is required")
	}

	// Log successful validation
	log.Printf("[DEBUG] BNP: Customer data valid - Name: %s %s, Email: %s, Phone: %s",
		data.CustomerData.FirstName, data.CustomerData.LastName,
		data.CustomerData.Email, data.CustomerData.Phone)

	log.Printf("[INFO] BNP: === PAYMENT DATA VALIDATION COMPLETED ===")
	log.Printf("[INFO] BNP: Status: SUCCESS - All validation checks passed")
	return nil
}

// determineGoodTypeIdsFromCart analyzes cart products and determines appropriate BNP good type IDs
func determineGoodTypeIdsFromCart(quote *sales.MagentoQuote) (string, error) {
	log.Printf("[INFO] BNP: === DETERMINING GOOD TYPE IDS FROM CART ===")

	// Get cart items
	quoteItems, err := quote.GetItems()
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to retrieve cart items: %v", err)
		return "", fmt.Errorf("failed to retrieve cart items: %w", err)
	}

	if len(quoteItems) == 0 {
		log.Printf("[ERROR] BNP: Cart is empty")
		return "", fmt.Errorf("cart is empty")
	}

	log.Printf("[INFO] BNP: Analyzing %d cart items for good type IDs", len(quoteItems))

	// Use the same logic as in the existing BNP calculator
	// We'll use the good type ID from the first item that has one
	var goodTypeIds string
	productRepo := product.NewPraktisProductRepository(nil)

	for i, item := range quoteItems {
		if item.Sku == "" {
			log.Printf("[WARNING] BNP: Skipping cart item %d with empty SKU", i+1)
			continue
		}

		log.Printf("[DEBUG] BNP: Processing cart item %d: SKU=%s, Qty=%.2f, Price=%.2f",
			i+1, item.Sku, item.Qty, item.Price)

		// Fetch product entity to get the good type ID
		productEntity, err := productRepo.GetSKU(item.Sku, []string{
			"stenik_jetcredit_good_type_id",
		})
		if err != nil {
			log.Printf("[WARNING] BNP: Failed to fetch product entity for SKU %s: %v", item.Sku, err)
			continue
		}

		itemGoodTypeIds := productEntity.MustGetVal("stenik_jetcredit_good_type_id")
		if itemGoodTypeIds != "" {
			goodTypeIds = itemGoodTypeIds
			log.Printf("[INFO] BNP: Using good type IDs '%s' from product %s (item %d)", goodTypeIds, item.Sku, i+1)
			break
		} else {
			log.Printf("[DEBUG] BNP: Product %s (item %d) has no good type IDs", item.Sku, i+1)
		}
	}

	// If no good type IDs found, return error
	if goodTypeIds == "" {
		log.Printf("[ERROR] BNP: No products with good type IDs found in cart")
		return "", fmt.Errorf("no products in cart support BNP financing")
	}

	log.Printf("[INFO] BNP: Successfully determined good type IDs: %s", goodTypeIds)
	return goodTypeIds, nil
}

// getBNPVariantArrayIndex dynamically determines the array index for a variant ID by calling the Magento BNP API
func getBNPVariantArrayIndex(variantID int, downPayment float64, goodTypeIds string, principal float64) (int, error) {
	log.Printf("[INFO] BNP: Determining array index for variant %d by calling Magento API", variantID)

	// Call the same Magento BNP API that the admin panel uses to get available variants
	variants, err := callMagentoBNPVariantsAPI(downPayment, goodTypeIds, principal)
	if err != nil {
		return 0, fmt.Errorf("failed to get variants from Magento API: %w", err)
	}

	log.Printf("[DEBUG] BNP: Retrieved %d variants from Magento API", len(variants))

	// Find the array index for the selected variant ID
	for index, variant := range variants {
		if variant.ID == variantID {
			log.Printf("[DEBUG] BNP: Found variant %d at array index %d", variantID, index)
			return index, nil
		}
	}

	return 0, fmt.Errorf("variant ID %d not found in available variants", variantID)
}

// BNPVariant represents a variant returned by the Magento BNP API
// Field names match what the admin panel template expects
type BNPVariant struct {
	ID                         int     `json:"id"`
	APR                        float64 `json:"apr"`
	CorrectDownpaymentAmount   float64 `json:"correct_downpayment_amount"`
	Installment                float64 `json:"installment"`          // Admin panel expects 'installment'
	Maturity                   int     `json:"maturity"`
	NIR                        float64 `json:"nir"`
	PricingSchemeID            int     `json:"pricing_scheme_id"`
	PricingSchemeName          string  `json:"pricing_scheme_name"`
	TotalRepayment             float64 `json:"total_repayment"`      // Admin panel expects 'total_repayment'
}

// callMagentoBNPVariantsAPI calls the BNP API to get available variants (same API that admin panel uses)
func callMagentoBNPVariantsAPI(downPayment float64, goodTypeIds string, principal float64) ([]BNPVariant, error) {
	log.Printf("[INFO] BNP: Calling BNP variants API with downPayment=%.2f, goodTypeIds=%s, principal=%.2f",
		downPayment, goodTypeIds, principal)

	// Use the same function that the GraphQL resolver uses to get variants
	// This calls the same BNP API that the admin panel uses
	variantGroups, err := credit_calculators.GetAvailableVariantsGroupedByPricingScheme(goodTypeIds, principal, downPayment)
	if err != nil {
		return nil, fmt.Errorf("failed to get BNP variants: %w", err)
	}

	if len(variantGroups) == 0 {
		return nil, fmt.Errorf("no variant groups returned from BNP calculator")
	}

	log.Printf("[DEBUG] BNP: BNP calculator returned %d variant groups", len(variantGroups))

	// Flatten all variants from all groups into a single array (same order as admin panel)
	var variants []BNPVariant
	for groupIndex, group := range variantGroups {
		log.Printf("[DEBUG] BNP: Processing group %d with scheme ID %s containing %d variants",
			groupIndex, group.SchemeID, len(group.Variants))

		for variantIndex, variant := range group.Variants {
			// Convert string fields to appropriate types
			variantID, _ := strconv.Atoi(variant.ID)
			apr, _ := strconv.ParseFloat(variant.Apr, 64)
			correctDownpayment, _ := strconv.ParseFloat(variant.CorrectDownpaymentAmount, 64)
			installment, _ := strconv.ParseFloat(variant.InstallmentAmount, 64)
			maturity, _ := strconv.Atoi(variant.Maturity)
			nir, _ := strconv.ParseFloat(variant.Nir, 64)
			schemeID, _ := strconv.Atoi(variant.PricingSchemeID)
			totalRepayment, _ := strconv.ParseFloat(variant.TotalRepaymentAmount, 64)

			bnpVariant := BNPVariant{
				ID:                       variantID,
				APR:                      apr,
				CorrectDownpaymentAmount: correctDownpayment,
				Installment:              installment,        // Admin panel expects 'installment'
				Maturity:                 maturity,
				NIR:                      nir,
				PricingSchemeID:          schemeID,
				PricingSchemeName:        variant.PricingSchemeName,
				TotalRepayment:           totalRepayment,     // Admin panel expects 'total_repayment'
			}
			variants = append(variants, bnpVariant)

			log.Printf("[DEBUG] BNP: Variant %d (group %d, variant %d): %d months, %.2f%% APR, %.2f BGN installment",
				variantID, groupIndex, variantIndex, maturity, apr, installment)
		}
	}

	log.Printf("[INFO] BNP: Successfully retrieved %d total variants from BNP API", len(variants))
	return variants, nil
}
