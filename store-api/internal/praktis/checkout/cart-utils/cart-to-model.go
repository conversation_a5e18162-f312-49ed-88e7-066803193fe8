package cart_utils

import (
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	product_index "praktis.bg/store-api/internal/praktis/catalog/product"
	"praktis.bg/store-api/internal/praktis/catalog/stock"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
)

func NewEmptyCart() (*model.StoreCart, error) {
	jwtToken, err := sales.GetNewCartToken().String()
	if err != nil {
		return nil, err
	}

	return &model.StoreCart{
		Token:      jwtToken,
		Items:      []*model.StoreCartItem{},
		StoreCode:  magento_core.DefaultStoreCode,
		CouponCode: "",
		Currency:   "BGN",
		Customer:   GetQuoteCustomerData(nil),
		Totals:     getTotals(nil),
		Shipping: &model.CartShipping{
			AvailableMethods:  []model.ShippingMethodType{},
			AvailableIn:       []*model.PraktisStore{},
			HasFreeShipping:   false,
			FreeShippingAfter: nil,
			SelectedMethod:    "",
			Address:           &model.ShippingAddress{},
		},
	}, nil
}

func QuoteToCartModel(quote *sales.MagentoQuote) *model.StoreCart {
	if quote == nil {
		return nil
	}

	items := quote.MustGetItems()
	cartItems := make([]*model.StoreCartItem, len(items))
	for i, item := range items {
		cartItems[i] = QuoteItemToCartItem(item, quote.QuoteCurrencyCode)
	}

	paymentMethod := ""
	if quote.Payment != nil {
		paymentMethod = quote.Payment.Method
	}

	return &model.StoreCart{
		ID:            quote.EntityID,
		Token:         quote.GetStringToken(),
		StoreCode:     quote.GetStore().StoreCode,
		Currency:      quote.QuoteCurrencyCode,
		CouponCode:    quote.CouponCode.String,
		Note:          quote.CustomerNote.String,
		Customer:      GetQuoteCustomerData(quote),
		Items:         cartItems,
		Shipping:      &model.CartShipping{},
		PaymentMethod: paymentMethod,
		Totals:        getTotals(quote),
	}
}

func GetCartShipping(quote *sales.MagentoQuote) *model.CartShipping {
	var freeShippingAfter *int
	var minAmountForFreeShippingMessage *int

	store := quote.GetStore()
	if store.GetConfigBool(XMLPathPraktisFreeShippingActive, true) {
		freeShippingAfter = store.GetConfigIntP(XMLPathPraktisFreeShippingFreeShippingAfter, 500)
		minAmountForFreeShippingMessage = store.GetConfigIntP(XMLPathPraktisFreeShippingShowMessageAfter, 500)
	}

	hasFreeShipping := false
	allItemsAreFree := true
	var skus []string
	for _, item := range quote.MustGetItems() {
		if allItemsAreFree && QuoteItemHasFreeShipping(item) == false {
			allItemsAreFree = false
		}
		skus = append(skus, item.Sku)
	}

	if allItemsAreFree {
		hasFreeShipping = true
	}

	shippingAddress := quote.GetShippingAddress()

	var address *model.ShippingAddress
	if shippingAddress != nil && shippingAddress.ShippingMethod.String != "" {
		address = addressToModel(shippingAddress, skus)
	}

	availableIn, err := GetCartAvailableStores(quote)
	core_utils.DebugError(err)

	return &model.CartShipping{
		AvailableMethods:                getCartAvailableMethods(quote, availableIn),
		AvailableIn:                     availableIn,
		HasFreeShipping:                 hasFreeShipping,
		FreeShippingAfter:               freeShippingAfter,
		MinAmountForFreeShippingMessage: minAmountForFreeShippingMessage,
		SelectedMethod:                  shippingAddress.ShippingMethod.String,
		Address:                         address,
	}
}

func GetCartAvailableStores(quote *sales.MagentoQuote) ([]*model.PraktisStore, error) {
	productIdsQtyMap := make(map[int64]float64)
	for _, i := range quote.MustGetItems() {
		productIdsQtyMap[i.ProductID] = i.Qty
	}

	storeE, err := product_index.GetPickupStores(productIdsQtyMap)
	if err != nil {
		return nil, err
	}

	var result []*model.PraktisStore
	for _, store := range storeE {
		result = append(result, store.ToModel())
	}

	return result, nil
}

func getCartAvailableMethods(quote *sales.MagentoQuote, availableIn []*model.PraktisStore) []model.ShippingMethodType {
	var availableMethods []model.ShippingMethodType

	if len(availableIn) > 0 {
		availableMethods = append(availableMethods, model.ShippingMethodTypeToStore)
	}

	onlyStorePickup := QuoteHasOnlyStorePickupItems(quote)

	if !onlyStorePickup {
		availableMethods = append(availableMethods,
			model.ShippingMethodTypeEcontToOffice, model.ShippingMethodTypeEcontToAddress,
		)
	}

	return availableMethods
}

func QuoteHasOnlyStorePickupItems(quote *sales.MagentoQuote) bool {
	items, _ := quote.GetItems()
	for _, item := range items {
		info, _ := stock.GetProductStock(item.ProductID, true)
		if info != nil {
			status := stock.GetZeronSiteStatus(info)
			if status.IsOnlyAvailableForStorePickup() {
				return true
			}
		}
	}

	return false
}

func getTotals(quote *sales.MagentoQuote) []*model.CartTotal {
	totals := make([]*model.CartTotal, 0)
	if quote == nil {
		return totals
	}

	totals = append(totals, &model.CartTotal{
		Code:  model.CartTotalCodeSubTotal,
		Label: "Обща сума",
		Amount: &model.Price{
			Value:    quote.Subtotal.Float64,
			Currency: quote.QuoteCurrencyCode,
		},
		Order: 0,
	})

	address := quote.GetShippingAddress()
	if address != nil {
		if address.DiscountAmount < 0 {
			totals = append(totals, &model.CartTotal{
				Code: model.CartTotalCodeDiscountTotal,
				Label: core_utils.IF(
					address.DiscountDescription.String != "",
					address.DiscountDescription.String,
					"Промо",
				),
				Amount: &model.Price{
					Value:    address.DiscountAmount,
					Currency: quote.QuoteCurrencyCode,
				},
				Order: 10,
			})
		}

		if address.PraktisShippingDiscountAmount != 0 {
			totals = append(totals, &model.CartTotal{
				Code:  model.CartTotalCodeDiscountTotal,
				Label: "Praktis Отстъпка от доставката",
				Amount: &model.Price{
					Value:    address.PraktisShippingDiscountAmount,
					Currency: quote.QuoteCurrencyCode,
				},
				Order: 20,
			})
		}

		if address.CashOnDeliveryTaxAmount > 0 {
			totals = append(totals, &model.CartTotal{
				Code:  model.CartTotalCodeOther,
				Label: "Такса наложен платеж",
				Amount: &model.Price{
					Value:    address.CashOnDeliveryTaxAmount,
					Currency: quote.QuoteCurrencyCode,
				},
				Order: 25,
			})
		}

		if address.ShippingMethod.String != "" {
			totals = append(totals, &model.CartTotal{
				Code: model.CartTotalCodeShippingTotal,
				Label: core_utils.IF(
					address.ShippingDescription.String != "",
					address.ShippingDescription.String,
					"Доставка",
				),
				Amount: &model.Price{
					Value:    address.ShippingAmount,
					Currency: quote.QuoteCurrencyCode,
				},
				Order: 30,
			})
		}

	}

	totals = append(totals, &model.CartTotal{
		Code:  model.CartTotalCodeGrantTotal,
		Label: "Крайна сума",
		Amount: &model.Price{
			Value:    quote.GrandTotal.Float64,
			Currency: quote.QuoteCurrencyCode,
		},
		Order: 99999,
	})

	return totals
}

func GetQuoteCustomerData(quote *sales.MagentoQuote) *model.OrderCustomer {
	val := &model.OrderCustomer{
		FirstName: "",
		LastName:  "",
		Email:     "",
		Phone:     "",
		Invoice:   nil,
	}

	if quote == nil {
		return val
	}

	billingAddress := quote.GetBillingAddress()
	if billingAddress == nil {
		return val
	} else {
		val.FirstName = billingAddress.Firstname.String
		val.LastName = billingAddress.Lastname.String
		val.Email = billingAddress.Email.String
		val.Phone = billingAddress.Telephone.String

		if billingAddress.Invoice.String == "1" {
			val.Invoice = &model.CustomerInvoice{
				Type:       "",
				City:       "",
				Address:    "",
				Company:    nil,
				Individual: nil,
			}
			switch billingAddress.InvoiceType.String {
			case string(model.InvoiceTypeCompany):
				val.Invoice.Type = model.InvoiceTypeCompany
				val.Invoice.City = billingAddress.InvoiceCompanyCity.String
				val.Invoice.Address = billingAddress.InvoiceCompanyAddress.String
				val.Invoice.Company = &model.CompanyInvoice{
					Name: billingAddress.InvoiceCompanyName.String,
					Mol:  billingAddress.InvoiceCompanyMol.String,
					Eik:  billingAddress.InvoiceCompanyBulstat.String,
					Vat:  billingAddress.InvoiceCompanyVat.String,
				}
			case string(model.InvoiceTypePersonal):
				val.Invoice.Type = model.InvoiceTypePersonal
				val.Invoice.City = billingAddress.InvoicePersonalCity.String
				val.Invoice.Address = billingAddress.InvoicePersonalAddress.String
				val.Invoice.Individual = &model.IndividualInvoice{
					Name: billingAddress.InvoicePersonalName.String,
					Egn:  billingAddress.InvoicePersonalPin.String,
					Vat:  billingAddress.InvoicePersonalVat.String,
				}
			default:
				core_utils.Debug("unknown invoice type %s", billingAddress.InvoiceType.String)
			}
		}
	}

	return val
}
