package cart_utils

import (
	"context"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
)

func RecalculateTotalsAndRefreshQuote(
	ctx context.Context, quote *sales.MagentoQuote,
) (*sales.MagentoQuote, error) {
	connector, err := praktis.GetNewCartConnector()
	if err != nil {
		return nil, err
	}

	var recalculated bool
	recalculated, err = connector.RecalculateTotals(quote.GetApiID())
	if err != nil {
		return nil, err
	}

	if recalculated {
		dataLoader := request_context.GetDataLoader(ctx)
		dataLoader.SetQuote(&sales.MagentoQuote{
			EntityID: quote.EntityID,
			CartID:   quote.CartID,
		})
		quote, err = dataLoader.ReloadCart()
		if err != nil {
			return nil, err
		}
	}

	return quote, nil
}
