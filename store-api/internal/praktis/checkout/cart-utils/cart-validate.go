package cart_utils

import (
	"errors"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/customer"
	"regexp"
)

func ValidateCheckoutCustomerInfo(data model.ClientInput) error {
	var err error
	if len(data.FirstName) < 3 || len(data.FirstName) > 250 {
		return errors.New("името трябва да е между 3 и 250 символа")
	} else if len(data.LastName) < 3 || len(data.LastName) > 250 {
		return errors.New("фамилията трябва да е между 3 и 250 символа")
	}

	if err = customer.ValidateEmail(data.Email); err != nil {
		return err
	}

	if data.RegisterOnOrder && customer.CheckEmailIsFree(data.Email) == false {
		return errors.New("потребител с този имейл вече съществува")
	}

	if err = customer.ValidatePhone(data.Phone); err != nil {
		return err
	}

	if data.RegisterOnOrder {
		if len(data.Password) < 6 || len(data.Password) > 250 {
			return errors.New("паролата трябва да е поне 6 символа")
		}
	}

	invoice := data.Invoice
	if invoice != nil {
		if len(invoice.City) < 3 || len(invoice.City) > 250 {
			return errors.New("градът трябва да е между 3 и 250 символа")
		} else if len(invoice.Address) < 3 || len(invoice.Address) > 250 {
			return errors.New("адресът трябва да е между 3 и 250 символа")
		}

		if invoice.Type == model.InvoiceTypeCompany {
			if invoice.Company == nil {
				return errors.New("липсва информация за фирмата")
			} else if len(invoice.Company.Name) < 3 || len(invoice.Company.Name) > 250 {
				return errors.New("липсва информация за име на фирмата")
			} else if len(invoice.Company.Mol) < 3 || len(invoice.Company.Mol) > 250 {
				return errors.New("липсва информация за МОЛ на фирмата")
			} else if err = validateEik(invoice.Company.Eik); err != nil {
				return err
			} else if err = validateVat(invoice.Company.Vat); err != nil {
				return err
			}
		} else if data.Invoice.Type == model.InvoiceTypePersonal {
			if invoice.Individual == nil {
				return errors.New("липсва информация за физическо лице")
			} else if len(invoice.Individual.Name) < 3 || len(invoice.Individual.Name) > 250 {
				return errors.New("липсва информация за име на физическото лице")
			} else if err = validateEGN(invoice.Individual.Egn); err != nil {
				return err
			} else if err = validateVat(invoice.Individual.Vat); err != nil {
				return err
			}
		} else {
			return errors.New("неизвестен тип на фактура")
		}
	}

	return nil
}

func validateEGN(egn string) error {
	matched, _ := regexp.MatchString(`^[0-9]{10}$`, egn)
	if !matched {
		return errors.New("невалидно ЕГН")
	}

	return nil
}

func validateEik(eik string) error {
	if len(eik) < 6 {
		return errors.New("ЕИК трябва да е поне 6 символа")
	}

	return nil
}

func validateVat(vat string) error {
	if vat != "" && len(vat) < 6 {
		return errors.New("невалиден ВАТ номер")
	}

	return nil
}
