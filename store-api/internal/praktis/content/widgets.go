package content

import (
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"regexp"
)

func ParseCategoryWidgets(cat *mage_entity.CategoryEntity) ([]model.CategoryWidget, error) {
	var previews []model.CategoryWidget
	raw := cat.GetAttributeValue("widgets")
	if raw == "" {
		return previews, nil
	}

	widgetRegexp := regexp.MustCompile(`\{\{widget\s+([^\}]+)\}\}`)
	widgetMatches := widgetRegexp.FindAllString(raw, -1)

	propertyRegexp := regexp.MustCompile(`(\w+)="([^"]+)"`)
	for _, widget := range widgetMatches {
		properties := propertyRegexp.FindAllStringSubmatch(widget, -1)

		if isCategoryPreviewWidget(properties) {
			preview := getCategoryPreviewWidget(cat, properties)
			if preview.Title != "" {
				previews = append(previews, preview)
			} else {
				core_utils.Warning(
					"widget: " + widget + " is not a proper category",
				)
				continue
			}
		} else {
			core_utils.Warning(
				"widget: " + widget + " is unknown widget type",
			)
			continue
		}
	}

	return previews, nil
}

func MustParseCategoryWidgets(cat *mage_entity.CategoryEntity) []model.CategoryWidget {
	widgets, err := ParseCategoryWidgets(cat)
	if err != nil {
		core_utils.ErrorWarning(err)
	}

	return widgets
}

const CategoryPreviewWidgetTemplate = "stenik/widgetbanner/categoryBox.phtml"

func isCategoryPreviewWidget(properties [][]string) bool {
	for _, property := range properties {
		if len(property) == 3 {
			key := property[1]
			value := property[2]

			if key == "template" && value == CategoryPreviewWidgetTemplate {
				return true
			}
		}
	}

	return false
}

func getCategoryPreviewWidget(cat *mage_entity.CategoryEntity, properties [][]string) model.CategoryLinkWidget {
	widget := model.CategoryLinkWidget{}
	for _, property := range properties {
		if len(property) == 3 {
			key := property[1]
			value := property[2]
			switch key {
			case "title":
				widget.Title = value
			case "image_src":
				widget.Image = &model.Image{
					Src: value,
				}
			case "url":
				widget.URL = &model.Link{
					Href: value,
				}
			}
		}
	}

	return widget
}
