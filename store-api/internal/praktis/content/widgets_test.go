package content

import (
	"fmt"
	"praktis.bg/store-api/graphql/model"
	"strings"
	"testing"
)

/**
<p>
{{widget type="test" title="11" image_src="img-src" url="test1" template="path/to/template.phtml"}}
{{widget type="test" title="22" image_src="img-src" url="test2" template="path/to/template.phtml"}}
</p>
*/

func Test_WidgetParser(t *testing.T) {
	widgets, err := ParseCategoryWidgets(fmt.Sprintf(`
<p>
{{widget type="test" title="11" image_src="img-src" url="test1" template="%s"}}
{{widget type="test" title="22" image_src="img-src" url="test2" template="%s"}}
</p>
`, CategoryPreviewWidgetTemplate, CategoryPreviewWidgetTemplate))

	if err != nil {
		t.<PERSON>rror(err)
	}

	if len(widgets) != 2 {
		t.Error("Expected 2 widgets, got", len(widgets))
	}

	if _, ok := widgets[0].(model.CategoryPreviewWidget); !ok {
		t.Error("Expected 1 CategoryPreviewWidget, got", fmt.Sprintf("%T", widgets[0]))
	}

	if _, ok := widgets[1].(model.CategoryPreviewWidget); !ok {
		t.Error("Expected 2 CategoryPreviewWidget, got", fmt.Sprintf("%T", widgets[0]))
	}
}

func Test_RealCategoryWidgets(t *testing.T) {
	// widgets from the live garde category
	widgetsRaw := `
<p>{{widget type="stenik_widgetbanner/widget_template" title="Градинска техника и оборудване" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/gradinski_mashini.jpg" url="gradina/gradinska-tehnika-i-oburudvane" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Градински ръчни инструменти" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/gradinski_rachni_instrumenti.jpg" url="gradina/gradinski-rachni-instrumenti" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Басейни и надуваеми забавления" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/baseini.jpg" url="gradina/baseyni-i-naduvaemi-zabavleniya" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Градински мебели и принадлежности" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/gradinski_mebeli.jpg" url="gradina/gradinski-mebeli-i-prinadlezhnosti" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Градински люлки" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/gradinski_liulki.jpg" url="gradina/gradinski-lyulki" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Градински шатри и павилиони" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/gradinski_shatri.jpg" url="gradina/gradinski-shatri-i-pavilioni" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Градински чадъри, навеси и тенти" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/gradinski_chadari.jpg" url="gradina/gradinski-chadari-navesi-i-tenti" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Барбекю" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/barbekiuta.jpg" url="gradina/barbekyuta" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Външни настилки" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/vanchni_nastilki.jpg" url="gradina/izkustvena-treva-283" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Осветление и декорация в градината" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/gradinsko_osvetlenie.jpg" url="gradina/osvetlenie-i-dekoratsiya-v-gradinata" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Напояване и поливни системи" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/napoiavane_polivni_sistemi.jpg" url="gradina/napoyavane-i-polivni-sistemi" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Оранжерии,компостери и аксесоари" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/oranjerii.jpg" url="gradina/oranzherii-komposteri-i-aksesoari" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Мрежи, найлони и огради" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/mreji_nailoni.jpg" url="gradina/mrezhi-nayloni-i-ogradi" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Растения,семена и торове" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/rastenia_semena_torove.jpg" url="gradina/rasteniya-semena-i-torove" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Работно облекло" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/rabotni_obuvki.jpg" url="mashini-i-instrumenti/predpazni-sredstva" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Винарство и консервиране" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/vinarstvo.jpg" url="gradina/vinarstvo-i-konservirane" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Къмпинг" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/kamping.jpg" url="gradina/kamping" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Грижи за домашни любимци" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/domashni_liubimci.jpg" url="gradina/grizhi-za-domashni-lyubimtsi" template="stenik/widgetbanner/categoryBox.phtml"}}{{widget type="stenik_widgetbanner/widget_template" title="Контрол над вредители" image_src="https://praktis.bg/media/wysiwyg/kategories/gradina/kontrol_vrediteli.jpg" url="gradina/kontrol-nad-vrediteli" template="stenik/widgetbanner/categoryBox.phtml"}}</p>
`

	widgets, err := ParseCategoryWidgets(widgetsRaw)
	if err != nil {
		t.Error(err)
	}

	if len(widgets) != strings.Count(widgetsRaw, "{widget") {
		t.Fatal("Expected", strings.Count(widgetsRaw, "{widget"), "widgets, got", len(widgets))
	}
}
