package econt

import (
	"gorm.io/gorm"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"time"
)

type EcontCity struct {
	db        *gorm.DB  `gorm:"-"`
	CityID    int64     `gorm:"column:city_id;primaryKey;autoIncrement"`
	PostCode  string    `gorm:"column:post_code"`
	Type      string    `gorm:"column:type"`
	Name      string    `gorm:"column:name"`
	NameEn    string    `gorm:"column:name_en"`
	ZoneID    int       `gorm:"column:zone_id"`
	CountryID int       `gorm:"column:country_id"`
	OfficeID  int       `gorm:"column:office_id"`
	CreatedAt time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt time.Time `gorm:"column:updated_at;autoUpdateTime"`

	Offices []*EcontOffice `gorm:"foreignKey:CityID;references:CityID"`
}

// TableName overrides the table name
func (e *EcontCity) TableName() string {
	return "extensa_econt_city"
}

func (e *EcontCity) DB() *gorm.DB {
	if e.db == nil {
		e.db = praktis.GetDbClient()
	}

	return e.db
}

func (e *EcontCity) ToModel() *model.EcontCity {
	return &model.EcontCity{
		ID: e.CityID,
		Country: &model.EcontCountry{
			ID:     1033,
			Code2:  "BG",
			Code3:  "BGR",
			Name:   "България",
			NameEn: "Bulgaria",
			IsEu:   true,
		},
		Type:         e.Type,
		Name:         e.Name,
		NameEn:       e.NameEn,
		PostCode:     e.PostCode,
		RegionName:   "",
		RegionNameEn: "",
		RegionCode:   "",
	}
}

func GetEcontCities(country string) ([]*EcontCity, error) {
	var cities []*EcontCity
	err := praktis.GetDbClient().
		Table("extensa_econt_city c").
		Select("c.*").
		Joins("JOIN extensa_econt_country ct ON c.country_id = ct.country_id").
		Where("ct.name_en = \"bulgaria\"").Find(&cities).Error
	if err != nil {
		return nil, err
	}

	return cities, nil
}
