package econt

import (
	"fmt"
	"gorm.io/gorm"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"strings"
	"time"
)

type OfficeCollection []*EcontOffice

type EcontOffice struct {
	db *gorm.DB `gorm:"-"`

	OfficeID          int64     `gorm:"column:office_id;primaryKey;autoIncrement"`
	Name              string    `gorm:"column:name"`
	NameEn            string    `gorm:"column:name_en"`
	OfficeCode        string    `gorm:"column:office_code"`
	Address           string    `gorm:"column:address"`
	AddressEn         string    `gorm:"column:address_en"`
	Phone             string    `gorm:"column:phone"`
	WorkBegin         time.Time `gorm:"column:work_begin"`
	WorkEnd           time.Time `gorm:"column:work_end"`
	WorkBeginSaturday time.Time `gorm:"column:work_begin_saturday"`
	WorkEndSaturday   time.Time `gorm:"column:work_end_saturday"`
	TimePriority      time.Time `gorm:"column:time_priority"`

	CityID int        `gorm:"column:city_id"`
	City   *EcontCity `gorm:"foreignKey:CityID;references:CityID"`

	IsMachine   int16     `gorm:"column:is_machine"`
	Coordinates string    `gorm:"column:coordinates"`
	CreatedAt   time.Time `gorm:"column:created_at;autoCreateTime"`
	UpdatedAt   time.Time `gorm:"column:updated_at;autoUpdateTime"`
}

func (e *EcontOffice) DB() *gorm.DB {
	if e.db == nil {
		e.db = praktis.GetDbClient()
	}

	return e.db
}

func (e *EcontOffice) SetDB(db *gorm.DB) *EcontOffice {
	e.db = db
	return e
}

func (e *EcontOffice) TableName() string {
	return "extensa_econt_office"
}

func (e *EcontOffice) Load(id string) error {
	return e.DB().
		Model(&EcontOffice{}).
		Preload("City").
		Where("office_code = ?", id).Find(e).Error
}

func (e *EcontOffice) ToModel() *model.EcontOffice {
	cordinates := strings.Replace(e.Coordinates, " ", "", -1)
	if cordinates == "" {
		cordinates = "0,0"
	}

	latitude, longitude := 0.0, 0.0
	_, err := fmt.Sscanf(cordinates, "%f,%f", &latitude, &longitude)
	if err != nil {
		latitude, longitude = 0.0, 0.0
	}

	var city *model.EcontCity
	if e.City != nil {
		city = e.City.ToModel()
	}

	return &model.EcontOffice{
		ID:     e.OfficeID,
		Code:   e.OfficeCode,
		Name:   e.Name,
		NameEn: e.NameEn,
		Phones: []string{e.Phone},
		IsAps:  e.IsMachine == 1,
		IsMps:  false,
		Adderess: &model.EcontOfficeAddress{
			City:          city,
			FullAddress:   e.Address,
			FullAddressEn: e.AddressEn,
			Location: &model.EcontOfficeLocation{
				Latitude:  latitude,
				Longitude: longitude,
			},
		},
	}
}

func GetEcontOffices(cityId string) ([]*EcontOffice, error) {
	var offices []*EcontOffice
	err := praktis.GetDbClient().
		Table("extensa_econt_office o").
		Model(&EcontOffice{}).
		Preload("City").
		Select("o.*").
		Joins("JOIN extensa_econt_city c ON o.city_id = c.city_id").
		Where("c.city_id = ?", cityId).
		Find(&offices).Error
	return offices, err
}
