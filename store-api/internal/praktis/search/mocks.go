package search

import (
	"github.com/siper92/api-base/cache"
	"time"
)

type MockCacheProvider struct {
	data map[string]interface{}
}

func (m *MockCacheProvider) Exists(key string) (bool, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) IsHSET(key string) bool {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) Delete(key string) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) TTL(key string) (time.Duration, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) UpdateTTl(key string, ttl time.Duration) (bool, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) SaveAsJSON(key string, val any, ttl time.Duration) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) LoadJSON(key string, result any) (any, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) SaveObj(o cache.CacheableObject) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) LoadObj(o cache.CacheableObject) error {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) GetMapKeys(key string, fields ...string) (map[string]string, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) GetMapValue(key string, field string) (string, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) MapKeyExists(key string, mapKey string) (bool, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) SetMapValue(key string, field string, value string) (bool, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) GetSet(key string) ([]string, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) AddSetMember(key string, members ...string) (bool, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) RemoveSetMember(key string, members ...string) (bool, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) InSet(key string, member string) (bool, error) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) SetPrefix(prefix string) {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) GetPrefix() string {
	//TODO implement me
	panic("implement me")
}

func (m *MockCacheProvider) Test() error {
	//TODO implement me
	panic("implement me")
}

func NewMockCacheProvider() *MockCacheProvider {
	return &MockCacheProvider{
		data: make(map[string]interface{}),
	}
}

func (m *MockCacheProvider) Get(key string) (string, error) {
	if val, ok := m.data[key]; ok {
		return val.(string), nil
	}
	return "", nil
}

func (m *MockCacheProvider) MustExists(key string) bool {
	_, exists := m.data[key]
	return exists
}

func (m *MockCacheProvider) GetMap(key string) (map[string]string, error) {
	if val, ok := m.data[key]; ok {
		return val.(map[string]string), nil
	}
	return make(map[string]string), nil
}

func (m *MockCacheProvider) Save(key string, val interface{}, ttl time.Duration) error {
	m.data[key] = val
	return nil
}

type MockDbClient struct {
	data map[string]interface{}
}

func (m *MockDbClient) Table(name string) interface{} {
	return m.data[name]
}

func SetMockDbClient(mock *MockDbClient) {
}

var mockCacheClient *MockCacheProvider

func SetMockCacheClient(mock *MockCacheProvider) {
	mockCacheClient = mock
}

// Override the GetCacheClient function from praktis package
func GetCacheClient() cache.CacheProvider {
	if mockCacheClient != nil {
		return mockCacheClient
	}
	return nil
}
