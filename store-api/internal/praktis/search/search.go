package search

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"math"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/catalog"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	"praktis.bg/store-api/internal/praktis/router"
	"praktis.bg/store-api/packages/magento-core/search"
	"praktis.bg/store-api/packages/magento-core/types"
	"sort"
	"strconv"
)

func NewSearchPage(params router.RouteParams) *model.SearchPage {
	return &model.SearchPage{
		Status: &model.PageStatus{
			StatusCode: 200,
		},
		State: &model.CatalogState{
			Filters: &model.Filters{
				Available: make([]*model.AvailableFilter, 0),
				Applied:   params.GetAppliedFilters(),
			},
			Pager: &model.Pager{
				Page:       params.GetPage(),
				TotalPages: 0,
				PageSize:   params.GetPageSize(),
				TotalItems: 0,
			},
			AvailableSorts: []*model.SortField{
				{
					Value: "relevance",
					Label: "Позиция",
				},
				{
					Value: "price",
					Label: "Цена",
				},
				{
					Value: "recently_added",
					Label: "Последно добавени",
				},
			},
			Sort: params.GetSort(),
		},
		Title: "Няма намерени резултати",
		Data: &model.SearchResults{
			PopularTerms: nil,
			Categories:   nil,
			TotalItems:   0,
			Products:     nil,
			Block:        nil,
		},
	}
}

func ToSearchFilters(filters []*model.AppliedFilter) map[string][]string {
	searchFilters := make(map[string][]string)
	for _, filter := range filters {
		if _, ok := searchFilters[filter.AttributeCode]; !ok {
			searchFilters[filter.AttributeCode] = []string{}
		}

		if filter.AttributeCode == "category_ids" {
			searchFilters[filter.AttributeCode] = append(searchFilters[filter.AttributeCode], filter.Value)
		} else if filter.AttributeCode == "price" {
			searchFilters[filter.AttributeCode] = []string{filter.Value}
		} else {
			searchFilters[filter.AttributeCode] = append(searchFilters[filter.AttributeCode], filter.Value)
		}
	}

	return searchFilters
}

func ToSearchSortOrder(sort string) string {
	searchSortBy := ""
	switch sort {
	case "price":
		searchSortBy = "price"
	//case "best_discount":
	//	searchParams.SortBy = "discount"
	case "recently_added":
		searchSortBy = "created"
	default:
		searchSortBy = "relevance"
	}

	return searchSortBy
}

func SearchResultsToAvailableFilters(searchResults search.SearchApiResult) []*model.AvailableFilter {
	var availableFilters []*model.AvailableFilter
	for _, filter := range searchResults.Filters {
		if filter.AttributeCode == "category_ids" {
			core_utils.Debug("category_ids filter is not supported")
			continue
		} else if filter.AttributeCode == "price" {
			core_utils.Debug("category_ids filter is not supported")
			continue
		}

		availableFilter := &model.AvailableFilter{
			Label:         filter.Title,
			AttributeCode: filter.AttributeCode,
			Options:       make([]*model.AttributeOption, 0),
			Type:          model.FilterRenderTypeList,
			RequestVar:    filter.AttributeCode,
		}

		if filter.AttributeCode == "available" {
			activeStores := product.GetActiveStores()

			storeOrderMap := make(map[string]int)
			for _, store := range activeStores {
				storeOrderMap[store.WarehouseCode] = store.DisplayOrder
			}

			for _, bucket := range filter.Buckets {
				order := 100

				if storeID, err := strconv.Atoi(bucket.Value); err == nil {
					if storeID >= catalog.WarehouseSofia && storeID <= catalog.WarehouseMega {
						for warehouseCode, displayOrder := range storeOrderMap {
							if warehouseCode == fmt.Sprintf("%d", storeID) {
								// two predefined elements
								order = displayOrder + 2
								break
							}
						}
					} else if storeID <= catalog.ByRequest {
						order = storeID
					}
				}

				availableFilter.Options = append(availableFilter.Options, &model.AttributeOption{
					Value: bucket.Value,
					Label: bucket.Value,
					Order: order,
				})
			}
		} else {
			for _, bucket := range filter.Buckets {
				availableFilter.Options = append(availableFilter.Options, &model.AttributeOption{
					Value: bucket.Value,
					Label: bucket.Value,
					Order: 0,
				})
			}
		}

		if len(availableFilter.Options) > 0 {
			sort.Slice(availableFilter.Options, func(i, j int) bool {
				return availableFilter.Options[i].Order < availableFilter.Options[j].Order
			})

			availableFilters = append(availableFilters, availableFilter)
		}
	}

	return availableFilters
}

func SearchResultsGetProducts(searchResults search.SearchApiResult) []model.Product {
	var products []model.Product
	ids := make([]int64, 0)
	for _, item := range searchResults.Items {
		id := types.ToInt64(item.ProductId)
		if id > 0 {
			ids = append(ids, id)
		}
	}
	productEntity, err := product.NewPraktisProductRepository(nil).GetIDs(ids, []string{})
	if err != nil {
		core_utils.ErrorWarning(err)
		return nil
	}

	for _, p := range productEntity {
		products = append(products, p.ToModel())
	}

	return products
}

func SearchResultsToPager(searchResults search.SearchApiResult, params router.RouteParams) *model.Pager {
	totalItems := searchResults.TotalItems
	pageSize := params.GetPageSize()
	if pageSize == 0 {
		pageSize = 1
	}
	totalPages := int(math.Ceil(float64(totalItems) / float64(pageSize)))

	return &model.Pager{
		Page:       params.GetPage(),
		TotalItems: searchResults.TotalItems,
		TotalPages: totalPages,
	}
}
