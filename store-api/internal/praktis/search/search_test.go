package search

import (
	"github.com/stretchr/testify/assert"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/router"
	"praktis.bg/store-api/packages/magento-core/search"
	"testing"
	"time"
)

func TestNewSearchPage(t *testing.T) {
	mockCache := NewMockCacheProvider()
	mockCache.Save("available-filters", map[string]string{
		"price":  "decimal",
		"name":   "varchar",
		"status": "int",
	}, time.Hour)

	tests := []struct {
		name     string
		params   []*model.QueryParam
		expected struct {
			statusCode int
			title      string
		}
	}{
		{
			name:   "Empty params",
			params: []*model.QueryParam{},
			expected: struct {
				statusCode int
				title      string
			}{
				statusCode: 200,
				title:      "Няма намерени резултати",
			},
		},
		{
			name: "With page param",
			params: []*model.QueryParam{
				{Name: "page", Value: "1"},
			},
			expected: struct {
				statusCode int
				title      string
			}{
				statusCode: 200,
				title:      "Няма намерени резултати",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			params := router.RouteParams(tt.params)
			page := NewSearchPage(params)
			assert.NotNil(t, page)
			assert.NotNil(t, page.Status)
			assert.Equal(t, tt.expected.statusCode, page.Status.StatusCode)
			assert.Equal(t, tt.expected.title, page.Title)
		})
	}
}

func TestToSearchFilters(t *testing.T) {
	tests := []struct {
		name     string
		filters  []*model.AppliedFilter
		expected map[string][]string
	}{
		{
			name:     "Empty filters",
			filters:  []*model.AppliedFilter{},
			expected: map[string][]string{},
		},
		{
			name: "Category filter",
			filters: []*model.AppliedFilter{
				{AttributeCode: "category_ids", Value: "123"},
			},
			expected: map[string][]string{
				"category_ids": {"123"},
			},
		},
		{
			name: "Price filter",
			filters: []*model.AppliedFilter{
				{AttributeCode: "price", Value: "0-100"},
			},
			expected: map[string][]string{
				"price": {"0-100"},
			},
		},
		{
			name: "Multiple filters",
			filters: []*model.AppliedFilter{
				{AttributeCode: "category_ids", Value: "123"},
				{AttributeCode: "price", Value: "0-100"},
				{AttributeCode: "color", Value: "red"},
			},
			expected: map[string][]string{
				"category_ids": {"123"},
				"price":        {"0-100"},
				"color":        {"red"},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToSearchFilters(tt.filters)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestToSearchSortOrder(t *testing.T) {
	tests := []struct {
		name     string
		sort     string
		expected string
	}{
		{
			name:     "Price sort",
			sort:     "price",
			expected: "price",
		},
		{
			name:     "Recently added sort",
			sort:     "recently_added",
			expected: "created",
		},
		{
			name:     "Default sort",
			sort:     "",
			expected: "relevance",
		},
		{
			name:     "Unknown sort",
			sort:     "unknown",
			expected: "relevance",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ToSearchSortOrder(tt.sort)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestSearchResultsToAvailableFilters(t *testing.T) {
	tests := []struct {
		name     string
		results  search.SearchApiResult
		expected int
	}{
		{
			name: "Empty results",
			results: search.SearchApiResult{
				Filters: []search.SearchFilter{},
			},
			expected: 0,
		},
		{
			name: "With available filter",
			results: search.SearchApiResult{
				Filters: []search.SearchFilter{
					{
						Title:         "Color",
						AttributeCode: "color",
						Buckets: []search.SearchFilterOption{
							{Value: "red", Count: 5},
							{Value: "blue", Count: 3},
						},
					},
				},
			},
			expected: 1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SearchResultsToAvailableFilters(tt.results)
			assert.Equal(t, tt.expected, len(result))
		})
	}
}

func TestSearchResultsToPager(t *testing.T) {
	tests := []struct {
		name         string
		searchResult search.SearchApiResult
		params       router.RouteParams
		expected     struct {
			page       int
			totalItems int
			totalPages int
		}
	}{
		{
			name: "Empty results",
			searchResult: search.SearchApiResult{
				TotalItems: 0,
			},
			params: router.RouteParams([]*model.QueryParam{}),
			expected: struct {
				page       int
				totalItems int
				totalPages int
			}{
				page:       1,
				totalItems: 0,
				totalPages: 0,
			},
		},
		{
			name: "Results with pagination",
			searchResult: search.SearchApiResult{
				TotalItems: 100,
			},
			params: router.RouteParams([]*model.QueryParam{
				{Name: "p", Value: "2"},
				{Name: "page_size", Value: "20"},
			}),
			expected: struct {
				page       int
				totalItems int
				totalPages int
			}{
				page:       2,
				totalItems: 100,
				totalPages: 5,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := SearchResultsToPager(tt.searchResult, tt.params)
			assert.Equal(t, tt.expected.page, result.Page)
			assert.Equal(t, tt.expected.totalItems, result.TotalItems)
			assert.Equal(t, tt.expected.totalPages, result.TotalPages)
		})
	}
}
