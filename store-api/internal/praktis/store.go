package praktis

import (
	"errors"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"path/filepath"
	config "praktis.bg/store-api/internal/config"
)

func GetCertificateFullPath(fileName string) (string, error) {
	rootPath := config.GetConfig().Api.CertificatesRootPath
	if fileName == "" {
		return "", errors.New("file name is empty")
	}

	absRootPath, err := filepath.Abs(rootPath)
	if err != nil {
		return "", err
	} else if core_utils.DirectoryExists(absRootPath) == false {
		return "", fmt.Errorf("directory does not exist: %s", absRootPath)
	}

	certPath := filepath.Join(absRootPath, fileName)
	if core_utils.FileExists(certPath) == false {
		return "", fmt.Errorf("file does not exist: %s", certPath)
	}

	return certPath, nil
}
