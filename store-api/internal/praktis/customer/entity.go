package customer

import (
	"github.com/siper92/api-base/cache"
	"praktis.bg/store-api/graphql/model"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"sync"
)

var _ mage_types.StoreEntity[*MagentoCustomer] = (*MagentoCustomer)(nil)

const StatusSubscribed = 1
const StatusNotActive = 2
const StatusUnsubscribed = 3
const StatusUnconfirmed = 4

type MagentoCustomer struct {
	repo     *MagentoCustomerRepository `gorm:"-"`
	token    *CustomerToken             `gorm:"-"`
	lock     sync.Mutex                 `gorm:"-"`
	cacheKey string                     `gorm:"-"`

	EntityID int64 `gorm:"primaryKey;column:entity_id"`
	GroupID  int64 `gorm:"column:group_id"`
	IsActive bool  `gorm:"column:is_active"`

	Email     string `gorm:"column:email"`
	FirstName string `gorm:"column:firstname"`
	LastName  string `gorm:"column:lastname"`
	Telephone string `gorm:"column:telephone"`

	IsSubscribed int `gorm:"column:subscriber_status"`

	WishlistID int64            `gorm:"column:wishlist_id"`
	Wishlist   *MagentoWishlist `gorm:"-"`

	Invoice model.CustomerInvoice `gorm:"-"`

	DefaultBillingID  int64                   `gorm:"-"`
	DefaultBilling    *MagentoCustomerAddress `gorm:"-"`
	DefaultShippingID int64                   `gorm:"-"`
	DefaultShipping   *MagentoCustomerAddress `gorm:"-"`

	loadedAddresses bool
	Addresses       []*MagentoCustomerAddress `gorm:"-"`
	loadedOrders    bool
	Orders          []*sales.MagentoOrder `gorm:"-"`
}

func (c *MagentoCustomer) Cache() cache.CacheProvider {
	return magento_core.GetStoreClient().GetCustomerCacheClient()
}

func (c *MagentoCustomer) SetRepository(r *MagentoCustomerRepository) *MagentoCustomer {
	c.repo = r
	return c
}

func (c *MagentoCustomer) Repository() *MagentoCustomerRepository {
	if c.repo == nil {
		c.repo = NewCustomerRepository(nil)
	}

	return c.repo
}

func (c *MagentoCustomer) TableName() string {
	return "customer_entity"
}

func (c *MagentoCustomer) GetID() int64 {
	return c.EntityID
}

func (c *MagentoCustomer) Validate() error {
	return nil
}

func (c *MagentoCustomer) IsValid() bool {
	return c.Validate() == nil
}

func (c *MagentoCustomer) New() *MagentoCustomer {
	return &MagentoCustomer{}
}

func (c *MagentoCustomer) NewSlice() []*MagentoCustomer {
	return []*MagentoCustomer{}
}

func (c *MagentoCustomer) Save() (*MagentoCustomer, error) {
	return c.Repository().Save(c)
}

func (c *MagentoCustomer) SetToken(token *CustomerToken) *MagentoCustomer {
	c.token = token
	return c
}
