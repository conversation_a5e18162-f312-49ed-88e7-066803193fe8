package customer

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
)

func CustomerToModel(c *MagentoCustomer) *model.Customer {
	tokenObj := c.GetToken()

	token, err := tokenObj.ToJWTToken()
	core_utils.ErrorWarning(err)

	return &model.Customer{
		ID:                       c.EntityID,
		Token:                    token.String(),
		IsSubscribed:             c.IsSubscribed == StatusSubscribed,
		FirstName:                c.FirstName,
		LastName:                 c.LastName,
		Email:                    c.Email,
		DefaultBillingAddressID:  &c.<PERSON>ing<PERSON>,
		DefaultShippingAddressID: &c.<PERSON>,
		Addresses:                make([]*model.CustomerAddress, 0),
		Orders:                   make([]*model.StoreOrder, 0),
		Wishlist: &model.CustomerWishlist{
			Skus:     make([]string, 0),
			Products: make([]model.Product, 0),
		},
		Social: &model.SocialLogins{
			Google: &model.SocialAccountInfo{
				ID:            "",
				Email:         "",
				VerifiedEmail: false,
				Name:          "",
				PictureURL:    "",
			},
			Facebook: &model.SocialAccountInfo{
				ID:            "",
				Email:         "",
				VerifiedEmail: false,
				Name:          "",
				PictureURL:    "",
			},
		},
	}
}

func CustomerShippingToAddress(c *MagentoCustomerAddress) (*model.CustomerAddress, error) {
	if c == nil {
		return &model.CustomerAddress{ID: 0}, fmt.Errorf("липсва адрес")
	}

	city, _ := c.GetVal("city")
	cityID, _ := c.GetVal("city_id")
	postCode, _ := c.GetVal("postcode")
	street, _ := c.GetVal("street")

	firstName, _ := c.GetVal("firstname")
	lastName, _ := c.GetVal("lastname")
	phone, _ := c.GetVal("telephone")

	companyName, _ := c.GetVal("company")
	if companyName == "" {
		companyName, _ = c.GetVal("invoice_company_name")
	}

	countryID, _ := c.GetVal("country_id")
	if countryID == "" {
		countryID = "BG"
	}

	return &model.CustomerAddress{
		ID:          c.EntityID,
		FirstName:   firstName,
		LastName:    lastName,
		Phone:       phone,
		CompanyName: &companyName,
		Country:     countryID,
		City:        city,
		CityID:      cityID,
		PostCode:    postCode,
		Street:      street,
	}, nil
}
