package customer

import (
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"strings"
)

var _ mage_types.EntityValueProvider = (*MagentoCustomerAddress)(nil)

func (m *MagentoCustomerAddress) GetVal(key string) (string, error) {
	_key := strings.ToLower(key)
	switch _key {
	case "entity_id", "id", "entityid":
		return strconv.FormatInt(m.EntityID, 10), nil
	case "parent_id", "parentid":
		return strconv.FormatInt(m.ParentID, 10), nil
	case "created_at", "createdat":
		return types.DateToString(&m.CreatedAt), nil
	case "updated_at", "updatedat":
		return types.DateToString(&m.UpdatedAt), nil
	case "is_active", "isactive":
		return strconv.FormatBool(m.IsActive), nil
	default:
		if m.Data != nil {
			if val, ok := m.Data[key]; ok {
				return val, nil
			}
		}
	}

	return "", mage_types.NewFieldError(key)
}

func (m *MagentoCustomerAddress) MustGetVal(key string) string {
	val, _ := m.GetVal(key)
	return val
}

func (m *MagentoCustomerAddress) GetData(keys ...string) (mage_types.EntityData, error) {
	if len(keys) == 0 {
		res := mage_types.EntityData{
			"entity_id":  strconv.FormatInt(m.EntityID, 10),
			"parent_id":  strconv.FormatInt(m.ParentID, 10),
			"created_at": types.DateToString(&m.CreatedAt),
			"updated_at": types.DateToString(&m.UpdatedAt),
			"is_active":  strconv.FormatBool(m.IsActive),
		}

		if m.Data != nil {
			for k, v := range m.Data {
				res[k] = v
			}
		}

		return res, nil
	} else {
		res := make(mage_types.EntityData)
		fieldErrors := mage_types.FieldErrors{}

		for _, key := range keys {
			val, err := m.GetVal(key)
			if err != nil {
				fieldErrors[key] = err
				continue
			}
			res[key] = val
		}

		if len(fieldErrors) > 0 {
			return res, fieldErrors
		}

		return res, nil
	}
}

func (m *MagentoCustomerAddress) SetVal(key string, value interface{}) error {
	switch strings.ToLower(key) {
	case "entity_id", "id", "entityid":
		m.EntityID = types.ToInt64(value)
	case "parent_id", "parentid":
		m.ParentID = types.ToInt64(value)
	case "created_at", "createdat":
		m.CreatedAt = types.ToDateTime(value)
	case "updated_at", "updatedat":
		m.UpdatedAt = types.ToDateTime(value)
	case "is_active", "isactive":
		m.IsActive = types.ToBool(value)
	default:
		if m.Data == nil {
			m.Data = mage_types.EntityData{}
		}
		m.Data[key] = types.ToStr(value)
	}

	return nil
}
