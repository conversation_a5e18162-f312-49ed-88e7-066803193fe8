package customer

import (
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
)

func GetCustomerShippingAddresses(c *MagentoCustomer) ([]*model.CustomerAddress, error) {
	var result []*model.CustomerAddress
	addresses, err := c.GetAddresses()
	if err != nil {
		return result, err
	}

	for _, address := range addresses {
		m, err := CustomerShippingToAddress(address)
		if err != nil {
			core_utils.ErrorWarning(err)
			continue
		}

		result = append(result, m)
	}

	return result, nil
}
