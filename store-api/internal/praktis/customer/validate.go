package customer

import (
	"errors"
	"gorm.io/gorm"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/econt"
	"regexp"
	"strings"
)

func ValidateRegistrationData(data model.CustomerRegistrationData) error {
	if len(data.Firstname) < 2 || len(data.Firstname) > 50 {
		return errors.New("името е невалидно")
	} else if len(data.Lastname) < 2 || len(data.Lastname) > 50 {
		return errors.New("фамилията е невалидна")
	} else if len(data.Email) < 3 || len(data.Email) > 250 {
		return errors.New("имеилът е невалиден")
	} else if CheckEmailIsFree(data.Email) == false {
		return errors.New("имеилът вече е зает")
	} else if len(data.Password) < 6 || len(data.Password) > 60 {
		return errors.New("паролата е невалидна")
	} else if data.Invoice != nil {
		if len(data.Invoice.City) < 2 || len(data.Invoice.City) > 255 {
			return errors.New("липсва градът или е невалиден")
		} else if len(data.Invoice.Address) < 2 || len(data.Invoice.Address) > 255 {
			return errors.New("липсва адресът или е невалиден")
		} else if data.Invoice.Type == model.InvoiceTypeCompany {
			if data.Invoice.Company == nil {
				return errors.New("липсва информация за фирмата")
			} else if len(data.Invoice.Company.Name) < 2 || len(data.Invoice.Company.Name) > 255 {
				return errors.New("име на фирмата е невалидно")
			} else if len(data.Invoice.Company.Mol) < 2 || len(data.Invoice.Company.Mol) > 255 {
				return errors.New("МОЛ е невалиден")
			}
		} else if data.Invoice.Type == model.InvoiceTypePersonal {
			if data.Invoice.Individual == nil {
				return errors.New("липсва информация за физическото лице")
			} else if len(data.Invoice.Individual.Name) < 2 || len(data.Invoice.Individual.Name) > 255 {
				return errors.New("име на физическото лице е невалидно")
			} else if len(data.Invoice.Individual.Egn) < 2 || len(data.Invoice.Individual.Egn) > 255 {
				return errors.New("ЕГН е невалидно")
			}
		}
	}

	return nil
}

func ValidateCustomerAddressInfo(data model.CustomerAddressInput) error {
	if len(data.FirstName) < 2 || len(data.FirstName) > 50 {
		return errors.New("името е невалидно")
	} else if len(data.LastName) < 2 || len(data.LastName) > 50 {
		return errors.New("фамилията е невалидна")
	} else if len(data.City) < 2 || len(data.City) > 50 {
		return errors.New("градът е невалиден")
	}

	var city econt.EcontCity
	err := praktis.GetDbClient().Model(&econt.EcontCity{}).Where("city_id = ?", data.CityID).First(&city).Error
	if err != nil {
		return err
	} else if city.CityID < 1 {
		return errors.New("градът не е валиден, невалидно ID")
	}

	return nil
}

func ValidateEmail(email string) error {
	if len(email) < 3 || len(email) > 250 {
		return errors.New("email must be between 3 and 250 characters")
	}

	// This pattern covers most common email formats
	pattern := `^[a-zA-Z0-9._%+\-]+@[a-zA-Z0-9.\-]+\.[a-zA-Z]{2,}$`

	match, _ := regexp.MatchString(pattern, email)
	if !match {
		return errors.New("invalid email address: " + email)
	}

	return nil
}

func ValidatePhone(phone string) error {
	if len(phone) > 20 {
		return errors.New("phone must be less than 20 characters")
	}

	if strings.HasPrefix(phone, "+") {
		phone = phone[1:]
	}

	match, _ := regexp.MatchString("^[0-9]+$", phone)
	if !match {
		return errors.New("invalid phone number: " + phone)
	}

	return nil
}

func CheckEmailIsFree(email string) bool {
	if email == "" {
		return false
	}

	var customer MagentoCustomer
	result := NewCustomerRepository(nil).DB().Table(customer.TableName()).
		Select("entity_id").
		Where("email = ?", email).
		First(&customer)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return true
		}

		return false
	}

	return customer.EntityID < 1
}
