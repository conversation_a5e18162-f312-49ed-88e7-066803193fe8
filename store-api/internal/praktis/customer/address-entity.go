package customer

import (
	"errors"
	core_utils "github.com/siper92/core-utils"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"time"
)

var NoAddressError = errors.New("no address found")

func (c *MagentoCustomer) GetDefaultBilling() (*MagentoCustomerAddress, error) {
	var err error
	if c.DefaultBilling == nil {
		if c.DefaultBillingID > 0 {
			for _, address := range c.MustGetAddresses() {
				if address.EntityID == c.DefaultBillingID {
					c.DefaultBilling = address
					break
				}
			}
		} else {
			return nil, NoAddressError
		}
	}

	return c.DefaultBilling, err
}

func (c *MagentoCustomer) GetDefaultShipping() (*MagentoCustomerAddress, error) {
	var err error
	if c.DefaultShipping == nil {
		if c.DefaultShippingID > 0 {
			for _, address := range c.MustGetAddresses() {
				if address.EntityID == c.DefaultShippingID {
					c.DefaultShipping = address
					break
				}
			}
		} else {
			return nil, NoAddressError
		}
	}

	return c.DefaultShipping, err
}

func (c *MagentoCustomer) loadOneAddresses() error {
	c.lock.Lock()
	defer c.lock.Unlock()

	if c.loadedAddresses == false {
		var err error
		c.Addresses, err = c.Repository().GetAddress(c.EntityID)
		c.loadedAddresses = true
		return err
	}

	return nil
}

func (c *MagentoCustomer) MustGetAddresses() []*MagentoCustomerAddress {
	err := c.loadOneAddresses()
	core_utils.ErrorWarning(err)

	if len(c.Addresses) == 0 {
		return make([]*MagentoCustomerAddress, 0)
	}

	return c.Addresses
}

func (c *MagentoCustomer) GetAddresses() ([]*MagentoCustomerAddress, error) {
	err := c.loadOneAddresses()
	return c.Addresses, err
}

type MagentoCustomerAddress struct {
	EntityID  int64     `gorm:"primaryKey;column:entity_id"`
	ParentID  int64     `gorm:"column:parent_id"`
	CreatedAt time.Time `gorm:"autoCreateTime;column:created_at"`
	UpdatedAt time.Time `gorm:"autoUpdateTime;column:updated_at"`
	IsActive  bool      `gorm:"column:is_active"`

	Data mage_types.EntityData `gorm:"-"`
}

func (*MagentoCustomerAddress) TableName() string {
	return "customer_address_entity"
}

func (m *MagentoCustomerAddress) GetID() int64 {
	return m.EntityID
}
