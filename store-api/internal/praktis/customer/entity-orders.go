package customer

import "praktis.bg/store-api/packages/magento-core/mage-store/sales"

func (c *MagentoCustomer) GetOrders() ([]*sales.MagentoOrder, error) {
	err := c.loadOrdersOnce()
	if err != nil {
		return nil, err
	}

	return c.Orders, nil
}

func (c *MagentoCustomer) loadOrdersOnce() error {
	c.lock.Lock()
	defer c.lock.Unlock()

	if c.loadedOrders == false {
		c.loadedOrders = true
		err := c.Repository().DB().Model(&sales.MagentoOrder{}).
			Where("customer_id = ?", c.EntityID).
			Find(&c.Orders).Error
		if err != nil {
			return err
		}
	}

	return nil
}
