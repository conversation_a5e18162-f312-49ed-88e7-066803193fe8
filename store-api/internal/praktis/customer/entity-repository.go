package customer

import (
	"gorm.io/gorm"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"sync"
)

var _ mage_types.TransactionAware[*MagentoCustomerRepository] = (*MagentoCustomerRepository)(nil)

type MagentoCustomerRepository struct {
	db          *gorm.DB
	lock        sync.Mutex
	addressLock sync.Mutex
}

func NewCustomerRepository(db *gorm.DB) *MagentoCustomerRepository {
	result := &MagentoCustomerRepository{
		db: db,
	}

	return result
}

func (m *MagentoCustomerRepository) DB() *gorm.DB {
	if m.db == nil {
		m.db = magento_core.GetStoreClient().DbConn()
	}

	return m.db
}

func (m *MagentoCustomerRepository) SetTransaction(tx *gorm.DB) *MagentoCustomerRepository {
	m.db = tx

	return m
}

func (m *MagentoCustomerRepository) Save(entity *MagentoCustomer) (*MagentoCustomer, error) {
	return entity, nil
}

func (m *MagentoCustomerRepository) GetByID(id int64) (*MagentoCustomer, error) {
	m.lock.Lock()
	defer m.lock.Unlock()

	result := &MagentoCustomer{
		EntityID: id,
	}
	err := m.DB().
		Select(types.TableSelects{
			"c.entity_id",
			"c.email",
			"s.subscriber_status",
			"c.website_id",
			"c.store_id",
			"c.group_id",
			"w.wishlist_id as wishlist_id",
		}.String()).
		Table("customer_entity c").
		Joins("left join newsletter_subscriber s on s.subscriber_email = c.email").
		Joins("left join wishlist w on w.customer_id = c.entity_id").
		Where("c.entity_id = ?", id).Scan(result).Error
	if err != nil {
		return nil, err
	}

	tables, err := mage_store.CustomerAttributes.SplitToByTable([]string{
		"default_billing", "default_shipping",
		"firstname", "lastname",
	})
	attrLoader := mage_store.NewAttributeLoader(m.DB(), types.CustomerEntityType)

	for table, ids := range tables {
		var data mage_types.EntityData
		data, err = attrLoader.LoadEntityAttribute(mage_store.LoadEntityAttribute{
			Table:    table,
			EntityId: id,
			AttrIds:  ids,
			StoreID:  mage_store.NoStoreID,
		})

		if val, ok := data["firstname"]; ok {
			result.FirstName = val
		}

		if val, ok := data["lastname"]; ok {
			result.LastName = val
		}

		if val, ok := data["default_billing"]; ok {
			result.DefaultBillingID, _ = strconv.ParseInt(val, 10, 64)
		}

		if val, ok := data["default_shipping"]; ok {
			result.DefaultShippingID, _ = strconv.ParseInt(val, 10, 64)
		}
	}

	return result, nil
}
