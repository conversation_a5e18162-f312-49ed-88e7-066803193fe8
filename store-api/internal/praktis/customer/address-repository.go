package customer

import (
	core_utils "github.com/siper92/core-utils"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	mage_types "praktis.bg/store-api/packages/magento-core/mage-store/mage-types"
	"praktis.bg/store-api/packages/magento-core/types"
)

var DefaultAttributes = []string{
	// delivery data
	"delivery_carrier", "delivery_type", "econt_office_id",
	// address data
	"country_id", "region", "city", "city_id", "postcode", "street",
	// customer data
	"firstname", "lastname", "telephone",
	// invoice data
	"invoice", "invoice_type",
	// company invoice data
	"invoice_company_address", "invoice_company_bulstat", "invoice_company_city", "invoice_company_mol", "invoice_company_name", "invoice_company_vat",
	// personal invoice data
	"invoice_personal_address", "invoice_personal_city", "invoice_personal_name", "invoice_personal_pin", "invoice_personal_vat",
}

func (m *MagentoCustomerRepository) GetAddressByID(addressID int64, attrs ...string) (*MagentoCustomerAddress, error) {
	m.addressLock.Lock()
	defer m.addressLock.Unlock()

	result := &MagentoCustomerAddress{
		EntityID: addressID,
	}
	err := m.DB().Find(result).Error
	if err != nil {
		return nil, err
	}

	var tables mage_store.TableAttributesMapping
	if len(attrs) == 0 {
		tables, err = mage_store.CustomerAddressAttributes.SplitToByTable(DefaultAttributes)
	} else {
		tables, err = mage_store.CustomerAddressAttributes.SplitToByTable(attrs)
	}

	attrLoader := mage_store.NewAttributeLoader(
		m.DB(), types.CustomerAddressEntityType,
	)
	dataChan := make(chan mage_types.EntityData)
	errChan := make(chan error, 1)

	go func() {
		defer close(dataChan)
		defer close(errChan)

		for table, ids := range tables {
			data, err := attrLoader.LoadEntityAttribute(mage_store.LoadEntityAttribute{
				Table:    table,
				EntityId: addressID,
				AttrIds:  ids,
				StoreID:  mage_store.NoStoreID,
			})

			if err != nil {
				errChan <- err
				return
			} else {
				dataChan <- data
			}
		}
	}()

	for data := range dataChan {
		for attrCode, attrVal := range data {
			err = result.SetVal(attrCode, attrVal)
			core_utils.ErrorWarning(err)
		}
	}

	if attrErr := <-errChan; attrErr != nil {
		return nil, attrErr
	}

	return result, nil
}

func (m *MagentoCustomerRepository) GetAddress(customerID int64, attrs ...string) ([]*MagentoCustomerAddress, error) {
	m.addressLock.Lock()
	defer m.addressLock.Unlock()

	var results []*MagentoCustomerAddress
	err := m.DB().
		Model(&MagentoCustomerAddress{}).
		Where("parent_id = ?", customerID).
		Scan(&results).Error
	if err != nil {
		return nil, err
	} else if len(results) == 0 {
		return results, nil
	}

	var tables mage_store.TableAttributesMapping
	if len(attrs) == 0 {
		tables, err = mage_store.CustomerAddressAttributes.SplitToByTable(DefaultAttributes)
	} else {
		tables, err = mage_store.CustomerAddressAttributes.SplitToByTable(attrs)
	}

	attrLoader := mage_store.NewAttributeLoader(
		m.DB(), types.CustomerAddressEntityType,
	)
	dataChan := make(chan map[int64]mage_types.EntityData)
	errChan := make(chan error, 1)

	var idIndexMap = make(map[int64]int)
	var addressIDs []int64
	for i, address := range results {
		idIndexMap[address.EntityID] = i
		addressIDs = append(addressIDs, address.EntityID)
	}

	go func() {
		defer close(dataChan)
		defer close(errChan)

		for table, ids := range tables {
			data, err := attrLoader.LoadAttribute(mage_store.AttributeValuesParams{
				Table:   table,
				Ids:     addressIDs,
				AttrIds: ids,
				StoreID: mage_store.NoStoreID,
			})

			if err != nil {
				errChan <- err
				return
			} else {
				dataChan <- data
			}
		}
	}()

	for addressesData := range dataChan {
		for addressID, data := range addressesData {
			index := idIndexMap[addressID]
			for attrCode, attrVal := range data {
				err = results[index].SetVal(attrCode, attrVal)
				core_utils.ErrorWarning(err)
			}
		}
	}

	if attrErr := <-errChan; attrErr != nil {
		return results, attrErr
	}

	return results, nil
}
