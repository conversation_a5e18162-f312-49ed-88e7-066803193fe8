package customer

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	"praktis.bg/store-api/internal/praktis"
	mage_product "praktis.bg/store-api/packages/magento-core/mage-store/mage-product"
	"time"
)

type MagentoWishlist struct {
	db         *gorm.DB
	WishlistID int64 `gorm:"primaryKey;column:wishlist_id"`
	CustomerID int64 `gorm:"column:customer_id"`
}

func (*MagentoWishlist) TableName() string {
	return "wishlist"
}

func (w *MagentoWishlist) DB() *gorm.DB {
	if w.db == nil {
		w.db = praktis.GetDbClient()
	}

	return w.db
}

func (w *MagentoWishlist) SetDB(db *gorm.DB) *MagentoWishlist {
	w.db = db
	return w
}

func getWishlistItemsKey(customerID int64) string {
	return fmt.Sprintf("customer:%d:wishlist", customerID)
}

func (w *MagentoWishlist) CacheKey() string {
	return getWishlistItemsKey(w.CustomerID)
}

func (w *MagentoWishlist) TTL() time.Duration {
	return 30 * time.Minute
}

func (w *MagentoWishlist) GetSkus() []string {
	if w == nil && w.WishlistID < 1 {
		return []string{}
	}

	cache := praktis.GetCacheClient()
	cacheKey := w.CacheKey()
	var result []string
	var err error
	if cache.MustExists(cacheKey) {
		result, err = cache.GetSet(cacheKey)
		if err == nil {
			return result
		}
	}

	core_utils.ErrorWarning(err)
	store := praktis.GetPraktisStore()
	err = w.DB().Raw(`select p.sku as sku 
from wishlist_item i
     inner join catalog_product_entity p on p.entity_id = i.product_id
where i.store_id = ? and i.wishlist_id = ?;`, store.GetID(), w.WishlistID).Scan(&result).Error
	if err != nil {
		core_utils.ErrorWarning(err)
		return result
	}

	_ = cache.Delete(cacheKey)
	_, err = cache.AddSetMember(cacheKey, result...)
	if err == nil {
		_, err = praktis.GetCacheClient().UpdateTTl(cacheKey, w.TTL())
	}

	return result
}

type MagentoWishlistItem struct {
	WishlistItemID int64     `gorm:"primaryKey;column:wishlist_item_id"`
	WishlistID     int64     `gorm:"column:wishlist_id"`
	ProductID      int64     `gorm:"column:product_id"`
	StoreID        int64     `gorm:"column:store_id"`
	AddedAt        time.Time `gorm:"column:added_at;autoCreateTime"`
	Qty            float64   `gorm:"column:qty"`
}

func (*MagentoWishlistItem) TableName() string {
	return `wishlist_item`
}

func (c *MagentoCustomer) GetWishlist() *MagentoWishlist {
	if c.Wishlist != nil && c.Wishlist.WishlistID > 0 {
		return c.Wishlist
	} else if c.WishlistID > 0 {
		c.Wishlist = &MagentoWishlist{
			WishlistID: c.WishlistID,
			CustomerID: c.EntityID,
		}
	} else {
		c.Wishlist = &MagentoWishlist{
			CustomerID: c.EntityID,
		}
	}

	return c.Wishlist
}

func (c *MagentoCustomer) GetWishlistOrCreate() *MagentoWishlist {
	w := c.GetWishlist()
	if w == nil || w.WishlistID < 1 {
		w = &MagentoWishlist{
			CustomerID: c.EntityID,
		}
		err := c.Repository().DB().Create(w).Error
		if err == nil {
			c.Wishlist = w
		} else {
			core_utils.ErrorWarning(err)
		}
	}

	return c.Wishlist
}

var AlreadyInWishlistError = fmt.Errorf("вече ше в списъка с желания")

func (w *MagentoWishlist) AddItem(sku string) error {
	id, err := mage_product.NewProductRepository().GetSkuID(sku)
	if err != nil {
		return err
	} else if w.WishlistID < 1 {
		return fmt.Errorf("[%d] не е намерен списък с желания", w.CustomerID)
	}

	newItems := &MagentoWishlistItem{
		WishlistID: w.WishlistID,
		ProductID:  id,
		StoreID:    int64(praktis.GetPraktisStore().GetID()),
		AddedAt:    time.Now(),
		Qty:        1,
	}
	var count int64 = 0
	w.DB().Model(&MagentoWishlistItem{}).
		Where("wishlist_id = ? and product_id = ?", w.WishlistID, id).
		Count(&count)
	if count > 0 {
		return AlreadyInWishlistError
	}

	err = w.DB().Save(newItems).Error
	if err != nil {
		return err
	}

	_ = praktis.GetCacheClient().Delete(w.CacheKey())

	return nil
}

func (w *MagentoWishlist) RemoveItem(sku string) error {
	id, err := mage_product.NewProductRepository().GetSkuID(sku)
	if err != nil {
		return err
	}

	item := &MagentoWishlistItem{}
	w.DB().Model(&MagentoWishlistItem{}).
		Where("wishlist_id = ? and product_id = ?", w.WishlistID, id).
		Find(item)
	if item.WishlistItemID < 1 {
		return AlreadyInWishlistError
	}

	err = w.DB().Delete(item).Error
	if err != nil {
		return err
	}

	_ = praktis.GetCacheClient().Delete(w.CacheKey())

	return nil
}
