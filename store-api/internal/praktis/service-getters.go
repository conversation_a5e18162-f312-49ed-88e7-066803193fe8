package praktis

import (
	cache "github.com/siper92/api-base/cache"
	"gorm.io/gorm"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

func GetPraktisStore() *magento_core.StoreEntity {
	return magento_core.GetStoreClient().GetStore()
}

func GetCacheClient() *cache.RedisCacheProvider {
	client := magento_core.GetStoreClient().GetCacheClient()
	return client
}

func GetDbClient() *gorm.DB {
	return magento_core.GetStoreClient().DbConn()
}
