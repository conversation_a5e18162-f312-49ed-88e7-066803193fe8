package slider

import (
	"database/sql"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
)

type StenikSliderSlide struct {
	SlideID             uint           `gorm:"column:slide_id;primaryKey"`
	Status              sql.NullInt16  `gorm:"column:status"`
	StoreID             sql.NullInt32  `gorm:"column:store_id"`
	Title               sql.NullString `gorm:"column:title"`
	SliderKey           sql.NullString `gorm:"column:slider_key"`
	Position            sql.NullInt32  `gorm:"column:position"`
	Image               sql.NullString `gorm:"column:image"`
	ImageMobile         sql.NullString `gorm:"column:image_mobile"`
	Link                sql.NullString `gorm:"column:link"`
	Content             sql.NullString `gorm:"column:content"`
	ActiveFromDate      sql.NullTime   `gorm:"column:active_from_date"`
	ActiveToDate        sql.NullTime   `gorm:"column:active_to_date"`
	LinkOpenInNewWindow sql.NullInt16  `gorm:"column:link_open_in_new_window"`
	LinkLeft            sql.NullString `gorm:"column:link_left"`
	LinkRight           sql.NullString `gorm:"column:link_right"`
	FrontendTitle       sql.NullString `gorm:"column:frontend_title"`
	FrontendSubtitle    sql.NullString `gorm:"column:frontend_subtitle"`
	ShowBuyButton       sql.NullInt32  `gorm:"column:show_buy_button"`
	LabelStyle          sql.NullString `gorm:"column:label_style"`
	LabelText           sql.NullString `gorm:"column:label_text"`
	AdditionalClass     sql.NullString `gorm:"column:additional_class"`
}

func (StenikSliderSlide) TableName() string {
	return "stenik_slider_slide"
}

func (s *StenikSliderSlide) ToModel() *model.CarouselSlide {
	var link *model.Link
	if s.Link.String != "" {
		link = &model.Link{
			Href:  s.Link.String,
			Text:  "",
			Title: nil,
		}
	}

	store := praktis.GetPraktisStore()
	var imageSrc *string
	if s.ImageMobile.String != "" {
		imageSrc = &s.ImageMobile.String
	}

	title := s.Title.String

	return &model.CarouselSlide{
		Title:       s.Title.String,
		Description: "",
		Link:        link,
		Image: &model.Image{
			Src:       store.GetMedialUrl(s.Image.String),
			MobileSrc: imageSrc,
			Alt:       &title,
			Title:     &title,
		},
	}
}

func GetSlider(sliderKey string) (*model.CarouselWidget, error) {
	var slides []StenikSliderSlide
	err := praktis.GetDbClient().
		Where("slider_key = ?", sliderKey).
		Where("status = 1").
		Order("position ASC").
		Find(&slides).Error
	if err != nil {
		return nil, err
	}

	var activeSlides []*model.CarouselSlide
	for _, slide := range slides {
		if slide.Status.Int16 == 1 {
			activeSlides = append(activeSlides, slide.ToModel())
		}
	}

	return &model.CarouselWidget{
		Identifier: sliderKey,
		Slides:     activeSlides,
	}, nil
}
