package cms

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/blog"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	slider2 "praktis.bg/store-api/internal/praktis/cms/slider"
	"praktis.bg/store-api/internal/praktis/cms/widgets"
	"sort"
	"strings"
)

func GetBrands(featured bool) ([]*model.Brand, error) {
	brands, err := product.GetAllBrands(featured)
	if err != nil {
		return nil, err
	}

	sort.Slice(brands, func(i, j int) bool {
		if brands[i].Position == brands[j].Position {
			return brands[i].BrandID < brands[j].BrandID
		}

		return brands[i].Position < brands[j].Position
	})

	result := make([]*model.Brand, len(brands))
	for i, brand := range brands {
		result[i] = brand.ToModel()
	}

	return result, nil
}

func ToHomePageData(sliderID, contentBlockIdentifier string) (*model.HomePage, error) {
	slider, err := slider2.GetSlider(sliderID)
	if err != nil {
		return nil, err
	}

	widgets, err := ParseBlockToWidget(contentBlockIdentifier)
	if err != nil {
		return nil, err
	}

	return &model.HomePage{
		HeroSlider: slider,
		Widgets:    widgets,
	}, nil
}

func ParseBlockToWidget(contentBlockIdentifier string) ([]model.Widget, error) {
	result := make([]model.Widget, 0)
	var content string
	err := praktis.GetDbClient().
		Table("cms_block").Select("content").
		Where("identifier = ?", contentBlockIdentifier).Limit(1).
		Scan(&content).Error
	if err != nil {
		return nil, err
	} else if len(content) < 1 {
		return nil, fmt.Errorf("block %s not found", contentBlockIdentifier)
	}

	rawWidgets, err := ParseRawWidgets(content)
	if err != nil {
		return nil, err
	}

	for _, rawWidget := range rawWidgets {
		var w model.Widget
		w, err = toWidget(rawWidget)
		if err != nil {
			core_utils.ErrorWarning(err)
			continue
		}
		result = append(result, w)
	}

	result = sortAndCombine(result)

	return result, nil
}

func sortAndCombine(result []model.Widget) []model.Widget {
	combinedWidgets := map[string]model.Widget{}
	orders := map[string]int{}
	var i int
	for _, widget := range result {
		i++
		switch w := widget.(type) {
		case *model.ProductsSliderWidget:
			if _, ok := orders[w.Identifier]; !ok {
				orders[w.Identifier] = i
			}

			if _, ok := combinedWidgets[w.Identifier]; ok {
				w2 := combinedWidgets[w.Identifier]
				var w2Type *model.ProductsSliderWidget
				w2Type, ok = w2.(*model.ProductsSliderWidget)
				w2Type.Tabs = append(w2Type.Tabs, w.Tabs...)
				combinedWidgets[w.Identifier] = w2Type
			} else {
				combinedWidgets[w.Identifier] = w
			}
		default:
			key := fmt.Sprintf("widget_%d", i)
			orders[key] = i
			combinedWidgets[key] = widget
		}

	}

	var sortableWidgets []struct {
		Key    string
		Widget model.Widget
		Order  int
	}

	for key, widget := range combinedWidgets {
		if orderValue, exists := orders[key]; exists {
			sortableWidgets = append(sortableWidgets, struct {
				Key    string
				Widget model.Widget
				Order  int
			}{
				Key:    key,
				Widget: widget,
				Order:  orderValue,
			})
		}
	}

	sort.Slice(sortableWidgets, func(i, j int) bool {
		return sortableWidgets[i].Order < sortableWidgets[j].Order
	})

	result = []model.Widget{}
	for _, item := range sortableWidgets {
		result = append(result, item.Widget)
	}

	return result
}

func toWidget(widget RawWidget) (model.Widget, error) {
	switch strings.ToLower(widget.Type) {
	case "pfg_widget/widget_catalog_product_list":
		return NewProductsSliderWidget(widget)
	case "stenik_slider/widget_slider":
		return GetSliderWidget(widget)
	case "praktis_theme/widget_seasonal_offering":
		return GetSeasonalOffering(widget)
	case "praktis_widgets/clnews_latest":
		return GetNewsWidget(widget)
	case "pfg_theme/services":
		return GetServicesWidget(widget)
	default:
		return nil, fmt.Errorf("widget type %s not supported", widget.Type)
	}
}

func GetSliderWidget(widget RawWidget) (*model.CarouselWidget, error) {
	sliderID, ok := widget.Data["slider_key"]
	if ok {
		return slider2.GetSlider(sliderID)
	}

	return nil, fmt.Errorf("slider %s not found", sliderID)
}

func GetNewsWidget(widget RawWidget) (model.Widget, error) {
	var news = &model.NewsWidget{
		Identifier: "praktis_widgets/clnews_latest",
		Title:      "",
		Subtitle:   "",
		Link: &model.Link{
			Href:  "/news",
			Text:  "Всички Новини",
			Title: core_utils.ToStringPointer("Всички Новини"),
		},
		Articles: nil,
	}

	news.Title, _ = widget.Data["title"]
	news.Subtitle, _ = widget.Data["sub_title"]

	posts, _ := blog.GetBlogPostCollection(blog.GetCollectionParams{
		Page:     1,
		PageSize: 5,
	})
	for _, post := range posts {
		article := &model.Article{
			Title:       post.Title,
			Description: post.Summary,
			Link: &model.Link{
				Href:  "/news/" + post.UrlKey,
				Text:  post.Title,
				Title: nil,
			},
			Image: &model.Image{
				Src:       post.PreviewImageUrl.String(),
				MobileSrc: nil,
				Alt:       &post.Title,
				Title:     &post.Title,
			},
		}
		news.Articles = append(news.Articles, article)
	}

	return news, nil
}

func GetServicesWidget(widget RawWidget) (model.Widget, error) {
	title, _ := widget.Data["title"]
	subtitle, _ := widget.Data["sub_title"]
	stores, _ := GetAvailableStores()
	var servicesW = &model.ServiceWidget{
		Title:    title,
		Subtitle: subtitle,
		Services: GetAvailableServices(),
		Form: &model.FormData{
			Title: "Изготвяне на оферта",
			Text: praktis.GetPraktisStore().GetConfig(
				"pfg_theme/messages/send_request_message",
				"Изпратете запитване",
			),
			FormID: "services_form",
		},
		AvailableStores: stores,
	}

	linkText, _ := widget.Data["link_text"]
	linkUrl, _ := widget.Data["link_url"]
	if len(linkText) > 0 && len(linkUrl) > 0 {
		servicesW.Link = &model.Link{
			Href:  linkUrl,
			Text:  linkText,
			Title: core_utils.ToStringPointer(linkText),
		}
	} else {
		servicesW.Link = &model.Link{
			Href:  "/services",
			Text:  "Всички услуги",
			Title: core_utils.ToStringPointer("Всички услуги"),
		}
	}

	return servicesW, nil
}

func GetSeasonalOffering(widget RawWidget) (model.Widget, error) {
	offeringID, ok := widget.Data["offering"]
	if ok {
		offering, err := widgets.GetTileWidget(offeringID)
		core_utils.ErrorWarning(err)
		if offering != nil {
			return offering, nil
		}
	}

	return nil, fmt.Errorf("offering '%s' not found", offeringID)
}
