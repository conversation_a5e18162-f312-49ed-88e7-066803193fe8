package cms

import (
	"regexp"
)

// RawWidget represents a parsed widget with its type and attributes
type RawWidget struct {
	Type string
	Data map[string]string
}

// ParseRawWidgets extracts all widgets from the given content
func ParseRawWidgets(content string) ([]RawWidget, error) {
	var widgets []RawWidget

	// {{widget type="..." attr1="val1" attr2="val2" ... }}
	re := regexp.MustCompile(`{{widget\s+(.*?)}}`)
	matches := re.FindAllStringSubmatch(content, -1)

	for _, match := range matches {
		if len(match) < 2 {
			continue
		}

		attributesStr := match[1]
		widget, err := parseWidgetAttributes(attributesStr)
		if err != nil {
			return nil, err
		}

		widgets = append(widgets, widget)
	}

	return widgets, nil
}

func parseWidgetAttributes(attributesStr string) (RawWidget, error) {
	widget := RawWidget{
		Data: make(map[string]string),
	}
	re := regexp.MustCompile(`(\w+)="([^"]*)"`)

	matches := re.FindAllStringSubmatch(attributesStr, -1)

	for _, match := range matches {
		if len(match) != 3 {
			continue
		}

		key := match[1]
		value := match[2]

		if key == "type" {
			widget.Type = value
		} else {
			widget.Data[key] = value
		}
	}

	return widget, nil
}
