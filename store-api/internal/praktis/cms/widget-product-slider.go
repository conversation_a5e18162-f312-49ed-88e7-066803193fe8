package cms

import (
	b64 "encoding/base64"
	"encoding/json"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	gorm "gorm.io/gorm"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	"strconv"
	"strings"
)

func NewRelatedProductsSliderWidget(
	store *magento_core.StoreEntity,
	id int64,
) (*model.ProductsSliderWidget, error) {
	limit := store.GetConfigInt("praktis_upsell/general/limit", 10)
	priceVariance := store.GetConfigInt("praktis_upsell/general/percent_range", 0)
	relatedIds, err := product.GetPraktisRelatedProducts(id, limit, priceVariance)
	if err != nil || len(relatedIds) < 1 {
		return nil, err
	}

	products, err := product.NewPraktisProductRepository(nil).GetIDs(relatedIds, nil)
	if err != nil {
		return nil, err
	}

	title := store.GetConfig("praktis_upsell/general/title", "Подобни продукти")
	var w = &model.ProductsSliderWidget{
		Title: title,
		Tabs: []*model.ProductsTab{
			{
				Title:    title,
				Products: make([]model.Product, len(products)),
			},
		},
	}

	for i, p := range products {
		// Ensure gallery images are loaded before converting to model
		if p.GetEntity() != nil {
			_ = p.GetEntity().LoadGallery()
		}
		w.Tabs[0].Products[i] = p.ToModel()
	}

	return w, nil
}

func NewProductsSliderWidget(widget RawWidget) (*model.ProductsSliderWidget, error) {
	var ok bool
	var w = &model.ProductsSliderWidget{}

	w.Title, ok = widget.Data["title"]
	if !ok {
		w.Title = "Unknown"
	}

	w.Subtitle, ok = widget.Data["subtitle"]
	if !ok {
		w.Subtitle = ""
	}

	w.Identifier, ok = widget.Data["tab_group_id"]
	if !ok {
		w.Identifier = w.Title
	}

	products, err := GetProductsByWidget(widget.Data)
	core_utils.ErrorWarning(err)

	w.Tabs = []*model.ProductsTab{
		{
			Title:    w.Title,
			Products: products,
		},
	}

	return w, nil
}

func GetProductsByWidget(data map[string]string) ([]model.Product, error) {
	// format base64 encoded string
	var attrFilterMap []ProductFilter
	filter, ok := data["product_attribute_filters"]
	if ok && len(filter) > 0 {
		attrFilterMap = toFiltersMapFromBase64(filter)
	}

	// format category/2404
	categoryFilter, ok := data["category_filter"]
	if ok {
		attrFilterMap = append(attrFilterMap, ProductFilter{
			AttributeCode: "category_id",
			Condition:     "=",
			Value:         strings.TrimPrefix(categoryFilter, "category/"),
		})
	}

	sortBy, ok := data["product_attribute_sort"]
	if !ok || len(sortBy) < 1 {
		sortBy = "entity_id"
	}

	sortDir, ok := data["product_attribute_sort_dir"]
	if sortDir != "asc" && sortDir != "desc" {
		sortDir = "desc"
	}

	limit, ok := data["product_limit"]
	if !ok {
		limit = "12"
	}

	var result []model.Product
	ids, err := getWidgetProductIds(
		attrFilterMap,
		sortBy,
		sortDir,
		limit,
	)
	if err != nil || len(ids) < 1 {
		core_utils.ErrorWarning(err)
		return result, err
	}

	products, err := product.NewPraktisProductRepository(nil).GetIDs(ids, nil)
	if err != nil {
		core_utils.ErrorWarning(err)
		return result, err
	}

	for _, p := range products {
		// Ensure gallery images are loaded before converting to model
		if p.GetEntity() != nil {
			_ = p.GetEntity().LoadGallery()
		}
		result = append(result, p.ToModel())
	}

	return result, nil
}

func toFiltersMapFromBase64(s string) []ProductFilter {
	sDec, err := b64.StdEncoding.DecodeString(s)
	core_utils.ErrorWarning(err)
	if err != nil {
		return []ProductFilter{}
	}

	var attrFilterMap []ProductFilter
	err = json.Unmarshal(sDec, &attrFilterMap)
	core_utils.ErrorWarning(err)

	return attrFilterMap
}

func getWidgetProductIds(
	filters []ProductFilter,
	sortBy string,
	sortDir string,
	limit string,
) ([]int64, error) {
	var ids []int64
	qb := praktis.GetDbClient().
		Table("catalog_product_entity p").Select("p.entity_id").
		Joins("inner join catalog_product_index_price pi on pi.entity_id = p.entity_id && pi.customer_group_id = 0 && pi.website_id = 1").
		Joins("inner join cataloginventory_stock_item csi on p.entity_id = csi.product_id")

	filterBy := map[string]struct{}{}
	for _, filter := range filters {
		qb = applyFilter(qb, filter)
		filterBy[filter.AttributeCode] = struct{}{}
	}

	qb = applySorting(qb, sortBy, sortDir, filterBy)

	limitInt, _ := strconv.Atoi(limit)
	if limitInt < 1 {
		limitInt = 12
	}

	err := qb.Limit(limitInt).Group("p.entity_id").Scan(&ids).Error
	if err != nil {
		core_utils.ErrorWarning(err)
		return nil, err
	}

	return ids, err
}

func applySorting(qb *gorm.DB, by string, dir string, by2 map[string]struct{}) *gorm.DB {
	switch by {
	case "_rand":
		qb = qb.Order("RAND()")
	case "_final_price":
		qb = qb.Order("pi.final_price " + dir)
	case "_discount_percent":
		qb = qb.Order("(1-(pi.final_price / pi.price)) " + dir)
	case "entity_id", "sku", "position":
		qb = qb.Order(fmt.Sprintf("p.entity_id %s", dir))
	default:
		storeId := praktis.GetPraktisStore().StoreID
		attr, err := mage_store.ProductAttributes.GetAttribute(by)
		if err != nil || attr.AttributeID < 1 {
			core_utils.ErrorWarning(err)
			return qb
		}

		if _, ok := by2[by]; !ok {
			qb = qb.Joins(fmt.Sprintf(
				"JOIN `%s` %s ON %s.entity_id = p.entity_id AND %s.attribute_id = %d AND %s.store_id in (0,%d)",
				attr.GetMagentoTable(),
				attr.GetAlias(storeId),
				attr.GetAlias(storeId),
				attr.GetAlias(storeId),
				attr.AttributeID,
				attr.GetAlias(storeId),
				storeId,
			))
		}

		qb = qb.Order(
			fmt.Sprintf("%s.value %s", attr.GetAlias(storeId), dir),
		)
	}

	return qb
}
