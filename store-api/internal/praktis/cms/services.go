package cms

import (
	"praktis.bg/store-api/graphql/model"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

func GetAvailableServices() []*model.Service {
	store := magento_core.GetStoreClient().GetStore()
	return []*model.Service{
		{
			ID:      1,
			Name:    "Машини под наем",
			URL:     "/mashini-pod-naem",
			IconURL: store.GetMedialUrl("/theme/services/rent_machine.svg"),
		},
		{
			ID:      2,
			Name:    "Транспорт до адрес",
			URL:     "/transport_praktis",
			IconURL: store.GetMedialUrl("/theme/services/transport.svg"),
		},
		{
			ID:      3,
			Name:    "Машинно тониране",
			URL:     "/mashini-pod-naem",
			IconURL: store.GetMedialUrl("/theme/services/machine_tuning.svg"),
		},
		{
			ID:      4,
			Name:    "Монтажни услуги",
			URL:     "/mashini-pod-naem",
			IconURL: store.GetMedialUrl("/theme/services/montaj.svg"),
		},
		{
			ID:      5,
			Name:    "Обкантване на килими",
			URL:     "/mashini-pod-naem",
			IconURL: store.GetMedialUrl("/theme/services/carpets_treatment.svg"),
		},
		{
			ID:      6,
			Name:    "Extra services...",
			URL:     "/tobe-added",
			IconURL: store.GetMedialUrl("/theme/services/rent_machine.svg"),
		},
		{
			ID:      7,
			Name:    "Even more extra services...",
			URL:     "/tobe-added",
			IconURL: store.GetMedialUrl("/theme/services/rent_machine.svg"),
		},
	}
}
