package cms

import (
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/entity"
	"praktis.bg/store-api/graphql/model"
	"sort"
)

func GetAvailableStores() ([]*model.PraktisStore, error) {
	var err error
	var _results []*model.PraktisStore
	var _resultEntities entity.PraktisStoreEntityCollection

	err = entity.NewPraktisStoreEntityRepository().
		Select("*").
		//Where("active = ?", 1).
		Order("display_order ASC").
		Find(&_resultEntities).Error
	if err != nil {
		core_utils.ErrorWarning(err)
		return nil, err
	}
	if len(_resultEntities) > 0 {
		for _, e := range _resultEntities {
			_results = append(_results, e.ToModel())
		}
	}

	return _results, err
}

// sortStoreEntitiesByDisplayOrder sorts stores by their DisplayOrder field in ascending order
func sortStoreEntitiesByDisplayOrder(stores entity.PraktisStoreEntityCollection) {
	sort.Slice(stores, func(i, j int) bool {
		return stores[i].DisplayOrder < stores[j].DisplayOrder
	})
}
