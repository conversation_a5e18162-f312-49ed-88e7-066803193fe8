package widgets

import (
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
	"strconv"
	"strings"
)

func GetTileWidget(offeringID string) (*model.TilesWidget, error) {
	offeringIDInt64, err := strconv.ParseInt(offeringID, 10, 64)
	if err != nil {
		return nil, err
	}

	offering, err := GetSeasonalOfferingByID(offeringIDInt64)
	if err != nil {
		return nil, err
	} else if offering == nil {
		return nil, nil
	}

	var link *model.Link
	if offering.LinkPath != "" {
		link = &model.Link{
			Href: offering.LinkPath,
			Text: offering.LinkLabel,
		}
	}

	var res = &model.TilesWidget{
		Identifier: "seasonal-offering-" + offeringID,
		Title:      offering.Title,
		Subtitle:   offering.SubTitle,
		ViewMore:   link,
		Rows:       []*model.TilesRow{},
	}

	var rows [][]*SeasonalOfferingBlock
	rows, err = offering.GetRowsOfBlocks()
	if len(rows) > 0 {
		res.Rows = make([]*model.TilesRow, len(rows))
		for i, row := range rows {
			res.Rows[i] = &model.TilesRow{
				Cols: make([]model.Tile, 0, len(row)),
			}
			for _, block := range row {
				if t := offeringBlockToTileModel(block); t != nil {
					res.Rows[i].Cols = append(res.Rows[i].Cols, t)
				}
			}
		}
	}

	return res, err
}

func offeringBlockToTileModel(block *SeasonalOfferingBlock) model.Tile {
	if block == nil {
		return nil
	}

	switch block.Type {
	case TypeCategories:
		return toCategoryTileModel(block)
	case TypeTopLeft, TypeTopRight, TypeHalfLeft, TypeHalfRight, TypeBottomLeft, TypeBottomRight:
		return toImageTileModel(block)
	case TypeHalfBottomLeftTopRight, TypeHalfTopLeftBottomRight:
		return toDoubleImageTileModel(block)
	default:
		return nil
	}
}

func toDoubleImageTileModel(block *SeasonalOfferingBlock) *model.DoubleImageTile {
	var image *model.Image
	if block.Image != "" {
		image = &model.Image{
			Src:       block.Image,
			MobileSrc: nil,
			Alt:       &block.TextTitle,
			Title:     &block.TextTitle,
		}
	}

	var bottomImage *model.Image
	if block.BottomImage != "" {
		bottomImage = &model.Image{
			Src:       block.BottomImage,
			MobileSrc: nil,
			Alt:       &block.BottomTextTitle,
			Title:     &block.BottomTextTitle,
		}
	}

	var linkOne *model.Link
	if block.URL != "" {
		linkOne = &model.Link{
			Href:  block.URL,
			Text:  "",
			Title: nil,
		}
	}

	var linkTwo *model.Link
	if block.BottomURL != "" {
		linkTwo = &model.Link{
			Href:  block.BottomURL,
			Text:  "",
			Title: nil,
		}
	}

	var res = &model.DoubleImageTile{
		ImageOne: image,
		ContentOne: &model.TileContent{
			Title: block.TextTitle,
			Text:  &block.TextContent,
			Link:  linkOne,
			Icon:  &model.Image{Src: ""},
		},
		ImageTwo: bottomImage,
		ContentTwo: &model.TileContent{
			Title: block.BottomTextTitle,
			Text:  &block.BottomTextContent,
			Link:  linkTwo,
			Icon:  &model.Image{Src: ""},
		},
	}

	switch block.Type {
	case TypeHalfBottomLeftTopRight:
		res.ImageOnePosition = model.DoubleImageTilePositionRight
		res.ImageTwoPosition = model.DoubleImageTilePositionLeft
	case TypeHalfTopLeftBottomRight:
		res.ImageOnePosition = model.DoubleImageTilePositionLeft
		res.ImageTwoPosition = model.DoubleImageTilePositionRight
	default:
		res.ImageOnePosition = model.DoubleImageTilePositionLeft
		res.ImageTwoPosition = model.DoubleImageTilePositionRight
	}

	return res
}

func toImageTileModel(block *SeasonalOfferingBlock) *model.ImageTile {
	var image *model.Image
	if block.Image != "" {
		image = &model.Image{
			Src:       block.Image,
			MobileSrc: nil,
			Alt:       &block.TextTitle,
			Title:     &block.TextTitle,
		}
	} else {
		image = &model.Image{
			Src:       "",
			MobileSrc: nil,
			Alt:       nil,
			Title:     nil,
		}
	}

	var link *model.Link
	if block.URL != "" {
		link = &model.Link{
			Href:  block.URL,
			Text:  "",
			Title: nil,
		}
	} else {
		link = &model.Link{
			Href:  "",
			Text:  "",
			Title: nil,
		}
	}

	var res = &model.ImageTile{
		Image:    image,
		BgColor:  "",
		Position: "",
		Content: &model.TileContent{
			Title: block.TextTitle,
			Text:  &block.TextContent,
			Link:  link,
			Icon: &model.Image{
				Src: "",
			},
		},
	}

	switch block.Type {
	case TypeTopLeft:
		res.Position = model.TileContentPositionLeftTop
	case TypeTopRight:
		res.Position = model.TileContentPositionRightTop
	case TypeBottomLeft:
		res.Position = model.TileContentPositionLeftBottom
	case TypeBottomRight:
		res.Position = model.TileContentPositionRightBottom
	case TypeHalfLeft:
		res.Position = model.TileContentPositionLeft
	case TypeHalfRight:
		res.Position = model.TileContentPositionRight
	default:
		res.Position = model.TileContentPositionLeftTop
	}

	return res
}

func toCategoryTileModel(block *SeasonalOfferingBlock) *model.CategoryTile {
	var image *model.Image
	if block.Image != "" {
		image = &model.Image{
			Src:       block.Image,
			MobileSrc: nil,
			Alt:       &block.TextTitle,
			Title:     &block.TextTitle,
		}
	} else {
		image = &model.Image{
			Src:       "",
			MobileSrc: nil,
			Alt:       nil,
			Title:     nil,
		}
	}

	var links []*model.Link
	ids := strings.Split(block.CategorySelect, ",")
	repo := mage_store.NewCategoryRepository()
	for _, id := range ids {
		category, err := repo.Get(types.ToEntityID(strings.TrimSpace(id)))
		if err != nil {
			core_utils.ErrorWarning(err)
		} else if category != nil {
			links = append(links, &model.Link{
				Text:  category.Name,
				Title: &category.Name,
				Href:  category.GetRelPath(),
			})
		}
	}

	return &model.CategoryTile{
		Title:      block.TextTitle,
		Image:      image,
		BgColor:    block.TextBackground,
		TextColor:  block.TextColor,
		Categories: links,
	}
}
