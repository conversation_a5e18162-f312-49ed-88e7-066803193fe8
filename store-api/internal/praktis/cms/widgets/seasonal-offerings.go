package widgets

import (
	"encoding/json"
	"praktis.bg/store-api/internal/praktis"
)

type SeasonalOffering struct {
	OfferingID int64  `gorm:"column:offering_id;primaryKey;autoIncrement" json:"offering_id"`
	Title      string `gorm:"column:title" json:"title"`
	SubTitle   string `gorm:"column:subtitle" json:"subtitle"`
	LinkPath   string `gorm:"column:url_path" json:"url_path"`
	LinkLabel  string `gorm:"column:url_title" json:"url_title"`
	Layout     string `gorm:"column:layout" json:"layout"`
}

func (*SeasonalOffering) TableName() string {
	return "praktis_seasonal_offerings"
}

type SeasonalOfferingBlockType int16

const (
	TypeTopLeft                SeasonalOfferingBlockType = 1
	TypeTopRight               SeasonalOfferingBlockType = 2
	TypeBottomLeft             SeasonalOfferingBlockType = 3
	TypeBottomRight            SeasonalOfferingBlockType = 4
	TypeHalfLeft               SeasonalOfferingBlockType = 5
	TypeHalfRight              SeasonalOfferingBlockType = 6
	TypeHalfBottomLeftTopRight SeasonalOfferingBlockType = 7
	TypeHalfTopLeftBottomRight SeasonalOfferingBlockType = 8
	TypeCategories             SeasonalOfferingBlockType = 9
)

type SeasonalOfferingBlock struct {
	BlockID              int64                     `gorm:"column:block_id;primaryKey;autoIncrement" json:"block_id"`
	ParentID             int64                     `gorm:"column:parent_id;default:0" json:"parent_id"`
	Name                 string                    `gorm:"column:name" json:"name"`
	Type                 SeasonalOfferingBlockType `gorm:"column:type;default:1" json:"type"`
	SortOrder            int16                     `gorm:"column:sort_order;default:1" json:"sort_order"`
	TextTitle            string                    `gorm:"column:text_title" json:"text_title"`
	TextContent          string                    `gorm:"column:text_content" json:"text_content"`
	TextColor            string                    `gorm:"column:text_color" json:"text_color"`
	TextBackground       string                    `gorm:"column:text_background" json:"text_background"`
	Image                string                    `gorm:"column:image" json:"image"`
	BottomTextTitle      string                    `gorm:"column:bottom_text_title" json:"bottom_text_title"`
	BottomTextContent    string                    `gorm:"column:bottom_text_content" json:"bottom_text_content"`
	BottomTextColor      string                    `gorm:"column:bottom_text_color" json:"bottom_text_color"`
	BottomTextBackground string                    `gorm:"column:bottom_text_background" json:"bottom_text_background"`
	BottomImage          string                    `gorm:"column:bottom_image" json:"bottom_image"`
	URL                  string                    `gorm:"column:url" json:"url"`
	BottomURL            string                    `gorm:"column:bottom_url" json:"bottom_url"`
	CategorySelect       string                    `gorm:"column:category_select" json:"category_select"`

	SeasonalOffering *SeasonalOffering `gorm:"foreignKey:ParentID;references:OfferingID" json:"seasonal_offering,omitempty"`
}

func (*SeasonalOfferingBlock) TableName() string {
	return "praktis_seasonal_offering_block"
}

func GetSeasonalOfferingByID(offeringID int64) (*SeasonalOffering, error) {
	var offering SeasonalOffering
	result := praktis.GetDbClient().Where("offering_id = ?", offeringID).First(&offering)
	if result.Error != nil {
		return nil, result.Error
	}
	return &offering, nil
}

func GetAllSeasonalOfferings() ([]*SeasonalOffering, error) {
	var offerings []*SeasonalOffering
	result := praktis.GetDbClient().Find(&offerings)
	if result.Error != nil {
		return nil, result.Error
	}
	return offerings, nil
}

func GetSeasonalOfferingBlockByID(blockID int64) (*SeasonalOfferingBlock, error) {
	var block SeasonalOfferingBlock
	result := praktis.GetDbClient().Where("block_id = ?", blockID).First(&block)
	if result.Error != nil {
		return nil, result.Error
	}
	return &block, nil
}

func GetSeasonalOfferingBlocks(offeringID int64) ([]*SeasonalOfferingBlock, error) {
	var blocks []*SeasonalOfferingBlock
	result := praktis.GetDbClient().Where("parent_id = ?", offeringID).Order("sort_order ASC").Find(&blocks)
	if result.Error != nil {
		return nil, result.Error
	}
	return blocks, nil
}

func GetSeasonalOfferingWithBlocks(offeringID int64) (*SeasonalOffering, []*SeasonalOfferingBlock, error) {
	offering, err := GetSeasonalOfferingByID(offeringID)
	if err != nil {
		return nil, nil, err
	}

	blocks, err := GetSeasonalOfferingBlocks(offeringID)
	if err != nil {
		return offering, nil, err
	}

	return offering, blocks, nil
}

func parseLayoutToIDMap(layout string) (map[int][]int64, error) {
	result := make(map[int][]int64)

	var layoutData [][]json.Number
	err := json.Unmarshal([]byte(layout), &layoutData)
	if err != nil {
		return nil, err
	}

	for rowIndex, row := range layoutData {
		idList := make([]int64, 0, len(row))

		for _, idStr := range row {
			id, err := idStr.Int64()
			if err != nil {
				return nil, err
			}
			idList = append(idList, id)
		}

		result[rowIndex] = idList
	}

	return result, nil
}

func (offering *SeasonalOffering) GetRowsOfBlocks() ([][]*SeasonalOfferingBlock, error) {
	if offering == nil || offering.Layout == "" {
		return nil, nil
	}

	idMap, err := parseLayoutToIDMap(offering.Layout)
	if err != nil {
		return nil, err
	}

	var allBlockIDs []int64
	for _, blockIDs := range idMap {
		allBlockIDs = append(allBlockIDs, blockIDs...)
	}

	if len(allBlockIDs) == 0 {
		return make([][]*SeasonalOfferingBlock, 0), nil
	}

	var allBlocks []*SeasonalOfferingBlock
	result := praktis.GetDbClient().Where("block_id IN ?", allBlockIDs).Find(&allBlocks)
	if result.Error != nil {
		return nil, result.Error
	}

	blockMap := make(map[int64]*SeasonalOfferingBlock)
	for _, block := range allBlocks {
		blockMap[block.BlockID] = block
	}

	maxRowIndex := -1
	for rowIndex := range idMap {
		if rowIndex > maxRowIndex {
			maxRowIndex = rowIndex
		}
	}

	layoutResult := make([][]*SeasonalOfferingBlock, maxRowIndex+1)
	for rowIndex, blockIDs := range idMap {
		if len(blockIDs) == 0 {
			continue
		}

		rowBlocks := make([]*SeasonalOfferingBlock, 0, len(blockIDs))
		for _, blockID := range blockIDs {
			if block, exists := blockMap[blockID]; exists {
				rowBlocks = append(rowBlocks, block)
			}
		}

		layoutResult[rowIndex] = rowBlocks
	}

	// remove empty rows
	var resultRows [][]*SeasonalOfferingBlock
	for _, row := range layoutResult {
		if len(row) > 0 {
			resultRows = append(resultRows, row)
		}
	}

	return resultRows, nil
}
