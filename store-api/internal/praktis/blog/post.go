package blog

import (
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"time"
)

type BlogPostImageUrl string

func (b BlogPostImageUrl) String() string {
	return praktis.GetPraktisStore().GetMedialUrl(string(b))
}

type BlogPostEntity struct {
	PostId          int              `json:"news_id" gorm:"column:news_id"`
	Title           string           `json:"title" gorm:"column:title"`
	UrlKey          string           `json:"url_key" gorm:"column:url_key"`
	Summary         string           `json:"short_content" gorm:"column:short_content"`
	Content         string           `json:"full_content" gorm:"column:full_content"`
	PreviewImageUrl BlogPostImageUrl `json:"image_short_content" gorm:"column:image_short_content"`
	MainImageUrl    BlogPostImageUrl `json:"image_full_content" gorm:"column:image_full_content"`
	PublishedAt     time.Time        `json:"news_time" gorm:"column:news_time"`
	MetaKeywords    string           `json:"meta_keywords" gorm:"column:meta_keywords"`
	MetaDescription string           `json:"meta_description" gorm:"column:meta_description"`
}

func (b BlogPostEntity) TableName() string {
	return "clnews_news"
}

func (b BlogPostEntity) ToModel() *model.BlogPost {
	canonical := "/news/" + b.UrlKey

	return &model.BlogPost{
		URLKey:          canonical,
		Title:           b.Title,
		Summary:         b.Summary,
		PreviewImageURL: b.PreviewImageUrl.String(),
		Content:         b.Content,
		MainImageURL:    b.PreviewImageUrl.String(),
		PublishedAt:     b.PublishedAt.Format("02/01/2006"),
	}
}

func (b BlogPostEntity) LoadByIdentifier() (BlogPostEntity, error) {
	var result BlogPostEntity
	err := praktis.GetDbClient().
		Model(result).
		Where("url_key = ?", b.UrlKey).
		Scan(&result).Error

	return result, err
}

const BlogPostsPerPage = 18

type BlogPostCollection []BlogPostEntity

type GetCollectionParams struct {
	Page       int
	PageSize   int
	Order      model.SortDirection
	CategoryId int
}

func GetBlogPostCollection(p GetCollectionParams) (BlogPostCollection, error) {
	var collection BlogPostCollection

	order := core_utils.IF(
		p.Order != model.SortDirectionAsc,
		model.SortDirectionDesc,
		model.SortDirectionAsc,
	)

	postsPerPage := core_utils.IF(
		p.PageSize > 0,
		p.PageSize,
		BlogPostsPerPage,
	)

	db := praktis.GetDbClient().
		Table(BlogPostEntity{}.TableName())

	if p.CategoryId > 0 {
		db = db.
			Joins("inner join clnews_news_category on clnews_news_category.news_id = clnews_news.news_id").
			Where("clnews_news_category.category_id = ?", p.CategoryId)
	}

	err := db.
		Offset((int(p.Page) - 1) * postsPerPage).
		Limit(postsPerPage).
		Order("news_time " + string(order)).
		Find(&collection).Error

	return collection, err
}

func GetTotalPages() int64 {
	var totalPosts int64

	praktis.GetDbClient().
		Model(BlogPostEntity{}).
		Count(&totalPosts)

	total := totalPosts / BlogPostsPerPage
	if totalPosts%BlogPostsPerPage > 0 {
		total++
	}

	return total
}
