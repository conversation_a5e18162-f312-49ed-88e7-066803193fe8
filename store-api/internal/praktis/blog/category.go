package blog

import (
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
)

type BlogCategoryTree []BlogCategoryEntity

type BlogCategoryEntity struct {
	ID              int    `json:"category_id" gorm:"column:category_id"`
	Title           string `json:"title" gorm:"column:title"`
	UrlKey          string `json:"url_key" gorm:"column:url_key"`
	MetaDescription string `json:"meta_description" gorm:"column:meta_description"`
	MetaKeywords    string `json:"meta_keywords" gorm:"column:meta_keywords"`
	Level           int    `json:"level" gorm:"column:level"`
	ParentId        int    `json:"parent_id" gorm:"column:parent_id"`

	Children BlogCategoryTree `json:"children" gorm:"-"`
}

func (BlogCategoryEntity) TableName() string {
	return "clnews_category"
}

func (b BlogCategoryEntity) ToModel() *model.BlogCategory {
	var children []*model.BlogCategory
	for _, child := range b.Children {
		children = append(children, child.ToModel())
	}

	canonicalURL := "/blog/category/" + b.UrlKey

	return &model.BlogCategory{
		ID:       b.ID,
		URLKey:   canonicalURL,
		Name:     b.Title,
		Children: children,
		Posts:    []*model.BlogPost{},
	}
}

func (b BlogCategoryEntity) LoadByIdentifier() (BlogCategoryEntity, error) {
	var result BlogCategoryEntity
	err := praktis.GetDbClient().
		Model(result).
		Where("url_key = ?", b.UrlKey).
		Scan(&result).Error

	var children []BlogCategoryEntity
	praktis.GetDbClient().
		Model(BlogCategoryEntity{}).
		Where("parent_id = ?", result.ID).
		Scan(&children)

	result.Children = children

	return result, err
}

func (c BlogCategoryTree) AddCategory(category BlogCategoryEntity) BlogCategoryTree {
	if category.Level < 1 {
		return append(c, category)
	} else {
		if category.ParentId != 0 {
			for i, cat := range c {
				if cat.ID == category.ParentId {
					c[i].Children = append(c[i].Children, category)
					return c
				} else {
					c[i].Children = c[i].Children.AddCategory(category)
				}
			}
		}
	}

	return c
}

func GetBlogCategoryTree() BlogCategoryTree {
	var allCategories []BlogCategoryEntity
	praktis.GetDbClient().
		Model(BlogCategoryEntity{}).
		Order("level, sort_order ASC").
		Scan(&allCategories)

	var categoryTree = make(BlogCategoryTree, 0)

	for _, category := range allCategories {
		categoryTree = categoryTree.AddCategory(category)
	}

	return categoryTree
}
