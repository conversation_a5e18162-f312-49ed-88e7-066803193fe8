type HtmlWidget {
  html: String
}

type CategoryLinkWidget {
  title: String!
  url: Link!
  image: Image!
}

# carousel

type CarouselSlide {
  title: String!
  description: String!
  link: Link

  price: Price
  priceLabels: MagentoLabels

  image: Image!
  features: [Image!]
  brand: Brand
}

type CarouselWidget {
  identifier: String!
  slides: [CarouselSlide!]!
}

# TilesWidget

type WidgetCategory {
  image: Image
  title: String!
  link: Link!
}

type TilesWidget {
  identifier: String!
  title: String!
  subtitle: String!
  # link property can only be required
  viewMore: Link
  rows: [TilesRow!]!
}

type TilesRow {
  cols: [Tile!]!
}

union Tile = ImageTile | DoubleImageTile | CategoryTile

type ImageTile {
  image: Image!
  bgColor: String!
  position: TileContentPosition!
  content: TileContent!
}

type DoubleImageTile {
  imageOnePosition: DoubleImageTilePosition!
  imageOne: Image!
  contentOne: TileContent!
  imageTwoPosition: DoubleImageTilePosition!
  imageTwo: Image!
  contentTwo: TileContent!
}

type CategoryTile {
  title: String!
  image: Image!
  bgColor: String!
  textColor: String!
  categories: [Link!]!
}

type TileContent {
  title: String!
  text: String
  link: Link!
  icon: Image
}

enum DoubleImageTilePosition {
  LEFT
  RIGHT
}

enum TileContentPosition {
  LEFT
  LEFT_TOP
  LEFT_BOTTOM
  RIGHT
  RIGHT_TOP
  RIGHT_BOTTOM
}

# FeaturesWidget

type FormData {
  title: String!
  text: String!
  formId: String!
}

type ServiceWidget {
  title: String!
  subtitle: String!
  link: Link!
  services: [Service!]!
  form: FormData!
  availableStores: [PraktisStore!]!
}

# NewsWidget

type Article {
  title: String!
  description: String!
  link: Link!
  image: Image!
}

type NewsWidget {
  identifier: String!
  title: String!
  subtitle: String!
  link: Link!
  articles: [Article!]!
}