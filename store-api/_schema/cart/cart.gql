extend type Query {
  getCart(cartToken: String!): StoreCart!
  getNewCart: StoreCart!
}

type StoreCart {
  id: ID!
  token: String!
  # cart data
  storeCode: String!
  currency: String!
  couponCode: String!
  note: String!
  customer: OrderCustomer!
  # items
  items: [StoreCartItem!]!
  # shipping
  shipping: CartShipping! @goField(forceResolver: true)
  # payment and totals
  paymentMethod: String!
  totals: [CartTotal!]!
}

type CartShipping {
  availableMethods: [ShippingMethodType!]!
  availableIn: [PraktisStore!]!
  hasFreeShipping: Boolean!
  freeShippingAfter: Int
  minAmountForFreeShippingMessage: Int
  selectedMethod: String!
  address: ShippingAddress
}

#########################################
# Cart Item
#########################################

type StoreCartItem {
  id: ID!
  sku: String!
  labels: StoreCartItemLabels!
  product: Product! @goField(forceResolver: true)
  # quantity
  baseQty: Float!
  # price
  discountAmount: Price!
  discountPercent: Float!
  price: Price!
  priceWithoutDiscount: Price!
  rowTotal: Price!
  rowTotalWithoutDiscount: Price!
}

type StoreCartItemLabels {
  freeShipping: Boolean!
  usePallet: Boolean!
}

#########################################
# Cart Shipping
#########################################

type AvailableShippingMethod {
  method: ShippingMethod!
  errorMsg: String!
  price: Price!
}

enum ShippingMethodType {
  NONE
  ECONT_TO_OFFICE
  ECONT_TO_ADDRESS
  TO_STORE
}

type ShippingMethod {
  name: String!
  code: String!
}

type ShippingAddress {
  id: ID!
  skus: [String!]!
  method: ShippingMethodType!
  officeCode: String!
  storeCode: String!
  cityId: String!
  city: String!
  postCode: String!
  street: String!
}

type PaymentMethod {
  name: String!
  code: String!
  extraContent: String!
}

enum CartTotalCode {
  OTHER
  DISCOUNT_TOTAL
  SHIPPING_TOTAL
  SUB_TOTAL
  GRANT_TOTAL
}

type CartTotal {
  code: CartTotalCode!
  label: String!
  amount: Price!
  order: Int!
}


