extend type Mutation {
  cartApplyCoupon(cartToken: String!, couponCode: String!): StoreCart!
  # checkout utils
  cartCalculateTotals(cartToken: String!): StoreCart!
  # step 1
  cartSaveClient(cartToken: String!, data: ClientInput!): StoreCart! # step1
  # step 2
  cartAvailableShippingMethods(cartToken: String!, data: ShippingInput!): [AvailableShippingMethod!]!
  cartSaveShipping(
    cartToken: String!,
    shippingMethodCode: String!,
    data: ShippingInput!
  ): StoreCart!
  # step 3
  cartAvailablePaymentMethods(cartToken: String!): [PaymentMethod!]!
  cartSavePayment(cartToken: String!, paymentMethodCode: String!): StoreCart!
  # order
  placeOrder(cartToken: String!, data: NewOrderInput!): PlaceOrderResponse
}

input ClientInput {
  firstName: String!
  lastName: String!
  phone: String!
  email: String!
  registerOnOrder: Boolean!
  password: String!
  invoice: InvoiceInput
}

input InvoiceInput {
  type: InvoiceType!
  city: String!
  address: String!
  company: CompanyInvoiceInput
  individual: IndividualInvoiceInput
}

input CompanyInvoiceInput {
  name: String!
  mol: String!
  eik: String!
  vat: String!
}

input IndividualInvoiceInput {
  name: String!
  egn: String!
  vat: String!
}

input ShippingInput {
  type: ShippingMethodType!
  cityId: String
  city: String!
  postCode: String!
  address: String!
  officeCode: String
  storeCode: String
}

input NewOrderInput {
  client: ClientInput!
  shipping: ShippingInput!
  shippingMethodCode: String!
  paymentMethodCode: String!
  promoCode: String
  note: String
}

type PlaceOrderResponse {
  orderNumber: String!
  status: OrderStatus!
  redirect: OrderRedirect!
  order: StoreOrder!
}

type OrderRedirect {
  url: String!
  data: [MapValue!]!
}
