extend type Query {
  getStaticContent: StaticContent!
  getStaticBlock(identifier: String!): CMSBlock!
  getStaticPage(pageId: ID!): CMSPage!
  getBrands(featured: Boolean): [Brand!]!
  getNotifications: [NotificationMessage!]!
  getStoreLogo: StoreLogo!
}

type Brand {
  name: String!
  image: Image!
  url: Link!
}

type CMSBlock {
  identifier: String!
  title: String!
  content: String!
}

type ApiKeys {
  googleMaps: String!
  googleLoginClientId: String!
  googleRecaptchaKey: String!
  facebookLoginAppId: String!
}

type HeaderData {
  scripts: [String!]!
}

type PageMessages {
  newsletter: String!
  sendInquiryMessage: String!
}

type StaticContent {
  apiKeys: ApiKeys!
  header: HeaderData!
  menu: Menu!
  footer: Footer!
  store: StoreInfo!
  messages: PageMessages!
  gdpr: GDPRConfig
  logo: StoreLogo
}

type GDPRConfig {
  rootId: String!
  modal: GDPRModalConfig!
  pixelId: String!
  gtagId: String!
  extraGTagsIds: [String!]!
  clarityId: String!
}

enum GDPRGrants {
  ad_storage
  ad_user_data
  ad_personalization
  analytics_storage
}

type GDPRModalConfig {
  title: String!
  content: String!
  cookieGroups: [CookieGroup!]!
}

type CookieGroup {
  id: String!
  title: String!
  content: String!
  grants: [GDPRGrants!]!
  cookiePatterns: [String!]!
  vendors: GDPRCookieVendors!
}

enum CookieVendorKey {
  google
  facebook
  clarity
  store
}

type GDPRCookieVendors {
  keys: [CookieVendorKey!]!
  config: [GDPRCookieVendorConfig!]!
}

type GDPRCookieVendorConfig {
  id: String!
  name: String!
  url: String!
  cookieCount: Int!
  cookieDetails: [GDPRCookieDetails]!
}

type GDPRCookieDetails {
  id: String!
  name: String!
  description: String!
  type: String!
  expiration: String!
}

type StoreLogo {
  url: String!
  width: Int
  height: Int
  alt: String
}


