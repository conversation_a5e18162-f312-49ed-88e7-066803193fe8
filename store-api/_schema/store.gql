extend type Mutation {
  storeSendContactMessage(input: ContactInput!): Boolean! @hasValidCaptcha
  storeSendInquiry(input: StoreInquiryInput!): Boolean! @hasValidCaptcha
  # newsletter
  storeNewsletterSubscribe(email: String!): <PERSON><PERSON><PERSON>! @hasValidCaptcha
  storeNewsletterUnsubscribe(id: ID!, code: String!): Boolean!
}

enum InquiryType {
  Personal
  Company
}

input StoreInquiryInput {
  store: String!
  name: String!
  email: String!
  phone: String!
  type: InquiryType!
  message: String!
}

input ContactInput {
  name: String!
  email: String!
  telephone: String!
  comment: String!
}