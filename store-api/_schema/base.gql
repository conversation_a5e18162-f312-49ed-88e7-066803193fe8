type Query {
  health: String
}

type Mutation {
  health: String
}

type AuthToken {
  token: String
}

enum ActionResultCode {
  SUCCESS
  ERROR
  QUEUED
  WAITING
}

type ActionResult {
  code: ActionResultCode!
  message: String
}

enum NotificationType {
  SUCCESS
  ERROR
  WARNING
  INFO
}

type NotificationMessage {
  title: String!
  message: String!
  type: NotificationType!
}

type CollectionSort {
  value: String!
  dir: SortDirection!
}

type SortField {
  value: String!
  label: String!
  dir: SortDirection!
}

enum SortDirection {
  ASC
  DESC
}

type MapLocation {
  lat: Float!
  lng: Float!
}

input KeyVal {
  key: String!
  value: String!
}

type MapValue {
  key: String!
  value: String!
}

type Link {
  href: String!
  text: String!
  title: String
}

type Image {
  src: String!
  mobileSrc: String
  alt: String
  title: String
}

enum Positions {
  LEFT_TOP
  LEFT_BOTTOM
  RIGHT_TOP
  RIGHT_BOTTOM
}

