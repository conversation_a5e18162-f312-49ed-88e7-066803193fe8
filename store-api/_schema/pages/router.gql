extend type Query {
  route(url: String!, query: [QueryParam]): Page!
  routeMeta(url: String!, query: [QueryParam]): PageMeta!
}

union PageData = CMSPage | CatalogPage | ProductPage | ErrorData

type Page {
  status: PageStatus!
  breadcrumbs: [Breadcrumb!]!
  data: PageData
}

input QueryParam {
  name: String!
  value: String!
}

type Breadcrumb {
  label: String!
  url: String!
  siblings: [Breadcrumb!]
  children: [Breadcrumb!]
}

type PageStatus {
  statusCode: Int!
  redirectUrl: String
  error: String
}

type ErrorData {
  code: Int!
  message: String!
}
