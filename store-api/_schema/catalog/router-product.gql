union Product = SimpleProduct | BundleProduct

type ProductPage {
  product: Product! @goField(forceResolver: true)
  boughtTogether: [SimpleProduct!] @goField(forceResolver: true)
  staticBlocks: [CMSBlock!] @goField(forceResolver: true)
  widgets: [ProductsSliderWidget!] @goField(forceResolver: true)
}

type ProductStock
{
  qty: Float!
  minQty: Float!
  blockForSale: Boolean!
  zeronSiteStatus: String!
  zeronBlockedDelivery: Boolean!
  manageStock: Boolean!
  inStock: Boolean!
  hasImages: Boolean!
  showOutOfStock: Boolean!
}

type SimpleProduct {
  id: ID!
  sku: String!
  name: String!
  price: ProductPrice! @goField(forceResolver: true)
  urlKey: String!
  shortDescription: String!
  description: String!

  stock: ProductStock! @goField(forceResolver: true)

  brand: Brand
  measures: ProductMeasures!

  labels: MagentoLabels!
  energyLabel: EnergyLabel

  image: Image!
  gallery: [GalleryImage!]!
  videos: [ProductVideo!]

  skuAvailability: [StoreAvailabilityItem!]! @goField(forceResolver: true)
}

type BundleProduct {
  id: ID!
  sku: String!
  name: String!
  price: ProductPrice! @goField(forceResolver: true)
  urlKey: String!
  shortDescription: String!
  description: String!

  stock: ProductStock

  brand: Brand
  labels: MagentoLabels!

  image: Image!
  gallery: [GalleryImage!]!
  videos: [ProductVideo!]

  bundled: [SimpleProduct!]!

  skuAvailability: [StoreAvailabilityItem!]! @goField(forceResolver: true)
}

type GalleryImage {
  image: Image!
  position: Int!
}

type ProductVideo {
  id: ID!
  title: String!
  description: String
  url: String!
  videoType: String!
  thumbnail: String
  position: Int!
}

type Price {
  value: Float!
  currency: String!
}

type ProductPrice {
  price: Price!
  special: Price
  specialFrom: String
  specialTo: String
}

type EnergyLabel {
  image: Image!
  labelUrl: String
  infoUrl: String
}

type MagentoLabels {
  warrantyMonths: Int!
  fromBrochure: Boolean
  freeDelivery: Boolean
  freeDeliveryUntil: String
  buyCheap: Boolean
  other: [String!]
}

type ProductMeasures {
  base: String!
  secondary: String!
  secondaryQty: Float!
  secondaryMeasureUsed: Boolean!
}

type StoreAvailabilityItem {
  available: AvailabilityStatus!
  sample: Boolean!
  store: PraktisStore!
}

enum AvailabilityStatus {
  AVAILABLE
  UNAVAILABLE
  LIMITED_AVAILABILITY
  INDIVIDUAL_ORDER
}
