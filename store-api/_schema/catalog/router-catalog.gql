union CatalogCategory = Category | SplashPage | SearchCategory
union CategoryWidget = CategoryLinkWidget

enum CatalogLayout {
  LANDING_PAGE
  PRODUCT_CATALOG
}

enum FilterRenderType {
  LIST
  SLIDER
}

type CatalogPage {
  layout: CatalogLayout!
  state: CatalogState! @goField(forceResolver: true)
  category: CatalogCategory!
  products: [Product!]! @goField(forceResolver: true)
}

type Category {
  id: ID!
  url: String!
  name: String!
  description: String
  image: Image
  icon: Image
  banner: CategoryBanner @goField(forceResolver: true)
  widgets: [CategoryWidget!] @goField(forceResolver: true)
  children: [Category!] @goField(forceResolver: true)
}

type SplashPage {
  id: ID!
  url: String!
  title: String!
  description: String!
  image: Image!
}

type CatalogState {
  filters: Filters!
  pager: Pager!
  availableSorts: [SortField!]!
  sort: CollectionSort!
}

type Pager {
  page: Int!
  totalPages: Int!
  pageSize: Int!
  totalItems: Int!
}

type AttributeOption {
  label: String!
  value: String!
  order: Int!
}

type AvailableFilter {
  label: String!
  attributeCode: String!
  options: [AttributeOption!]!
  type: FilterRenderType!
  requestVar: String!
  position: Int!
}

type AppliedFilter {
  attributeCode: String!
  requestVar: String!
  label: String!
  value: String!
}

type Filters {
  available: [AvailableFilter!]!
  applied: [AppliedFilter!]!
}

type CategoryBanner {
  image: Image!
  url: String!
}
