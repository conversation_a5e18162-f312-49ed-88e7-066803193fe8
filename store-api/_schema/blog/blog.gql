extend type Query {
  getBlogPosts(page: Int!, size: Int!): MagentoBlog!
  getBlogPost(identifier: String!): BlogPost!
  getFeaturedBlogPost: BlogPost!
#  getBlogCategories: [BlogCategory!]!
#  getBlogCategory(urlKey: String!, page: Int): BlogCategory!
}
#
type BlogCategory {
  id: Int!
  urlKey: String! # URL key
  name: String!
  children: [BlogCategory!]!
  posts: [BlogPost!]!
}

type MagentoBlog {
  posts: [BlogPost!]!
  currentPage: Int!
  totalPages: Int!
}

type BlogPost {
  urlKey: String! # URL key
  title: String!
  summary: String!
  previewImageUrl: String!
  content: String!
  mainImageUrl: String!
  publishedAt: String!
}