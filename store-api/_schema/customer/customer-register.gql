extend type Mutation {
  # store account
  customerRegister(data: CustomerRegistrationData!): RegistrationResponse! @hasValidCaptcha
  customerPasswordForgot(email: String!): Boolean! @hasValidCaptcha
  customerPasswordReset(customerId: ID!, resetToken: String!, password: String!): Boolean! @hasValidCaptcha
}

type RegistrationResponse {
  create: Boolean!
  requireConfirmation: Boolean!
}

input CustomerRegistrationData {
  email: String!
  firstname: String!
  lastname: String!
  password: String!
  phone: String!
  newsletterSubscribe: Boolean!
  invoice: InvoiceInput
}
