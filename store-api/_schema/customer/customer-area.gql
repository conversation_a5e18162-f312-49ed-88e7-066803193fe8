extend type Query {
  customerData: Customer! @hasValidToken
  customerWishlist: CustomerWishlist! @hasValidToken
}

extend type Mutation {
  # wishlist
  customerWishlistAdd(sku: String!): CustomerWishlist! @hasValidToken
  customerWishlistRemove(sku: String!): CustomerWishlist! @hasValidToken
  # customer info
  customerUpdateInfo(data: CustomerUpdateInput): Customer! @hasValidToken
  customerUpdatePassword(
    oldPassword: String!,
    newPassword: String!
  ): Customer! @hasValidToken
  # customer address
  customerAddressAdd(data: CustomerAddressInput!): [CustomerAddress!]! @hasValidToken
  customerAddressUpdate(addressID: ID!, data: CustomerAddressInput!): [CustomerAddress!]! @hasValidToken
  customerAddressRemove(addressID: ID!): [CustomerAddress!]! @hasValidToken
}

input CustomerUpdateInput {
  firstName: String
  lastName: String
  email: String
  newsletterSubscribe: Boolean
  defaultBillingAddressID: ID
  defaultShippingAddressID: ID
}

input CustomerAddressInput {
  isDefaultBilling: Boolean
  isDefaultShipping: Boolean
  # customer data
  firstName: String!
  lastName: String!
  phone: String!
  companyName: String
  # address data
  city: String!
  cityID: String!
  postCode: String!
  street: String!
}
