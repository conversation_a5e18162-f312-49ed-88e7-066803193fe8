enum InvoiceType {
  company
  personal
}

type Customer
{
  id: ID!
  token: String!
  isSubscribed: Boolean!
  firstName: String!
  lastName: String!
  email: String!
  defaultBillingAddressID: ID
  defaultShippingAddressID: ID
  addresses: [CustomerAddress!]! @goField(forceResolver: true)
  orders: [StoreOrder!]! @goField(forceResolver: true)
  wishlist: CustomerWishlist!
  # social login
  social: SocialLogins!
}

type CustomerWishlist {
  skus: [String!]!
  products: [Product!]! @goField(forceResolver: true)
}

type CustomerAddress {
  id: ID!
  # customer data
  firstName: String!
  lastName: String!
  phone: String!
  # invoice data
  companyName: String
  # address data
  country: String!
  city: String!
  cityID: String!
  postCode: String!
  street: String!
}

enum LoginProvider {
  Facebook
  Google
}

type SocialLogins {
  google: SocialAccountInfo
  facebook: SocialAccountInfo
}

type SocialAccountInfo {
  ID: String!
  email: String!
  verifiedEmail: Boolean!
  name: String!
  pictureURL: String!
}

type CustomerInvoice {
  id: ID!
  type: InvoiceType!
  city: String!
  address: String!
  company: CompanyInvoice
  individual: IndividualInvoice
}

type CompanyInvoice {
  name: String!
  mol: String!
  eik: String!
  vat: String!
}

type IndividualInvoice {
  name: String!
  egn: String!
  vat: String!
}
