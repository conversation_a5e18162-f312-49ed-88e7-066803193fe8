extend type Query {
  getEcontCity(country: String!): [EcontCity]!
  getEcontOffice(cityID: String!): [EcontOffice]!
}

type EcontCity {
  id: ID!
  country: EcontCountry!
  type: String!
  name: String!
  nameEn: String!
  postCode: String!
  regionName: String!
  regionNameEn: String!
  regionCode: String!
}

type EcontCountry {
  id: ID!
  code2: String!
  code3: String!
  name: String!
  nameEn: String!
  isEU: Boolean!
}

type EcontOffice {
  id: ID!
  code: String!
  name: String!
  nameEn: String!
  phones: [String!]!
  isAPS: Boolean!
  isMPS: Boolean!
  adderess: EcontOfficeAddress!
}

type EcontOfficeAddress {
  city: EcontCity!
  fullAddress: String!
  fullAddressEn: String!
  location: EcontOfficeLocation!
}

type EcontOfficeLocation {
  latitude: Float!
  longitude: Float!
}
