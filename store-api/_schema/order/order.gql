type OrderStatus {
  label: String!
  code: String!
}

type OrderCustomer {
  firstName: String!
  lastName: String!
  email: String!
  phone: String!
  invoice: CustomerInvoice
}

type StoreOrder {
  id: ID!
  incrementId: String!
  state: String!
  status: OrderStatus!
  couponCode: String!
  protectCode: String!
  createAt: String!
  items: [OrderItem!]! @goField(forceResolver: true)
  currency: String!
  totals: [CartTotal!]!
  shippingMethod: ShippingMethod!
  paymentMethod: PaymentMethod!
  note: String!
  shippingAddress: OrderAddress! @goField(forceResolver: true)
  invoice: CustomerInvoice @goField(forceResolver: true)
}

type OrderItem {
  id: ID!
  sku: String!
  product: Product! @goField(forceResolver: true)
  # quantity
  baseQty: Float!
  # price
  discountAmount: Price!
  discountPercent: Float!
  price: Price!
  priceWithoutDiscount: Price!
  rowTotal: Price!
  rowTotalWithoutDiscount: Price!
}

type OrderAddress {
  id: ID!
  firstName: String!
  lastName: String!
  email: String!
  telephone: String!
  city: String!
  postcode: String!
  street: String!
}