type PraktisStore
@entity(
  table: "theme_praktis_store",
  toModel: {excludeFields: ["active", "locationData", "scheduleData"]}
)
@cacheable(
  ttlMin: 60,
  key: "fmt.Sprintf(\"praktis_store:warehause:%s\", p.<PERSON>)"
  excludeFields: ["businessHours", "location", "descriptionArea", "noticeArea", "gallery", "services", "availableSkus"]
)
{
  ID: ID! @id
  identity: String! @field
  name: String! @field
  city: String! @field
  address: String! @field
  phone: String! @field
  email: String! @field
  displayOrder: Int! @field
  acceptOrders: Boolean! @field

  warehouseCode: String! @field
  warehouseID: ID! @field

  descriptionAreaData: String! @field(private: true)
  descriptionArea: DescriptionArea @transform(src: "descriptionAreaData", t: ["toStoreDescription"])

  transportInformation: String! @field

  metaTitle: String! @field
  metaDescription: String! @field
  metaKeywords: String! @field

  locationData: String! @field(filterable: false, private: true)
  location: MapLocation! @transform(src: "locationData", t: ["toMapLocation"])
  # src: "scheduleData" skipped because it's assumed from the field name
  businessHoursData: String! @field(filterable: false, private: true)
  businessHours: [StoreSchedule!]! @transform(t: ["toBusinessHours"])

  virtualTour: String! @field
  gallery: [StoreImage!]! @refList(id: "PraktisStoreID", refId: "ID") @goField(forceResolver: true)
  services: [Service!]! @goField(forceResolver: true)
}

type DescriptionArea {
  title: String!
  content: String!
  formID: String
  backgroundImage: String!
}

type StoreSchedule {
  day: String!
  open: String!
  close: String!
}

type StoreImage @entity(
  table: "theme_praktis_store_gallery"
  toModel: {excludeFields: ["ID"]}
)
@cacheable(ttlMin: 60)
{
  ID: ID! @id
  position: Int! @field
  isPrimary: Boolean! @field
  praktisStoreID: ID! @field(private: true)
  src: String! @field @transform(src: "src" t: ["toMediaUrl"])
  mobileSrc: String @field @transform(src: "mobileSrc" t: ["toMediaUrlPointer"])
  alt: String @field
  title: String @field
}

type Warehouse @entity(
  table: "stenik_zeron_warehouse"
) {
  WarehouseID: ID! @id
  WarehouseCode: Int! @field
  Name: String! @field
  Address: String! @field
  PostalCode: String! @field(col: "postcode")
}

