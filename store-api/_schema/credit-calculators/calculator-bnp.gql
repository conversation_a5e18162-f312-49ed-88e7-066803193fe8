extend type Query {
  getCreditCalculatorBNPParibas(
    sku: String!,
    downPayment: Float!,
    qty: Int!,
  ): [BNPVariantGroup!]!

  getCreditCalculatorBNPParibasForQuote(
    cartToken: String!,
    downPayment: Float!,
  ): [BNPVariantGroup!]!

  getBNPPricingSchemes(
    goodTypeIds: String!,
    principal: Float!,
    downPayment: Float!,
  ): [BNPPricingScheme!]!

  calculateBNPLoan(
    cartToken: String!,
    downPayment: Float!,
    pricingVariantId: Int!,
  ): LoanCalculation!

  getBNPGoodCategories: [BNPGoodCategory!]!
  getBNPGoodTypes(categoryId: String): [BNPGoodType!]!
}

extend type Mutation {
  cartSaveBNPPayment(
    cartToken: String!,
    paymentData: BNPPaymentInput!,
  ): StoreCart!

  submitBNPApplication(
    orderNumber: String!,
  ): Boolean!
}

type BNPVariantGroup {
  schemeId: String!
  variants: [PricingVariant!]!
}

type PricingVariant {
  id: String!
  apr: String!
  correctDownpaymentAmount: String!
  installmentAmount: String!
  maturity: String!
  nir: String!
  pricingSchemeId: String!
  pricingSchemeName: String!
  processingFeeAmount: String!
  totalRepaymentAmount: String!

  # Legacy field names for Magento template compatibility
  installment: String!
  total_repayment: String!
}

type BNPPricingScheme {
  id: String!
  name: String!
}

type LoanCalculation {
  apr: String!
  correctDownpaymentAmount: String!
  installmentAmount: String!
  maturity: String!
  nir: String!
  pricingSchemeId: String!
  pricingSchemeName: String!
  pricingVariantId: String!
  processingFeeAmount: String!
  totalRepaymentAmount: String!
}

input BNPPaymentInput {
  downPayment: Float!
  pricingVariantId: Int!
  customerData: BNPCustomerDataInput!
}

input BNPCustomerDataInput {
  firstName: String!
  lastName: String!
  phone: String!
  email: String!
  address: String!
  city: String!
  postCode: String!
  egn: String
  companyName: String
  eik: String
  mol: String
}

type BNPGoodCategory {
  id: ID!
  name: String!
}

type BNPGoodType {
  id: ID!
  name: String!
  categoryId: ID!
}
