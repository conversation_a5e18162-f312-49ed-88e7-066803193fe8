# Test queries for BNP Good Categories and Types

# Query to get all good categories
query GetBNPGoodCategories {
  getBNPGoodCategories {
    id
    name
  }
}

# Query to get all good types
query GetAllBNPGoodTypes {
  getBNPGoodTypes {
    id
    name
    categoryId
  }
}

# Query to get good types for a specific category
query GetBNPGoodTypesForCategory($categoryId: ID) {
  getBNPGoodTypes(categoryId: $categoryId) {
    id
    name
    categoryId
  }
}

# Example variables for the specific category query:
# {
#   "categoryId": "1"
# }
