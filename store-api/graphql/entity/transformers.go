package entity

import (
	"encoding/json"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"strconv"
	"strings"
)

func toMapLocation(locationData string) model.MapLocation {
	parts := strings.Split(locationData, "|")
	if len(parts) != 2 {
		return model.MapLocation{
			Lat: 0,
			Lng: 0,
		}
	}

	lat, _ := strconv.ParseFloat(parts[0], 64)
	lng, _ := strconv.ParseFloat(parts[1], 64)

	return model.MapLocation{
		Lat: lat,
		Lng: lng,
	}
}

func toBusinessHours(scheduleData string) []*model.StoreSchedule {
	schedule := make([]*model.StoreSchedule, 7)
	for i := 0; i < 7; i++ {
		schedule[i] = &model.StoreSchedule{
			Day:   "",
			Open:  "",
			Close: "",
		}
	}

	var data = map[string]interface{}{}
	err := json.Unmarshal([]byte(scheduleData), &data)
	if err != nil {
		core_utils.ErrorWarning(err)
		return []*model.StoreSchedule{}
	}

	for key, value := range data {
		index := 0
		switch key {
		case "mon":
			index = 0
			key = "Понеделник"
		case "tue":
			index = 1
			key = "Вторник"
		case "wed":
			index = 2
			key = "Сряда"
		case "thu":
			index = 3
			key = "Четвъртък"
		case "fri":
			index = 4
			key = "Петък"
		case "sat":
			index = 5
			key = "Събота"
		case "sun":
			index = 6
			key = "Неделя"
		}

		daySchedule := &model.StoreSchedule{
			Day: key,
		}

		from, ok := value.(map[string]interface{})["from"]
		if ok {
			if fromS, ok := from.(string); ok {
				daySchedule.Open = fromS
			}
		}
		to, _ := value.(map[string]interface{})["to"]
		if ok {
			if toS, ok := to.(string); ok {
				daySchedule.Close = toS
			}
		}

		schedule[index] = daySchedule
	}

	return schedule
}

func toStoreDescription(descriptionAreaData string) model.DescriptionArea {
	// TODO - get dynamic data
	return model.DescriptionArea{
		Title:           "Изготвяне на оферта",
		Content:         descriptionAreaData,
		FormID:          core_utils.ToStringPointer("send_request"),
		BackgroundImage: "https://praktis.bg/public/logo.png",
	}
}

func toMediaUrl(src string) string {
	if src == "" {
		return ""
	}

	return magento_core.GetStoreClient().GetStore().GetMedialUrl(src)
}

func toMediaUrlPointer(src *string) string {
	if src == nil || *src == "" {
		return ""
	}

	return magento_core.GetStoreClient().GetStore().GetMedialUrl(*src)
}
