// GEN[0.0.1 | prak-dev-1.0.0]:dt:2024-10-10 15:40:03
package entity

//start:imports
import (
	"github.com/siper92/api-base/api_entity"
	"praktis.bg/store-api/graphql/model"
	"strings"
	"time"
)

//end:imports

//start:warehouse_entity: 60318fb090d0eaa488b75a07a65911e5b817f6bfe03aabd4a03dd4b8cb71ce03
var _ api_entity.Entity = (*WarehouseEntity)(nil)

type WarehouseEntity struct {
	WarehouseID   int64 `gorm:"primaryKey"`
	Address       string
	Name          string
	PostalCode    string `gorm:"column:postcode"`
	WarehouseCode int
	CreatedAt     time.Time `gorm:"autoCreateTime"`
	UpdatedAt     time.Time `gorm:"autoUpdateTime"`
}

type WarehouseEntityCollection []*WarehouseEntity

//start:gorm
func (w *WarehouseEntity) TableName() string {
	return "stenik_zeron_warehouse"
}

func (w *WarehouseEntity) GetID() int64 {
	return w.WarehouseID
}

func (w *WarehouseEntity) GetFilterableFields() map[string]bool {
	return map[string]bool{
		"address":       true,
		"name":          true,
		"postalcode":    true,
		"warehousecode": true,
	}
}

func (w *WarehouseEntity) IsFilterable(field string) bool {
	val, ok := w.GetFilterableFields()[strings.ToLower(field)]
	return ok && val
}

func (w *WarehouseEntity) GetPrivateFields() map[string]bool {
	return map[string]bool{}
}

func (w *WarehouseEntity) IsPrivateField(field string) bool {
	val, ok := w.GetPrivateFields()[strings.ToLower(field)]
	return ok && val
}

//end:gorm

var _ api_entity.ConvertToModel[*model.Warehouse] = (*WarehouseEntity)(nil)

func (w *WarehouseEntity) ToModel() *model.Warehouse {
	result := &model.Warehouse{}
	result.WarehouseID = w.WarehouseID
	result.WarehouseCode = w.WarehouseCode
	result.Name = w.Name
	result.Address = w.Address
	result.PostalCode = w.PostalCode

	return result
}

//end:warehouse_entity
