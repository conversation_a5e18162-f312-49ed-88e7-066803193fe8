// GEN[0.0.1 | prak-dev-1.0.0]:dt:2024-05-20 22:11:38
package entity

//start:imports
import (
	"fmt"
	"github.com/siper92/api-base/api_entity"
	"gorm.io/gorm"
	praktis "praktis.bg/store-api/internal/praktis"
)

//end:imports

//start:repository: 80796c10826df2819c68f6a3bef1cb47b9fb79ed9c2af6ef9d005d5927fe2fc3
var _ api_entity.HasRepository[*PraktisStoreEntity] = (*PraktisStoreEntity)(nil)

func (p *PraktisStoreEntity) NewRepository() api_entity.Repository[*PraktisStoreEntity] {
	return &PraktisStoreEntityRepository{}
}

var _ api_entity.Repository[*PraktisStoreEntity] = (*PraktisStoreEntityRepository)(nil)

type PraktisStoreEntityRepository struct {
}

func NewPraktisStoreEntityRepository() *PraktisStoreEntityRepository {
	return &PraktisStoreEntityRepository{}
}

func (repo *PraktisStoreEntityRepository) New() (result *PraktisStoreEntity) {
	return &PraktisStoreEntity{}
}

func (repo *PraktisStoreEntityRepository) NewSlice() (result []*PraktisStoreEntity) {
	return make([]*PraktisStoreEntity, 0)
}

func (repo *PraktisStoreEntityRepository) Conn() *gorm.DB {
	return praktis.GetDbClient()
}

func (repo *PraktisStoreEntityRepository) GetByID(id int64) (*PraktisStoreEntity, error) {
	res := repo.New()
	tx := praktis.GetDbClient().Where("ID = ?", id).Find(res)
	if tx.Error != nil {
		return res, tx.Error
	}

	return res, nil
}

func (repo *PraktisStoreEntityRepository) GetByIDs(ids ...int64) (result []*PraktisStoreEntity, err error) {
	if len(ids) == 0 {
		return result, fmt.Errorf("no ids provided")
	}
	tx := praktis.GetDbClient().Where("ID IN (?)", ids).Find(&result)
	return result, tx.Error
}

// list

func (repo *PraktisStoreEntityRepository) DB() *gorm.DB {
	return praktis.GetDbClient()
}

func (repo *PraktisStoreEntityRepository) Select(query interface{}, args ...interface{}) *gorm.DB {
	return praktis.GetDbClient().Table(repo.New().TableName()).Select(query, args...)
}

func (repo *PraktisStoreEntityRepository) Filter(filters ...api_entity.GormFilter) *gorm.DB {
	if len(filters) == 0 {
		panic("no filters provided")
	}

	qb := repo.DB()
	for _, filter := range filters {
		qb = filter.ApplyTo(qb)
	}

	return qb
}

func (repo *PraktisStoreEntityRepository) GetOne(filters ...api_entity.GormFilter) (result *PraktisStoreEntity, _ error) {
	qb := repo.Filter(filters...).First(&result)
	return result, qb.Error
}

func (repo *PraktisStoreEntityRepository) GetResults(filters ...api_entity.GormFilter) (result []*PraktisStoreEntity, _ error) {
	var qb *gorm.DB
	if len(filters) == 0 {
		qb = repo.DB().Find(&result)
	} else {
		qb = repo.Filter(filters...).Find(&result)
	}

	return result, qb.Error
}

func (repo *PraktisStoreEntityRepository) Count(filters ...api_entity.GormFilter) (count int64, _ error) {
	qb := repo.Filter(filters...).Count(&count)
	return count, qb.Error
}

// update

func (repo *PraktisStoreEntityRepository) Create(newVal *PraktisStoreEntity) (*PraktisStoreEntity, error) {
	if newVal == nil {
		return nil, fmt.Errorf("entity is nil")
	} else if newVal.GetID() != 0 {
		return nil, fmt.Errorf("cannot create entity with %s", newVal.GetID())
	}

	// @todo .Omit("Name", "Age", "CreatedAt") to ignore fields
	// @todo: handle CreatedAt field
	tx := praktis.GetDbClient().Create(newVal)
	if tx.Error != nil {
		return newVal, tx.Error
	}

	return newVal, nil
}

func (repo *PraktisStoreEntityRepository) Update(val *PraktisStoreEntity) (bool, error) {
	tx := praktis.GetDbClient().Save(val)
	return tx.Error == nil, tx.Error
}

func (repo *PraktisStoreEntityRepository) Delete(val *PraktisStoreEntity) (bool, error) {
	tx := praktis.GetDbClient().Delete(val)
	return tx.Error == nil, tx.Error
}

//end:repository
