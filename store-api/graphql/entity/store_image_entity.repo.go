// GEN[0.0.1 | prak-dev-1.0.0]:dt:2024-05-20 22:11:38
package entity

//start:imports
import (
	"fmt"
	"github.com/siper92/api-base/api_entity"
	"gorm.io/gorm"
	praktis "praktis.bg/store-api/internal/praktis"
)

//end:imports

//start:repository: 6c0ff43afa636fdf4283828f90556a6adb6a7fac1746a26a65b8890e1fe71d39
var _ api_entity.HasRepository[*StoreImageEntity] = (*StoreImageEntity)(nil)

func (s *StoreImageEntity) NewRepository() api_entity.Repository[*StoreImageEntity] {
	return &StoreImageEntityRepository{}
}

var _ api_entity.Repository[*StoreImageEntity] = (*StoreImageEntityRepository)(nil)

type StoreImageEntityRepository struct {
}

func NewStoreImageEntityRepository() *StoreImageEntityRepository {
	return &StoreImageEntityRepository{}
}

func (repo *StoreImageEntityRepository) New() (result *StoreImageEntity) {
	return &StoreImageEntity{}
}

func (repo *StoreImageEntityRepository) NewSlice() (result []*StoreImageEntity) {
	return make([]*StoreImageEntity, 0)
}

func (repo *StoreImageEntityRepository) Conn() *gorm.DB {
	return praktis.GetDbClient()
}

func (repo *StoreImageEntityRepository) GetByID(id int64) (*StoreImageEntity, error) {
	res := repo.New()
	tx := praktis.GetDbClient().Where("ID = ?", id).Find(res)
	if tx.Error != nil {
		return res, tx.Error
	}

	return res, nil
}

func (repo *StoreImageEntityRepository) GetByIDs(ids ...int64) (result []*StoreImageEntity, err error) {
	if len(ids) == 0 {
		return result, fmt.Errorf("no ids provided")
	}
	tx := praktis.GetDbClient().Where("ID IN (?)", ids).Find(&result)
	return result, tx.Error
}

// list

func (repo *StoreImageEntityRepository) DB() *gorm.DB {
	return praktis.GetDbClient()
}

func (repo *StoreImageEntityRepository) Select(query interface{}, args ...interface{}) *gorm.DB {
	return praktis.GetDbClient().Table(repo.New().TableName()).Select(query, args...)
}

func (repo *StoreImageEntityRepository) Filter(filters ...api_entity.GormFilter) *gorm.DB {
	if len(filters) == 0 {
		panic("no filters provided")
	}

	qb := repo.DB()
	for _, filter := range filters {
		qb = filter.ApplyTo(qb)
	}

	return qb
}

func (repo *StoreImageEntityRepository) GetOne(filters ...api_entity.GormFilter) (result *StoreImageEntity, _ error) {
	qb := repo.Filter(filters...).First(&result)
	return result, qb.Error
}

func (repo *StoreImageEntityRepository) GetResults(filters ...api_entity.GormFilter) (result []*StoreImageEntity, _ error) {
	var qb *gorm.DB
	if len(filters) == 0 {
		qb = repo.DB().Find(&result)
	} else {
		qb = repo.Filter(filters...).Find(&result)
	}

	return result, qb.Error
}

func (repo *StoreImageEntityRepository) Count(filters ...api_entity.GormFilter) (count int64, _ error) {
	qb := repo.Filter(filters...).Count(&count)
	return count, qb.Error
}

// update

func (repo *StoreImageEntityRepository) Create(newVal *StoreImageEntity) (*StoreImageEntity, error) {
	if newVal == nil {
		return nil, fmt.Errorf("entity is nil")
	} else if newVal.GetID() != 0 {
		return nil, fmt.Errorf("cannot create entity with %s", newVal.GetID())
	}

	// @todo .Omit("Name", "Age", "CreatedAt") to ignore fields
	// @todo: handle CreatedAt field
	tx := praktis.GetDbClient().Create(newVal)
	if tx.Error != nil {
		return newVal, tx.Error
	}

	return newVal, nil
}

func (repo *StoreImageEntityRepository) Update(val *StoreImageEntity) (bool, error) {
	tx := praktis.GetDbClient().Save(val)
	return tx.Error == nil, tx.Error
}

func (repo *StoreImageEntityRepository) Delete(val *StoreImageEntity) (bool, error) {
	tx := praktis.GetDbClient().Delete(val)
	return tx.Error == nil, tx.Error
}

//end:repository
