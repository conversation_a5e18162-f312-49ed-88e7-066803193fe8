package entity

import (
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	"praktis.bg/store-api/internal/praktis"
)

func GetStoreByIdentity(identity string) (*PraktisStoreEntity, error) {
	warehouseCode, err := getWarehouseCodeFromIdentity(identity)
	if err != nil {
		return nil, err
	}

	return GetStoreByWarehouseCode(warehouseCode)
}

func getWarehouseCodeFromIdentity(identity string) (string, error) {
	key := fmt.Sprintf("praktis_store:indetity:%s", identity)

	cache := praktis.GetCacheClient()
	if cache.MustExists(key) {
		val, err := cache.Get(key)
		if err != nil {
			return "", err
		}

		return val, nil
	}
	store := &PraktisStoreEntity{
		Identity: identity,
	}

	err := praktis.GetDbClient().
		Model(store).
		Where("identity = ?", identity).
		First(store).Error
	core_utils.ErrorWarning(err)

	return store.WarehouseCode, store.SaveCache()
}

func GetStoreByWarehouseCodeInTransaction(db *gorm.DB, warehouseCode string) (*PraktisStoreEntity, error) {
	store := &PraktisStoreEntity{
		WarehouseCode: warehouseCode,
	}

	err := praktis.GetDbClient().
		Model(store).
		Where("warehouse_code = ?", warehouseCode).
		First(store).Error
	if err != nil {
		return nil, fmt.Errorf("error loading entity: %w", err)
	}

	return store, store.SaveCache()
}

func GetStoreByWarehouseCode(warehouseCode string) (*PraktisStoreEntity, error) {
	return GetStoreByWarehouseCodeInTransaction(praktis.GetDbClient(), warehouseCode)
}
