// GEN[0.0.1 | prak-dev-1.0.0]:dt:2025-05-29 16:07:05
package entity

//start:imports
import (
	"fmt"
	"github.com/siper92/api-base/api_entity"
	redis "github.com/siper92/api-base/cache"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"strconv"
	"strings"
	"time"
)

//end:imports

//start:praktis_store_entity: 2b01cad1a2ed04b327a60f886f0823250e79dbee582190b326f5169e816abb26
var _ api_entity.Entity = (*PraktisStoreEntity)(nil)

type PraktisStoreEntity struct {
	ID                   int64 `gorm:"primaryKey"`
	AcceptOrders         bool
	Address              string
	BusinessHours        []model.StoreSchedule `gorm:"-"`
	BusinessHoursData    string
	cacheKey             string `gorm:"-"`
	City                 string
	DescriptionArea      model.DescriptionArea `gorm:"-"`
	DescriptionAreaData  string
	DisplayOrder         int
	Email                string
	fromCache            bool                `gorm:"-"`
	Gallery              []*StoreImageEntity `gorm:"foreignKey:PraktisStoreID;references:ID"`
	Identity             string
	Location             model.MapLocation `gorm:"-"`
	LocationData         string
	MetaDescription      string
	MetaKeywords         string
	MetaTitle            string
	Name                 string
	Phone                string
	Services             []model.Service `gorm:"-"`
	TransportInformation string
	VirtualTour          string
	WarehouseCode        string
	WarehouseID          int64
	CreatedAt            time.Time `gorm:"autoCreateTime"`
	UpdatedAt            time.Time `gorm:"autoUpdateTime"`
}

type PraktisStoreEntityCollection []*PraktisStoreEntity

//start:gorm
func (p *PraktisStoreEntity) TableName() string {
	return "theme_praktis_store"
}

func (p *PraktisStoreEntity) GetID() int64 {
	return p.ID
}

func (p *PraktisStoreEntity) GetFilterableFields() map[string]bool {
	return map[string]bool{
		"acceptorders":         true,
		"address":              true,
		"city":                 true,
		"descriptionareadata":  true,
		"displayorder":         true,
		"email":                true,
		"identity":             true,
		"metadescription":      true,
		"metakeywords":         true,
		"metatitle":            true,
		"name":                 true,
		"phone":                true,
		"transportinformation": true,
		"virtualtour":          true,
		"warehousecode":        true,
		"warehouseid":          true,
	}
}

func (p *PraktisStoreEntity) IsFilterable(field string) bool {
	val, ok := p.GetFilterableFields()[strings.ToLower(field)]
	return ok && val
}

func (p *PraktisStoreEntity) GetPrivateFields() map[string]bool {
	return map[string]bool{
		"businesshoursdata":   true,
		"descriptionareadata": true,
		"locationdata":        true,
	}
}

func (p *PraktisStoreEntity) IsPrivateField(field string) bool {
	val, ok := p.GetPrivateFields()[strings.ToLower(field)]
	return ok && val
}

//end:gorm

//start:cacheable: 26968d0eac568a9039e2d4ad9f3335e7f5fbd7969ee996da72e0b752b81eaa4e
var _ redis.CacheableObject = (*PraktisStoreEntity)(nil)

func (p *PraktisStoreEntity) CacheKey() string {
	if p.cacheKey != "" {
		return p.cacheKey
	}

	return fmt.Sprintf("praktis_store:warehause:%s", p.WarehouseCode)
}

func (p *PraktisStoreEntity) SetCacheKey(key string) {
	p.cacheKey = key
}

func (p *PraktisStoreEntity) CacheTTL() time.Duration {
	return 60 * time.Minute
}

func (p *PraktisStoreEntity) GetCacheObject() map[string]string {
	return map[string]string{
		"ID":                   redis.ToMapValue(p.ID),
		"AcceptOrders":         redis.ToMapValue(p.AcceptOrders),
		"Address":              p.Address,
		"BusinessHoursData":    p.BusinessHoursData,
		"City":                 p.City,
		"DescriptionAreaData":  p.DescriptionAreaData,
		"DisplayOrder":         redis.ToMapValue(p.DisplayOrder),
		"Email":                p.Email,
		"Identity":             p.Identity,
		"LocationData":         p.LocationData,
		"MetaDescription":      p.MetaDescription,
		"MetaKeywords":         p.MetaKeywords,
		"MetaTitle":            p.MetaTitle,
		"Name":                 p.Name,
		"Phone":                p.Phone,
		"TransportInformation": p.TransportInformation,
		"VirtualTour":          p.VirtualTour,
		"WarehouseCode":        p.WarehouseCode,
		"WarehouseID":          redis.ToMapValue(p.WarehouseID),
	}
}

func (p *PraktisStoreEntity) SetCacheObject(cacheData map[string]string) error {
	p.fromCache = true

	for key, val := range cacheData {
		switch key {
		case "ID":
			iVal, err := strconv.ParseInt(val, 10, 64)
			if err != nil {
				return fmt.Errorf("%s -> error parsing int64: %s", key, err)
			}
			p.ID = iVal
			break
		case "AcceptOrders":
			boolVal, err := strconv.ParseBool(val)
			if err != nil {
				return fmt.Errorf("%s -> error parsing bool: %s", key, err)
			}
			p.AcceptOrders = boolVal
			break
		case "Address":
			p.Address = val
			break
		case "BusinessHoursData":
			p.BusinessHoursData = val
			break
		case "City":
			p.City = val
			break
		case "DescriptionAreaData":
			p.DescriptionAreaData = val
			break
		case "DisplayOrder":
			iVal, err := strconv.Atoi(val)
			if err != nil {
				return fmt.Errorf("%s -> error parsing int: %s", key, err)
			}
			p.DisplayOrder = iVal
			break
		case "Email":
			p.Email = val
			break
		case "Identity":
			p.Identity = val
			break
		case "LocationData":
			p.LocationData = val
			break
		case "MetaDescription":
			p.MetaDescription = val
			break
		case "MetaKeywords":
			p.MetaKeywords = val
			break
		case "MetaTitle":
			p.MetaTitle = val
			break
		case "Name":
			p.Name = val
			break
		case "Phone":
			p.Phone = val
			break
		case "TransportInformation":
			p.TransportInformation = val
			break
		case "VirtualTour":
			p.VirtualTour = val
			break
		case "WarehouseCode":
			p.WarehouseCode = val
			break
		case "WarehouseID":
			iVal, err := strconv.ParseInt(val, 10, 64)
			if err != nil {
				return fmt.Errorf("%s -> error parsing int64: %s", key, err)
			}
			p.WarehouseID = iVal
			break
		}
	}

	return nil
}

func (p *PraktisStoreEntity) LoadCache() error {
	cacheData, err := praktis.GetCacheClient().GetMap(p.CacheKey())
	if err != nil {
		return err
	}

	if len(cacheData) == 0 {
		return nil
	}

	return p.SetCacheObject(cacheData)
}

func (p *PraktisStoreEntity) SaveCache() error {
	return praktis.GetCacheClient().Save(p.CacheKey(), p, p.CacheTTL())
}

func (p *PraktisStoreEntity) DeleteCache() error {
	return praktis.GetCacheClient().Delete(p.CacheKey())
}

func (p *PraktisStoreEntity) IsCacheLoaded() bool {
	return p.fromCache
}

func (col PraktisStoreEntityCollection) SaveCache(cacheKey string, ttl time.Duration) error {
	var keys []string
	for _, item := range col {
		err := praktis.GetCacheClient().Save(item.CacheKey(), item, ttl)
		if err != nil {
			return err
		}

		keys = append(keys, item.CacheKey())
	}

	for _, item := range col {
		err := item.SaveCache()
		if err != nil {
			return err
		}
	}

	_ = praktis.GetCacheClient().Delete(cacheKey)
	_, err := praktis.GetCacheClient().AddSetMember(cacheKey, keys...)
	if err == nil {
		_, err = praktis.GetCacheClient().UpdateTTl(cacheKey, ttl)
	}

	return err
}

func (col PraktisStoreEntityCollection) LoadCache(cacheKey string) (items PraktisStoreEntityCollection, _ error) {
	if praktis.GetCacheClient().MustExists(cacheKey) {
		itemKeys, err := praktis.GetCacheClient().GetSet(cacheKey)
		if err != nil {
			return items, err
		}

		for _, key := range itemKeys {
			if praktis.GetCacheClient().MustExists(key) == false {
				_, _ = praktis.GetCacheClient().RemoveSetMember(cacheKey, key)
				continue
			}

			var item PraktisStoreEntity
			item.SetCacheKey(key)
			if err = item.LoadCache(); err != nil {
				return items, err
			} else if item.IsCacheLoaded() {
				items = append(items, &item)
			} else {
				return items, fmt.Errorf("cache key not found: %s -> %s", cacheKey, key)
			}
		}
	}

	return items, nil
}

//end:cacheable

var _ api_entity.ConvertToModel[*model.PraktisStore] = (*PraktisStoreEntity)(nil)

func (p *PraktisStoreEntity) ToModel() *model.PraktisStore {
	result := &model.PraktisStore{}
	result.ID = p.ID
	result.Identity = p.Identity
	result.Name = p.Name
	result.City = p.City
	result.Address = p.Address
	result.Phone = p.Phone
	result.Email = p.Email
	result.DisplayOrder = p.DisplayOrder
	result.AcceptOrders = p.AcceptOrders
	result.WarehouseCode = p.WarehouseCode
	result.WarehouseID = p.WarehouseID
	{
		_step1 := toStoreDescription(p.DescriptionAreaData)
		result.DescriptionArea = &_step1
	}
	result.TransportInformation = p.TransportInformation
	result.MetaTitle = p.MetaTitle
	result.MetaDescription = p.MetaDescription
	result.MetaKeywords = p.MetaKeywords
	{
		_step1 := toMapLocation(p.LocationData)
		result.Location = &_step1
	}
	{
		_step1 := toBusinessHours(p.BusinessHoursData)
		result.BusinessHours = _step1
	}
	result.VirtualTour = p.VirtualTour
	result.Gallery = make([]*model.StoreImage, 0)
	for _, item := range p.Gallery {
		result.Gallery = append(result.Gallery, item.ToModel())
	}
	result.Services = make([]*model.Service, 0)
	for _, item := range p.Services {
		result.Services = append(result.Services, &item)
	}

	return result
}

//end:praktis_store_entity
