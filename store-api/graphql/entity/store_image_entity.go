// GEN[0.0.1 | prak-dev-1.0.0]:dt:2025-05-27 17:19:10
package entity

//start:imports
import (
	"fmt"
	"github.com/siper92/api-base/api_entity"
	redis "github.com/siper92/api-base/cache"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"strconv"
	"strings"
	"time"
)

//end:imports

//start:store_image_entity: eaa28738644a125c9ba802584fe36baa50ab73fcca924ac82a9f5c3cd69bed69
var _ api_entity.Entity = (*StoreImageEntity)(nil)

type StoreImageEntity struct {
	ID             int64 `gorm:"primaryKey"`
	Alt            *string
	cacheKey       string `gorm:"-"`
	fromCache      bool   `gorm:"-"`
	IsPrimary      bool
	MobileSrc      *string
	Position       int
	PraktisStoreID int64
	Src            string
	Title          *string
	CreatedAt      time.Time `gorm:"autoCreateTime"`
	UpdatedAt      time.Time `gorm:"autoUpdateTime"`
}

type StoreImageEntityCollection []*StoreImageEntity

//start:gorm
func (s *StoreImageEntity) TableName() string {
	return "theme_praktis_store_gallery"
}

func (s *StoreImageEntity) GetID() int64 {
	return s.ID
}

func (s *StoreImageEntity) GetFilterableFields() map[string]bool {
	return map[string]bool{
		"alt":            true,
		"isprimary":      true,
		"mobilesrc":      true,
		"position":       true,
		"praktisstoreid": true,
		"src":            true,
		"title":          true,
	}
}

func (s *StoreImageEntity) IsFilterable(field string) bool {
	val, ok := s.GetFilterableFields()[strings.ToLower(field)]
	return ok && val
}

func (s *StoreImageEntity) GetPrivateFields() map[string]bool {
	return map[string]bool{
		"id":             true,
		"praktisstoreid": true,
	}
}

func (s *StoreImageEntity) IsPrivateField(field string) bool {
	val, ok := s.GetPrivateFields()[strings.ToLower(field)]
	return ok && val
}

//end:gorm

//start:cacheable: 5c2d9b3b703546477fc12523733dc89272695efb9a5d1fdba6286ee184624e8e
var _ redis.CacheableObject = (*StoreImageEntity)(nil)

func (s *StoreImageEntity) CacheKey() string {
	if s.cacheKey != "" {
		return s.cacheKey
	}

	return fmt.Sprintf("StoreImageEntity:%v", s.GetID())
}

func (s *StoreImageEntity) SetCacheKey(key string) {
	s.cacheKey = key
}

func (s *StoreImageEntity) CacheTTL() time.Duration {
	return 60 * time.Minute
}

func (s *StoreImageEntity) GetCacheObject() map[string]string {
	return map[string]string{
		"ID":             redis.ToMapValue(s.ID),
		"Alt":            redis.ToMapValue(s.Alt),
		"IsPrimary":      redis.ToMapValue(s.IsPrimary),
		"MobileSrc":      redis.ToMapValue(s.MobileSrc),
		"Position":       redis.ToMapValue(s.Position),
		"PraktisStoreID": redis.ToMapValue(s.PraktisStoreID),
		"Src":            s.Src,
		"Title":          redis.ToMapValue(s.Title),
	}
}

func (s *StoreImageEntity) SetCacheObject(cacheData map[string]string) error {
	s.fromCache = true

	for key, val := range cacheData {
		switch key {
		case "ID":
			iVal, err := strconv.ParseInt(val, 10, 64)
			if err != nil {
				return fmt.Errorf("%s -> error parsing int64: %s", key, err)
			}
			s.ID = iVal
			break
		case "Alt":
			// @TODO s.Alt = *string not supported
			break
		case "IsPrimary":
			boolVal, err := strconv.ParseBool(val)
			if err != nil {
				return fmt.Errorf("%s -> error parsing bool: %s", key, err)
			}
			s.IsPrimary = boolVal
			break
		case "MobileSrc":
			// @TODO s.MobileSrc = *string not supported
			break
		case "Position":
			iVal, err := strconv.Atoi(val)
			if err != nil {
				return fmt.Errorf("%s -> error parsing int: %s", key, err)
			}
			s.Position = iVal
			break
		case "PraktisStoreID":
			iVal, err := strconv.ParseInt(val, 10, 64)
			if err != nil {
				return fmt.Errorf("%s -> error parsing int64: %s", key, err)
			}
			s.PraktisStoreID = iVal
			break
		case "Src":
			s.Src = val
			break
		case "Title":
			// @TODO s.Title = *string not supported
			break
		}
	}

	return nil
}

func (s *StoreImageEntity) LoadCache() error {
	cacheData, err := praktis.GetCacheClient().GetMap(s.CacheKey())
	if err != nil {
		return err
	}

	if len(cacheData) == 0 {
		return nil
	}

	return s.SetCacheObject(cacheData)
}

func (s *StoreImageEntity) SaveCache() error {
	return praktis.GetCacheClient().Save(s.CacheKey(), s, s.CacheTTL())
}

func (s *StoreImageEntity) DeleteCache() error {
	return praktis.GetCacheClient().Delete(s.CacheKey())
}

func (s *StoreImageEntity) IsCacheLoaded() bool {
	return s.fromCache
}

func (col StoreImageEntityCollection) SaveCache(cacheKey string, ttl time.Duration) error {
	var keys []string
	for _, item := range col {
		err := praktis.GetCacheClient().Save(item.CacheKey(), item, ttl)
		if err != nil {
			return err
		}

		keys = append(keys, item.CacheKey())
	}

	for _, item := range col {
		err := item.SaveCache()
		if err != nil {
			return err
		}
	}

	_ = praktis.GetCacheClient().Delete(cacheKey)
	_, err := praktis.GetCacheClient().AddSetMember(cacheKey, keys...)
	if err == nil {
		_, err = praktis.GetCacheClient().UpdateTTl(cacheKey, ttl)
	}

	return err
}

func (col StoreImageEntityCollection) LoadCache(cacheKey string) (items StoreImageEntityCollection, _ error) {
	if praktis.GetCacheClient().MustExists(cacheKey) {
		itemKeys, err := praktis.GetCacheClient().GetSet(cacheKey)
		if err != nil {
			return items, err
		}

		for _, key := range itemKeys {
			if praktis.GetCacheClient().MustExists(key) == false {
				_, _ = praktis.GetCacheClient().RemoveSetMember(cacheKey, key)
				continue
			}

			var item StoreImageEntity
			item.SetCacheKey(key)
			if err = item.LoadCache(); err != nil {
				return items, err
			} else if item.IsCacheLoaded() {
				items = append(items, &item)
			} else {
				return items, fmt.Errorf("cache key not found: %s -> %s", cacheKey, key)
			}
		}
	}

	return items, nil
}

//end:cacheable

var _ api_entity.ConvertToModel[*model.StoreImage] = (*StoreImageEntity)(nil)

func (s *StoreImageEntity) ToModel() *model.StoreImage {
	result := &model.StoreImage{}
	result.Position = s.Position
	result.IsPrimary = s.IsPrimary
	{
		_step1 := toMediaUrl(s.Src)
		result.Src = _step1
	}
	{
		_step1 := toMediaUrlPointer(s.MobileSrc)
		result.MobileSrc = &_step1
	}
	result.Alt = s.Alt
	result.Title = s.Title

	return result
}

//end:store_image_entity
