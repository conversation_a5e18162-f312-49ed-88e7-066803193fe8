package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"fmt"

	"github.com/siper92/api-base/api_entity"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/entity"
	"praktis.bg/store-api/graphql/generated"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/cms"
)

// Gallery is the resolver for the gallery field.
func (r *praktisStoreResolver) Gallery(ctx context.Context, obj *model.PraktisStore) ([]*model.StoreImage, error) {
	var result []*model.StoreImage

	if obj.ID < 1 {
		return nil, fmt.Errorf("obj.ID is required")
	}

	var err error
	var entities entity.StoreImageEntityCollection

	entities, err = entity.NewStoreImageEntityRepository().GetResults(
		api_entity.Where{`praktis_store_id = ?`, obj.ID},
	)
	if err != nil {
		core_utils.ErrorWarning(err)
		return nil, err
	}

	for _, e := range entities {
		result = append(result, e.ToModel())
	}

	core_utils.ErrorWarning(err)

	return result, nil
}

// Services is the resolver for the services field.
func (r *praktisStoreResolver) Services(ctx context.Context, obj *model.PraktisStore) ([]*model.Service, error) {
	return cms.GetAvailableServices(), nil
}

// PraktisStore returns generated.PraktisStoreResolver implementation.
func (r *Resolver) PraktisStore() generated.PraktisStoreResolver { return &praktisStoreResolver{r} }

type praktisStoreResolver struct{ *Resolver }
