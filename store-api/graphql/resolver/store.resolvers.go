package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"

	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/entity"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
)

// StoreSendContactMessage is the resolver for the storeSendContactMessage field.
func (r *mutationResolver) StoreSendContactMessage(ctx context.Context, input model.ContactInput) (bool, error) {
	connector, err := praktis.GetNewStoreConnector()
	if err != nil {
		return false, err
	}

	return connector.SendContactMessage(input)
}

// StoreSendInquiry is the resolver for the storeSendInquiry field.
func (r *mutationResolver) StoreSendInquiry(ctx context.Context, input model.StoreInquiryInput) (bool, error) {
	connector, err := praktis.GetNewStoreConnector()
	if err != nil {
		return false, err
	}

	data := map[string]string{
		"store":   input.Store,
		"name":    input.Name,
		"email":   input.Email,
		"phone":   input.Phone,
		"message": input.Message,
		"type":    input.Type.String(),
	}

	storeObj := request_context.GetDataLoader(ctx).GetStore()

	templateID := storeObj.GetConfig("pfg_theme/emails/store_request_template", "")
	to := storeObj.GetConfig("pfg_theme/emails/store_request_recipient", "")

	if templateID == "" {
		core_utils.Warning("Store request template not found")
		return false, nil
	} else if to == "" {
		core_utils.Warning("Store request recipient not found")
		return false, nil
	}

	if to == "store" {
		storeIdentity := input.Store
		store, err := entity.GetStoreByIdentity(storeIdentity)
		if err != nil {
			return false, err
		}
		to = store.Email
	}

	return connector.SendEmailTemplateMessage(templateID, to, data)
}

// StoreNewsletterSubscribe is the resolver for the storeNewsletterSubscribe field.
func (r *mutationResolver) StoreNewsletterSubscribe(ctx context.Context, email string) (bool, error) {
	connector, err := praktis.GetNewStoreConnector()
	if err != nil {
		return false, err
	}
	return connector.NewsletterSubscribe(email)
}

// StoreNewsletterUnsubscribe is the resolver for the storeNewsletterUnsubscribe field.
func (r *mutationResolver) StoreNewsletterUnsubscribe(ctx context.Context, id int64, code string) (bool, error) {
	connector, err := praktis.GetNewStoreConnector()
	if err != nil {
		return false, err
	}

	return connector.NewsletterUnsubscribe(id, code)
}
