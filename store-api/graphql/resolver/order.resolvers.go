package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"fmt"

	"praktis.bg/store-api/graphql/generated"
	"praktis.bg/store-api/graphql/model"
	product_index "praktis.bg/store-api/internal/praktis/catalog/product"
	order_utils "praktis.bg/store-api/internal/praktis/checkout/order-utils"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
)

// Product is the resolver for the product field.
func (r *orderItemResolver) Product(ctx context.Context, obj *model.OrderItem) (model.Product, error) {
	var sku string
	if obj.Product != nil {
		switch p := obj.Product.(type) {
		case *model.SimpleProduct:
			if p.ID > 0 && p.Name != "" {
				return p, nil
			} else {
				sku = p.Sku
			}
		case *model.BundleProduct:
			if p.ID > 0 && p.Name != "" {
				return p, nil
			} else {
				sku = p.Sku
			}
		default:
			sku = obj.Sku
		}
	}

	if sku == "" {
		return nil, fmt.Errorf("sku is missing for item %d", obj.ID)
	}

	product, err := product_index.NewPraktisProductRepository(nil).GetSKU(obj.Sku, nil)
	if err != nil {
		return nil, err
	}

	return product.ToModel(), nil
}

// Items is the resolver for the items field.
func (r *storeOrderResolver) Items(ctx context.Context, obj *model.StoreOrder) ([]*model.OrderItem, error) {
	if len(obj.Items) > 0 {
		return obj.Items, nil
	}

	var result []*model.OrderItem
	items, err := (&sales.MagentoOrder{
		EntityID: obj.ID,
	}).GetItems()
	if err != nil {
		return result, err
	}

	for _, item := range items {
		result = append(result, order_utils.ToOrderItemModel(item, obj.Currency))
	}

	return result, nil
}

// ShippingAddress is the resolver for the shippingAddress field.
func (r *storeOrderResolver) ShippingAddress(ctx context.Context, obj *model.StoreOrder) (*model.OrderAddress, error) {
	if obj.ShippingAddress.City != "" {
		return obj.ShippingAddress, nil
	}

	var shippingAddress sales.MagentoOrderAddress
	err := sales.NewOrderRepository(nil).DB().Model(&sales.MagentoOrderAddress{}).
		Where("entity_id = ?", obj.ShippingAddress.ID).
		Find(&shippingAddress).Error
	if err != nil {
		return obj.ShippingAddress, err
	}

	obj.ShippingAddress = order_utils.ToOrderShippingAddress(&shippingAddress)

	return obj.ShippingAddress, err
}

// Invoice is the resolver for the invoice field.
func (r *storeOrderResolver) Invoice(ctx context.Context, obj *model.StoreOrder) (*model.CustomerInvoice, error) {
	if obj.Invoice.Type != "" {
		return obj.Invoice, nil
	}

	var billingAddress sales.MagentoOrderAddress
	err := sales.NewOrderRepository(nil).DB().Model(&sales.MagentoOrderAddress{}).
		Where("entity_id = ?", obj.Invoice.ID).
		Find(&billingAddress).Error
	if err != nil {
		return obj.Invoice, err
	}

	obj.Invoice = order_utils.ToOrderInvoice(&billingAddress)

	return obj.Invoice, nil
}

// OrderItem returns generated.OrderItemResolver implementation.
func (r *Resolver) OrderItem() generated.OrderItemResolver { return &orderItemResolver{r} }

// StoreOrder returns generated.StoreOrderResolver implementation.
func (r *Resolver) StoreOrder() generated.StoreOrderResolver { return &storeOrderResolver{r} }

type orderItemResolver struct{ *Resolver }
type storeOrderResolver struct{ *Resolver }
