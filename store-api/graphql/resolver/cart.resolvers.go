package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"errors"
	"fmt"

	"praktis.bg/store-api/graphql/generated"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	product_index "praktis.bg/store-api/internal/praktis/catalog/product"
	cart_utils "praktis.bg/store-api/internal/praktis/checkout/cart-utils"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
)

// GetCart is the resolver for the getCart field.
func (r *queryResolver) GetCart(ctx context.Context, cartToken string) (*model.StoreCart, error) {
	quote, err := request_context.GetDataLoader(ctx).GetTokenQuote(cartToken)
	if err != nil {
		if errors.Is(err, sales.NoQuoteIDError) {
			return cart_utils.NewEmptyCart()
		}

		return nil, err
	}

	return cart_utils.QuoteToCartModel(quote), nil
}

// GetNewCart is the resolver for the getNewCart field.
func (r *queryResolver) GetNewCart(ctx context.Context) (*model.StoreCart, error) {
	return cart_utils.NewEmptyCart()
}

// Shipping is the resolver for the shipping field.
func (r *storeCartResolver) Shipping(ctx context.Context, obj *model.StoreCart) (*model.CartShipping, error) {
	if obj.ID < 1 {
		return &model.CartShipping{
			AvailableMethods:                make([]model.ShippingMethodType, 0),
			AvailableIn:                     make([]*model.PraktisStore, 0),
			HasFreeShipping:                 false,
			FreeShippingAfter:               nil,
			MinAmountForFreeShippingMessage: nil,
			SelectedMethod:                  "",
			Address:                         &model.ShippingAddress{},
		}, nil
	}

	quote, err := request_context.GetDataLoader(ctx).GetQuote(obj.ID)
	if err != nil {
		return nil, err
	}

	return cart_utils.GetCartShipping(quote), err
}

// Product is the resolver for the product field.
func (r *storeCartItemResolver) Product(ctx context.Context, obj *model.StoreCartItem) (model.Product, error) {
	if obj.Product != nil {
		switch p := obj.Product.(type) {
		case *model.SimpleProduct:
			if p.ID > 0 {
				return p, nil
			}
		case *model.BundleProduct:
			if p.ID > 0 {
				return p, nil
			}
		default:
			return nil, fmt.Errorf("unknown product type: %T", p)
		}
	} else if obj.Sku == "" {
		return nil, errors.New("sku is required")
	}

	product, err := product_index.NewPraktisProductRepository(nil).GetSKU(obj.Sku, []string{})
	if err != nil {
		return nil, err
	}

	return product.ToModel(), nil
}

// StoreCart returns generated.StoreCartResolver implementation.
func (r *Resolver) StoreCart() generated.StoreCartResolver { return &storeCartResolver{r} }

// StoreCartItem returns generated.StoreCartItemResolver implementation.
func (r *Resolver) StoreCartItem() generated.StoreCartItemResolver { return &storeCartItemResolver{r} }

type storeCartResolver struct{ *Resolver }
type storeCartItemResolver struct{ *Resolver }
