package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/cms"
	"praktis.bg/store-api/internal/praktis/router"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
)

// GetStaticContent is the resolver for the getStaticContent field.
func (r *queryResolver) GetStaticContent(ctx context.Context) (*model.StaticContent, error) {
	storeObj := request_context.GetDataLoader(ctx).GetStore()
	cache := magento_core.GetStoreClient().GetCacheClient()

	var err error
	result := &model.StaticContent{}

	if cache.MustExists(router.StaticContentCacheKey) {
		data, err := cache.Get(router.StaticContentCacheKey)
		if len(data) > 0 && err == nil {
			if err = json.Unmarshal([]byte(data), result); err == nil {
				return result, nil
			}
		} else {
			core_utils.ErrorWarning(err)
		}
	}

	result = router.GetStaticContent(storeObj)
	jsonRaw, _ := json.Marshal(result)
	err = cache.Save(router.StaticContentCacheKey, jsonRaw, router.StaticContentCacheLifetime)
	core_utils.ErrorWarning(err)

	return result, nil
}

// GetStaticBlock is the resolver for the getStaticBlock field.
func (r *queryResolver) GetStaticBlock(ctx context.Context, identifier string) (*model.CMSBlock, error) {
	cacheKey := fmt.Sprintf("cms_block_%s", identifier)
	cache := magento_core.GetStoreClient().GetCacheClient()
	if cache.MustExists(cacheKey) {
		data, _ := cache.Get(cacheKey)
		if len(data) > 0 {
			block := &model.CMSBlock{}
			if err := json.Unmarshal([]byte(data), block); err == nil {
				return block, nil
			}
		}
	}

	storeObj := request_context.GetDataLoader(ctx).GetStore()
	repo := mage_entity.NewCmsBlockRepository(storeObj)
	block, err := repo.Get(identifier)
	if err != nil {
		return nil, err
	}
	var title, content string
	if block != nil {
		title = block.Title
		content = block.Content
	}

	var res = &model.CMSBlock{
		Identifier: identifier,
		Content:    content,
		Title:      title,
	}

	jsonRaw, _ := json.Marshal(res)
	err = cache.Save(cacheKey, jsonRaw, router.StaticContentCacheLifetime)
	core_utils.ErrorWarning(err)

	return res, nil
}

// GetStaticPage is the resolver for the getStaticPage field.
func (r *queryResolver) GetStaticPage(ctx context.Context, pageID int64) (*model.CMSPage, error) {
	storeObj := magento_core.GetStoreClient().GetStore()
	repo := mage_entity.NewCMSPageRepository(storeObj)

	page, err := repo.Get(pageID)
	if err != nil {
		return nil, err
	} else if page == nil {
		return nil, fmt.Errorf("page %d not found", pageID)
	} else if page.IsActive == false {
		return nil, fmt.Errorf("page %d is not active", pageID)
	}

	return &model.CMSPage{
		Identifier: page.Identifier,
		Content:    page.Content,
		Title:      page.Title,
	}, err
}

// GetBrands is the resolver for the getBrands field.
func (r *queryResolver) GetBrands(ctx context.Context, featured *bool) ([]*model.Brand, error) {
	_featured := false
	if featured != nil {
		_featured = *featured
	}

	return cms.GetBrands(_featured)
}

// GetNotifications is the resolver for the getNotifications field.
func (r *queryResolver) GetNotifications(ctx context.Context) ([]*model.NotificationMessage, error) {
	return []*model.NotificationMessage{}, nil
}

// GetStoreLogo is the resolver for the getStoreLogo field.
func (r *queryResolver) GetStoreLogo(ctx context.Context) (*model.StoreLogo, error) {
	storeObj := request_context.GetDataLoader(ctx).GetStore()
	cache := magento_core.GetStoreClient().GetCacheClient()

	// Define a cache key for the logo
	cacheKey := "store_logo_data"

	// Try to get from cache first
	if cache.MustExists(cacheKey) {
		data, err := cache.Get(cacheKey)
		if len(data) > 0 && err == nil {
			logo := &model.StoreLogo{}
			if err = json.Unmarshal([]byte(data), logo); err == nil {
				return logo, nil
			} else {
				core_utils.ErrorWarning(err)
			}
		}
	}

	// If not in cache or cache error, fetch the data
	// Fetch the logo path from Magento config
	logoPath := storeObj.GetConfig("design/header/logo_src", "")

	// Check if the specific image ID is stored
	logoImageID := storeObj.GetConfig("design/header/logo_src_image", "")

	// If logo path is empty but we have an image ID, try to build the full URL
	if logoPath == "" && logoImageID != "" {
		// Assuming the image ID needs to be combined with a base URL
		baseMediaURL := storeObj.GetConfig("web/secure/base_media_url", "")
		if baseMediaURL != "" {
			logoPath = baseMediaURL + "logo/" + logoImageID
		}
	}

	// Get optional alt text
	logoAlt := storeObj.GetConfig("design/header/logo_alt", "")

	// Get optional width and height (if configured)
	widthStr := storeObj.GetConfig("design/header/logo_width", "")
	heightStr := storeObj.GetConfig("design/header/logo_height", "")

	var width, height *int
	if widthStr != "" {
		if widthVal, err := strconv.Atoi(widthStr); err == nil {
			width = &widthVal
		}
	}

	if heightStr != "" {
		if heightVal, err := strconv.Atoi(heightStr); err == nil {
			height = &heightVal
		}
	}

	var alt *string
	if logoAlt != "" {
		alt = &logoAlt
	}

	logo := &model.StoreLogo{
		URL:    logoPath,
		Width:  width,
		Height: height,
		Alt:    alt,
	}

	// Save to cache for future requests
	jsonRaw, _ := json.Marshal(logo)
	err := cache.Save(cacheKey, jsonRaw, router.StaticContentCacheLifetime)
	core_utils.ErrorWarning(err)

	return logo, nil
}
