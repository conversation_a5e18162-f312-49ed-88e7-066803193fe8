package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"encoding/json"
	"fmt"

	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	credit_calculators "praktis.bg/store-api/internal/praktis/credit-calculators"
)

// GetCreditCalculatorTBIBank is the resolver for the getCreditCalculatorTBIBank field.
func (r *queryResolver) GetCreditCalculatorTBIBank(ctx context.Context, sku string) (*model.TbiConfiguration, error) {
	store := request_context.GetDataLoader(ctx).GetStore()
	cid := store.GetConfig("avalon_paymentmethodbnpl_tab_options/properties_all/bnpl_cid", "")
	if cid == "" {
		return nil, fmt.Errorf("cid is empty")
	}

	body, err := credit_calculators.GetTBISchemaResponse(cid)
	if err != nil {
		return nil, err
	}

	var jsonResult model.TbiConfiguration
	err = json.Unmarshal(body, &jsonResult)
	if err != nil {
		return nil, err
	}

	return &jsonResult, nil
}
