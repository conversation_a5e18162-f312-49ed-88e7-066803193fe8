package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"encoding/json"

	"github.com/siper92/api-base/cache"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/cms"
)

// GetHomepage is the resolver for the getHomepage field.
func (r *queryResolver) GetHomepage(ctx context.Context) (*model.HomePage, error) {
	var homepage *model.HomePage
	cacheKey := "cms:homepage"
	if praktis.GetCacheClient().MustExists(cacheKey) && false {
		rawData, err := praktis.GetCacheClient().Get(cacheKey)
		if err == nil {
			homepage = &model.HomePage{}
			err = json.Unmarshal([]byte(rawData), homepage)
			if err == nil {
				return homepage, nil
			}
		}
	}

	store := request_context.GetDataLoader(ctx).GetStore()
	sliderID := store.GetConfig("pfg_theme/pages/home_page_slider", "")
	contentBlock := store.GetConfig("pfg_theme/pages/home_page_content_block", "")

	homepage, err := cms.ToHomePageData(sliderID, contentBlock)
	if err != nil {
		return nil, err
	}

	cacheValue, _ := json.Marshal(homepage)
	if len(cacheValue) > 0 {
		_ = praktis.GetCacheClient().Save(cacheKey, string(cacheValue), cache.InfiniteTTL)
	}

	return homepage, nil
}
