package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"fmt"

	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/generated"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	"praktis.bg/store-api/internal/praktis/router"
	praktis_search "praktis.bg/store-api/internal/praktis/search"
	"praktis.bg/store-api/packages/magento-core/search"
	"praktis.bg/store-api/packages/magento-core/types"
)

// Search is the resolver for the search field.
func (r *queryResolver) Search(ctx context.Context, searchQuery string) (*model.SearchResults, error) {
	result := &model.SearchResults{
		PopularTerms: make([]string, 0),
		Categories:   make([]*model.SearchCategory, 0),
		TotalItems:   0,
		Products:     make([]model.Product, 0),
		Block:        nil,
	}
	searchResults, err := search.GetSearchResults(search.SearchParams{
		ItemsPerPage:       6,
		SearchString:       searchQuery,
		IncludeSuggestions: true,
		IncludeCategories:  true,
	})

	if err != nil {
		core_utils.ErrorWarning(err)
		return result, err
	}

	cats := search.GetCategoryList(searchResults.Categories)
	for _, cat := range cats {
		result.Categories = append(result.Categories, &model.SearchCategory{
			Name: cat.Name,
			URL:  cat.GetRelPath(),
		})
	}

	result.TotalItems = searchResults.TotalItems

	if len(searchResults.Items) > 0 {
		ids := make([]int64, 0, len(searchResults.Items))
		for _, item := range searchResults.Items {
			ids = append(ids, types.ToInt64(item.ProductId))
		}

		products, err := product.NewPraktisProductRepository(nil).GetIDs(ids, []string{})
		core_utils.ErrorWarning(err)

		for _, prod := range products {
			result.Products = append(result.Products, prod.ToModel())
		}
	} else {
		core_utils.ErrorWarning(fmt.Errorf("no items found"))
		result.Products = []model.Product{}
	}

	if len(searchResults.Suggestions) > 0 {
		result.PopularTerms = searchResults.Suggestions
	}

	return result, nil
}

// SearchPage is the resolver for the searchPage field.
func (r *queryResolver) SearchPage(ctx context.Context, searchQuery string, query []*model.QueryParam) (*model.SearchPage, error) {
	params := router.RouteParams(query)
	result := praktis_search.NewSearchPage(params)

	filters := params.GetAppliedFilters()
	searchResults, err := search.GetSearchResults(search.SearchParams{
		ItemsPerPage:       params.GetPageSize(),
		Page:               params.GetPage(),
		SearchString:       searchQuery,
		SortBy:             praktis_search.ToSearchSortOrder(params.GetSort().Value),
		SortDirection:      params.GetSort().Dir.String(),
		IncludeFilters:     true,
		IncludeSuggestions: true,
		IncludeCategories:  true,
		Filters:            praktis_search.ToSearchFilters(filters),
	})
	if err != nil {
		core_utils.ErrorWarning(err)
		return result, err
	}

	result.Data.TotalItems = searchResults.TotalItems
	result.Title = fmt.Sprintf("Има намерени %d резултата за '%s'",
		searchResults.TotalItems, searchQuery,
	)
	result.Data.PopularTerms = searchResults.Suggestions
	result.Data.Products = praktis_search.SearchResultsGetProducts(searchResults)
	// @TODO: add categories
	result.State.Filters.Available = praktis_search.SearchResultsToAvailableFilters(searchResults)
	result.State.Pager = praktis_search.SearchResultsToPager(searchResults, params)

	return result, nil
}

// Block is the resolver for the block field.
func (r *searchResultsResolver) Block(ctx context.Context, obj *model.SearchResults) (*model.CMSBlock, error) {
	return &model.CMSBlock{
		Identifier: "search-results-block",
		Title:      "",
		Content:    "",
	}, nil
}

// SearchResults returns generated.SearchResultsResolver implementation.
func (r *Resolver) SearchResults() generated.SearchResultsResolver { return &searchResultsResolver{r} }

type searchResultsResolver struct{ *Resolver }
