package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"fmt"
	"math"

	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/generated"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/catalog"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	"praktis.bg/store-api/internal/praktis/content"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
	"praktis.bg/store-api/packages/magento-core/types"
)

// State is the resolver for the state field.
func (r *catalogPageResolver) State(ctx context.Context, obj *model.CatalogPage) (*model.CatalogState, error) {
	if obj.Layout == model.CatalogLayoutLandingPage {
		return obj.State, nil
	}

	productCollection, err := request_context.GetDataLoader(ctx).GetProductCollection(obj)
	if err != nil {
		return obj.State, err
	}

	availableFilters, err := productCollection.GetFilters()
	obj.State.Filters.Available = availableFilters
	if err != nil {
		core_utils.ErrorWarning(err)
		return nil, err
	}

	page := productCollection.GetPage()
	if page == 0 {
		page = 1
	}
	totalItems := productCollection.GetItemsCount()
	pageSize := productCollection.GetPageSize()
	if pageSize == 0 {
		pageSize = 1
	}

	totalPages := int(math.Ceil(float64(totalItems) / float64(pageSize)))

	obj.State.AvailableSorts = productCollection.GetAvailableSorts()
	obj.State.Sort = productCollection.GetSort()
	obj.State.Pager = &model.Pager{
		Page:       page,
		TotalItems: totalItems,
		TotalPages: totalPages,
		PageSize:   pageSize,
	}

	return obj.State, err
}

// Products is the resolver for the products field.
func (r *catalogPageResolver) Products(ctx context.Context, obj *model.CatalogPage) ([]model.Product, error) {
	if obj.Layout == model.CatalogLayoutLandingPage {
		return obj.Products, nil
	}

	productCollection, err := request_context.GetDataLoader(ctx).GetProductCollection(obj)
	if err != nil {
		return obj.Products, err
	}

	var products []*product.PraktisProduct
	products, err = productCollection.GetItems()
	if err != nil {
		return obj.Products, err
	}

	for _, _product := range products {
		obj.Products = append(obj.Products, _product.ToModel())
	}

	return obj.Products, nil
}

// Banner is the resolver for the banner field.
func (r *categoryResolver) Banner(ctx context.Context, obj *model.Category) (*model.CategoryBanner, error) {
	var err error
	var banner map[string]string

	cacheClient := magento_core.GetStoreClient().GetCacheClient()
	cacheKey := fmt.Sprintf("category-banner-%d-data", obj.ID)

	if cacheClient.MustExists(cacheKey) {
		banner, err = cacheClient.GetMap(cacheKey)
		if err != nil {
			core_utils.ErrorWarning(err)
		}
	} else {
		banner, err = catalog.GetCategoryBanner(obj.ID)
		if err != nil {
			core_utils.ErrorWarning(err)
		}

		err = cacheClient.Save(cacheKey, banner, cache.InfiniteTTL)
		if err != nil {
			core_utils.ErrorWarning(err)
		}
	}

	return &model.CategoryBanner{
		Image: &model.Image{
			Src:       banner["image"],
			MobileSrc: core_utils.ToStringPointer(banner["mobile_image"]),
			Title:     core_utils.ToStringPointer(banner["title"]),
		},
		URL: banner["link"],
	}, nil
}

// Widgets is the resolver for the widgets field.
func (r *categoryResolver) Widgets(ctx context.Context, obj *model.Category) ([]model.CategoryWidget, error) {
	categoryRepo := mage_entity.NewCategoryRepository()

	cat, err := categoryRepo.Get(
		types.ToEntityID(obj.ID),
		"widgets",
	)
	if err != nil {
		return obj.Widgets, err
	}

	obj.Widgets = content.MustParseCategoryWidgets(cat)

	return obj.Widgets, nil
}

// Children is the resolver for the children field.
func (r *categoryResolver) Children(ctx context.Context, obj *model.Category) ([]*model.Category, error) {
	categoryRepo := mage_entity.NewCategoryRepository()

	cats, err := categoryRepo.GetChildren(
		types.ToEntityID(obj.ID),
		"name",
		"url_path",
		"thumbnail",
	)
	if err != nil {
		return obj.Children, err
	}

	obj.Children = make([]*model.Category, 0)
	for _, cat := range *cats {
		obj.Children = append(obj.Children, catalog.CategoryToModelCategory(cat))
	}

	return obj.Children, nil
}

// CatalogPage returns generated.CatalogPageResolver implementation.
func (r *Resolver) CatalogPage() generated.CatalogPageResolver { return &catalogPageResolver{r} }

// Category returns generated.CategoryResolver implementation.
func (r *Resolver) Category() generated.CategoryResolver { return &categoryResolver{r} }

type catalogPageResolver struct{ *Resolver }
type categoryResolver struct{ *Resolver }
