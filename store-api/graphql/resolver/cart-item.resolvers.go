package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"errors"
	"fmt"

	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/middleware"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	cart_utils "praktis.bg/store-api/internal/praktis/checkout/cart-utils"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
)

// CartItemAdd is the resolver for the cartItemAdd field.
func (r *mutationResolver) CartItemAdd(ctx context.Context, cartToken string, item model.NewCartItem) (*model.StoreCart, error) {
	dataLoader := request_context.GetDataLoader(ctx)
	quote, err := dataLoader.GetTokenQuote(cartToken)

	customerID := dataLoader.GetCurrentCustomerID()
	if err != nil {
		if errors.Is(err, sales.NoQuoteIDError) {
			quote, err = sales.NewQuoteRepository(nil).CreateNew(sales.NewCartData{
				CustomerID: customerID,
				CustomerIP: middleware.GetCustomerIPAddress(ctx),
			})
			if err != nil {
				return nil, err
			}
			dataLoader.SetQuote(quote)
		} else {
			return nil, err
		}
	}
	if quote == nil {
		return nil, errors.New("неможе да се намери количката")
	}

	if quote.CustomerID != customerID {
		var isGuest int16 = 0
		if customerID < 1 {
			isGuest = 1
		}
		err = quote.Repository().DB().Model(quote).Updates(map[string]interface{}{
			"customer_id":       customerID,
			"customer_is_guest": isGuest,
			"remote_ip":         middleware.GetCustomerIPAddress(ctx),
		}).Error
		if err != nil {
			return nil, fmt.Errorf("неуспешно обновяване на количката: %w", err)
		}
	}

	connector, err := praktis.GetNewCartConnector()
	if err != nil {
		return nil, err
	}

	if added, err := connector.AddToCart(
		quote.GetApiID(),
		item.Sku,
		item.BaseQty,
	); err != nil {
		return nil, fmt.Errorf("error: %w", err)
	} else if added == false {
		return nil, errors.New("failed to add to cart")
	}

	quote, err = dataLoader.ReloadCart()
	return cart_utils.QuoteToCartModel(quote), err
}

// CartItemUpdate is the resolver for the cartItemUpdate field.
func (r *mutationResolver) CartItemUpdate(ctx context.Context, cartToken string, item model.NewCartItem) (*model.StoreCart, error) {
	dataLoader := request_context.GetDataLoader(ctx)
	quote, err := dataLoader.GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	}

	connector, err := praktis.GetNewCartConnector()
	if err != nil {
		return nil, err
	}

	if updated, err := connector.UpdateQuantity(
		quote.GetApiID(),
		item.Sku,
		item.BaseQty,
	); err != nil {
		core_utils.ErrorWarning(err)
		return nil, fmt.Errorf("грешка при актуализацията на количеството: %w", err)
	} else if updated == false {
		return nil, errors.New("възникна грешка при актуализацията на количеството")
	}

	quote, err = dataLoader.ReloadCart()
	return cart_utils.QuoteToCartModel(quote), err
}

// CartItemRemove is the resolver for the cartItemRemove field.
func (r *mutationResolver) CartItemRemove(ctx context.Context, cartToken string, sku string) (*model.StoreCart, error) {
	dataLoader := request_context.GetDataLoader(ctx)
	quote, err := dataLoader.GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	}

	connector, err := praktis.GetNewCartConnector()
	if err != nil {
		return nil, err
	}

	if removed, err := connector.RemoveFromCart(
		quote.GetApiID(),
		sku,
	); err != nil {
		return nil, err
	} else if removed == false {
		return nil, errors.New("възникна грешка при при изтриване на артикула")
	}

	quote, err = dataLoader.ReloadCart()
	return cart_utils.QuoteToCartModel(quote), err
}
