package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/blog"
)

// GetBlogPosts is the resolver for the getBlogPosts field.
func (r *queryResolver) GetBlogPosts(ctx context.Context, page int, size int) (*model.MagentoBlog, error) {
	if size%4 != 0 {
		size = 4
	}

	if size > 40 || size < 4 {
		size = 4
	}

	cacheKey := fmt.Sprintf("blogPosts:%d:%d", page, size)
	var result *model.MagentoBlog

	if praktis.GetCacheClient().MustExists(cacheKey) {
		val, _ := praktis.GetCacheClient().Get(cacheKey)
		if len(val) > 0 {
			err := json.Unmarshal([]byte(val), &result)
			if err == nil {
				return result, nil
			}
		}
	}

	var blogPosts []*model.BlogPost
	posts, err := blog.GetBlogPostCollection(blog.GetCollectionParams{
		Page:     core_utils.IF(page > 0, page, 1),
		PageSize: size,
	})

	if err != nil {
		return result, err
	}

	for _, post := range posts {
		blogPosts = append(blogPosts, post.ToModel())
	}

	result = &model.MagentoBlog{
		Posts:       blogPosts,
		CurrentPage: page,
		TotalPages:  int(blog.GetTotalPages()),
	}

	if len(posts) > 0 {
		cacheValue, _ := json.Marshal(result)
		_ = praktis.GetCacheClient().Save(cacheKey, string(cacheValue), time.Minute*30)
	}

	return result, nil
}

// GetBlogPost is the resolver for the getBlogPost field.
func (r *queryResolver) GetBlogPost(ctx context.Context, identifier string) (*model.BlogPost, error) {
	cacheKey := "blogPost:" + identifier
	var featuredPost *model.BlogPost
	if praktis.GetCacheClient().MustExists(cacheKey) {
		val, _ := praktis.GetCacheClient().Get(cacheKey)
		if len(val) > 0 {
			err := json.Unmarshal([]byte(val), &featuredPost)
			if err == nil {
				return featuredPost, nil
			}
		}
	}

	post, err := blog.BlogPostEntity{
		UrlKey: identifier,
	}.LoadByIdentifier()
	if err != nil {
		return featuredPost, err
	}

	postModel := post.ToModel()
	if postModel.Title != "" {
		cacheValue, err := json.Marshal(postModel)
		if err == nil {
			err = praktis.GetCacheClient().Save(cacheKey, string(cacheValue), time.Minute*30)
			core_utils.DebugError(err)
		} else {
			core_utils.DebugError(err)
		}
	}

	return postModel, nil
}

// GetFeaturedBlogPost is the resolver for the getFeaturedBlogPost field.
func (r *queryResolver) GetFeaturedBlogPost(ctx context.Context) (*model.BlogPost, error) {
	cacheKey := "featuredBlogPost"
	var featuredPost *model.BlogPost
	if praktis.GetCacheClient().MustExists(cacheKey) {
		val, _ := praktis.GetCacheClient().Get(cacheKey)
		if len(val) > 0 {
			if err := json.Unmarshal([]byte(val), &featuredPost); err == nil {
				return featuredPost, nil
			}
		}
	}

	posts, err := blog.GetBlogPostCollection(blog.GetCollectionParams{
		Page:     1,
		PageSize: 1,
	})

	if err != nil {
		return featuredPost, err
	}

	for _, post := range posts {
		postData := post.ToModel()
		if postData.Title != "" {
			cacheValue, err := json.Marshal(postData)
			if err == nil {
				err = praktis.GetCacheClient().Save(cacheKey, string(cacheValue), time.Minute*30)
				core_utils.DebugError(err)
			} else {
				core_utils.DebugError(err)
			}
		}

		return postData, nil
	}

	return featuredPost, nil
}
