package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"

	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	cart_utils "praktis.bg/store-api/internal/praktis/checkout/cart-utils"
	"praktis.bg/store-api/internal/praktis/customer"
)

// CustomerRegister is the resolver for the customerRegister field.
func (r *mutationResolver) CustomerRegister(ctx context.Context, data model.CustomerRegistrationData) (*model.RegistrationResponse, error) {
	err := customer.ValidateRegistrationData(data)
	if err != nil {
		return nil, err
	}

	res, _, err := cart_utils.RegisterClientViaMagento(data)
	return res, err
}

// CustomerPasswordForgot is the resolver for the customerPasswordForgot field.
func (r *mutationResolver) CustomerPasswordForgot(ctx context.Context, email string) (bool, error) {
	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return false, err
	}

	var forgotFlowInitialized bool
	if forgotFlowInitialized, err = connector.ForgotPassword(email); err != nil {
		return forgotFlowInitialized, err
	}

	return forgotFlowInitialized, nil
}

// CustomerPasswordReset is the resolver for the customerPasswordReset field.
func (r *mutationResolver) CustomerPasswordReset(ctx context.Context, customerID int64, resetToken string, password string) (bool, error) {
	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return false, err
	}

	var resetPassword bool
	if resetPassword, err = connector.ResetPassword(customerID, resetToken, password); err != nil {
		return resetPassword, err
	}

	return resetPassword, nil
}
