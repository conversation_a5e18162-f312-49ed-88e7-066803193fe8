package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"

	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/cms"
)

// AvailableServices is the resolver for the availableServices field.
func (r *queryResolver) AvailableServices(ctx context.Context) ([]*model.Service, error) {
	return cms.GetAvailableServices(), nil
}
