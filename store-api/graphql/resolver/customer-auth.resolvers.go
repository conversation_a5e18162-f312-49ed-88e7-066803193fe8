package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"errors"
	"fmt"

	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/customer"
	"praktis.bg/store-api/internal/store_connect"
)

// CustomerLogin is the resolver for the customerLogin field.
func (r *mutationResolver) CustomerLogin(ctx context.Context, email string, password string) (*model.Customer, error) {
	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return nil, err
	}

	var registered store_connect.CustomerActionResult
	if registered, err = connector.Login(email, password); err != nil {
		return nil, err
	} else if registered.Success == false {
		return nil, errors.New("failed to register customer")
	}

	customerEntity, err := customer.NewCustomerRepository(nil).GetByID(registered.CustomerID)
	if err != nil {
		return nil, err
	}

	err = customerEntity.SaveToken()
	request_context.GetDataLoader(ctx).SetCustomer(customerEntity)

	return customer.CustomerToModel(customerEntity), err
}

// CustomerLoginRefresh is the resolver for the customerLoginRefresh field.
func (r *mutationResolver) CustomerLoginRefresh(ctx context.Context) (*model.Customer, error) {
	customerEntity, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	newToken, err := customerEntity.NewToken()
	if err != nil {
		return nil, err
	}

	customerEntity.SetToken(newToken)
	err = customerEntity.SaveToken()

	return customer.CustomerToModel(customerEntity), err
}

// CustomerLogout is the resolver for the customerLogout field.
func (r *mutationResolver) CustomerLogout(ctx context.Context) (bool, error) {
	customerEntity, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return true, err
	}

	return customerEntity.ClearToken()
}

// CustomerLoginProvider is the resolver for the customerLoginProvider field.
func (r *mutationResolver) CustomerLoginProvider(ctx context.Context, typeArg model.LoginProvider, providerToken string) (*model.SocialLoginResponse, error) {
	panic(fmt.Errorf("not implemented: CustomerLoginProvider - customerLoginProvider"))
}
