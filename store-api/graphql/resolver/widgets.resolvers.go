package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"

	"praktis.bg/store-api/graphql/model"
)

// GetWidget is the resolver for the getWidget field.
func (r *queryResolver) GetWidget(ctx context.Context, identity string) (model.Widget, error) {
	// new resolver - no directive found
	panic("new resolver")
}
