package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"

	core_utils "github.com/siper92/core-utils"
	"gorm.io/gorm"
	"praktis.bg/store-api/graphql/middleware"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	cart_utils "praktis.bg/store-api/internal/praktis/checkout/cart-utils"
	order_utils "praktis.bg/store-api/internal/praktis/checkout/order-utils"
	shipping_utils "praktis.bg/store-api/internal/praktis/checkout/shipping-utils"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"praktis.bg/store-api/packages/magento-core/types"
)

// CartApplyCoupon is the resolver for the cartApplyCoupon field.
func (r *mutationResolver) CartApplyCoupon(ctx context.Context, cartToken string, couponCode string) (*model.StoreCart, error) {
	dataLoader := request_context.GetDataLoader(ctx)
	quote, err := dataLoader.GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	}

	connector, err := praktis.GetNewCartConnector()
	if err != nil {
		return nil, err
	}

	customerID := dataLoader.GetCurrentCustomerID()

	if applied, err := connector.ApplyCouponCode(
		quote.GetApiID(),
		couponCode,
		customerID,
	); err != nil {
		return nil, err
	} else if applied == false {
		return nil, errors.New("неможе да се примоли промо код: " + couponCode)
	}
	core_utils.Warning("::DEBUGLOGWARN:: APPLIED promo code {%s} for customer id {%d}", couponCode, customerID)
	quote, err = dataLoader.ReloadCart()
	return cart_utils.QuoteToCartModel(quote), err
}

// CartCalculateTotals is the resolver for the cartCalculateTotals field.
func (r *mutationResolver) CartCalculateTotals(ctx context.Context, cartToken string) (*model.StoreCart, error) {
	quote, err := request_context.GetDataLoader(ctx).GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	}

	quote, err = cart_utils.RecalculateTotalsAndRefreshQuote(ctx, quote)

	return cart_utils.QuoteToCartModel(quote), err
}

// CartSaveClient is the resolver for the cartSaveClient field.
func (r *mutationResolver) CartSaveClient(ctx context.Context, cartToken string, data model.ClientInput) (*model.StoreCart, error) {
	dataLoader := request_context.GetDataLoader(ctx)
	quote, err := dataLoader.GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	} else if err = cart_utils.ValidateCheckoutCustomerInfo(data); err != nil {
		return nil, err
	}

	customerID := dataLoader.GetCurrentCustomerID()

	_ = quote.GetAddresses() // load addresses
	err = praktis.GetDbClient().Transaction(func(tx *gorm.DB) error {
		quote, err = cart_utils.SaveClientData(
			tx, quote, data, customerID,
			middleware.GetCustomerIPAddress(ctx),
		)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	quote, err = dataLoader.ReloadCart()
	if err != nil {
		return nil, err
	}

	return cart_utils.QuoteToCartModel(quote), nil
}

// CartAvailableShippingMethods is the resolver for the cartAvailableShippingMethods field.
func (r *mutationResolver) CartAvailableShippingMethods(ctx context.Context, cartToken string, data model.ShippingInput) ([]*model.AvailableShippingMethod, error) {
	dataLoader := request_context.GetDataLoader(ctx)
	quote, err := dataLoader.GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	} else if err = cart_utils.ValidateCheckoutShippingInfo(data); err != nil {
		return nil, err
	}

	_ = quote.GetAddresses() // load addresses
	err = praktis.GetDbClient().Transaction(func(tx *gorm.DB) error {
		var defaultShippingMethod string // empty is ok
		_, err = cart_utils.SetQuoteShippingData(
			tx, quote, data, defaultShippingMethod,
		)
		return err
	})
	if err != nil {
		return nil, err
	}

	quote, err = cart_utils.RecalculateTotalsAndRefreshQuote(ctx, quote)
	if err != nil {
		return nil, err
	}

	codeMap := map[string]struct{}{}
	var result []*model.AvailableShippingMethod
	switch data.Type {
	case model.ShippingMethodTypeEcontToAddress, model.ShippingMethodTypeEcontToOffice:
		for _, rate := range quote.GetShippingRates() {
			code := fmt.Sprintf("%s_%s", rate.Carrier.String, rate.Method.String)
			if _, ok := codeMap[code]; ok {
				continue
			}

			result = append(result, &model.AvailableShippingMethod{
				Method: &model.ShippingMethod{
					Name: rate.MethodTitle.String,
					Code: code,
				},
				Price: &model.Price{
					Value:    rate.Price,
					Currency: quote.QuoteCurrencyCode,
				},
			})
		}
	case model.ShippingMethodTypeToStore:
		stores, err := cart_utils.GetCartAvailableStores(quote)
		if err != nil {
			return nil, err
		}

		storeShip := shipping_utils.NewPraktisShipping(quote.GetStore())
		result = append(result, &model.AvailableShippingMethod{
			Method: &model.ShippingMethod{
				Name: storeShip.GetTitle(),
				Code: storeShip.GetShippingMethod(),
			},
			ErrorMsg: core_utils.IF(
				len(stores) > 0,
				"",
				storeShip.GetConfigVal("specificerrmsg"),
			),
			Price: &model.Price{
				Value:    0,
				Currency: quote.QuoteCurrencyCode,
			},
		})
	default:
		return nil, fmt.Errorf("шипинг методът не е позволен: %s", data.Type)
	}

	return result, nil
}

// CartSaveShipping is the resolver for the cartSaveShipping field.
func (r *mutationResolver) CartSaveShipping(ctx context.Context, cartToken string, shippingMethodCode string, data model.ShippingInput) (*model.StoreCart, error) {
	dataLoader := request_context.GetDataLoader(ctx)
	quote, err := dataLoader.GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	} else if err = cart_utils.ValidateCheckoutShippingInfo(data); err != nil {
		return nil, err
	} else if err = order_utils.ValidateShippingMethod(
		dataLoader.GetStore(), shippingMethodCode,
	); err != nil {
		return nil, err
	}

	_ = quote.GetAddresses() // load addresses
	err = praktis.GetDbClient().Transaction(func(tx *gorm.DB) error {
		_, err = cart_utils.SetQuoteShippingData(
			tx, quote, data, shippingMethodCode,
		)
		return err
	})
	if err != nil {
		return nil, err
	}

	quote, err = cart_utils.RecalculateTotalsAndRefreshQuote(ctx, quote)
	if err != nil {
		return nil, err
	}

	return cart_utils.QuoteToCartModel(quote), nil
}

// CartAvailablePaymentMethods is the resolver for the cartAvailablePaymentMethods field.
func (r *mutationResolver) CartAvailablePaymentMethods(ctx context.Context, cartToken string) ([]*model.PaymentMethod, error) {
	quote, err := request_context.GetDataLoader(ctx).GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	}

	var result []*model.PaymentMethod
	methods := cart_utils.GetAvailablePraktisPayments(quote.GetStore())
	for _, method := range methods {
		address := quote.GetShippingAddress()
		if method.GetCode() == cart_utils.CacheOnDeliveryCode &&
			address != nil &&
			address.ShippingType.Int32 == shipping_utils.ShippingTypeGetFromStore {
			continue
		}
		if method.GetCode() == cart_utils.TBICreditCardCode {
			if quote.GrandTotal.Valid && quote.GrandTotal.Float64 <= 100.00 {
				continue
			}
		}

		result = append(result, cart_utils.ToMethodModel(method))
	}

	return result, nil
}

// CartSavePayment is the resolver for the cartSavePayment field.
func (r *mutationResolver) CartSavePayment(ctx context.Context, cartToken string, paymentMethodCode string) (*model.StoreCart, error) {
	quote, err := request_context.GetDataLoader(ctx).GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	}

	err = order_utils.ValidatePaymentMethod(quote.GetStore(), paymentMethodCode)
	if err != nil {
		return nil, err
	}

	err = quote.Repository().DB().Transaction(func(tx *gorm.DB) error {
		return cart_utils.UpdateQuotePaymentMethod(tx, quote, paymentMethodCode)
	})
	if err != nil {
		return nil, err
	}

	quote, err = cart_utils.RecalculateTotalsAndRefreshQuote(ctx, quote)

	return cart_utils.QuoteToCartModel(quote), err
}

// PlaceOrder is the resolver for the placeOrder field.
func (r *mutationResolver) PlaceOrder(ctx context.Context, cartToken string, data model.NewOrderInput) (*model.PlaceOrderResponse, error) {
	dataLoader := request_context.GetDataLoader(ctx)
	quote, err := dataLoader.GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	}

	customerID := dataLoader.GetCurrentCustomerID()

	var dbConn = praktis.GetDbClient()
	_ = quote.GetAddresses()
	err = dbConn.Transaction(func(tx *gorm.DB) error {
		if err = order_utils.ValidateNewOrderData(
			quote.GetStore(), data,
		); err != nil {
			return err
		}

		// For BNP payments, retrieve persistent payment data that won't be affected by external API calls
		var bnpPaymentData string
		var hasBNPPayment bool

		if data.PaymentMethodCode == cart_utils.StenikLeasingJetCreditCode {
			log.Printf("[INFO] BNP: Retrieving persistent payment data before order creation operations")

			bnpPaymentData, err = cart_utils.RetrieveBNPPaymentDataPersistent(tx, quote.EntityID)
			if err != nil {
				log.Printf("[ERROR] BNP: Failed to retrieve persistent payment data: %v", err)
				return fmt.Errorf("failed to retrieve BNP payment data: %w", err)
			}

			if bnpPaymentData != "" {
				hasBNPPayment = true
				log.Printf("[INFO] BNP: Persistent payment data retrieved successfully (%d bytes)", len(bnpPaymentData))
			} else {
				log.Printf("[WARNING] BNP: No persistent payment data found")
			}
		}

		quote, err = cart_utils.SaveClientData(
			tx, quote, *data.Client, customerID,
			middleware.GetCustomerIPAddress(ctx),
		)
		if err != nil {
			return err
		}

		// Restore BNP payment data from persistent storage if it was cleared
		if hasBNPPayment {
			err = restoreBNPPaymentData(tx, quote.EntityID, bnpPaymentData)
			if err != nil {
				log.Printf("[ERROR] BNP: Failed to restore payment data after client save: %v", err)
				return fmt.Errorf("failed to restore BNP payment data: %w", err)
			}
		}
		quote, err = cart_utils.SetQuoteShippingData(
			tx,
			quote,
			*data.Shipping,
			data.ShippingMethodCode,
		)
		if err != nil {
			return err
		}

		// Restore BNP payment data from persistent storage if it was cleared
		if hasBNPPayment {
			err = restoreBNPPaymentData(tx, quote.EntityID, bnpPaymentData)
			if err != nil {
				log.Printf("[ERROR] BNP: Failed to restore payment data after shipping save: %v", err)
				return fmt.Errorf("failed to restore BNP payment data: %w", err)
			}
		}

		// Update only the customer_note field without affecting related entities
		err = tx.Model(&sales.MagentoQuote{}).Where("entity_id = ?", quote.EntityID).UpdateColumn("customer_note", sql.NullString{
			String: types.ToStr(data.Note),
			Valid:  data.Note != nil,
		}).Error
		if err != nil {
			return err
		}

		err = cart_utils.UpdateQuotePaymentMethod(tx, quote, data.PaymentMethodCode)
		if err != nil {
			return err
		}

		// Final restore of BNP payment data from persistent storage before validation
		if hasBNPPayment {
			err = restoreBNPPaymentData(tx, quote.EntityID, bnpPaymentData)
			if err != nil {
				log.Printf("[ERROR] BNP: Failed to restore payment data before validation: %v", err)
				return fmt.Errorf("failed to restore BNP payment data: %w", err)
			}
		}

		// Reload payment information after method update to ensure we have the latest data
		var payment sales.MagentoQuotePayment
		err = tx.Where("quote_id = ?", quote.EntityID).First(&payment).Error
		if err != nil {
			return fmt.Errorf("failed to reload payment information: %w", err)
		}
		quote.Payment = &payment

		// For BNP payments, do a direct database check to ensure payment data exists
		if data.PaymentMethodCode == cart_utils.StenikLeasingJetCreditCode {
			var additionalInfo sql.NullString
			err = tx.Model(&sales.MagentoQuotePayment{}).
				Where("quote_id = ?", quote.EntityID).
				Select("additional_information").
				Scan(&additionalInfo).Error
			if err != nil {
				return fmt.Errorf("failed to verify BNP payment data: %w", err)
			}

			log.Printf("[DEBUG] BNP: Database check - Valid: %v, Value: %s", additionalInfo.Valid, additionalInfo.String)

			if !additionalInfo.Valid || additionalInfo.String == "" {
				return fmt.Errorf("BNP payment validation failed: loan calculation data is missing despite restore attempts. Please call cartSaveBNPPayment again and immediately place the order")
			}

			// Update the payment object with the correct additional information
			quote.Payment.AdditionalInformation = additionalInfo.String
			log.Printf("[DEBUG] BNP: Updated payment object with additional information")
		}

		// Validate BNP payment data if applicable (after payment method update)
		if err = order_utils.ValidateQuoteForBNPPayment(quote); err != nil {
			return fmt.Errorf("BNP payment validation failed: %w", err)
		}

		return nil
	})
	if err != nil {
		return nil, fmt.Errorf("не може да се създаде почъвка: %s", err.Error())
	}

	connector, err := praktis.GetNewCartConnector()
	if err != nil {
		return nil, err
	}

	response, err := connector.PlaceOrder(
		quote.GetApiID(),
		data,
	)
	if err != nil {
		return nil, err
	}

	var order *sales.MagentoOrder
	incrementID := response.Order.IncrementID

	// load order
	order, err = sales.NewOrderRepository(dbConn).GetByIncrementID(incrementID)
	if err != nil {
		return order_utils.NewPlaceOrderResponse(response, order),
			order_utils.OrderDoneError(incrementID, err)
	}

	// customer assignment
	if data.Client.RegisterOnOrder {
		customerID, err = cart_utils.RegisterClientOnOrder(data)
		if err != nil {
			return order_utils.NewPlaceOrderResponse(response, order),
				order_utils.OrderDoneError(incrementID, err)
		}
	} else {
		if order.CustomerID.Int32 != int32(customerID) {
			err = dbConn.Model(order).UpdateColumn("customer_id", customerID).Error
			if err != nil {
				return order_utils.NewPlaceOrderResponse(response, order),
					order_utils.OrderDoneError(incrementID, err)
			}
		}
	}

	// Transfer BNP payment data from backup storage to order payment record for admin panel display
	if data.PaymentMethodCode == cart_utils.StenikLeasingJetCreditCode {
		err = cart_utils.TransferBNPPaymentDataToOrder(dbConn, quote.EntityID, order.EntityID)
		if err != nil {
			log.Printf("[ERROR] BNP: Failed to transfer payment data to order %s: %v", incrementID, err)
			// Don't fail the order creation, but log the error
		} else {
			log.Printf("[INFO] BNP: Payment data successfully transferred to order %s", incrementID)

			// Reload the order to get the updated payment information for post-processing
			log.Printf("[DEBUG] BNP: Reloading order %s to get updated payment information", incrementID)
			reloadedOrder, reloadErr := sales.NewOrderRepository(dbConn).GetByIncrementID(incrementID)
			if reloadErr != nil {
				log.Printf("[WARNING] BNP: Failed to reload order %s: %v", incrementID, reloadErr)
				// Use the original order if reload fails
			} else {
				order = reloadedOrder
				log.Printf("[DEBUG] BNP: Order %s reloaded successfully", incrementID)

				// Manually load the payment if it's not loaded by GORM preload
				if order.Payment == nil {
					log.Printf("[DEBUG] BNP: Payment not loaded by preload, manually loading payment for order %d", order.EntityID)
					var payment sales.MagentoOrderPayment
					paymentErr := dbConn.Where("parent_id = ?", order.EntityID).First(&payment).Error
					if paymentErr != nil {
						log.Printf("[WARNING] BNP: Failed to manually load payment for order %d: %v", order.EntityID, paymentErr)
					} else {
						order.Payment = &payment
						log.Printf("[DEBUG] BNP: Payment manually loaded successfully - ID: %d, Method: %s", payment.EntityID, payment.Method)
					}
				} else {
					log.Printf("[DEBUG] BNP: Payment already loaded by preload - ID: %d, Method: %s", order.Payment.EntityID, order.Payment.Method)
				}
			}
		}
	}

	// Handle post-order processing for BNP payments
	err = order_utils.HandlePostOrderProcessing(order)
	if err != nil {
		// Log error but don't fail the order placement
		log.Printf("[WARNING] Post-order processing failed for order %s: %v", incrementID, err)
		return order_utils.NewPlaceOrderResponse(response, order),
			order_utils.OrderDoneError(incrementID, fmt.Errorf("post-order processing failed: %w", err))
	}

	return order_utils.NewPlaceOrderResponse(response, order), nil
}

// Helper function for BNP payment data restoration
func restoreBNPPaymentData(tx *gorm.DB, quoteID int64, backupData string) error {
	if backupData == "" {
		return fmt.Errorf("no backup data provided")
	}

	log.Printf("[INFO] BNP: Restoring payment data for quote %d (%d bytes)", quoteID, len(backupData))

	// Check if the current data is missing or empty
	var currentInfo sql.NullString
	err := tx.Model(&sales.MagentoQuotePayment{}).
		Where("quote_id = ?", quoteID).
		Select("additional_information").
		Scan(&currentInfo).Error
	if err != nil {
		return fmt.Errorf("failed to check current payment data: %w", err)
	}

	// Only restore if the current data is missing or empty
	if !currentInfo.Valid || currentInfo.String == "" {
		log.Printf("[INFO] BNP: Current payment data is missing, restoring from backup")

		err = tx.Model(&sales.MagentoQuotePayment{}).
			Where("quote_id = ?", quoteID).
			UpdateColumn("additional_information", backupData).Error
		if err != nil {
			return fmt.Errorf("failed to restore payment data: %w", err)
		}

		log.Printf("[INFO] BNP: Payment data restored successfully")
	} else {
		log.Printf("[DEBUG] BNP: Current payment data exists (%d bytes), no restore needed", len(currentInfo.String))
	}

	return nil
}
