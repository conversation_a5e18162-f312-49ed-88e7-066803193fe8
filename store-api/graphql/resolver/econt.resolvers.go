package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"encoding/json"
	"fmt"

	cache2 "github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/econt"
)

// GetEcontCity is the resolver for the getEcontCity field.
func (r *queryResolver) GetEcontCity(ctx context.Context, country string) ([]*model.EcontCity, error) {
	cacheKey := fmt.Sprintf("econt_cities_%s", country)
	cache := praktis.GetCacheClient()
	if cache.MustExists(cacheKey) {
		cachedData, err := cache.Get(cacheKey)
		if err == nil {
			var cities []*model.EcontCity
			err = json.Unmarshal([]byte(cachedData), &cities)
			if err == nil {
				return cities, nil
			}
		}
	}

	cities, err := econt.GetEcontCities(country)
	if err != nil {
		return nil, err
	}

	var citiesModel []*model.EcontCity
	for _, city := range cities {
		citiesModel = append(citiesModel, city.ToModel())
	}

	cachedData, _ := json.Marshal(citiesModel)
	if len(cachedData) > 0 {
		err = cache.Save(cacheKey, string(cachedData), cache2.InfiniteTTL)
		core_utils.ErrorWarning(err)
	}

	return citiesModel, nil
}

// GetEcontOffice is the resolver for the getEcontOffice field.
func (r *queryResolver) GetEcontOffice(ctx context.Context, cityID string) ([]*model.EcontOffice, error) {
	cacheKey := fmt.Sprintf("econt_offices_%s", cityID)
	cache := praktis.GetCacheClient()
	if cache.MustExists(cacheKey) {
		cachedData, err := cache.Get(cacheKey)
		if err == nil {
			var offices []*model.EcontOffice
			err = json.Unmarshal([]byte(cachedData), &offices)
			if err == nil {
				return offices, nil
			}
		}
	}

	offices, err := econt.GetEcontOffices(cityID)
	if err != nil {
		return nil, err
	}

	var officesModel []*model.EcontOffice
	for _, office := range offices {
		officesModel = append(officesModel, office.ToModel())
	}

	cachedData, _ := json.Marshal(officesModel)
	if len(cachedData) > 0 {
		err = cache.Save(cacheKey, string(cachedData), cache2.InfiniteTTL)
		core_utils.ErrorWarning(err)
	}

	return officesModel, nil
}
