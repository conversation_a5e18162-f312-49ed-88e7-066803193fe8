package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"errors"
	"fmt"

	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/customer"
	"praktis.bg/store-api/internal/store_connect"
)

// CustomerWishlistAdd is the resolver for the customerWishlistAdd field.
func (r *mutationResolver) CustomerWishlistAdd(ctx context.Context, sku string) (*model.CustomerWishlist, error) {
	customerEntity, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	wishlist := customerEntity.GetWishlistOrCreate()
	err = wishlist.AddItem(sku)

	return &model.CustomerWishlist{
		Skus:     wishlist.GetSkus(),
		Products: make([]model.Product, 0),
	}, err
}

// CustomerWishlistRemove is the resolver for the customerWishlistRemove field.
func (r *mutationResolver) CustomerWishlistRemove(ctx context.Context, sku string) (*model.CustomerWishlist, error) {
	customerEntity, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	wishlist := customerEntity.GetWishlistOrCreate()
	err = wishlist.RemoveItem(sku)

	return &model.CustomerWishlist{
		Skus:     wishlist.GetSkus(),
		Products: make([]model.Product, 0),
	}, err
}

// CustomerUpdateInfo is the resolver for the customerUpdateInfo field.
func (r *mutationResolver) CustomerUpdateInfo(ctx context.Context, data *model.CustomerUpdateInput) (*model.Customer, error) {
	token, err := request_context.GetDataLoader(ctx).GetCustomerToken()
	if err != nil {
		return nil, err
	}

	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return nil, err
	}

	var updateResult store_connect.CustomerActionResult
	if updateResult, err = connector.UpdateInfo(token.CustomerID, data); err != nil {
		return nil, fmt.Errorf("грешка при обновяване на данните: %w", err)
	} else if updateResult.Success == false {
		return nil, errors.New("грешка при обновяване на данните")
	}

	customerEntity, err := request_context.GetDataLoader(ctx).ReloadCustomer()
	return customer.CustomerToModel(customerEntity), err
}

// CustomerUpdatePassword is the resolver for the customerUpdatePassword field.
func (r *mutationResolver) CustomerUpdatePassword(ctx context.Context, oldPassword string, newPassword string) (*model.Customer, error) {
	customerEntity, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return nil, err
	}

	var updateResult store_connect.CustomerActionResult
	if updateResult, err = connector.UpdatePassword(customerEntity.Email, oldPassword, newPassword); err != nil {
		return nil, fmt.Errorf("грешка при промяна на паролата: %w", err)
	} else if updateResult.Success == false {
		return nil, errors.New("промяната на паролата не беше успешна")
	}

	return customer.CustomerToModel(customerEntity), err
}

// CustomerAddressAdd is the resolver for the customerAddressAdd field.
func (r *mutationResolver) CustomerAddressAdd(ctx context.Context, data model.CustomerAddressInput) ([]*model.CustomerAddress, error) {
	customerToken, err := request_context.GetDataLoader(ctx).GetCustomerToken()
	if err != nil {
		return nil, err
	}

	if err = customer.ValidateCustomerAddressInfo(data); err != nil {
		return nil, err
	}

	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return nil, err
	}

	var updateResult store_connect.CustomerAddressActionResult
	if updateResult, err = connector.CreateCustomerAddress(customerToken.CustomerID, data); err != nil {
		return nil, fmt.Errorf("грешка при добавяне на адрес: %w", err)
	} else if updateResult.Success == false {
		return nil, errors.New("грешка при добавяне на адрес")
	}

	c, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	return customer.GetCustomerShippingAddresses(c)
}

// CustomerAddressUpdate is the resolver for the customerAddressUpdate field.
func (r *mutationResolver) CustomerAddressUpdate(ctx context.Context, addressID int64, data model.CustomerAddressInput) ([]*model.CustomerAddress, error) {
	customerToken, err := request_context.GetDataLoader(ctx).GetCustomerToken()
	if err != nil {
		return nil, err
	}

	if err = customer.ValidateCustomerAddressInfo(data); err != nil {
		return nil, err
	}

	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return nil, err
	}

	var updateResult store_connect.CustomerAddressActionResult
	if updateResult, err = connector.UpdateCustomerAddress(customerToken.CustomerID, addressID, data); err != nil {
		return nil, fmt.Errorf("грешка при редактиране на адрес: %w", err)
	} else if updateResult.Success == false {
		return nil, errors.New("грешка при редактиране на адрес")
	}

	c, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	return customer.GetCustomerShippingAddresses(c)
}

// CustomerAddressRemove is the resolver for the customerAddressRemove field.
func (r *mutationResolver) CustomerAddressRemove(ctx context.Context, addressID int64) ([]*model.CustomerAddress, error) {
	customerToken, err := request_context.GetDataLoader(ctx).GetCustomerToken()
	if err != nil {
		return nil, err
	}

	connector, err := praktis.GetNewCustomerConnector()
	if err != nil {
		return nil, err
	}

	var deleteResult store_connect.CustomerActionResult
	if deleteResult, err = connector.DeleteCustomerAddress(customerToken.CustomerID, addressID); err != nil {
		return nil, fmt.Errorf("грешка при премахване на адрес: %w", err)
	} else if deleteResult.Success == false {
		return nil, errors.New("грешка при премахване на адрес")
	}

	c, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	return customer.GetCustomerShippingAddresses(c)
}

// CustomerData is the resolver for the customerData field.
func (r *queryResolver) CustomerData(ctx context.Context) (*model.Customer, error) {
	customerEntity, err := request_context.GetDataLoader(ctx).GetCustomer()
	return customer.CustomerToModel(customerEntity), err
}

// CustomerWishlist is the resolver for the customerWishlist field.
func (r *queryResolver) CustomerWishlist(ctx context.Context) (*model.CustomerWishlist, error) {
	customerEntity, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	wishlist := customerEntity.GetWishlist()
	return &model.CustomerWishlist{
		Skus:     wishlist.GetSkus(),
		Products: make([]model.Product, 0),
	}, err
}
