package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"

	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/entity"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/cms"
	mage_store "praktis.bg/store-api/packages/magento-core/mage-store"
)

// AvailableStores is the resolver for the availableStores field.
func (r *queryResolver) AvailableStores(ctx context.Context) ([]*model.PraktisStore, error) {
	return cms.GetAvailableStores()
}

// GetStore is the resolver for the getStore field.
func (r *queryResolver) GetStore(ctx context.Context, identity string) (*model.StorePageData, error) {
	_resultEntity, err := entity.GetStoreByIdentity(identity)
	if err != nil {
		core_utils.ErrorWarning(err)
		return nil, err
	}

	store := request_context.GetDataLoader(ctx).GetStore()
	messageBlockID := store.GetConfig("pfg_theme/messages/shop_page_message", "")
	messageHtml := ""
	if messageBlockID != "" {
		cms, err := mage_store.NewCmsBlockRepository(store).Get(messageBlockID)
		if err != nil {
			return nil, err
		} else if cms.IsActive {
			messageHtml = cms.Content
			if messageHtml == "" {
				messageHtml = cms.RawContent
			}
		}
	}

	return &model.StorePageData{
		MessageBlock: messageHtml,
		Store:        _resultEntity.ToModel(),
	}, nil
}
