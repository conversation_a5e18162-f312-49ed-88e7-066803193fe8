package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"fmt"
	"log"

	"gorm.io/gorm"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	cart_utils "praktis.bg/store-api/internal/praktis/checkout/cart-utils"
	credit_calculators "praktis.bg/store-api/internal/praktis/credit-calculators"
	"praktis.bg/store-api/packages/magento-core/types"
)

// CartSaveBNPPayment is the resolver for the cartSaveBNPPayment field.
func (r *mutationResolver) CartSaveBNPPayment(ctx context.Context, cartToken string, paymentData model.BNPPaymentInput) (*model.StoreCart, error) {
	quote, err := request_context.GetDataLoader(ctx).GetTokenQuote(cartToken)
	if err != nil {
		return nil, err
	}

	// Validate payment data
	err = cart_utils.ValidateBNPPaymentData(paymentData)
	if err != nil {
		return nil, fmt.Errorf("invalid payment data: %w", err)
	}

	// Update quote with BNP payment data
	err = quote.Repository().DB().Transaction(func(tx *gorm.DB) error {
		return cart_utils.UpdateQuotePaymentMethodWithBNPData(tx, quote, paymentData)
	})
	if err != nil {
		return nil, err
	}

	// Recalculate totals and refresh quote
	quote, err = cart_utils.RecalculateTotalsAndRefreshQuote(ctx, quote)
	if err != nil {
		return nil, err
	}

	return cart_utils.QuoteToCartModel(quote), nil
}

// SubmitBNPApplication is the resolver for the submitBNPApplication field.
func (r *mutationResolver) SubmitBNPApplication(ctx context.Context, orderNumber string) (bool, error) {
	err := credit_calculators.SubmitBNPApplication(orderNumber)
	if err != nil {
		return false, err
	}
	return true, nil
}

// GetCreditCalculatorBNPParibas is the resolver for the getCreditCalculatorBNPParibas field.
func (r *queryResolver) GetCreditCalculatorBNPParibas(ctx context.Context, sku string, downPayment float64, qty int) ([]*model.BNPVariantGroup, error) {
	//store := request_context.GetDataLoader(ctx).GetStore()

	productEntity, err := product.NewPraktisProductRepository(nil).GetSKU(sku, []string{
		"stenik_jetcredit_good_type_id",
		"price",
		"special_price",
		"special_from",
		"special_to",
		"zeron_use_second_measure",
		"zeron_base_measure",
		"zeron_measure",
		"zeron_measure_qty",
	})
	if err != nil {
		return nil, err
	}
	finalPrice := productEntity.GetFinalPrice()
	if productEntity.UsesSecondMeasure() {
		val := productEntity.MustGetVal("zeron_measure_qty")
		valFloat := types.ToFloat(val)
		if valFloat > 0 {
			finalPrice = finalPrice * valFloat
		}
	}
	principal := finalPrice * float64(qty)
	goodTypeIds := productEntity.MustGetVal("stenik_jetcredit_good_type_id")

	return credit_calculators.GetAvailableVariantsGroupedByPricingScheme(
		goodTypeIds,
		principal,
		downPayment,
	)
}

// GetCreditCalculatorBNPParibasForQuote is the resolver for the getCreditCalculatorBNPParibasForQuote field.
func (r *queryResolver) GetCreditCalculatorBNPParibasForQuote(ctx context.Context, cartToken string, downPayment float64) ([]*model.BNPVariantGroup, error) {
	// Get the current user's quote/cart
	quote, err := request_context.GetDataLoader(ctx).GetTokenQuote(cartToken)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve quote: %w", err)
	}

	// Get all items from the quote
	quoteItems, err := quote.GetItems()
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve quote items: %w", err)
	}

	if len(quoteItems) == 0 {
		log.Printf("[INFO] BNP: No items found in quote, returning empty result")
		return []*model.BNPVariantGroup{}, nil
	}

	// Calculate total quote amount using the quote's subtotal
	// This ensures we use the correct total that includes all pricing rules, discounts, etc.
	totalAmount := quote.Subtotal.Float64
	if totalAmount <= 0 {
		log.Printf("[WARNING] BNP: Quote subtotal is zero or negative (%.2f), returning empty result", totalAmount)
		return []*model.BNPVariantGroup{}, nil
	}

	// Determine the good type ID to use for the entire quote
	// We'll use the good type ID from the first item that has one
	// This is a business decision - alternatives could be:
	// 1. Use the most common good type ID across all items
	// 2. Use a specific good type ID for mixed carts
	// 3. Require all items to have the same good type ID
	// For now, we use the first available one for simplicity
	var goodTypeIds string
	productRepo := product.NewPraktisProductRepository(nil)

	log.Printf("[INFO] BNP: Analyzing %d quote items to determine good type IDs", len(quoteItems))

	for i, item := range quoteItems {
		if item.Sku == "" {
			log.Printf("[WARNING] BNP: Skipping quote item %d with empty SKU", i+1)
			continue
		}

		log.Printf("[DEBUG] BNP: Processing quote item %d: SKU=%s, Qty=%.2f, Price=%.2f, RowTotal=%.2f",
			i+1, item.Sku, item.Qty, item.Price, item.RowTotal)

		// Fetch product entity to get the good type ID
		productEntity, err := productRepo.GetSKU(item.Sku, []string{
			"stenik_jetcredit_good_type_id",
		})
		if err != nil {
			log.Printf("[WARNING] BNP: Failed to fetch product entity for SKU %s: %v", item.Sku, err)
			continue
		}

		itemGoodTypeIds := productEntity.MustGetVal("stenik_jetcredit_good_type_id")
		if itemGoodTypeIds != "" {
			goodTypeIds = itemGoodTypeIds
			log.Printf("[INFO] BNP: Using good type IDs '%s' from product %s (item %d) for entire quote", goodTypeIds, item.Sku, i+1)
			break
		} else {
			log.Printf("[DEBUG] BNP: Product %s (item %d) has no good type IDs", item.Sku, i+1)
		}
	}

	// If no good type IDs found, return empty result
	if goodTypeIds == "" {
		log.Printf("[INFO] BNP: No products with good type IDs found in quote, returning empty result")
		return []*model.BNPVariantGroup{}, nil
	}

	// Make a single BNP API call with the total quote amount
	log.Printf("[INFO] BNP: Calculating financing options for quote total: %.2f with good type IDs: %s", totalAmount, goodTypeIds)

	variantGroups, err := credit_calculators.GetAvailableVariantsGroupedByPricingScheme(
		goodTypeIds,
		totalAmount,
		downPayment,
	)
	if err != nil {
		log.Printf("[ERROR] BNP: Failed to get variants for quote total: %v", err)
		return nil, fmt.Errorf("failed to calculate financing options: %w", err)
	}

	log.Printf("[INFO] BNP: Successfully calculated financing options for quote total %.2f, returning %d variant groups", totalAmount, len(variantGroups))
	return variantGroups, nil
}

// GetBNPPricingSchemes is the resolver for the getBNPPricingSchemes field.
func (r *queryResolver) GetBNPPricingSchemes(ctx context.Context, goodTypeIds string, principal float64, downPayment float64) ([]*model.BNPPricingScheme, error) {
	return credit_calculators.GetBNPPricingSchemes(goodTypeIds, principal, downPayment)
}

// CalculateBNPLoan is the resolver for the calculateBNPLoan field.
func (r *queryResolver) CalculateBNPLoan(ctx context.Context, cartToken string, downPayment float64, pricingVariantID int) (*model.LoanCalculation, error) {
	return credit_calculators.CalculateBNPLoan(cartToken, downPayment, pricingVariantID)
}

// GetBNPGoodCategories is the resolver for the getBNPGoodCategories field.
func (r *queryResolver) GetBNPGoodCategories(ctx context.Context) ([]*model.BNPGoodCategory, error) {
	return credit_calculators.GetBNPGoodCategories()
}

// GetBNPGoodTypes is the resolver for the getBNPGoodTypes field.
func (r *queryResolver) GetBNPGoodTypes(ctx context.Context, categoryID *string) ([]*model.BNPGoodType, error) {
	// Add debug logging to understand the issue
	if categoryID == nil {
		log.Printf("[DEBUG] BNP: GetBNPGoodTypes called with categoryID=nil")
	} else {
		log.Printf("[DEBUG] BNP: GetBNPGoodTypes called with categoryID=%s", *categoryID)
	}

	return credit_calculators.GetBNPGoodTypes(categoryID)
}
