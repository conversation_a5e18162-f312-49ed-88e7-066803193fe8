package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"

	"praktis.bg/store-api/graphql/generated"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	order_utils "praktis.bg/store-api/internal/praktis/checkout/order-utils"
	"praktis.bg/store-api/internal/praktis/customer"
)

// Addresses is the resolver for the addresses field.
func (r *customerResolver) Addresses(ctx context.Context, obj *model.Customer) ([]*model.CustomerAddress, error) {
	customerEntity, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return nil, err
	}

	return customer.GetCustomerShippingAddresses(customerEntity)
}

// Orders is the resolver for the orders field.
func (r *customerResolver) Orders(ctx context.Context, obj *model.Customer) ([]*model.StoreOrder, error) {
	var result []*model.StoreOrder
	customerE, err := request_context.GetDataLoader(ctx).GetCustomer()
	if err != nil {
		return result, err
	}

	orders, err := customerE.GetOrders()
	if err != nil {
		return result, err
	}

	for _, order := range orders {
		result = append(result, order_utils.ToOrderModel(order))
	}

	return result, nil
}

// Products is the resolver for the products field.
func (r *customerWishlistResolver) Products(ctx context.Context, obj *model.CustomerWishlist) ([]model.Product, error) {
	var result []model.Product
	var err error

	if len(obj.Skus) > 0 {
		var products []*product.PraktisProduct
		products, err = product.NewPraktisProductRepository(nil).GetSKUs(obj.Skus, []string{})
		for _, prod := range products {
			result = append(result, prod.ToModel())
		}
	}

	return result, err
}

// Customer returns generated.CustomerResolver implementation.
func (r *Resolver) Customer() generated.CustomerResolver { return &customerResolver{r} }

// CustomerWishlist returns generated.CustomerWishlistResolver implementation.
func (r *Resolver) CustomerWishlist() generated.CustomerWishlistResolver {
	return &customerWishlistResolver{r}
}

type customerResolver struct{ *Resolver }
type customerWishlistResolver struct{ *Resolver }
