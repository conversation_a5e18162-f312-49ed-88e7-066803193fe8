package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strings"

	"github.com/siper92/api-base/cache"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/generated"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/catalog/product"
	"praktis.bg/store-api/internal/praktis/catalog/stock"
	"praktis.bg/store-api/internal/praktis/cms"
	magento_core "praktis.bg/store-api/packages/magento-core"
	mage_entity "praktis.bg/store-api/packages/magento-core/mage-store"
)

// Price is the resolver for the price field.
func (r *bundleProductResolver) Price(ctx context.Context, obj *model.BundleProduct) (*model.ProductPrice, error) {
	return obj.Price, nil
}

// SkuAvailability is the resolver for the skuAvailability field.
func (r *bundleProductResolver) SkuAvailability(ctx context.Context, obj *model.BundleProduct) ([]*model.StoreAvailabilityItem, error) {
	var result []*model.StoreAvailabilityItem
	return result, nil
}

// Product is the resolver for the product field.
func (r *productPageResolver) Product(ctx context.Context, obj *model.ProductPage) (model.Product, error) {
	id, err := product.GetProductModelID(obj.Product)
	if err != nil {
		return nil, err
	}

	index, err := product.NewPraktisProductRepository(nil).GetID(id, []string{})
	if err != nil {
		return nil, err
	}

	return index.ToModel(), nil
}

// BoughtTogether is the resolver for the boughtTogether field.
func (r *productPageResolver) BoughtTogether(ctx context.Context, obj *model.ProductPage) ([]*model.SimpleProduct, error) {
	id, err := product.GetProductModelID(obj.Product)
	if err != nil {
		return nil, err
	}

	repo := product.NewPraktisProductRepository(nil)
	skus := repo.GetBundledProducts(id)
	if skus == nil || len(skus) < 1 {
		return nil, nil
	}

	items, err := repo.GetSKUs(skus, []string{})
	var result []*model.SimpleProduct
	for _, item := range items {
		modelV := item.ToModel()
		if v, ok := modelV.(*model.SimpleProduct); ok {
			if product.IsValidProduct(v) {
				result = append(result, v)
			}
		}
	}

	return result, err
}

// StaticBlocks is the resolver for the staticBlocks field.
func (r *productPageResolver) StaticBlocks(ctx context.Context, obj *model.ProductPage) ([]*model.CMSBlock, error) {
	var result []*model.CMSBlock
	cacheClient := praktis.GetCacheClient()
	cacheKey := fmt.Sprintf("products:static_blocks")
	if cacheClient.MustExists(cacheKey) {
		data, err := cacheClient.GetMap(cacheKey)
		if err != nil {
			return nil, err
		}

		keysOrder := map[string]string{}
		for key, content := range data {
			parts := strings.Split(content, "|&&|")
			var title, contentText, orderVal string

			if len(parts) > 0 {
				title = parts[0]
			}
			if len(parts) > 1 {
				contentText = parts[1]
			}
			if len(parts) > 2 {
				orderVal = parts[2]
			}

			result = append(result, &model.CMSBlock{
				Identifier: key,
				Title:      title,
				Content:    contentText,
			})
			keysOrder[key] = orderVal
		}

		slices.SortFunc(result, func(a, b *model.CMSBlock) int {
			orderA, okA := keysOrder[a.Identifier]
			orderB, okB := keysOrder[b.Identifier]
			if !okA && !okB {
				return 0
			} else if !okA {
				return 1
			} else if !okB {
				return -1
			}

			return strings.Compare(orderA, orderB)
		})

		return result, err
	}

	storeObj := request_context.GetDataLoader(ctx).GetStore()
	if storeObj == nil {
		return nil, errors.New("mage-store not found")
	}

	var blockIds []string
	for _, path := range []magento_core.ConfigValuePath{
		//"product_page/view/perks_block_id",
		//"product_page/view/text_below_description",
		"product_page/view/delivery_info",
		"product_page/view/additional_block",
	} {
		value := storeObj.GetConfig(path, "")
		if value != "" {
			blockIds = append(blockIds, value)
		}
	}

	// 19

	blocks := make([]*mage_entity.CmsBlock, 0)
	db := praktis.GetDbClient().
		Table("cms_block").
		Select([]string{"identifier", "title", "compiled_content"}).
		Where("block_id in (?)", blockIds)
	err := db.Scan(&blocks).Error
	if err != nil {
		return nil, err
	}

	cacheData := make(map[string]string)
	for index, block := range blocks {
		result = append(result, &model.CMSBlock{
			Identifier: block.Identifier,
			Title:      block.Title,
			Content:    block.Content,
		})
		cacheData[block.Identifier] = fmt.Sprintf("%s|&&|%s|&&|%d", block.Title, block.Content, index)
	}

	if len(cacheData) > 0 {
		err = cacheClient.SaveMap(cacheKey, cacheData, cache.InfiniteTTL)
		core_utils.ErrorWarning(err)

		return result, err
	} else {
		return result, nil
	}
}

// Widgets is the resolver for the widgets field.
func (r *productPageResolver) Widgets(ctx context.Context, obj *model.ProductPage) ([]*model.ProductsSliderWidget, error) {
	if obj == nil || obj.Product == nil {
		return nil, errors.New("product not found")
	}

	store := request_context.GetDataLoader(ctx).GetStore()

	var result []*model.ProductsSliderWidget
	switch p := obj.Product.(type) {
	case *model.SimpleProduct:
		id := p.ID
		if store.GetConfigBool("praktis_upsell/general/enabled", false) {
			w, err := cms.NewRelatedProductsSliderWidget(store, id)
			core_utils.ErrorWarning(err)
			if w != nil {
				result = append(result, w)
			}
		}
	}

	return result, nil
}

// Price is the resolver for the price field.
func (r *simpleProductResolver) Price(ctx context.Context, obj *model.SimpleProduct) (*model.ProductPrice, error) {
	return obj.Price, nil
}

// Stock is the resolver for the stock field.
func (r *simpleProductResolver) Stock(ctx context.Context, obj *model.SimpleProduct) (*model.ProductStock, error) {
	if obj == nil || obj.ID < 1 {
		return nil, errors.New("product not found")
	}

	return stock.GetProductStock(obj.ID, len(obj.Gallery) > 0)
}

// SkuAvailability is the resolver for the skuAvailability field.
func (r *simpleProductResolver) SkuAvailability(ctx context.Context, obj *model.SimpleProduct) ([]*model.StoreAvailabilityItem, error) {
	return product.GetStoreAvailabilityModel(obj.Sku)
}

// BundleProduct returns generated.BundleProductResolver implementation.
func (r *Resolver) BundleProduct() generated.BundleProductResolver { return &bundleProductResolver{r} }

// ProductPage returns generated.ProductPageResolver implementation.
func (r *Resolver) ProductPage() generated.ProductPageResolver { return &productPageResolver{r} }

// SimpleProduct returns generated.SimpleProductResolver implementation.
func (r *Resolver) SimpleProduct() generated.SimpleProductResolver { return &simpleProductResolver{r} }

type bundleProductResolver struct{ *Resolver }
type productPageResolver struct{ *Resolver }
type simpleProductResolver struct{ *Resolver }
