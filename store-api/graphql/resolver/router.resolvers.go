package resolver

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.53

import (
	"context"
	"errors"

	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis/router"
	"praktis.bg/store-api/internal/response_handler"
	magento_core "praktis.bg/store-api/packages/magento-core"
)

// Route is the resolver for the route field.
func (r *queryResolver) Route(ctx context.Context, url string, query []*model.QueryParam) (*model.Page, error) {
	store := request_context.GetDataLoader(ctx).GetStore()
	urlRewrite, err := magento_core.NewUrlRewriteRepository(store).Get(url)
	if err != nil {
		return nil, err
	}

	if urlRewrite.GetRedirectCode() > 0 {
		return &model.Page{
			Status: &model.PageStatus{
				StatusCode: urlRewrite.GetRedirectCode(),
				RedirectURL: core_utils.ToStringPointer(
					urlRewrite.TargetPath,
				),
			},
		}, nil
	} else if urlRewrite.IDPath != "" {
		var pageData *model.Page
		pageData, err = router.GetRoutePage(urlRewrite, query)
		if err != nil {
			return nil, err
		}

		return pageData, nil
	}

	return nil, errors.New(response_handler.NotFoundError)
}

// RouteMeta is the resolver for the routeMeta field.
func (r *queryResolver) RouteMeta(ctx context.Context, url string, query []*model.QueryParam) (*model.PageMeta, error) {
	store := request_context.GetDataLoader(ctx).GetStore()

	var err error
	var urlRewrite *magento_core.UrlRewrite
	_url := url
	if router.IsHomePage(url) > 0 {
		return router.GetCmsPageMeta(1, store)
	} else {
		urlRewrite, err = magento_core.NewUrlRewriteRepository(store).Get(url)
		if err != nil {
			return router.GetDefaultMeta(store, url), err
		}
	}

	switch urlRewrite.IDPath.Type() {
	case magento_core.ProductUrl:
		return router.GetProductMeta(urlRewrite.IDPath.GetID(), store)
	case magento_core.CmsPageUrl:
		return router.GetCmsPageMeta(urlRewrite.IDPath.GetID(), store)
	case magento_core.CategoryUrl:
		return router.GetCategoryMeta(urlRewrite.IDPath.GetID(), store)
	case magento_core.SplashPageUrl:
		return router.GetSplashPageMeta(urlRewrite.IDPath.GetID(), store)
	}

	return router.GetDefaultMeta(store, _url), nil
}
