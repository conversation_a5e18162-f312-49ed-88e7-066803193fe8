package request_context

import (
	"context"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/entity"
	"praktis.bg/store-api/graphql/model"
	"praktis.bg/store-api/internal/praktis"
	"praktis.bg/store-api/internal/praktis/catalog"
	product_index "praktis.bg/store-api/internal/praktis/catalog/product"
	"praktis.bg/store-api/internal/praktis/customer"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"praktis.bg/store-api/packages/magento-core/mage-store/sales"
	"sync"
)

const RequestDataContextKey = "RequestData"
const TokenHeader = "X-JWT-token"

type DataLoader struct {
	locks struct {
		products sync.RWMutex
		cart     sync.RWMutex
		customer sync.RWMutex
	}
	ProductCollection *catalog.ProductIndexCollection
	Quote             *sales.MagentoQuote
	RawRequestToken   string
	CustomerToken     *customer.CustomerToken
	Customer          *customer.MagentoCustomer
	Stores            []*entity.PraktisStoreEntity
}

func UseMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		data := &DataLoader{
			locks: struct {
				products sync.RWMutex
				cart     sync.RWMutex
				customer sync.RWMutex
			}{
				products: sync.RWMutex{},
				cart:     sync.RWMutex{},
				customer: sync.RWMutex{},
			},
		}

		data.RawRequestToken = c.GetHeader(customer.CustomerHeader)

		ctx := context.WithValue(
			c.Request.Context(),
			RequestDataContextKey,
			data,
		)
		c.Request = c.Request.WithContext(ctx)

		c.Next()
	}
}

func GetDataLoader(ctx context.Context) *DataLoader {
	result, ok := ctx.Value(RequestDataContextKey).(*DataLoader)
	if result == nil || !ok {
		return &DataLoader{}
	}

	return result
}

func (r *DataLoader) GetProductCollection(obj *model.CatalogPage) (*catalog.ProductIndexCollection, error) {
	var err error
	r.locks.products.RLock()

	if r.ProductCollection != nil {
		r.locks.products.RUnlock()
		return r.ProductCollection, nil
	}

	r.locks.products.RUnlock()
	r.locks.products.Lock()
	defer r.locks.products.Unlock()

	switch c := obj.Category.(type) {
	case *model.Category:
		r.ProductCollection, err = catalog.NewCategoryProductCollection(
			r.GetStore(),
			obj.State,
			c.ID,
		)
	case *model.SplashPage:
		r.ProductCollection, err = catalog.NewSplashPageProductCollection(
			r.GetStore(),
			obj.State,
			c.ID,
		)
	}

	return r.ProductCollection, err
}

func (r *DataLoader) SetQuote(q *sales.MagentoQuote) {
	r.locks.cart.Lock()
	defer r.locks.cart.Unlock()

	r.Quote = q
}

func (r *DataLoader) GetQuoteStores() ([]*entity.PraktisStoreEntity, error) {
	if r.Stores != nil {
		return r.Stores, nil
	}

	if r.Quote == nil {
		core_utils.Debug("No quote found")
		return []*entity.PraktisStoreEntity{}, nil
	}

	var productIdsQtyMap map[int64]float64
	for _, i := range r.Quote.MustGetItems() {
		productIdsQtyMap[i.ProductID] = i.Qty
	}

	var err error
	r.Stores, err = product_index.GetPickupStores(productIdsQtyMap)

	return r.Stores, err
}

func (r *DataLoader) GetTokenQuote(cartToken string) (*sales.MagentoQuote, error) {
	r.locks.cart.Lock()
	defer r.locks.cart.Unlock()

	data, err := sales.GetCartTokenData(cartToken)
	if err != nil {
		return nil, err
	}

	if r.Quote != nil && r.Quote.CartID == data.CartID {
		return r.Quote, nil
	}

	repo := sales.NewQuoteRepository(nil)
	r.Quote, err = repo.GetByCartID(data.CartID)
	if err != nil {
		return nil, err
	} else if r.Quote != nil {
		r.Quote.Token = data
		r.Quote.RawToken, err = data.String()

		if r.Quote.IsActive.Int16 < 1 {
			return r.Quote, fmt.Errorf("неактивна количка")
		}
	}

	return r.Quote, err
}

var NoCartError = errors.New("няма заредена количка")

func (r *DataLoader) GetQuote(id int64) (*sales.MagentoQuote, error) {
	r.locks.cart.Lock()
	defer r.locks.cart.Unlock()

	if r.Quote != nil && r.Quote.EntityID == id {
		return r.Quote, nil
	}

	var err error
	r.Quote, err = sales.NewQuoteRepository(nil).GetByID(id)
	if r.Quote == nil {
		return nil, fmt.Errorf("липсва количка с ID: %d", id)
	} else if r.Quote.IsActive.Int16 < 1 {
		return nil, fmt.Errorf("неактивка количла")
	}

	return r.Quote, err
}

func (r *DataLoader) GetStore() *magento_core.StoreEntity {
	return praktis.GetPraktisStore()
}

func (r *DataLoader) ReloadCart() (*sales.MagentoQuote, error) {
	r.locks.cart.Lock()
	defer r.locks.cart.Unlock()

	if r.Quote == nil || (r.Quote.CartID == "" && r.Quote.EntityID < 1) {
		return nil, NoCartError
	}

	var err error
	r.Quote, err = r.Quote.Load()

	return r.Quote, err
}

var NoCustomerTokenError = errors.New("потребителя не е е логнат")
var TokenNotInCache = errors.New("токена не е намерен в кеша")

type InvalidTokenError struct {
	Token           string
	ValidationError error
}

func (e *InvalidTokenError) Error() string {
	return fmt.Sprintf("невалиден токен: %s", e.ValidationError)
}

func (r *DataLoader) GetCustomerToken() (*customer.CustomerToken, error) {
	if r.CustomerToken != nil {
		return r.CustomerToken, nil
	} else if r.RawRequestToken == "" {
		return nil, NoCustomerTokenError
	}

	var err error
	r.CustomerToken, err = customer.NewCustomerToken(r.RawRequestToken)
	if err != nil {
		return r.CustomerToken, &InvalidTokenError{
			Token:           r.RawRequestToken,
			ValidationError: err,
		}
	}

	if err = r.CustomerToken.Validate(); err != nil {
		return r.CustomerToken, &InvalidTokenError{
			Token:           r.RawRequestToken,
			ValidationError: err,
		}
	}

	if (&customer.MagentoCustomer{
		EntityID: r.CustomerToken.CustomerID,
	}).TokenIsInCache(r.RawRequestToken) {
		return r.CustomerToken, nil
	}

	return r.CustomerToken, TokenNotInCache
}

func (r *DataLoader) GetCurrentCustomerID() int64 {
	customerToken, _ := r.GetCustomerToken()
	var customerID int64
	if customerToken != nil {
		customerID = customerToken.CustomerID
	}

	return customerID
}

func (r *DataLoader) SetCustomer(c *customer.MagentoCustomer) {
	r.Customer = c
	return
}

func (r *DataLoader) GetCustomer() (*customer.MagentoCustomer, error) {
	r.locks.customer.RLock()
	if r.Customer != nil {
		r.locks.customer.RUnlock()
		return r.Customer, nil
	}

	r.locks.customer.RUnlock()
	r.locks.customer.Lock()
	defer r.locks.customer.Unlock()

	customerToken, err := r.GetCustomerToken()
	if err != nil {
		return r.Customer, err
	}

	r.Customer, err = customer.NewCustomerRepository(nil).GetByID(customerToken.CustomerID)
	if r.Customer != nil {
		r.Customer.SetToken(customerToken)
	}

	return r.Customer, err
}

func (r *DataLoader) ReloadCustomer() (*customer.MagentoCustomer, error) {
	customerToken, err := r.GetCustomerToken()
	if err != nil {
		return r.Customer, err
	}

	r.Customer, err = customer.NewCustomerRepository(nil).GetByID(customerToken.CustomerID)
	if r.Customer != nil {
		r.Customer.SetToken(customerToken)
	}

	return r.Customer, err
}
