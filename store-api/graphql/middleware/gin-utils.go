package middleware

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
)

func GinContextToContextMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		ctx := context.WithValue(c.Request.Context(), "GinContextKey", c)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

func GinContextFromContext(ctx context.Context) (*gin.Context, error) {
	ginContext := ctx.Value("GinContextKey")
	if ginContext == nil {
		err := fmt.<PERSON><PERSON>rf("could not retrieve gin.Context")
		return nil, err
	}

	gc, ok := ginContext.(*gin.Context)
	if !ok {
		err := fmt.Errorf("gin.Context has wrong type")
		return nil, err
	}
	return gc, nil
}

func GetCustomerIPAddress(ctx context.Context) string {
	gc, err := GinContextFromContext(ctx)
	if err != nil {
		return ""
	}

	ip := gc.Get<PERSON>eader("X-Forwarded-For")
	if ip != "" {
		return ip
	}

	return gc.ClientIP()
}
