package directives

import (
	"context"
	"fmt"
	"github.com/99designs/gqlgen/graphql"
	core_utils "github.com/siper92/core-utils"
	"praktis.bg/store-api/graphql/middleware"
	"praktis.bg/store-api/graphql/middleware/request_context"
	"praktis.bg/store-api/internal/config"
	"praktis.bg/store-api/packages/magento-core/auth"
	"time"
)

const CaptchaHeader = "x-captcha-token"

func HasValidCaptcha() func(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error) {
	return func(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error) {
		gc, err := middleware.GinContextFromContext(ctx)
		if err != nil {
			return nil, err
		}

		captchaToken := gc.GetHeader(CaptchaHeader)
		if captchaToken == "" || len(captchaToken) < 20 {
			if config.GetConfig().IsDevMode() {
				return next(ctx)
			}

			return nil, fmt.Errorf("captcha token is missing")
		}

		storeObj := request_context.GetDataLoader(ctx).GetStore()
		secret := storeObj.GetConfig("pfg_theme/api_keys/google_recaptcha_secret", "")

		start := time.Now()
		valid, err := auth.SyncValidateCaptcha(secret, captchaToken)
		if err != nil {
			return nil, err
		}

		if !valid {
			return nil, fmt.Errorf("invalid captcha")
		}

		core_utils.Debug("Captcha validation took %v\n", time.Since(start))

		return next(ctx)
	}
}
