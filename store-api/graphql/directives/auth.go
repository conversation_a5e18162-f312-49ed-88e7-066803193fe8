package directives

import (
	"context"
	"errors"
	"fmt"
	"github.com/99designs/gqlgen/graphql"
	"github.com/golang-jwt/jwt/v5"
	"praktis.bg/store-api/graphql/middleware/request_context"
)

func HasValidToken() func(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error) {
	return func(ctx context.Context, obj interface{}, next graphql.Resolver) (res interface{}, err error) {
		_, err = request_context.GetDataLoader(ctx).GetCustomerToken()
		if err != nil {
			if errors.Is(err, jwt.ErrTokenInvalidClaims) {
				return nil, errors.New("невалиден токен")
			}

			return nil, fmt.Errorf("невалиден токен: %w", err)
		}

		return next(ctx)
	}
}
