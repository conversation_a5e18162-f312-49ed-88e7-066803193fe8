// Code generated by github.com/99designs/gqlgen, DO NOT EDIT.

package model

import (
	"fmt"
	"io"
	"strconv"
)

type CatalogCategory interface {
	IsCatalogCategory()
}

type CategoryWidget interface {
	IsCategoryWidget()
}

type PageData interface {
	IsPageData()
}

type Product interface {
	IsProduct()
}

type Tile interface {
	IsTile()
}

type Widget interface {
	IsWidget()
}

type ActionResult struct {
	Code    ActionResultCode `json:"code"`
	Message *string          `json:"message,omitempty"`
}

type APIKeys struct {
	GoogleMaps          string `json:"googleMaps"`
	GoogleLoginClientID string `json:"googleLoginClientId"`
	GoogleRecaptchaKey  string `json:"googleRecaptchaKey"`
	FacebookLoginAppID  string `json:"facebookLoginAppId"`
}

type ApplicationLinks struct {
	Android *string `json:"android,omitempty"`
	Ios     *string `json:"ios,omitempty"`
}

type AppliedFilter struct {
	AttributeCode string `json:"attributeCode"`
	RequestVar    string `json:"requestVar"`
	Label         string `json:"label"`
	Value         string `json:"value"`
}

type Article struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Link        *Link  `json:"link"`
	Image       *Image `json:"image"`
}

type AttributeOption struct {
	Label string `json:"label"`
	Value string `json:"value"`
	Order int    `json:"order"`
}

type AuthToken struct {
	Token *string `json:"token,omitempty"`
}

type AvailableFilter struct {
	Label         string             `json:"label"`
	AttributeCode string             `json:"attributeCode"`
	Options       []*AttributeOption `json:"options"`
	Type          FilterRenderType   `json:"type"`
	RequestVar    string             `json:"requestVar"`
	Position      int                `json:"position"`
}

type AvailableShippingMethod struct {
	Method   *ShippingMethod `json:"method"`
	ErrorMsg string          `json:"errorMsg"`
	Price    *Price          `json:"price"`
}

type BNPCustomerDataInput struct {
	FirstName   string  `json:"firstName"`
	LastName    string  `json:"lastName"`
	Phone       string  `json:"phone"`
	Email       string  `json:"email"`
	Address     string  `json:"address"`
	City        string  `json:"city"`
	PostCode    string  `json:"postCode"`
	Egn         *string `json:"egn,omitempty"`
	CompanyName *string `json:"companyName,omitempty"`
	Eik         *string `json:"eik,omitempty"`
	Mol         *string `json:"mol,omitempty"`
}

type BNPGoodCategory struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

type BNPGoodType struct {
	ID         int64  `json:"id"`
	Name       string `json:"name"`
	CategoryID int64  `json:"categoryId"`
}

type BNPPaymentInput struct {
	DownPayment      float64               `json:"downPayment"`
	PricingVariantID int                   `json:"pricingVariantId"`
	CustomerData     *BNPCustomerDataInput `json:"customerData"`
}

type BNPPricingScheme struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

type BNPVariantGroup struct {
	SchemeID string            `json:"schemeId"`
	Variants []*PricingVariant `json:"variants"`
}

type BlogCategory struct {
	ID       int             `json:"id"`
	URLKey   string          `json:"urlKey"`
	Name     string          `json:"name"`
	Children []*BlogCategory `json:"children"`
	Posts    []*BlogPost     `json:"posts"`
}

type BlogPost struct {
	URLKey          string `json:"urlKey"`
	Title           string `json:"title"`
	Summary         string `json:"summary"`
	PreviewImageURL string `json:"previewImageUrl"`
	Content         string `json:"content"`
	MainImageURL    string `json:"mainImageUrl"`
	PublishedAt     string `json:"publishedAt"`
}

type Brand struct {
	Name  string `json:"name"`
	Image *Image `json:"image"`
	URL   *Link  `json:"url"`
}

type Breadcrumb struct {
	Label    string        `json:"label"`
	URL      string        `json:"url"`
	Siblings []*Breadcrumb `json:"siblings,omitempty"`
	Children []*Breadcrumb `json:"children,omitempty"`
}

type BundleProduct struct {
	ID               int64                    `json:"id"`
	Sku              string                   `json:"sku"`
	Name             string                   `json:"name"`
	Price            *ProductPrice            `json:"price"`
	URLKey           string                   `json:"urlKey"`
	ShortDescription string                   `json:"shortDescription"`
	Description      string                   `json:"description"`
	Stock            *ProductStock            `json:"stock,omitempty"`
	Brand            *Brand                   `json:"brand,omitempty"`
	Labels           *MagentoLabels           `json:"labels"`
	Image            *Image                   `json:"image"`
	Gallery          []*GalleryImage          `json:"gallery"`
	Videos           []*ProductVideo          `json:"videos,omitempty"`
	Bundled          []*SimpleProduct         `json:"bundled"`
	SkuAvailability  []*StoreAvailabilityItem `json:"skuAvailability"`
}

func (BundleProduct) IsProduct() {}

type CMSBlock struct {
	Identifier string `json:"identifier"`
	Title      string `json:"title"`
	Content    string `json:"content"`
}

type CMSPage struct {
	Links      []*Link `json:"links"`
	Identifier string  `json:"identifier"`
	Title      string  `json:"title"`
	Content    string  `json:"content"`
}

func (CMSPage) IsPageData() {}

type CacheConfig struct {
	UseCache  *bool    `json:"useCache,omitempty"`
	CustomKey *string  `json:"customKey,omitempty"`
	ExtraKeys []string `json:"extraKeys,omitempty"`
	TTLMin    *int     `json:"ttlMin,omitempty"`
}

type CarouselSlide struct {
	Title       string         `json:"title"`
	Description string         `json:"description"`
	Link        *Link          `json:"link,omitempty"`
	Price       *Price         `json:"price,omitempty"`
	PriceLabels *MagentoLabels `json:"priceLabels,omitempty"`
	Image       *Image         `json:"image"`
	Features    []*Image       `json:"features,omitempty"`
	Brand       *Brand         `json:"brand,omitempty"`
}

type CarouselWidget struct {
	Identifier string           `json:"identifier"`
	Slides     []*CarouselSlide `json:"slides"`
}

func (CarouselWidget) IsWidget() {}

type CartShipping struct {
	AvailableMethods                []ShippingMethodType `json:"availableMethods"`
	AvailableIn                     []*PraktisStore      `json:"availableIn"`
	HasFreeShipping                 bool                 `json:"hasFreeShipping"`
	FreeShippingAfter               *int                 `json:"freeShippingAfter,omitempty"`
	MinAmountForFreeShippingMessage *int                 `json:"minAmountForFreeShippingMessage,omitempty"`
	SelectedMethod                  string               `json:"selectedMethod"`
	Address                         *ShippingAddress     `json:"address,omitempty"`
}

type CartTotal struct {
	Code   CartTotalCode `json:"code"`
	Label  string        `json:"label"`
	Amount *Price        `json:"amount"`
	Order  int           `json:"order"`
}

type CatalogPage struct {
	Layout   CatalogLayout   `json:"layout"`
	State    *CatalogState   `json:"state"`
	Category CatalogCategory `json:"category"`
	Products []Product       `json:"products"`
}

func (CatalogPage) IsPageData() {}

type CatalogState struct {
	Filters        *Filters        `json:"filters"`
	Pager          *Pager          `json:"pager"`
	AvailableSorts []*SortField    `json:"availableSorts"`
	Sort           *CollectionSort `json:"sort"`
}

type Category struct {
	ID          int64            `json:"id"`
	URL         string           `json:"url"`
	Name        string           `json:"name"`
	Description *string          `json:"description,omitempty"`
	Image       *Image           `json:"image,omitempty"`
	Icon        *Image           `json:"icon,omitempty"`
	Banner      *CategoryBanner  `json:"banner,omitempty"`
	Widgets     []CategoryWidget `json:"widgets,omitempty"`
	Children    []*Category      `json:"children,omitempty"`
}

func (Category) IsCatalogCategory() {}

type CategoryBanner struct {
	Image *Image `json:"image"`
	URL   string `json:"url"`
}

type CategoryLinkWidget struct {
	Title string `json:"title"`
	URL   *Link  `json:"url"`
	Image *Image `json:"image"`
}

func (CategoryLinkWidget) IsCategoryWidget() {}

func (CategoryLinkWidget) IsWidget() {}

type CategoryTile struct {
	Title      string  `json:"title"`
	Image      *Image  `json:"image"`
	BgColor    string  `json:"bgColor"`
	TextColor  string  `json:"textColor"`
	Categories []*Link `json:"categories"`
}

func (CategoryTile) IsTile() {}

type ClearCacheConfig struct {
	Clear     *bool    `json:"clear,omitempty"`
	ExtraKeys []string `json:"extraKeys,omitempty"`
}

type ClientInput struct {
	FirstName       string        `json:"firstName"`
	LastName        string        `json:"lastName"`
	Phone           string        `json:"phone"`
	Email           string        `json:"email"`
	RegisterOnOrder bool          `json:"registerOnOrder"`
	Password        string        `json:"password"`
	Invoice         *InvoiceInput `json:"invoice,omitempty"`
}

type CollectionSort struct {
	Value string        `json:"value"`
	Dir   SortDirection `json:"dir"`
}

type CompanyInvoice struct {
	Name string `json:"name"`
	Mol  string `json:"mol"`
	Eik  string `json:"eik"`
	Vat  string `json:"vat"`
}

type CompanyInvoiceInput struct {
	Name string `json:"name"`
	Mol  string `json:"mol"`
	Eik  string `json:"eik"`
	Vat  string `json:"vat"`
}

type Constraints struct {
	OnDelete *ConstraintType `json:"onDelete,omitempty"`
	OnUpdate *ConstraintType `json:"onUpdate,omitempty"`
}

type ContactInfo struct {
	Phone *string `json:"phone,omitempty"`
}

type ContactInput struct {
	Name      string `json:"name"`
	Email     string `json:"email"`
	Telephone string `json:"telephone"`
	Comment   string `json:"comment"`
}

type CookieGroup struct {
	ID             string             `json:"id"`
	Title          string             `json:"title"`
	Content        string             `json:"content"`
	Grants         []GDPRGrants       `json:"grants"`
	CookiePatterns []string           `json:"cookiePatterns"`
	Vendors        *GDPRCookieVendors `json:"vendors"`
}

type Customer struct {
	ID                       int64              `json:"id"`
	Token                    string             `json:"token"`
	IsSubscribed             bool               `json:"isSubscribed"`
	FirstName                string             `json:"firstName"`
	LastName                 string             `json:"lastName"`
	Email                    string             `json:"email"`
	DefaultBillingAddressID  *int64             `json:"defaultBillingAddressID,omitempty"`
	DefaultShippingAddressID *int64             `json:"defaultShippingAddressID,omitempty"`
	Addresses                []*CustomerAddress `json:"addresses"`
	Orders                   []*StoreOrder      `json:"orders"`
	Wishlist                 *CustomerWishlist  `json:"wishlist"`
	Social                   *SocialLogins      `json:"social"`
}

type CustomerAddress struct {
	ID          int64   `json:"id"`
	FirstName   string  `json:"firstName"`
	LastName    string  `json:"lastName"`
	Phone       string  `json:"phone"`
	CompanyName *string `json:"companyName,omitempty"`
	Country     string  `json:"country"`
	City        string  `json:"city"`
	CityID      string  `json:"cityID"`
	PostCode    string  `json:"postCode"`
	Street      string  `json:"street"`
}

type CustomerAddressInput struct {
	IsDefaultBilling  *bool   `json:"isDefaultBilling,omitempty"`
	IsDefaultShipping *bool   `json:"isDefaultShipping,omitempty"`
	FirstName         string  `json:"firstName"`
	LastName          string  `json:"lastName"`
	Phone             string  `json:"phone"`
	CompanyName       *string `json:"companyName,omitempty"`
	City              string  `json:"city"`
	CityID            string  `json:"cityID"`
	PostCode          string  `json:"postCode"`
	Street            string  `json:"street"`
}

type CustomerInvoice struct {
	ID         int64              `json:"id"`
	Type       InvoiceType        `json:"type"`
	City       string             `json:"city"`
	Address    string             `json:"address"`
	Company    *CompanyInvoice    `json:"company,omitempty"`
	Individual *IndividualInvoice `json:"individual,omitempty"`
}

type CustomerRegistrationData struct {
	Email               string        `json:"email"`
	Firstname           string        `json:"firstname"`
	Lastname            string        `json:"lastname"`
	Password            string        `json:"password"`
	Phone               string        `json:"phone"`
	NewsletterSubscribe bool          `json:"newsletterSubscribe"`
	Invoice             *InvoiceInput `json:"invoice,omitempty"`
}

type CustomerUpdateInput struct {
	FirstName                *string `json:"firstName,omitempty"`
	LastName                 *string `json:"lastName,omitempty"`
	Email                    *string `json:"email,omitempty"`
	NewsletterSubscribe      *bool   `json:"newsletterSubscribe,omitempty"`
	DefaultBillingAddressID  *int64  `json:"defaultBillingAddressID,omitempty"`
	DefaultShippingAddressID *int64  `json:"defaultShippingAddressID,omitempty"`
}

type CustomerWishlist struct {
	Skus     []string  `json:"skus"`
	Products []Product `json:"products"`
}

type DescriptionArea struct {
	Title           string  `json:"title"`
	Content         string  `json:"content"`
	FormID          *string `json:"formID,omitempty"`
	BackgroundImage string  `json:"backgroundImage"`
}

type DoubleImageTile struct {
	ImageOnePosition DoubleImageTilePosition `json:"imageOnePosition"`
	ImageOne         *Image                  `json:"imageOne"`
	ContentOne       *TileContent            `json:"contentOne"`
	ImageTwoPosition DoubleImageTilePosition `json:"imageTwoPosition"`
	ImageTwo         *Image                  `json:"imageTwo"`
	ContentTwo       *TileContent            `json:"contentTwo"`
}

func (DoubleImageTile) IsTile() {}

type EcontCity struct {
	ID           int64         `json:"id"`
	Country      *EcontCountry `json:"country"`
	Type         string        `json:"type"`
	Name         string        `json:"name"`
	NameEn       string        `json:"nameEn"`
	PostCode     string        `json:"postCode"`
	RegionName   string        `json:"regionName"`
	RegionNameEn string        `json:"regionNameEn"`
	RegionCode   string        `json:"regionCode"`
}

type EcontCountry struct {
	ID     int64  `json:"id"`
	Code2  string `json:"code2"`
	Code3  string `json:"code3"`
	Name   string `json:"name"`
	NameEn string `json:"nameEn"`
	IsEu   bool   `json:"isEU"`
}

type EcontOffice struct {
	ID       int64               `json:"id"`
	Code     string              `json:"code"`
	Name     string              `json:"name"`
	NameEn   string              `json:"nameEn"`
	Phones   []string            `json:"phones"`
	IsAps    bool                `json:"isAPS"`
	IsMps    bool                `json:"isMPS"`
	Adderess *EcontOfficeAddress `json:"adderess"`
}

type EcontOfficeAddress struct {
	City          *EcontCity           `json:"city"`
	FullAddress   string               `json:"fullAddress"`
	FullAddressEn string               `json:"fullAddressEn"`
	Location      *EcontOfficeLocation `json:"location"`
}

type EcontOfficeLocation struct {
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

type EditExtraValidations struct {
	BeforeSave []string `json:"beforeSave,omitempty"`
	AfterSave  []string `json:"afterSave,omitempty"`
}

type EnergyLabel struct {
	Image    *Image  `json:"image"`
	LabelURL *string `json:"labelUrl,omitempty"`
	InfoURL  *string `json:"infoUrl,omitempty"`
}

type EntityToModel struct {
	Enable        *bool    `json:"enable,omitempty"`
	ExcludeFields []string `json:"excludeFields,omitempty"`
}

type ErrorData struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func (ErrorData) IsPageData() {}

type Filter struct {
	Condition string  `json:"Condition"`
	Value     *string `json:"value,omitempty"`
}

type Filters struct {
	Available []*AvailableFilter `json:"available"`
	Applied   []*AppliedFilter   `json:"applied"`
}

type Footer struct {
	Columns  []*FooterColumn   `json:"columns,omitempty"`
	Social   *SocialLinks      `json:"social,omitempty"`
	AppLinks *ApplicationLinks `json:"appLinks,omitempty"`
	Contacts *StoreContacts    `json:"contacts,omitempty"`
}

type FooterColumn struct {
	Title *string `json:"title,omitempty"`
	Links []*Link `json:"links,omitempty"`
}

type FormData struct {
	Title  string `json:"title"`
	Text   string `json:"text"`
	FormID string `json:"formId"`
}

type GDPRConfig struct {
	RootID        string           `json:"rootId"`
	Modal         *GDPRModalConfig `json:"modal"`
	PixelID       string           `json:"pixelId"`
	GtagID        string           `json:"gtagId"`
	ExtraGTagsIds []string         `json:"extraGTagsIds"`
	ClarityID     string           `json:"clarityId"`
}

type GDPRCookieDetails struct {
	ID          string `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Type        string `json:"type"`
	Expiration  string `json:"expiration"`
}

type GDPRCookieVendorConfig struct {
	ID            string               `json:"id"`
	Name          string               `json:"name"`
	URL           string               `json:"url"`
	CookieCount   int                  `json:"cookieCount"`
	CookieDetails []*GDPRCookieDetails `json:"cookieDetails"`
}

type GDPRCookieVendors struct {
	Keys   []CookieVendorKey         `json:"keys"`
	Config []*GDPRCookieVendorConfig `json:"config"`
}

type GDPRModalConfig struct {
	Title        string         `json:"title"`
	Content      string         `json:"content"`
	CookieGroups []*CookieGroup `json:"cookieGroups"`
}

type GalleryImage struct {
	Image    *Image `json:"image"`
	Position int    `json:"position"`
}

type HeaderData struct {
	Scripts []string `json:"scripts"`
}

type HomePage struct {
	HeroSlider *CarouselWidget `json:"heroSlider"`
	Widgets    []Widget        `json:"widgets"`
}

type HTMLWidget struct {
	HTML *string `json:"html,omitempty"`
}

func (HTMLWidget) IsWidget() {}

type Image struct {
	Src       string  `json:"src"`
	MobileSrc *string `json:"mobileSrc,omitempty"`
	Alt       *string `json:"alt,omitempty"`
	Title     *string `json:"title,omitempty"`
}

type ImageTile struct {
	Image    *Image              `json:"image"`
	BgColor  string              `json:"bgColor"`
	Position TileContentPosition `json:"position"`
	Content  *TileContent        `json:"content"`
}

func (ImageTile) IsTile() {}

type IndividualInvoice struct {
	Name string `json:"name"`
	Egn  string `json:"egn"`
	Vat  string `json:"vat"`
}

type IndividualInvoiceInput struct {
	Name string `json:"name"`
	Egn  string `json:"egn"`
	Vat  string `json:"vat"`
}

type InvoiceInput struct {
	Type       InvoiceType             `json:"type"`
	City       string                  `json:"city"`
	Address    string                  `json:"address"`
	Company    *CompanyInvoiceInput    `json:"company,omitempty"`
	Individual *IndividualInvoiceInput `json:"individual,omitempty"`
}

type KeyVal struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Link struct {
	Href  string  `json:"href"`
	Text  string  `json:"text"`
	Title *string `json:"title,omitempty"`
}

type LoanCalculation struct {
	Apr                      string `json:"apr"`
	CorrectDownpaymentAmount string `json:"correctDownpaymentAmount"`
	InstallmentAmount        string `json:"installmentAmount"`
	Maturity                 string `json:"maturity"`
	Nir                      string `json:"nir"`
	PricingSchemeID          string `json:"pricingSchemeId"`
	PricingSchemeName        string `json:"pricingSchemeName"`
	PricingVariantID         string `json:"pricingVariantId"`
	ProcessingFeeAmount      string `json:"processingFeeAmount"`
	TotalRepaymentAmount     string `json:"totalRepaymentAmount"`
}

type MagentoBlog struct {
	Posts       []*BlogPost `json:"posts"`
	CurrentPage int         `json:"currentPage"`
	TotalPages  int         `json:"totalPages"`
}

type MagentoLabels struct {
	WarrantyMonths    int      `json:"warrantyMonths"`
	FromBrochure      *bool    `json:"fromBrochure,omitempty"`
	FreeDelivery      *bool    `json:"freeDelivery,omitempty"`
	FreeDeliveryUntil *string  `json:"freeDeliveryUntil,omitempty"`
	BuyCheap          *bool    `json:"buyCheap,omitempty"`
	Other             []string `json:"other,omitempty"`
}

type MapLocation struct {
	Lat float64 `json:"lat"`
	Lng float64 `json:"lng"`
}

type MapValue struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Menu struct {
	Categories []*MenuItem `json:"categories"`
}

type MenuItem struct {
	ID          int64            `json:"id"`
	URL         string           `json:"url"`
	Name        string           `json:"name"`
	Thumbnail   string           `json:"thumbnail"`
	SideSection *MenuSideSection `json:"sideSection,omitempty"`
	Children    []*MenuItem      `json:"children,omitempty"`
}

type MenuSideSection struct {
	Image   string `json:"image"`
	URL     string `json:"url"`
	RawHTML string `json:"rawHtml"`
}

type Mutation struct {
}

type NewCartItem struct {
	Sku     string  `json:"sku"`
	BaseQty float64 `json:"baseQty"`
}

type NewOrderInput struct {
	Client             *ClientInput   `json:"client"`
	Shipping           *ShippingInput `json:"shipping"`
	ShippingMethodCode string         `json:"shippingMethodCode"`
	PaymentMethodCode  string         `json:"paymentMethodCode"`
	PromoCode          *string        `json:"promoCode,omitempty"`
	Note               *string        `json:"note,omitempty"`
}

type NewsWidget struct {
	Identifier string     `json:"identifier"`
	Title      string     `json:"title"`
	Subtitle   string     `json:"subtitle"`
	Link       *Link      `json:"link"`
	Articles   []*Article `json:"articles"`
}

func (NewsWidget) IsWidget() {}

type NotificationMessage struct {
	Title   string           `json:"title"`
	Message string           `json:"message"`
	Type    NotificationType `json:"type"`
}

type OrderAddress struct {
	ID        int64  `json:"id"`
	FirstName string `json:"firstName"`
	LastName  string `json:"lastName"`
	Email     string `json:"email"`
	Telephone string `json:"telephone"`
	City      string `json:"city"`
	Postcode  string `json:"postcode"`
	Street    string `json:"street"`
}

type OrderCustomer struct {
	FirstName string           `json:"firstName"`
	LastName  string           `json:"lastName"`
	Email     string           `json:"email"`
	Phone     string           `json:"phone"`
	Invoice   *CustomerInvoice `json:"invoice,omitempty"`
}

type OrderItem struct {
	ID                      int64   `json:"id"`
	Sku                     string  `json:"sku"`
	Product                 Product `json:"product"`
	BaseQty                 float64 `json:"baseQty"`
	DiscountAmount          *Price  `json:"discountAmount"`
	DiscountPercent         float64 `json:"discountPercent"`
	Price                   *Price  `json:"price"`
	PriceWithoutDiscount    *Price  `json:"priceWithoutDiscount"`
	RowTotal                *Price  `json:"rowTotal"`
	RowTotalWithoutDiscount *Price  `json:"rowTotalWithoutDiscount"`
}

type OrderRedirect struct {
	URL  string      `json:"url"`
	Data []*MapValue `json:"data"`
}

type OrderStatus struct {
	Label string `json:"label"`
	Code  string `json:"code"`
}

type Page struct {
	Status      *PageStatus   `json:"status"`
	Breadcrumbs []*Breadcrumb `json:"breadcrumbs"`
	Data        PageData      `json:"data,omitempty"`
}

type PageMessages struct {
	Newsletter         string `json:"newsletter"`
	SendInquiryMessage string `json:"sendInquiryMessage"`
}

type PageMeta struct {
	Title        string `json:"title"`
	Description  string `json:"description"`
	Keywords     string `json:"keywords"`
	Image        *Image `json:"image,omitempty"`
	CanonicalURL string `json:"canonicalUrl"`
	Robots       string `json:"robots"`
}

type PageStatus struct {
	StatusCode  int     `json:"statusCode"`
	RedirectURL *string `json:"redirectUrl,omitempty"`
	Error       *string `json:"error,omitempty"`
}

type Pager struct {
	Page       int `json:"page"`
	TotalPages int `json:"totalPages"`
	PageSize   int `json:"pageSize"`
	TotalItems int `json:"totalItems"`
}

type PaymentMethod struct {
	Name         string `json:"name"`
	Code         string `json:"code"`
	ExtraContent string `json:"extraContent"`
}

type PlaceOrderResponse struct {
	OrderNumber string         `json:"orderNumber"`
	Status      *OrderStatus   `json:"status"`
	Redirect    *OrderRedirect `json:"redirect"`
	Order       *StoreOrder    `json:"order"`
}

type PraktisStore struct {
	ID                   int64            `json:"ID"`
	Identity             string           `json:"identity"`
	Name                 string           `json:"name"`
	City                 string           `json:"city"`
	Address              string           `json:"address"`
	Phone                string           `json:"phone"`
	Email                string           `json:"email"`
	DisplayOrder         int              `json:"displayOrder"`
	AcceptOrders         bool             `json:"acceptOrders"`
	WarehouseCode        string           `json:"warehouseCode"`
	WarehouseID          int64            `json:"warehouseID"`
	DescriptionAreaData  string           `json:"descriptionAreaData"`
	DescriptionArea      *DescriptionArea `json:"descriptionArea,omitempty"`
	TransportInformation string           `json:"transportInformation"`
	MetaTitle            string           `json:"metaTitle"`
	MetaDescription      string           `json:"metaDescription"`
	MetaKeywords         string           `json:"metaKeywords"`
	LocationData         string           `json:"locationData"`
	Location             *MapLocation     `json:"location"`
	BusinessHoursData    string           `json:"businessHoursData"`
	BusinessHours        []*StoreSchedule `json:"businessHours"`
	VirtualTour          string           `json:"virtualTour"`
	Gallery              []*StoreImage    `json:"gallery"`
	Services             []*Service       `json:"services"`
}

type Price struct {
	Value    float64 `json:"value"`
	Currency string  `json:"currency"`
}

type PricingVariant struct {
	ID                       string `json:"id"`
	Apr                      string `json:"apr"`
	CorrectDownpaymentAmount string `json:"correctDownpaymentAmount"`
	InstallmentAmount        string `json:"installmentAmount"`
	Maturity                 string `json:"maturity"`
	Nir                      string `json:"nir"`
	PricingSchemeID          string `json:"pricingSchemeId"`
	PricingSchemeName        string `json:"pricingSchemeName"`
	ProcessingFeeAmount      string `json:"processingFeeAmount"`
	TotalRepaymentAmount     string `json:"totalRepaymentAmount"`
	Installment              string `json:"installment"`
	TotalRepayment           string `json:"total_repayment"`
}

type PrivacyData struct {
	PopupAgreement *string `json:"popupAgreement,omitempty"`
}

type ProductMeasures struct {
	Base                 string  `json:"base"`
	Secondary            string  `json:"secondary"`
	SecondaryQty         float64 `json:"secondaryQty"`
	SecondaryMeasureUsed bool    `json:"secondaryMeasureUsed"`
}

type ProductPage struct {
	Product        Product                 `json:"product"`
	BoughtTogether []*SimpleProduct        `json:"boughtTogether,omitempty"`
	StaticBlocks   []*CMSBlock             `json:"staticBlocks,omitempty"`
	Widgets        []*ProductsSliderWidget `json:"widgets,omitempty"`
}

func (ProductPage) IsPageData() {}

type ProductPrice struct {
	Price       *Price  `json:"price"`
	Special     *Price  `json:"special,omitempty"`
	SpecialFrom *string `json:"specialFrom,omitempty"`
	SpecialTo   *string `json:"specialTo,omitempty"`
}

type ProductStock struct {
	Qty                  float64 `json:"qty"`
	MinQty               float64 `json:"minQty"`
	BlockForSale         bool    `json:"blockForSale"`
	ZeronSiteStatus      string  `json:"zeronSiteStatus"`
	ZeronBlockedDelivery bool    `json:"zeronBlockedDelivery"`
	ManageStock          bool    `json:"manageStock"`
	InStock              bool    `json:"inStock"`
	HasImages            bool    `json:"hasImages"`
	ShowOutOfStock       bool    `json:"showOutOfStock"`
}

type ProductVideo struct {
	ID          int64   `json:"id"`
	Title       string  `json:"title"`
	Description *string `json:"description,omitempty"`
	URL         string  `json:"url"`
	VideoType   string  `json:"videoType"`
	Thumbnail   *string `json:"thumbnail,omitempty"`
	Position    int     `json:"position"`
}

type ProductsSliderWidget struct {
	Identifier string         `json:"identifier"`
	Title      string         `json:"title"`
	Subtitle   string         `json:"subtitle"`
	Tabs       []*ProductsTab `json:"tabs"`
}

func (ProductsSliderWidget) IsWidget() {}

type ProductsTab struct {
	Title    string    `json:"title"`
	Products []Product `json:"products"`
}

type Query struct {
}

type QueryParam struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type RegistrationResponse struct {
	Create              bool `json:"create"`
	RequireConfirmation bool `json:"requireConfirmation"`
}

type ResultsPager struct {
	SortField string           `json:"sortField"`
	SortOrder EntityFilterSort `json:"sortOrder"`
	Page      *int             `json:"page,omitempty"`
	PageSize  *int             `json:"pageSize,omitempty"`
}

type SearchCategory struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

func (SearchCategory) IsCatalogCategory() {}

type SearchPage struct {
	Status *PageStatus    `json:"status"`
	State  *CatalogState  `json:"state"`
	Title  string         `json:"title"`
	Data   *SearchResults `json:"data"`
}

type SearchResults struct {
	PopularTerms []string          `json:"popularTerms"`
	Categories   []*SearchCategory `json:"categories"`
	TotalItems   int               `json:"totalItems"`
	Products     []Product         `json:"products"`
	Block        *CMSBlock         `json:"block,omitempty"`
}

type Service struct {
	ID      int64  `json:"id"`
	Name    string `json:"name"`
	URL     string `json:"url"`
	IconURL string `json:"iconUrl"`
	Image   *Image `json:"image"`
}

type ServiceWidget struct {
	Title           string          `json:"title"`
	Subtitle        string          `json:"subtitle"`
	Link            *Link           `json:"link"`
	Services        []*Service      `json:"services"`
	Form            *FormData       `json:"form"`
	AvailableStores []*PraktisStore `json:"availableStores"`
}

func (ServiceWidget) IsWidget() {}

type ShippingAddress struct {
	ID         int64              `json:"id"`
	Skus       []string           `json:"skus"`
	Method     ShippingMethodType `json:"method"`
	OfficeCode string             `json:"officeCode"`
	StoreCode  string             `json:"storeCode"`
	CityID     string             `json:"cityId"`
	City       string             `json:"city"`
	PostCode   string             `json:"postCode"`
	Street     string             `json:"street"`
}

type ShippingInput struct {
	Type       ShippingMethodType `json:"type"`
	CityID     *string            `json:"cityId,omitempty"`
	City       string             `json:"city"`
	PostCode   string             `json:"postCode"`
	Address    string             `json:"address"`
	OfficeCode *string            `json:"officeCode,omitempty"`
	StoreCode  *string            `json:"storeCode,omitempty"`
}

type ShippingMethod struct {
	Name string `json:"name"`
	Code string `json:"code"`
}

type SimpleProduct struct {
	ID               int64                    `json:"id"`
	Sku              string                   `json:"sku"`
	Name             string                   `json:"name"`
	Price            *ProductPrice            `json:"price"`
	URLKey           string                   `json:"urlKey"`
	ShortDescription string                   `json:"shortDescription"`
	Description      string                   `json:"description"`
	Stock            *ProductStock            `json:"stock"`
	Brand            *Brand                   `json:"brand,omitempty"`
	Measures         *ProductMeasures         `json:"measures"`
	Labels           *MagentoLabels           `json:"labels"`
	EnergyLabel      *EnergyLabel             `json:"energyLabel,omitempty"`
	Image            *Image                   `json:"image"`
	Gallery          []*GalleryImage          `json:"gallery"`
	Videos           []*ProductVideo          `json:"videos,omitempty"`
	SkuAvailability  []*StoreAvailabilityItem `json:"skuAvailability"`
}

func (SimpleProduct) IsProduct() {}

type SocialAccountInfo struct {
	ID            string `json:"ID"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verifiedEmail"`
	Name          string `json:"name"`
	PictureURL    string `json:"pictureURL"`
}

type SocialLinks struct {
	Facebook *string `json:"facebook,omitempty"`
	Youtube  *string `json:"youtube,omitempty"`
	Viber    *string `json:"viber,omitempty"`
}

type SocialLoginResponse struct {
	NewRegistration bool      `json:"newRegistration"`
	Customer        *Customer `json:"customer"`
}

type SocialLogins struct {
	Google   *SocialAccountInfo `json:"google,omitempty"`
	Facebook *SocialAccountInfo `json:"facebook,omitempty"`
}

type SortField struct {
	Value string        `json:"value"`
	Label string        `json:"label"`
	Dir   SortDirection `json:"dir"`
}

type SplashPage struct {
	ID          int64  `json:"id"`
	URL         string `json:"url"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Image       *Image `json:"image"`
}

func (SplashPage) IsCatalogCategory() {}

type StaticContent struct {
	APIKeys  *APIKeys      `json:"apiKeys"`
	Header   *HeaderData   `json:"header"`
	Menu     *Menu         `json:"menu"`
	Footer   *Footer       `json:"footer"`
	Store    *StoreInfo    `json:"store"`
	Messages *PageMessages `json:"messages"`
	Gdpr     *GDPRConfig   `json:"gdpr,omitempty"`
	Logo     *StoreLogo    `json:"logo,omitempty"`
}

type StoreAvailabilityItem struct {
	Available AvailabilityStatus `json:"available"`
	Sample    bool               `json:"sample"`
	Store     *PraktisStore      `json:"store"`
}

type StoreCart struct {
	ID            int64            `json:"id"`
	Token         string           `json:"token"`
	StoreCode     string           `json:"storeCode"`
	Currency      string           `json:"currency"`
	CouponCode    string           `json:"couponCode"`
	Note          string           `json:"note"`
	Customer      *OrderCustomer   `json:"customer"`
	Items         []*StoreCartItem `json:"items"`
	Shipping      *CartShipping    `json:"shipping"`
	PaymentMethod string           `json:"paymentMethod"`
	Totals        []*CartTotal     `json:"totals"`
}

type StoreCartItem struct {
	ID                      int64                `json:"id"`
	Sku                     string               `json:"sku"`
	Labels                  *StoreCartItemLabels `json:"labels"`
	Product                 Product              `json:"product"`
	BaseQty                 float64              `json:"baseQty"`
	DiscountAmount          *Price               `json:"discountAmount"`
	DiscountPercent         float64              `json:"discountPercent"`
	Price                   *Price               `json:"price"`
	PriceWithoutDiscount    *Price               `json:"priceWithoutDiscount"`
	RowTotal                *Price               `json:"rowTotal"`
	RowTotalWithoutDiscount *Price               `json:"rowTotalWithoutDiscount"`
}

type StoreCartItemLabels struct {
	FreeShipping bool `json:"freeShipping"`
	UsePallet    bool `json:"usePallet"`
}

type StoreContacts struct {
	General     *ContactInfo `json:"general,omitempty"`
	OnlineStore *ContactInfo `json:"onlineStore,omitempty"`
}

type StoreImage struct {
	ID             int64   `json:"ID"`
	Position       int     `json:"position"`
	IsPrimary      bool    `json:"isPrimary"`
	PraktisStoreID int64   `json:"praktisStoreID"`
	Src            string  `json:"src"`
	MobileSrc      *string `json:"mobileSrc,omitempty"`
	Alt            *string `json:"alt,omitempty"`
	Title          *string `json:"title,omitempty"`
}

type StoreInfo struct {
	BaseURL  string         `json:"baseUrl"`
	Location *MapLocation   `json:"location"`
	Contacts *StoreContacts `json:"contacts"`
}

type StoreInquiryInput struct {
	Store   string      `json:"store"`
	Name    string      `json:"name"`
	Email   string      `json:"email"`
	Phone   string      `json:"phone"`
	Type    InquiryType `json:"type"`
	Message string      `json:"message"`
}

type StoreLogo struct {
	URL    string  `json:"url"`
	Width  *int    `json:"width,omitempty"`
	Height *int    `json:"height,omitempty"`
	Alt    *string `json:"alt,omitempty"`
}

type StoreOrder struct {
	ID              int64            `json:"id"`
	IncrementID     string           `json:"incrementId"`
	State           string           `json:"state"`
	Status          *OrderStatus     `json:"status"`
	CouponCode      string           `json:"couponCode"`
	ProtectCode     string           `json:"protectCode"`
	CreateAt        string           `json:"createAt"`
	Items           []*OrderItem     `json:"items"`
	Currency        string           `json:"currency"`
	Totals          []*CartTotal     `json:"totals"`
	ShippingMethod  *ShippingMethod  `json:"shippingMethod"`
	PaymentMethod   *PaymentMethod   `json:"paymentMethod"`
	Note            string           `json:"note"`
	ShippingAddress *OrderAddress    `json:"shippingAddress"`
	Invoice         *CustomerInvoice `json:"invoice,omitempty"`
}

type StorePageData struct {
	MessageBlock string        `json:"messageBlock"`
	Store        *PraktisStore `json:"store"`
}

type StoreSchedule struct {
	Day   string `json:"day"`
	Open  string `json:"open"`
	Close string `json:"close"`
}

type TbiConfiguration struct {
	TbiMinstojnost        string `json:"tbi_minstojnost"`
	TbiMinstojnostBnpl    string `json:"tbi_minstojnost_bnpl"`
	TbiMaxstojnost        string `json:"tbi_maxstojnost"`
	TbiMaxstojnostBnpl    string `json:"tbi_maxstojnost_bnpl"`
	TbiZaglavie           string `json:"tbi_zaglavie"`
	TbiCustomButtonStatus string `json:"tbi_custom_button_status"`
	TbiPurcentDefault     string `json:"tbi_purcent_default"`
	Reklama               string `json:"reklama"`
	Tbi5mPurcentDefault   string `json:"tbi_5m_purcent_default"`
	Tbi5m                 string `json:"tbi_5m"`
	Tbi5mCategories       string `json:"tbi_5m_categories"`
	Tbi5mManufacturers    string `json:"tbi_5m_manufacturers"`
	Tbi5mMin              string `json:"tbi_5m_min"`
	Tbi5mMax              string `json:"tbi_5m_max"`
	Tbi5mPv               string `json:"tbi_5m_pv"`
	Tbi4m                 string `json:"tbi_4m"`
	Tbi4mCategories       string `json:"tbi_4m_categories"`
	Tbi4mManufacturers    string `json:"tbi_4m_manufacturers"`
	Tbi4mMin              string `json:"tbi_4m_min"`
	Tbi4mMax              string `json:"tbi_4m_max"`
	Tbi4mPv               string `json:"tbi_4m_pv"`
	Tbi6m                 string `json:"tbi_6m"`
	Tbi6mCategories       string `json:"tbi_6m_categories"`
	Tbi6mManufacturers    string `json:"tbi_6m_manufacturers"`
	Tbi6mMin              string `json:"tbi_6m_min"`
	Tbi6mMax              string `json:"tbi_6m_max"`
	Tbi6mPv               string `json:"tbi_6m_pv"`
	Tbi9m                 string `json:"tbi_9m"`
	Tbi9mCategories       string `json:"tbi_9m_categories"`
	Tbi9mManufacturers    string `json:"tbi_9m_manufacturers"`
	Tbi9mMin              string `json:"tbi_9m_min"`
	Tbi9mMax              string `json:"tbi_9m_max"`
	Tbi9mPv               string `json:"tbi_9m_pv"`
	Tbi12m                string `json:"tbi_12m"`
	Tbi12mCategories      string `json:"tbi_12m_categories"`
	Tbi12mManufacturers   string `json:"tbi_12m_manufacturers"`
	Tbi12mMin             string `json:"tbi_12m_min"`
	Tbi12mMax             string `json:"tbi_12m_max"`
	Tbi12mPv              string `json:"tbi_12m_pv"`
	Tbi3mPurcent          string `json:"tbi_3m_purcent"`
	Tbi4mPurcent          string `json:"tbi_4m_purcent"`
	Tbi6mPurcent          string `json:"tbi_6m_purcent"`
	Tbi7mPurcent          string `json:"tbi_7m_purcent"`
	Tbi9mPurcent          string `json:"tbi_9m_purcent"`
	Tbi11mPurcent         string `json:"tbi_11m_purcent"`
	Tbi12mPurcent         string `json:"tbi_12m_purcent"`
	Tbi15mPurcent         string `json:"tbi_15m_purcent"`
	Tbi18mPurcent         string `json:"tbi_18m_purcent"`
	Tbi24mPurcent         string `json:"tbi_24m_purcent"`
	Tbi30mPurcent         string `json:"tbi_30m_purcent"`
	Tbi36mPurcent         string `json:"tbi_36m_purcent"`
	Tbi42mPurcent         string `json:"tbi_42m_purcent"`
	Tbi48mPurcent         string `json:"tbi_48m_purcent"`
	Tbi54mPurcent         string `json:"tbi_54m_purcent"`
	Tbi60mPurcent         string `json:"tbi_60m_purcent"`
	TbiOver5000           string `json:"tbi_over_5000"`
	TbiStatus             string `json:"tbi_status"`
	TbiBnpl               string `json:"tbi_bnpl"`
	TbiButtonStatus       string `json:"tbi_button_status"`
	TbiButtonTextVisible  string `json:"tbi_button_text_visible"`
	TbiButtonKvadrat      string `json:"tbi_button_kvadrat"`
	TbiIsDirect           string `json:"tbi_is_direct"`
	TbiIsCart             string `json:"tbi_is_cart"`
	TbiEur                string `json:"tbi_eur"`
}

type TileContent struct {
	Title string  `json:"title"`
	Text  *string `json:"text,omitempty"`
	Link  *Link   `json:"link"`
	Icon  *Image  `json:"icon,omitempty"`
}

type TilesRow struct {
	Cols []Tile `json:"cols"`
}

type TilesWidget struct {
	Identifier string      `json:"identifier"`
	Title      string      `json:"title"`
	Subtitle   string      `json:"subtitle"`
	ViewMore   *Link       `json:"viewMore,omitempty"`
	Rows       []*TilesRow `json:"rows"`
}

func (TilesWidget) IsWidget() {}

type ValidationCheck struct {
	Check Constraint `json:"check"`
	Value *string    `json:"value,omitempty"`
}

type Warehouse struct {
	WarehouseID   int64  `json:"WarehouseID"`
	WarehouseCode int    `json:"WarehouseCode"`
	Name          string `json:"Name"`
	Address       string `json:"Address"`
	PostalCode    string `json:"PostalCode"`
}

type WidgetCategory struct {
	Image *Image `json:"image,omitempty"`
	Title string `json:"title"`
	Link  *Link  `json:"link"`
}

type ActionResultCode string

const (
	ActionResultCodeSuccess ActionResultCode = "SUCCESS"
	ActionResultCodeError   ActionResultCode = "ERROR"
	ActionResultCodeQueued  ActionResultCode = "QUEUED"
	ActionResultCodeWaiting ActionResultCode = "WAITING"
)

var AllActionResultCode = []ActionResultCode{
	ActionResultCodeSuccess,
	ActionResultCodeError,
	ActionResultCodeQueued,
	ActionResultCodeWaiting,
}

func (e ActionResultCode) IsValid() bool {
	switch e {
	case ActionResultCodeSuccess, ActionResultCodeError, ActionResultCodeQueued, ActionResultCodeWaiting:
		return true
	}
	return false
}

func (e ActionResultCode) String() string {
	return string(e)
}

func (e *ActionResultCode) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ActionResultCode(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ActionResultCode", str)
	}
	return nil
}

func (e ActionResultCode) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type AvailabilityStatus string

const (
	AvailabilityStatusAvailable           AvailabilityStatus = "AVAILABLE"
	AvailabilityStatusUnavailable         AvailabilityStatus = "UNAVAILABLE"
	AvailabilityStatusLimitedAvailability AvailabilityStatus = "LIMITED_AVAILABILITY"
	AvailabilityStatusIndividualOrder     AvailabilityStatus = "INDIVIDUAL_ORDER"
)

var AllAvailabilityStatus = []AvailabilityStatus{
	AvailabilityStatusAvailable,
	AvailabilityStatusUnavailable,
	AvailabilityStatusLimitedAvailability,
	AvailabilityStatusIndividualOrder,
}

func (e AvailabilityStatus) IsValid() bool {
	switch e {
	case AvailabilityStatusAvailable, AvailabilityStatusUnavailable, AvailabilityStatusLimitedAvailability, AvailabilityStatusIndividualOrder:
		return true
	}
	return false
}

func (e AvailabilityStatus) String() string {
	return string(e)
}

func (e *AvailabilityStatus) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = AvailabilityStatus(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid AvailabilityStatus", str)
	}
	return nil
}

func (e AvailabilityStatus) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type CartTotalCode string

const (
	CartTotalCodeOther         CartTotalCode = "OTHER"
	CartTotalCodeDiscountTotal CartTotalCode = "DISCOUNT_TOTAL"
	CartTotalCodeShippingTotal CartTotalCode = "SHIPPING_TOTAL"
	CartTotalCodeSubTotal      CartTotalCode = "SUB_TOTAL"
	CartTotalCodeGrantTotal    CartTotalCode = "GRANT_TOTAL"
)

var AllCartTotalCode = []CartTotalCode{
	CartTotalCodeOther,
	CartTotalCodeDiscountTotal,
	CartTotalCodeShippingTotal,
	CartTotalCodeSubTotal,
	CartTotalCodeGrantTotal,
}

func (e CartTotalCode) IsValid() bool {
	switch e {
	case CartTotalCodeOther, CartTotalCodeDiscountTotal, CartTotalCodeShippingTotal, CartTotalCodeSubTotal, CartTotalCodeGrantTotal:
		return true
	}
	return false
}

func (e CartTotalCode) String() string {
	return string(e)
}

func (e *CartTotalCode) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = CartTotalCode(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid CartTotalCode", str)
	}
	return nil
}

func (e CartTotalCode) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type CatalogLayout string

const (
	CatalogLayoutLandingPage    CatalogLayout = "LANDING_PAGE"
	CatalogLayoutProductCatalog CatalogLayout = "PRODUCT_CATALOG"
)

var AllCatalogLayout = []CatalogLayout{
	CatalogLayoutLandingPage,
	CatalogLayoutProductCatalog,
}

func (e CatalogLayout) IsValid() bool {
	switch e {
	case CatalogLayoutLandingPage, CatalogLayoutProductCatalog:
		return true
	}
	return false
}

func (e CatalogLayout) String() string {
	return string(e)
}

func (e *CatalogLayout) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = CatalogLayout(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid CatalogLayout", str)
	}
	return nil
}

func (e CatalogLayout) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type Constraint string

const (
	ConstraintRequired Constraint = "REQUIRED"
	ConstraintEmail    Constraint = "EMAIL"
	ConstraintNumeric  Constraint = "NUMERIC"
	ConstraintOneOf    Constraint = "ONE_OF"
	ConstraintPassword Constraint = "PASSWORD"
	ConstraintMin      Constraint = "MIN"
	ConstraintMax      Constraint = "MAX"
)

var AllConstraint = []Constraint{
	ConstraintRequired,
	ConstraintEmail,
	ConstraintNumeric,
	ConstraintOneOf,
	ConstraintPassword,
	ConstraintMin,
	ConstraintMax,
}

func (e Constraint) IsValid() bool {
	switch e {
	case ConstraintRequired, ConstraintEmail, ConstraintNumeric, ConstraintOneOf, ConstraintPassword, ConstraintMin, ConstraintMax:
		return true
	}
	return false
}

func (e Constraint) String() string {
	return string(e)
}

func (e *Constraint) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = Constraint(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid Constraint", str)
	}
	return nil
}

func (e Constraint) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ConstraintType string

const (
	ConstraintTypeCascade  ConstraintType = "CASCADE"
	ConstraintTypeRestrict ConstraintType = "RESTRICT"
	ConstraintTypeSetNull  ConstraintType = "SET_NULL"
	ConstraintTypeNoAction ConstraintType = "NO_ACTION"
)

var AllConstraintType = []ConstraintType{
	ConstraintTypeCascade,
	ConstraintTypeRestrict,
	ConstraintTypeSetNull,
	ConstraintTypeNoAction,
}

func (e ConstraintType) IsValid() bool {
	switch e {
	case ConstraintTypeCascade, ConstraintTypeRestrict, ConstraintTypeSetNull, ConstraintTypeNoAction:
		return true
	}
	return false
}

func (e ConstraintType) String() string {
	return string(e)
}

func (e *ConstraintType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ConstraintType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ConstraintType", str)
	}
	return nil
}

func (e ConstraintType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type CookieVendorKey string

const (
	CookieVendorKeyGoogle   CookieVendorKey = "google"
	CookieVendorKeyFacebook CookieVendorKey = "facebook"
	CookieVendorKeyClarity  CookieVendorKey = "clarity"
	CookieVendorKeyStore    CookieVendorKey = "store"
)

var AllCookieVendorKey = []CookieVendorKey{
	CookieVendorKeyGoogle,
	CookieVendorKeyFacebook,
	CookieVendorKeyClarity,
	CookieVendorKeyStore,
}

func (e CookieVendorKey) IsValid() bool {
	switch e {
	case CookieVendorKeyGoogle, CookieVendorKeyFacebook, CookieVendorKeyClarity, CookieVendorKeyStore:
		return true
	}
	return false
}

func (e CookieVendorKey) String() string {
	return string(e)
}

func (e *CookieVendorKey) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = CookieVendorKey(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid CookieVendorKey", str)
	}
	return nil
}

func (e CookieVendorKey) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type DoubleImageTilePosition string

const (
	DoubleImageTilePositionLeft  DoubleImageTilePosition = "LEFT"
	DoubleImageTilePositionRight DoubleImageTilePosition = "RIGHT"
)

var AllDoubleImageTilePosition = []DoubleImageTilePosition{
	DoubleImageTilePositionLeft,
	DoubleImageTilePositionRight,
}

func (e DoubleImageTilePosition) IsValid() bool {
	switch e {
	case DoubleImageTilePositionLeft, DoubleImageTilePositionRight:
		return true
	}
	return false
}

func (e DoubleImageTilePosition) String() string {
	return string(e)
}

func (e *DoubleImageTilePosition) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = DoubleImageTilePosition(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid DoubleImageTilePosition", str)
	}
	return nil
}

func (e DoubleImageTilePosition) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type EntityFilterSort string

const (
	EntityFilterSortAsc  EntityFilterSort = "ASC"
	EntityFilterSortDesc EntityFilterSort = "DESC"
)

var AllEntityFilterSort = []EntityFilterSort{
	EntityFilterSortAsc,
	EntityFilterSortDesc,
}

func (e EntityFilterSort) IsValid() bool {
	switch e {
	case EntityFilterSortAsc, EntityFilterSortDesc:
		return true
	}
	return false
}

func (e EntityFilterSort) String() string {
	return string(e)
}

func (e *EntityFilterSort) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = EntityFilterSort(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid EntityFilterSort", str)
	}
	return nil
}

func (e EntityFilterSort) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type FilterRenderType string

const (
	FilterRenderTypeList   FilterRenderType = "LIST"
	FilterRenderTypeSlider FilterRenderType = "SLIDER"
)

var AllFilterRenderType = []FilterRenderType{
	FilterRenderTypeList,
	FilterRenderTypeSlider,
}

func (e FilterRenderType) IsValid() bool {
	switch e {
	case FilterRenderTypeList, FilterRenderTypeSlider:
		return true
	}
	return false
}

func (e FilterRenderType) String() string {
	return string(e)
}

func (e *FilterRenderType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = FilterRenderType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid FilterRenderType", str)
	}
	return nil
}

func (e FilterRenderType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type GDPRGrants string

const (
	GDPRGrantsAdStorage         GDPRGrants = "ad_storage"
	GDPRGrantsAdUserData        GDPRGrants = "ad_user_data"
	GDPRGrantsAdPersonalization GDPRGrants = "ad_personalization"
	GDPRGrantsAnalyticsStorage  GDPRGrants = "analytics_storage"
)

var AllGDPRGrants = []GDPRGrants{
	GDPRGrantsAdStorage,
	GDPRGrantsAdUserData,
	GDPRGrantsAdPersonalization,
	GDPRGrantsAnalyticsStorage,
}

func (e GDPRGrants) IsValid() bool {
	switch e {
	case GDPRGrantsAdStorage, GDPRGrantsAdUserData, GDPRGrantsAdPersonalization, GDPRGrantsAnalyticsStorage:
		return true
	}
	return false
}

func (e GDPRGrants) String() string {
	return string(e)
}

func (e *GDPRGrants) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = GDPRGrants(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid GDPRGrants", str)
	}
	return nil
}

func (e GDPRGrants) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type InquiryType string

const (
	InquiryTypePersonal InquiryType = "Personal"
	InquiryTypeCompany  InquiryType = "Company"
)

var AllInquiryType = []InquiryType{
	InquiryTypePersonal,
	InquiryTypeCompany,
}

func (e InquiryType) IsValid() bool {
	switch e {
	case InquiryTypePersonal, InquiryTypeCompany:
		return true
	}
	return false
}

func (e InquiryType) String() string {
	return string(e)
}

func (e *InquiryType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = InquiryType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid InquiryType", str)
	}
	return nil
}

func (e InquiryType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type InvoiceType string

const (
	InvoiceTypeCompany  InvoiceType = "company"
	InvoiceTypePersonal InvoiceType = "personal"
)

var AllInvoiceType = []InvoiceType{
	InvoiceTypeCompany,
	InvoiceTypePersonal,
}

func (e InvoiceType) IsValid() bool {
	switch e {
	case InvoiceTypeCompany, InvoiceTypePersonal:
		return true
	}
	return false
}

func (e InvoiceType) String() string {
	return string(e)
}

func (e *InvoiceType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = InvoiceType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid InvoiceType", str)
	}
	return nil
}

func (e InvoiceType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type LoginProvider string

const (
	LoginProviderFacebook LoginProvider = "Facebook"
	LoginProviderGoogle   LoginProvider = "Google"
)

var AllLoginProvider = []LoginProvider{
	LoginProviderFacebook,
	LoginProviderGoogle,
}

func (e LoginProvider) IsValid() bool {
	switch e {
	case LoginProviderFacebook, LoginProviderGoogle:
		return true
	}
	return false
}

func (e LoginProvider) String() string {
	return string(e)
}

func (e *LoginProvider) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = LoginProvider(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid LoginProvider", str)
	}
	return nil
}

func (e LoginProvider) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type NotificationType string

const (
	NotificationTypeSuccess NotificationType = "SUCCESS"
	NotificationTypeError   NotificationType = "ERROR"
	NotificationTypeWarning NotificationType = "WARNING"
	NotificationTypeInfo    NotificationType = "INFO"
)

var AllNotificationType = []NotificationType{
	NotificationTypeSuccess,
	NotificationTypeError,
	NotificationTypeWarning,
	NotificationTypeInfo,
}

func (e NotificationType) IsValid() bool {
	switch e {
	case NotificationTypeSuccess, NotificationTypeError, NotificationTypeWarning, NotificationTypeInfo:
		return true
	}
	return false
}

func (e NotificationType) String() string {
	return string(e)
}

func (e *NotificationType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = NotificationType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid NotificationType", str)
	}
	return nil
}

func (e NotificationType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type Positions string

const (
	PositionsLeftTop     Positions = "LEFT_TOP"
	PositionsLeftBottom  Positions = "LEFT_BOTTOM"
	PositionsRightTop    Positions = "RIGHT_TOP"
	PositionsRightBottom Positions = "RIGHT_BOTTOM"
)

var AllPositions = []Positions{
	PositionsLeftTop,
	PositionsLeftBottom,
	PositionsRightTop,
	PositionsRightBottom,
}

func (e Positions) IsValid() bool {
	switch e {
	case PositionsLeftTop, PositionsLeftBottom, PositionsRightTop, PositionsRightBottom:
		return true
	}
	return false
}

func (e Positions) String() string {
	return string(e)
}

func (e *Positions) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = Positions(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid Positions", str)
	}
	return nil
}

func (e Positions) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type ShippingMethodType string

const (
	ShippingMethodTypeNone           ShippingMethodType = "NONE"
	ShippingMethodTypeEcontToOffice  ShippingMethodType = "ECONT_TO_OFFICE"
	ShippingMethodTypeEcontToAddress ShippingMethodType = "ECONT_TO_ADDRESS"
	ShippingMethodTypeToStore        ShippingMethodType = "TO_STORE"
)

var AllShippingMethodType = []ShippingMethodType{
	ShippingMethodTypeNone,
	ShippingMethodTypeEcontToOffice,
	ShippingMethodTypeEcontToAddress,
	ShippingMethodTypeToStore,
}

func (e ShippingMethodType) IsValid() bool {
	switch e {
	case ShippingMethodTypeNone, ShippingMethodTypeEcontToOffice, ShippingMethodTypeEcontToAddress, ShippingMethodTypeToStore:
		return true
	}
	return false
}

func (e ShippingMethodType) String() string {
	return string(e)
}

func (e *ShippingMethodType) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = ShippingMethodType(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid ShippingMethodType", str)
	}
	return nil
}

func (e ShippingMethodType) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type SortDirection string

const (
	SortDirectionAsc  SortDirection = "ASC"
	SortDirectionDesc SortDirection = "DESC"
)

var AllSortDirection = []SortDirection{
	SortDirectionAsc,
	SortDirectionDesc,
}

func (e SortDirection) IsValid() bool {
	switch e {
	case SortDirectionAsc, SortDirectionDesc:
		return true
	}
	return false
}

func (e SortDirection) String() string {
	return string(e)
}

func (e *SortDirection) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = SortDirection(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid SortDirection", str)
	}
	return nil
}

func (e SortDirection) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}

type TileContentPosition string

const (
	TileContentPositionLeft        TileContentPosition = "LEFT"
	TileContentPositionLeftTop     TileContentPosition = "LEFT_TOP"
	TileContentPositionLeftBottom  TileContentPosition = "LEFT_BOTTOM"
	TileContentPositionRight       TileContentPosition = "RIGHT"
	TileContentPositionRightTop    TileContentPosition = "RIGHT_TOP"
	TileContentPositionRightBottom TileContentPosition = "RIGHT_BOTTOM"
)

var AllTileContentPosition = []TileContentPosition{
	TileContentPositionLeft,
	TileContentPositionLeftTop,
	TileContentPositionLeftBottom,
	TileContentPositionRight,
	TileContentPositionRightTop,
	TileContentPositionRightBottom,
}

func (e TileContentPosition) IsValid() bool {
	switch e {
	case TileContentPositionLeft, TileContentPositionLeftTop, TileContentPositionLeftBottom, TileContentPositionRight, TileContentPositionRightTop, TileContentPositionRightBottom:
		return true
	}
	return false
}

func (e TileContentPosition) String() string {
	return string(e)
}

func (e *TileContentPosition) UnmarshalGQL(v interface{}) error {
	str, ok := v.(string)
	if !ok {
		return fmt.Errorf("enums must be strings")
	}

	*e = TileContentPosition(str)
	if !e.IsValid() {
		return fmt.Errorf("%s is not a valid TileContentPosition", str)
	}
	return nil
}

func (e TileContentPosition) MarshalGQL(w io.Writer) {
	fmt.Fprint(w, strconv.Quote(e.String()))
}
