query GetTBICredit($sku: String!) {
    getCreditCalculatorTBIBank(sku: $sku) {
        ...TBICreditData
    }
}

query GetBNPCredit($sku: String!, $downPayment: Float!, $qty: Int!) {
    getCreditCalculatorBNPParibas(sku: $sku, downPayment: $downPayment, qty: $qty) {
        ...BNPVariantGroup
    }
}

fragment TBICreditData on TbiConfiguration {
    tbi_minstojnost
    tbi_minstojnost_bnpl
    tbi_maxstojnost
    tbi_maxstojnost_bnpl
    tbi_zaglavie
    tbi_custom_button_status
    tbi_purcent_default
    reklama
    tbi_5m_purcent_default
    tbi_5m
    tbi_5m_categories
    tbi_5m_manufacturers
    tbi_5m_min
    tbi_5m_max
    tbi_5m_pv
    tbi_4m
    tbi_4m_categories
    tbi_4m_manufacturers
    tbi_4m_min
    tbi_4m_max
    tbi_4m_pv
    tbi_6m
    tbi_6m_categories
    tbi_6m_manufacturers
    tbi_6m_min
    tbi_6m_max
    tbi_6m_pv
    tbi_9m
    tbi_9m_categories
    tbi_9m_manufacturers
    tbi_9m_min
    tbi_9m_max
    tbi_9m_pv
    tbi_12m
    tbi_12m_categories
    tbi_12m_manufacturers
    tbi_12m_min
    tbi_12m_max
    tbi_12m_pv
    tbi_3m_purcent
    tbi_4m_purcent
    tbi_6m_purcent
    tbi_7m_purcent
    tbi_9m_purcent
    tbi_11m_purcent
    tbi_12m_purcent
    tbi_15m_purcent
    tbi_18m_purcent
    tbi_24m_purcent
    tbi_30m_purcent
    tbi_36m_purcent
    tbi_42m_purcent
    tbi_48m_purcent
    tbi_54m_purcent
    tbi_60m_purcent
    tbi_over_5000
    tbi_status
    tbi_bnpl
    tbi_button_status
    tbi_button_text_visible
    tbi_button_kvadrat
    tbi_is_direct
    tbi_is_cart
    tbi_eur
}

fragment PricingVariant on PricingVariant {
    id
    apr
    correctDownpaymentAmount
    installmentAmount
    maturity
    nir
    pricingSchemeId
    pricingSchemeName
    totalRepaymentAmount
    processingFeeAmount
    # Legacy field names for Magento template compatibility
    installment
    total_repayment
}

fragment BNPVariantGroup on BNPVariantGroup {
    schemeId
    variants {
        ...PricingVariant
    }
}