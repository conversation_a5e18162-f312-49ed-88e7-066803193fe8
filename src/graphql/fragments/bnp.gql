# BNP Paribas related fragments

fragment BNPPricingScheme on BNPPricingScheme {
    id
    name
}

fragment PricingVariant on PricingVariant {
    id
    apr
    correctDownpaymentAmount
    installmentAmount
    maturity
    nir
    pricingSchemeId
    pricingSchemeName
    totalRepaymentAmount
    processingFeeAmount
    # Legacy field names for Magento template compatibility
    installment
    total_repayment
}

fragment BNPVariantGroup on BNPVariantGroup {
    schemeId
    variants {
        ...PricingVariant
    }
}

fragment LoanCalculation on LoanCalculation {
    apr
    correctDownpaymentAmount
    installmentAmount
    maturity
    nir
    pricingSchemeId
    pricingSchemeName
    pricingVariantId
    processingFeeAmount
    totalRepaymentAmount
}



# Note: Fragments cannot be defined on input types (BNPCustomerDataInput, BNPPaymentInput)
# These are used for mutations and should be constructed directly in the mutation variables
