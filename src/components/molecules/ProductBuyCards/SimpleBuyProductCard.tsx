'use client'

import React, { useCallback, useEffect, useMemo, useState } from 'react'

import { cn } from '@components/lib/utils'
import { AvailabilityCheckButton } from '@components/molecules/ProductBuyCards/components/AvailabilityCheckButton'
import { BuyButton } from '@components/molecules/ProductBuyCards/components/BuyButton'
import { LeasingCalculator } from '@components/molecules/ProductBuyCards/components/LeasingCalculator'
import { BNPLeasingCalculator } from '@components/molecules/ProductBuyCards/components/BNPLeasingCalculator'
import { PriceInfo } from '@components/molecules/ProductBuyCards/components/PriceInfo'
import { PriceTags } from '@components/molecules/ProductBuyCards/components/PriceTags'
import { ProductActions } from '@components/molecules/ProductBuyCards/components/ProductActions'
import { ProductBuyBadge } from '@components/molecules/ProductBuyCards/components/ProductBuyBadge'
import { ProductBuyDivider } from '@components/molecules/ProductBuyCards/components/ProductBuyDivider'
import { ProductInfoLink } from '@components/molecules/ProductBuyCards/components/ProductInfoLink'
import { SingleCounter } from '@components/molecules/ProductBuyCards/components/SingleCounter'
import { Card, CardContent } from '@components/theme/ui/card'
import { useCartStore } from '@features/cart/cart-state'
import { calcDiscountPercent } from '@features/product/filters'
import { SimpleProductViewFragment } from '@lib/_generated/graphql_sdk'
import { ProductStatus } from './components/ProductStatus'
import { siteStatusHelper, SiteStatusHelper } from '@lib/utils/SiteStatusHelper'
import { trackAddToCart } from '@lib/fbp/faceBookPixelHelper'

interface SimpleBuyProductCardProps {
  highlighted?: boolean
  product: SimpleProductViewFragment
  children: React.ReactNode
}

const statusLabels = [
  'NO_STATUS',
  'NOT_ACTIVE',
  'DEPLETED',
  'AVAILABLE',
  'AVAILABLE_PICKUP',
  'AWAITING_DELIVERY',
  'AWAITING_DELIVERY_PICKUP',
  'INDIVIDUAL_ORDER',
  'INDIVIDUAL_ORDER_PICKUP',
  'ONLY_IN_STORE',
  'LIMITED_VISIBILITY',
]

export const SimpleBuyProductCard: React.FC<SimpleBuyProductCardProps> = ({ highlighted, product, children }) => {
  const [loading, setLoading] = React.useState(false)
  const [count, setCount] = React.useState(1)

  const cartStore = useCartStore()

  const {
    sku,
    id: productId,
    price: {
      price: { currency: productCurrency },
    },
  } = product

  const totalAmount = useMemo(() => {
    return count * (product.price.special?.value || product.price.price.value)
  }, [count, product])

  const clickHandler = useCallback(() => {
    setLoading(true)
    cartStore.addItem(sku, count).finally(() => {
      trackAddToCart({
        contents: [{ id: productId, quantity: count }],
        content_type: 'product',
        value: totalAmount,
        currency: productCurrency,
      })
      setLoading(false)
    })
  }, [cartStore, count, productId, productCurrency, sku, totalAmount])

  const { price: regularPrice, special: promoPrice } = product.price

  const status = siteStatusHelper.getStatus(product)

  return (
    <Card className={cn('flex flex-col w-full flex-1', highlighted && 'bg-primary')}>
      <div className={cn('rounded-xl', !highlighted && 'bg-primary')} id="product-buy-card">
        <div className="flex flex-wrap gap-2 pt-2.5 pl-3">
          {promoPrice?.value && (
            <ProductBuyBadge
              highlightLabel={`-${calcDiscountPercent(regularPrice.value, promoPrice.value).toFixed(0)}%`}
              regularLabel="отстъпка"
            />
          )}
          {product.labels.buyCheap && <ProductBuyBadge highlightLabel="Купи изгодно" />}
          {product.labels.fromBrochure && (
            <ProductBuyBadge regularLabel="Продукт от брошура" url="/produkti-broshura" />
          )}
        </div>
        <CardContent className="py-6 px-5 sm:px-10 ">
          <PriceTags data={product.price} energyLabel={product?.energyLabel} />
          {product.energyLabel?.infoUrl ? (
            <>
              <ProductBuyDivider />
              {product.energyLabel?.infoUrl && (
                <div className="flex flex-col py-2.5 gap-2">
                  <ProductInfoLink title="Продуктов информационен лист" url={product.energyLabel.infoUrl} />
                </div>
              )}
            </>
          ) : null}
          {(status === SiteStatusHelper.AVAILABLE ||
            status === SiteStatusHelper.AVAILABLE_PICKUP ||
            status === SiteStatusHelper.INDIVIDUAL_ORDER ||
            status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP) && (
            <>
              <ProductBuyDivider />
              <div className="flex flex-wrap sm:grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-1 gap-y-3 xl:grid-cols-2 3xl:grid-cols-6 justify-between pt-5 pb-0">
                <div className="sm:col-span-1 lg:col-span-2 xl:col-span-1 3xl:col-span-3 4xl:col-span-2 flex justify-start">
                  <SingleCounter label={product.measures.base} onChangeAction={setCount} />
                </div>
                <div className="sm:col-span-2 lg:col-span-2 xl:col-span-1 3xl:col-span-3 4xl:col-span-4 flex justify-end">
                  {(status === SiteStatusHelper.AVAILABLE || status === SiteStatusHelper.INDIVIDUAL_ORDER) && (
                    <BuyButton loading={loading} onClick={clickHandler} />
                  )}
                  {(status === SiteStatusHelper.AVAILABLE_PICKUP ||
                    status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP) && (
                    <BuyButton
                      loading={loading}
                      onClick={clickHandler}
                      buttonText={!loading ? 'Купи онлайн и вземи от магазин' : 'Купи онлайн и вземи от...'}
                      className="whitespace-normal tracking-tight py-6 px-2"
                    />
                  )}
                </div>
              </div>
            </>
          )}

          <ProductStatus
            product={product}
            type="text"
            className="col-span-2 text-center text-white py-4"
            allowedStatuses={[
              SiteStatusHelper.NO_STATUS,
              SiteStatusHelper.NOT_ACTIVE,
              SiteStatusHelper.DEPLETED,
              // SiteStatusHelper.AVAILABLE,

              // SiteStatusHelper.AVAILABLE_PICKUP,
              // SiteStatusHelper.AWAITING_DELIVERY,
              // SiteStatusHelper.AWAITING_DELIVERY_PICKUP,

              // SiteStatusHelper.INDIVIDUAL_ORDER,

              // SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP,
              SiteStatusHelper.ONLY_IN_STORE,
              SiteStatusHelper.LIMITED_VISIBILITY,
            ]}
          />

          {(status === SiteStatusHelper.AVAILABLE ||
            status === SiteStatusHelper.AVAILABLE_PICKUP ||
            status === SiteStatusHelper.INDIVIDUAL_ORDER ||
            status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP) && (
            <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-1 gap-y-3 xl:grid-cols-2 justify-between pt-5 pb-0">
              <div className="xl:col-start-2">
                <PriceInfo
                  price={{
                    price: {
                      value: totalAmount,
                      currency: productCurrency,
                    },
                  }}
                />
              </div>
            </div>
          )}
        </CardContent>
      </div>
      <div className="flex flex-col flex-1 py-4 px-3 sm:px-6">
        <div className="flex flex-col w-full gap-4 flex-1 justify-start">
          {(status === SiteStatusHelper.AVAILABLE ||
            status === SiteStatusHelper.AVAILABLE_PICKUP ||
            status === SiteStatusHelper.INDIVIDUAL_ORDER ||
            status === SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP) && (
            <>
              {totalAmount > 100 && (
                <LeasingCalculator quantity={count} product={product} type={highlighted ? 'secondary' : 'primary'} />
              )}
              {status !== SiteStatusHelper.INDIVIDUAL_ORDER && status !== SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP && (
                <AvailabilityCheckButton type={highlighted ? 'secondary' : 'primary'} product={product} />
              )}
            </>
          )}
          <ProductStatus
            product={product}
            allowedStatuses={[
              SiteStatusHelper.NO_STATUS,
              SiteStatusHelper.NOT_ACTIVE,
              // SiteStatusHelper.DEPLETED,
              // SiteStatusHelper.AVAILABLE,

              SiteStatusHelper.AVAILABLE_PICKUP,
              SiteStatusHelper.AWAITING_DELIVERY,
              SiteStatusHelper.AWAITING_DELIVERY_PICKUP,

              SiteStatusHelper.INDIVIDUAL_ORDER,

              SiteStatusHelper.INDIVIDUAL_ORDER_PICKUP,
              // SiteStatusHelper.ONLY_IN_STORE,
              SiteStatusHelper.LIMITED_VISIBILITY,
            ]}
          />
        </div>
        {children}
      </div>
    </Card>
  )
}
