import React, { useState, useMemo } from 'react'
import { SimpleProductViewFragment, BnpVariantGroupFragment, PricingVariantFragment } from '@lib/_generated/graphql_sdk'
import { Button } from '@components/theme/ui/button'
import { formatPrice } from '@features/product/filters'

interface BNPPaymentDisplayProps {
  product: SimpleProductViewFragment
  quantity: number
  variantGroups: BnpVariantGroupFragment[]
  totalPrice: number
}

export const BNPPaymentDisplay: React.FC<BNPPaymentDisplayProps> = ({
  product,
  quantity,
  variantGroups,
  totalPrice
}) => {
  const [activeTab, setActiveTab] = useState(0)
  const [selectedVariant, setSelectedVariant] = useState<PricingVariantFragment | null>(null)

  // Combine all variants from all groups and sort them
  const allVariants = useMemo(() => {
    const variants = variantGroups.flatMap(group => group.variants)
    
    // Sort variants - promotional (0% APR) first, then by maturity
    return variants.sort((a, b) => {
      const aIsPromo = parseFloat(a.apr) <= 0.1
      const bIsPromo = parseFloat(b.apr) <= 0.1
      
      if (aIsPromo && !bIsPromo) return -1
      if (!aIsPromo && bIsPromo) return 1
      
      return parseInt(a.maturity) - parseInt(b.maturity)
    })
  }, [variantGroups])

  // Get scheme names for tabs
  const schemeNames = useMemo(() => {
    return variantGroups.map(group => {
      const firstVariant = group.variants[0]
      return firstVariant?.pricingSchemeName || `Схема ${group.schemeId}`
    })
  }, [variantGroups])

  const handleVariantSelect = (variant: PricingVariantFragment) => {
    setSelectedVariant(variant)
  }

  const handleProceedToCheckout = () => {
    if (selectedVariant) {
      // Here you would typically redirect to checkout or open a more detailed form
      console.log('Proceeding to checkout with variant:', selectedVariant)
      // For now, we'll just show an alert
      alert(`Избрахте ${selectedVariant.maturity} месеца с месечна вноска ${formatPrice(parseFloat(selectedVariant.installmentAmount))}`)
    }
  }

  const renderVariantTable = (variants: PricingVariantFragment[]) => {
    return (
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-200 px-4 py-2 text-left">Период</th>
              <th className="border border-gray-200 px-4 py-2 text-right">Месечна вноска</th>
              <th className="border border-gray-200 px-4 py-2 text-right">ГПР</th>
              <th className="border border-gray-200 px-4 py-2 text-right">ГЛП</th>
              <th className="border border-gray-200 px-4 py-2 text-right">Обща сума</th>
              <th className="border border-gray-200 px-4 py-2 text-center">Избор</th>
            </tr>
          </thead>
          <tbody>
            {variants.map((variant) => {
              const isPromo = parseFloat(variant.apr) <= 0.1
              const isSelected = selectedVariant?.id === variant.id
              
              return (
                <tr 
                  key={variant.id}
                  className={`hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''} ${isPromo ? 'bg-orange-50' : ''}`}
                >
                  <td className="border border-gray-200 px-4 py-2">
                    <div className="flex items-center gap-2">
                      <span>{variant.maturity} месеца</span>
                      {isPromo && (
                        <span className="bg-orange-500 text-white text-xs px-2 py-1 rounded">
                          ПРОМО
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="border border-gray-200 px-4 py-2 text-right font-semibold">
                    {formatPrice(parseFloat(variant.installmentAmount))}
                  </td>
                  <td className="border border-gray-200 px-4 py-2 text-right">
                    {parseFloat(variant.apr).toFixed(2)}%
                  </td>
                  <td className="border border-gray-200 px-4 py-2 text-right">
                    {parseFloat(variant.nir).toFixed(2)}%
                  </td>
                  <td className="border border-gray-200 px-4 py-2 text-right">
                    {formatPrice(parseFloat(variant.totalRepaymentAmount))}
                  </td>
                  <td className="border border-gray-200 px-4 py-2 text-center">
                    <Button
                      size="sm"
                      variant={isSelected ? "default" : "outline"}
                      onClick={() => handleVariantSelect(variant)}
                    >
                      {isSelected ? 'Избрано' : 'Избери'}
                    </Button>
                  </td>
                </tr>
              )
            })}
          </tbody>
        </table>
      </div>
    )
  }

  if (variantGroups.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Няма налични схеми за лизинг за този продукт.</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Product Info */}
      <div className="bg-gray-50 p-4 rounded-lg">
        <h3 className="font-semibold mb-2">{product.name}</h3>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Количество:</span>
            <span className="ml-2 font-semibold">{quantity}</span>
          </div>
          <div>
            <span className="text-gray-600">Обща стойност:</span>
            <span className="ml-2 font-semibold">{formatPrice(totalPrice)}</span>
          </div>
        </div>
      </div>

      {/* Tabs for different schemes */}
      {variantGroups.length > 1 && (
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {variantGroups.map((group, index) => (
              <button
                key={group.schemeId}
                onClick={() => setActiveTab(index)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === index
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {schemeNames[index]}
              </button>
            ))}
          </nav>
        </div>
      )}

      {/* Variant Table */}
      {variantGroups.length === 1 ? (
        // Show all variants if only one scheme
        renderVariantTable(allVariants)
      ) : (
        // Show variants from active tab
        variantGroups[activeTab] && renderVariantTable(variantGroups[activeTab].variants)
      )}

      {/* Selected Variant Summary */}
      {selectedVariant && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="font-semibold text-blue-900 mb-2">Избрана схема:</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-blue-700">Период:</span>
              <div className="font-semibold">{selectedVariant.maturity} месеца</div>
            </div>
            <div>
              <span className="text-blue-700">Месечна вноска:</span>
              <div className="font-semibold">{formatPrice(parseFloat(selectedVariant.installmentAmount))}</div>
            </div>
            <div>
              <span className="text-blue-700">ГПР:</span>
              <div className="font-semibold">{parseFloat(selectedVariant.apr).toFixed(2)}%</div>
            </div>
            <div>
              <span className="text-blue-700">Обща сума:</span>
              <div className="font-semibold">{formatPrice(parseFloat(selectedVariant.totalRepaymentAmount))}</div>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3">
        <Button variant="outline">
          Затвори
        </Button>
        <Button 
          onClick={handleProceedToCheckout}
          disabled={!selectedVariant}
        >
          Продължи към поръчка
        </Button>
      </div>
    </div>
  )
}
