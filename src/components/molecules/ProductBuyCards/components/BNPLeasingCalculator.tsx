import React, { useCallback, useState } from 'react'

import Text from '@atoms/Text'
import { cn } from '@components/lib/utils'
import { Button } from '@components/theme/ui/button'
import { GraphQLBackend } from '@lib/api/graphql'
import { SimpleProductViewFragment, BnpVariantGroupFragment } from '@lib/_generated/graphql_sdk'
import * as Dialog from '@/src/components/theme/ui/dialog'
import { LucideCircleX, LucideLoaderCircle } from 'lucide-react'
import { Callout } from '@components/molecules/Callout'
import { BNPPaymentDisplay } from './BNPPaymentDisplay'

interface BNPLeasingCalculatorProps {
  type: 'primary' | 'secondary'
  product: SimpleProductViewFragment
  quantity?: number
}

export const BNPLeasingCalculator: React.FC<BNPLeasingCalculatorProps> = ({ 
  type, 
  product, 
  quantity = 1 
}) => {
  const [variantGroups, setVariantGroups] = useState<BnpVariantGroupFragment[]>()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(false)

  const { sku } = product
  const productPrice = product.price.special?.value || product.price.price.value
  const totalPrice = productPrice * quantity

  const onClick = useCallback(async () => {
    if (!variantGroups) {
      setError(false)
      setLoading(true)

      try {
        // Calculate total price for the selected quantity
        const calculatedTotalPrice = productPrice * quantity

        const response = await GraphQLBackend.GetCreditCalculatorBNPParibas({
          sku: sku,
          downPayment: 0, // Default to 0 down payment for initial calculation
          qty: quantity // This ensures the API gets the correct quantity
        })

        if (response.getCreditCalculatorBNPParibas) {
          setVariantGroups(response.getCreditCalculatorBNPParibas)
        } else {
          setError(true)
        }
      } catch (err) {
        console.error('BNP Calculator error:', err)
        setError(true)
        setVariantGroups(undefined)
      } finally {
        setLoading(false)
      }
    }
  }, [variantGroups, sku, quantity, productPrice])

  return (
    <Dialog.Dialog>
      <Dialog.DialogTrigger asChild>
        <div className="flex gap-3 items-center">
          {/* BNP Paribas Logo/Brand */}
          <div className="inline-flex items-center justify-center w-12 h-8 bg-blue-600 text-white rounded text-xs font-bold">
            BNP
          </div>
          <Button
            onClick={onClick}
            variant="outline"
            className={cn(
              'bg-transparent text-xs w-full py-2 tracking-tighter lg:tracking-widest',
              type === 'primary'
                ? 'text-black border-black hover:bg-black hover:text-white hover:border-white'
                : 'text-white hover:bg-white hover:text-primary hover:border-primary'
            )}
          >
            <Text>BNP Paribas лизинг</Text>
          </Button>
        </div>
      </Dialog.DialogTrigger>
      <Dialog.DialogContent className="rounded-lg max-w-[95vw] sm:max-w-4xl max-h-[90vh] bg-white overflow-hidden">
        <Dialog.DialogHeader className="pb-2">
          <Dialog.DialogTitle className="font-bold text-lg">BNP Paribas лизинг</Dialog.DialogTitle>
        </Dialog.DialogHeader>

        <div className="overflow-hidden">
          {variantGroups && (
            <BNPPaymentDisplay
              product={product}
              quantity={quantity}
              variantGroups={variantGroups}
              totalPrice={totalPrice}
            />
          )}

          {loading && (
            <Callout
              variant="warning"
              title="Моля, изчакайте..."
              icon={<LucideLoaderCircle className="animate-spin" />}
              className="mb-0"
            >
              <Text>Зареждане на информация за лизинг...</Text>
            </Callout>
          )}

          {error && (
            <Callout
              variant="error"
              title="Грешка при зареждане на информация"
              icon={<LucideCircleX />}
              className="mb-0"
            >
              <Text>В момента нямаме информация за лизинг на този продукт. Моля, опитайте по-късно.</Text>
            </Callout>
          )}
        </div>
      </Dialog.DialogContent>
    </Dialog.Dialog>
  )
}
