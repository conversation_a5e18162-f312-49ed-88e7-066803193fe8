package api

import (
	"bytes"
	"crypto/tls"
	"encoding/json"
	"errors"
	"fmt"
	core_utils "github.com/siper92/core-utils"
	"io"
	"net/http"
	"net/http/cookiejar"
	netUrl "net/url"
	"strings"
	"time"
)

type ExternalApi struct {
	BaseUrl string
	Headers map[string]string
}

var ExternalApiErrorNotFound = errors.New(http.StatusText(http.StatusNotFound))
var ExternalApiErrorBadRequest = errors.New(http.StatusText(http.StatusBadRequest))

func (e ExternalApi) Get(query map[string]string) ([]byte, error) {
	var res []byte

	url := e.BaseUrl
	if len(query) > 0 {
		url += "?"
		for key, value := range query {
			url += netUrl.QueryEscape(key) + "=" + netUrl.QueryEscape(value) + "&"
		}
	}

	url = strings.Trim(url, "&")

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return res, err
	}

	for key, value := range e.Headers {
		req.Header.Set(key, value)
	}

	return e.Do(req)
}

func (e ExternalApi) Post(
	postPath string,
	data interface{},
) ([]byte, error) {
	var res []byte

	b, err := json.Marshal(data)
	if err != nil {
		return res, errors.New("невалидни данни за " + string(e.BaseUrl))
	}

	url := e.BaseUrl
	if len(postPath) > 0 {
		url = strings.TrimSuffix(url, "/")
		postPath = strings.TrimPrefix(postPath, "/")
		url = url + "/" + postPath
	}

	req, err := http.NewRequest(http.MethodPost, url, bytes.NewReader(b))
	if err != nil {
		return res, err
	}

	for key, value := range e.Headers {
		req.Header.Set(key, value)
	}
	AddXDebugCookieToRequest(req)

	return e.Do(req)
}

func (e ExternalApi) Do(req *http.Request) ([]byte, error) {
	var res []byte
	client, err := GetHttpClient()
	if err != nil {
		return res, err
	}

	start := time.Now()
	backendResponse, err := client.Do(req)
	core_utils.LogTimeElapsed(start, "CallAPI->"+req.URL.String())
	if err != nil {
		return res, err
	}

	res, err = io.ReadAll(backendResponse.Body)
	if err != nil {
		return res, err
	}

	if backendResponse.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(backendResponse.Body)
		if backendResponse.StatusCode == http.StatusNotFound {
			return res, ExternalApiErrorNotFound
		} else if backendResponse.StatusCode == http.StatusBadRequest {
			msg := GetErrorMessageFromRawResponse(res)
			if msg == "" || strings.Contains(strings.ToLower(msg), "unknown err") {
				return res, ExternalApiErrorBadRequest
			}

			return res, errors.New(msg)
		} else if backendResponse.StatusCode >= http.StatusInternalServerError {
			core_utils.Notice(string(body))

			return res, errors.New("internal server error")
		}

		return res, fmt.Errorf("status: %d: Error: %s",
			backendResponse.StatusCode,
			body,
		)
	}

	return res, err
}

func GetHttpClient() (http.Client, error) {
	jar, err := cookiejar.New(nil)
	var client http.Client

	if err != nil {
		return client, errors.New("приблем с междинни бизквитки")
	}

	if core_utils.IsDebugMode() {
		client = http.Client{
			Jar:     jar,
			Timeout: 2 * time.Minute,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		}
	} else {
		client = http.Client{
			Jar:     jar,
			Timeout: 2 * time.Minute,
		}
	}

	return client, nil
}

func AddXDebugCookieToRequest(r *http.Request) {
	if core_utils.IsDebugMode() || true {
		cookie := &http.Cookie{
			Name:   "XDEBUG_SESSION",
			Value:  "PHPSTORM",
			MaxAge: 60,
		}
		r.AddCookie(cookie)
		core_utils.Notice("XDEBUG_SESSION=PHPSTORM")
	}
}

func GetErrorMessageFromRawResponse(body []byte) string {
	var val map[string]interface{}
	_ = json.Unmarshal(body, &val)
	if _, ok := val["error"]; ok {
		return val["error"].(string)
	}

	return "Unknown error: " + string(body)
}
