2025/06/23 21:43:51 [INFO] BNP: Method: CalculateLoan
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/CalculateLoan/433147/353/367.00/0.00/481442
2025/06/23 21:43:51 [INFO] BNP: HTTP Method: GET
2025/06/23 21:43:51 [DEBUG] BNP: Request Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:43:51 [DEBUG] BNP: Request Parameters:
2025/06/23 21:43:51 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:43:51 [DEBUG] BNP:   API Method: CalculateLoan
2025/06/23 21:43:51 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:43:51 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:43:51 [DEBUG] BNP:   Principal: 367.00
2025/06/23 21:43:51 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:43:51 [DEBUG] BNP:   Additional Param[6]: 481442
2025/06/23 21:43:51 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/CalculateLoan/433147/353/367.00/0.00/481442 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:43:51 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:43:51 [INFO] BNP: Method: CalculateLoan
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: Duration: 198.629563ms
2025/06/23 21:43:51 [INFO] BNP: Status Code: 200
2025/06/23 21:43:51 [INFO] BNP: Status: 200 OK
2025/06/23 21:43:51 [DEBUG] BNP: Response Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Length: 583
2025/06/23 21:43:51 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:43:51 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:43:51 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:43:51 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:43:51 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:43:50 GMT
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Length: 583 bytes
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><CreditProposition><APR>27.17</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>65.57</InstallmentAmount><Maturity>6</Maturity><NIR>24.28</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481442</PricingVariantId><ProcessingFeeAmount>0</ProcessingFeeAmount><TotalRepaymentAmount>393.42</TotalRepaymentAmount></CreditProposition></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:43:51 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Response Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: errorCode: 0
2025/06/23 21:43:51 [DEBUG] BNP: errorMessage:
2025/06/23 21:43:51 [DEBUG] BNP: hasLoanData: true
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: processingFeeAmount: 0
2025/06/23 21:43:51 [DEBUG] BNP: success: true
2025/06/23 21:43:51 [DEBUG] BNP: apr: 27.17
2025/06/23 21:43:51 [DEBUG] BNP: installmentAmount: 65.57
2025/06/23 21:43:51 [DEBUG] BNP: maturity: 6
2025/06/23 21:43:51 [DEBUG] BNP: totalRepaymentAmount: 393.42
2025/06/23 21:43:51 [INFO] BNP: CalculateLoan completed successfully in 198.629563ms for variant 481442
2025/06/23 21:43:51 [INFO] BNP: Loan Details - APR: 27.17, Installment: 65.57, Maturity: 6 months, Total: 393.42
2025/06/23 21:43:51 [INFO] BNP: Loan calculation successful - APR: 27.17, Installment: 65.57, Total: 393.42
2025/06/23 21:43:51 [DEBUG] BNP: Preparing payment additional information...
2025/06/23 21:43:51 [DEBUG] BNP: Customer data - Name: Stoyan Atanasov, Email: <EMAIL>, Phone: 0883555204
2025/06/23 21:43:51 [INFO] BNP: Determining array index for variant 481442 by calling Magento API
2025/06/23 21:43:51 [INFO] BNP: Calling BNP variants API with downPayment=0.00, goodTypeIds=353, principal=367.00
2025/06/23 21:43:51 [INFO] BNP: Starting GetAvailableVariantsGroupedByPricingScheme with goodTypeIds='353', principal=367.00, downPayment=0.00
2025/06/23 21:43:51 [DEBUG] BNP: Starting BNP Calculator API initialization
2025/06/23 21:43:51 [DEBUG] BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
2025/06/23 21:43:51 [DEBUG] BNP: Using merchant ID: 433147
2025/06/23 21:43:51 [INFO] BNP: Using merchant ID: 433147
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:51 [DEBUG] BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
2025/06/23 21:43:51 [DEBUG] BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
2025/06/23 21:43:51 [INFO] BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:51 [INFO] BNP: Operating in mode: PRODUCTION
2025/06/23 21:43:51 [DEBUG] BNP: Starting TLS configuration setup
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:51 [DEBUG] BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
2025/06/23 21:43:51 [DEBUG] BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
2025/06/23 21:43:51 [INFO] BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:43:51 [DEBUG] BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:51 [DEBUG] BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
2025/06/23 21:43:51 [DEBUG] BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
2025/06/23 21:43:51 [INFO] BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:43:51 [DEBUG] BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:51 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:51 [DEBUG] BNP: No key password configured (as expected)
2025/06/23 21:43:51 [DEBUG] BNP: Loading X509 certificate/key pair
2025/06/23 21:43:51 [INFO] BNP: Successfully loaded X509 certificate/key pair
2025/06/23 21:43:51 [INFO] BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
2025/06/23 21:43:51 [DEBUG] BNP: Certificate count in TLS config: 1
2025/06/23 21:43:51 [INFO] BNP: TLS configuration loaded successfully
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: method: GetAvailablePricingSchemes
2025/06/23 21:43:51 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:43:51 [DEBUG] BNP: principal: 367
2025/06/23 21:43:51 [DEBUG] BNP: downPayment: 0
2025/06/23 21:43:51 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:43:51 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingSchemes
2025/06/23 21:43:51 [DEBUG] BNP: URL Parameters: [433147 353 367.00 0.00]
2025/06/23 21:43:51 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/367.00/0.00
2025/06/23 21:43:51 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:43:51 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/367.00/0.00
2025/06/23 21:43:51 [INFO] BNP: HTTP Method: GET
2025/06/23 21:43:51 [DEBUG] BNP: Request Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:43:51 [DEBUG] BNP: Request Parameters:
2025/06/23 21:43:51 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:43:51 [DEBUG] BNP:   API Method: GetAvailablePricingSchemes
2025/06/23 21:43:51 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:43:51 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:43:51 [DEBUG] BNP:   Principal: 367.00
2025/06/23 21:43:51 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:43:51 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingSchemes/433147/353/367.00/0.00 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:43:51 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:43:51 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: Duration: 196.273263ms
2025/06/23 21:43:51 [INFO] BNP: Status Code: 200
2025/06/23 21:43:51 [INFO] BNP: Status: 200 OK
2025/06/23 21:43:51 [DEBUG] BNP: Response Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:43:50 GMT
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Length: 675
2025/06/23 21:43:51 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:43:51 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:43:51 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:43:51 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Length: 675 bytes
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingScheme><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName></PricingScheme></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:43:51 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Response Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: errorCode: 0
2025/06/23 21:43:51 [DEBUG] BNP: errorMessage:
2025/06/23 21:43:51 [DEBUG] BNP: schemeCount: 3
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: inputSchemes: 3
2025/06/23 21:43:51 [DEBUG] BNP: outputSchemes: 3
2025/06/23 21:43:51 [DEBUG] BNP: success: true
2025/06/23 21:43:51 [INFO] BNP: GetAvailablePricingSchemes completed successfully in 196.273263ms, returned 3 schemes
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: instalment: 0
2025/06/23 21:43:51 [DEBUG] BNP: schemeId: 1695
2025/06/23 21:43:51 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:43:51 [DEBUG] BNP: principal: 367
2025/06/23 21:43:51 [DEBUG] BNP: downPayment: 0
2025/06/23 21:43:51 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:43:51 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP: URL Parameters: [433147 353 367.00 0.00 0.00 1695]
2025/06/23 21:43:51 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/1695
2025/06/23 21:43:51 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:43:51 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/1695
2025/06/23 21:43:51 [INFO] BNP: HTTP Method: GET
2025/06/23 21:43:51 [DEBUG] BNP: Request Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:43:51 [DEBUG] BNP: Request Parameters:
2025/06/23 21:43:51 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:43:51 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:43:51 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:43:51 [DEBUG] BNP:   Principal: 367.00
2025/06/23 21:43:51 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:43:51 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/23 21:43:51 [DEBUG] BNP:   Additional Param[7]: 1695
2025/06/23 21:43:51 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/1695 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:43:51 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:43:51 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: Duration: 49.970917ms
2025/06/23 21:43:51 [INFO] BNP: Status Code: 200
2025/06/23 21:43:51 [INFO] BNP: Status: 200 OK
2025/06/23 21:43:51 [DEBUG] BNP: Response Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:43:51 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:43:51 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:43:51 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:43:51 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:43:50 GMT
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Length: 2522
2025/06/23 21:43:51 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Length: 2522 bytes
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>28.99</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>28.87</InstallmentAmount><Maturity>15</Maturity><NIR>25.73</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481446</PricingVariantId><TotalRepaymentAmount>433.06</TotalRepaymentAmount></PricingVariant><PricingVariant><APR>28.87</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>32.63</InstallmentAmount><Maturity>13</Maturity><NIR>25.66</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481445</PricingVariantId><TotalRepaymentAmount>424.25</TotalRepaymentAmount></PricingVariant><PricingVariant><APR>28.84</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>34.99</InstallmentAmount><Maturity>12</Maturity><NIR>25.60</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481444</PricingVariantId><TotalRepaymentAmount>419.85</TotalRepaymentAmount></PricingVariant><PricingVariant><APR>28.34</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>45.18</InstallmentAmount><Maturity>9</Maturity><NIR>25.22</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481443</PricingVariantId><TotalRepaymentAmount>406.64</TotalRepaymentAmount></PricingVariant><PricingVariant><APR>27.17</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>65.57</InstallmentAmount><Maturity>6</Maturity><NIR>24.28</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481442</PricingVariantId><TotalRepaymentAmount>393.42</TotalRepaymentAmount></PricingVariant><PricingVariant><APR>23.73</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>126.74</InstallmentAmount><Maturity>3</Maturity><NIR>21.47</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481441</PricingVariantId><TotalRepaymentAmount>380.21</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:43:51 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Response Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: errorCode: 0
2025/06/23 21:43:51 [DEBUG] BNP: errorMessage:
2025/06/23 21:43:51 [DEBUG] BNP: variantCount: 6
2025/06/23 21:43:51 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 481446, defaulting to '0'
2025/06/23 21:43:51 [DEBUG] BNP: Parsed variant ID=481446, APR=28.99, Maturity=15, InstallmentAmount=28.87
2025/06/23 21:43:51 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 481445, defaulting to '0'
2025/06/23 21:43:51 [DEBUG] BNP: Parsed variant ID=481445, APR=28.87, Maturity=13, InstallmentAmount=32.63
2025/06/23 21:43:51 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 481444, defaulting to '0'
2025/06/23 21:43:51 [DEBUG] BNP: Parsed variant ID=481444, APR=28.84, Maturity=12, InstallmentAmount=34.99
2025/06/23 21:43:51 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 481443, defaulting to '0'
2025/06/23 21:43:51 [DEBUG] BNP: Parsed variant ID=481443, APR=28.34, Maturity=9, InstallmentAmount=45.18
2025/06/23 21:43:51 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 481442, defaulting to '0'
2025/06/23 21:43:51 [DEBUG] BNP: Parsed variant ID=481442, APR=27.17, Maturity=6, InstallmentAmount=65.57
2025/06/23 21:43:51 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 481441, defaulting to '0'
2025/06/23 21:43:51 [DEBUG] BNP: Parsed variant ID=481441, APR=23.73, Maturity=3, InstallmentAmount=126.74
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: inputVariants: 6
2025/06/23 21:43:51 [DEBUG] BNP: outputVariants: 6
2025/06/23 21:43:51 [DEBUG] BNP: schemeId: 1695
2025/06/23 21:43:51 [DEBUG] BNP: success: true
2025/06/23 21:43:51 [INFO] BNP: GetAvailablePricingVariants completed successfully in 49.970917ms, returned 6 variants for scheme 1695
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:43:51 [DEBUG] BNP: principal: 367
2025/06/23 21:43:51 [DEBUG] BNP: downPayment: 0
2025/06/23 21:43:51 [DEBUG] BNP: instalment: 0
2025/06/23 21:43:51 [DEBUG] BNP: schemeId: 2791
2025/06/23 21:43:51 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:43:51 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP: URL Parameters: [433147 353 367.00 0.00 0.00 2791]
2025/06/23 21:43:51 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/2791
2025/06/23 21:43:51 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:43:51 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/2791
2025/06/23 21:43:51 [INFO] BNP: HTTP Method: GET
2025/06/23 21:43:51 [DEBUG] BNP: Request Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:43:51 [DEBUG] BNP: Request Parameters:
2025/06/23 21:43:51 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:43:51 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:43:51 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:43:51 [DEBUG] BNP:   Principal: 367.00
2025/06/23 21:43:51 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:43:51 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/23 21:43:51 [DEBUG] BNP:   Additional Param[7]: 2791
2025/06/23 21:43:51 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/2791 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:43:51 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:43:51 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: Duration: 49.329202ms
2025/06/23 21:43:51 [INFO] BNP: Status Code: 200
2025/06/23 21:43:51 [INFO] BNP: Status: 200 OK
2025/06/23 21:43:51 [DEBUG] BNP: Response Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:43:51 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:43:51 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:43:51 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:43:51 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:43:50 GMT
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Length: 572
2025/06/23 21:43:51 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Length: 572 bytes
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>0</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>183.50</InstallmentAmount><Maturity>2</Maturity><NIR>0</NIR><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName><PricingVariantId>792441</PricingVariantId><TotalRepaymentAmount>367.00</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:43:51 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Response Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: variantCount: 1
2025/06/23 21:43:51 [DEBUG] BNP: errorCode: 0
2025/06/23 21:43:51 [DEBUG] BNP: errorMessage:
2025/06/23 21:43:51 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 792441, defaulting to '0'
2025/06/23 21:43:51 [DEBUG] BNP: Parsed variant ID=792441, APR=0, Maturity=2, InstallmentAmount=183.50
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: schemeId: 2791
2025/06/23 21:43:51 [DEBUG] BNP: success: true
2025/06/23 21:43:51 [DEBUG] BNP: inputVariants: 1
2025/06/23 21:43:51 [DEBUG] BNP: outputVariants: 1
2025/06/23 21:43:51 [INFO] BNP: GetAvailablePricingVariants completed successfully in 49.329202ms, returned 1 variants for scheme 2791
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:43:51 [DEBUG] BNP: principal: 367
2025/06/23 21:43:51 [DEBUG] BNP: downPayment: 0
2025/06/23 21:43:51 [DEBUG] BNP: instalment: 0
2025/06/23 21:43:51 [DEBUG] BNP: schemeId: 2792
2025/06/23 21:43:51 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:43:51 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP: URL Parameters: [433147 353 367.00 0.00 0.00 2792]
2025/06/23 21:43:51 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/2792
2025/06/23 21:43:51 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:43:51 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/2792
2025/06/23 21:43:51 [INFO] BNP: HTTP Method: GET
2025/06/23 21:43:51 [DEBUG] BNP: Request Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:43:51 [DEBUG] BNP: Request Parameters:
2025/06/23 21:43:51 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:43:51 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:43:51 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:43:51 [DEBUG] BNP:   Principal: 367.00
2025/06/23 21:43:51 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:43:51 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/23 21:43:51 [DEBUG] BNP:   Additional Param[7]: 2792
2025/06/23 21:43:51 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/367.00/0.00/0.00/2792 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:43:51 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:43:51 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [INFO] BNP: Duration: 47.945547ms
2025/06/23 21:43:51 [INFO] BNP: Status Code: 200
2025/06/23 21:43:51 [INFO] BNP: Status: 200 OK
2025/06/23 21:43:51 [DEBUG] BNP: Response Headers:
2025/06/23 21:43:51 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:43:51 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:43:51 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:43:51 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:43:50 GMT
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Length: 576
2025/06/23 21:43:51 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:43:51 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Length: 576 bytes
2025/06/23 21:43:51 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>-0.02</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>122.33</InstallmentAmount><Maturity>3</Maturity><NIR>0</NIR><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName><PricingVariantId>792443</PricingVariantId><TotalRepaymentAmount>367.00</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:43:51 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Response Validation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: variantCount: 1
2025/06/23 21:43:51 [DEBUG] BNP: errorCode: 0
2025/06/23 21:43:51 [DEBUG] BNP: errorMessage:
2025/06/23 21:43:51 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 792443, defaulting to '0'
2025/06/23 21:43:51 [DEBUG] BNP: Parsed variant ID=792443, APR=-0.02, Maturity=3, InstallmentAmount=122.33
2025/06/23 21:43:51 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:51 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:43:51 [INFO] BNP: Timestamp: 2025-06-23T21:43:51Z
2025/06/23 21:43:51 [DEBUG] BNP: inputVariants: 1
2025/06/23 21:43:51 [DEBUG] BNP: outputVariants: 1
2025/06/23 21:43:51 [DEBUG] BNP: schemeId: 2792
2025/06/23 21:43:51 [DEBUG] BNP: success: true
2025/06/23 21:43:51 [INFO] BNP: GetAvailablePricingVariants completed successfully in 47.945547ms, returned 1 variants for scheme 2792
2025/06/23 21:43:51 [DEBUG] BNP: BNP calculator returned 3 variant groups
2025/06/23 21:43:51 [DEBUG] BNP: Processing group 0 with scheme ID 1695 containing 6 variants
2025/06/23 21:43:51 [DEBUG] BNP: Variant 481446 (group 0, variant 0): 15 months, 28.99% APR, 28.87 BGN installment
2025/06/23 21:43:51 [DEBUG] BNP: Variant 481445 (group 0, variant 1): 13 months, 28.87% APR, 32.63 BGN installment
2025/06/23 21:43:51 [DEBUG] BNP: Variant 481444 (group 0, variant 2): 12 months, 28.84% APR, 34.99 BGN installment
2025/06/23 21:43:51 [DEBUG] BNP: Variant 481443 (group 0, variant 3): 9 months, 28.34% APR, 45.18 BGN installment
2025/06/23 21:43:51 [DEBUG] BNP: Variant 481442 (group 0, variant 4): 6 months, 27.17% APR, 65.57 BGN installment
2025/06/23 21:43:51 [DEBUG] BNP: Variant 481441 (group 0, variant 5): 3 months, 23.73% APR, 126.74 BGN installment
2025/06/23 21:43:51 [DEBUG] BNP: Processing group 1 with scheme ID 2791 containing 1 variants
2025/06/23 21:43:51 [DEBUG] BNP: Variant 792441 (group 1, variant 0): 2 months, 0.00% APR, 183.50 BGN installment
2025/06/23 21:43:51 [DEBUG] BNP: Processing group 2 with scheme ID 2792 containing 1 variants
2025/06/23 21:43:51 [DEBUG] BNP: Variant 792443 (group 2, variant 0): 3 months, -0.02% APR, 122.33 BGN installment
2025/06/23 21:43:51 [INFO] BNP: Successfully retrieved 8 total variants from BNP API
2025/06/23 21:43:51 [DEBUG] BNP: Found variant 481442 at array index 4
2025/06/23 21:43:51 [DEBUG] BNP: Retrieved 8 variants from Magento API
2025/06/23 21:43:51 [DEBUG] BNP: Added 8 variants to payment data with numeric keys for admin panel display
2025/06/23 21:43:51 [DEBUG] BNP: Serializing payment data to PHP format for Magento compatibility...
2025/06/23 21:43:51 [DEBUG] BNP: Payment data serialized successfully (3923 bytes)
2025/06/23 21:43:51 [DEBUG] BNP: Updating quote payment records...
2025/06/23 21:43:51 [DEBUG] BNP: Using existing quote payment record
2025/06/23 21:43:51 [DEBUG] BNP: Updating payment record 1 (ID: 1838712)
2025/06/23 21:43:51 [DEBUG] BNP: Payment record 1838712 updated successfully
2025/06/23 21:43:51 [INFO] BNP: Storing persistent payment data for quote 1144966 (2827 bytes)

2025/06/23 21:43:51 /graphapi/internal/praktis/checkout/cart-utils/payments.go:394
[6.573ms] [rows:1] UPDATE `sales_flat_quote_payment` SET `additional_information`='a:20:{s:9:"principal";s:3:"367";s:1:"2";a:9:{s:11:"installment";s:5:"34.99";s:3:"nir";s:4:"25.6";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481444;s:3:"apr";s:5:"28.84";s:15:"total_repayment";s:6:"419.85";s:8:"maturity";i:12;s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}s:1:"6";a:9:{s:17:"pricing_scheme_id";i:2791;s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 2м. с кредитна карта";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:792441;s:8:"maturity";i:2;s:3:"nir";s:1:"0";s:3:"apr";s:1:"0";s:11:"installment";s:5:"183.5";s:15:"total_repayment";s:3:"367";}s:16:"selected_variant";a:9:{s:2:"id";s:6:"481442";s:3:"apr";s:5:"27.17";s:11:"installment";s:5:"65.57";s:15:"total_repayment";s:6:"393.42";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";}s:11:"downpayment";s:1:"0";s:3:"pin";s:10:"9102288468";s:5:"email";s:26:"<EMAIL>";s:10:"variant_id";i:4;s:5:"phone";s:10:"0883555204";s:1:"0";a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481446;s:15:"total_repayment";s:6:"433.06";s:8:"maturity";i:15;s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:3:"apr";s:5:"28.99";s:11:"installment";s:5:"28.87";s:3:"nir";s:5:"25.73";}s:1:"1";a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:15:"total_repayment";s:6:"424.25";s:8:"maturity";i:13;s:11:"installment";s:5:"32.63";s:3:"nir";s:5:"25.66";s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:2:"id";i:481445;s:3:"apr";s:5:"28.87";}s:1:"7";a:9:{s:2:"id";i:792443;s:11:"installment";s:6:"122.33";s:3:"apr";s:5:"-0.02";s:15:"total_repayment";s:3:"367";s:8:"maturity";i:3;s:3:"nir";s:1:"0";s:17:"pricing_scheme_id";i:2792;s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 3м. с кредитна карта";s:26:"correct_downpayment_amount";s:1:"0";}s:4:"loan";a:10:{s:3:"apr";s:5:"27.17";s:26:"correct_downpayment_amount";s:1:"0";s:18:"installment_amount";s:5:"65.57";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:21:"processing_fee_amount";s:1:"0";s:22:"total_repayment_amount";s:6:"393.42";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:18:"pricing_variant_id";s:6:"481442";}s:5:"names";s:15:"Stoyan Atanasov";s:4:"name";s:15:"Stoyan Atanasov";s:1:"4";a:9:{s:11:"installment";s:5:"65.57";s:3:"nir";s:5:"24.28";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:2:"id";i:481442;s:3:"apr";s:5:"27.17";s:15:"total_repayment";s:6:"393.42";s:8:"maturity";i:6;s:17:"pricing_scheme_id";i:1695;s:26:"correct_downpayment_amount";s:1:"0";}s:1:"5";a:9:{s:17:"pricing_scheme_id";i:1695;s:2:"id";i:481441;s:3:"apr";s:5:"23.73";s:11:"installment";s:6:"126.74";s:3:"nir";s:5:"21.47";s:15:"total_repayment";s:6:"380.21";s:8:"maturity";i:3;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";}s:13:"customer_data";a:11:{s:3:"egn";s:10:"9102288468";s:9:"last_name";s:8:"Atanasov";s:9:"post_code";s:4:"1000";s:5:"email";s:26:"<EMAIL>";s:7:"address";s:10:"sdasdsadas";s:4:"city";s:5:"sofia";s:12:"company_name";s:0:"";s:3:"eik";s:0:"";s:3:"mol";s:0:"";s:10:"first_name";s:6:"Stoyan";s:5:"phone";s:10:"0883555204";}s:13:"good_type_ids";s:3:"353";s:1:"3";a:9:{s:15:"total_repayment";s:6:"406.64";s:17:"pricing_scheme_id";i:1695;s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481443;s:3:"apr";s:5:"28.34";s:11:"installment";s:5:"45.18";s:8:"maturity";i:9;s:3:"nir";s:5:"25.22";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}}' WHERE `payment_id` = 1838712

2025/06/23 21:43:51 /graphapi/internal/praktis/checkout/cart-utils/payments.go:440
[1.223ms] [rows:0]
		CREATE TABLE IF NOT EXISTS bnp_payment_data_storage (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			quote_id bigint(20) NOT NULL,
			payment_data text NOT NULL,
			created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			UNIQUE KEY unique_quote_id (quote_id),
			KEY idx_quote_id (quote_id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

2025/06/23 21:43:51 [INFO] BNP: Persistent payment data stored successfully in dedicated table
2025/06/23 21:43:51 [INFO] BNP: === PAYMENT DATA UPDATE COMPLETED ===
2025/06/23 21:43:51 [INFO] BNP: Quote: 1144966, Updated 1 payment records, Status: SUCCESS

2025/06/23 21:43:51 /graphapi/internal/praktis/checkout/cart-utils/payments.go:468
[2.101ms] [rows:1]
		INSERT INTO bnp_payment_data_storage (quote_id, payment_data, created_at, updated_at)
		VALUES (1144966, '{"0":{"apr":28.99,"correct_downpayment_amount":0,"id":481446,"installment":28.87,"maturity":15,"nir":25.73,"pricing_scheme_id":1695,"pricing_scheme_name":"1,2% месечно оскъпяване","total_repayment":433.06},"1":{"apr":28.87,"correct_downpayment_amount":0,"id":481445,"installment":32.63,"maturity":13,"nir":25.66,"pricing_scheme_id":1695,"pricing_scheme_name":"1,2% месечно оскъпяване","total_repayment":424.25},"2":{"apr":28.84,"correct_downpayment_amount":0,"id":481444,"installment":34.99,"maturity":12,"nir":25.6,"pricing_scheme_id":1695,"pricing_scheme_name":"1,2% месечно оскъпяване","total_repayment":419.85},"3":{"apr":28.34,"correct_downpayment_amount":0,"id":481443,"installment":45.18,"maturity":9,"nir":25.22,"pricing_scheme_id":1695,"pricing_scheme_name":"1,2% месечно оскъпяване","total_repayment":406.64},"4":{"apr":27.17,"correct_downpayment_amount":0,"id":481442,"installment":65.57,"maturity":6,"nir":24.28,"pricing_scheme_id":1695,"pricing_scheme_name":"1,2% месечно оскъпяване","total_repayment":393.42},"5":{"apr":23.73,"correct_downpayment_amount":0,"id":481441,"installment":126.74,"maturity":3,"nir":21.47,"pricing_scheme_id":1695,"pricing_scheme_name":"1,2% месечно оскъпяване","total_repayment":380.21},"6":{"apr":0,"correct_downpayment_amount":0,"id":792441,"installment":183.5,"maturity":2,"nir":0,"pricing_scheme_id":2791,"pricing_scheme_name":"Вземи сега, плати после:0% за 2м. с кредитна карта","total_repayment":367},"7":{"apr":-0.02,"correct_downpayment_amount":0,"id":792443,"installment":122.33,"maturity":3,"nir":0,"pricing_scheme_id":2792,"pricing_scheme_name":"Вземи сега, плати после:0% за 3м. с кредитна карта","total_repayment":367},"customer_data":{"address":"sdasdsadas","city":"sofia","company_name":"","egn":"9102288468","eik":"","email":"<EMAIL>","first_name":"Stoyan","last_name":"Atanasov","mol":"","phone":"0883555204","post_code":"1000"},"downpayment":0,"email":"<EMAIL>","good_type_ids":"353","loan":{"apr":"27.17","correct_downpayment_amount":"0","installment_amount":"65.57","maturity":"6","nir":"24.28","pricing_scheme_id":"1695","pricing_scheme_name":"1,2% месечно оскъпяване","pricing_variant_id":"481442","processing_fee_amount":"0","total_repayment_amount":"393.42"},"name":"Stoyan Atanasov","names":"Stoyan Atanasov","phone":"0883555204","pin":"9102288468","principal":367,"selected_variant":{"apr":"27.17","correct_downpayment_amount":"0","id":"481442","installment":"65.57","maturity":"6","nir":"24.28","pricing_scheme_id":"1695","pricing_scheme_name":"1,2% месечно оскъпяване","total_repayment":"393.42"},"variant_id":4}', NOW(), NOW())
		ON DUPLICATE KEY UPDATE
		payment_data = VALUES(payment_data),
		updated_at = NOW()


2025/06/23 21:43:51 /graphapi/internal/praktis/checkout/cart-utils/payments.go:417
[0.268ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144966
2025/06/23 21:43:51 [DEBUG] BNP: Immediate verification after save - Valid: true, Length: 3923
XDEBUG_SESSION=PHPSTORM
CallAPI->http://server/theme_api/sales_cart/calculateTotals took 279.051279ms

2025/06/23 21:43:51 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:84
[0.168ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144966

2025/06/23 21:43:51 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:84
[0.637ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE entity_id = 1144966 ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

2025/06/23 21:43:51 /graphapi/packages/magento-core/mage-store/sales/quote-item.go:67
[0.185ms] [rows:1] SELECT * FROM `sales_flat_quote_item` WHERE quote_id = 1144966

2025/06/23 21:43:51 /graphapi/packages/magento-core/mage-store/sales/quote.go:218
[0.625ms] [rows:2] SELECT * FROM `sales_flat_quote_address` WHERE `sales_flat_quote_address`.`quote_id` = 1144966

2025/06/23 21:43:51 /graphapi/packages/magento-core/mage-store/mage-product/product-image.go:101
[0.265ms] [rows:1] select value, label, position from catalog_product_entity_media_gallery g
    inner join catalog_product_entity_media_gallery_value go on g.value_id = go.value_id
where entity_id = 198407 and disabled = 0 and store_id in (0) order by position asc

2025/06/23 21:43:51 /graphapi/internal/praktis/catalog/product/product-video.go:50
[0.159ms] [rows:0]
		SELECT
			cpe.entity_id as product_id,
			iv.video_id,
			iv.title as video_title,
			iv.description as video_description,
			iv.url as video_url,
			iv.video_type,
			iv.video_store_view,
			iv.video_status,
			iv.image as video_thumbnail,
			ipv.video_position
		FROM catalog_product_entity cpe
			INNER JOIN iwd_product_video ipv ON cpe.entity_id = ipv.product_id
			INNER JOIN iwd_video iv ON ipv.video_id = iv.video_id
		WHERE cpe.entity_id = 198407
			AND iv.video_status = 1  -- Only active videos
		ORDER BY ipv.video_position


2025/06/23 21:43:51 /graphapi/internal/praktis/catalog/product/product-availability.go:38
[0.139ms] [rows:5] SELECT * FROM `stenik_zeron_warehouse_product` WHERE product_id = 198407

2025/06/23 21:43:51 /graphapi/internal/praktis/catalog/product/product-availability.go:104
[0.187ms] [rows:9] SELECT * FROM `theme_praktis_store` ORDER BY display_order ASC
Found 9 active stores
Store: ID=20, Name=PRAKTIS - София MEGA, WarehouseCode=1001
Store: ID=18, Name=PRAKTIS-Пловдив, WarehouseCode=901
Store: ID=13, Name=PRAKTIS - Хасково, WarehouseCode=601
Store: ID=12, Name=PRAKTIS - Велико Търново, WarehouseCode=501
Store: ID=10, Name=PRAKTIS - Видин, WarehouseCode=201
Store: ID=11, Name=PRAKTIS - Стара Загора, WarehouseCode=401
Store: ID=15, Name=PRAKTIS - Русе, WarehouseCode=801
Store: ID=7, Name=PRAKTIS - София Хаджи Димитър, WarehouseCode=101
Store: ID=19, Name=Мебели Търговски Център-Стара Загора, WarehouseCode=420

2025/06/23 21:43:51 /graphapi/internal/praktis/catalog/product/product-availability.go:151
[0.135ms] [rows:5] SELECT * FROM `stenik_zeron_warehouse_product` WHERE product_id in (198407)

2025/06/23 21:43:51 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.126ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '901' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:43:51 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.079ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '201' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:43:51 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.075ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:43:51 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.070ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '401' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:43:51 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.074ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '101' ORDER BY `theme_praktis_store`.`id` LIMIT 1
[GIN] 2025/06/23 - 21:43:51 | 200 |  842.403177ms |       ********* | POST     "/graphql"

2025/06/23 21:43:52 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[0.489ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144966

2025/06/23 21:43:52 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[1.344ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE uuid = '6366d629-e396-4a28-9591-19aec08bb978' ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

2025/06/23 21:43:52 /graphapi/packages/magento-core/mage-store/sales/quote.go:218
[0.576ms] [rows:2] SELECT * FROM `sales_flat_quote_address` WHERE `sales_flat_quote_address`.`quote_id` = 1144966
2025/06/23 21:43:52 [INFO] BNP: Retrieving persistent payment data before order creation operations
2025/06/23 21:43:52 [INFO] BNP: Retrieving persistent payment data for quote 1144966

2025/06/23 21:43:52 /graphapi/internal/praktis/checkout/cart-utils/payments.go:489
[0.320ms] [rows:1] SELECT * FROM `bnp_payment_data_storage` WHERE quote_id = 1144966 ORDER BY `bnp_payment_data_storage`.`id` LIMIT 1
2025/06/23 21:43:52 [INFO] BNP: Retrieved persistent payment data (2827 bytes) for quote 1144966
2025/06/23 21:43:52 [INFO] BNP: Persistent payment data retrieved successfully (2827 bytes)

2025/06/23 21:43:52 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:259
[2.752ms] [rows:1] UPDATE `sales_flat_quote` SET `customer_email`='<EMAIL>',`customer_firstname`='Stoyan',`customer_id`=0,`customer_is_guest`=0,`customer_lastname`='Atanasov',`remote_ip`='*************',`updated_at`='2025-06-23 21:43:52.927' WHERE `entity_id` = 1144966

2025/06/23 21:43:52 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:275
[0.345ms] [rows:1] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-23 21:42:20',`updated_at`='2025-06-23 21:43:52.93',`address_type`='billing',`quote_id`=1144966,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`=NULL,`weight`=0,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=0,`base_grand_total`=0,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=NULL,`base_shipping_discount_amount`=NULL,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267138

2025/06/23 21:43:52 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:275
[0.304ms] [rows:1] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-23 21:42:20',`updated_at`='2025-06-23 21:43:52.93',`address_type`='shipping',`quote_id`=1144966,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`='Вземи от обект - Вземи от обект',`weight`=22.6,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=367,`base_grand_total`=367,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=0,`base_shipping_discount_amount`=0,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267139
2025/06/23 21:43:52 [INFO] BNP: Restoring payment data for quote 1144966 (2827 bytes)

2025/06/23 21:43:52 /graphapi/graphql/resolver/cart-checkout.resolvers.go:506
[0.137ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144966
2025/06/23 21:43:52 [DEBUG] BNP: Current payment data exists (3891 bytes), no restore needed

2025/06/23 21:43:52 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.210ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:43:52 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:47
[0.310ms] [rows:0] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-23 21:42:20',`updated_at`='2025-06-23 21:43:52.931',`address_type`='billing',`quote_id`=1144966,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`=NULL,`weight`=0,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=0,`base_grand_total`=0,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=NULL,`base_shipping_discount_amount`=NULL,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267138

2025/06/23 21:43:52 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:47
[0.421ms] [rows:0] INSERT INTO `sales_flat_quote_address` (`created_at`,`updated_at`,`address_type`,`quote_id`,`customer_id`,`customer_address_id`,`email`,`prefix`,`firstname`,`middlename`,`lastname`,`street`,`city_id`,`store_code`,`office_code`,`city`,`region`,`region_id`,`postcode`,`country_id`,`telephone`,`same_as_billing`,`free_shipping`,`collect_shipping_rates`,`shipping_method`,`shipping_description`,`weight`,`shipping_amount`,`base_shipping_amount`,`discount_amount`,`base_discount_amount`,`grand_total`,`base_grand_total`,`customer_notes`,`discount_description`,`shipping_discount_amount`,`base_shipping_discount_amount`,`invoice`,`invoice_type`,`invoice_company_name`,`invoice_company_mol`,`invoice_company_city`,`invoice_company_address`,`invoice_company_bulstat`,`invoice_company_vat`,`invoice_personal_name`,`invoice_personal_pin`,`invoice_personal_city`,`invoice_personal_vat`,`invoice_personal_address`,`praktis_shipping_discount_amount`,`base_praktis_shipping_discount_amount`,`cash_on_delivery_tax_amount`,`base_cash_on_delivery_tax_amount`,`shipping_type`,`address_id`) VALUES ('2025-06-23 21:42:20','2025-06-23 21:43:52.931','billing',1144966,NULL,NULL,'<EMAIL>',NULL,'Stoyan',NULL,'Atanasov','(1001) бул. Ботевградско шосе 527',NULL,'1001',NULL,'гр. София',NULL,NULL,'1000','BG','0883555204',1,0,0,'stenik_siteshipping_from_store_stenik_siteshipping_from_store',NULL,0,0,0,0,0,0,0,NULL,NULL,NULL,NULL,'0','','','','','','','','','','','','',0,0,0,0,22,2267138) ON DUPLICATE KEY UPDATE `updated_at`='2025-06-23 21:43:52.931',`address_type`=VALUES(`address_type`),`quote_id`=VALUES(`quote_id`),`customer_id`=VALUES(`customer_id`),`customer_address_id`=VALUES(`customer_address_id`),`email`=VALUES(`email`),`prefix`=VALUES(`prefix`),`firstname`=VALUES(`firstname`),`middlename`=VALUES(`middlename`),`lastname`=VALUES(`lastname`),`street`=VALUES(`street`),`city_id`=VALUES(`city_id`),`store_code`=VALUES(`store_code`),`office_code`=VALUES(`office_code`),`city`=VALUES(`city`),`region`=VALUES(`region`),`region_id`=VALUES(`region_id`),`postcode`=VALUES(`postcode`),`country_id`=VALUES(`country_id`),`telephone`=VALUES(`telephone`),`same_as_billing`=VALUES(`same_as_billing`),`free_shipping`=VALUES(`free_shipping`),`collect_shipping_rates`=VALUES(`collect_shipping_rates`),`shipping_method`=VALUES(`shipping_method`),`shipping_description`=VALUES(`shipping_description`),`weight`=VALUES(`weight`),`shipping_amount`=VALUES(`shipping_amount`),`base_shipping_amount`=VALUES(`base_shipping_amount`),`discount_amount`=VALUES(`discount_amount`),`base_discount_amount`=VALUES(`base_discount_amount`),`grand_total`=VALUES(`grand_total`),`base_grand_total`=VALUES(`base_grand_total`),`customer_notes`=VALUES(`customer_notes`),`discount_description`=VALUES(`discount_description`),`shipping_discount_amount`=VALUES(`shipping_discount_amount`),`base_shipping_discount_amount`=VALUES(`base_shipping_discount_amount`),`invoice`=VALUES(`invoice`),`invoice_type`=VALUES(`invoice_type`),`invoice_company_name`=VALUES(`invoice_company_name`),`invoice_company_mol`=VALUES(`invoice_company_mol`),`invoice_company_city`=VALUES(`invoice_company_city`),`invoice_company_address`=VALUES(`invoice_company_address`),`invoice_company_bulstat`=VALUES(`invoice_company_bulstat`),`invoice_company_vat`=VALUES(`invoice_company_vat`),`invoice_personal_name`=VALUES(`invoice_personal_name`),`invoice_personal_pin`=VALUES(`invoice_personal_pin`),`invoice_personal_city`=VALUES(`invoice_personal_city`),`invoice_personal_vat`=VALUES(`invoice_personal_vat`),`invoice_personal_address`=VALUES(`invoice_personal_address`),`praktis_shipping_discount_amount`=VALUES(`praktis_shipping_discount_amount`),`base_praktis_shipping_discount_amount`=VALUES(`base_praktis_shipping_discount_amount`),`cash_on_delivery_tax_amount`=VALUES(`cash_on_delivery_tax_amount`),`base_cash_on_delivery_tax_amount`=VALUES(`base_cash_on_delivery_tax_amount`),`shipping_type`=VALUES(`shipping_type`)

2025/06/23 21:43:52 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.175ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:43:52 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:47
[0.290ms] [rows:0] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-23 21:42:20',`updated_at`='2025-06-23 21:43:52.932',`address_type`='shipping',`quote_id`=1144966,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`='Вземи от обект - Вземи от обект',`weight`=22.6,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=367,`base_grand_total`=367,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=0,`base_shipping_discount_amount`=0,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267139
2025/06/23 21:43:52 [INFO] BNP: Restoring payment data for quote 1144966 (2827 bytes)

2025/06/23 21:43:52 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:47
[0.377ms] [rows:0] INSERT INTO `sales_flat_quote_address` (`created_at`,`updated_at`,`address_type`,`quote_id`,`customer_id`,`customer_address_id`,`email`,`prefix`,`firstname`,`middlename`,`lastname`,`street`,`city_id`,`store_code`,`office_code`,`city`,`region`,`region_id`,`postcode`,`country_id`,`telephone`,`same_as_billing`,`free_shipping`,`collect_shipping_rates`,`shipping_method`,`shipping_description`,`weight`,`shipping_amount`,`base_shipping_amount`,`discount_amount`,`base_discount_amount`,`grand_total`,`base_grand_total`,`customer_notes`,`discount_description`,`shipping_discount_amount`,`base_shipping_discount_amount`,`invoice`,`invoice_type`,`invoice_company_name`,`invoice_company_mol`,`invoice_company_city`,`invoice_company_address`,`invoice_company_bulstat`,`invoice_company_vat`,`invoice_personal_name`,`invoice_personal_pin`,`invoice_personal_city`,`invoice_personal_vat`,`invoice_personal_address`,`praktis_shipping_discount_amount`,`base_praktis_shipping_discount_amount`,`cash_on_delivery_tax_amount`,`base_cash_on_delivery_tax_amount`,`shipping_type`,`address_id`) VALUES ('2025-06-23 21:42:20','2025-06-23 21:43:52.932','shipping',1144966,NULL,NULL,'<EMAIL>',NULL,'Stoyan',NULL,'Atanasov','(1001) бул. Ботевградско шосе 527',NULL,'1001',NULL,'гр. София',NULL,NULL,'1000','BG','0883555204',1,0,0,'stenik_siteshipping_from_store_stenik_siteshipping_from_store','Вземи от обект - Вземи от обект',22.6,0,0,0,0,367,367,NULL,NULL,0,0,'0','','','','','','','','','','','','',0,0,0,0,22,2267139) ON DUPLICATE KEY UPDATE `updated_at`='2025-06-23 21:43:52.932',`address_type`=VALUES(`address_type`),`quote_id`=VALUES(`quote_id`),`customer_id`=VALUES(`customer_id`),`customer_address_id`=VALUES(`customer_address_id`),`email`=VALUES(`email`),`prefix`=VALUES(`prefix`),`firstname`=VALUES(`firstname`),`middlename`=VALUES(`middlename`),`lastname`=VALUES(`lastname`),`street`=VALUES(`street`),`city_id`=VALUES(`city_id`),`store_code`=VALUES(`store_code`),`office_code`=VALUES(`office_code`),`city`=VALUES(`city`),`region`=VALUES(`region`),`region_id`=VALUES(`region_id`),`postcode`=VALUES(`postcode`),`country_id`=VALUES(`country_id`),`telephone`=VALUES(`telephone`),`same_as_billing`=VALUES(`same_as_billing`),`free_shipping`=VALUES(`free_shipping`),`collect_shipping_rates`=VALUES(`collect_shipping_rates`),`shipping_method`=VALUES(`shipping_method`),`shipping_description`=VALUES(`shipping_description`),`weight`=VALUES(`weight`),`shipping_amount`=VALUES(`shipping_amount`),`base_shipping_amount`=VALUES(`base_shipping_amount`),`discount_amount`=VALUES(`discount_amount`),`base_discount_amount`=VALUES(`base_discount_amount`),`grand_total`=VALUES(`grand_total`),`base_grand_total`=VALUES(`base_grand_total`),`customer_notes`=VALUES(`customer_notes`),`discount_description`=VALUES(`discount_description`),`shipping_discount_amount`=VALUES(`shipping_discount_amount`),`base_shipping_discount_amount`=VALUES(`base_shipping_discount_amount`),`invoice`=VALUES(`invoice`),`invoice_type`=VALUES(`invoice_type`),`invoice_company_name`=VALUES(`invoice_company_name`),`invoice_company_mol`=VALUES(`invoice_company_mol`),`invoice_company_city`=VALUES(`invoice_company_city`),`invoice_company_address`=VALUES(`invoice_company_address`),`invoice_company_bulstat`=VALUES(`invoice_company_bulstat`),`invoice_company_vat`=VALUES(`invoice_company_vat`),`invoice_personal_name`=VALUES(`invoice_personal_name`),`invoice_personal_pin`=VALUES(`invoice_personal_pin`),`invoice_personal_city`=VALUES(`invoice_personal_city`),`invoice_personal_vat`=VALUES(`invoice_personal_vat`),`invoice_personal_address`=VALUES(`invoice_personal_address`),`praktis_shipping_discount_amount`=VALUES(`praktis_shipping_discount_amount`),`base_praktis_shipping_discount_amount`=VALUES(`base_praktis_shipping_discount_amount`),`cash_on_delivery_tax_amount`=VALUES(`cash_on_delivery_tax_amount`),`base_cash_on_delivery_tax_amount`=VALUES(`base_cash_on_delivery_tax_amount`),`shipping_type`=VALUES(`shipping_type`)
2025/06/23 21:43:52 [DEBUG] BNP: Current payment data exists (3891 bytes), no restore needed

2025/06/23 21:43:52 /graphapi/graphql/resolver/cart-checkout.resolvers.go:506
[0.118ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144966

2025/06/23 21:43:52 /graphapi/graphql/resolver/cart-checkout.resolvers.go:341
[0.171ms] [rows:1] UPDATE `sales_flat_quote` SET `customer_note`='' WHERE entity_id = 1144966
2025/06/23 21:43:52 [DEBUG] UpdateQuotePaymentMethod: Processing payment ID 1838712, current method: stenik_leasingjetcredit, target method: stenik_leasingjetcredit
2025/06/23 21:43:52 [DEBUG] UpdateQuotePaymentMethod: Payment method already correct for payment ID 1838712, skipping update
2025/06/23 21:43:52 [INFO] BNP: Restoring payment data for quote 1144966 (2827 bytes)

2025/06/23 21:43:52 /graphapi/graphql/resolver/cart-checkout.resolvers.go:506
[0.103ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144966
2025/06/23 21:43:52 [DEBUG] BNP: Current payment data exists (3891 bytes), no restore needed

2025/06/23 21:43:52 /graphapi/graphql/resolver/cart-checkout.resolvers.go:365
[0.166ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144966 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:43:52 /graphapi/graphql/resolver/cart-checkout.resolvers.go:377
[0.118ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144966
2025/06/23 21:43:52 [DEBUG] BNP: Database check - Valid: true, Value: a:20:{s:9:"principal";s:3:"367";i:2;a:9:{s:11:"installment";s:5:"34.99";s:3:"nir";s:4:"25.6";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481444;s:3:"apr";s:5:"28.84";s:15:"total_repayment";s:6:"419.85";s:8:"maturity";i:12;s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}i:6;a:9:{s:17:"pricing_scheme_id";i:2791;s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 2м. с кредитна карта";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:792441;s:8:"maturity";i:2;s:3:"nir";s:1:"0";s:3:"apr";s:1:"0";s:11:"installment";s:5:"183.5";s:15:"total_repayment";s:3:"367";}s:16:"selected_variant";a:9:{s:2:"id";s:6:"481442";s:3:"apr";s:5:"27.17";s:11:"installment";s:5:"65.57";s:15:"total_repayment";s:6:"393.42";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";}s:11:"downpayment";s:1:"0";s:3:"pin";s:10:"9102288468";s:5:"email";s:26:"<EMAIL>";s:10:"variant_id";i:4;s:5:"phone";s:10:"0883555204";i:0;a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481446;s:15:"total_repayment";s:6:"433.06";s:8:"maturity";i:15;s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:3:"apr";s:5:"28.99";s:11:"installment";s:5:"28.87";s:3:"nir";s:5:"25.73";}i:1;a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:15:"total_repayment";s:6:"424.25";s:8:"maturity";i:13;s:11:"installment";s:5:"32.63";s:3:"nir";s:5:"25.66";s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:2:"id";i:481445;s:3:"apr";s:5:"28.87";}i:7;a:9:{s:2:"id";i:792443;s:11:"installment";s:6:"122.33";s:3:"apr";s:5:"-0.02";s:15:"total_repayment";s:3:"367";s:8:"maturity";i:3;s:3:"nir";s:1:"0";s:17:"pricing_scheme_id";i:2792;s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 3м. с кредитна карта";s:26:"correct_downpayment_amount";s:1:"0";}s:4:"loan";a:10:{s:3:"apr";s:5:"27.17";s:26:"correct_downpayment_amount";s:1:"0";s:18:"installment_amount";s:5:"65.57";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:21:"processing_fee_amount";s:1:"0";s:22:"total_repayment_amount";s:6:"393.42";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:18:"pricing_variant_id";s:6:"481442";}s:5:"names";s:15:"Stoyan Atanasov";s:4:"name";s:15:"Stoyan Atanasov";i:4;a:9:{s:11:"installment";s:5:"65.57";s:3:"nir";s:5:"24.28";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:2:"id";i:481442;s:3:"apr";s:5:"27.17";s:15:"total_repayment";s:6:"393.42";s:8:"maturity";i:6;s:17:"pricing_scheme_id";i:1695;s:26:"correct_downpayment_amount";s:1:"0";}i:5;a:9:{s:17:"pricing_scheme_id";i:1695;s:2:"id";i:481441;s:3:"apr";s:5:"23.73";s:11:"installment";s:6:"126.74";s:3:"nir";s:5:"21.47";s:15:"total_repayment";s:6:"380.21";s:8:"maturity";i:3;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";}s:13:"customer_data";a:11:{s:3:"egn";s:10:"9102288468";s:9:"last_name";s:8:"Atanasov";s:9:"post_code";s:4:"1000";s:5:"email";s:26:"<EMAIL>";s:7:"address";s:10:"sdasdsadas";s:4:"city";s:5:"sofia";s:12:"company_name";s:0:"";s:3:"eik";s:0:"";s:3:"mol";s:0:"";s:10:"first_name";s:6:"Stoyan";s:5:"phone";s:10:"0883555204";}s:13:"good_type_ids";s:3:"353";i:3;a:9:{s:15:"total_repayment";s:6:"406.64";s:17:"pricing_scheme_id";i:1695;s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481443;s:3:"apr";s:5:"28.34";s:11:"installment";s:5:"45.18";s:8:"maturity";i:9;s:3:"nir";s:5:"25.22";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}}
2025/06/23 21:43:52 [DEBUG] BNP: Updated payment object with additional information
2025/06/23 21:43:52 [DEBUG] BNP: === VALIDATING QUOTE FOR BNP PAYMENT ===
2025/06/23 21:43:52 [DEBUG] BNP: Quote ID: 1144966
2025/06/23 21:43:52 [DEBUG] BNP: Payment ID: 1838712
2025/06/23 21:43:52 [DEBUG] BNP: Payment method: stenik_leasingjetcredit
2025/06/23 21:43:52 [DEBUG] BNP: Additional information length: 3891
2025/06/23 21:43:52 [DEBUG] BNP: Additional information content: a:20:{s:9:"principal";s:3:"367";i:2;a:9:{s:11:"installment";s:5:"34.99";s:3:"nir";s:4:"25.6";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481444;s:3:"apr";s:5:"28.84";s:15:"total_repayment";s:6:"419.85";s:8:"maturity";i:12;s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}i:6;a:9:{s:17:"pricing_scheme_id";i:2791;s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 2м. с кредитна карта";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:792441;s:8:"maturity";i:2;s:3:"nir";s:1:"0";s:3:"apr";s:1:"0";s:11:"installment";s:5:"183.5";s:15:"total_repayment";s:3:"367";}s:16:"selected_variant";a:9:{s:2:"id";s:6:"481442";s:3:"apr";s:5:"27.17";s:11:"installment";s:5:"65.57";s:15:"total_repayment";s:6:"393.42";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";}s:11:"downpayment";s:1:"0";s:3:"pin";s:10:"9102288468";s:5:"email";s:26:"<EMAIL>";s:10:"variant_id";i:4;s:5:"phone";s:10:"0883555204";i:0;a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481446;s:15:"total_repayment";s:6:"433.06";s:8:"maturity";i:15;s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:3:"apr";s:5:"28.99";s:11:"installment";s:5:"28.87";s:3:"nir";s:5:"25.73";}i:1;a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:15:"total_repayment";s:6:"424.25";s:8:"maturity";i:13;s:11:"installment";s:5:"32.63";s:3:"nir";s:5:"25.66";s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:2:"id";i:481445;s:3:"apr";s:5:"28.87";}i:7;a:9:{s:2:"id";i:792443;s:11:"installment";s:6:"122.33";s:3:"apr";s:5:"-0.02";s:15:"total_repayment";s:3:"367";s:8:"maturity";i:3;s:3:"nir";s:1:"0";s:17:"pricing_scheme_id";i:2792;s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 3м. с кредитна карта";s:26:"correct_downpayment_amount";s:1:"0";}s:4:"loan";a:10:{s:3:"apr";s:5:"27.17";s:26:"correct_downpayment_amount";s:1:"0";s:18:"installment_amount";s:5:"65.57";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:21:"processing_fee_amount";s:1:"0";s:22:"total_repayment_amount";s:6:"393.42";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:18:"pricing_variant_id";s:6:"481442";}s:5:"names";s:15:"Stoyan Atanasov";s:4:"name";s:15:"Stoyan Atanasov";i:4;a:9:{s:11:"installment";s:5:"65.57";s:3:"nir";s:5:"24.28";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:2:"id";i:481442;s:3:"apr";s:5:"27.17";s:15:"total_repayment";s:6:"393.42";s:8:"maturity";i:6;s:17:"pricing_scheme_id";i:1695;s:26:"correct_downpayment_amount";s:1:"0";}i:5;a:9:{s:17:"pricing_scheme_id";i:1695;s:2:"id";i:481441;s:3:"apr";s:5:"23.73";s:11:"installment";s:6:"126.74";s:3:"nir";s:5:"21.47";s:15:"total_repayment";s:6:"380.21";s:8:"maturity";i:3;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";}s:13:"customer_data";a:11:{s:3:"egn";s:10:"9102288468";s:9:"last_name";s:8:"Atanasov";s:9:"post_code";s:4:"1000";s:5:"email";s:26:"<EMAIL>";s:7:"address";s:10:"sdasdsadas";s:4:"city";s:5:"sofia";s:12:"company_name";s:0:"";s:3:"eik";s:0:"";s:3:"mol";s:0:"";s:10:"first_name";s:6:"Stoyan";s:5:"phone";s:10:"0883555204";}s:13:"good_type_ids";s:3:"353";i:3;a:9:{s:15:"total_repayment";s:6:"406.64";s:17:"pricing_scheme_id";i:1695;s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481443;s:3:"apr";s:5:"28.34";s:11:"installment";s:5:"45.18";s:8:"maturity";i:9;s:3:"nir";s:5:"25.22";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}}
2025/06/23 21:43:52 [DEBUG] BNP: Payment data is in PHP serialized format (expected for Magento)
2025/06/23 21:43:52 [DEBUG] BNP: PHP serialized payment data validation passed
XDEBUG_SESSION=PHPSTORM
CallAPI->http://server/theme_api/sales_order/createNewOrder took 431.224978ms

2025/06/23 21:43:53 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.318ms] [rows:1] SELECT * FROM `sales_flat_order_payment` WHERE `sales_flat_order_payment`.`parent_id` = 301023

2025/06/23 21:43:53 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[1.508ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '120303012' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
2025/06/23 21:43:53 [INFO] BNP: Transferring payment data from quote 1144966 to order 301023
2025/06/23 21:43:53 [INFO] BNP: Retrieving persistent payment data for quote 1144966

2025/06/23 21:43:53 /graphapi/internal/praktis/checkout/cart-utils/payments.go:489
[0.154ms] [rows:1] SELECT * FROM `bnp_payment_data_storage` WHERE quote_id = 1144966 ORDER BY `bnp_payment_data_storage`.`id` LIMIT 1
2025/06/23 21:43:53 [INFO] BNP: Retrieved persistent payment data (2827 bytes) for quote 1144966
2025/06/23 21:43:53 [INFO] BNP: Found BNP payment data (2827 bytes), transferring to order payment
2025/06/23 21:43:53 [DEBUG] BNP: Converted variant_id from float64 4 to int 4 for admin panel compatibility
2025/06/23 21:43:53 [INFO] BNP: Converted payment data to PHP serialized format (4019 bytes)

2025/06/23 21:43:53 /graphapi/internal/praktis/checkout/cart-utils/payments.go:553
[1.807ms] [rows:1]
		UPDATE sales_flat_order_payment
2025/06/23 21:43:53 [INFO] BNP: Payment data successfully transferred to order 301023 (1 records updated)
		SET additional_information = 'a:20:{s:1:"1";a:9:{s:3:"apr";s:5:"28.87";s:26:"correct_downpayment_amount";s:1:"0";s:11:"installment";s:5:"32.63";s:2:"id";s:6:"481445";s:8:"maturity";s:2:"13";s:3:"nir";s:5:"25.66";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:15:"total_repayment";s:6:"424.25";}s:1:"5";a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";s:6:"481441";s:3:"nir";s:5:"21.47";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:15:"total_repayment";s:6:"380.21";s:3:"apr";s:5:"23.73";s:11:"installment";s:6:"126.74";s:8:"maturity";s:1:"3";s:17:"pricing_scheme_id";s:4:"1695";}s:1:"6";a:9:{s:11:"installment";s:5:"183.5";s:8:"maturity";s:1:"2";s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 2м. с кредитна карта";s:15:"total_repayment";s:3:"367";s:3:"apr";s:1:"0";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";s:6:"792441";s:3:"nir";s:1:"0";s:17:"pricing_scheme_id";s:4:"2791";}s:10:"variant_id";i:4;s:1:"3";a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:8:"maturity";s:1:"9";s:15:"total_repayment";s:6:"406.64";s:3:"apr";s:5:"28.34";s:2:"id";s:6:"481443";s:11:"installment";s:5:"45.18";s:3:"nir";s:5:"25.22";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}s:13:"customer_data";a:11:{s:9:"post_code";s:4:"1000";s:10:"first_name";s:6:"Stoyan";s:9:"last_name";s:8:"Atanasov";s:7:"address";s:10:"sdasdsadas";s:4:"city";s:5:"sofia";s:12:"company_name";s:0:"";s:3:"egn";s:10:"9102288468";s:3:"eik";s:0:"";s:5:"email";s:26:"<EMAIL>";s:3:"mol";s:0:"";s:5:"phone";s:10:"0883555204";}s:11:"downpayment";s:1:"0";s:4:"loan";a:10:{s:3:"apr";s:5:"27.17";s:26:"correct_downpayment_amount";s:1:"0";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:21:"processing_fee_amount";s:1:"0";s:18:"installment_amount";s:5:"65.57";s:18:"pricing_variant_id";s:6:"481442";s:22:"total_repayment_amount";s:6:"393.42";}s:4:"name";s:15:"Stoyan Atanasov";s:9:"principal";s:3:"367";s:1:"2";a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";s:6:"481444";s:11:"installment";s:5:"34.99";s:8:"maturity";s:2:"12";s:3:"nir";s:4:"25.6";s:17:"pricing_scheme_id";s:4:"1695";s:3:"apr";s:5:"28.84";s:15:"total_repayment";s:6:"419.85";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}s:1:"4";a:9:{s:2:"id";s:6:"481442";s:11:"installment";s:5:"65.57";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:15:"total_repayment";s:6:"393.42";s:3:"apr";s:5:"27.17";s:26:"correct_downpayment_amount";s:1:"0";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}s:3:"pin";s:10:"9102288468";s:1:"0";a:9:{s:3:"nir";s:5:"25.73";s:17:"pricing_scheme_id";s:4:"1695";s:3:"apr";s:5:"28.99";s:2:"id";s:6:"481446";s:11:"installment";s:5:"28.87";s:15:"total_repayment";s:6:"433.06";s:26:"correct_downpayment_amount";s:1:"0";s:8:"maturity";s:2:"15";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}s:1:"7";a:9:{s:8:"maturity";s:1:"3";s:3:"nir";s:1:"0";s:26:"correct_downpayment_amount";s:1:"0";s:11:"installment";s:6:"122.33";s:17:"pricing_scheme_id";s:4:"2792";s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 3м. с кредитна карта";s:15:"total_repayment";s:3:"367";s:3:"apr";s:5:"-0.02";s:2:"id";s:6:"792443";}s:5:"email";s:26:"<EMAIL>";s:13:"good_type_ids";s:3:"353";s:5:"names";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:16:"selected_variant";a:9:{s:2:"id";s:6:"481442";s:8:"maturity";s:1:"6";s:15:"total_repayment";s:6:"393.42";s:3:"apr";s:5:"27.17";s:26:"correct_downpayment_amount";s:1:"0";s:11:"installment";s:5:"65.57";s:3:"nir";s:5:"24.28";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}}'
		WHERE parent_id = 301023


2025/06/23 21:43:53 /graphapi/internal/praktis/checkout/cart-utils/payments.go:577
[0.146ms] [rows:-]
		SELECT entity_id, additional_information
		FROM sales_flat_order_payment
2025/06/23 21:43:53 [SUCCESS] BNP: VERIFICATION PASSED - Payment data confirmed in database
2025/06/23 21:43:53 [SUCCESS] BNP: Order ID: 301023, Payment ID: 301022, Data Length: 4019 bytes
2025/06/23 21:43:53 [SUCCESS] BNP: Data Preview: a:20:{s:1:"1";a:9:{s:3:"apr";s:5:"28.87";s:26:"correct_downpayment_amount";s:1:"0";s:11:"installment...
		WHERE parent_id = 301023


2025/06/23 21:43:53 /graphapi/internal/praktis/checkout/cart-utils/payments.go:599
[1.147ms] [rows:1] DELETE FROM `bnp_payment_data_storage` WHERE quote_id = 1144966
2025/06/23 21:43:53 [INFO] BNP: Backup data cleaned up for quote 1144966
2025/06/23 21:43:53 [INFO] BNP: Payment data successfully transferred to order 120303012
2025/06/23 21:43:53 [DEBUG] BNP: Reloading order 120303012 to get updated payment information

2025/06/23 21:43:53 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.191ms] [rows:1] SELECT * FROM `sales_flat_order_payment` WHERE `sales_flat_order_payment`.`parent_id` = 301023

2025/06/23 21:43:53 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.568ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '120303012' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
2025/06/23 21:43:53 [DEBUG] BNP: Order 120303012 reloaded successfully
2025/06/23 21:43:53 [DEBUG] BNP: Payment already loaded by preload - ID: 301022, Method: stenik_leasingjetcredit
2025/06/23 21:43:53 [INFO] Post-order processing started for order 120303012
2025/06/23 21:43:53 [DEBUG] BNP: Order details - ID: 301023, IncrementID: 120303012
2025/06/23 21:43:53 [DEBUG] BNP: Order payment found - ID: 301022, Method: stenik_leasingjetcredit
2025/06/23 21:43:53 [INFO] Order 120303012 uses BNP payment method, processing BNP data transfer and application
2025/06/23 21:43:53 [INFO] BNP: === POST-ORDER PAYMENT DATA VERIFICATION ===
2025/06/23 21:43:53 [INFO] BNP: Order: 120303012, Order ID: 301023, Payment ID: 301022

2025/06/23 21:43:53 /graphapi/internal/praktis/checkout/order-utils/bnp_post_processing.go:51
[0.091ms] [rows:-]
		SELECT entity_id, additional_information
		FROM sales_flat_order_payment
		WHERE parent_id = 301023

2025/06/23 21:43:53 [SUCCESS] BNP: Payment data verified in database
2025/06/23 21:43:53 [SUCCESS] BNP: Order: 120303012, Payment ID: 301022, Data Length: 4019 bytes
2025/06/23 21:43:53 [SUCCESS] BNP: Data Preview: a:20:{s:1:"1";a:9:{s:3:"apr";s:5:"28.87";s:26:"correct_downpayment_amount";s:1:"0";s:11:"installment...
2025/06/23 21:43:53 [WARNING] BNP: Failed to parse payment data JSON: invalid character 'a' looking for beginning of value
2025/06/23 21:43:53 [SUCCESS] BNP: Database verification PASSED
2025/06/23 21:43:53 [SUCCESS] BNP: Admin panel will be able to display BNP applicant information
2025/06/23 21:43:53 [INFO] BNP: === APPLICATION SUBMISSION START ===
2025/06/23 21:43:53 [INFO] BNP: Order Number: 120303012
2025/06/23 21:43:53 [INFO] BNP: Timestamp: 2025-06-23T21:43:53Z
2025/06/23 21:43:53 [DEBUG] BNP: Loading order from database...

2025/06/23 21:43:53 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.166ms] [rows:1] SELECT * FROM `sales_flat_order_payment` WHERE `sales_flat_order_payment`.`parent_id` = 301023

2025/06/23 21:43:53 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.463ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '120303012' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
2025/06/23 21:43:53 [INFO] BNP: Order loaded successfully - ID: 301023, Status: pending, Total: 367.00
2025/06/23 21:43:53 [DEBUG] BNP: Payment already loaded by preload - ID: 301022, Method: stenik_leasingjetcredit
2025/06/23 21:43:53 [DEBUG] BNP: Validating payment method...
2025/06/23 21:43:53 [DEBUG] BNP: Order payment method: stenik_leasingjetcredit
2025/06/23 21:43:53 [INFO] BNP: Payment method validation successful
2025/06/23 21:43:53 [DEBUG] BNP: Loading payment additional information...
2025/06/23 21:43:53 [DEBUG] BNP: Searching for quote payment with quote_id: 1144966

2025/06/23 21:43:53 /graphapi/internal/praktis/credit-calculators/bnp-application.go:202
[0.124ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144966 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1
2025/06/23 21:43:53 [DEBUG] BNP: Found quote payment with additional information (3891 bytes)
2025/06/23 21:43:53 [WARNING] BNP: Failed to parse payment additional information: invalid character 'a' looking for beginning of value
2025/06/23 21:43:53 [DEBUG] BNP: Raw additional information: a:20:{s:9:"principal";s:3:"367";i:2;a:9:{s:11:"installment";s:5:"34.99";s:3:"nir";s:4:"25.6";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481444;s:3:"apr";s:5:"28.84";s:15:"total_repayment";s:6:"419.85";s:8:"maturity";i:12;s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}i:6;a:9:{s:17:"pricing_scheme_id";i:2791;s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 2м. с кредитна карта";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:792441;s:8:"maturity";i:2;s:3:"nir";s:1:"0";s:3:"apr";s:1:"0";s:11:"installment";s:5:"183.5";s:15:"total_repayment";s:3:"367";}s:16:"selected_variant";a:9:{s:2:"id";s:6:"481442";s:3:"apr";s:5:"27.17";s:11:"installment";s:5:"65.57";s:15:"total_repayment";s:6:"393.42";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";}s:11:"downpayment";s:1:"0";s:3:"pin";s:10:"9102288468";s:5:"email";s:26:"<EMAIL>";s:10:"variant_id";i:4;s:5:"phone";s:10:"0883555204";i:0;a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481446;s:15:"total_repayment";s:6:"433.06";s:8:"maturity";i:15;s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:3:"apr";s:5:"28.99";s:11:"installment";s:5:"28.87";s:3:"nir";s:5:"25.73";}i:1;a:9:{s:26:"correct_downpayment_amount";s:1:"0";s:15:"total_repayment";s:6:"424.25";s:8:"maturity";i:13;s:11:"installment";s:5:"32.63";s:3:"nir";s:5:"25.66";s:17:"pricing_scheme_id";i:1695;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:2:"id";i:481445;s:3:"apr";s:5:"28.87";}i:7;a:9:{s:2:"id";i:792443;s:11:"installment";s:6:"122.33";s:3:"apr";s:5:"-0.02";s:15:"total_repayment";s:3:"367";s:8:"maturity";i:3;s:3:"nir";s:1:"0";s:17:"pricing_scheme_id";i:2792;s:19:"pricing_scheme_name";s:86:"Вземи сега, плати после:0% за 3м. с кредитна карта";s:26:"correct_downpayment_amount";s:1:"0";}s:4:"loan";a:10:{s:3:"apr";s:5:"27.17";s:26:"correct_downpayment_amount";s:1:"0";s:18:"installment_amount";s:5:"65.57";s:8:"maturity";s:1:"6";s:3:"nir";s:5:"24.28";s:21:"processing_fee_amount";s:1:"0";s:22:"total_repayment_amount";s:6:"393.42";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:18:"pricing_variant_id";s:6:"481442";}s:5:"names";s:15:"Stoyan Atanasov";s:4:"name";s:15:"Stoyan Atanasov";i:4;a:9:{s:11:"installment";s:5:"65.57";s:3:"nir";s:5:"24.28";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:2:"id";i:481442;s:3:"apr";s:5:"27.17";s:15:"total_repayment";s:6:"393.42";s:8:"maturity";i:6;s:17:"pricing_scheme_id";i:1695;s:26:"correct_downpayment_amount";s:1:"0";}i:5;a:9:{s:17:"pricing_scheme_id";i:1695;s:2:"id";i:481441;s:3:"apr";s:5:"23.73";s:11:"installment";s:6:"126.74";s:3:"nir";s:5:"21.47";s:15:"total_repayment";s:6:"380.21";s:8:"maturity";i:3;s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";}s:13:"customer_data";a:11:{s:3:"egn";s:10:"9102288468";s:9:"last_name";s:8:"Atanasov";s:9:"post_code";s:4:"1000";s:5:"email";s:26:"<EMAIL>";s:7:"address";s:10:"sdasdsadas";s:4:"city";s:5:"sofia";s:12:"company_name";s:0:"";s:3:"eik";s:0:"";s:3:"mol";s:0:"";s:10:"first_name";s:6:"Stoyan";s:5:"phone";s:10:"0883555204";}s:13:"good_type_ids";s:3:"353";i:3;a:9:{s:15:"total_repayment";s:6:"406.64";s:17:"pricing_scheme_id";i:1695;s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";i:481443;s:3:"apr";s:5:"28.34";s:11:"installment";s:5:"45.18";s:8:"maturity";i:9;s:3:"nir";s:5:"25.22";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";}}
2025/06/23 21:43:53 [DEBUG] BNP: Preparing application data...

2025/06/23 21:43:53 /graphapi/internal/praktis/credit-calculators/bnp-application.go:296
[0.124ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144966 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:43:53 /graphapi/internal/praktis/credit-calculators/bnp-application.go:329
[0.102ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144966 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:43:53 /graphapi/internal/praktis/credit-calculators/bnp-application.go:363
[0.089ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144966 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:43:53 /graphapi/internal/praktis/credit-calculators/bnp-application.go:393
[0.078ms] [rows:-]
			SELECT additional_information
			FROM sales_flat_order_payment
			WHERE parent_id = 301023


2025/06/23 21:43:53 /graphapi/internal/praktis/credit-calculators/bnp-application.go:425
[0.071ms] [rows:-]
			SELECT additional_information
			FROM sales_flat_order_payment
			WHERE parent_id = 301023


2025/06/23 21:43:53 /graphapi/internal/praktis/credit-calculators/bnp-application.go:453
[0.172ms] [rows:1] SELECT * FROM `sales_flat_order_item` WHERE order_id = 301023

2025/06/23 21:43:53 /graphapi/internal/praktis/credit-calculators/bnp-application.go:464
[0.132ms] [rows:1] SELECT * FROM `sales_flat_order_item` WHERE order_id = 301023
2025/06/23 21:43:53 [INFO] BNP: Application data prepared - Customer: Stoyan Atanasov, Phone: , EGN: 9102288468, Subtotal: 367.00 BGN
2025/06/23 21:43:53 [DEBUG] BNP: Customer Address: sdasdsadas
2025/06/23 21:43:53 [DEBUG] BNP: Order items: 3492751
2025/06/23 21:43:53 [DEBUG] BNP: Getting email configuration...
2025/06/23 21:43:53 [INFO] BNP: Application will be sent to: <EMAIL>
2025/06/23 21:43:53 [DEBUG] BNP: Email sending temporarily disabled for testing
2025/06/23 21:43:53 [INFO] BNP: Would send application <NAME_EMAIL> for order 120303012
2025/06/23 21:43:53 [INFO] BNP: Application email sending skipped (disabled for testing)
2025/06/23 21:43:53 [INFO] BNP: === APPLICATION SUBMISSION COMPLETED ===
2025/06/23 21:43:53 [INFO] BNP: Order: 120303012, Duration: 1.473259ms, Status: SUCCESS
2025/06/23 21:43:53 [INFO] BNP application submitted successfully for order 120303012
[GIN] 2025/06/23 - 21:43:53 | 200 |  450.184653ms |       ********* | POST     "/graphql"
2025/06/23 21:43:53 [DEBUG] BNP: Starting BNP Calculator API initialization
2025/06/23 21:43:53 [DEBUG] BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
2025/06/23 21:43:53 [DEBUG] BNP: Using merchant ID: 433147
2025/06/23 21:43:53 [INFO] BNP: Using merchant ID: 433147
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:53 [DEBUG] BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
2025/06/23 21:43:53 [DEBUG] BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
2025/06/23 21:43:53 [INFO] BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:53 [INFO] BNP: Operating in mode: PRODUCTION
2025/06/23 21:43:53 [DEBUG] BNP: Starting TLS configuration setup
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:53 [DEBUG] BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
2025/06/23 21:43:53 [DEBUG] BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
2025/06/23 21:43:53 [INFO] BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:43:53 [DEBUG] BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:53 [DEBUG] BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
2025/06/23 21:43:53 [DEBUG] BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
2025/06/23 21:43:53 [INFO] BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:43:53 [DEBUG] BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:43:53 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:43:53 [DEBUG] BNP: No key password configured (as expected)
2025/06/23 21:43:53 [DEBUG] BNP: Loading X509 certificate/key pair
2025/06/23 21:43:53 [INFO] BNP: Successfully loaded X509 certificate/key pair
2025/06/23 21:43:53 [INFO] BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
2025/06/23 21:43:53 [DEBUG] BNP: Certificate count in TLS config: 1
2025/06/23 21:43:53 [INFO] BNP: TLS configuration loaded successfully
2025/06/23 21:43:53 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:53 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:43:53 [INFO] BNP: Timestamp: 2025-06-23T21:43:53Z
2025/06/23 21:43:53 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:43:53 [DEBUG] BNP: principal: 367
2025/06/23 21:43:53 [DEBUG] BNP: downPayment: 0
2025/06/23 21:43:53 [DEBUG] BNP: method: GetAvailablePricingSchemes
2025/06/23 21:43:53 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:43:53 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingSchemes
2025/06/23 21:43:53 [DEBUG] BNP: URL Parameters: [433147 353 367.00 0.00]
2025/06/23 21:43:53 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/367.00/0.00
2025/06/23 21:43:53 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:43:53 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/23 21:43:53 [INFO] BNP: Timestamp: 2025-06-23T21:43:53Z
2025/06/23 21:43:53 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/367.00/0.00
2025/06/23 21:43:53 [INFO] BNP: HTTP Method: GET
2025/06/23 21:43:53 [DEBUG] BNP: Request Headers:
2025/06/23 21:43:53 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:43:53 [DEBUG] BNP: Request Parameters:
2025/06/23 21:43:53 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:43:53 [DEBUG] BNP:   API Method: GetAvailablePricingSchemes
2025/06/23 21:43:53 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:43:53 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:43:53 [DEBUG] BNP:   Principal: 367.00
2025/06/23 21:43:53 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:43:53 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingSchemes/433147/353/367.00/0.00 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

[GIN] 2025/06/23 - 21:43:53 | 200 |    1.498375ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:53 | 200 |       57.61µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:53 | 200 |     319.987µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:53 | 200 |     285.977µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:53 | 200 |     403.886µs |       ********* | POST     "/graphql"
2025/06/23 21:43:53 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:43:53 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/23 21:43:53 [INFO] BNP: Timestamp: 2025-06-23T21:43:53Z
2025/06/23 21:43:53 [INFO] BNP: Duration: 199.876984ms
2025/06/23 21:43:53 [INFO] BNP: Status Code: 200
2025/06/23 21:43:53 [INFO] BNP: Status: 200 OK
2025/06/23 21:43:53 [DEBUG] BNP: Response Headers:
2025/06/23 21:43:53 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:43:53 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:43:53 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:43:53 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:43:53 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:43:52 GMT
2025/06/23 21:43:53 [DEBUG] BNP:   Content-Length: 675
2025/06/23 21:43:53 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:43:53 [DEBUG] BNP: Response Body Length: 675 bytes
2025/06/23 21:43:53 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingScheme><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName></PricingScheme></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:43:53 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:43:53 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:53 [INFO] BNP: Operation: Response Validation
2025/06/23 21:43:53 [INFO] BNP: Timestamp: 2025-06-23T21:43:53Z
2025/06/23 21:43:53 [DEBUG] BNP: errorCode: 0
2025/06/23 21:43:53 [DEBUG] BNP: errorMessage:
2025/06/23 21:43:53 [DEBUG] BNP: schemeCount: 3
2025/06/23 21:43:53 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:43:53 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:43:53 [INFO] BNP: Timestamp: 2025-06-23T21:43:53Z
2025/06/23 21:43:53 [DEBUG] BNP: inputSchemes: 3
2025/06/23 21:43:53 [DEBUG] BNP: outputSchemes: 3
2025/06/23 21:43:53 [DEBUG] BNP: success: true
2025/06/23 21:43:53 [INFO] BNP: GetAvailablePricingSchemes completed successfully in 199.876984ms, returned 3 schemes
[GIN] 2025/06/23 - 21:43:53 | 200 |  200.950077ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |    1.466981ms |       ********* | POST     "/graphql"

2025/06/23 21:43:57 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[1.266ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1

2025/06/23 21:43:57 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[0.416ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1
[GIN] 2025/06/23 - 21:43:57 | 200 |    1.731191ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |    2.311008ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     230.643µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     267.286µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     271.821µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     329.701µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |      72.736µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     174.815µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     250.486µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |       100.6µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     171.051µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     272.268µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     371.273µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |      82.136µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |       37.01µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:43:57 | 200 |     398.066µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |    2.538375ms |       ********* | POST     "/graphql"

2025/06/23 21:44:11 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[3.564ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1

2025/06/23 21:44:11 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[2.866ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1
[GIN] 2025/06/23 - 21:44:11 | 200 |    4.105279ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |    4.632451ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |     183.792µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |     242.688µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |     335.652µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |     370.915µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |      58.039µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |        56.2µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |     314.643µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |      250.49µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |     176.447µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |      259.95µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |     344.225µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |      90.561µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |      41.955µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:11 | 200 |     239.233µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     1.95358ms |       ********* | POST     "/graphql"

2025/06/23 21:44:14 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[2.933ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1

2025/06/23 21:44:14 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[3.007ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1
[GIN] 2025/06/23 - 21:44:14 | 200 |    3.624652ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |    4.168012ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     322.021µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     293.176µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     278.362µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     256.362µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |       58.68µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |      30.278µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     455.684µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     331.889µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     291.982µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     268.802µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     283.465µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |      58.452µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |      47.383µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:44:14 | 200 |     341.168µs |       ********* | POST     "/graphql"
