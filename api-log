2025/06/23 21:17:06 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[1.708ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE uuid = '32d547dd-9f19-41d1-928a-316aea542f33' ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1
2025/06/23 21:17:06 [INFO] BNP: === PAYMENT DATA VALIDATION START ===
2025/06/23 21:17:06 [DEBUG] BNP: Validating BNP payment data...
2025/06/23 21:17:06 [DEBUG] BNP: Validating payment parameters...
2025/06/23 21:17:06 [DEBUG] BNP: Payment parameters valid - DownPayment: 0.00, VariantID: 481435
2025/06/23 21:17:06 [DEBUG] BNP: Validating customer data...
2025/06/23 21:17:06 [DEBUG] BNP: Customer data valid - Name: <PERSON><PERSON><PERSON>, Email: <EMAIL>, Phone: 0883555204
2025/06/23 21:17:06 [INFO] BNP: === PAYMENT DATA VALIDATION COMPLETED ===
2025/06/23 21:17:06 [INFO] BNP: Status: SUCCESS - All validation checks passed
2025/06/23 21:17:06 [INFO] BNP: === PAYMENT DATA UPDATE START ===
2025/06/23 21:17:06 [INFO] BNP: Quote ID: 1144963
2025/06/23 21:17:06 [INFO] BNP: Variant ID: 481435, Down Payment: 0.00
2025/06/23 21:17:06 [DEBUG] BNP: Updating payment method to BNP Paribas...
2025/06/23 21:17:06 [DEBUG] UpdateQuotePaymentMethod: Processing payment ID 1838709, current method: stenik_leasingjetcredit, target method: stenik_leasingjetcredit
2025/06/23 21:17:06 [DEBUG] UpdateQuotePaymentMethod: Payment method already correct for payment ID 1838709, skipping update
2025/06/23 21:17:06 [INFO] BNP: Payment method updated successfully
2025/06/23 21:17:06 [INFO] BNP: Calculated principal from cart subtotal: 221.00
2025/06/23 21:17:06 [INFO] BNP: === DETERMINING GOOD TYPE IDS FROM CART ===
2025/06/23 21:17:06 [INFO] BNP: Analyzing 1 cart items for good type IDs

2025/06/23 21:17:06 /graphapi/packages/magento-core/mage-store/sales/quote-item.go:67
[0.598ms] [rows:1] SELECT * FROM `sales_flat_quote_item` WHERE quote_id = 1144963
2025/06/23 21:17:06 [DEBUG] BNP: Processing cart item 1: SKU=360242, Qty=1.00, Price=221.00
2025/06/23 21:17:06 [INFO] BNP: Using good type IDs '353' from product 360242 (item 1)
2025/06/23 21:17:06 [INFO] BNP: Successfully determined good type IDs: 353
2025/06/23 21:17:06 [INFO] BNP: Determined good type IDs: 353
2025/06/23 21:17:06 [DEBUG] BNP: Calculating loan details...
2025/06/23 21:17:06 [DEBUG] BNP: Loan parameters - GoodTypeIds: 353, Principal: 221.00, DownPayment: 0.00, VariantID: 481435
2025/06/23 21:17:06 [DEBUG] BNP: Starting BNP Calculator API initialization
2025/06/23 21:17:06 [DEBUG] BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
2025/06/23 21:17:06 [DEBUG] BNP: Using merchant ID: 433147
2025/06/23 21:17:06 [INFO] BNP: Using merchant ID: 433147
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [DEBUG] BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
2025/06/23 21:17:06 [DEBUG] BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
2025/06/23 21:17:06 [INFO] BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [INFO] BNP: Operating in mode: PRODUCTION
2025/06/23 21:17:06 [DEBUG] BNP: Starting TLS configuration setup
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [DEBUG] BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
2025/06/23 21:17:06 [DEBUG] BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
2025/06/23 21:17:06 [INFO] BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:17:06 [DEBUG] BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [DEBUG] BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
2025/06/23 21:17:06 [DEBUG] BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
2025/06/23 21:17:06 [INFO] BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:17:06 [DEBUG] BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [DEBUG] BNP: No key password configured (as expected)
2025/06/23 21:17:06 [DEBUG] BNP: Loading X509 certificate/key pair
2025/06/23 21:17:06 [INFO] BNP: Successfully loaded X509 certificate/key pair
2025/06/23 21:17:06 [INFO] BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
2025/06/23 21:17:06 [DEBUG] BNP: Certificate count in TLS config: 1
2025/06/23 21:17:06 [INFO] BNP: TLS configuration loaded successfully
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: method: CalculateLoan
2025/06/23 21:17:06 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:17:06 [DEBUG] BNP: principal: 221
2025/06/23 21:17:06 [DEBUG] BNP: downPayment: 0
2025/06/23 21:17:06 [DEBUG] BNP: pricingVariantId: 481435
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: CalculateLoan
2025/06/23 21:17:06 [DEBUG] BNP: URL Parameters: [433147 353 221.00 0.00 481435]
2025/06/23 21:17:06 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/CalculateLoan/433147/353/221.00/0.00/481435
2025/06/23 21:17:06 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:17:06 [INFO] BNP: Method: CalculateLoan
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/CalculateLoan/433147/353/221.00/0.00/481435
2025/06/23 21:17:06 [INFO] BNP: HTTP Method: GET
2025/06/23 21:17:06 [DEBUG] BNP: Request Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:17:06 [DEBUG] BNP: Request Parameters:
2025/06/23 21:17:06 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:17:06 [DEBUG] BNP:   API Method: CalculateLoan
2025/06/23 21:17:06 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:17:06 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:17:06 [DEBUG] BNP:   Principal: 221.00
2025/06/23 21:17:06 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:17:06 [DEBUG] BNP:   Additional Param[6]: 481435
2025/06/23 21:17:06 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/CalculateLoan/433147/353/221.00/0.00/481435 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:17:06 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:17:06 [INFO] BNP: Method: CalculateLoan
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: Duration: 200.447492ms
2025/06/23 21:17:06 [INFO] BNP: Status Code: 200
2025/06/23 21:17:06 [INFO] BNP: Status: 200 OK
2025/06/23 21:17:06 [DEBUG] BNP: Response Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:17:06 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:17:05 GMT
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Length: 583
2025/06/23 21:17:06 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:17:06 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:17:06 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Length: 583 bytes
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><CreditProposition><APR>23.73</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>76.32</InstallmentAmount><Maturity>3</Maturity><NIR>21.47</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481435</PricingVariantId><ProcessingFeeAmount>0</ProcessingFeeAmount><TotalRepaymentAmount>228.96</TotalRepaymentAmount></CreditProposition></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:17:06 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Response Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: errorCode: 0
2025/06/23 21:17:06 [DEBUG] BNP: errorMessage:
2025/06/23 21:17:06 [DEBUG] BNP: hasLoanData: true
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: success: true
2025/06/23 21:17:06 [DEBUG] BNP: apr: 23.73
2025/06/23 21:17:06 [DEBUG] BNP: installmentAmount: 76.32
2025/06/23 21:17:06 [DEBUG] BNP: maturity: 3
2025/06/23 21:17:06 [DEBUG] BNP: totalRepaymentAmount: 228.96
2025/06/23 21:17:06 [DEBUG] BNP: processingFeeAmount: 0
2025/06/23 21:17:06 [INFO] BNP: CalculateLoan completed successfully in 200.447492ms for variant 481435
2025/06/23 21:17:06 [INFO] BNP: Loan Details - APR: 23.73, Installment: 76.32, Maturity: 3 months, Total: 228.96
2025/06/23 21:17:06 [INFO] BNP: Loan calculation successful - APR: 23.73, Installment: 76.32, Total: 228.96
2025/06/23 21:17:06 [DEBUG] BNP: Preparing payment additional information...
2025/06/23 21:17:06 [DEBUG] BNP: Customer data - Name: Stoyan Atanasov, Email: <EMAIL>, Phone: 0883555204
2025/06/23 21:17:06 [INFO] BNP: Determining array index for variant 481435 by calling Magento API
2025/06/23 21:17:06 [INFO] BNP: Calling BNP variants API with downPayment=0.00, goodTypeIds=353, principal=221.00
2025/06/23 21:17:06 [INFO] BNP: Starting GetAvailableVariantsGroupedByPricingScheme with goodTypeIds='353', principal=221.00, downPayment=0.00
2025/06/23 21:17:06 [DEBUG] BNP: Starting BNP Calculator API initialization
2025/06/23 21:17:06 [DEBUG] BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
2025/06/23 21:17:06 [DEBUG] BNP: Using merchant ID: 433147
2025/06/23 21:17:06 [INFO] BNP: Using merchant ID: 433147
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [DEBUG] BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
2025/06/23 21:17:06 [DEBUG] BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
2025/06/23 21:17:06 [INFO] BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [INFO] BNP: Operating in mode: PRODUCTION
2025/06/23 21:17:06 [DEBUG] BNP: Starting TLS configuration setup
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [DEBUG] BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
2025/06/23 21:17:06 [DEBUG] BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
2025/06/23 21:17:06 [INFO] BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:17:06 [DEBUG] BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [DEBUG] BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
2025/06/23 21:17:06 [DEBUG] BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
2025/06/23 21:17:06 [INFO] BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:17:06 [DEBUG] BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:06 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:06 [DEBUG] BNP: No key password configured (as expected)
2025/06/23 21:17:06 [DEBUG] BNP: Loading X509 certificate/key pair
2025/06/23 21:17:06 [INFO] BNP: Successfully loaded X509 certificate/key pair
2025/06/23 21:17:06 [INFO] BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
2025/06/23 21:17:06 [DEBUG] BNP: Certificate count in TLS config: 1
2025/06/23 21:17:06 [INFO] BNP: TLS configuration loaded successfully
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: method: GetAvailablePricingSchemes
2025/06/23 21:17:06 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:17:06 [DEBUG] BNP: principal: 221
2025/06/23 21:17:06 [DEBUG] BNP: downPayment: 0
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingSchemes
2025/06/23 21:17:06 [DEBUG] BNP: URL Parameters: [433147 353 221.00 0.00]
2025/06/23 21:17:06 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/221.00/0.00
2025/06/23 21:17:06 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:17:06 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/221.00/0.00
2025/06/23 21:17:06 [INFO] BNP: HTTP Method: GET
2025/06/23 21:17:06 [DEBUG] BNP: Request Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:17:06 [DEBUG] BNP: Request Parameters:
2025/06/23 21:17:06 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:17:06 [DEBUG] BNP:   API Method: GetAvailablePricingSchemes
2025/06/23 21:17:06 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:17:06 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:17:06 [DEBUG] BNP:   Principal: 221.00
2025/06/23 21:17:06 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:17:06 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingSchemes/433147/353/221.00/0.00 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:17:06 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:17:06 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: Duration: 197.781104ms
2025/06/23 21:17:06 [INFO] BNP: Status Code: 200
2025/06/23 21:17:06 [INFO] BNP: Status: 200 OK
2025/06/23 21:17:06 [DEBUG] BNP: Response Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:17:06 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:17:06 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:17:06 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:17:06 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:17:05 GMT
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Length: 675
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Length: 675 bytes
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingScheme><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName></PricingScheme></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:17:06 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Response Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: errorCode: 0
2025/06/23 21:17:06 [DEBUG] BNP: errorMessage:
2025/06/23 21:17:06 [DEBUG] BNP: schemeCount: 3
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: inputSchemes: 3
2025/06/23 21:17:06 [DEBUG] BNP: outputSchemes: 3
2025/06/23 21:17:06 [DEBUG] BNP: success: true
2025/06/23 21:17:06 [INFO] BNP: GetAvailablePricingSchemes completed successfully in 197.781104ms, returned 3 schemes
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:17:06 [DEBUG] BNP: principal: 221
2025/06/23 21:17:06 [DEBUG] BNP: downPayment: 0
2025/06/23 21:17:06 [DEBUG] BNP: instalment: 0
2025/06/23 21:17:06 [DEBUG] BNP: schemeId: 1695
2025/06/23 21:17:06 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP: URL Parameters: [433147 353 221.00 0.00 0.00 1695]
2025/06/23 21:17:06 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/1695
2025/06/23 21:17:06 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:17:06 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/1695
2025/06/23 21:17:06 [INFO] BNP: HTTP Method: GET
2025/06/23 21:17:06 [DEBUG] BNP: Request Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:17:06 [DEBUG] BNP: Request Parameters:
2025/06/23 21:17:06 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:17:06 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:17:06 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:17:06 [DEBUG] BNP:   Principal: 221.00
2025/06/23 21:17:06 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:17:06 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/23 21:17:06 [DEBUG] BNP:   Additional Param[7]: 1695
2025/06/23 21:17:06 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/1695 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:17:06 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:17:06 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: Duration: 47.696513ms
2025/06/23 21:17:06 [INFO] BNP: Status Code: 200
2025/06/23 21:17:06 [INFO] BNP: Status: 200 OK
2025/06/23 21:17:06 [DEBUG] BNP: Response Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:17:06 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:17:06 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:17:05 GMT
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Length: 930
2025/06/23 21:17:06 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:17:06 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Length: 930 bytes
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>27.11</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>39.48</InstallmentAmount><Maturity>6</Maturity><NIR>24.28</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481436</PricingVariantId><TotalRepaymentAmount>236.91</TotalRepaymentAmount></PricingVariant><PricingVariant><APR>23.73</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>76.32</InstallmentAmount><Maturity>3</Maturity><NIR>21.47</NIR><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName><PricingVariantId>481435</PricingVariantId><TotalRepaymentAmount>228.96</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:17:06 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Response Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: errorCode: 0
2025/06/23 21:17:06 [DEBUG] BNP: errorMessage:
2025/06/23 21:17:06 [DEBUG] BNP: variantCount: 2
2025/06/23 21:17:06 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 481436, defaulting to '0'
2025/06/23 21:17:06 [DEBUG] BNP: Parsed variant ID=481436, APR=27.11, Maturity=6, InstallmentAmount=39.48
2025/06/23 21:17:06 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 481435, defaulting to '0'
2025/06/23 21:17:06 [DEBUG] BNP: Parsed variant ID=481435, APR=23.73, Maturity=3, InstallmentAmount=76.32
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: inputVariants: 2
2025/06/23 21:17:06 [DEBUG] BNP: outputVariants: 2
2025/06/23 21:17:06 [DEBUG] BNP: schemeId: 1695
2025/06/23 21:17:06 [DEBUG] BNP: success: true
2025/06/23 21:17:06 [INFO] BNP: GetAvailablePricingVariants completed successfully in 47.696513ms, returned 2 variants for scheme 1695
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:17:06 [DEBUG] BNP: principal: 221
2025/06/23 21:17:06 [DEBUG] BNP: downPayment: 0
2025/06/23 21:17:06 [DEBUG] BNP: instalment: 0
2025/06/23 21:17:06 [DEBUG] BNP: schemeId: 2791
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP: URL Parameters: [433147 353 221.00 0.00 0.00 2791]
2025/06/23 21:17:06 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/2791
2025/06/23 21:17:06 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:17:06 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/2791
2025/06/23 21:17:06 [INFO] BNP: HTTP Method: GET
2025/06/23 21:17:06 [DEBUG] BNP: Request Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:17:06 [DEBUG] BNP: Request Parameters:
2025/06/23 21:17:06 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:17:06 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:17:06 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:17:06 [DEBUG] BNP:   Principal: 221.00
2025/06/23 21:17:06 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:17:06 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/23 21:17:06 [DEBUG] BNP:   Additional Param[7]: 2791
2025/06/23 21:17:06 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/2791 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:17:06 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:17:06 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: Duration: 49.607947ms
2025/06/23 21:17:06 [INFO] BNP: Status Code: 200
2025/06/23 21:17:06 [INFO] BNP: Status: 200 OK
2025/06/23 21:17:06 [DEBUG] BNP: Response Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:17:06 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:17:06 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:17:05 GMT
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Length: 572
2025/06/23 21:17:06 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:17:06 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Length: 572 bytes
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>0</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>110.50</InstallmentAmount><Maturity>2</Maturity><NIR>0</NIR><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName><PricingVariantId>792441</PricingVariantId><TotalRepaymentAmount>221.00</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:17:06 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Response Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: variantCount: 1
2025/06/23 21:17:06 [DEBUG] BNP: errorCode: 0
2025/06/23 21:17:06 [DEBUG] BNP: errorMessage:
2025/06/23 21:17:06 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 792441, defaulting to '0'
2025/06/23 21:17:06 [DEBUG] BNP: Parsed variant ID=792441, APR=0, Maturity=2, InstallmentAmount=110.50
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: inputVariants: 1
2025/06/23 21:17:06 [DEBUG] BNP: outputVariants: 1
2025/06/23 21:17:06 [DEBUG] BNP: schemeId: 2791
2025/06/23 21:17:06 [DEBUG] BNP: success: true
2025/06/23 21:17:06 [INFO] BNP: GetAvailablePricingVariants completed successfully in 49.607947ms, returned 1 variants for scheme 2791
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: downPayment: 0
2025/06/23 21:17:06 [DEBUG] BNP: instalment: 0
2025/06/23 21:17:06 [DEBUG] BNP: schemeId: 2792
2025/06/23 21:17:06 [DEBUG] BNP: method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:17:06 [DEBUG] BNP: principal: 221
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:06 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP: URL Parameters: [433147 353 221.00 0.00 0.00 2792]
2025/06/23 21:17:06 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/2792
2025/06/23 21:17:06 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:17:06 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/2792
2025/06/23 21:17:06 [INFO] BNP: HTTP Method: GET
2025/06/23 21:17:06 [DEBUG] BNP: Request Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:17:06 [DEBUG] BNP: Request Parameters:
2025/06/23 21:17:06 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:17:06 [DEBUG] BNP:   API Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:17:06 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:17:06 [DEBUG] BNP:   Principal: 221.00
2025/06/23 21:17:06 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:17:06 [DEBUG] BNP:   Additional Param[6]: 0.00
2025/06/23 21:17:06 [DEBUG] BNP:   Additional Param[7]: 2792
2025/06/23 21:17:06 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingVariants/433147/353/221.00/0.00/0.00/2792 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

2025/06/23 21:17:06 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:17:06 [INFO] BNP: Method: GetAvailablePricingVariants
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [INFO] BNP: Duration: 50.692265ms
2025/06/23 21:17:06 [INFO] BNP: Status Code: 200
2025/06/23 21:17:06 [INFO] BNP: Status: 200 OK
2025/06/23 21:17:06 [DEBUG] BNP: Response Headers:
2025/06/23 21:17:06 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:17:06 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:17:06 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:17:06 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:17:05 GMT
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Length: 574
2025/06/23 21:17:06 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:17:06 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Length: 574 bytes
2025/06/23 21:17:06 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingVariant><APR>0.03</APR><CorrectDownPaymentAmount>0</CorrectDownPaymentAmount><InstallmentAmount>73.67</InstallmentAmount><Maturity>3</Maturity><NIR>0</NIR><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName><PricingVariantId>792443</PricingVariantId><TotalRepaymentAmount>221.00</TotalRepaymentAmount></PricingVariant></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:17:06 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Response Validation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: errorCode: 0
2025/06/23 21:17:06 [DEBUG] BNP: errorMessage:
2025/06/23 21:17:06 [DEBUG] BNP: variantCount: 1
2025/06/23 21:17:06 [DEBUG] BNP: ProcessingFeeAmount was empty for variant 792443, defaulting to '0'
2025/06/23 21:17:06 [DEBUG] BNP: Parsed variant ID=792443, APR=0.03, Maturity=3, InstallmentAmount=73.67
2025/06/23 21:17:06 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:06 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:17:06 [INFO] BNP: Timestamp: 2025-06-23T21:17:06Z
2025/06/23 21:17:06 [DEBUG] BNP: schemeId: 2792
2025/06/23 21:17:06 [DEBUG] BNP: success: true
2025/06/23 21:17:06 [DEBUG] BNP: inputVariants: 1
2025/06/23 21:17:06 [DEBUG] BNP: outputVariants: 1
2025/06/23 21:17:06 [INFO] BNP: GetAvailablePricingVariants completed successfully in 50.692265ms, returned 1 variants for scheme 2792
2025/06/23 21:17:06 [DEBUG] BNP: BNP calculator returned 3 variant groups
2025/06/23 21:17:06 [DEBUG] BNP: Processing group 0 with scheme ID 1695 containing 2 variants
2025/06/23 21:17:06 [DEBUG] BNP: Variant 481436 (group 0, variant 0): 6 months, 27.11% APR, 39.48 BGN installment
2025/06/23 21:17:06 [DEBUG] BNP: Variant 481435 (group 0, variant 1): 3 months, 23.73% APR, 76.32 BGN installment
2025/06/23 21:17:06 [DEBUG] BNP: Processing group 1 with scheme ID 2791 containing 1 variants
2025/06/23 21:17:06 [DEBUG] BNP: Variant 792441 (group 1, variant 0): 2 months, 0.00% APR, 110.50 BGN installment
2025/06/23 21:17:06 [DEBUG] BNP: Processing group 2 with scheme ID 2792 containing 1 variants
2025/06/23 21:17:06 [DEBUG] BNP: Variant 792443 (group 2, variant 0): 3 months, 0.03% APR, 73.67 BGN installment
2025/06/23 21:17:06 [INFO] BNP: Successfully retrieved 4 total variants from BNP API
2025/06/23 21:17:06 [DEBUG] BNP: Retrieved 4 variants from Magento API
2025/06/23 21:17:06 [DEBUG] BNP: Found variant 481435 at array index 1
2025/06/23 21:17:06 [DEBUG] BNP: Dynamically determined array index 1 for variant ID 481435
2025/06/23 21:17:06 [DEBUG] BNP: Serializing payment data to PHP format for Magento compatibility...
2025/06/23 21:17:06 [DEBUG] BNP: Payment data serialized successfully (1361 bytes)
2025/06/23 21:17:06 [DEBUG] BNP: Updating quote payment records...
2025/06/23 21:17:06 [DEBUG] BNP: Using existing quote payment record
2025/06/23 21:17:06 [DEBUG] BNP: Updating payment record 1 (ID: 1838709)

2025/06/23 21:17:06 /graphapi/internal/praktis/checkout/cart-utils/payments.go:361
[6.837ms] [rows:1] UPDATE `sales_flat_quote_payment` SET `additional_information`='a:12:{s:4:"loan";a:10:{s:26:"correct_downpayment_amount";s:1:"0";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:22:"total_repayment_amount";s:6:"228.96";s:3:"apr";s:5:"23.73";s:18:"pricing_variant_id";s:6:"481435";s:21:"processing_fee_amount";s:1:"0";s:18:"installment_amount";s:5:"76.32";}s:13:"customer_data";a:11:{s:5:"email";s:26:"<EMAIL>";s:7:"address";s:11:"zh.k. Teres";s:4:"city";s:8:"Radinovo";s:12:"company_name";s:0:"";s:9:"last_name";s:8:"Atanasov";s:5:"phone";s:10:"0883555204";s:3:"egn";s:10:"9102288469";s:3:"eik";s:0:"";s:3:"mol";s:0:"";s:10:"first_name";s:6:"Stoyan";s:9:"post_code";s:4:"4202";}s:3:"pin";s:10:"9102288469";s:5:"email";s:26:"<EMAIL>";s:9:"principal";s:3:"221";s:10:"variant_id";i:1;s:11:"downpayment";s:1:"0";s:5:"names";s:15:"Stoyan Atanasov";s:4:"name";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:13:"good_type_ids";s:3:"353";s:16:"selected_variant";a:9:{s:15:"total_repayment";s:6:"228.96";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";s:6:"481435";s:11:"installment";s:5:"76.32";s:3:"apr";s:5:"23.73";s:17:"pricing_scheme_id";s:4:"1695";}}' WHERE `payment_id` = 1838709
2025/06/23 21:17:06 [DEBUG] BNP: Payment record 1838709 updated successfully
2025/06/23 21:17:06 [INFO] BNP: Storing persistent payment data for quote 1144963 (985 bytes)

2025/06/23 21:17:06 /graphapi/internal/praktis/checkout/cart-utils/payments.go:407
[1.701ms] [rows:0]
		CREATE TABLE IF NOT EXISTS bnp_payment_data_storage (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			quote_id bigint(20) NOT NULL,
			payment_data text NOT NULL,
			created_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
			updated_at timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			UNIQUE KEY unique_quote_id (quote_id),
			KEY idx_quote_id (quote_id)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci

2025/06/23 21:17:06 [INFO] BNP: Persistent payment data stored successfully in dedicated table
2025/06/23 21:17:06 [INFO] BNP: === PAYMENT DATA UPDATE COMPLETED ===
2025/06/23 21:17:06 [INFO] BNP: Quote: 1144963, Updated 1 payment records, Status: SUCCESS

2025/06/23 21:17:06 /graphapi/internal/praktis/checkout/cart-utils/payments.go:435
[2.596ms] [rows:1]
		INSERT INTO bnp_payment_data_storage (quote_id, payment_data, created_at, updated_at)
		VALUES (1144963, '{"customer_data":{"address":"zh.k. Teres","city":"Radinovo","company_name":"","egn":"9102288469","eik":"","email":"<EMAIL>","first_name":"Stoyan","last_name":"Atanasov","mol":"","phone":"0883555204","post_code":"4202"},"downpayment":0,"email":"<EMAIL>","good_type_ids":"353","loan":{"apr":"23.73","correct_downpayment_amount":"0","installment_amount":"76.32","maturity":"3","nir":"21.47","pricing_scheme_id":"1695","pricing_scheme_name":"1,2% месечно оскъпяване","pricing_variant_id":"481435","processing_fee_amount":"0","total_repayment_amount":"228.96"},"name":"Stoyan Atanasov","names":"Stoyan Atanasov","phone":"0883555204","pin":"9102288469","principal":221,"selected_variant":{"apr":"23.73","correct_downpayment_amount":"0","id":"481435","installment":"76.32","maturity":"3","nir":"21.47","pricing_scheme_id":"1695","pricing_scheme_name":"1,2% месечно оскъпяване","total_repayment":"228.96"},"variant_id":1}', NOW(), NOW())
		ON DUPLICATE KEY UPDATE
		payment_data = VALUES(payment_data),
		updated_at = NOW()

2025/06/23 21:17:06 [DEBUG] BNP: Immediate verification after save - Valid: true, Length: 1361

2025/06/23 21:17:06 /graphapi/internal/praktis/checkout/cart-utils/payments.go:384
[0.336ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144963
XDEBUG_SESSION=PHPSTORM
CallAPI->http://server/theme_api/sales_cart/calculateTotals took 274.487064ms

2025/06/23 21:17:07 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:84
[0.201ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144963

2025/06/23 21:17:07 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:84
[0.685ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE entity_id = 1144963 ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

2025/06/23 21:17:07 /graphapi/packages/magento-core/mage-store/sales/quote-item.go:67
[0.220ms] [rows:1] SELECT * FROM `sales_flat_quote_item` WHERE quote_id = 1144963

2025/06/23 21:17:07 /graphapi/packages/magento-core/mage-store/sales/quote.go:218
[0.290ms] [rows:2] SELECT * FROM `sales_flat_quote_address` WHERE `sales_flat_quote_address`.`quote_id` = 1144963
redis: nil

2025/06/23 21:17:07 /graphapi/packages/magento-core/mage-store/mage-product/product-image.go:101
[0.277ms] [rows:1] select value, label, position from catalog_product_entity_media_gallery g
    inner join catalog_product_entity_media_gallery_value go on g.value_id = go.value_id
where entity_id = 92500 and disabled = 0 and store_id in (0) order by position asc

2025/06/23 21:17:07 /graphapi/internal/praktis/catalog/product/product-video.go:50
[0.196ms] [rows:0]
		SELECT
			cpe.entity_id as product_id,
			iv.video_id,
			iv.title as video_title,
			iv.description as video_description,
			iv.url as video_url,
			iv.video_type,
			iv.video_store_view,
			iv.video_status,
			iv.image as video_thumbnail,
			ipv.video_position
		FROM catalog_product_entity cpe
			INNER JOIN iwd_product_video ipv ON cpe.entity_id = ipv.product_id
			INNER JOIN iwd_video iv ON ipv.video_id = iv.video_id
		WHERE cpe.entity_id = 92500
			AND iv.video_status = 1  -- Only active videos
		ORDER BY ipv.video_position


2025/06/23 21:17:07 /graphapi/internal/praktis/catalog/product/product-availability.go:38
[0.377ms] [rows:7] SELECT * FROM `stenik_zeron_warehouse_product` WHERE product_id = 92500

2025/06/23 21:17:07 /graphapi/internal/praktis/catalog/product/product-availability.go:104
[0.328ms] [rows:9] SELECT * FROM `theme_praktis_store` ORDER BY display_order ASC
Found 9 active stores
Store: ID=20, Name=PRAKTIS - София MEGA, WarehouseCode=1001
Store: ID=18, Name=PRAKTIS-Пловдив, WarehouseCode=901
Store: ID=13, Name=PRAKTIS - Хасково, WarehouseCode=601
Store: ID=12, Name=PRAKTIS - Велико Търново, WarehouseCode=501
Store: ID=10, Name=PRAKTIS - Видин, WarehouseCode=201
Store: ID=11, Name=PRAKTIS - Стара Загора, WarehouseCode=401
Store: ID=15, Name=PRAKTIS - Русе, WarehouseCode=801
Store: ID=7, Name=PRAKTIS - София Хаджи Димитър, WarehouseCode=101
Store: ID=19, Name=Мебели Търговски Център-Стара Загора, WarehouseCode=420

2025/06/23 21:17:07 /graphapi/internal/praktis/catalog/product/product-availability.go:151
[0.240ms] [rows:7] SELECT * FROM `stenik_zeron_warehouse_product` WHERE product_id in (92500)

2025/06/23 21:17:07 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.244ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '501' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:17:07 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.218ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '801' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:17:07 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.218ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '901' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:17:07 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.207ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '101' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:17:07 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.205ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:17:07 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.217ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '601' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:17:07 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.200ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '401' ORDER BY `theme_praktis_store`.`id` LIMIT 1
[GIN] 2025/06/23 - 21:17:07 | 200 |  847.396268ms |       ********* | POST     "/graphql"

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[0.457ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE `sales_flat_quote_payment`.`quote_id` = 1144963

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/quote-repository.go:187
[1.321ms] [rows:1] SELECT * FROM `sales_flat_quote` WHERE uuid = '32d547dd-9f19-41d1-928a-316aea542f33' ORDER BY `sales_flat_quote`.`entity_id` LIMIT 1

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/quote.go:218
[0.729ms] [rows:2] SELECT * FROM `sales_flat_quote_address` WHERE `sales_flat_quote_address`.`quote_id` = 1144963
2025/06/23 21:17:08 [INFO] BNP: Retrieving persistent payment data before order creation operations
2025/06/23 21:17:08 [INFO] BNP: Retrieving persistent payment data for quote 1144963
2025/06/23 21:17:08 [INFO] BNP: Retrieved persistent payment data (985 bytes) for quote 1144963
2025/06/23 21:17:08 [INFO] BNP: Persistent payment data retrieved successfully (985 bytes)

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/payments.go:456
[0.393ms] [rows:1] SELECT * FROM `bnp_payment_data_storage` WHERE quote_id = 1144963 ORDER BY `bnp_payment_data_storage`.`id` LIMIT 1

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:259
[6.775ms] [rows:1] UPDATE `sales_flat_quote` SET `customer_email`='<EMAIL>',`customer_firstname`='Stoyan',`customer_id`=0,`customer_is_guest`=0,`customer_lastname`='Atanasov',`remote_ip`='*************',`updated_at`='2025-06-23 21:17:08.217' WHERE `entity_id` = 1144963

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:275
[0.676ms] [rows:1] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-23 21:16:18',`updated_at`='2025-06-23 21:17:08.224',`address_type`='billing',`quote_id`=1144963,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`=NULL,`weight`=0,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=0,`base_grand_total`=0,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=NULL,`base_shipping_discount_amount`=NULL,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267132

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:275
[0.591ms] [rows:1] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-23 21:16:18',`updated_at`='2025-06-23 21:17:08.225',`address_type`='shipping',`quote_id`=1144963,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`='Вземи от обект - Вземи от обект',`weight`=37,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=221,`base_grand_total`=221,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=0,`base_shipping_discount_amount`=0,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267133
2025/06/23 21:17:08 [INFO] BNP: Restoring payment data for quote 1144963 (985 bytes)
2025/06/23 21:17:08 [DEBUG] BNP: Current payment data exists (1361 bytes), no restore needed

2025/06/23 21:17:08 /graphapi/graphql/resolver/cart-checkout.resolvers.go:506
[0.512ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144963

2025/06/23 21:17:08 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.401ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:47
[0.601ms] [rows:0] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-23 21:16:18',`updated_at`='2025-06-23 21:17:08.227',`address_type`='billing',`quote_id`=1144963,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`=NULL,`weight`=0,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=0,`base_grand_total`=0,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=NULL,`base_shipping_discount_amount`=NULL,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267132

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:47
[0.789ms] [rows:0] INSERT INTO `sales_flat_quote_address` (`created_at`,`updated_at`,`address_type`,`quote_id`,`customer_id`,`customer_address_id`,`email`,`prefix`,`firstname`,`middlename`,`lastname`,`street`,`city_id`,`store_code`,`office_code`,`city`,`region`,`region_id`,`postcode`,`country_id`,`telephone`,`same_as_billing`,`free_shipping`,`collect_shipping_rates`,`shipping_method`,`shipping_description`,`weight`,`shipping_amount`,`base_shipping_amount`,`discount_amount`,`base_discount_amount`,`grand_total`,`base_grand_total`,`customer_notes`,`discount_description`,`shipping_discount_amount`,`base_shipping_discount_amount`,`invoice`,`invoice_type`,`invoice_company_name`,`invoice_company_mol`,`invoice_company_city`,`invoice_company_address`,`invoice_company_bulstat`,`invoice_company_vat`,`invoice_personal_name`,`invoice_personal_pin`,`invoice_personal_city`,`invoice_personal_vat`,`invoice_personal_address`,`praktis_shipping_discount_amount`,`base_praktis_shipping_discount_amount`,`cash_on_delivery_tax_amount`,`base_cash_on_delivery_tax_amount`,`shipping_type`,`address_id`) VALUES ('2025-06-23 21:16:18','2025-06-23 21:17:08.227','billing',1144963,NULL,NULL,'<EMAIL>',NULL,'Stoyan',NULL,'Atanasov','(1001) бул. Ботевградско шосе 527',NULL,'1001',NULL,'гр. София',NULL,NULL,'1000','BG','0883555204',1,0,0,'stenik_siteshipping_from_store_stenik_siteshipping_from_store',NULL,0,0,0,0,0,0,0,NULL,NULL,NULL,NULL,'0','','','','','','','','','','','','',0,0,0,0,22,2267132) ON DUPLICATE KEY UPDATE `updated_at`='2025-06-23 21:17:08.227',`address_type`=VALUES(`address_type`),`quote_id`=VALUES(`quote_id`),`customer_id`=VALUES(`customer_id`),`customer_address_id`=VALUES(`customer_address_id`),`email`=VALUES(`email`),`prefix`=VALUES(`prefix`),`firstname`=VALUES(`firstname`),`middlename`=VALUES(`middlename`),`lastname`=VALUES(`lastname`),`street`=VALUES(`street`),`city_id`=VALUES(`city_id`),`store_code`=VALUES(`store_code`),`office_code`=VALUES(`office_code`),`city`=VALUES(`city`),`region`=VALUES(`region`),`region_id`=VALUES(`region_id`),`postcode`=VALUES(`postcode`),`country_id`=VALUES(`country_id`),`telephone`=VALUES(`telephone`),`same_as_billing`=VALUES(`same_as_billing`),`free_shipping`=VALUES(`free_shipping`),`collect_shipping_rates`=VALUES(`collect_shipping_rates`),`shipping_method`=VALUES(`shipping_method`),`shipping_description`=VALUES(`shipping_description`),`weight`=VALUES(`weight`),`shipping_amount`=VALUES(`shipping_amount`),`base_shipping_amount`=VALUES(`base_shipping_amount`),`discount_amount`=VALUES(`discount_amount`),`base_discount_amount`=VALUES(`base_discount_amount`),`grand_total`=VALUES(`grand_total`),`base_grand_total`=VALUES(`base_grand_total`),`customer_notes`=VALUES(`customer_notes`),`discount_description`=VALUES(`discount_description`),`shipping_discount_amount`=VALUES(`shipping_discount_amount`),`base_shipping_discount_amount`=VALUES(`base_shipping_discount_amount`),`invoice`=VALUES(`invoice`),`invoice_type`=VALUES(`invoice_type`),`invoice_company_name`=VALUES(`invoice_company_name`),`invoice_company_mol`=VALUES(`invoice_company_mol`),`invoice_company_city`=VALUES(`invoice_company_city`),`invoice_company_address`=VALUES(`invoice_company_address`),`invoice_company_bulstat`=VALUES(`invoice_company_bulstat`),`invoice_company_vat`=VALUES(`invoice_company_vat`),`invoice_personal_name`=VALUES(`invoice_personal_name`),`invoice_personal_pin`=VALUES(`invoice_personal_pin`),`invoice_personal_city`=VALUES(`invoice_personal_city`),`invoice_personal_vat`=VALUES(`invoice_personal_vat`),`invoice_personal_address`=VALUES(`invoice_personal_address`),`praktis_shipping_discount_amount`=VALUES(`praktis_shipping_discount_amount`),`base_praktis_shipping_discount_amount`=VALUES(`base_praktis_shipping_discount_amount`),`cash_on_delivery_tax_amount`=VALUES(`cash_on_delivery_tax_amount`),`base_cash_on_delivery_tax_amount`=VALUES(`base_cash_on_delivery_tax_amount`),`shipping_type`=VALUES(`shipping_type`)

2025/06/23 21:17:08 /graphapi/graphql/entity/praktis_store_getter.go:52
[0.269ms] [rows:1] SELECT * FROM `theme_praktis_store` WHERE warehouse_code = '1001' ORDER BY `theme_praktis_store`.`id` LIMIT 1

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:47
[0.520ms] [rows:0] UPDATE `sales_flat_quote_address` SET `created_at`='2025-06-23 21:16:18',`updated_at`='2025-06-23 21:17:08.228',`address_type`='shipping',`quote_id`=1144963,`customer_id`=NULL,`customer_address_id`=NULL,`email`='<EMAIL>',`prefix`=NULL,`firstname`='Stoyan',`middlename`=NULL,`lastname`='Atanasov',`street`='(1001) бул. Ботевградско шосе 527',`city_id`=NULL,`store_code`='1001',`office_code`=NULL,`city`='гр. София',`region`=NULL,`region_id`=NULL,`postcode`='1000',`country_id`='BG',`telephone`='0883555204',`same_as_billing`=1,`free_shipping`=0,`collect_shipping_rates`=0,`shipping_method`='stenik_siteshipping_from_store_stenik_siteshipping_from_store',`shipping_description`='Вземи от обект - Вземи от обект',`weight`=37,`shipping_amount`=0,`base_shipping_amount`=0,`discount_amount`=0,`base_discount_amount`=0,`grand_total`=221,`base_grand_total`=221,`customer_notes`=NULL,`discount_description`=NULL,`shipping_discount_amount`=0,`base_shipping_discount_amount`=0,`invoice`='0',`invoice_type`='',`invoice_company_name`='',`invoice_company_mol`='',`invoice_company_city`='',`invoice_company_address`='',`invoice_company_bulstat`='',`invoice_company_vat`='',`invoice_personal_name`='',`invoice_personal_pin`='',`invoice_personal_city`='',`invoice_personal_vat`='',`invoice_personal_address`='',`praktis_shipping_discount_amount`=0,`base_praktis_shipping_discount_amount`=0,`cash_on_delivery_tax_amount`=0,`base_cash_on_delivery_tax_amount`=0,`shipping_type`=22 WHERE `address_id` = 2267133

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/address-save.go:47
2025/06/23 21:17:08 [INFO] BNP: Restoring payment data for quote 1144963 (985 bytes)
[0.702ms] [rows:0] INSERT INTO `sales_flat_quote_address` (`created_at`,`updated_at`,`address_type`,`quote_id`,`customer_id`,`customer_address_id`,`email`,`prefix`,`firstname`,`middlename`,`lastname`,`street`,`city_id`,`store_code`,`office_code`,`city`,`region`,`region_id`,`postcode`,`country_id`,`telephone`,`same_as_billing`,`free_shipping`,`collect_shipping_rates`,`shipping_method`,`shipping_description`,`weight`,`shipping_amount`,`base_shipping_amount`,`discount_amount`,`base_discount_amount`,`grand_total`,`base_grand_total`,`customer_notes`,`discount_description`,`shipping_discount_amount`,`base_shipping_discount_amount`,`invoice`,`invoice_type`,`invoice_company_name`,`invoice_company_mol`,`invoice_company_city`,`invoice_company_address`,`invoice_company_bulstat`,`invoice_company_vat`,`invoice_personal_name`,`invoice_personal_pin`,`invoice_personal_city`,`invoice_personal_vat`,`invoice_personal_address`,`praktis_shipping_discount_amount`,`base_praktis_shipping_discount_amount`,`cash_on_delivery_tax_amount`,`base_cash_on_delivery_tax_amount`,`shipping_type`,`address_id`) VALUES ('2025-06-23 21:16:18','2025-06-23 21:17:08.228','shipping',1144963,NULL,NULL,'<EMAIL>',NULL,'Stoyan',NULL,'Atanasov','(1001) бул. Ботевградско шосе 527',NULL,'1001',NULL,'гр. София',NULL,NULL,'1000','BG','0883555204',1,0,0,'stenik_siteshipping_from_store_stenik_siteshipping_from_store','Вземи от обект - Вземи от обект',37,0,0,0,0,221,221,NULL,NULL,0,0,'0','','','','','','','','','','','','',0,0,0,0,22,2267133) ON DUPLICATE KEY UPDATE `updated_at`='2025-06-23 21:17:08.229',`address_type`=VALUES(`address_type`),`quote_id`=VALUES(`quote_id`),`customer_id`=VALUES(`customer_id`),`customer_address_id`=VALUES(`customer_address_id`),`email`=VALUES(`email`),`prefix`=VALUES(`prefix`),`firstname`=VALUES(`firstname`),`middlename`=VALUES(`middlename`),`lastname`=VALUES(`lastname`),`street`=VALUES(`street`),`city_id`=VALUES(`city_id`),`store_code`=VALUES(`store_code`),`office_code`=VALUES(`office_code`),`city`=VALUES(`city`),`region`=VALUES(`region`),`region_id`=VALUES(`region_id`),`postcode`=VALUES(`postcode`),`country_id`=VALUES(`country_id`),`telephone`=VALUES(`telephone`),`same_as_billing`=VALUES(`same_as_billing`),`free_shipping`=VALUES(`free_shipping`),`collect_shipping_rates`=VALUES(`collect_shipping_rates`),`shipping_method`=VALUES(`shipping_method`),`shipping_description`=VALUES(`shipping_description`),`weight`=VALUES(`weight`),`shipping_amount`=VALUES(`shipping_amount`),`base_shipping_amount`=VALUES(`base_shipping_amount`),`discount_amount`=VALUES(`discount_amount`),`base_discount_amount`=VALUES(`base_discount_amount`),`grand_total`=VALUES(`grand_total`),`base_grand_total`=VALUES(`base_grand_total`),`customer_notes`=VALUES(`customer_notes`),`discount_description`=VALUES(`discount_description`),`shipping_discount_amount`=VALUES(`shipping_discount_amount`),`base_shipping_discount_amount`=VALUES(`base_shipping_discount_amount`),`invoice`=VALUES(`invoice`),`invoice_type`=VALUES(`invoice_type`),`invoice_company_name`=VALUES(`invoice_company_name`),`invoice_company_mol`=VALUES(`invoice_company_mol`),`invoice_company_city`=VALUES(`invoice_company_city`),`invoice_company_address`=VALUES(`invoice_company_address`),`invoice_company_bulstat`=VALUES(`invoice_company_bulstat`),`invoice_company_vat`=VALUES(`invoice_company_vat`),`invoice_personal_name`=VALUES(`invoice_personal_name`),`invoice_personal_pin`=VALUES(`invoice_personal_pin`),`invoice_personal_city`=VALUES(`invoice_personal_city`),`invoice_personal_vat`=VALUES(`invoice_personal_vat`),`invoice_personal_address`=VALUES(`invoice_personal_address`),`praktis_shipping_discount_amount`=VALUES(`praktis_shipping_discount_amount`),`base_praktis_shipping_discount_amount`=VALUES(`base_praktis_shipping_discount_amount`),`cash_on_delivery_tax_amount`=VALUES(`cash_on_delivery_tax_amount`),`base_cash_on_delivery_tax_amount`=VALUES(`base_cash_on_delivery_tax_amount`),`shipping_type`=VALUES(`shipping_type`)
2025/06/23 21:17:08 [DEBUG] BNP: Current payment data exists (1361 bytes), no restore needed

2025/06/23 21:17:08 /graphapi/graphql/resolver/cart-checkout.resolvers.go:506
[0.255ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144963

2025/06/23 21:17:08 [DEBUG] UpdateQuotePaymentMethod: Processing payment ID 1838709, current method: stenik_leasingjetcredit, target method: stenik_leasingjetcredit
2025/06/23 21:17:08 /graphapi/graphql/resolver/cart-checkout.resolvers.go:341
2025/06/23 21:17:08 [DEBUG] UpdateQuotePaymentMethod: Payment method already correct for payment ID 1838709, skipping update
[0.368ms] [rows:1] UPDATE `sales_flat_quote` SET `customer_note`='' WHERE entity_id = 1144963
2025/06/23 21:17:08 [INFO] BNP: Restoring payment data for quote 1144963 (985 bytes)

2025/06/23 21:17:08 [DEBUG] BNP: Current payment data exists (1361 bytes), no restore needed
2025/06/23 21:17:08 /graphapi/graphql/resolver/cart-checkout.resolvers.go:506
[0.238ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144963

2025/06/23 21:17:08 /graphapi/graphql/resolver/cart-checkout.resolvers.go:365
[0.301ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144963 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:17:08 /graphapi/graphql/resolver/cart-checkout.resolvers.go:377
[0.113ms] [rows:1] SELECT `additional_information` FROM `sales_flat_quote_payment` WHERE quote_id = 1144963
2025/06/23 21:17:08 [DEBUG] BNP: Database check - Valid: true, Value: a:12:{s:4:"loan";a:10:{s:26:"correct_downpayment_amount";s:1:"0";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:22:"total_repayment_amount";s:6:"228.96";s:3:"apr";s:5:"23.73";s:18:"pricing_variant_id";s:6:"481435";s:21:"processing_fee_amount";s:1:"0";s:18:"installment_amount";s:5:"76.32";}s:13:"customer_data";a:11:{s:5:"email";s:26:"<EMAIL>";s:7:"address";s:11:"zh.k. Teres";s:4:"city";s:8:"Radinovo";s:12:"company_name";s:0:"";s:9:"last_name";s:8:"Atanasov";s:5:"phone";s:10:"0883555204";s:3:"egn";s:10:"9102288469";s:3:"eik";s:0:"";s:3:"mol";s:0:"";s:10:"first_name";s:6:"Stoyan";s:9:"post_code";s:4:"4202";}s:3:"pin";s:10:"9102288469";s:5:"email";s:26:"<EMAIL>";s:9:"principal";s:3:"221";s:10:"variant_id";i:1;s:11:"downpayment";s:1:"0";s:5:"names";s:15:"Stoyan Atanasov";s:4:"name";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:13:"good_type_ids";s:3:"353";s:16:"selected_variant";a:9:{s:15:"total_repayment";s:6:"228.96";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";s:6:"481435";s:11:"installment";s:5:"76.32";s:3:"apr";s:5:"23.73";s:17:"pricing_scheme_id";s:4:"1695";}}
2025/06/23 21:17:08 [DEBUG] BNP: Updated payment object with additional information
2025/06/23 21:17:08 [DEBUG] BNP: === VALIDATING QUOTE FOR BNP PAYMENT ===
2025/06/23 21:17:08 [DEBUG] BNP: Quote ID: 1144963
2025/06/23 21:17:08 [DEBUG] BNP: Payment ID: 1838709
2025/06/23 21:17:08 [DEBUG] BNP: Payment method: stenik_leasingjetcredit
2025/06/23 21:17:08 [DEBUG] BNP: Additional information length: 1361
2025/06/23 21:17:08 [DEBUG] BNP: Additional information content: a:12:{s:4:"loan";a:10:{s:26:"correct_downpayment_amount";s:1:"0";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:22:"total_repayment_amount";s:6:"228.96";s:3:"apr";s:5:"23.73";s:18:"pricing_variant_id";s:6:"481435";s:21:"processing_fee_amount";s:1:"0";s:18:"installment_amount";s:5:"76.32";}s:13:"customer_data";a:11:{s:5:"email";s:26:"<EMAIL>";s:7:"address";s:11:"zh.k. Teres";s:4:"city";s:8:"Radinovo";s:12:"company_name";s:0:"";s:9:"last_name";s:8:"Atanasov";s:5:"phone";s:10:"0883555204";s:3:"egn";s:10:"9102288469";s:3:"eik";s:0:"";s:3:"mol";s:0:"";s:10:"first_name";s:6:"Stoyan";s:9:"post_code";s:4:"4202";}s:3:"pin";s:10:"9102288469";s:5:"email";s:26:"<EMAIL>";s:9:"principal";s:3:"221";s:10:"variant_id";i:1;s:11:"downpayment";s:1:"0";s:5:"names";s:15:"Stoyan Atanasov";s:4:"name";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:13:"good_type_ids";s:3:"353";s:16:"selected_variant";a:9:{s:15:"total_repayment";s:6:"228.96";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";s:6:"481435";s:11:"installment";s:5:"76.32";s:3:"apr";s:5:"23.73";s:17:"pricing_scheme_id";s:4:"1695";}}
2025/06/23 21:17:08 [DEBUG] BNP: Payment data is in PHP serialized format (expected for Magento)
2025/06/23 21:17:08 [DEBUG] BNP: PHP serialized payment data validation passed
XDEBUG_SESSION=PHPSTORM
CallAPI->http://server/theme_api/sales_order/createNewOrder took 450.828523ms

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.200ms] [rows:1] SELECT * FROM `sales_flat_order_payment` WHERE `sales_flat_order_payment`.`parent_id` = 301021

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[1.162ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '120303010' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
2025/06/23 21:17:08 [INFO] BNP: Transferring payment data from quote 1144963 to order 301021
2025/06/23 21:17:08 [INFO] BNP: Retrieving persistent payment data for quote 1144963
2025/06/23 21:17:08 [INFO] BNP: Retrieved persistent payment data (985 bytes) for quote 1144963
2025/06/23 21:17:08 [INFO] BNP: Found BNP payment data (985 bytes), transferring to order payment

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/payments.go:456
[0.092ms] [rows:1] SELECT * FROM `bnp_payment_data_storage` WHERE quote_id = 1144963 ORDER BY `bnp_payment_data_storage`.`id` LIMIT 1
2025/06/23 21:17:08 [DEBUG] BNP: Converted variant_id from float64 1 to int 1 for admin panel compatibility
2025/06/23 21:17:08 [INFO] BNP: Converted payment data to PHP serialized format (1361 bytes)
2025/06/23 21:17:08 [INFO] BNP: Payment data successfully transferred to order 301021 (1 records updated)

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/payments.go:520
[1.248ms] [rows:1]
		UPDATE sales_flat_order_payment
		SET additional_information = 'a:12:{s:4:"loan";a:10:{s:3:"apr";s:5:"23.73";s:3:"nir";s:5:"21.47";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";s:18:"installment_amount";s:5:"76.32";s:8:"maturity";s:1:"3";s:17:"pricing_scheme_id";s:4:"1695";s:18:"pricing_variant_id";s:6:"481435";s:21:"processing_fee_amount";s:1:"0";s:22:"total_repayment_amount";s:6:"228.96";}s:10:"variant_id";i:1;s:13:"customer_data";a:11:{s:5:"email";s:26:"<EMAIL>";s:3:"mol";s:0:"";s:5:"phone";s:10:"0883555204";s:7:"address";s:11:"zh.k. Teres";s:3:"egn";s:10:"9102288469";s:3:"eik";s:0:"";s:10:"first_name";s:6:"Stoyan";s:9:"last_name";s:8:"Atanasov";s:9:"post_code";s:4:"4202";s:4:"city";s:8:"Radinovo";s:12:"company_name";s:0:"";}s:5:"email";s:26:"<EMAIL>";s:13:"good_type_ids";s:3:"353";s:4:"name";s:15:"Stoyan Atanasov";s:5:"names";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:3:"pin";s:10:"9102288469";s:9:"principal";s:3:"221";s:11:"downpayment";s:1:"0";s:16:"selected_variant";a:9:{s:2:"id";s:6:"481435";s:3:"nir";s:5:"21.47";s:15:"total_repayment";s:6:"228.96";s:8:"maturity";s:1:"3";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:3:"apr";s:5:"23.73";s:26:"correct_downpayment_amount";s:1:"0";s:11:"installment";s:5:"76.32";}}'
		WHERE parent_id = 301021


2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/payments.go:544
[0.100ms] [rows:-]
		SELECT entity_id, additional_information
		FROM sales_flat_order_payment
		WHERE parent_id = 301021

2025/06/23 21:17:08 [SUCCESS] BNP: VERIFICATION PASSED - Payment data confirmed in database
2025/06/23 21:17:08 [SUCCESS] BNP: Order ID: 301021, Payment ID: 301020, Data Length: 1361 bytes
2025/06/23 21:17:08 [SUCCESS] BNP: Data Preview: a:12:{s:4:"loan";a:10:{s:3:"apr";s:5:"23.73";s:3:"nir";s:5:"21.47";s:19:"pricing_scheme_name";s:40:"...

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/cart-utils/payments.go:566
[1.041ms] [rows:1] DELETE FROM `bnp_payment_data_storage` WHERE quote_id = 1144963
2025/06/23 21:17:08 [INFO] BNP: Backup data cleaned up for quote 1144963
2025/06/23 21:17:08 [INFO] BNP: Payment data successfully transferred to order 120303010
2025/06/23 21:17:08 [DEBUG] BNP: Reloading order 120303010 to get updated payment information

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.143ms] [rows:1] SELECT * FROM `sales_flat_order_payment` WHERE `sales_flat_order_payment`.`parent_id` = 301021

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.411ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '120303010' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
2025/06/23 21:17:08 [DEBUG] BNP: Order 120303010 reloaded successfully
2025/06/23 21:17:08 [DEBUG] BNP: Payment already loaded by preload - ID: 301020, Method: stenik_leasingjetcredit
2025/06/23 21:17:08 [INFO] Post-order processing started for order 120303010
2025/06/23 21:17:08 [DEBUG] BNP: Order details - ID: 301021, IncrementID: 120303010
2025/06/23 21:17:08 [DEBUG] BNP: Order payment found - ID: 301020, Method: stenik_leasingjetcredit
2025/06/23 21:17:08 [INFO] Order 120303010 uses BNP payment method, processing BNP data transfer and application
2025/06/23 21:17:08 [INFO] BNP: === POST-ORDER PAYMENT DATA VERIFICATION ===
2025/06/23 21:17:08 [INFO] BNP: Order: 120303010, Order ID: 301021, Payment ID: 301020

2025/06/23 21:17:08 /graphapi/internal/praktis/checkout/order-utils/bnp_post_processing.go:51
[0.073ms] [rows:-]
		SELECT entity_id, additional_information
		FROM sales_flat_order_payment
		WHERE parent_id = 301021

2025/06/23 21:17:08 [SUCCESS] BNP: Payment data verified in database
2025/06/23 21:17:08 [SUCCESS] BNP: Order: 120303010, Payment ID: 301020, Data Length: 1361 bytes
2025/06/23 21:17:08 [SUCCESS] BNP: Data Preview: a:12:{s:4:"loan";a:10:{s:3:"apr";s:5:"23.73";s:3:"nir";s:5:"21.47";s:19:"pricing_scheme_name";s:40:"...
2025/06/23 21:17:08 [WARNING] BNP: Failed to parse payment data JSON: invalid character 'a' looking for beginning of value
2025/06/23 21:17:08 [SUCCESS] BNP: Database verification PASSED
2025/06/23 21:17:08 [SUCCESS] BNP: Admin panel will be able to display BNP applicant information
2025/06/23 21:17:08 [INFO] BNP: === APPLICATION SUBMISSION START ===
2025/06/23 21:17:08 [INFO] BNP: Order Number: 120303010
2025/06/23 21:17:08 [INFO] BNP: Timestamp: 2025-06-23T21:17:08Z
2025/06/23 21:17:08 [DEBUG] BNP: Loading order from database...

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.118ms] [rows:1] SELECT * FROM `sales_flat_order_payment` WHERE `sales_flat_order_payment`.`parent_id` = 301021

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/order-repository.go:62
[0.327ms] [rows:1] SELECT * FROM `sales_flat_order` WHERE increment_id = '120303010' ORDER BY `sales_flat_order`.`entity_id` LIMIT 1
2025/06/23 21:17:08 [INFO] BNP: Order loaded successfully - ID: 301021, Status: pending, Total: 221.00
2025/06/23 21:17:08 [DEBUG] BNP: Payment already loaded by preload - ID: 301020, Method: stenik_leasingjetcredit
2025/06/23 21:17:08 [DEBUG] BNP: Validating payment method...
2025/06/23 21:17:08 [DEBUG] BNP: Order payment method: stenik_leasingjetcredit
2025/06/23 21:17:08 [INFO] BNP: Payment method validation successful
2025/06/23 21:17:08 [DEBUG] BNP: Loading payment additional information...
2025/06/23 21:17:08 [DEBUG] BNP: Searching for quote payment with quote_id: 1144963
2025/06/23 21:17:08 [DEBUG] BNP: Found quote payment with additional information (1361 bytes)
2025/06/23 21:17:08 [WARNING] BNP: Failed to parse payment additional information: invalid character 'a' looking for beginning of value
2025/06/23 21:17:08 [DEBUG] BNP: Raw additional information: a:12:{s:4:"loan";a:10:{s:26:"correct_downpayment_amount";s:1:"0";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:17:"pricing_scheme_id";s:4:"1695";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:22:"total_repayment_amount";s:6:"228.96";s:3:"apr";s:5:"23.73";s:18:"pricing_variant_id";s:6:"481435";s:21:"processing_fee_amount";s:1:"0";s:18:"installment_amount";s:5:"76.32";}s:13:"customer_data";a:11:{s:5:"email";s:26:"<EMAIL>";s:7:"address";s:11:"zh.k. Teres";s:4:"city";s:8:"Radinovo";s:12:"company_name";s:0:"";s:9:"last_name";s:8:"Atanasov";s:5:"phone";s:10:"0883555204";s:3:"egn";s:10:"9102288469";s:3:"eik";s:0:"";s:3:"mol";s:0:"";s:10:"first_name";s:6:"Stoyan";s:9:"post_code";s:4:"4202";}s:3:"pin";s:10:"9102288469";s:5:"email";s:26:"<EMAIL>";s:9:"principal";s:3:"221";s:10:"variant_id";i:1;s:11:"downpayment";s:1:"0";s:5:"names";s:15:"Stoyan Atanasov";s:4:"name";s:15:"Stoyan Atanasov";s:5:"phone";s:10:"0883555204";s:13:"good_type_ids";s:3:"353";s:16:"selected_variant";a:9:{s:15:"total_repayment";s:6:"228.96";s:8:"maturity";s:1:"3";s:3:"nir";s:5:"21.47";s:19:"pricing_scheme_name";s:40:"1,2% месечно оскъпяване";s:26:"correct_downpayment_amount";s:1:"0";s:2:"id";s:6:"481435";s:11:"installment";s:5:"76.32";s:3:"apr";s:5:"23.73";s:17:"pricing_scheme_id";s:4:"1695";}}
2025/06/23 21:17:08 [DEBUG] BNP: Preparing application data...

2025/06/23 21:17:08 /graphapi/internal/praktis/credit-calculators/bnp-application.go:202
[0.126ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144963 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:17:08 /graphapi/internal/praktis/credit-calculators/bnp-application.go:293
[0.085ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144963 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:17:08 /graphapi/internal/praktis/credit-calculators/bnp-application.go:326
[0.078ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144963 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:17:08 /graphapi/internal/praktis/credit-calculators/bnp-application.go:360
[0.077ms] [rows:1] SELECT * FROM `sales_flat_quote_payment` WHERE quote_id = 1144963 ORDER BY `sales_flat_quote_payment`.`payment_id` LIMIT 1

2025/06/23 21:17:08 /graphapi/internal/praktis/credit-calculators/bnp-application.go:390
[0.064ms] [rows:-]
			SELECT additional_information
			FROM sales_flat_order_payment
			WHERE parent_id = 301021


2025/06/23 21:17:08 /graphapi/internal/praktis/credit-calculators/bnp-application.go:422
[0.057ms] [rows:-]
			SELECT additional_information
			FROM sales_flat_order_payment
			WHERE parent_id = 301021


2025/06/23 21:17:08 /graphapi/internal/praktis/credit-calculators/bnp-application.go:450
[0.150ms] [rows:1] SELECT * FROM `sales_flat_order_item` WHERE order_id = 301021

2025/06/23 21:17:08 /graphapi/internal/praktis/credit-calculators/bnp-application.go:461
[0.122ms] [rows:1] SELECT * FROM `sales_flat_order_item` WHERE order_id = 301021
2025/06/23 21:17:08 [INFO] BNP: Application data prepared - Customer: Stoyan Atanasov, Phone: , EGN: 9102288469, Subtotal: 221.00 BGN
2025/06/23 21:17:08 [DEBUG] BNP: Customer Address: zh.k. Teres
2025/06/23 21:17:08 [DEBUG] BNP: Order items: 360242
2025/06/23 21:17:08 [DEBUG] BNP: Getting email configuration...
2025/06/23 21:17:08 [INFO] BNP: Application will be sent to: <EMAIL>
2025/06/23 21:17:08 [DEBUG] BNP: Sending application email...
2025/06/23 21:17:08 [INFO] BNP: Sending application <NAME_EMAIL>
2025/06/23 21:17:08 [INFO] BNP: Would send <NAME_EMAIL> with content:

Subject: BNP Paribas Leasing Application - Order {120303010 true}

Dear BNP Paribas Team,

A new leasing application has been submitted through our online store.

Order Details:
- Order Number: {120303010 true}
- Order Date: 2025-06-23 21:17:08
- Subtotal: 221.00 BGN

Customer Information:
- Name: Stoyan Atanasov
- Email: {<EMAIL> true}

- EGN/PIN: 9102288469
- Address: zh.k. Teres

Products:
360242





Order Items Details:
<table border='1' cellpadding='5' cellspacing='0'><tr><th>Product</th><th>SKU</th><th>Qty</th><th>Price</th></tr><tr><td>360242</td><td>360242</td><td>1</td><td>221.00 BGN</td></tr></table>

Best regards,
Praktis.bg Team
2025/06/23 21:17:08 [INFO] BNP: Application email sent <NAME_EMAIL>
2025/06/23 21:17:08 [INFO] BNP: Application email sent successfully
2025/06/23 21:17:08 [INFO] BNP: === APPLICATION SUBMISSION COMPLETED ===
2025/06/23 21:17:08 [INFO] BNP: Order: 120303010, Duration: 1.286911ms, Status: SUCCESS
2025/06/23 21:17:08 [INFO] BNP application submitted successfully for order 120303010

2025/06/23 21:17:08 /graphapi/packages/magento-core/mage-store/sales/order-status.go:38
[1.260ms] [rows:36] SELECT
    sos.status,
    COALESCE(sosl.label, sos.label) as label
FROM
    sales_order_status sos
        LEFT JOIN
    sales_order_status_label sosl
    ON sos.status = sosl.status
        AND sosl.store_id = 1
order by sos.status asc
[GIN] 2025/06/23 - 21:17:08 | 200 |  477.216716ms |       ********* | POST     "/graphql"
2025/06/23 21:17:08 [DEBUG] BNP: Starting BNP Calculator API initialization
2025/06/23 21:17:08 [DEBUG] BNP: Merchant ID from config (path: stenik_leasingjetcredit/service/merchant_id): '433147'
2025/06/23 21:17:08 [DEBUG] BNP: Using merchant ID: 433147
2025/06/23 21:17:08 [INFO] BNP: Using merchant ID: 433147
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:08 [DEBUG] BNP: Getting PRODUCTION URL from config path: stenik_leasingjetcredit/service/production_url
2025/06/23 21:17:08 [DEBUG] BNP: Base URL from config: 'https://ws.pbpf.bg/ServicesPricing/'
2025/06/23 21:17:08 [INFO] BNP: Using base URL: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:08 [INFO] BNP: Operating in mode: PRODUCTION
2025/06/23 21:17:08 [DEBUG] BNP: Starting TLS configuration setup
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:08 [DEBUG] BNP: Getting PRODUCTION certificate filename from config path: stenik_leasingjetcredit/service/production_certificate_path
2025/06/23 21:17:08 [DEBUG] BNP: Raw certificate filename from Magento config: 'bnp-cert2023.pem'
2025/06/23 21:17:08 [INFO] BNP: Certificate full path resolved: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:17:08 [DEBUG] BNP: Certificate path resolved to: /tmp/certificates/bnp-cert2023.pem
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:08 [DEBUG] BNP: Getting PRODUCTION private key filename from config path: stenik_leasingjetcredit/service/production_key_path
2025/06/23 21:17:08 [DEBUG] BNP: Raw private key filename from Magento config: 'bnp-key2023-nopass.pem'
2025/06/23 21:17:08 [INFO] BNP: Private key full path resolved: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:17:08 [DEBUG] BNP: Private key path resolved to: /tmp/certificates/bnp-key2023-nopass.pem
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode config value: '0' (from path: stenik_leasingjetcredit/service/sandbox_mode)
2025/06/23 21:17:08 [DEBUG] BNP: Sandbox mode resolved to: false
2025/06/23 21:17:08 [DEBUG] BNP: No key password configured (as expected)
2025/06/23 21:17:08 [DEBUG] BNP: Loading X509 certificate/key pair
2025/06/23 21:17:08 [INFO] BNP: Successfully loaded X509 certificate/key pair
2025/06/23 21:17:08 [INFO] BNP: TLS configuration created successfully with Renegotiation=FreelyAsClient, ServerName=ws.pbpf.bg, MinVersion=TLS1.2, InsecureSkipVerify=false
2025/06/23 21:17:08 [DEBUG] BNP: Certificate count in TLS config: 1
2025/06/23 21:17:08 [INFO] BNP: TLS configuration loaded successfully
2025/06/23 21:17:08 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:08 [INFO] BNP: Operation: Parameter Validation
2025/06/23 21:17:08 [INFO] BNP: Timestamp: 2025-06-23T21:17:08Z
2025/06/23 21:17:08 [DEBUG] BNP: method: GetAvailablePricingSchemes
2025/06/23 21:17:08 [DEBUG] BNP: goodTypeIds: 353
2025/06/23 21:17:08 [DEBUG] BNP: principal: 221
2025/06/23 21:17:08 [DEBUG] BNP: downPayment: 0
2025/06/23 21:17:08 [DEBUG] BNP: URL Construction - Original Base: https://ws.pbpf.bg/ServicesPricing/
2025/06/23 21:17:08 [DEBUG] BNP: URL Construction - Trimmed Base: https://ws.pbpf.bg/ServicesPricing, Method: GetAvailablePricingSchemes
2025/06/23 21:17:08 [DEBUG] BNP: URL Parameters: [433147 353 221.00 0.00]
2025/06/23 21:17:08 [INFO] BNP: Final constructed URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/221.00/0.00
2025/06/23 21:17:08 [INFO] BNP: === OUTGOING REQUEST ===
2025/06/23 21:17:08 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/23 21:17:08 [INFO] BNP: Timestamp: 2025-06-23T21:17:08Z
2025/06/23 21:17:08 [INFO] BNP: URL: https://ws.pbpf.bg/ServicesPricing/GetAvailablePricingSchemes/433147/353/221.00/0.00
2025/06/23 21:17:08 [INFO] BNP: HTTP Method: GET
2025/06/23 21:17:08 [DEBUG] BNP: Request Headers:
2025/06/23 21:17:08 [DEBUG] BNP:   User-Agent: MerchantPos
2025/06/23 21:17:08 [DEBUG] BNP: Request Parameters:
2025/06/23 21:17:08 [DEBUG] BNP:   Service Path: ServicesPricing
2025/06/23 21:17:08 [DEBUG] BNP:   API Method: GetAvailablePricingSchemes
2025/06/23 21:17:08 [DEBUG] BNP:   Merchant ID: 433147
2025/06/23 21:17:08 [DEBUG] BNP:   Good Type IDs: 353
2025/06/23 21:17:08 [DEBUG] BNP:   Principal: 221.00
2025/06/23 21:17:08 [DEBUG] BNP:   Down Payment: 0.00
2025/06/23 21:17:08 [DEBUG] BNP: Full Request Dump:
GET /ServicesPricing/GetAvailablePricingSchemes/433147/353/221.00/0.00 HTTP/1.1
Host: ws.pbpf.bg
User-Agent: MerchantPos
Accept-Encoding: gzip

[GIN] 2025/06/23 - 21:17:08 | 200 |    1.692952ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:08 | 200 |      89.622µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:08 | 200 |     196.933µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:08 | 200 |     278.803µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:08 | 200 |     364.556µs |       ********* | POST     "/graphql"
2025/06/23 21:17:08 [INFO] BNP: === INCOMING RESPONSE ===
2025/06/23 21:17:08 [INFO] BNP: Method: GetAvailablePricingSchemes
2025/06/23 21:17:08 [INFO] BNP: Timestamp: 2025-06-23T21:17:08Z
2025/06/23 21:17:08 [INFO] BNP: Duration: 200.122481ms
2025/06/23 21:17:08 [INFO] BNP: Status Code: 200
2025/06/23 21:17:08 [INFO] BNP: Status: 200 OK
2025/06/23 21:17:08 [DEBUG] BNP: Response Headers:
2025/06/23 21:17:08 [DEBUG] BNP:   Content-Type: application/xml; charset=utf-8
2025/06/23 21:17:08 [DEBUG] BNP:   Server: Microsoft-IIS/10.0
2025/06/23 21:17:08 [DEBUG] BNP:   X-Aspnet-Version: 4.0.30319
2025/06/23 21:17:08 [DEBUG] BNP:   X-Powered-By: ASP.NET
2025/06/23 21:17:08 [DEBUG] BNP:   Date: Mon, 23 Jun 2025 21:17:08 GMT
2025/06/23 21:17:08 [DEBUG] BNP:   Content-Length: 675
2025/06/23 21:17:08 [DEBUG] BNP:   Cache-Control: private
2025/06/23 21:17:08 [DEBUG] BNP: Response Body Length: 675 bytes
2025/06/23 21:17:08 [DEBUG] BNP: Response Body Content:
<Result xmlns:i="http://www.w3.org/2001/XMLSchema-instance"><Data><PricingScheme><PricingSchemeId>1695</PricingSchemeId><PricingSchemeName>1,2% месечно оскъпяване</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2791</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 2м. с кредитна карта</PricingSchemeName></PricingScheme><PricingScheme><PricingSchemeId>2792</PricingSchemeId><PricingSchemeName>Вземи сега, плати после:0% за 3м. с кредитна карта</PricingSchemeName></PricingScheme></Data><ErrorCode>0</ErrorCode><ErrorDetails/><ErrorMessage/></Result>
2025/06/23 21:17:08 [INFO] BNP: Response Status: SUCCESS
2025/06/23 21:17:08 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:08 [INFO] BNP: Operation: Response Validation
2025/06/23 21:17:08 [INFO] BNP: Timestamp: 2025-06-23T21:17:08Z
2025/06/23 21:17:08 [DEBUG] BNP: errorCode: 0
2025/06/23 21:17:08 [DEBUG] BNP: errorMessage:
2025/06/23 21:17:08 [DEBUG] BNP: schemeCount: 3
2025/06/23 21:17:08 [INFO] BNP: === BUSINESS LOGIC ===
2025/06/23 21:17:08 [INFO] BNP: Operation: Data Transformation
2025/06/23 21:17:08 [INFO] BNP: Timestamp: 2025-06-23T21:17:08Z
2025/06/23 21:17:08 [DEBUG] BNP: inputSchemes: 3
2025/06/23 21:17:08 [DEBUG] BNP: outputSchemes: 3
2025/06/23 21:17:08 [DEBUG] BNP: success: true
2025/06/23 21:17:08 [INFO] BNP: GetAvailablePricingSchemes completed successfully in 200.122481ms, returned 3 schemes
[GIN] 2025/06/23 - 21:17:08 | 200 |  201.364097ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |    1.948214ms |       ********* | POST     "/graphql"

2025/06/23 21:17:29 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[2.744ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1

2025/06/23 21:17:29 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[3.573ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1
[GIN] 2025/06/23 - 21:17:29 | 200 |    4.246105ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |    4.546734ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     501.943µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     498.757µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     712.156µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     246.422µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |      49.231µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |      32.972µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     340.309µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |      274.47µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     261.127µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     320.367µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     281.458µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |      45.129µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |       59.06µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:29 | 200 |     191.298µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |    1.891543ms |       ********* | POST     "/graphql"

2025/06/23 21:17:34 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[3.597ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1

2025/06/23 21:17:34 /graphapi/packages/magento-core/url-rewrite-repository.go:35
[3.636ms] [rows:0] SELECT `request_path`,`id_path`,`target_path`,`options` FROM `core_url_rewrite` WHERE (request_path = 'searchanise/async' OR request_path = 'searchanise/async/') AND store_id = 1
[GIN] 2025/06/23 - 21:17:34 | 200 |    4.236107ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |    4.718138ms |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |     157.334µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |       267.8µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |     218.969µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |     243.514µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |      37.644µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |      42.108µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |     273.412µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |     208.337µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |     149.265µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |     138.879µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |      99.127µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |      42.313µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |      38.407µs |       ********* | POST     "/graphql"
[GIN] 2025/06/23 - 21:17:34 | 200 |     331.435µs |       ********* | POST     "/graphql"