package magento_core

import (
	"errors"
	"fmt"
	"strings"
)

func (s *StoreEntity) InitStoreByCode(code string) error {
	storeClient.DbConn().
		Where("code = ? and is_active = 1", code).
		First(s)
	if s.StoreID < 1 {
		return errors.New("could not load mage-store")
	}
	s.RootCategoryID = s.GetConfigInt(
		RootCategoryIDConfigPath,
		0,
	)
	if s.RootCategoryID < 1 {
		return errors.New("could not load root category")
	}

	s.BaseURL = s.GetConfig(
		BaseURLConfigPath,
		"",
	)
	if s.BaseURL == "" {
		return errors.New("could not load base url")
	}

	s.MediaURL = s.GetConfig(
		MediaURLConfigPath,
		"",
	)
	if s.MediaURL == "" {
		return errors.New("could not load media url")
	}

	if strings.Contains(s.MediaURL, "{{secure_base_url}}") {
		s.MediaURL = strings.Replace(s.MediaURL, "{{secure_base_url}}", s.BaseURL, 1)
	} else if strings.Contains(s.MediaURL, "{{unsecure_base_url}}") {
		s.MediaURL = strings.Replace(s.MediaURL, "{{unsecure_base_url}}", s.BaseURL, 1)
	}

	return nil
}

type StoreProvider interface {
	GetStore() *StoreEntity
}

const DefaultStoreID = 0
const DefaultStoreCode = "default"

// StoreEntity mage-store
type StoreEntity struct {
	StoreID        int
	StoreCode      string `gorm:"column:code"`
	WebsiteID      int
	GroupID        int
	Name           string
	RootCategoryID int    `gorm:"-"`
	BaseURL        string `gorm:"-"`
	MediaURL       string `gorm:"-"`
}

const (
	RootCategoryIDConfigPath ConfigValuePath = "catalog/category/root_id"
	BaseURLConfigPath        ConfigValuePath = "web/secure/base_url"
	MediaURLConfigPath       ConfigValuePath = "web/secure/base_media_url"
)

func (s *StoreEntity) TableName() string {
	return "core_store"
}

func (s *StoreEntity) GetID() int {
	return s.StoreID
}

func (s *StoreEntity) GetImageUrl(path string) string {
	return strings.TrimRight(s.MediaURL, "/") + strings.Replace(path, "//", "/", -1)
}

func (s *StoreEntity) GetMedialUrl(url string) string {
	return prepUrl(s.MediaURL, url)
}

func (s *StoreEntity) GetBaseUrl(url string) string {
	return prepUrl(s.BaseURL, url)
}

func (s *StoreEntity) GetBaseCurrencyCode() string {
	// @TODO - get from magento config
	return "BGN"
}

func (s *StoreEntity) GetCountryCode() string {
	// @TODO - get from magento config
	return "BG"
}

func prepUrl(base string, uri string) string {
	if strings.Index(uri, "http") == 0 {
		return uri
	}

	uri = strings.TrimLeft(uri, "/")
	base = strings.TrimRight(base, "/")

	return fmt.Sprintf("%s/%s", base, uri)
}

var JWTSecret []byte

func GetJWTSecret() ([]byte, error) {
	if JWTSecret == nil || len(JWTSecret) == 0 {
		return []byte{}, errors.New("mage core JWT secret is nopt initialized")
	}

	return JWTSecret, nil
}

func SetJWTSecret(secret []byte) {
	JWTSecret = secret
}
