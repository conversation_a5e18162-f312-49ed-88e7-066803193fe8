1. Речник
NIR - ГЛП - (Годишен Лихвен Процент)
APR - ГПР - (Годишен Процент на Разходите)
POS - Търговски обект на партньор
SSL - Secure Socket Layer
HTTPS - Hypertext Transfer Protocol Secure

2. Общ преглед
BNP Paribas PF предоставя на търговските партньори уеб услуга, наричана Pricing Service. Тя представлява уеб базиран сервиз, който осигурява интерфейс за комуникация между сайта на търговския партньор и търговската система на BNP Paribas PF. Този сервиз предоставя набор от методи, чиито параметри се предават като QueryString. Резултатът от всеки метод е във формат XML. Комуникацията между уеб сервиза и сайта на търговския партньор е защитена и криптирана със сертификати. Протоколът за комуникация е HTTPS. Всеки търговски партньор получава два (2) сертификата – един за тестове и един за реална работна среда.

3. Интерфейс

3.1.Адреси
Услугата може да бъде достъпвана на следните адреси:
Тестова среда: https://ws-test.bnpparibas-pf.bg/ServicesPricing/<MethodName>/<parameters>
Продуктова среда: https://ws.bnpparibas-pf.bg/ServicesPricing/<MethodName>/<parameters>

3.2.Резултат
Всеки един метод на сервиза връща един и същи тип резултат в следния формат:
Легенда:
• Data – Това са специфичните за метода колекции от резултати.
• ErrorCode – Код на грешка (ако възникне такава). Нула (0) означава, че няма грешка.
• ErrorMessage – Описание на грешка, ако такава е регистрирана.
• ErrorDetails – Детайлно описание на грешката, ако такава е регистрирана

3.3.Методи

3.3.1.GetGoodCategories
Този метод връща списък от стокови категории по уникално ID на търговския партньор. Уникалният номер ще бъде предоставен на всеки търговски партньор от BNP Paribas PF.

3.3.1.1. Входна информация
| Параметър | Описание | Формат | Задълж. |
| PosId     | Уникален номер на търговски партньор | Integer | Да

3.3.1.2. Изходна информация

| Параметър | Описание | Формат |
| GoodCategoryId | Уникален номер на стокова категория | Integer |
| GoodCategoryName | Име на стокова категория | String |

3.3.2.GetGoodTypes
Този метод връща списък със стоките, които принадлежат на съответна категория.

3.3.2.1. Входна информация

| Параметър | Описание | Формат | Задълж. |
| GoodCategoryId | Уникален номер на стокова категория | Integer | Да |

3.3.2.2. Изходна информация
| Параметър | Описание | Формат |
| GoodTypeId | Уникален номер на стока | Integer |
| GoodTypeName | Име на стока | String |

3.3.3.GetAvailablePricingSchemes
Този метод връща списък с първите 10 най-подходящи схеми на плащане, спрямо подадените критерии.

3.3.3.1. Входна информация
| Параметър | Описание | Формат | Задълж. |
| PosId | Уникален номер на търговски |партньор | Integer | Да |
| Goods | Списък от уникални номера на стоки | Integer[] (sep. “,”) | Да |
| TotalGoodPrice | Обща цена на всички стоки | Decimal | Да |
| DownPayment | Първоначална вноска | Decimal | Да |

3.3.3.2. Изходна информация
| Параметър | Описание | Формат |
| PricingSchemeId | Уникален номер на ценова схема | Integer |
| PricingSchemeName | Име на ценова схема | String |

3.3.4.GetAvailablePricingVariants
Този метод връща списък с първите 15 най-подходящи варианти за съответния търговски партньор.

3.3.4.1. Входна информация
| Параметър | Описание | Формат | Задълж. |
| PosId | Уникален номер на търговски партньор | Integer | Да |
| Goods | Списък от уникални номера на стоки | Integer[](sep. “,”) | Да |
| TotalGoodsPrice | Обща цена на всички стоки | Decimal | Да |
| DownPayment | Първоначална вноска | Decimal | Да |
| PreferredInstallment | Предпочитана вноска | Decimal | Не |
| PricingSchemeId | Уникален номер на ценова схема | Integer | Да |

3.3.4.2. Изходна информация
| Параметър | Описание | Формат |
| PreferredInstallment | Предпочитана вноска | Decimal |
| PricingVariantId | Уникален номер на ценови вариант | Integer |
| PricingSchemeId | Уникален номер на ценова схема | Integer |
| PricingSchemeName | Име на ценова схема | String |
| Maturity Матуритет (бр. вноски) | Integer |
| NIR% | Номинален лихвен процент | Decimal |
| APR% | Годишен лихвен процент | Decimal |
| CorrectDownPaymentAmount | Задължителна първоначална вноска | Decimal |
| InstallmentAmount | Месечна вноска | Decimal |
| TotalRepaymentAmount | Пълна сума на кредита | Decimal |

3.3.5.CalculateLoan
Този метод връща финалните данни за желания кредит.

3.3.5.1. Входна информация
| Параметър | Описание | Формат | Задълж. |
| PosId | Уникален номер на търговски партньор | Integer | Да |
| Goods Списък от уникални номера на стоки | Integer[] (sep. “,”) | Да |
| TotalGoodsPrice | Обща цена на всички стоки | Decimal | Да |
| DownPayment | Първоначална вноска | Decimal | Да |
| PricingVariantId | Уникален номер на ценова схема | Integer | Да |

3.3.5.2. Изходна информация
| Параметър | Описание | Формат |
| PricingVariantId | Уникален номер на ценови вариант | Integer |
| PricingSchemeId Уникален номер на ценова схема | Integer |
| PricingSchemeName | Име на ценова схема | String |
| Maturity Матуритет (бр. вноски) | Integer |
| InstallmentAmount | Месечна вноска | Decimal |
| NIR% | Годишен лихвен процент ГЛП | Decimal |
| APR% | Годишен процент на разходите ГПР | Decimal |
| ProcessingFeeAmount | Такса обработка | Decimal |
| CorrectDownPaymentAmount | Задължителна първоначална вноска | Decimal |
| TotalRepaymentAmount | Пълна сума на кредита | Decimal |

4. Грешки
| Код | Име на метод | Описание |
| 403 | n/a | Неразпознат сертификат |
| 404 | n/a | Невалиден адрес
| 500 | n/a | Грешка при обработка на заявката
| 501 GetGoodCategories | Грешка при зареждане на категории стока
| 5011 | GetGoodCategories | Невалидни входни параметри
| 502 | GetGoodTypes | Грешка при зареждане на типове стока
| 5021 | GetGoodTypes | Невалидни входни параметри
| 503 | GetAvailablePricingSchemes | Грешка при зареждане на ценови схеми
| 5031 | GetAvailablePricingSchemes | Невалидна първоначална вноска: Мин. = 0.00 лв.; Макс. = {0} лв.
| 5032 | GetAvailablePricingSchemes | Невалидни входни параметри
| 504 | GetAvailablePricingVariants | Грешка при зареждане на варианти
| 5041 | GetAvailablePricingVariants | Невалидна първоначална вноска: Мин. = 0.00 лв.; Макс. = {0} лв.
| 5042 | GetAvailablePricingVariants | Невалидни входни параметри
| 505 | CalculateLoan | Грешка при изчисляване параметри на кредит
| 5051 | CalculateLoan | Невалидна първоначална вноска: Мин. = 0.00 лв.; Макс. = {0} лв.
| 5052 | CalculateLoan | Невалидни входни параметри
| 506 | n/a | Грешка при обработка на заявката
| 601 | GetGoodCategories | Грешка при зареждане на категории стока
| 602 | GetGoodTypes | Грешка при зареждане на типове стока
| 603 | GetAvailablePricingSchemes | Грешка при зареждане на ценови схеми
| 6031 | GetAvailablePricingSchemes | Не е намерен търговски партньор
| 6032 | GetAvailablePricingSchemes | Не е намерена стока
| 604 | GetAvailablePricingVariants | Грешка при зареждане на варианти
| 605 | CalculateLoan | Грешка при изчисляване параметри на кредит
| 606 | n/a | Грешка при обработка на заявкат
