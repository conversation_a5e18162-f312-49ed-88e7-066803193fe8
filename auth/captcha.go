package auth

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"time"
)

type CaptchaResponse struct {
	Success     bool      `json:"success"`
	ChallengeTs time.Time `json:"challenge_ts"`
	Hostname    string    `json:"hostname"`
	Score       float64   `json:"score"`
	Action      string    `json:"action"`
}

func SyncValidateCaptcha(
	secret string,
	captchaResponse string,
) (bool, error) {

	response, err := http.PostForm("https://www.google.com/recaptcha/api/siteverify", url.Values{
		"secret":   {secret},
		"response": {captchaResponse}})
	if err != nil {
		return false, err
	}

	defer response.Body.Close()
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return false, err
	}

	var capResponse CaptchaResponse
	err = json.Unmarshal(body, &capResponse)
	if err != nil {
		return false, err
	}

	captchaScore := 0.0
	if capResponse.Score < captchaScore {
		return false, errors.New(fmt.Sprintf("Captcha score is to low score: %f", capResponse.Score))
	}

	return capResponse.Success, nil
}
