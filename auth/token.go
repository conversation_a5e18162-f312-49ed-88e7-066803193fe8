package auth

import (
	"errors"
	"fmt"
	"github.com/golang-jwt/jwt/v5"
	magento_core "praktis.bg/store-api/packages/magento-core"
	"time"
)

type JWTToken string

const ExpirationKey = "exp"

func GetNewToken(claims jwt.MapClaims) (JWTToken, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	secret, err := magento_core.GetJWTSecret()
	if err != nil {
		return "", err
	}

	tokenString, err := token.SignedString(secret)
	if err != nil {
		return "", err
	}

	return JWTToken(tokenString), nil
}

var InvalidTokenError = errors.New("invalid token")

// GetClaims returns the claims of the token
func (t JWTToken) GetClaims() (jwt.MapClaims, error) {
	if t.IsValidTokenString() == false {
		return jwt.MapClaims{}, InvalidTokenError
	}

	token, err := jwt.Parse(t.String(), func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}

		return magento_core.GetJWTSecret()
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(jwt.MapClaims); ok && token.Valid {
		return claims, nil
	} else {
		return nil, InvalidTokenError
	}
}

func (t JWTToken) IsActive() bool {
	if t == "" {
		return false
	}

	claims, err := t.GetClaims()
	if err != nil {
		return false
	}

	return IsActiveToken(claims)
}

func (t JWTToken) String() string {
	return string(t)
}

func (t JWTToken) IsValidTokenString() bool {
	val := t.String()
	if len(val) < 20 {
		return false
	}

	return true
}

func IsActiveToken(claims jwt.MapClaims) bool {
	exp, ok := claims[ExpirationKey].(float64)
	if !ok || int64(exp) < time.Now().Unix() {
		return false
	}

	return true
}
